namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Configuration class for measure-related settings in BI metadata generation.
    /// Contains definitions of columns that should not be treated as measures.
    /// </summary>
    public class MeasureConfiguration
    {
        /// <summary>
        /// Columns that are not considered request measures and should be excluded from measure calculations.
        /// Reference: https://dev.azure.com/msasg/Bing_Ads/_git/AdsAppsDB?path=%2Fprivate%2FDatamart%2Fadvertiserbi%2FSchema%2FProcedure%2Fprc_CheckType.sql&_a=contents&version=GBmaster
        /// </summary>
        public IReadOnlySet<string> NonRequestMeasures { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // Core identifiers and metadata
            "LoadTime", "AccountId", "AdvertiserCustomerId", "CampaignId", "OrderId", "OrderItemId",
            "CountryCode", "DateKey", "ToDateKey", "DeviceTypeId", "IncomingPublisherWebSiteCountry",
            "PubAccountId", "StartedViewCnt", "SkippedViewCnt",

            // Asset and extension identifiers
            "AdAssetAssociationTypeId", "AdExtensionGroupId", "AdExtensionId", "AdId", "AdPosition",
            "AdTitle", "AgeBucketId", "AgencyId", "AssetId", "AssociationId", "AudienceID", "AudienceName",
            "BiddedMatchTypeID", "BidSuggestionId", "BudgetId", "CategoryList", "CompanyId",

            // Quality and rating identifiers
            "CORVRating", "CPClickRating", "CQBRRating", "CQualityScore", "CurrencyId", "CustomerId",

            // Time and location identifiers
            "DayOfWeek", "DestinationUrl", "DeviceOSId", "DomainTypeId", "FeedId", "FeedItemId",
            "FeedUrl", "GenderId", "GoalId", "GoalTypeId", "GregorianDate", "GroupId", "HasCategory",
            "HourOfDay", "IndustryId", "IsOther", "JobFunctionId", "KeywordOrderId", "KeywordOrderItemId",
            "LandingPageTitle", "Level", "MatchTypeId", "MediumID", "MonthStartDate", "NegativeKeyword",
            "NetworkId", "PagePositionId2", "QuarterStartDate", "Radius", "SearchPhrase", "SearchTerm",
            "Status", "TargetedLocationTypeId", "TargetTypeId", "TargetValueID", "TextEqual",
            "TopLevelCategory", "WebsiteCoverage", "WeekStartDate", "WeekStartDateMonday", "YearNum",

            // Asset headline and description identifiers
            "Headline1AssetId", "Headline2AssetId", "Headline3AssetId", "Description1AssetId",
            "Description2AssetId", "SubMatchTypeId",

            // Audience auction metrics (Note: StartedViewCnt and SkippedViewCnt are not created in all required ClickHouse tables)
            "AudienceAuctionParticipantCnt", "AudienceAuctionWonCnt", "AudienceAuctionLostToBudgetCnt",
            "AudienceAuctionLostToRankCnt", "AudienceClicks", "SameSectionImpressions", "SameSectionClicks",
            "MediumName"
        };

        /// <summary>
        /// Query-specific measure customizations that override default behavior.
        /// </summary>
        public Dictionary<ClickhouseQueryType, MeasureCustomization> QueryMeasureCustomizations { get; set; } = 
            new Dictionary<ClickhouseQueryType, MeasureCustomization>
            {
                {
                    ClickhouseQueryType.prc_CampaignSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToRemove = new List<string> { "DistributionChannelId" },
                        MeasuresToAdd = new List<string> { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" }
                    }
                },
                {
                    ClickhouseQueryType.rpt_CampaignActivity,
                    new MeasureCustomization
                    {
                        MeasuresToRemove = new List<string> { "DistributionChannelId" }
                    }
                },
                {
                    ClickhouseQueryType.prc_AssetCombinationSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToRemove = new List<string> { "CTA" },
                        MeasuresToReplace = new List<string> { "Impressions", "Clicks", "TotalCost", "Conversions" }
                    }
                },
                {
                    ClickhouseQueryType.prc_KeywordSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToAdd = new List<string> { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" }
                    }
                },
                {
                    ClickhouseQueryType.prc_OrderSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToAdd = new List<string> { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" }
                    }
                },
                {
                    ClickhouseQueryType.prc_AccountSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToAdd = new List<string> { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" }
                    }
                },
                {
                    ClickhouseQueryType.prc_CampaignExperimentSummary_ui,
                    new MeasureCustomization
                    {
                        MeasuresToAdd = new List<string> { "AdvertiserReportedRevenue", "ConversionValueCnt", "FullConversionValueCnt" }
                    }
                }
            };

        /// <summary>
        /// Dimension customizations for specific queries.
        /// </summary>
        public Dictionary<ClickhouseQueryType, DimensionCustomization> QueryDimensionCustomizations { get; set; } = 
            new Dictionary<ClickhouseQueryType, DimensionCustomization>
            {
                {
                    ClickhouseQueryType.prc_TimeSummary_Dim,
                    new DimensionCustomization
                    {
                        DimensionsToReplace = new List<string> { "QuarterStartDate", "GregorianDate", "MonthStartDate", "WeekStartDate", "DayOfWeek", "HourOfDay", "YearNum" }
                    }
                }
            };
    }

    /// <summary>
    /// Represents customizations to be applied to measures for a specific query type.
    /// </summary>
    public class MeasureCustomization
    {
        /// <summary>
        /// Measures to remove from the default list.
        /// </summary>
        public List<string> MeasuresToRemove { get; set; } = new List<string>();

        /// <summary>
        /// Measures to add to the default list.
        /// </summary>
        public List<string> MeasuresToAdd { get; set; } = new List<string>();

        /// <summary>
        /// If specified, replaces all measures with this list.
        /// </summary>
        public List<string>? MeasuresToReplace { get; set; }
    }

    /// <summary>
    /// Represents customizations to be applied to dimensions for a specific query type.
    /// </summary>
    public class DimensionCustomization
    {
        /// <summary>
        /// Dimensions to remove from the default list.
        /// </summary>
        public List<string> DimensionsToRemove { get; set; } = new List<string>();

        /// <summary>
        /// Dimensions to add to the default list.
        /// </summary>
        public List<string> DimensionsToAdd { get; set; } = new List<string>();

        /// <summary>
        /// If specified, replaces all dimensions with this list.
        /// </summary>
        public List<string>? DimensionsToReplace { get; set; }
    }
}
