using ClickHouse.Client.ADO;
using ClickHouse.Client.Utility;
using Microsoft.AdCenter.Advertiser.BIDatamart.ClickhouseLocalTest;
using Microsoft.Data.SqlClient;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Generates metadata for BI (Business Intelligence) data processing in ClickHouse.
    /// This class handles the generation of column mappings, query metadata, and data group configurations
    /// for various BI reports and procedures.
    /// </summary>
    public class BiMetadataGenerator
    {
        #region Configuration Dependencies

        private readonly BiMetadataConfiguration _configuration;
        private readonly ISqlQueryProvider _sqlQueryProvider;
        private readonly IClickHouseQueryProvider _clickHouseQueryProvider;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of BiMetadataGenerator with default configuration.
        /// </summary>
        public BiMetadataGenerator() : this(new BiMetadataConfiguration(), new DefaultSqlQueryProvider(), new DefaultClickHouseQueryProvider())
        {
        }

        /// <summary>
        /// Initializes a new instance of BiMetadataGenerator with custom configuration and providers.
        /// </summary>
        /// <param name="configuration">Configuration settings for metadata generation</param>
        /// <param name="sqlQueryProvider">Provider for SQL queries</param>
        /// <param name="clickHouseQueryProvider">Provider for ClickHouse queries</param>
        public BiMetadataGenerator(BiMetadataConfiguration configuration, ISqlQueryProvider sqlQueryProvider, IClickHouseQueryProvider clickHouseQueryProvider)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _sqlQueryProvider = sqlQueryProvider ?? throw new ArgumentNullException(nameof(sqlQueryProvider));
            _clickHouseQueryProvider = clickHouseQueryProvider ?? throw new ArgumentNullException(nameof(clickHouseQueryProvider));
        }

        #endregion

        #region Static Factory Methods (for backward compatibility)

        /// <summary>
        /// Creates a BiMetadataGenerator instance with default configuration.
        /// This method provides backward compatibility with the original static methods.
        /// </summary>
        /// <returns>BiMetadataGenerator instance with default configuration</returns>
        public static BiMetadataGenerator CreateDefault()
        {
            return new BiMetadataGenerator();
        }

        /// <summary>
        /// Creates a BiMetadataGenerator instance with configuration loaded from file.
        /// </summary>
        /// <param name="configFilePath">Path to the configuration JSON file</param>
        /// <returns>BiMetadataGenerator instance with loaded configuration</returns>
        public static BiMetadataGenerator CreateFromConfig(string configFilePath)
        {
            var configuration = BiMetadataConfiguration.FromJsonFile(configFilePath);
            return new BiMetadataGenerator(configuration, new DefaultSqlQueryProvider(), new DefaultClickHouseQueryProvider());
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Generates a dictionary of all ClickHouse BI data columns from the system metadata.
        /// </summary>
        /// <param name="outputFilePath">Path where the JSON output will be written</param>
        /// <returns>Dictionary mapping lowercase column names to their actual case-sensitive names</returns>
        public Dictionary<string, string> GenerateAllCHBiDataColumnsInstance(string outputFilePath)
        {
            using var conn = _clickHouseQueryProvider.CreateClickHouseConnection();

            string query = _clickHouseQueryProvider.GetColumnInfoQuery();

            var allColumns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            using (var dataReader = conn.ExecuteReaderAsync(query).GetAwaiter().GetResult())
            {
                while (dataReader.Read())
                {
                    var column = dataReader.GetString(0);

                    // Skip deprecated columns
                    if (_configuration.DeprecatedColumns.Contains(column))
                        continue;

                    var lowerColumn = column.ToLowerInvariant();

                    // Use TryAdd to avoid duplicate key exceptions
                    allColumns.TryAdd(lowerColumn, column);
                }
            }

            // Serialize and write to file
            var jsonOptions = new JsonSerializerOptions { WriteIndented = true };
            var allColumnsJson = JsonSerializer.Serialize(allColumns, jsonOptions);

            File.WriteAllText(outputFilePath, allColumnsJson);

            return allColumns;
        }

        /// <summary>
        /// Static method for backward compatibility - generates ClickHouse BI data columns.
        /// </summary>
        /// <param name="outputFilePath">Path where the JSON output will be written</param>
        /// <returns>Dictionary mapping lowercase column names to their actual case-sensitive names</returns>
        public static Dictionary<string, string> GenerateAllCHBiDataColumns(string outputFilePath)
        {
            using var conn = QueryUtility2.GetClickhouseConnection();

            const string query = @"
                SELECT DISTINCT name AS ColumnName
                FROM system.columns
                WHERE table NOT LIKE '%Rollup%'
                  AND database = 'AdvertiserBI'
                  AND name NOT LIKE '%.%'
                  AND table NOT LIKE '%Delta_%'
                ORDER BY ColumnName ASC";

            var allColumns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            using (var dataReader = conn.ExecuteReaderAsync(query).GetAwaiter().GetResult())
            {
                while (dataReader.Read())
                {
                    var column = dataReader.GetString(0);
                    var lowerColumn = column.ToLowerInvariant();

                    // Use TryAdd to avoid duplicate key exceptions
                    allColumns.TryAdd(lowerColumn, column);
                }
            }

            // Serialize and write to file
            var jsonOptions = new JsonSerializerOptions { WriteIndented = true };
            var allColumnsJson = JsonSerializer.Serialize(allColumns, jsonOptions);

            File.WriteAllText(outputFilePath, allColumnsJson);

            return allColumns;
        }

        #endregion

        /// <summary>
        /// Generates C# code for ClickHouse data columns mapping.
        /// </summary>
        /// <param name="allCHColumnsDic">Dictionary of all ClickHouse columns</param>
        /// <param name="outputFilePath">Path where the generated code will be written</param>
        public static void GenerateCodeForCHDataColumns(Dictionary<string, string> allCHColumnsDic, string outputFilePath)
        {
            if (allCHColumnsDic == null)
                throw new ArgumentNullException(nameof(allCHColumnsDic));

            if (string.IsNullOrWhiteSpace(outputFilePath))
                throw new ArgumentException("Output file path cannot be null or empty", nameof(outputFilePath));

            const string codeTemplate = @"using System;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public partial class ColumnMap
    {
        public static Dictionary<string, string> ClickhouseDataColumns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {};
    }
}
";

            StringBuilder sb = new StringBuilder();
            sb.Append("{");
            sb.AppendLine();

            foreach (var column in allCHColumnsDic)
            {
                sb.Append(@"            { ").Append($"\"{column.Key}\"").Append(",").Append($"\"{column.Value}\"").Append(@" },");
                sb.AppendLine();
            }
            sb.Remove(sb.Length - 3, 3);
            sb.AppendLine();
            sb.Append(@"        };");

            var finalCode = codeTemplate.Replace(@"{};", sb.ToString());
            File.WriteAllText(outputFilePath, finalCode);
        }

        /// <summary>
        /// Fetches BI data group information from the database and constructs BiDataGroup objects.
        /// </summary>
        /// <param name="conn">SQL connection to use for database queries</param>
        /// <param name="cmdStr">SQL command string to execute</param>
        /// <param name="allCHColumnsDic">Dictionary mapping column names to their proper case</param>
        /// <returns>Dictionary of BiDataCategory to BiDataGroup mappings</returns>
        public static Dictionary<BiDataCategory, BiDataGroup> FetchBiDataGroupData(
            SqlConnection conn,
            string cmdStr,
            Dictionary<string, string> allCHColumnsDic)
        {
            if (conn == null)
                throw new ArgumentNullException(nameof(conn));

            if (string.IsNullOrWhiteSpace(cmdStr))
                throw new ArgumentException("Command string cannot be null or empty", nameof(cmdStr));

            if (allCHColumnsDic == null)
                throw new ArgumentNullException(nameof(allCHColumnsDic));

            var biDataGroups = new Dictionary<BiDataCategory, BiDataGroup>();

            using (var cmd = new SqlCommand(cmdStr, conn))
            using (var dbReader = cmd.ExecuteReader())
            {
                while (dbReader.Read())
                {
                    var biDataCategory = dbReader.GetString(0);
                    var conversionTableName = dbReader.IsDBNull(1) ? null : dbReader.GetString(1);
                    var columnName = dbReader.GetString(2);
                    var existInTablesBitMask = dbReader.GetInt32(3);
                    var defaultValueIfNonExist = dbReader.IsDBNull(4) ? null :
                        NormalizeClickHouseExpression(dbReader.GetString(4));
                    var columnAlias = dbReader.IsDBNull(5) ? null : dbReader.GetString(5);

                    // Normalize column name using the provided dictionary
                    columnName = allCHColumnsDic.TryGetValue(columnName.ToLowerInvariant(), out var normalizedName)
                        ? normalizedName
                        : columnName;

                    // Handle special case for SameSectionCnt
                    if (columnName.Equals("SameSectionCnt", StringComparison.OrdinalIgnoreCase))
                    {
                        columnAlias = "SameSectionCount"; // Special case hard coded in SP
                    }

                    // Parse and normalize category
                    if (!Enum.TryParse<BiDataCategory>(biDataCategory, out var category))
                    {
                        continue; // Skip invalid categories
                    }

                    // Handle special category mapping
                    if (category == BiDataCategory.ProductOfferImpressionShareUsage)
                    {
                        category = BiDataCategory.ImpressionShareProductOfferUsage;
                    }

                    // Add or update the category in the dictionary
                    var columnProperties = new ColumnProperties(existInTablesBitMask, defaultValueIfNonExist, columnAlias);

                    if (biDataGroups.TryGetValue(category, out var existingGroup))
                    {
                        existingGroup.Columns.Add(columnName, columnProperties);
                    }
                    else
                    {
                        var columns = new Dictionary<string, ColumnProperties>(StringComparer.OrdinalIgnoreCase)
                        {
                            { columnName, columnProperties }
                        };
                        biDataGroups.Add(category, new BiDataGroup(columns, conversionTableName,
                            hasConversionData: !string.IsNullOrEmpty(conversionTableName)));
                    }
                }
            } // DataReader is disposed here

            // Fetch additional in-store conversion data after the first DataReader is closed
            FetchBiDataGroupDataForInStoreConv(biDataGroups, BiDataCategory.AccountUsage, conn);
            FetchBiDataGroupDataForInStoreConv(biDataGroups, BiDataCategory.CampaignUsage, conn);

            return biDataGroups;
        }

        /// <summary>
        /// Normalizes SQL expressions to ClickHouse-compatible format.
        /// </summary>
        /// <param name="expression">The SQL expression to normalize</param>
        /// <returns>Normalized ClickHouse expression</returns>
        private static string NormalizeClickHouseExpression(string expression)
        {
            return expression
                .Replace("Datekey", "DateKey", StringComparison.OrdinalIgnoreCase)
                .Replace("IIF(", "IF(", StringComparison.OrdinalIgnoreCase)
                .Replace("ISNULL", "ifZeroOrNull", StringComparison.OrdinalIgnoreCase)
                .Replace("isnull", "ifZeroOrNull", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Fetches and updates BI data group information for in-store conversion tables.
        /// </summary>
        /// <param name="biDataGroups">Dictionary of BI data groups to update</param>
        /// <param name="category">The BI data category to process</param>
        /// <param name="conn">SQL connection to use for database queries</param>
        private static void FetchBiDataGroupDataForInStoreConv(
            Dictionary<BiDataCategory, BiDataGroup> biDataGroups,
            BiDataCategory category,
            SqlConnection conn)
        {
            if (biDataGroups == null)
                throw new ArgumentNullException(nameof(biDataGroups));

            if (conn == null)
                throw new ArgumentNullException(nameof(conn));

            if (!biDataGroups.TryGetValue(category, out var biDataGroup))
            {
                return; // Category not found, nothing to update
            }

            var cmdStr = $"SELECT Column_Name FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Daily{category}_InStoreConv'";

            using var cmd = new SqlCommand(cmdStr, conn);
            using var dbReader = cmd.ExecuteReader();

            while (dbReader.Read())
            {
                var column = dbReader.GetString(0);
                if (biDataGroup.Columns.TryGetValue(column, out var columnProperties))
                {
                    // Add bit flag 64 for in-store conversion table existence
                    columnProperties.ExistInTablesBitMask += 64;
                }
            }
        }

        /// <summary>
        /// Generates BI data groups by fetching metadata from the database and constructing BiDataGroup objects.
        /// </summary>
        /// <param name="outputFilePath">Path where the JSON output will be written</param>
        /// <param name="allCHColumnsDic">Dictionary mapping column names to their proper case</param>
        /// <returns>Dictionary of BiDataCategory to BiDataGroup mappings</returns>
        public static Dictionary<BiDataCategory, BiDataGroup> GenerateBiDataGroups(string outputFilePath, Dictionary<string, string> allCHColumnsDic)
        {
            if (string.IsNullOrWhiteSpace(outputFilePath))
                throw new ArgumentException("Output file path cannot be null or empty", nameof(outputFilePath));

            if (allCHColumnsDic == null)
                throw new ArgumentNullException(nameof(allCHColumnsDic));

            using var conn = QueryUtility2.GetCISqlConnection();
            conn.Open();

            string cmdStr = @"
select f.FactGroup as BiDataCategory, 
	ConversionFactTable as ConversionTableName,
	ColumnName,
	cast(IsExistBitmask as int) as ExistInTablesBitMask,
	ColumnExpression as DefaultValueIfNonExist,
    ColumnAlias
from dbo.FactGroupMetadata f
inner join dbo.ColumnMetadata c on f.FactGroup = c.FactGroup
UNION
SELECT FactGroup as BiDataCategory,
       '' as ConversionTableName,
	   ColumnName,
	cast(IsExistBitmask as int) as ExistInTablesBitMask,
	ColumnExpression as DefaultValueIfNonExist,
    ColumnAlias
FROM dbo.ColumnMetadata where FactGroup not in (select FactGroup FROM dbo.FactGroupMetadata)
order by f.FactGroup, ColumnName";

            var biDataGroups = FetchBiDataGroupData(conn, cmdStr, allCHColumnsDic);

            var factGroupNotInMetadata = Enum.GetValues(typeof(BiDataCategory)).Cast<BiDataCategory>().Except(biDataGroups.Keys)
               .Where(x => (x == BiDataCategory.vHotelVerticalUsage) || (x == BiDataCategory.HotelVerticalUsage) //hard code for prc_HotelVerticalSummary_ui  
                   || (x == BiDataCategory.vHotelVerticalImpressionShareUsage) || (x == BiDataCategory.HotelVerticalImpressionShareUsage)
                   || (x == BiDataCategory.HotelBookingUsage)
                   || (x == BiDataCategory.MatchedOffers));
            if (factGroupNotInMetadata.Any())
            {
                var cmdStrFunc = (string factGroupName) =>
                $@"DROP TABLE IF EXISTS #BiDataCategory;
                    SELECT 
                        '{factGroupName}' AS BiDataCategory,     
                        MAX(CASE WHEN table_name = 'Hourly{factGroupName}_Conv' THEN 'Hourly{factGroupName}_Conv' 
                            ELSE NULL END) AS ConversionTableName,
                        column_name AS ColumnName,
                        SUM (CASE WHEN table_name = 'InProgress{factGroupName}' THEN 1
                                  WHEN table_name = 'Hourly{factGroupName}' THEN 2
                                  WHEN table_name = 'Daily{factGroupName}' THEN 4
                                  WHEN table_name = 'Monthly{factGroupName}' THEN 8
                                  WHEN table_name = 'Hourly{factGroupName}_Conv' THEN 16
                            ELSE 0 END) AS ExistInTablesBitMask,
                        NULL AS DefaultValueIfNonExist,
                        NULL AS ColumnAlias       
                    INTO #BiDataCategory
                    FROM information_schema.columns 
                    WHERE table_name IN ('InProgress{factGroupName}', 'Hourly{factGroupName}', 'Daily{factGroupName}',
                        'Monthly{factGroupName}', 'Hourly{factGroupName}_Conv') 
                    GROUP BY column_name;
                    
                    IF @@ROWCOUNT = 0
                    BEGIN 
                        INSERT INTO #BiDataCategory
                        SELECT 
                            '{factGroupName}' AS BiDataCategory,     
                            MAX(CASE WHEN table_name = 'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName +'_Conv' COLLATE SQL_Latin1_General_CP1_CI_AS 
								THEN 'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName +'_Conv' COLLATE SQL_Latin1_General_CP1_CI_AS
                                ELSE NULL END) AS ConversionTableName,
                            column_name AS ColumnName,
                            SUM (CASE WHEN table_name = 'InProgress 'COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName THEN 1
                                      WHEN table_name = 'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName THEN 2
                                      WHEN table_name = 'Daily' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName THEN 4
                                      WHEN table_name = 'Monthly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName THEN 8
                                      WHEN table_name = 'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName +'_Conv' COLLATE SQL_Latin1_General_CP1_CI_AS THEN 16
                                ELSE 0 END) AS ExistInTablesBitMask,
                            NULL AS DefaultValueIfNonExist,
                            NULL AS ColumnAlias        
                        FROM information_schema.columns AS l
                        CROSS APPLY (SELECT  FactGroupViewName FROM dbo.RollupArchiveStatistics WHERE FactGroupName = '{factGroupName}')  AS r
                        WHERE l.table_name IN (
							'InProgress' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName,
							'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName, 
							'Daily' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName, 
							'Monthly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName,
							'Hourly' COLLATE SQL_Latin1_General_CP1_CI_AS + r.FactGroupViewName + '_Conv' COLLATE SQL_Latin1_General_CP1_CI_AS)
                        GROUP BY l.column_name
                    END

                    SELECT * FROM #BiDataCategory";

                foreach (var factGroup in factGroupNotInMetadata)
                {
                    cmdStr = cmdStrFunc(factGroup.ToString());
                    var biDataGroup = FetchBiDataGroupData(conn, cmdStr, allCHColumnsDic);
                    biDataGroup.ToList().ForEach(x => biDataGroups.Add(x.Key, x.Value));
                }

                if (biDataGroups.TryGetValue(BiDataCategory.MatchedOffers, out var matchedOffers))
                {
                    //supoort both daily and hourly
                    foreach (var column in matchedOffers.Columns)
                    {
                        column.Value.ExistInTablesBitMask = 6;
                    }
                }

            }

            var reachUsageMetadata = Enum.GetValues(typeof(BiDataCategory)).Cast<BiDataCategory>().Except(biDataGroups.Keys)
              .Where(x => (x == BiDataCategory.CampaignReachUsage) || (x == BiDataCategory.AdReachUsage) || (x == BiDataCategory.OrderReachUsage));

            if (reachUsageMetadata.Any())
            {
                foreach (var category in reachUsageMetadata)
                {
                    var columns = new Dictionary<string, ColumnProperties>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "DateKey", new ColumnProperties(4, null, null) },
                            { "AccountId", new ColumnProperties(4, null, null) },
                            { "CampaignId", new ColumnProperties(4, null, null) },
                            { "MediumId", new ColumnProperties(0, "10*1", null) }
                        };

                    if (category == BiDataCategory.OrderReachUsage)
                    {
                        columns.Add("OrderId", new ColumnProperties(4, null, null));
                    }
                    else if (category == BiDataCategory.AdReachUsage)
                    {
                        columns.Add("OrderId", new ColumnProperties(4, null, null));
                        columns.Add("AdId", new ColumnProperties(4, null, null));
                    }

                    foreach (var day in Enumerable.Range(1, 30))
                    {
                        columns.Add($"Reach{day}d", new ColumnProperties(4, null, null));
                    }
                    columns.Add("LoadTime", new ColumnProperties(4, null, null));
                    biDataGroups.Add(category, new BiDataGroup(columns, null, false, false));
                }
            }


            cmdStr = @"select FactGroupName as BiDataCategory,StreamingEnabled, HasConversionData from dbo.RollupArchiveStatistics";
            using (var cmd = new SqlCommand(cmdStr, conn))
            using (var dbReader = cmd.ExecuteReader())
            {
                string biDataCategory;
                bool streamingEnabled;
                bool hasConversionData;

                while (dbReader.Read())
                {
                    biDataCategory = dbReader.GetString(0);
                    streamingEnabled = dbReader.GetBoolean(1);
                    hasConversionData = dbReader.GetBoolean(2);
                    if (!Enum.TryParse(typeof(BiDataCategory), biDataCategory, out var r)) {
                        continue;
                    }
                    var category = (BiDataCategory)Enum.Parse(typeof(BiDataCategory), biDataCategory);
                    if (category == BiDataCategory.ProductOfferImpressionShareUsage)
                    {
                        category = BiDataCategory.ImpressionShareProductOfferUsage;
                    }
                    if (biDataGroups.ContainsKey(category))
                    {
                        biDataGroups[category].StreamingEnabled = streamingEnabled;
                        biDataGroups[category].HasConversionData = hasConversionData;
                    }
                    else
                    {
                        biDataGroups.Add(category, new BiDataGroup(new Dictionary<string, ColumnProperties>(StringComparer.OrdinalIgnoreCase), null, streamingEnabled, hasConversionData));
                    }
                }
            }

            biDataGroups[BiDataCategory.CookieCampaignExperimentUsage].HasConversionData = false;
            biDataGroups[BiDataCategory.CookieCampaignExperimentUsage].ConversionTableName = "";
            biDataGroups[BiDataCategory.AssetGroupUsage].ConversionTableName = "HourlyAssetGroupUsage_Conv";
            biDataGroups[BiDataCategory.OrderItemDDAUsage].ConversionTableName = "HourlyOrderItemDDAUsage_Conv";
            biDataGroups[BiDataCategory.ProductOfferCampaignUsage].ConversionTableName = "HourlyProductOfferCampaignUsage_Conv";
            biDataGroups[BiDataCategory.MSXAccountUsage].HasConversionData = false;
            biDataGroups[BiDataCategory.CampaignExperimentUsage].StreamingEnabled = false;
            biDataGroups[BiDataCategory.CallDetailsUsage].HasConversionData = false;
            biDataGroups[BiDataCategory.CallDetailsUsage].ConversionTableName = string.Empty;
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].HasConversionData = true;
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].ConversionTableName = "HourlyAssetSnapShotUsage_Conv";

            foreach (var item in Constants2.ColumnsDefaultValue)
            {
                foreach (var column in item.Value)
                {
                    if (string.IsNullOrWhiteSpace(biDataGroups[item.Key].Columns[column.columnName].DefaultValueIfNonExist))
                    {
                        biDataGroups[item.Key].Columns[column.columnName].DefaultValueIfNonExist = column.defaultValue;
                    }
                }
            }

            biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("SearchUniqueImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("TotalSearchAbsTopPosition", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));

            //there are SOV measures
            // biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("BudgetFilteredImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            // biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("RankFilteredImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            // biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("EligibleImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            //biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("BenchmarkBid_Numerator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            //biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("BenchmarkBid_Denominator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            //biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("BenchmarkCTR_Numerator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            //biDataGroups[BiDataCategory.ProductOfferUsage].Columns.Add("BenchmarkCTR_Denominator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));

            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("SearchUniqueImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("TotalSearchAbsTopPosition", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("EligibleImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("BudgetFilteredImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("RankFilteredImpressionCnt", new ColumnProperties(0, "CAST(0 AS BIGINT)", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("BenchmarkBid_Numerator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("BenchmarkBid_Denominator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("BenchmarkCTR_Numerator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns.Add("BenchmarkCTR_Denominator", new ColumnProperties(0, "CAST(0 AS DECIMAL(18, 6))", null));

            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["CooperativeConversionCnt"] = new ColumnProperties(16, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["FullConversionCnt"] = new ColumnProperties(16, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["FullAdvertiserReportedRevenue"] = new ColumnProperties(16, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["CooperativeClickCnt"] = new ColumnProperties(7, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["ConversionEnabledClickCnt"] = new ColumnProperties(7, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["CooperativeImpressionCnt"] = new ColumnProperties(7, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["ConversionEnabledTotalAmount"] = new ColumnProperties(7, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["TotalConversionCnt"] = new ColumnProperties(0, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.BSCQueryUsage].Columns["TotalConversionCredit"] = new ColumnProperties(0, "CAST(0 AS DECIMAL(28,12))", null);

            biDataGroups[BiDataCategory.CookieCampaignExperimentUsage].Columns["AdvertiserReportedRevenue"] = new ColumnProperties(0, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.CookieCampaignExperimentUsage].Columns["ConversionValueCnt"] = new ColumnProperties(0, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.CookieCampaignExperimentUsage].Columns["FullConversionValueCnt"] = new ColumnProperties(0, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.CampaignUsage].Columns["ConversionValueCnt"] = new ColumnProperties(0, "CAST(f.AdvertiserReportedRevenue AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.CampaignUsage].Columns["FullConversionValueCnt"] = new ColumnProperties(0, "CAST(f.FullAdvertiserReportedRevenue AS DECIMAL(28,12))", null);

            biDataGroups[BiDataCategory.MSANImpressionShareCampaignUsage].Columns["TargetTypeId"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.MSANImpressionShareCampaignUsage].Columns["PagePositionId2"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.MSANImpressionShareCampaignUsage].Columns["GoalTypeId"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.MSANImpressionShareCampaignUsage].Columns["TargetValueId"] = new ColumnProperties(0, "CAST(0x7FFFFFFFFFFFFFFF AS Int64)", null);

            biDataGroups[BiDataCategory.ImpressionShareCampaignUsage].Columns["TargetTypeId"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.ImpressionShareCampaignUsage].Columns["PagePositionId2"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.ImpressionShareCampaignUsage].Columns["GoalTypeId"] = new ColumnProperties(0, "CAST(255 AS UInt8)", null);
            biDataGroups[BiDataCategory.ImpressionShareCampaignUsage].Columns["TargetValueId"] = new ColumnProperties(0, "CAST(0x7FFFFFFFFFFFFFFF AS Int64)", null);

            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["AccountId"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["AdAssetAssociationTypeId"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["AssetId"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["CampaignId"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["ClickCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["CompletedViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["DateKey"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["ImpressionCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["LoadTime"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["OrderId"] = new ColumnProperties(20, null, null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["Percent25ViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["Percent50ViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["Percent75ViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["SkippedViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["StartedViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["TotalAmount"] = new ColumnProperties(4, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["TotalWatchTime"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["ViewCnt"] = new ColumnProperties(4, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["ConversionCredit"] = new ColumnProperties(16, "CAST(0 AS DECIMAL(28,12))", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["ConversionCnt"] = new ColumnProperties(16, "CAST(0 AS BIGINT)", null);
            biDataGroups[BiDataCategory.AssetAdGroupSnapshotUsage].Columns["AdvertiserReportedRevenue"] = new ColumnProperties(16, "CAST(0 AS DECIMAL(28,12))", null);


            biDataGroups[BiDataCategory.CampaignUsage].Columns["ConversionLag"] = new ColumnProperties(16, "CAST(0 AS Int32)", null);
            biDataGroups[BiDataCategory.GoalUsage].Columns["ConversionLag"] = new ColumnProperties(3, "CAST(0 AS Int32)", null);
            biDataGroups[BiDataCategory.AccountUsage].Columns["ConversionLag"] = new ColumnProperties(16, "CAST(0 AS Int32)", null);

            string biDataGroupsJson = JsonSerializer.Serialize(biDataGroups, new JsonSerializerOptions { WriteIndented = true });
            var writer = new StreamWriter(outputFilePath);
            writer.Write(biDataGroupsJson);
            writer.Close();

            return biDataGroups;
        }
    

        /// <summary>
        /// Generates query metadata by fetching information from the database about reports, columns, joins, and parameters.
        /// </summary>
        /// <param name="outputFilePath">Path where the JSON output will be written</param>
        /// <param name="allCHColumnsDic">Dictionary mapping column names to their proper case</param>
        /// <returns>Dictionary of ClickhouseQueryType to QueryMetadata mappings</returns>
        public static Dictionary<ClickhouseQueryType, QueryMetadata> GenerateQueryMetadatas(string outputFilePath, Dictionary<string, string> allCHColumnsDic)
        {
            if (string.IsNullOrWhiteSpace(outputFilePath))
                throw new ArgumentException("Output file path cannot be null or empty", nameof(outputFilePath));

            if (allCHColumnsDic == null)
                throw new ArgumentNullException(nameof(allCHColumnsDic));

            // Use default configuration for static method
            var defaultConfig = new BiMetadataConfiguration();

            using var conn = QueryUtility2.GetCISqlConnection();
            conn.Open();
                string cmdStr = @"
select ReportName as QueryType, 
	RequestedColumnName as RequestColumnName, 
	ReportHierarchyName as JoinTableAlias, 
	DependentColumnName, 
	ColumnExpression
from dbo.ReportMetadata
where RequestedColumnName not like '%##ISOTHER##%'
order by ReportName, RequestedColumnName";

                Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas = new Dictionary<ClickhouseQueryType, QueryMetadata>();

                using (var cmd = new SqlCommand(cmdStr, conn))
                using (var dbReader = cmd.ExecuteReader())
                {
                    ClickhouseQueryType queryType;
                    string requestColumnName, queryTypeStr;
                    string? joinTableAlias, dependentColumnName, columnExpression;

                    while (dbReader.Read())
                    {
                        queryTypeStr = dbReader.GetString(0);
                        if(!Enum.TryParse(typeof(ClickhouseQueryType), queryTypeStr, true, out var q1))
                        {
                            continue;
                        }
                        queryType = (ClickhouseQueryType)Enum.Parse(typeof(ClickhouseQueryType), queryTypeStr);
                        requestColumnName = dbReader.GetString(1).Trim();
                        // Skip deprecated columns
                        if (defaultConfig.DeprecatedColumns.Contains(requestColumnName))
                        {
                            continue;
                        }
                        requestColumnName = allCHColumnsDic.ContainsKey(requestColumnName.ToLower()) ? allCHColumnsDic[requestColumnName.ToLower()] : requestColumnName;
                        joinTableAlias = dbReader.IsDBNull(2) ? null : dbReader.GetString(2);
                        dependentColumnName = dbReader.IsDBNull(3) ? null : ReplaceWithCHColumns(dbReader.GetString(3), allCHColumnsDic).Replace("IIF(", "IF(").Replace("ISNULL", "ifZeroOrNull").Replace("isnull", "ifZeroOrNull").Replace("AS TINYINT", "AS TINYINT UNSIGNED", StringComparison.OrdinalIgnoreCase);
                        columnExpression = dbReader.IsDBNull(4) ? null : ReplaceWithCHColumns(dbReader.GetString(4), allCHColumnsDic).Replace("IIF(", "IF(").Replace("ISNULL", "ifZeroOrNull").Replace("isnull", "ifZeroOrNull").Replace("N'", "'").Replace("AS TINYINT", "AS TINYINT UNSIGNED", StringComparison.OrdinalIgnoreCase);

                        if(!string.IsNullOrWhiteSpace(columnExpression) && (requestColumnName.Trim().EndsWith("Longitude", StringComparison.OrdinalIgnoreCase) || requestColumnName.Trim().EndsWith("Latitude", StringComparison.OrdinalIgnoreCase)) &&
                            columnExpression.Contains("0.0"))
                        {
                            columnExpression = columnExpression.Replace("0.0", "toFloat32(0.0)");
                        }

                        if(requestColumnName.Contains("Historical", StringComparison.OrdinalIgnoreCase) && joinTableAlias!=null &&joinTableAlias.Equals("hqs", StringComparison.OrdinalIgnoreCase))
                        {
                            if (string.IsNullOrWhiteSpace(dependentColumnName))
                            {
                                dependentColumnName = "<factalias>.DateKey";
                            }
                            else
                            {
                                dependentColumnName = $"{dependentColumnName},<factalias>.DateKey";
                            }
                        }

                        if (requestColumnName == "BiddedMatchTypeId")
                        {
                            columnExpression = null;
                        }
                        if (queryType == ClickhouseQueryType.prc_GeoLocationSummary_Dim && (requestColumnName == "Neighborhood" || requestColumnName == "NeighborhoodLatitude" || requestColumnName == "NeighborhoodLongitude"))
                        {
                            joinTableAlias = "geo";
                        }
                        if (queryMetadatas.ContainsKey(queryType))
                        {
                            queryMetadatas[queryType].RequestColumns.Add(requestColumnName, new RequestColumn(joinTableAlias, dependentColumnName, columnExpression));
                        }
                        else
                        {
                            var biDataCategories = BiProcessorDataCategory.ProcessorDataCategories.TryGetValue(queryType, out var categories) ? categories : new List<BiDataCategory>();
                            queryMetadatas.Add(queryType, new QueryMetadata(new HashSet<QueryParameter>(), new Dictionary<string, RequestColumn>(), new Dictionary<string, ColumnTransformation>(), new Dictionary<string, QueryJoinCondition>(), biDataCategories));
                            queryMetadatas[queryType].RequestColumns.Add(requestColumnName, new RequestColumn(joinTableAlias, dependentColumnName, columnExpression));
                        }
                    }
                }

                foreach (var reort in Constants2.ReportRequestColumns)
                {
                    foreach (var column in reort.Value)
                    {
                        if (queryMetadatas.ContainsKey(reort.Key))
                        {
                            queryMetadatas[reort.Key].RequestColumns.Add(column.Key, column.Value);
                        }
                        else
                        {
                            var biDataCategories = BiProcessorDataCategory.ProcessorDataCategories.TryGetValue(reort.Key, out var categories) ? categories : new List<BiDataCategory>();
                            queryMetadatas.Add(reort.Key, new QueryMetadata(new HashSet<QueryParameter>(), new Dictionary<string, RequestColumn>(), new Dictionary<string, ColumnTransformation>(), new Dictionary<string, QueryJoinCondition>(), biDataCategories));
                            queryMetadatas[reort.Key].RequestColumns.Add(column.Key, column.Value);
                        }
                    }
                }

                cmdStr = @"
select distinct rh.ReportName as QueryType,
	rh.ReportHierarchyId as JoinHierarchyId,
	rh.ReportHierarchyName as JoinTableAlias,
	rh.ObjectName as JoinTableName,
	rh.Direction as JoinType,
	rhc.ColumnName as LeftColumn,
	rh.ReferencedReportHierarchyName as RightTableAlias,
	rhc.ReferencedColumnName as RightColumn,
	rh.FilterExpression as FilterExpression
from dbo.ReportHierarchy rh
inner join dbo.ReportHierarchyColumns rhc
on rh.ReportHierarchyId = rhc.ReportHierarchyId
order by rh.ReportName, rh.ReportHierarchyId, rhc.ColumnName";
                using (var cmd2 = new SqlCommand(cmdStr, conn))
                using (var dbReader2 = cmd2.ExecuteReader())
                {
                    string joinTableName, leftColumn, rightTableAlias, rightColumn, filterExp;
                    int joinHierarchyId;
                    string? joinType;
                    string queryTypeStr;
                    ClickhouseQueryType queryType;
                    string? joinTableAlias;

                    while (dbReader2.Read())
                    {
                        queryTypeStr = dbReader2.GetString(0);
                        if (!Enum.TryParse(typeof(ClickhouseQueryType), queryTypeStr, true, out var q1))
                        {
                            continue;
                        }
                        queryType = (ClickhouseQueryType)Enum.Parse(typeof(ClickhouseQueryType), queryTypeStr, true);
                        joinHierarchyId = dbReader2.GetInt32(1);
                        joinTableAlias = dbReader2.GetString(2);
                        joinTableName = dbReader2.GetString(3);

                        //fix vDailyBidSuggestion
                        joinTableName = joinTableName.Replace("DailyBidSuggestion", "vDailyBidSuggestion");
                        // dimension table in clickhouse need to be queried by view vXXXXXXDim
                        //exception: AllDailyOrderHistoricalQualityScore, <category>, #NodeHotelMap, UserDefinedDimension4
                        if (!defaultConfig.DimTablePrefixNotCreateView.Any(x => joinTableName.StartsWith(x)))
                        {
                            if (joinTableName == "keywordorderitem")
                            {
                                joinTableName = "KeywordOrderItem";
                            }
                            else if (joinTableName == "languageLocale")
                            {
                                joinTableName = "LanguageLocale";
                            }
                            joinTableName = $"v{joinTableName}Dim";
                        }

                        //if (dimTableNameNotEndWithDim.Contains(joinTableName))
                        //{
                        //    joinTableName += "Dim";
                        //}

                        joinType = dbReader2.IsDBNull(4) ? null : FormatJoinType(dbReader2.GetString(4));
                        leftColumn = Regex.Replace(dbReader2.GetString(5), defaultConfig.IdReplacementPattern, "Id");//replace ID with Id, but not LCID
                        rightTableAlias = dbReader2.GetString(6);
                        rightColumn = Regex.Replace(dbReader2.GetString(7), defaultConfig.IdReplacementPattern, "Id");
                        leftColumn = allCHColumnsDic.ContainsKey(leftColumn.ToLower()) ? allCHColumnsDic[leftColumn.ToLower()] : leftColumn;
                        rightColumn = allCHColumnsDic.ContainsKey(rightColumn.ToLower()) ? allCHColumnsDic[rightColumn.ToLower()] : rightColumn;

                        filterExp = dbReader2.IsDBNull(8) ? string.Empty : dbReader2.GetString(8);

                        if (queryType == ClickhouseQueryType.rpt_DestinationURLActivity && joinTableAlias == Constants2.vLanguageLocaleDim_Alias)
                        {
                            rightTableAlias = Constants2.vDistributionChannel_FlatDim_Alias;
                        }
                        if (queryMetadatas[queryType].JoinConditions.ContainsKey(joinTableAlias))
                        {
                            queryMetadatas[queryType].JoinConditions[joinTableAlias].JoinColumns.Add(new JoinColumn(leftColumn, rightColumn));
                        }
                        else
                        {
                            var joinColumns = new List<JoinColumn>() { new JoinColumn(leftColumn, rightColumn) };
                            queryMetadatas[queryType].JoinConditions.Add(joinTableAlias, new QueryJoinCondition(joinTableName, joinHierarchyId, joinType, rightTableAlias, joinColumns, filterExp));
                        }
                    }
                }

                string reportsNotInMeta = string.Empty;
                reportsNotInMeta = string.Join(',', defaultConfig.ReportsNotInMetaTable.Select(a => $"'{a}'").ToArray());

                cmdStr = @"
select QueryType, QueryParameter , typeName, userDefined
FROM (
select distinct rm.ReportName COLLATE SQL_Latin1_General_CP1_CI_AS as QueryType, 
	substring(parameters.name, 2, CASE when len(parameters.name) = 0 then 0 else len(parameters.name)-1 END) as QueryParameter, parameters.parameter_id, types.name as typeName,  types.is_user_defined AS userDefined
from sys.parameters 
inner join sys.procedures on parameters.object_id = procedures.object_id 
inner join sys.types on parameters.system_type_id = types.system_type_id AND parameters.user_type_id = types.user_type_id
inner join dbo.ReportMetadata rm on procedures.name = rm.ReportName COLLATE SQL_Latin1_General_CP1_CI_AS
union 
select distinct procedures.name as QueryType, 
	substring(parameters.name, 2, len(parameters.name)-1) as QueryParameter, parameters.parameter_id, types.name as typeName,  types.is_user_defined AS userDefined
from sys.parameters 
inner join sys.procedures on parameters.object_id = procedures.object_id
inner join sys.types on parameters.system_type_id = types.system_type_id AND parameters.user_type_id = types.user_type_id
where   procedures.name in (" + reportsNotInMeta + @")) p
order by QueryType, parameter_id";
                using (var cmd3 = new SqlCommand(cmdStr, conn))
                using (var dbReader3 = cmd3.ExecuteReader())
                {
                    string typeName;
                    bool userDefined;
                    string queryTypeStr;
                    ClickhouseQueryType queryType;

                    while (dbReader3.Read())
                    {
                        QueryParameter queryParameter;
                        queryTypeStr = dbReader3.GetString(0);
                        if (!Enum.TryParse(typeof(ClickhouseQueryType), queryTypeStr, true, out var q1))
                        {
                            continue;
                        }
                        queryType = (ClickhouseQueryType)Enum.Parse(typeof(ClickhouseQueryType), queryTypeStr);
                        var parameterName = Regex.Replace(dbReader3.GetString(1), defaultConfig.IdReplacementPattern, "Id");
                        typeName = dbReader3.GetString(2);
                        userDefined = dbReader3.GetBoolean(3);
                        if (!queryMetadatas.ContainsKey(queryType))
                        {
                            var biDataCategories = BiProcessorDataCategory.ProcessorDataCategories.TryGetValue(queryType, out var categories) ? categories : new List<BiDataCategory>();
                            queryMetadatas.Add(queryType, new QueryMetadata(new HashSet<QueryParameter>(), new Dictionary<string, RequestColumn>(), new Dictionary<string, ColumnTransformation>(), new Dictionary<string, QueryJoinCondition>(), biDataCategories));
                        }

                        queryParameter = new QueryParameter(parameterName, typeName, userDefined);
                        if (!queryMetadatas[queryType].QueryParameters.Where(a => a.ParameterName.Equals(queryParameter.ParameterName)).Any())
                        {
                            queryMetadatas[queryType].QueryParameters.Add(queryParameter);
                        }
                    }
                }

                CustomizeQueryMetadatas(queryMetadatas);

                FillColumnTransformation(queryMetadatas);

                AddCHOnlyReportToQueryMetadata(queryMetadatas);

                var options = new JsonSerializerOptions();
                options.Converters.Add(new JsonStringEnumConverter());
                options.WriteIndented = true;
                string queryMetadatasJson = JsonSerializer.Serialize(queryMetadatas, options);
                var writer = new StreamWriter(outputFilePath);
                writer.Write(queryMetadatasJson);
                writer.Close();

                return queryMetadatas;
        }

        private static void AddCHOnlyReportToQueryMetadata(Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas)
        {
            //Category report
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport] = new QueryMetadata(new HashSet<QueryParameter>(), new Dictionary<string, RequestColumn>(), new Dictionary<string, ColumnTransformation>(), new Dictionary<string, QueryJoinCondition>(), new List<BiDataCategory>());
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("ICEL1Id", new RequestColumn(null, "<factalias>.ICEL1Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("ICEL2Id", new RequestColumn(null, "<factalias>.ICEL2Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("ICEL3Id", new RequestColumn(null, "<factalias>.ICEL3Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("CountryCode", new RequestColumn(null, "<factalias>.CountryCode", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("AccountId", new RequestColumn(null, "<factalias>.AccountId", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("StartDate", new RequestColumn(null, "<factalias>.StartDate", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryInsightsReport].RequestColumns.Add("EndDate", new RequestColumn(null, "<factalias>.EndDate", null));

            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport] = new QueryMetadata(new HashSet<QueryParameter>(), new Dictionary<string, RequestColumn>(), new Dictionary<string, ColumnTransformation>(), new Dictionary<string, QueryJoinCondition>(), new List<BiDataCategory>());
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("ICEL1Id", new RequestColumn(null, "<factalias>.ICEL1Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("ICEL2Id", new RequestColumn(null, "<factalias>.ICEL2Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("ICEL3Id", new RequestColumn(null, "<factalias>.ICEL3Id", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("CountryCode", new RequestColumn(null, "<factalias>.CountryCode", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("StartDate", new RequestColumn(null, "<factalias>.StartDate", null));
            queryMetadatas[ClickhouseQueryType.rpt_SearchVerticalCategoryClickShareReport].RequestColumns.Add("EndDate", new RequestColumn(null, "<factalias>.EndDate", null));
        }

        private static string FormatJoinType(string joinType)
        {
            return joinType.Replace("<hqsjoin>", "LEFT")
                .Replace("<hqs_hash>", "")
                .Replace("<vorderitem_hash>", "")
                .Replace("<orderitemdata_hash>", "")
                .Replace("<geo_hash_join>", "INNER")
                .Replace("HASH", "");
        }

        private static void FillColumnTransformation(Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas)
        {
            foreach (var queryMetadata in queryMetadatas)
            {
                if (CustomColumnTransformation.ColumnTransformations.ContainsKey(queryMetadata.Key))
                {
                    queryMetadata.Value.ColumnTransformations = CustomColumnTransformation.ColumnTransformations[queryMetadata.Key];
                }
            }

            foreach (var queryMetadata in queryMetadatas)
            {
                string reportName = queryMetadata.Key.ToString();
                if ((reportName.Contains("campaign", StringComparison.OrdinalIgnoreCase) || reportName.Contains("account", StringComparison.OrdinalIgnoreCase)
                    || reportName.Contains("order", StringComparison.OrdinalIgnoreCase) || reportName.Contains("TimeSummary", StringComparison.OrdinalIgnoreCase))
                    && (!reportName.Contains("extension", StringComparison.OrdinalIgnoreCase) && !reportName.Contains("quality", StringComparison.OrdinalIgnoreCase)
                    && !reportName.Contains("measures", StringComparison.OrdinalIgnoreCase) && !reportName.Contains("target", StringComparison.OrdinalIgnoreCase)
                    && !reportName.Contains("prediction", StringComparison.OrdinalIgnoreCase) && !reportName.Contains("experiment", StringComparison.OrdinalIgnoreCase)))
                {
                    if (!queryMetadata.Value.ColumnTransformations.ContainsKey("TotalCost"))
                    {
                        queryMetadata.Value.ColumnTransformations.Add("TotalCost", new ColumnTransformation("TotalAmount,OfflinePhoneCost", "ISNULL(SUM(CAST(TotalAmount AS DECIMAL(28,12))),0) - ISNULL(SUM(CAST(OfflinePhoneCost AS DECIMAL(28,12))),0) AS TotalCost"));
                    }
                    if (!queryMetadata.Value.ColumnTransformations.ContainsKey("Clicks") && !reportName.Contains("ImpressionShare", StringComparison.OrdinalIgnoreCase))
                    {
                        queryMetadata.Value.ColumnTransformations.Add("Clicks", new ColumnTransformation("ClickCnt,OfflinePhoneCallCnt", "SUM(CAST(ClickCnt AS BIGINT)) - ISNULL(SUM(CAST(OfflinePhoneCallCnt AS BIGINT)),0) Clicks"));
                    }

                    if (!queryMetadata.Value.ColumnTransformations.ContainsKey("ConversionEnabledClicks"))
                    {
                        // With new logic to handle the null value in query builder, it should use below logic, will check others later 
                        if (reportName.Contains("rpt_CampaignActivity", StringComparison.OrdinalIgnoreCase))
                        {
                            queryMetadata.Value.ColumnTransformations.Add("ConversionEnabledClicks", new ColumnTransformation("ConversionEnabledClickCnt,ClickCnt,OfflinePhoneCallCnt", "ISNULL(SUM(CASE WHEN ISNULL (ConversionEnabledClickCnt,0) >= 0 THEN ConversionEnabledClickCnt ELSE  ClickCnt END),0) - ISNULL(SUM(CAST(OfflinePhoneCallCnt AS BIGINT)),0) AS ConversionEnabledClicks"));
                        }
                        else
                        {
                            queryMetadata.Value.ColumnTransformations.Add("ConversionEnabledClicks", new ColumnTransformation("ConversionEnabledClickCnt,ClickCnt,OfflinePhoneCallCnt", "ISNULL(SUM(CASE WHEN ISNULL (ConversionEnabledClickCnt,0) > 0 THEN ConversionEnabledClickCnt ELSE  ClickCnt END),0) - ISNULL(SUM(CAST(OfflinePhoneCallCnt AS BIGINT)),0) AS ConversionEnabledClicks"));
                        }
                    }
                }
            }
        }

        private static void CustomizeQueryMetadatas(Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas)
        {
            foreach (var item in BiProcessorDataCategory.DependentReports)
            {
                var reportMetadata = queryMetadatas[item.Key];
                foreach (var dependQueryType in item.Value)
                {
                    var requestColumns = queryMetadatas[dependQueryType].RequestColumns;
                    foreach (var column in requestColumns)
                    {
                        if (!reportMetadata.RequestColumns.ContainsKey(column.Key))
                        {
                            reportMetadata.RequestColumns.Add(column.Key, column.Value);
                        }
                    }
                    var joinConditions = queryMetadatas[dependQueryType].JoinConditions;
                    foreach (var join in joinConditions)
                    {
                        if (!reportMetadata.JoinConditions.ContainsKey(join.Key))
                        {
                            reportMetadata.JoinConditions.Add(join.Key, join.Value);
                        }
                    }
                }
            }
            Constants2.prc_CampaignExperimentSummary_uiCoulumns.ForEach(x => queryMetadatas[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].RequestColumns.Add(x, new RequestColumn(null, $"<factalias>.{x}", null)));
            Constants2.prc_AdExtensionsOrderAssociation_uiCoulumns.ForEach(x => queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns.Add(x, new RequestColumn(null, $"<factalias>.{x}", null)));
            Constants2.prc_AdExtensionsByOrderV2_uiCoulumns.ForEach(x => queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns.Add(x, new RequestColumn(null, $"<factalias>.{x}", null)));
            Constants2.prc_AssetSummary_uiCoulumns.ForEach(x => queryMetadatas[ClickhouseQueryType.prc_AssetSummary_ui].RequestColumns.Add(x, new RequestColumn(null, $"<factalias>.{x}", null)));
            Constants2.prc_AssetCombinationSummary_uiCoulumns.ForEach(x => queryMetadatas[ClickhouseQueryType.prc_AssetCombinationSummary_ui].RequestColumns.Add(x, new RequestColumn(null, $"<factalias>.{x}", null)));
            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns.Add("CurrencyId", new RequestColumn("acc,cur", "cur.CurrencyId", "cur.CurrencyId"));
            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].JoinConditions.Add("cur", new QueryJoinCondition("vCurrencyDim", 489, null, "acc", new List<JoinColumn> { new JoinColumn("CurrencyId", "PreferredCurrencyId") }));

            if (queryMetadatas.TryGetValue(ClickhouseQueryType.prc_GetMeteredCallDetails, out var metadata))
            {
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns = queryMetadatas[ClickhouseQueryType.prc_CallDetailsSummary_Dim].RequestColumns;
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("AccountName", new RequestColumn("va", "AccountName", null));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("AccountStatusName", new RequestColumn("va", "AccountStatusName", null));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("AdGroupStatusName", new RequestColumn("o", "AdGroupStatusName", null));
                //queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("CallStatusName", new RequestColumn(null, null, "CASE WHEN \u003Cfactalias\u003E.CallDuration \u003E 0 then 1 else 0 end"));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("CallTypeName", new RequestColumn(null, "\u003Cfactalias\u003E.IsOfflineCall", "CAST(\u003Cfactalias\u003E.IsOfflineCall as NVARCHAR(50))"));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("GregorianDate", new RequestColumn(null, "\u003Cfactalias\u003E.GregorianDate", null));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("CallReasonName", new RequestColumn(null, "\u003Cfactalias\u003E.CallEndReason", "\u003Cfactalias\u003E.CallEndReason"));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("Region", new RequestColumn(null, "\u003Cfactalias\u003E.Region", null));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Add("City", new RequestColumn(null, "\u003Cfactalias\u003E.City", null));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].RequestColumns.Remove("CallDuration");
                //

                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].JoinConditions = queryMetadatas[ClickhouseQueryType.prc_CallDetailsSummary_Dim].JoinConditions;
                var joinColumns = new List<JoinColumn>() { new JoinColumn("AccountId", "AccountId") };
                metadata.JoinConditions.Add("va", new QueryJoinCondition("vAccount", 580, null, "\u003Cfactalias\u003E", joinColumns));
                queryMetadatas[ClickhouseQueryType.prc_GetMeteredCallDetails].JoinConditions.Add("o", queryMetadatas[ClickhouseQueryType.rpt_AgeGenderSummaryV2].JoinConditions["o"]);
            }

            //remove deprecated columns
            foreach (var item in queryMetadatas)
            {
                if (Constants2.DeprecateColumns.TryGetValue(item.Key, out var deprecateColumns))
                {
                    foreach (var column in deprecateColumns)
                    {
                        item.Value.RequestColumns.Remove(column);
                    }
                }
            }


            if (!queryMetadatas[ClickhouseQueryType.prc_AgeGenderSummary_ui].RequestColumns.Any())
            {
                // copy columns from rpt_AgeGenderSummaryV2 as the metadata is not available for prc_AgeGenderSummary_ui
                queryMetadatas[ClickhouseQueryType.prc_AgeGenderSummary_ui].RequestColumns =
                    queryMetadatas[ClickhouseQueryType.rpt_AgeGenderSummaryV2].RequestColumns;
            }

            if (!queryMetadatas[ClickhouseQueryType.prc_AudienceSummary_ui].RequestColumns.Any())
            {
                // copy columns from prc_AudienceSummary_ui as the metadata is not available for rpt_AudienceSummary
                queryMetadatas[ClickhouseQueryType.prc_AudienceSummary_ui].RequestColumns =
                    queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].RequestColumns.ToDictionary(entry => entry.Key,
                        entry => entry.Value);
                queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns =
                    queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].RequestColumns.ToDictionary(entry => entry.Key,
                        entry => entry.Value);
                queryMetadatas[ClickhouseQueryType.prc_AudienceSummary_ui].RequestColumns.Add("QuarterStartDate", new RequestColumn(null, "<factalias>.QuarterStartDate", null));
                queryMetadatas[ClickhouseQueryType.prc_AudienceSummary_ui].RequestColumns["AudienceId"] = new RequestColumn(null, "<factalias>.TargetValueId", "<factalias>.TargetValueId");
            }

            // fix the wrong metadata, was vTargetGroupDetail
            queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].JoinConditions["tgtgrpdet"].JoinTableName =
                "vTargetGroupDetailDim";
            
            
            queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].JoinConditions["tgtgrpdet"].JoinColumns =
                queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].JoinConditions["tgtgrpdet"].JoinColumns
                    .Where(c => !c.LeftColumn.Equals("OrderId")).ToList();
            

            // fix the wrong metadata,
            queryMetadatas[ClickhouseQueryType.rpt_DSAAutoTargetCategorySummary].JoinConditions["category"].JoinTableName =
                "vAdLandingPageUrlSecondLevelCategory";
            queryMetadatas[ClickhouseQueryType.prc_PerformanceTargetSummary_ui].RequestColumns.Add("DateKey", new RequestColumn(null, "<factalias>.DateKey", null));
            queryMetadatas[ClickhouseQueryType.prc_PerformanceTargetSummary_ui].RequestColumns["PTId"] =
                new RequestColumn("p", "PTId", null);
            queryMetadatas[ClickhouseQueryType.prc_PerformanceTargetSummary_ui].JoinConditions
                .Add("p", new QueryJoinCondition("@PerfTargets", 999, null, "\u003Cfactalias\u003E", new List<JoinColumn>()
                {
                   new JoinColumn("CampaignId", "CampaignId")
                }));

            //fix shopping procs metadata
            queryMetadatas[ClickhouseQueryType.prc_ProductTabSummaryForAccountCampaign_ui].RequestColumns.Add("GlobalOfferId", new RequestColumn(null, "<factalias>.GlobalOfferId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductTabSummaryForAccountCampaign_ui].RequestColumns.Add("CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummary_V1_ui].RequestColumns.Add("AccountId", new RequestColumn(null, "<factalias>.AccountId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummary_V1_ui].RequestColumns.Add("CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummary_V1_ui].RequestColumns.Add("OrderId", new RequestColumn(null, "<factalias>.OrderId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummary_V1_ui].RequestColumns.Add("ProductOfferId", new RequestColumn(null, "<factalias>.ProductOfferId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummary_V1_ui].RequestColumns.Add("ChannelTypeId", new RequestColumn(null, "<factalias>.ChannelTypeId", null));

            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummaryForAccountCampaignOrder_ui].RequestColumns.Add("AccountId", new RequestColumn(null, "<factalias>.AccountId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummaryForAccountCampaignOrder_ui].RequestColumns.Add("CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummaryForAccountCampaignOrder_ui].RequestColumns.Add("OrderId", new RequestColumn(null, "<factalias>.OrderId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummaryForAccountCampaignOrder_ui].RequestColumns.Add("ProductOfferId", new RequestColumn(null, "<factalias>.ProductOfferId", null));
            queryMetadatas[ClickhouseQueryType.prc_ProductGroupSummaryForAccountCampaignOrder_ui].RequestColumns.Add("ChannelTypeId", new RequestColumn(null, "<factalias>.ChannelTypeId", null));

            queryMetadatas[ClickhouseQueryType.prc_GetPGCriteriaSelectionSummary_Perf_ui].RequestColumns.Add("AccountId", new RequestColumn(null, "<factalias>.AccountId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetPGCriteriaSelectionSummary_Perf_ui].RequestColumns.Add("CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetPGCriteriaSelectionSummary_Perf_ui].RequestColumns.Add("OrderId", new RequestColumn(null, "<factalias>.OrderId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetPGCriteriaSelectionSummary_Perf_ui].RequestColumns.Add("ProductOfferId", new RequestColumn(null, "<factalias>.ProductOfferId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetPGCriteriaSelectionSummary_Perf_ui].RequestColumns.Add("ChannelTypeId", new RequestColumn(null, "<factalias>.ChannelTypeId", null));

            queryMetadatas[ClickhouseQueryType.rpt_ProductPartitionSummaryV2].RequestColumns["MediumId"] = new RequestColumn("mdm,cam", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS NVARCHAR(50)) ELSE CAST(ifZeroOrNull(<factalias>.MediumId, 0) AS NVARCHAR(50)) END", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS NVARCHAR(50)) ELSE CAST(ifZeroOrNull(<factalias>.MediumId, 0) AS NVARCHAR(50)) END");
            queryMetadatas[ClickhouseQueryType.rpt_ProductPartitionSummaryV2].RequestColumns["MediumName"] = new RequestColumn("mdm,cam", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS NVARCHAR(50)) ELSE CAST(ifZeroOrNull(<factalias>.MediumId, 0) AS NVARCHAR(50)) END", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS NVARCHAR(50)) ELSE CAST(ifZeroOrNull(<factalias>.MediumId, 0) AS NVARCHAR(50)) END");

            if (!queryMetadatas[ClickhouseQueryType.prc_ProfileSummary_ui].RequestColumns.Any())
            {
                // copy columns from prc_ProfileSummary_ui as the metadata is not available for rpt_ProfileSummary
                queryMetadatas[ClickhouseQueryType.prc_ProfileSummary_ui].RequestColumns =
                    queryMetadatas[ClickhouseQueryType.rpt_ProfileSummary].RequestColumns.ToDictionary(
                        entry => entry.Key,
                        entry => entry.Value);
            }

            queryMetadatas[ClickhouseQueryType.prc_CampaignSummary_ui].RequestColumns["PagePositionId2"] = new RequestColumn("cam",
    "cam.AdvertisingChannelTypeId,f.PagePositionId2",
"CAST( IF( cam.AdvertisingChannelTypeId=9,254, <factalias>.PagePositionId2) AS INT)");

            queryMetadatas[ClickhouseQueryType.prc_CampaignSummary_ui].RequestColumns["NetworkId"] = new RequestColumn("cam",
    "cam.AdvertisingChannelTypeId,f.NetworkId",
"CAST( IF( cam.AdvertisingChannelTypeId=9,255,  <factalias>.NetworkId) AS INT)");

            queryMetadatas[ClickhouseQueryType.prc_CampaignSummary_ui].RequestColumns["MediumId"] = new RequestColumn("cam",
    "cam.AdvertisingChannelTypeId,f.MediumId",
"CAST( IF( cam.AdvertisingChannelTypeId=9,255,  <factalias>.MediumId) AS INT)");
            queryMetadatas[ClickhouseQueryType.prc_CampaignSummary_ui].JoinConditions.Add("cam", new QueryJoinCondition("vCampaignType", 999,null,"\u003Cfactalias\u003E", new List<JoinColumn>() { new("CampaignId", "CampaignId") }));

            queryMetadatas[ClickhouseQueryType.prc_GetSearchPhraseSummary].RequestColumns.Add("SearchPhrase", new RequestColumn("null", "<factalias>.SearchPhrase", null));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchPhraseSummary].RequestColumns.Add("OrderId", new RequestColumn("null", "<factalias>.OrderId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchPhraseSummary].RequestColumns.Add("CampaignId", new RequestColumn("null", "<factalias>.CampaignId", null));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchPhraseSummary].RequestColumns.Add("AccountId", new RequestColumn("null", "<factalias>.AccountId", null));

            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns["GroupId"] = new RequestColumn("TvpAccountIdGroup", "<factalias>.AccountId", "TvpAccountIdGroup.GroupId");
            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].JoinConditions.Add("TvpAccountIdGroup",
                new QueryJoinCondition("TvpAccountIdGroup", 1489, null, "\u003Cfactalias\u003E",
                    new List<JoinColumn> { new("AccountId", "AccountId") }));
            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns["PagePositionId2"] = new RequestColumn(null,
    "f.AdvertisingChannelTypeId,f.NetworkId,f.PagePositionId",
"CAST( IF( <factalias>.AdvertisingChannelTypeId=9,254, IF(<factalias>.NetworkId IN (0, 4, 5), <factalias>.NetworkId * 10, <factalias>.NetworkId * 10 + <factalias>.PagePositionId)) AS INT)");

            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns["NetworkId"] = new RequestColumn(null,
    "f.AdvertisingChannelTypeId,f.NetworkId",
"CAST( IF( <factalias>.AdvertisingChannelTypeId=9,255,  <factalias>.NetworkId) AS INT)");

            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns["MediumId"] = new RequestColumn(null,
    "f.AdvertisingChannelTypeId,f.MediumId",
"CAST( IF( <factalias>.AdvertisingChannelTypeId=9,255,  <factalias>.MediumId) AS INT)");


            queryMetadatas[ClickhouseQueryType.prc_AccountImpressionShare_ui].RequestColumns["GroupId"] = new RequestColumn("TvpAccountIdGroup", "<factalias>.AccountId", "TvpAccountIdGroup.GroupId");
            queryMetadatas[ClickhouseQueryType.prc_AccountImpressionShare_ui].JoinConditions.Add("TvpAccountIdGroup",
                new QueryJoinCondition("TvpAccountIdGroup", 1489, null, "\u003Cfactalias\u003E",
                    new List<JoinColumn> { new("AccountId", "AccountId") }));

            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns["GoalTypeId"] = new RequestColumn(null, "<factalias>.GoalTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns.Add("QuarterStartDate", new RequestColumn(null, "<factalias>.QuarterStartDate", null));
            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns.Add("PagePositionId2", new RequestColumn("cam", "<factalias>.PagePositionId2", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(254 AS INT) ELSE PagePositionId2 END "));
            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].JoinConditions.Add("cam", new QueryJoinCondition("vCampaignType", 999, null, "\u003Cfactalias\u003E", new List<JoinColumn>() { new("CampaignId", "CampaignId") }));
            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns["NetworkId"] = new RequestColumn("cam",
"cam.AdvertisingChannelTypeId,f.NetworkId",
"CAST( IF( cam.AdvertisingChannelTypeId=9,255,  <factalias>.NetworkId) AS INT)");

            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["GregorianDate"] = new RequestColumn(null, "<factalias>.GregorianDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["HourNum"] = new RequestColumn(null, "<factalias>.HourNum", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["HourOfDay"] = new RequestColumn(null, "<factalias>.HourNum", "<factalias>.HourNum");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["QuarterStartDate"] = new RequestColumn(null, "<factalias>.QuarterStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["WeekStartDate"] = new RequestColumn(null, "<factalias>.WeekStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["WeekStartDateMonday"] = new RequestColumn(null, "<factalias>.WeekStartDateMonday", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["YearNum"] = new RequestColumn(null, "<factalias>.YearNum", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["MonthStartDate"] = new RequestColumn(null, "<factalias>.MonthStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["DateKey"] = new RequestColumn(null, "<factalias>.DateKey", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["DayOfWeek"] = new RequestColumn(null, "<factalias>.DayOfWeek", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["DeviceTypeId"] = new RequestColumn(null, "<factalias>.DeviceTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["ClickTypeId"] = new RequestColumn(null, "<factalias>.ClickTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["TargetValueId"] = new RequestColumn(null, "<factalias>.TargetValueId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["NetworkId"] = new RequestColumn("cam", "<factalias>.NetworkId", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS UInt8) ELSE NetworkId END ");
          //  queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["MediumId"] = new RequestColumn("cam", "<factalias>.MediumId", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS INT) ELSE MediumId END ");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["PagePositionId2"] = new RequestColumn("cam", "<factalias>.PagePositionId2", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(254 AS INT) ELSE PagePositionId2 END ");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].JoinConditions.Add("cam", new QueryJoinCondition("vCampaignType", 999, null, "<factalias>", new List<JoinColumn>() { new("CampaignId", "CampaignId"), new("AccountId", "AccountId") }));
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["MatchTypeId"] = new RequestColumn(null, "<factalias>.MatchTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["BiddedMatchTypeId"] = new RequestColumn(null, "<factalias>.BiddedMatchTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsOrderAssociation_ui].RequestColumns["TargetTypeId"] = new RequestColumn(null, "<factalias>.TargetTypeId", null);
          
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["TargetValueId"] = new RequestColumn(null, "<factalias>.TargetValueId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["NetworkId"] = new RequestColumn("cam", "<factalias>.NetworkId", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS UInt8) ELSE NetworkId END ");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["MediumId"] = new RequestColumn("cam", "<factalias>.MediumId", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(255 AS INT) ELSE MediumId END ");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["PagePositionId2"] = new RequestColumn("cam", "<factalias>.PagePositionId2", "CASE WHEN cam.AdvertisingChannelTypeId = 9 THEN CAST(254 AS INT) ELSE PagePositionId2 END ");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].JoinConditions.Add("cam", new QueryJoinCondition("vCampaignType", 999, null, "<factalias>", new List<JoinColumn>() { new("CampaignId", "CampaignId"), new("AccountId", "AccountId") }));

            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("IncludeIsOther", "bit", false));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("KeywordOrderId", "TVPBigInt", false));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("NumOfWeeks", "INT", false));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("MaxRows", "INT", false));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].RequestColumns["MatchTypeId"] = new RequestColumn(null, "<factalias>.IsOther,<factalias>.MatchTypeId", "CAST(IF(<factalias>.IsOther =0,<factalias>.MatchTypeId,1) AS TINYINT UNSIGNED)");
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("CustomColumns", "tvpCustomColumns", true));
            queryMetadatas[ClickhouseQueryType.prc_GetSearchQuerySummaryLite].QueryParameters.Add(new QueryParameter("KeywordAndBiddedMatchTypeIds", "tvpBigIntTinyInt", true));

            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["GregorianDate"] = new RequestColumn(null, "<factalias>.GregorianDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["HourNum"] = new RequestColumn(null, "<factalias>.HourNum", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["HourOfDay"] = new RequestColumn(null, "<factalias>.HourNum", "<factalias>.HourNum");
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["QuarterStartDate"] = new RequestColumn(null, "<factalias>.QuarterStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["WeekStartDate"] = new RequestColumn(null, "<factalias>.WeekStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["WeekStartDateMonday"] = new RequestColumn(null, "<factalias>.WeekStartDateMonday", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["YearNum"] = new RequestColumn(null, "<factalias>.YearNum", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["MonthStartDate"] = new RequestColumn(null, "<factalias>.MonthStartDate", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["DateKey"] = new RequestColumn(null, "<factalias>.DateKey", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["DayOfWeek"] = new RequestColumn(null, "<factalias>.DayOfWeek", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["DeviceTypeId"] = new RequestColumn(null, "<factalias>.DeviceTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["MatchTypeId"] = new RequestColumn(null, "<factalias>.MatchTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["BiddedMatchTypeId"] = new RequestColumn(null, "<factalias>.BiddedMatchTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["GoalId"] = new RequestColumn(null, "<factalias>.GoalId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["TargetTypeId"] = new RequestColumn(null, "<factalias>.TargetTypeId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["TargetValueId"] = new RequestColumn(null, "<factalias>.TargetValueId", null);
            queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns["GoalTypeId"] = new RequestColumn(null, "<factalias>.GoalTypeId", null);

            //fix PagePositionId2, ToVerify, probably  needed some change
            queryMetadatas[ClickhouseQueryType.prc_AdSummary_ui].RequestColumns.Add("PagePositionId2", new RequestColumn(null, "<factalias>.PagePositionId2", null));
            queryMetadatas[ClickhouseQueryType.prc_OrderSummary_ui].RequestColumns.Add("PagePositionId2", new RequestColumn(null, "<factalias>.PagePositionId2", null));

           // queryMetadatas[ClickhouseQueryType.prc_AdExtensionsByOrderV2_ui].RequestColumns.Add("DeviceOSId", new RequestColumn(null, "<factalias>.DeviceOSId", null));


            //fix hotel procs metadata
            queryMetadatas[ClickhouseQueryType.prc_HotelVerticalSummary_ui].RequestColumns.Add("VerticalCampaignId", new RequestColumn(null, "<factalias>.VerticalCampaignId", null));

            queryMetadatas[ClickhouseQueryType.prc_GeoLocationSummary_Dim].RequestColumns["TargetedLocationTypeId"] = new RequestColumn(null, "<factalias>.TargetedLocationTypeId", "CAST(<factalias>.TargetedLocationTypeId AS INT)");

            // fix app summary metadata
            queryMetadatas[ClickhouseQueryType.rpt_AppSummary].RequestColumns["CampaignType"] = new RequestColumn("cam", "AdvertisingChannelTypeId,CampaignFeatureBitMask", "CAST(CASE WHEN bitAnd(cam.CampaignFeatureBitMask, 128) = 128 THEN 10 ELSE ifZeroOrNull(cam.AdvertisingChannelTypeId, 1) END AS NVARCHAR(20))");
            queryMetadatas[ClickhouseQueryType.prc_PublisherPlacementSummary_Dim].RequestColumns["CampaignType"] = new RequestColumn("cam", "AdvertisingChannelTypeId,CampaignFeatureBitMask", "CAST(CASE WHEN bitAnd(cam.CampaignFeatureBitMask, 128) = 128 THEN 10 ELSE ifZeroOrNull(cam.AdvertisingChannelTypeId, 1) END AS NVARCHAR(20))");
            queryMetadatas[ClickhouseQueryType.rpt_PublisherPlacementSummaryV2].RequestColumns["CampaignType"] = new RequestColumn("cam", "AdvertisingChannelTypeId,CampaignFeatureBitMask", "CAST(CASE WHEN bitAnd(cam.CampaignFeatureBitMask, 128) = 128 THEN 10 ELSE ifZeroOrNull(cam.AdvertisingChannelTypeId, 1) END AS NVARCHAR(20))");

            queryMetadatas[ClickhouseQueryType.prc_AccountSummary_ui].RequestColumns["DeviceOSId"] = new RequestColumn(null, "<factalias>.SOSId", "CAST(<factalias>.SOSId as smallint)");

            // fix BudgetStatusName
            queryMetadatas[ClickhouseQueryType.rpt_CampaignActivity].RequestColumns["BudgetStatusName"] = new RequestColumn("bgt", "bgt.BudgetPauseTypeId", "CAST(bgt.BudgetPauseTypeId AS Nullable(NVARCHAR(25)))");
            queryMetadatas[ClickhouseQueryType.rpt_CampaignImpressionSharePerf].RequestColumns["BudgetStatusName"] = new RequestColumn("bgt", "bgt.BudgetPauseTypeId", "CAST(bgt.BudgetPauseTypeId AS Nullable(NVARCHAR(25)))");
            queryMetadatas[ClickhouseQueryType.rpt_CampaignPerf].RequestColumns["BudgetStatusName"] = new RequestColumn("bgt", "bgt.BudgetPauseTypeId", "CAST(bgt.BudgetPauseTypeId AS Nullable(NVARCHAR(25)))");

            // fix GoalTypeId, as join_use_nulls = 0 need to convert 0 to null
            queryMetadatas[ClickhouseQueryType.rpt_CampaignActivity].RequestColumns["GoalTypeId"].ColumnExpression = "CASE WHEN `vg`.GoalTypeId != 0 THEN `vg`.GoalTypeId WHEN `vg`.GoalTypeId = 0 AND `vg`.GoalId > 0 THEN 0 ELSE NULL END";
            queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].RequestColumns["GoalTypeId"].ColumnExpression = "CASE WHEN `vg`.GoalTypeId != 0 THEN `vg`.GoalTypeId WHEN `vg`.GoalTypeId = 0 AND `vg`.GoalId > 0 THEN 0 ELSE NULL END";
            queryMetadatas[ClickhouseQueryType.rpt_OrderSummary].RequestColumns["GoalTypeId"].ColumnExpression = "CASE WHEN `vg`.GoalTypeId != 0 THEN `vg`.GoalTypeId WHEN `vg`.GoalTypeId = 0 AND `vg`.GoalId > 0 THEN 0 ELSE NULL END";

            // fix GoalTypeId and GoalName for rpt_KeywordAuctionSummary
            queryMetadatas[ClickhouseQueryType.rpt_KeywordAuctionSummary].RequestColumns["GoalTypeId"] = new RequestColumn("vg", "vg.GoalTypeId", null);
            queryMetadatas[ClickhouseQueryType.rpt_KeywordAuctionSummary].RequestColumns["GoalName"] = new RequestColumn("vg", "vg.GoalName", null);


            //reach
           // queryMetadatas[ClickhouseQueryType.rpt_CampaignActivity].BIDataCategories.Add(BiDataCategory.CampaignReachUsage);

            //BidAdjustment
            queryMetadatas[ClickhouseQueryType.prc_AudienceSummary_ui].RequestColumns["BidAdjustment"] = new RequestColumn("tgtgrpdet", "tgtgrpdet.IncrementPct", "CAST(tgtgrpdet.IncrementPct AS Nullable(Decimal(38,13)))");
            queryMetadatas[ClickhouseQueryType.prc_TargetSummaryV2_ui].RequestColumns["BidAdjustment"] = new RequestColumn("tgtgrpdet", "tgtgrpdet.IncrementPct", "CAST(tgtgrpdet.IncrementPct AS Nullable(Decimal(38,13)))");
            queryMetadatas[ClickhouseQueryType.rpt_AudienceSummary].RequestColumns["BidAdjustment"] = new RequestColumn("tgtgrpdet", "tgtgrpdet.IncrementPct", "CAST(tgtgrpdet.IncrementPct AS Nullable(Decimal(38,13)))");


            //MediumName
            queryMetadatas[ClickhouseQueryType.rpt_AccountActivity].RequestColumns["MediumName"].JoinTableAlias = null;
            queryMetadatas[ClickhouseQueryType.rpt_AccountImpressionSharePerf].RequestColumns["MediumName"].JoinTableAlias = null;


            //fix rpt_AccountActivity
            queryMetadatas[ClickhouseQueryType.rpt_AccountActivity].RequestColumns["MediumName"] = new RequestColumn(null, "CAST(<factalias>.MediumId as Nullable(NVARCHAR(50)))", null);
            queryMetadatas[ClickhouseQueryType.rpt_AccountActivity].RequestColumns["MediumId"] = new RequestColumn(null, "CAST(<factalias>.MediumId as Nullable(NVARCHAR(50)))", null);
            queryMetadatas[ClickhouseQueryType.rpt_AccountActivity].RequestColumns["NetworkId"] = new RequestColumn(null, "<factalias>.NetworkId", null);
            queryMetadatas[ClickhouseQueryType.rpt_AccountActivity].RequestColumns["PagePositionId2"] = new RequestColumn(null, "<factalias>.PagePositionId2", null);



            // fix asset summary metadata
            var assetType = queryMetadatas[ClickhouseQueryType.rpt_AssetSummary].RequestColumns["AssetType"];
            queryMetadatas[ClickhouseQueryType.rpt_AssetSummary].RequestColumns["AssetType"] = new RequestColumn(assetType.JoinTableAlias, $"CAST({assetType.DependentColumnName} AS INT)", $"CAST({assetType.ColumnExpression} AS INT)");
            var assetSource = queryMetadatas[ClickhouseQueryType.rpt_AssetSummary].RequestColumns["AssetSource"];
            queryMetadatas[ClickhouseQueryType.rpt_AssetSummary].RequestColumns["AssetSource"] = new RequestColumn(assetSource.JoinTableAlias, $"CAST({assetSource.DependentColumnName?.Replace("GET_BIT", "bitTest")} AS INT)", $"CAST({assetSource.ColumnExpression?.Replace("GET_BIT", "bitTest")} AS INT)");

            //dayofweek
            queryMetadatas[ClickhouseQueryType.rpt_HotelSummary].RequestColumns["CheckInDateDayOfWeek"] = new RequestColumn(null, $"<factalias>.CheckInDate", $"CAST(toDayOfWeek( <factalias>.CheckInDate, 3) AS SMALLINT) ");

            //fix Rpt_LocationActivitySummary
            queryMetadatas[ClickhouseQueryType.rpt_LocationActivitySummary].JoinConditions["order"].JoinType = "INNER";
            queryMetadatas[ClickhouseQueryType.rpt_LocationActivitySummary].JoinConditions["ko"].JoinType = "INNER";
        }


        /// <summary>
        /// Generates C# code for column default values by querying the database schema.
        /// </summary>
        /// <param name="outputFilePath">Path where the generated code will be written</param>
        public static void GenerateCodeForColumnDefaultValues(string outputFilePath)
        {
            if (string.IsNullOrWhiteSpace(outputFilePath))
                throw new ArgumentException("Output file path cannot be null or empty", nameof(outputFilePath));

            string codeTemplate = @"using System;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public partial class ColumnMap
    {
        public static Dictionary<string, string> ColumnDefaultValues = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {};
    }
}
";

            var columnDefaultValues = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            using var conn = QueryUtility2.GetCISqlConnection();
            conn.Open();

            const string cmdStr = @"
                SELECT DISTINCT
                    LOWER(COLUMN_NAME) AS ColumnName,
                    SUBSTRING(COLUMN_DEFAULT, 2, LEN(COLUMN_DEFAULT) - 2) AS DefaultValue
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'dbo'
                  AND COLUMN_DEFAULT IS NOT NULL
                  AND TABLE_NAME NOT LIKE '%Delta%'
                  AND TABLE_NAME NOT LIKE '%_Archive%'
                  AND TABLE_NAME NOT LIKE '%External_%'
                  AND TABLE_NAME NOT LIKE '%InProgress%'
                  AND TABLE_NAME NOT LIKE '%NotReadyForProd%'
                  AND TABLE_NAME NOT LIKE '%Temp_%'
                  AND TABLE_NAME NOT LIKE '%_NoIS%'
                  AND TABLE_NAME NOT LIKE '%_Migrate%'
                  AND TABLE_NAME NOT LIKE '%Procedure%'
                ORDER BY ColumnName ASC";

            using var cmd = new SqlCommand(cmdStr, conn);
            using var dbReader = cmd.ExecuteReaderAsync().GetAwaiter().GetResult();

            while (dbReader.Read())
            {
                var columnName = dbReader.GetString(0);
                var defaultValue = dbReader.GetString(1);

                if (columnDefaultValues.TryGetValue(columnName, out var existingValue))
                {
                    // Prefer non-getdate() values over getdate()
                    if (existingValue.Equals("getdate()", StringComparison.OrdinalIgnoreCase) &&
                        !defaultValue.Equals("getdate()", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDefaultValues[columnName] = defaultValue;
                    }
                }
                else
                {
                    columnDefaultValues[columnName] = defaultValue;
                }
            }

            // Generate the dictionary entries
            StringBuilder sb = new StringBuilder();
            sb.Append("{");
            sb.AppendLine();

            foreach (var column in columnDefaultValues)
            {
                sb.Append(@"            { ").Append($"\"{column.Key}\"").Append(",").Append($"\"{column.Value}\"").Append(@" },");
                sb.AppendLine();
            }
            sb.Remove(sb.Length - 3, 3);
            sb.AppendLine();
            sb.Append(@"        };");

            var finalCode = codeTemplate.Replace(@"{};", sb.ToString());
            File.WriteAllText(outputFilePath, finalCode);
        }

        public static Dictionary<ClickhouseQueryType, ReportColumns> GenerateColumnsForQueries(string outputFilePath, Dictionary<BiDataCategory, BiDataGroup> biDataGroups, Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas)
        {
            var columnsForQueries = new Dictionary<ClickhouseQueryType, ReportColumns>();
            foreach (var queryMetadata in queryMetadatas)
            {
                var dimensions = queryMetadata.Value.RequestColumns.Select(x => {
                    if (queryMetadata.Key == ClickhouseQueryType.rpt_AccountActivity)
                    {
                        if (!string.IsNullOrWhiteSpace(x.Value.JoinTableAlias) && (x.Value.JoinTableAlias.Equals("oi") || x.Value.JoinTableAlias.Equals("vDC") || x.Value.JoinTableAlias.Equals("vOC") || x.Value.JoinTableAlias.Equals("ko,vkoi")))
                        {
                            return string.Empty;
                        }
                    }
                    return x.Key;
                }).Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                var allColumns = new List<string>();
                for (int i = 0; i < queryMetadata.Value.BIDataCategories.Count; i++)
                {
                    var category = queryMetadata.Value.BIDataCategories[i];
                    allColumns.AddRange(biDataGroups[category].Columns.Select(x => !string.IsNullOrWhiteSpace(x.Value.ColumnAlias) ? x.Value.ColumnAlias : x.Key));
                }
                // Use default measure configuration for static method
                var measureConfig = new MeasureConfiguration();
                var measures = allColumns.Except(dimensions).Except(measureConfig.NonRequestMeasures).ToList();
                columnsForQueries.Add(queryMetadata.Key, new ReportColumns(dimensions, measures));
            }

            //customize for specific reports
            columnsForQueries[ClickhouseQueryType.prc_CampaignSummary_ui].Measures.Remove("DistributionChannelId");
            columnsForQueries[ClickhouseQueryType.rpt_CampaignActivity].Measures.Remove("DistributionChannelId");
            columnsForQueries[ClickhouseQueryType.prc_AssetCombinationSummary_ui].Measures.Remove("CTA");
            columnsForQueries[ClickhouseQueryType.prc_AssetCombinationSummary_ui].Measures = new List<string>() { "Impressions", "Clicks", "TotalCost", "Conversions" };
            columnsForQueries[ClickhouseQueryType.prc_TimeSummary_Dim].Dimensions = new List<string>() { "QuarterStartDate", "GregorianDate", "MonthStartDate", "WeekStartDate", "DayOfWeek", "HourOfDay", "YearNum" };
            //columnsForQueries[ClickhouseQueryType.prc_GetMeteredCallDetails].Measures = new List<string>() { "PhoneCost", "CallDuration" };
            columnsForQueries[ClickhouseQueryType.prc_KeywordSummary_ui].Measures.AddRange(new List<string>() { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" });
            columnsForQueries[ClickhouseQueryType.prc_CampaignSummary_ui].Measures.AddRange(new List<string>() { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" });
            columnsForQueries[ClickhouseQueryType.prc_OrderSummary_ui].Measures.AddRange(new List<string>() { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" });
            columnsForQueries[ClickhouseQueryType.prc_AccountSummary_ui].Measures.AddRange(new List<string>() { "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment" });
            //CookieCampaignExperimentUsage
            columnsForQueries[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].Measures.Add("AdvertiserReportedRevenue");
            columnsForQueries[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].Measures.Add("ConversionValueCnt");
            columnsForQueries[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].Measures.Add("FullConversionValueCnt");

            var options = new JsonSerializerOptions();
            options.Converters.Add(new JsonStringEnumConverter());
            options.WriteIndented = true;
            string columnsForQueriesJson = JsonSerializer.Serialize(columnsForQueries, options);
            var writer = new StreamWriter(outputFilePath);
            writer.Write(columnsForQueriesJson);
            writer.Close();

            return columnsForQueries;
        }

        private static string ReplaceWithCHColumns(string expression, Dictionary<string, string> allCHBiDataColumns)
        {
            // Use default configuration for static method
            var defaultConfig = new BiMetadataConfiguration();

            StringBuilder sb = new StringBuilder();
            string token = string.Empty;
            for (int i = 0; i < expression.Length; i++)
            {
                if (expression[i] == '.' || expression[i] == '(')
                {
                    sb.Append(token);
                    token = string.Empty;
                    sb.Append(expression[i]);
                }
                else if (expression[i] == ',' || expression[i] == ' ' || expression[i] == ')')
                {
                    if (defaultConfig.NonColumns.Contains(token.ToLower()) || token.IndexOfAny(defaultConfig.SpecialSymbols) != -1)
                    {
                        sb.Append(token);
                    }
                    else if (!string.IsNullOrEmpty(token))
                    {
                        if (allCHBiDataColumns.ContainsKey(token.ToLower()))
                        {
                            sb.Append(allCHBiDataColumns[token.ToLower()]);
                        }
                        else
                        {
                            sb.Append(token);
                        }
                    }
                    token = string.Empty;
                    sb.Append(expression[i]);
                }
                else
                {
                    token += expression[i];
                }
            }

            if (defaultConfig.NonColumns.Contains(token.ToLower()) || token.IndexOfAny(defaultConfig.SpecialSymbols) != -1)
            {
                sb.Append(token);
            }
            else if (!string.IsNullOrEmpty(token))
            {
                if (allCHBiDataColumns.ContainsKey(token.ToLower()))
                {
                    sb.Append(allCHBiDataColumns[token.ToLower()]);
                }
                else
                {
                    sb.Append(token);
                }
            }
            return sb.ToString();
        }
    }
}
