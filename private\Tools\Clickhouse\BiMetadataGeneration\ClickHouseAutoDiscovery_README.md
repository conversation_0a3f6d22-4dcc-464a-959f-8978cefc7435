# ClickHouse Auto-Discovery Integration

## 概述

本文档描述了在 `BiMetadataGenerator.GenerateBiDataGroups` 函数中集成的 ClickHouse 自动发现功能。该功能能够自动从 ClickHouse 系统表中发现表结构和列信息，并生成相应的 BiDataGroup 元数据。

## 功能特性

### 1. 自动表发现
- 自动搜索匹配特定模式的表名
- 支持以下表类型模式：
  - `InProgress{FactGroupName}` (位掩码: 1)
  - `Hourly{FactGroupName}` (位掩码: 2)  
  - `Daily{FactGroupName}` (位掩码: 4)
  - `Monthly{FactGroupName}` (位掩码: 8)
  - `Hourly{FactGroupName}_Conv` (位掩码: 16)

### 2. 自动列发现
- 从发现的表中提取所有列信息
- 计算每列在不同表中的存在位掩码
- 支持列名大小写映射

### 3. 元数据生成
- 自动生成 `BiDataGroup` 对象
- 设置转换表名称（如果存在 `_Conv` 表）
- 配置流式处理和转换数据标志

## 代码更改

### 主要修改的文件

#### 1. `BiMetadataGenerator.cs`
- **方法签名更改**: `GenerateBiDataGroups` 现在是异步方法
- **新增方法**:
  - `DiscoverAndAddClickHouseFactGroups`: 主要的发现协调方法
  - `DiscoverFactGroupFromClickHouse`: 发现特定事实组的方法
  - `CalculateColumnBitMaskAsync`: 异步计算列位掩码的方法

#### 2. `Program.cs`
- **方法签名更改**: `Main` 方法现在是异步的
- **调用更新**: 更新了对 `GenerateBiDataGroups` 的调用以支持异步

### 新增的发现逻辑

```csharp
// 在 GenerateBiDataGroups 方法中添加的代码
// Add ClickHouse auto-discovery for ContentPerformanceUsage and other fact groups
await DiscoverAndAddClickHouseFactGroups(biDataGroups, allCHColumnsDic);
```

### 配置的事实组

当前配置为自动发现以下事实组：
- `ContentPerformanceUsage`: 对应 `BiDataCategory.ContentPerformanceUsage`

## 使用方法

### 1. 基本使用
```csharp
var allCHColumnsDic = BiMetadataGenerator.GenerateAllCHBiDataColumns(allColumnsJsonFilePath);
var biDataGroups = await BiMetadataGenerator.GenerateBiDataGroups(biDataGroupsJsonFilePath, allCHColumnsDic);
```

### 2. 测试功能
使用提供的测试类：
```csharp
await TestClickHouseDiscovery.TestDiscoveryAsync();
```

## 错误处理

- **连接失败**: 如果 ClickHouse 连接失败，会记录警告但不会中断整个元数据生成过程
- **表不存在**: 如果指定的表不存在，会跳过该事实组的发现
- **列查询失败**: 单个列查询失败不会影响其他列的处理

## 配置要求

### ClickHouse 连接
- 使用 `QueryUtility2.GetClickhouseConnection()` 获取连接
- 默认连接到配置的 ClickHouse 实例

### 数据库权限
需要以下 ClickHouse 权限：
- 读取 `system.tables` 表
- 读取 `system.columns` 表
- 访问目标数据库中的表

## 扩展性

### 添加新的事实组
在 `DiscoverAndAddClickHouseFactGroups` 方法中的 `factGroupsToDiscover` 字典中添加新条目：

```csharp
var factGroupsToDiscover = new Dictionary<BiDataCategory, string>
{
    { BiDataCategory.ContentPerformanceUsage, "ContentPerformanceUsage" },
    { BiDataCategory.YourNewFactGroup, "YourNewFactGroupName" }
};
```

### 自定义表模式
可以修改 `tablePatterns` 数组来支持不同的表命名模式。

## 性能考虑

- **异步操作**: 所有数据库操作都是异步的，避免阻塞主线程
- **连接管理**: 使用 `using` 语句确保连接正确释放
- **批量查询**: 尽可能使用批量查询减少数据库往返次数

## 故障排除

### 常见问题

1. **连接超时**
   - 检查 ClickHouse 服务是否运行
   - 验证连接字符串配置

2. **表未发现**
   - 确认表名符合预期模式
   - 检查数据库名称是否正确

3. **权限错误**
   - 验证数据库用户权限
   - 确认可以访问系统表

### 调试信息
启用控制台输出查看详细的发现过程信息。

## 未来改进

1. **配置化**: 将事实组发现配置移到外部配置文件
2. **缓存**: 添加发现结果缓存以提高性能
3. **并行处理**: 并行处理多个事实组的发现
4. **更多表模式**: 支持更灵活的表命名模式
