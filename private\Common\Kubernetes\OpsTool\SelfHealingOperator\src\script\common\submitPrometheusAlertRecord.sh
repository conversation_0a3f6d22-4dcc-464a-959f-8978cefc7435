#!/bin/bash

BASEDIR=$(dirname "$0")
source $BASEDIR/../utils/utils.sh

Help()
{
   # 1. Define base info
   local overview="Submit audit log record for any Prometheus alert with standardized information"
   local author="Ying Liu"

   # 2. Define supported options
   local supported_opts=("--alertName" "-n" "-p" "-w" "--requestId")

   # 3. Define sample with script name
   local script_name=$(basename "$0")
   local sample="$script_name --alertName HighCPU -n default -p pod1,pod2"
   
   # 4. Handle both help formats
   if [ "${inputArgs[json_help]}" = true ]; then
      PrintJsonHelpInfo "$overview" "$author" "$sample" "${supported_opts[@]}"
   else
      PrintHelpInfo "$overview" "$author"
      PrintCommonHelpInfo "${supported_opts[@]}"
      PrintAdditionalInfo "Sample" "$sample"
   fi
}

declare  -A inputArgs
ParseArgs "$@"

namespace=${inputArgs[namespace]}
pods=${inputArgs[pods]}
workerNode=${inputArgs[workerNodeName]}
alertName=${inputArgs[alertName]}
requestId=${inputArgs[requestId]}

#=====Start of Customized Code =====

if [ -z "$alertName" ]; then
   echo "Missing required args: --alertName <alert_name>"
   Help
   exit 255
fi 

# Build detailed message with available information
message="Prometheus Alert: $alertName"

if [ -n "$namespace" ]; then
   message="$message | Namespace: $namespace"
fi

if [ -n "$pods" ]; then
   message="$message | Pods: $pods"
fi

if [ -n "$workerNode" ]; then
   message="$message | Worker Node: $workerNode"
fi

# Build script arguments for audit log
scriptArgs=""
if [ -n "$namespace" ]; then
   scriptArgs="$scriptArgs -n $namespace"
fi
if [ -n "$pods" ]; then
   scriptArgs="$scriptArgs -p $pods"
fi
if [ -n "$workerNode" ]; then
   scriptArgs="$scriptArgs -w $workerNode"
fi
scriptArgs="$scriptArgs --alertName $alertName"

echo "Recording audit log for Prometheus alert: $alertName"
echo "Message: $message"

# Submit audit log using the new alert-specific function
Submit_AlertExecution_AuditLogs "$(basename $0)" "$alertName" "$message" "$requestId" "$scriptArgs"

echo "Audit log submitted successfully for alert: $alertName"