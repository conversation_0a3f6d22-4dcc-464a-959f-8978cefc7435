﻿using CampaignMiddleTierTest.Framework;
using CampaignMiddleTierTest.Framework.Utilities;
using CampaignTest.ApiFunctionalTests;
using CampaignTest.ApiFunctionalTests.Collections;
using Microsoft.BingAds.CampaignManagement;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ApiFactories = CampaignTest.ApiFunctionalTests.Factories;
using FieldNames = CampaignTest.Framework.FieldNames;
using ResponseValidator = CampaignTest.ApiFunctionalTests.Validators.ResponseValidator;

namespace Microsoft.Advertising.Advertiser.APIV13.Goal
{
    [TestClass]
    public class AddConversionGoals : CampaignTestBase
    {
        private static CustomerInfo cInfo = GoalsCustomer;
        private static CustomerInfo ViewThroughConversionsCustomerInfo = ViewThroughConversionPilotCustomer;
        private static CustomerInfo MainGoalConversionsCustomerInfo = MainConversionGoalPilotCustomer;
        private static CustomerInfo GoalCategoryPilotCustomerInfo = GoalCategoryPilotCustomer;
        private static CustomerInfo ConversionGoalAttributionModelPilotCustomer = ConversionGoalAttributionModelCustomer;
        private static long tagId;
        private static long tagId_ViewThroughConversionCustomer;
        private static long tagId_MainGoalConversionCustomer;
        private static long tagId_ThirdPartyConversionCustomer;
        private static long tagId_GoalCategoryCustomer;
        private static long tagid_ConversionGoalAttributionModelCustomer;

        [ClassInitialize]
        public static void Initialize(TestContext context)
        {
            tagId = GetUetTagId(cInfo);
            tagId_ViewThroughConversionCustomer = GetUetTagId(ViewThroughConversionsCustomerInfo);
            tagId_MainGoalConversionCustomer = GetUetTagId(MainGoalConversionsCustomerInfo);
            tagId_ThirdPartyConversionCustomer = GetUetTagId(ThirdPartyConversionPilotCustomer);
            tagId_GoalCategoryCustomer = GetUetTagId(GoalCategoryPilotCustomerInfo);
            tagid_ConversionGoalAttributionModelCustomer = GetUetTagId(ConversionGoalAttributionModelPilotCustomer);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_Success()
        {
            SetMsClickIdTaggingAndValidate(cInfo, "false");

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url,
                ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.Event, ConversionGoalType.AppInstall, ConversionGoalType.OfflineConversion}, tagId);

            goalsCollection.Add_Success(cInfo);

            GetMsClickIdTaggingAndValidate(cInfo, "true");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_MainGoalConversionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_MainGoalConversionCustomer);

            goalsCollection.Goals[0].ExcludeFromBidding = true;
            goalsCollection.Add_Success(MainGoalConversionsCustomerInfo);
        }

        [TestMethod, Priority(2)]
        [SkipInit]
        public void AddConversionGoals_EnhancedConversions_FirstTime()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.EnhancedConversions });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].IsEnhancedConversionsEnabled = true;
            ConversionGoal[] apiGoals = new ConversionGoal[1] { goalsCollection.Goals[0] };

            goalsCollection.Add_Fail(customer, apiGoals);
        }

        [TestMethod, Priority(2)]
        [SkipInit]
        public void AddConversionGoals_EnhancedConversions_SecondTime()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions });
            long tagId = GetUetTagId(customer);

            dynamic goal = ConversionGoalCollection.CreateDestinationGoal(tagId);
            goal.IsEnhancedConversionsEnabled = true;
            ConversionGoalCollection.PostGoal(cInfo: customer, goal: goal);

            // Create a goal with enhanced conversions enabled through API
            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId);
            goalsCollection.Goals[0].IsEnhancedConversionsEnabled = true;

            dynamic response = goalsCollection.Add_Success(customer);
            var goalId = (long?)response.ConversionGoalIds[0];
            Assert.IsNotNull(goalId);

            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), type: ConversionGoalType.Duration, customer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsEnhancedConversionsEnabled);
            Assert.IsNotNull(resultGoals);
            var resultGoal = resultGoals[0] as DurationGoal;
            Assert.IsTrue(resultGoal.IsEnhancedConversionsEnabled);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_ViewThroughConversionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);

            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;

            goalsCollection.Add_Success(ViewThroughConversionsCustomerInfo);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void AddUpdateGetConversionGoals_AttributionModelPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(3, ConversionGoalType.Duration, tagid_ConversionGoalAttributionModelCustomer);

            goalsCollection.Goals[0].AttributionModelType = AttributionModelType.LastClick;
            goalsCollection.Goals[1].AttributionModelType = AttributionModelType.LastTouch;
            goalsCollection.Goals[2].AttributionModelType = null;

            goalsCollection.Add_Success(ConversionGoalAttributionModelPilotCustomer, ReturnAdditionalFields: ConversionGoalAdditionalField.AttributionModelType, isEnabledForAttributionModel: true);

            goalsCollection.Goals[0].AttributionModelType = AttributionModelType.LastTouch;
            goalsCollection.Goals[1].AttributionModelType = AttributionModelType.LastClick;
            goalsCollection.Update_Success(ConversionGoalAttributionModelPilotCustomer, ReturnAdditionalFields: ConversionGoalAdditionalField.AttributionModelType, isEnabledForAttributionModel: true);

            goalsCollection.Goals[0].AttributionModelType = null;
            goalsCollection.Goals[1].AttributionModelType = null;
            goalsCollection.Update_Success(ConversionGoalAttributionModelPilotCustomer, ReturnAdditionalFields: ConversionGoalAdditionalField.AttributionModelType,
                additionalAssert: (actualGoals) =>
                { 
                    actualGoals[0].AttributionModelType = AttributionModelType.LastTouch;
                    actualGoals[1].AttributionModelType = AttributionModelType.LastClick;
                }, isEnabledForAttributionModel: true);
        }

        public void AddConversionGoals_GoalCategoryPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_GoalCategoryCustomer);

            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.AddToCart;

            goalsCollection.Add_Success_For_GoalCategory(GoalCategoryPilotCustomerInfo);

        }

        public void AddConversionGoals_GoalCategoryPilotEnabled_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_GoalCategoryCustomer);

            goalsCollection.Goals[0].GoalCategory = null;


            var response = goalsCollection.Add(GoalCategoryPilotCustomerInfo);

            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidGoalCategory);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_ViewThroughConversionPilotEnabled_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);

            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.Exceed).ViewThroughConversionWindowInMinutes;

            goalsCollection.Goals[1].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.Negative).ViewThroughConversionWindowInMinutes;

            var response = goalsCollection.Add(ViewThroughConversionsCustomerInfo);

            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidViewThroughConversionWindowInMinutes);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidViewThroughConversionWindowInMinutes);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_ViewThroughConversionPilotEnabled_NoneUetBasedGoal_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.AppInstall, tagId_ViewThroughConversionCustomer);

            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;

            var response = goalsCollection.Add(ViewThroughConversionsCustomerInfo);

            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ViewThroughConversionNotApplicableToGoalType);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_BatchError_Mix()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(8, ConversionGoalType.Duration, tagId);
            goalsCollection.Goals[0].Name = null;
            goalsCollection.Goals[1].Id = 1;
            goalsCollection.Goals[2].Status = ConversionGoalStatus.Deleted;
            goalsCollection.Goals[3] = null;
            goalsCollection.Goals[4].ConversionWindowInMinutes =
                ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Exceed).ConversionWindowInMinutes;
            goalsCollection.Goals[5].ConversionWindowInMinutes =
                ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Negative).ConversionWindowInMinutes;
            ((DurationGoal)goalsCollection.Goals[6]).MinimumDurationInSeconds =
                ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(
                    ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Negative).MinimumDurationInSeconds;
            ((DurationGoal)goalsCollection.Goals[7]).MinimumDurationInSeconds =
                ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(
                    ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Exceed).MinimumDurationInSeconds;

            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalNameNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.ConversionGoalIdIsNotNull);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidConversionGoalStatus);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.ConversionGoalIsNull);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidConversionGoalConversionWindow);
            ResponseValidator.ValidateAPIPatialError(response, 5, ErrorCodes.InvalidConversionGoalConversionWindow);
            ResponseValidator.ValidateAPIPatialError(response, 6, ErrorCodes.InvalidDurationGoalDurationTime);
            ResponseValidator.ValidateAPIPatialError(response, 7, ErrorCodes.InvalidDurationGoalDurationTime);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_ValidNames_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(3, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.Valid).Name;
            //goalsCollection.Goals[1].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.BasicPrintableAscii).Name;
            goalsCollection.Goals[1].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.MaxLengthBoundary).Name;
            goalsCollection.Goals[2].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.Numeric).Name;

            goalsCollection.Add_Success(cInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidName_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(4, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.WhiteSpace).Name;
            goalsCollection.Goals[1].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.Unspecified).Name;
            goalsCollection.Goals[2].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.NonprintableAscii).Name;
            goalsCollection.Goals[3].Name = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Name.OverTheMaxLengthBoundary).Name;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalNameNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.ConversionGoalNameNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidConversionGoalName);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidConversionGoalName);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidStatus_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].Status = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Status.Deleted).Status;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalStatus);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_EmptyGoalArray_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(0, ConversionGoalType.Url, tagId);
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalsNotPassed);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_GoalIsNull_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0] = null;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalIsNull);
            var addGoalsList = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoalIds);
            Assert.AreEqual(2, addGoalsList.Count);
            Assert.IsNull(addGoalsList[0]);
            Assert.IsNotNull(addGoalsList[1]);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_DuplicateGoalNames_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[1].Name = goalsCollection.Goals[0].Name;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.DuplicateConversionGoalName);
            var addGoalsList = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoalIds);
            Assert.AreEqual(2, addGoalsList.Count);
            Assert.IsNull(addGoalsList[1]);
            Assert.IsNotNull(addGoalsList[0]);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_ExceedsLimit_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(101, ConversionGoalType.Url, tagId);
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalArrayExceedsLimit);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_ExistGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);

            ConversionGoalCollection goalsCollection2 = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection2.Goals[0].Name = goalsCollection.Goals[0].Name;
            var response = goalsCollection2.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalNameAlreadyExists);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_TagIdIsNotPassed_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].TagId = null;
            goalsCollection.Goals[1].TagId = 9999999;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidUetTagId);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidUetTagId);
        }

// Doesn't make sense for REST API since we use the Type field to identify the object type to create
#if !REST_API
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_ConversionGoalTypeNotMatched_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Goals[0].Type = ConversionGoalType.Event;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalTypeNotMatched);
        }
#endif

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidRevenue_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.AppInstall, ConversionGoalType.Event }, tagId);

            goalsCollection.Goals[0].Revenue.Type = ConversionGoalRevenueType.VariableValue;
            goalsCollection.Goals[1].Revenue.Type = ConversionGoalRevenueType.VariableValue;
            goalsCollection.Goals[2].Revenue.Type = ConversionGoalRevenueType.VariableValue;
            goalsCollection.Goals[3].Revenue.Value = -100;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidConversionGoalRevenueValue);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_UrlGoalUrlExpression_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(4, ConversionGoalType.Url, tagId);
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.Valid).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[1]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.MaxLengthBoundary).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[2]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.LowerLengthBoundary).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[3]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.Numeric).UrlExpression;
            goalsCollection.Add_Success(cInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidUrlGoalUrlExpression_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(4, ConversionGoalType.Url, tagId);
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.Unspecified).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[1]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.WhiteSpace).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[2]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.NonprintableAscii).UrlExpression;
            ((UrlGoal)goalsCollection.Goals[3]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.OverTheMaxLengthBoundary).UrlExpression;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.UrlGoalUrlExpressionNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.UrlGoalUrlExpressionNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidUrlGoalUrlExpression);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidUrlGoalUrlExpression);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_AppInstallGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(7, ConversionGoalType.AppInstall, null);
            ((AppInstallGoal)goalsCollection.Goals[0]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.Unspecified).AppStoreId;
            ((AppInstallGoal)goalsCollection.Goals[1]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.WhiteSpace).AppStoreId;
            ((AppInstallGoal)goalsCollection.Goals[2]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.NonprintableAscii).AppStoreId;
            ((AppInstallGoal)goalsCollection.Goals[3]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.OverTheMaxLengthBoundary).AppStoreId;
            ((AppInstallGoal)goalsCollection.Goals[4]).AppPlatform = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppPlatform.FakeStore).AppPlatform;
            goalsCollection.Goals[5].CountType = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.CountType.Unique).CountType;
            goalsCollection.Goals[6].Scope = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Scope.Account).Scope;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.AppInstallGoalStoreIdNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.AppInstallGoalStoreIdNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidAppInstallGoalStoreId);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidAppInstallGoalStoreId);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidAppInstallGoalAppPlatform);
            ResponseValidator.ValidateAPIPatialError(response, 5, ErrorCodes.InvalidAppInstallGoalCountType);
            ResponseValidator.ValidateAPIPatialError(response, 6, ErrorCodes.InvalidAppInstallGoalScope);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidMinimumPagesViewedForGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.PagesViewedPerVisit, tagId, ApiFactories.PageViewsPerVisitGoalFactory.MinimumPagesViewed.Invalid);
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidMinimumPagesViewedForGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidRegularExpression_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = "*thankyou";
            ((UrlGoal)goalsCollection.Goals[0]).UrlOperator = ExpressionOperator.RegularExpression;

            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidRegularExpression);

            goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Event, tagId);
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = "*thankyou";
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = ExpressionOperator.RegularExpression;
            response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidRegularExpression);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidDurationGoalDurationTime_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            ((DurationGoal)goalsCollection.Goals[0]).MinimumDurationInSeconds = ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Negative).MinimumDurationInSeconds;
            ((DurationGoal)goalsCollection.Goals[1]).MinimumDurationInSeconds = ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Exceed).MinimumDurationInSeconds;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidDurationGoalDurationTime);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidDurationGoalDurationTime);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidLookBackWindow_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            goalsCollection.Goals[0].ConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Negative).ConversionWindowInMinutes;
            goalsCollection.Goals[1].ConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Exceed).ConversionWindowInMinutes;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalConversionWindow);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalConversionWindow);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidEventGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(9, ConversionGoalType.Event, tagId);
            ((EventGoal)goalsCollection.Goals[0]).Value = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.Value.Unspecified).Value;
            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ActionExpression.WhiteSpace).ActionExpression;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.CategoryExpression.Unspecified).CategoryExpression;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.LabelExpression.WhiteSpace).LabelExpression;
            ((EventGoal)goalsCollection.Goals[1]).Value = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.Value.InvalidRange).Value;
            ((EventGoal)goalsCollection.Goals[2]).ActionExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ActionExpression.NonprintableAscii).ActionExpression;
            ((EventGoal)goalsCollection.Goals[3]).CategoryExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.CategoryExpression.NonprintableAscii).CategoryExpression;
            ((EventGoal)goalsCollection.Goals[4]).LabelExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.LabelExpression.NonprintableAscii).LabelExpression;
            ((EventGoal)goalsCollection.Goals[5]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[5]).LabelOperator = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.LabelOperator.Equals).LabelOperator;
            ((EventGoal)goalsCollection.Goals[6]).Value = null;
            ((EventGoal)goalsCollection.Goals[6]).ValueOperator = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ValueOperator.Equals).ValueOperator;
            ((EventGoal)goalsCollection.Goals[7]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[7]).ActionOperator = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ActionOperator.BeginsWith).ActionOperator;
            ((EventGoal)goalsCollection.Goals[8]).CategoryExpression = "";
            ((EventGoal)goalsCollection.Goals[8]).CategoryOperator = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.CategoryOperator.Contains).CategoryOperator;

            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.EventGoalExpressionWithOperatorNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidEventGoalValue);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidEventGoalActionExpression);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidEventGoalCategoryExpression);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidEventGoalLabelExpression);
            ResponseValidator.ValidateAPIPatialError(response, 5, ErrorCodes.InvalidEventGoalLabelExpression);
            ResponseValidator.ValidateAPIPatialError(response, 6, ErrorCodes.InvalidEventGoalValue);
            ResponseValidator.ValidateAPIPatialError(response, 7, ErrorCodes.InvalidEventGoalActionExpression);
            ResponseValidator.ValidateAPIPatialError(response, 8, ErrorCodes.InvalidEventGoalCategoryExpression);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_OfflineConversionGoal_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.OfflineConversion, tagId);
            //TagId in OfflineConversionGoal will not be checked, and will be replaced by null.
            goalsCollection.Goals[0].TagId = 9999999;

            goalsCollection.Add_Success(cInfo);
            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(2, resultGoals.Count);
            Assert.IsNotNull(resultGoals[0] as OfflineConversionGoal);
            Assert.IsNull((resultGoals[0] as OfflineConversionGoal).TagId);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_DefaultValue_Success()
        {
            var goals = new List<ConversionGoal>();
            var urlGoal = new UrlGoal();
            urlGoal.Name = "url" + DateTime.Now.Ticks;
            urlGoal.TagId = tagId;
            urlGoal.UrlExpression = "www.bing.com";
            urlGoal.GoalCategory = ConversionGoalCategory.Other;
            goals.Add(urlGoal);
            var durationGoal = new DurationGoal();
            durationGoal.Name = "duration" + DateTime.Now.Ticks;
            durationGoal.GoalCategory = ConversionGoalCategory.Other;
            durationGoal.TagId = tagId;
            goals.Add(durationGoal);
            var pageGoal = new PagesViewedPerVisitGoal();
            pageGoal.Name = "page" + DateTime.Now.Ticks;
            pageGoal.GoalCategory = ConversionGoalCategory.Other;
            pageGoal.TagId = tagId;
            goals.Add(pageGoal);
            var evenGoal = new EventGoal();
            evenGoal.Name = "event" + DateTime.Now.Ticks;
            evenGoal.GoalCategory = ConversionGoalCategory.Other;
            evenGoal.TagId = tagId;
            evenGoal.ActionExpression = "action";
            evenGoal.CategoryExpression = "category";
            evenGoal.LabelExpression = "label";
            evenGoal.Value = 2;
            goals.Add(evenGoal);
            var appGoal = new AppInstallGoal();
            appGoal.Name = "App" + DateTime.Now.Ticks;
            appGoal.GoalCategory = ConversionGoalCategory.Download;
            appGoal.AppStoreId = "abcde";
            goals.Add(appGoal);
            var offlineGoal = new OfflineConversionGoal();
            offlineGoal.Name = "offline" + DateTime.Now.Ticks;
            offlineGoal.GoalCategory = ConversionGoalCategory.Other;
            offlineGoal.Scope = EntityScope.Account;
            goals.Add(offlineGoal);
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(goals);
            goalsCollection.Add_Success(cInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_CurrencyCode_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Account, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue, ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);
            goalsCollection.Add_Success(cInfo);

            goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Account, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue, ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified);
            goalsCollection.Add_Success(cInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidCurrencyCode_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Duration,
                    ConversionGoalType.AppInstall, ConversionGoalType.PagesViewedPerVisit}, tagId,
                ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue, ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);

            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNotNull);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.GoalCurrencyCodeIsNotNull);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.GoalCurrencyCodeIsNotNull);

            goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue, ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified);
            goalsCollection.Goals[0].Revenue.CurrencyCode = null;
            goalsCollection.Goals[1].Revenue.CurrencyCode = null;

            response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNull);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.GoalCurrencyCodeIsNull);

            goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Account, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue, ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Invalid);
            response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidGoalCurrencyCode);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidGoalCurrencyCode);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InStoreTransactionGoal_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            List<object> inStoreGoals = goalsCollection.Get_Success(null, ConversionGoalType.InStoreTransaction, cInfo);
            if (inStoreGoals.Count == 0)
            {
                goalsCollection.Add_Success(cInfo);
                List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
                Assert.IsNotNull(resultGoals);
                Assert.AreEqual(1, resultGoals.Count);
                Assert.IsNotNull(resultGoals[0] as InStoreTransactionGoal);
                Assert.IsNull((resultGoals[0] as InStoreTransactionGoal).TagId);
                goalsCollection.AreEqualInSequence(resultGoals);
            }
            else if (inStoreGoals.Count == 1)
            {
                Assert.IsNotNull(inStoreGoals[0] as InStoreTransactionGoal);
                Assert.IsNull((inStoreGoals[0] as InStoreTransactionGoal).TagId);
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", inStoreGoals.Count, cInfo.CustomerId);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InvalidInStoreTransactionGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.InStoreTransaction, null);
            goalsCollection.Goals[0].Scope = EntityScope.Account;
            goalsCollection.Goals[1].Revenue.Type = ConversionGoalRevenueType.FixedValue;
            goalsCollection.Goals[1].Revenue.Value = 1;
            goalsCollection.Goals[1].Revenue.CurrencyCode = null;

            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InStoreTransactionGoalScopeInvalid);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.GoalCurrencyCodeIsNull);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_MultiInStoreTransactionGoal_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            List<object> inStoreGoals = goalsCollection.Get_Success(null, ConversionGoalType.InStoreTransaction, cInfo);
            if (inStoreGoals.Count == 0)
            {
                goalsCollection.Add_Success(cInfo);
                List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
                Assert.IsNotNull(resultGoals);
                Assert.AreEqual(1, resultGoals.Count);
                Assert.IsNotNull(resultGoals[0] as InStoreTransactionGoal);
                Assert.IsNull((resultGoals[0] as InStoreTransactionGoal).TagId);
            }
            else if (inStoreGoals.Count == 1)
            {
                Assert.IsNotNull(inStoreGoals[0] as InStoreTransactionGoal);
                Assert.IsNull((inStoreGoals[0] as InStoreTransactionGoal).TagId);
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", inStoreGoals.Count, cInfo.CustomerId);
            }

            ConversionGoalCollection anotherGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            var response = anotherGoalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.OnlyOneInStoreTransactionGoalAllowedPerCustomer);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_InStoreTransactionPilotNotEnabled_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            var response = goalsCollection.Add();
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InStoreTransactionPilotNotEnabledForCustomer);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddOfflineConversionGoals_CurrencyAcrossAllAccounts_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);
            goalsCollection.Add_Success(cInfo);

            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ConversionGoalType.OfflineConversion, cInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(1, resultGoals.Count);
            Assert.IsNotNull(resultGoals[0] as OfflineConversionGoal);
            goalsCollection.AreEqualInSequence(resultGoals);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddOfflineConversionGoals_CurrencyAcrossAllAccounts_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified);
            goalsCollection.Goals[0].Revenue.CurrencyCode = null;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNull);

            goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Invalid);
            response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidGoalCurrencyCode);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_CurrencyAcrossAllAccounts_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);
            goalsCollection.Add_Success(cInfo);

            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ConversionGoalType.Url | ConversionGoalType.Event, cInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(2, resultGoals.Count);
            goalsCollection.AreEqualInSequence(resultGoals);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void AddConversionGoals_CurrencyAcrossAllAccounts_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified);
            goalsCollection.Goals[0].Revenue.CurrencyCode = null;
            goalsCollection.Goals[1].Revenue.CurrencyCode = null;
            var response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNull);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.GoalCurrencyCodeIsNull);

            goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Invalid);
            response = goalsCollection.Add(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidGoalCurrencyCode);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidGoalCurrencyCode);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_ThirdPartyConversionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url,
                ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.Event, ConversionGoalType.AppInstall, ConversionGoalType.OfflineConversion}, tagId_ThirdPartyConversionCustomer);

            (goalsCollection.Goals[5] as OfflineConversionGoal).IsExternallyAttributed = true;

            goalsCollection.Add_Success(ThirdPartyConversionPilotCustomer);
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_AutoGoal_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagId);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;

            goalsCollection.Add_Success(customer, ReturnAdditionalFields:ConversionGoalAdditionalField.IsAutoGoal);
        }


        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void AddConversionGoals_AutoGoal_AssignParameter_Failure()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagId);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            //we don't support assignment of event goal parameter in API
            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = ConversionGoalCollection.GenerateEventActionForAutoGoal(goalsCollection.Goals[0].GoalCategory);
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = ExpressionOperator.Equals;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;

            var response = goalsCollection.Add(customer);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidEventParameterForAutoGoal);
        }

        private static long GetUetTagId(CustomerInfo cInfo = null)
        {
            var uetTagCollection = new UETTagCollection(1);
            var uetTags = uetTagCollection.Get_Success(null, cInfo);
            if (uetTags.Count > 0)
            {
                return ((UetTag)uetTags[0]).Id.Value;
            }
            uetTagCollection.Add_Success(cInfo);
            return uetTagCollection.Tags[0].Id.Value;
        }

        private void GetMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string expectedMsClickIdTagging)
        {
            AccountPropertyCollection collection = new AccountPropertyCollection(expectedMsClickIdTagging, null, null);
            List<object> accountProperties = collection.Get_Sucess(new[] { AccountPropertyName.MSCLKIDAutoTaggingEnabled }, customerInfo);
            collection.Validate(accountProperties);
        }

        private void SetMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string msClickIdTagging)
        {
            AccountPropertyCollection collection = new AccountPropertyCollection(msClickIdTagging, null, null);
            collection.Set_Success(customerInfo);
            List<object> accountProperties = collection.Get_Sucess(new[] { AccountPropertyName.MSCLKIDAutoTaggingEnabled }, customerInfo);
            collection.Validate(accountProperties);
        }
    }
}
