infra-monitoring:
  azureManagePrometheus:
    enabled: true
  
  keyvault:
      name: CampaignSecretsKVSI
      tenantId: 975f013f-7f24-47e8-a7d3-abc4752bf346
      grafanaIngressCertName: AKSGrafanaIngressTLSCertificate
      grafanaAADOauthClientSecretName: GrafanaAADOAuthClientSecret
      prometheusWebhookAuthUsername: AKS-SelfhealingOperator-APIKEY-NAME  # placeholder for prometheus webhook http-auth, won't be used in the actual deployment when prometheusWebhookAuthPassword is not defined
  
  podMonitor:
      metricRelabelingsEnabled: true
      metricRelabelings:
      - action: drop
        regex: AuditHistoryBI(.*)
        sourceLabels:
        - datasource
      - action: drop
        regex: BscOfferDB(.*)
        sourceLabels:
        - datasource
  
  alertWebhook:
      enabled: true
      alertRule:
        name: prometheus-alert-rule-campaign
        rules:
        - alert: container_restart
          expr: 'increase(kube_pod_container_status_restarts_total{job="kube-state-metrics",namespace=~".*"}[10m]) > 1 and kube_pod_container_status_ready{job="kube-state-metrics",namespace=~".*"} == 0'
          for: 1m
          labels:
            app: campaignmt
        - alert: container_oom
          expr: 'sum(kube_pod_container_status_last_terminated_reason{reason="OOMKilled",job="kube-state-metrics",namespace=~".*"}) by (namespace,pod,container) > 0 and sum(kube_pod_container_status_ready{job="kube-state-metrics",namespace=~".*"} ) by (namespace,pod,container) ==0'
          labels:
            app: campaignmt
        - alert: container_start_failure_imagepull
          expr: 'sum by (namespace,pod,container) (kube_pod_container_status_waiting_reason{container!="", reason=~"ErrImagePull|ImagePullBackOff"}) > 0'
          for: 15m
          labels:
            app: campaignmt
        - alert: container_high_cpu
          expr: 'sum(rate(container_cpu_usage_seconds_total{cpu="total", namespace=~"default|campaignmt|dsp|adscopilot"}[1m])) by (pod,container,namespace)/ avg(kube_pod_container_resource_limits{resource="cpu",namespace=~"default|campaignmt|dsp|adscopilot"}) by (pod,container,namespace)  > 0.9'
          for: 5m
          labels:
            app: campaignmt
        - alert: container_high_cpu_too_long
          expr: 'sum(rate(container_cpu_usage_seconds_total{cpu="total", namespace=~"default|campaignmt|dsp|adscopilot"}[1m])) by (pod,container,namespace)/ avg(kube_pod_container_resource_limits{resource="cpu",namespace=~"default|campaignmt|dsp|adscopilot"}) by (pod,container,namespace)  > 0.9'
          for: 15m
          labels:
            app: campaignmt
        - alert: container_high_memory_too_long
          expr: 'avg(container_memory_working_set_bytes{namespace=~"default|campaignmt|dsp|adscopilot"}) by (pod,container,namespace) / avg(kube_pod_container_resource_limits{namespace=~"default|campaignmt|dsp|adscopilot", resource="memory"}) by (pod,container,namespace) > 0.97'
          for: 15m
          labels:
            app: campaignmt
        - alert: dotnet_process_high_memory_too_long
          expr: 'avg(container_memory_working_set_bytes{namespace=~"default|campaignmt|dsp|adscopilot"}) by (pod,container,namespace) / avg(kube_pod_container_resource_limits{namespace=~"default|campaignmt|dsp|adscopilot", resource="memory"}) by (pod,container,namespace) > 0.97'
          for: 15m
          labels:
            app: campaignmt
        - alert: container_high_threadcount
          expr: 'process_num_threads{namespace=~"default|campaignmt|dsp|adscopilot"} > 1000'
          for: 5m
          labels:
            app: campaignmt
        - alert: cluster_total_resource_request_low
          expr: 'count(kube_configmap_info{namespace="kube-system",configmap="cluster-autoscaler-status"}) == 1 and sum(namespace_cpu:kube_pod_container_resource_requests:sum{}) / sum(kube_node_status_allocatable{resource="cpu"}) < 0.9 and sum(namespace_memory:kube_pod_container_resource_requests:sum{}) / sum(kube_node_status_allocatable{resource="memory"}) < 0.9'
          for: 3m
          labels:
            app: campaignmt
        - alert: cluster_total_resource_request_high
          expr: 'count(kube_configmap_info{namespace="kube-system",configmap="cluster-autoscaler-status"}) == 1 and (sum(namespace_cpu:kube_pod_container_resource_requests:sum{}) / sum(kube_node_status_allocatable{resource="cpu"}) > 1 or sum(namespace_memory:kube_pod_container_resource_requests:sum{}) / sum(kube_node_status_allocatable{resource="memory"}) > 1)'
          for: 3m
          labels:
            app: campaignmt
        - alert: overprovisioning_pending
          expr: 'count(kube_configmap_info{namespace="kube-system",configmap="cluster-autoscaler-status"}) == 1 and count(kube_pod_status_phase{namespace="overprovisioning", phase="Pending"}) > 0'
          for: 10m
          labels:
            app: campaignmt
        - alert: cluster_valid_resource_request_high_with_pending_overprovisioning
          expr: 'count(kube_configmap_info{namespace="kube-system",configmap="cluster-autoscaler-status"}) == 1 and sum(namespace_cpu:kube_pod_container_resource_requests:sum{namespace!="overprovisioning"}) / sum(kube_node_status_allocatable{resource="cpu"}) > 0.7 and sum(namespace_memory:kube_pod_container_resource_requests:sum{namespace!="overprovisioning"}) / sum(kube_node_status_allocatable{resource="memory"}) > 0.7 and count(kube_pod_status_phase{namespace="overprovisioning", pod=~"overprovisioning.*", phase="Pending"}) > 0' 
          for: 1m
          labels:
            app: campaignmt
        - alert: application_pods_pending_too_long_force_binpack
          expr: 'count(kube_configmap_info{namespace="kube-system",configmap="cluster-autoscaler-status"}) == 1 and sum(count (kube_pod_status_phase{namespace!="overprovisioning",phase="Pending"} > 0))' 
          for: 20m
          labels:
            app: campaignmt
        - alert: application_pods_pending_too_long
          expr: 'sum(absent(kube_configmap_info{configmap="cluster-autoscaler-status",namespace="kube-system"})) == 1 and sum(count (kube_pod_status_phase{namespace!="overprovisioning",phase="Pending"} > 0))' 
          for: 5m
          labels:
            app: campaignmt
        - alert: prometheus_storage_usage_too_high
          expr: 'max without(instance,node) ((topk(1, kubelet_volume_stats_capacity_bytes{cluster="", job="kubelet", metrics_path="/metrics", namespace="monitoring", persistentvolumeclaim="prometheus-prometheus-prometheus-db-prometheus-prometheus-prometheus-0"})-topk(1, kubelet_volume_stats_available_bytes{cluster="", job="kubelet", metrics_path="/metrics", namespace="monitoring", persistentvolumeclaim="prometheus-prometheus-prometheus-db-prometheus-prometheus-prometheus-0"}))/topk(1, kubelet_volume_stats_capacity_bytes{cluster="", job="kubelet", metrics_path="/metrics", namespace="monitoring", persistentvolumeclaim="prometheus-prometheus-prometheus-db-prometheus-prometheus-prometheus-0"})* 100) > 80'
          for: 5m
          labels:
            app: campaignmt
        - alert : worker_node_not_ready_not_reachable
          expr: sum by (node) (kube_node_status_condition{condition="Ready",job="kube-state-metrics",status="unknown|false"} == 1) or sum by (node)((kube_node_spec_taint{effect="NoSchedule",job="kube-state-metrics",key="node.kubernetes.io/unreachable"} unless ignoring(key, value) kube_node_spec_taint{job="kube-state-metrics",key=~"ToBeDeletedByClusterAutoscaler"}) == 1)
          for: 30m
          labels:
            app: campaignmt
        - alert : kube-pod-status-phase-failed
          expr: sum by (phase,namespace,pod) (kube_pod_status_phase{phase="Failed"}) >0
          for: 5m
          labels:
            app: campaignmt
        - alert : kube-pod-status-phase-failed-perNamespace
          expr: sum by (phase,namespace) (kube_pod_status_phase{phase="Failed"}) >0
          for: 5m
          labels:
            app: campaignmt
        - alert: pod_not_ready_ratio_high
          expr: |
            (
              sum by (namespace, controller) (
                kube_pod_status_ready{condition="false", namespace=~"default|campaignmt|dsp|adscopilot"}
                * on(namespace, pod) group_left(controller)
                label_replace(kube_pod_owner{namespace=~"default|campaignmt|dsp|adscopilot",owner_kind="ReplicaSet"}, "controller", "$1", "owner_name", "(.*)")
              )
              /
              sum by (namespace, controller) (
                kube_pod_status_ready{namespace=~"default|campaignmt|dsp|adscopilot"}
                * on(namespace, pod) group_left(controller)
                label_replace(kube_pod_owner{namespace=~"default|campaignmt|dsp|adscopilot",owner_kind="ReplicaSet"}, "controller", "$1", "owner_name", "(.*)")
              ) != 0
            ) > 0.25
          for: 10m
          labels:
            app: campaignmt
          annotations:
            summary: "High ratio of not-ready pods detected"
            description: "Controller {{ $labels.controller }} in namespace {{ $labels.namespace }} has more than 20% of pods not ready for over 10 minutes"
  
  kube-prometheus-stack:
      enabled: true
      alertmanager:
        config:
          route:
            group_wait: 30s
            group_interval: 5m
            repeat_interval: 5m
            group_by: ['job']
            receiver: monitoring/prometheus-campaignmt-alert-manager-global/webhook
            routes:
            - receiver: monitoring/prometheus-campaignmt-alert-manager-global/webhook
              matchers:
                - app="campaignmt"
            - receiver: monitoring/prometheus-campaignmt-alert-manager-global/null
          receivers:
          - name: monitoring/prometheus-campaignmt-alert-manager-global/null
          - name: monitoring/prometheus-campaignmt-alert-manager-global/webhook
            webhook_configs:
              - send_resolved: false
                url: http://selfhealing-internal-svc.default/SelfHealing/adapter/prometheus
          templates: []
      grafana:
        grafana.ini:
          auth.azuread:
            client_id: "f9517570-23b6-47cc-96ee-ebd8a5fa5328"
            auth_url: https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/oauth2/v2.0/authorize
            token_url: https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/oauth2/v2.0/token
            
  grafanadashboard:
    required:
    - configmapName: grafana-dashboard-campaignmt-home-dashboard-cm
      fileName: MT_Home_Dashboard.json
    - configmapName: grafana-dashboard-campaignmt-cluster-overview
      fileName: MT_cluster_overview.json
    - configmapName: grafana-dashboard-campaignmt-resource-cm
      fileName: MT_resource_metrics.json
    - configmapName: grafana-dashboard-campaignmt-resource-application-cm
      fileName: MT_resource_metrics_application.json
    - configmapName: grafana-dashboard-campaignmt-httpmetrics-cm
      fileName: MT_http_metrics.json
    - configmapName: grafana-dashboard-campaignmt-autoscale-cm
      fileName: MT_autoscale_metrics.json
    - configmapName: grafana-dashboard-campaignmt-autoscale-by-taskbacklog-cm
      fileName: MT_autoscale_by_taskBacklog.json
    - configmapName: grafana-dashboard-campaignmt-db-connection-metrics-cm
      fileName: MT_db_connection_metrics.json  
    - configmapName: grafana-dashboard-campaignmt-argo-rollout-metrics-cm
      fileName: MT_Argo_rollouts_metrics.json
    - configmapName: grafana-dashboard-campaignmt-prometheus-metrics-cm
      fileName: MT_infra_prometheus.json
    - configmapName: grafana-dashboard-campaignmt-alerts-metrics-cm
      fileName: MT_infra_alerts.json    
    - configmapName: grafana-dashboard-campaignmt-oom-metrics-cm
      fileName: MT_infra_OOM.json
    - configmapName: grafana-dashboard-campaignmt-services-importapp
      fileName: MT_Service_ImportApp.json
    - configmapName: grafana-dashboard-campaignmt-services-mcaofflineservice
      fileName: MT_Service_MCAOfflineService.json        