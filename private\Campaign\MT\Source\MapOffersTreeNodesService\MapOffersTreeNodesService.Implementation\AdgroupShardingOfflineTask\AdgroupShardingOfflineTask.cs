﻿namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Config;
    using GenericFramework.QueueProcessorRuntime;
    using MapOffersTreeNodesTasks.Common;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.DAO;
    using Microsoft.Advertising.ServiceLocation;

    internal class AdgroupShardingOfflineTask : IQueueTask
    {
        private readonly Dictionary<OperationType, OperationTypeConfig> operationTypeConfigs;
        private readonly Dictionary<OperationType, DbServerPollingThrottler<ShardInfo>> dbServerPollingThrottlers;
        private readonly string[] dbKeysMain;
        private readonly DBType dbTypeShard;
        private readonly DBType dbLibraryShard;
        private readonly DBType dbSharedLibraryShard;
        private readonly DBType dbCampaignEntityShard;
        private readonly ILogShared logger;
        private readonly Random random;

        public AdgroupShardingOfflineTask()
        {
            var opTypeConfigs = new List<OperationTypeConfig>
            {
                new OperationTypeConfig(OperationType.AdLimit, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.KeywordLimit, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.OrderLimit, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.OrderAudienceLimit, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AllEntityLimit, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.OrderDelete, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.OpcCampaignId, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.OpcOrderId, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.CampaignDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdExOrderEditorialStatus, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.CampaignActive, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.LabelDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.LabelIdLevelRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AccountLevelLabelRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AccountLevelLabelsPerEntityRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.LabelIdLevelLabelsPerEntityRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AccountDeleteForPurge, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.OrderEditorialStatusQueueProcessor, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.NegativeKeywordLimit, EmittingDbType.LibraryShard),
                new OperationTypeConfig(OperationType.AdexOrderEditorialExpending, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AimOrderLevelPrivacyCheck, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AudienceIdLevelRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AccountLevelAudienceRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AudienceDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.CampaignLevelOrderTargetSizeRecount, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.PauseSharedLibraryEntityAssociations, EmittingDbType.SharedLibraryShard),
                new OperationTypeConfig(OperationType.TagUsedByCustomerGoalRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TagUsedByCustomerRemarketingListRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AudienceLevelUsedbyAdGroupRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TagUsedByCustomerProductAudienceRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SharedEntityUsedByRecount, EmittingDbType.SharedLibraryShard),
                new OperationTypeConfig(OperationType.PauseGoalForAccount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.PauseAudienceAssociationForAccount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdsByBingAutoApply, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TagUsedByAccountGoalRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TagUsedByAccountRemarketingListRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TagUsedByAccountProductAudienceRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AccountLevelAudienceUsedbyAdGroupRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AimCampaignLevelPrivacyCheck, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AudienceLevelCampaignAudienceAssociationRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AccountLevelCampaignAudienceAssociationRecount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AccoutImageReLicense, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.LicenseStockImage, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.StockImageRekey, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.NewStockImageRekey, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.NegativeKeywordCatalogMigration, EmittingDbType.LibraryShard),
                new OperationTypeConfig(OperationType.AdExEditorialStatusByAccount, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AppextensionsMetaDataSync, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AppInstallAdsMetaDataSync, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.CustomerSharedEntityInvalidAssociationCleanup, EmittingDbType.LibraryShard),
                new OperationTypeConfig(OperationType.StaToExtaAutoApply, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdExEditorialStatusByCampaign, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.PublishSmartPage, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SmartPageCustomDomainDnsSetup, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.PublishSmartPagePreview, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.CreateOrUpdateDraftCampaignForSmartPage, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SmartPageLeadsTracking, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SetM365FlagForSmartPage, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.FreeUpSmartPageCustomDomain, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SmartPageUpdateESCUrl, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SmartPageCustomDomainPostUpdate, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SmartPageCustomDomainRefresh, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.VideoAdaptiveStreamingTranscode, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.PauseAdGroupAudienceAssociationForEURestriction, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.PauseCampaignAudienceAssociationForEURestriction, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.VideoDeleteCleanup, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.AdImpressionTrackingUrl, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.FreeUpSmartPageSubdomain, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdExtensionOfflineProcessing, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.MarkSmartPageEditorialRejected, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.VideoDownload, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.DSAAutoTargetLimit, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.LabelMccDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.LabelMccEntityLevelDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.LabelMccIdLevelRecount, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.AccountLevelLabelMccRecount, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.LabelMccIdLevelLabelsPerEntityRecount, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.MultiMediaAdsAutoApply, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.ResponsiveSearchAdsOptInAutoApplyAutoGenAsset, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdOfflineValidation, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.FeedSize, Config.EnableFeedTaskInCampaignEntityLibraryShard ? EmittingDbType.MainAndCampaignEntityShard : EmittingDbType.Main),
                new OperationTypeConfig(OperationType.FeedStatusSync, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.MultiMediaAdsToTextAssets, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.RSACustomizereOfflineDelete, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TextAssetCountsAndLimitCheck, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.TextAssetBlobRefresh, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.ImageBulkCropping, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.MigrateMMAUnderDSAAdGroup, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AccountPilotMigrationProcessor, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.EnablePredictiveTargeting, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.DisablePredictiveTargeting, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AssetGroupEditorialRollup, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.SmartShoppingToPerformanceMaxUpgrade, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AssetGroupAssetEditorialStatus, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.BingPlacesCreateOrClaimBusinessListing, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AccountLevelImageAnnotation, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AssetLevelImageAnnotation, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AudienceAdSmartCropping, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.PauseAllMcaCampaigns, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.ESCAccountMigration, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.TextAdAssetAutoGeneration, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.VideoDataBackfill, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.DSAToPMaxIDMappingProcessor, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.DSAToPMaxMigrationProcessor, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.SSOBlockingMigration, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdVideoAssetsMetadataJsonFill, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.CroppingTypeForImageAdExtensionBackfill, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.UpdateAIGCSearchIndexProcessor, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.LogoAdExtensionAutoGenerationProcessor, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.UpdateExternalCampaignIdMapDetailByTaskStatus, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.AdOfflineAdd, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.AssetGroupVideoAssetMetadataJsonFill, EmittingDbType.Shard),
                new OperationTypeConfig(OperationType.CCPilotPropagation, EmittingDbType.Main),
                new OperationTypeConfig(OperationType.VideoClipchampConversionProcessor, EmittingDbType.Both),
                new OperationTypeConfig(OperationType.AccountsActiveProcessor, EmittingDbType.Main)
           };

            this.operationTypeConfigs = opTypeConfigs.ToDictionary(otc => otc.Type);
            this.dbServerPollingThrottlers = opTypeConfigs.ToDictionary(otc => otc.Type, otc => new DbServerPollingThrottler<ShardInfo>(this.logger, otc.BackoffInterval));

            this.random = new Random();
            this.dbKeysMain = new[] { DBKey.Campaign };
            this.dbTypeShard = DBType.CampaignAdGroupShard;
            this.dbLibraryShard = DBType.CampaignNegativeKeywordShard;
            this.dbSharedLibraryShard = DBType.CampaignSharedLibraryShard;
            this.dbCampaignEntityShard = DBType.CampaignEntityLibraryShard;

            this.logger = ServiceLocator.Current.Resolve<ILogShared>();
        }

        public string Name
        {
            get { return "AdgroupShardingOfflineTask"; }
        }

        public ITaskConfig GetTaskConfig()
        {
            return new OfflineTaskConfig();
        }

        public Task<IEnumerable<IQueueStore>> GetStoreInstancesAsync()
        {
            var connectionsMain = MetadataHelper.GetPartitionConnectionProviders(this.dbKeysMain).ToList();
            var dummyModValues = Enumerable.Repeat<ShardInfo>(null, connectionsMain.Count).ToList();

            var shardMap = MetadataHelper.GetShardMapOrNull(this.dbTypeShard);
            var shardGroups = shardMap != null ? shardMap.GetShardGroups() : Array.Empty<IShardGroup>();
            var connectionsWithShard = shardGroups.SelectMany(shardGroup => Enumerable.Range(0, shardGroup.GetShardCount())
                .Select(
                    shardNumber =>
                        new Tuple<IConnectionProvider, ShardInfo>(shardGroup.GetShardConnectionByShardNumber(shardNumber),
                            new ShardInfo { ModValue = shardNumber, ShardGroup = shardGroup })));

            var libraryShardMap = MetadataHelper.GetShardMapOrNull(this.dbLibraryShard);
            var libraryShardGroups = libraryShardMap != null ? libraryShardMap.GetShardGroups() : Array.Empty<IShardGroup>();
            var connectionsWithLibraryShard = libraryShardGroups.SelectMany(libraryShardGroup => Enumerable.Range(0, libraryShardGroup.GetShardCount())
                .Select(
                    shardNumber =>
                        new Tuple<IConnectionProvider, ShardInfo>(libraryShardGroup.GetShardConnectionByShardNumber(shardNumber),
                            new ShardInfo { ModValue = shardNumber, ShardGroup = libraryShardGroup })));

            var sharedLibraryShardMap = MetadataHelper.GetShardMapOrNull(this.dbSharedLibraryShard);
            var sharedLibraryShardGroups = sharedLibraryShardMap != null ? sharedLibraryShardMap.GetShardGroups() : Array.Empty<IShardGroup>();
            var connectionsWithSharedLibraryShard = sharedLibraryShardGroups.SelectMany(sharedLibraryShardGroup => Enumerable.Range(0, sharedLibraryShardGroup.GetShardCount())
                .Select(
                    shardNumber =>
                        new Tuple<IConnectionProvider, ShardInfo>(sharedLibraryShardGroup.GetShardConnectionByShardNumber(shardNumber),
                            new ShardInfo { ModValue = shardNumber, ShardGroup = sharedLibraryShardGroup })));

            var campaignEntityShardMap = MetadataHelper.GetShardMapOrNull(this.dbCampaignEntityShard);
            var campaignEntityShardGroups = campaignEntityShardMap != null ? campaignEntityShardMap.GetShardGroups() : Array.Empty<IShardGroup>();
            var connectionsWithCampaignEntityShard = campaignEntityShardGroups.SelectMany(campaignEntityShardGroup => Enumerable.Range(0, campaignEntityShardGroup.GetShardCount())
                .Select(
                    shardNumber =>
                        new Tuple<IConnectionProvider, ShardInfo>(campaignEntityShardGroup.GetShardConnectionByShardNumber(shardNumber),
                            new ShardInfo { ModValue = shardNumber, ShardGroup = campaignEntityShardGroup })));

            string enabledShardPartitionsList = Config.EnabledShardPartitions();
            if (!string.IsNullOrWhiteSpace(enabledShardPartitionsList))
            {
                var enabledPartitions =
                    enabledShardPartitionsList.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(name => int.Parse(name.Trim()));

                connectionsWithShard = connectionsWithShard.Where(connection =>
                {
                    string connectionString = connection.Item1.GetConnectionString(this.logger);
                    if (connection.Item1.PartitionId.HasValue)
                    {
                        return enabledPartitions.Any(p => p == connection.Item1.PartitionId.Value);
                    }
                    else
                    {
                        this.logger.LogApplicationError(string.Format("PartitionId is null for {0}", PiiCleaner.CleanseDBConnectionString(this.logger, connectionString)));
                        return false;
                    }
                });
            }

            var connectionsShard = connectionsWithShard.Select(cm => cm.Item1).ToList();
            var shardInfo = connectionsWithShard.Select(cm => cm.Item2).ToList();

            var connectionsForMainAndShard = new List<IConnectionProvider>();
            connectionsForMainAndShard.AddRange(connectionsMain);
            connectionsForMainAndShard.AddRange(connectionsShard);

            var tagsForMainAndShard = new List<ShardInfo>();
            tagsForMainAndShard.AddRange(dummyModValues);
            tagsForMainAndShard.AddRange(shardInfo);

            var campaignEntityConnectionProviders = connectionsWithCampaignEntityShard.Select(cm => cm.Item1).ToList();
            var campaignEntityShardInfo = connectionsWithCampaignEntityShard.Select(cm => cm.Item2).ToList();

            var connectionsForMainAndCampaignEntityShard = new List<IConnectionProvider>();
            connectionsForMainAndCampaignEntityShard.AddRange(connectionsMain);
            connectionsForMainAndCampaignEntityShard.AddRange(campaignEntityConnectionProviders);

            var tagsForMainAndCampaignEntityShard = new List<ShardInfo>();
            tagsForMainAndCampaignEntityShard.AddRange(dummyModValues);
            tagsForMainAndCampaignEntityShard.AddRange(campaignEntityShardInfo);

            foreach (var pair in this.dbServerPollingThrottlers)
            {
                EmittingDbType dbType = this.operationTypeConfigs[pair.Key].EmittingDbType;
                switch (dbType)
                {
                    case EmittingDbType.Main:
                        pair.Value.ConsiderConnections(connectionsMain, dummyModValues);
                        break;
                    case EmittingDbType.Shard:
                        pair.Value.ConsiderConnections(connectionsShard, shardInfo);
                        break;
                    case EmittingDbType.Both:
                        pair.Value.ConsiderConnections(connectionsForMainAndShard, tagsForMainAndShard);
                        break;
                    case EmittingDbType.LibraryShard:
                        pair.Value.ConsiderConnections(
                            connectionsWithLibraryShard.Select(cm => cm.Item1).ToList(),
                            connectionsWithLibraryShard.Select(cm => cm.Item2).ToList());
                        break;
                    case EmittingDbType.SharedLibraryShard:
                        pair.Value.ConsiderConnections(
                            connectionsWithSharedLibraryShard.Select(cm => cm.Item1).ToList(),
                            connectionsWithSharedLibraryShard.Select(cm => cm.Item2).ToList());
                        break;
                    case EmittingDbType.CampaignEntityShard:
                        pair.Value.ConsiderConnections(campaignEntityConnectionProviders, campaignEntityShardInfo);
                        break;
                    case EmittingDbType.MainAndCampaignEntityShard:
                        pair.Value.ConsiderConnections(connectionsForMainAndCampaignEntityShard, tagsForMainAndCampaignEntityShard);
                        break;
                    default:
                        throw new NotImplementedException("Unexpected EmittingDbType: " + dbType);
                }
            }

            // Select the enabled operation types' pollers.
            var stores = this.dbServerPollingThrottlers.Where(pair => this.operationTypeConfigs[pair.Key].IsEnabled)
                .SelectMany(
                    pair =>
                        pair.Value.GetPollEntities()
                            .Select(
                                pe => new CampaignDbQueueStore(this.logger, this.operationTypeConfigs[pair.Key], pe)))
                .OrderBy(store => this.random.Next())
                .ToList();

            this.logger.LogInfo("{0} : main connection count = {1}, shard connections count = {2}, db store count = {3}", this.Name, connectionsMain.Count, connectionsShard.Count, stores.Count);
            return Task.FromResult(stores as IEnumerable<IQueueStore>);
        }

        public IJobProcessor GetJobProcessor()
        {
            return new OfflineTaskProcessor(this.logger);
        }
    }
}
