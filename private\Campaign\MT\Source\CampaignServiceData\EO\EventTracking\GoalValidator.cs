﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.EventTracking
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Data.SqlTypes;
    using System.Linq;
    using ClientCenter;
    using Common;
    using Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities.Internal;
    using Shared.MT;
    using EventTracking = Entities.EventTracking;

    public class GoalValidator
    {
        private IAdExtensionDomainDataStorage AdextensionDomainDataStorage { get; set; }

        private ICheckPilotFlag PilotChecker { get; set; }

        private static readonly HashSet<string> CurrencyCode = Enum.GetNames(typeof (EventTracking.CurrencyCode)).ToHashSet(x=>x);

        private static readonly HashSet<GoalEntityType> EnhancedConversionsGoalType = new HashSet<GoalEntityType>
        {
            GoalEntityType.DestinationGoal,
            GoalEntityType.DurationGoal,
            GoalEntityType.EventGoal,
            GoalEntityType.PageViewsPerVisitGoal,
            GoalEntityType.OfflineConversionGoal,
        };

        public GoalValidator(IAdExtensionDomainDataStorage adextensionDomainDataStorage, ICheckPilotFlag pilotChecker)
        {
            this.AdextensionDomainDataStorage = adextensionDomainDataStorage;
            this.PilotChecker = pilotChecker;
        }

        //Before validate add goals, must checked goal type and added default value.
        public void ValidateGoals(
            CustomerCallContext context, 
            EventTracking.Goal[] goals, 
            BatchResult<long> batchResult,
            IDictionary<int, bool> PilotOfCustomerDict,
            HashSet<String> existedGoalsNameCollection = null,
            bool[] skipCurrencyCodeValidations = null)
        {
            if (goals.SafeCount() == 0)
            {
                batchResult.AddError(CampaignManagementErrorCode.GoalsNotPassed);
                return;
            }

            if (goals.SafeCount() > MiddleTierConstants.GoalBatchMaxLength)
            {
                batchResult.AddError(CampaignManagementErrorCode.GoalsBatchSizeExceedsLimit);
                return;
            }

            var isCustomerPilotForInStoreVisitGoal = IsFeatureEnabled(PilotOfCustomerDict, (int)CustomerFeatureFlag.InStoreVisitConversion);
            var isCustomerEnabledForEnhancedConversions = IsFeatureEnabled(PilotOfCustomerDict, (int)CustomerFeatureFlag.EnhancedConversions);

            if (isCustomerPilotForInStoreVisitGoal && goals.Where(g => g.Type == EventTracking.GoalEntityType.InStoreVisitGoal).Count() > 1)
            {
                batchResult.AddError(CampaignManagementErrorCode.OnlyOneInStoreVisitGoalBeAllowedPerCustomer);
            }

            bool hasMultipleSmartGoal = false;

            for (int index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;

                var goal = goals[index];
                var itemErrors = new List<CampaignManagementErrorDetail>();

                // skip tagid validation for AppInstall goals, OfflineConversion goals and InStoreTransaction goals: they are created automatically)
                if (!(goal is EventTracking.ApplicationInstallGoal || goal is EventTracking.OfflineConversionGoal || goal is EventTracking.InStoreTransactionGoal || goal is EventTracking.InStoreVisitGoal))
                {
                    ValidateGoalTagId(goal.TagId, itemErrors);
                }

                if (!EnhancedConversionsGoalType.Contains(goal.Type.Value) && goal.IsEnhancedConversionsEnabled == true)
                {
                    batchResult.AddError(CampaignManagementErrorCode.NotEligibleForEnhancedConversions);
                    return;
                }

                if (goal is EventTracking.SmartGoal)
                {
                    if (hasMultipleSmartGoal)
                    {
                        batchResult.AddError(CampaignManagementErrorCode.SmartGoalShouldBeOnlyOne);
                        return;
                    }
                    else
                    {
                        hasMultipleSmartGoal = true;
                    }
                }

                ValidateGoalName(goal, itemErrors, existedGoalsNameCollection);
                Validator.ValidateEnumValue(goal.Status, CampaignManagementErrorCode.InvalidGoalStatus, itemErrors);
                Validator.ValidateEnumValue(goal.Type, CampaignManagementErrorCode.InvalidGoalEntityType, itemErrors);
                ValidateGoalLookBackWindow(goal, itemErrors);
                ValidateGoalViewThroughLookBackWindow(goal, itemErrors);
                ValidateGoalAttributionModelType(goal, itemErrors, isCustomerEnabledForExternalAttribution: true, isCustomerEnabledForConversionGoalAttributionModel: true);

                bool skipCurrencyCodeValidation = false;
                if (skipCurrencyCodeValidations != null)
                {
                    skipCurrencyCodeValidation = skipCurrencyCodeValidations[index];
                }

                ValidateGoalRevenue(goal, itemErrors,  skipCurrencyCodeValidation);
                ValidateSpecificGoal(context, goal, itemErrors, AdextensionDomainDataStorage, PilotOfCustomerDict, PilotChecker);

                ValidateAutoGoal(context, goal, itemErrors, PilotOfCustomerDict);

                if (itemErrors.Any())
                {
                    batchResult.AddEntityErrors(index, itemErrors);
                }
            }
        }

        //Before validate update goals, must checked goal type and back fill/add default value.
        public void ValidateUpdateGoals(
            CustomerCallContext context, 
            EventTracking.Goal[] goals, 
            Dictionary<int, EventTracking.Goal> existingGoals, 
            BatchResult<long> batchResult, 
            IDictionary<int, bool> PilotOfCustomerDict, 
            bool? skipCurrencyCodeValidation = false,
            IDictionary<int, bool> PilotOfAccountDict = null)
        {
            var skipCurrencyCodeValidations = new bool[goals.Length];
            var goalIds = goals.OrEmpty().Select(x => (long)x.Id).ToList();

            if (context.Logger != null)
            {
                context.Logger.LogInfo($"skipCurrencyCodeValidation={skipCurrencyCodeValidation}");
            }

            Validator.ValidateIds<long>(
                batchResult,
                goalIds,
                MiddleTierConstants.GoalBatchMaxLength,
                CampaignManagementErrorCode.DuplicateGoalId,
                CampaignManagementErrorCode.InvalidGoalId,
                CampaignManagementErrorCode.GoalIdsNotPassed,
                CampaignManagementErrorCode.GoalsBatchSizeExceedsLimit);

            for (int index = 0; index < goals.Length; index++)
            {
                skipCurrencyCodeValidations[index] = false;

                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;

                bool isGoalReadOnly = existingGoals[index].IsReadOnly.HasValue
                    && existingGoals[index].IsReadOnly.Value;

                if (isGoalReadOnly)
                {
                    batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalIsReadOnly);
                }

                // Goal type edit is not allowed to convert in-store transaction, offline conversion or hotel booking goal to another goal type, and vice verse.
                if (goals[index].Type != existingGoals[index].Type &&
                    (existingGoals[index].Type == EventTracking.GoalEntityType.InStoreTransactionGoal ||
                     goals[index].Type == EventTracking.GoalEntityType.InStoreTransactionGoal ||
                     existingGoals[index].Type == EventTracking.GoalEntityType.OfflineConversionGoal ||
                     goals[index].Type == EventTracking.GoalEntityType.OfflineConversionGoal ||
                     existingGoals[index].Type == EventTracking.GoalEntityType.ProductConversionGoal ||
                     goals[index].Type == EventTracking.GoalEntityType.ProductConversionGoal ||
                     existingGoals[index].Type == EventTracking.GoalEntityType.SmartGoal ||
                     goals[index].Type == EventTracking.GoalEntityType.SmartGoal ||
                     existingGoals[index].Type == EventTracking.GoalEntityType.InStoreVisitGoal ||
                     goals[index].Type == EventTracking.GoalEntityType.InStoreVisitGoal))
                {
                    batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalTypeCannotBeChanged);
                }

                if (DynamicConfigValues.EnableGoalNullableProperties)
                {
                    // Every type doesn't need a check on CurrencyCode when its status is being changed.
                    skipCurrencyCodeValidations[index] = goals[index].Status != existingGoals[index].Status;
                }
                else
                {
                    // Skip currency code validation if it is an update operation on status.
                    if (goals[index].Status != existingGoals[index].Status
                        && (goals[index].Type == EventTracking.GoalEntityType.DestinationGoal
                           || goals[index].Type == EventTracking.GoalEntityType.EventGoal
                           || goals[index].Type == EventTracking.GoalEntityType.DurationGoal
                           || goals[index].Type == EventTracking.GoalEntityType.PageViewsPerVisitGoal
                           || goals[index].Type == EventTracking.GoalEntityType.ApplicationInstallGoal))
                    {
                        skipCurrencyCodeValidations[index] = true;
                    }
                }

                if (skipCurrencyCodeValidation.HasValue)
                {
                    skipCurrencyCodeValidations[index] = skipCurrencyCodeValidations[index] || skipCurrencyCodeValidation.Value;
                }
                if (goals[index].AttributionModelType.HasValue && !isAttributionModelTypeEditalbe(goals[index], existingGoals[index]))
                {
                    batchResult.AddEntityError(index, CampaignManagementErrorCode.AttributionModelTypeCannotBeUpdated);
                }
                if (!goals[index].AttributionModelType.HasValue)
                {
                    // treate null as no update
                    goals[index].AttributionModelType = existingGoals[index].AttributionModelType;
                }
                if (DynamicConfigValues.EnableSmartGoal && goals[index].Type == EventTracking.GoalEntityType.SmartGoal)
                {
                    if (!ValidateSmartGoalForSomeParametersEdit(goals[index], existingGoals[index], context.Request.CustomerAccountId))
                    {
                        batchResult.AddEntityError(index, CampaignManagementErrorCode.SmartGoalCouldNotBeEditInSomeParameters);
                    }
                }

                //validate for goal scope change
                if (goals[index].Type != EventTracking.GoalEntityType.ApplicationInstallGoal)
                {
                    if (existingGoals[index].IsAccountLevel != goals[index].IsAccountLevel)
                    {
                        if (!existingGoals[index].IsAccountLevel)
                        {
                            batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalLevelCannotBeDowngraded);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(goals[index].ClarityEventDefinitionId) && goals[index].ClarityEventDefinitionId.Length > 1024)
                {
                    batchResult.AddEntityError(index, CampaignManagementErrorCode.ValueTooLong);
                }

                var isCustomerEnabledForAutoConversion = false;
                PilotOfCustomerDict.TryGetValue((int)CustomerFeatureFlag.AutoConversion, out isCustomerEnabledForAutoConversion);
                if (isCustomerEnabledForAutoConversion)
                {
                    bool isAutoGoal = existingGoals[index].IsAutoGoal.HasValue && existingGoals[index].IsAutoGoal.Value;

                    if (isAutoGoal)
                    {
                        if (goals[index].Type != existingGoals[index].Type)
                        {
                            batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalTypeCannotBeChanged);
                        }

                        if (goals[index].GoalCategory != existingGoals[index].GoalCategory)
                        {
                            batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalCategoryCannotBeChangedForAutoGoal);
                        }

                        if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
                        {
                            if (goals[index].IsAutoGoal != true)
                            {
                                //the original goal is auto goal, can not change to manual goal
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.IsAutoGoalFieldCannotBeChanged);
                            }

                            if (goals[index].TagId != existingGoals[index].TagId)
                            {
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.TagNotAllowToChangeForAutoGoal);
                            }

                            var eventAutoGoal = goals[index] as EventGoal;
                            var existingAutGoal = existingGoals[index] as EventGoal;

                            if (eventAutoGoal == null || existingAutGoal == null)
                            {
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.InvalidGoalTypeForAutoGoal);
                            }

                            if (eventAutoGoal.Action != existingAutGoal.Action || eventAutoGoal.ActionOperator != existingAutGoal.ActionOperator ||
                                eventAutoGoal.Category != existingAutGoal.Category || eventAutoGoal.CategoryOperator != existingAutGoal.CategoryOperator ||
                                eventAutoGoal.Label != existingAutGoal.Label || eventAutoGoal.LabelOperator != existingAutGoal.LabelOperator ||
                                eventAutoGoal.Value != existingAutGoal.Value || eventAutoGoal.ValueOperator != existingAutGoal.ValueOperator)
                            {
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.EventParameterNotAllowToChangeForAutoGoal);
                            }
                        }
                    }
                    else
                    {
                        if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
                        {
                            if (goals[index].IsAutoGoal == true)
                            {
                                //the original goal is manual goal, can not change to auto goal.
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.IsAutoGoalFieldCannotBeChanged);
                            }
                        }
                    }
                }

                var isAccountEnabledForMobileAppCampaignConversionGoal = false;
                if (PilotOfAccountDict != null)
                {
                    PilotOfAccountDict.TryGetValue((int)AccountFeatureFlag.MobileAppCampaignConversionGoal, out isAccountEnabledForMobileAppCampaignConversionGoal);
                    if (isAccountEnabledForMobileAppCampaignConversionGoal)
                    {
                        bool isAppInstallGoal = existingGoals[index].Type.HasValue && existingGoals[index].Type.Value == GoalEntityType.ApplicationInstallGoal
                            && goals[index].Type.HasValue && goals[index].Type.Value == GoalEntityType.ApplicationInstallGoal;

                        if (isAppInstallGoal)
                        {
                            if (((ApplicationInstallGoal)goals[index]).ApplicationStoreId != ((ApplicationInstallGoal)existingGoals[index]).ApplicationStoreId)
                            {
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalApplicationStoreIdCannotBeChanged);
                            }
                            if (((ApplicationInstallGoal)goals[index]).ApplicationPlatform != ((ApplicationInstallGoal)existingGoals[index]).ApplicationPlatform)
                            {
                                batchResult.AddEntityError(index, CampaignManagementErrorCode.GoalApplicationPlatformCannotBeChanged);
                            }
                        }                            
                    }
                }
            }

            ValidateGoals(context, goals, batchResult, PilotOfCustomerDict, null, skipCurrencyCodeValidations);
        }

        public bool ValidateSmartGoalForSomeParametersEdit(EventTracking.Goal goal, EventTracking.Goal existingGoal, long? AccountId)
        {
            if (ValidateSmartGoalEqual(goal, existingGoal) && AccountId == existingGoal.AccountId)
                return true;
            else
                return false;
        }

        public bool ValidateSmartGoalEqual(EventTracking.Goal goal, EventTracking.Goal existingGoal)
        {
            return existingGoal.Id == goal.Id && existingGoal.TagId == goal.TagId && existingGoal.Name == goal.Name && existingGoal.Type == goal.Type && existingGoal.LookbackWindowDays == goal.LookbackWindowDays && existingGoal.LookbackWindowHours == goal.LookbackWindowHours && existingGoal.LookbackWindowMinutes == goal.LookbackWindowMinutes && existingGoal.Revenue.Type == goal.Revenue.Type && existingGoal.Revenue.Value == goal.Revenue.Value && existingGoal.Revenue.CurrencyCode == goal.Revenue.CurrencyCode && existingGoal.IsAccountLevel == goal.IsAccountLevel && existingGoal.ConversionCountType == goal.ConversionCountType && existingGoal.ViewThroughLookbackWindowinDays == goal.ViewThroughLookbackWindowinDays && existingGoal.ViewThroughLookbackWindowinHours == goal.ViewThroughLookbackWindowinHours && existingGoal.ViewThroughLookbackWindowinMinutes == goal.ViewThroughLookbackWindowinMinutes && existingGoal.GoalCategory == goal.GoalCategory;

        }

        public bool IsCustomerEnabledForCampaignAnalyticsMigration(CustomerCallContext context)
        {
            return this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.CampaignAnalyticsMigration);
        }

        public bool IsCustomerEnabledForInStoreVisitGoal(CustomerCallContext context)
        {
            bool IsConfigEnabled = DynamicConfigValues.EnableInStoreVisitConversion;
            bool IsFeatureIdEnabled = this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.InStoreVisitConversion);
            context.Logger.LogInfo($"DynamicConfigValues.InStoreVisitConversion={IsConfigEnabled}, IsFeatureIdEnabled(InStoreVisitConversion)={IsFeatureIdEnabled}");
            return IsConfigEnabled && IsFeatureIdEnabled;
        }

        public bool IsCustomerEnabledForInStoreTransaction(CustomerCallContext context)
        {
            return this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.InStoreTransaction);
        }

        public bool IsCustomerEnabledForAutoConversion(CustomerCallContext context)
        {
            return DynamicConfigValues.EnableAutoConversion && this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.AutoConversion);
        }

        public bool IsCustomerEnabledForBoost(CustomerCallContext context)
        {
            return this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.Boost);
        }

        public bool IsCustomerEnableProductConversionGoal(CustomerCallContext context)
        {
            return this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.EnhancedShoppingCampaigns) ||
                    this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.CNMarketExpansion) ||
                    this.PilotChecker.IsFeatureIdEnabled(context, (int)CustomerFeatureFlag.AIM);
        }

        public bool IsAccountEnabledForLinkedInCampaign(CustomerCallContext context)
        {
            return context?.Request?.CustomerAccountId != null && this.PilotChecker.IsAccountEnabledForLinkedInCampaign(context.Logger, context.Request);
        }

        public void ValidateGoalTypeFilter(CustomerCallContext context, List<EventTracking.GoalEntityType> goalTypeFilter, BaseResult batchResult)
        {
            if (goalTypeFilter != null)
            {
                if (goalTypeFilter.Contains(EventTracking.GoalEntityType.InStoreTransactionGoal)
                    && !IsCustomerEnabledForInStoreTransaction(context))
                {
                    batchResult.AddError(CampaignManagementErrorCode.InStoreTransactionPilotNotEnabledForCustomer);
                }

                if (goalTypeFilter.Contains(EventTracking.GoalEntityType.ProductConversionGoal)
                    && !IsCustomerEnableProductConversionGoal(context))
                {
                    batchResult.AddError(CampaignManagementErrorCode.CustomerNotEligibleForProductConversionGoal);
                }                
            }
        }

        private static void ValidateGoalTagId(long? tagId, List<CampaignManagementErrorDetail> itemErrors)
        {
            if (tagId == null)
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidTagId));
            }
            else
            {
                Validator.ValidateLong(tagId.Value, CampaignManagementErrorCode.InvalidTagId, 1, CampaignManagementErrorCode.InvalidTagId, null, null, itemErrors);
            }
        }

        private static void ValidateGoalName(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, HashSet<String> existedGoalsNameCollection)
        {
            Validator.ValidateString(
                goal.Name,
                CampaignManagementErrorCode.GoalNameNullOrEmpty,
                CampaignManagementErrorCode.GoalNameNullOrEmpty,
                1,
                CampaignManagementErrorCode.GoalNameNullOrEmpty,
                MiddleTierConstants.GoalNameMaxLength,
                CampaignManagementErrorCode.GoalNameIsTooLong,
                CampaignManagementErrorCode.GoalNameHasInvalidCharacters,
                itemErrors);
            if (existedGoalsNameCollection != null && existedGoalsNameCollection.Contains(goal.Name))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.GoalWithSameNameAlreadyExistsUnderTag));
            }
        }

        private static bool ValidateLookBackWindowNoneNegativeValue(List<int> targets)
        {
            bool isValid = true;
            foreach (int target in targets)
            {
                if (target < 0)
                {
                    isValid = false;
                    return isValid;
                }
            }
            return isValid;
        }

        private static void ValidateGoalLookBackWindow(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors)
        {
            if (!ValidateLookBackWindowNoneNegativeValue(new List<int>() { goal.LookbackWindowDays ?? 0, goal.LookbackWindowHours ?? 0, goal.LookbackWindowMinutes ?? 0 }))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalLookbackWindow));
                return;
            }

            var lookBackMinutes = EventTrackingHelper.ComputeWindowMinutes(goal.LookbackWindowDays ?? 0, goal.LookbackWindowHours ?? 0, goal.LookbackWindowMinutes ?? 0);

            //for in store visit goal, conversion window can only be supported at daily level (1 to 30 days)
            if (lookBackMinutes > MiddleTierConstants.GoalLookBackWindowMaxValue || goal.Type == GoalEntityType.InStoreVisitGoal && (lookBackMinutes > MiddleTierConstants.InStoreVisitGoalLookBackWindowMaxValue || lookBackMinutes < MiddleTierConstants.InStoreVisitGoalLookBackWindowMinValue || (lookBackMinutes % MiddleTierConstants.GoalLookBackWindowOneDayValue) != 0))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalLookbackWindow));
            }
        }

        private static void ValidateGoalViewThroughLookBackWindow(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors)
        {
            // if all these three fields are null , it's supposed to be valid for addGoals scenario which will be assigned with default value for this goal
            // and for update scenario , this case is regarded as no modification to value
            if (goal.ViewThroughLookbackWindowinDays == null && goal.ViewThroughLookbackWindowinHours == null && goal.ViewThroughLookbackWindowinMinutes == null)
            {
                return;
            }

            if (!EventTrackingHelper.IsUetBasedGoal(goal))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.ViewThroughConversionNotApplicableToGoalType));
                return;
            }

            if (!ValidateLookBackWindowNoneNegativeValue(new List<int>() { goal.ViewThroughLookbackWindowinDays ?? 0, goal.ViewThroughLookbackWindowinHours ?? 0, goal.ViewThroughLookbackWindowinMinutes ?? 0 }))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalViewThroughLookbackWindow));
                return;
            }

            var ViewThroughLookBackMinutes = EventTrackingHelper.ComputeWindowMinutesAllowNull(goal.ViewThroughLookbackWindowinDays, goal.ViewThroughLookbackWindowinHours, goal.ViewThroughLookbackWindowinMinutes).Value;

            if (ViewThroughLookBackMinutes <= 0 || ViewThroughLookBackMinutes > MiddleTierConstants.GoalViewThroughLookBackWindowMaxValue)
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalViewThroughLookbackWindow));
            }
        }

        private static bool isAttributionModelTypeEditalbe(EventTracking.Goal currentGoal, EventTracking.Goal existingGoal)
        {
            var editableGoalAttributesWhiteList = new List<AttributionModelType>()
            {
                AttributionModelType.Default,
                AttributionModelType.LastTouch
            };

            var existingAttributionModelType = existingGoal.AttributionModelType ?? AttributionModelType.Default;
            var currentGoalAttributionModelType = currentGoal.AttributionModelType ?? AttributionModelType.Default;

            if (existingAttributionModelType != currentGoalAttributionModelType)
            {
                if (editableGoalAttributesWhiteList.Contains(existingAttributionModelType) &&
                    editableGoalAttributesWhiteList.Contains(currentGoalAttributionModelType))
                {
                    return true;
                }

                return false;
            }

            return true;
        }

        private static void ValidateGoalAttributionModelType(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, bool isCustomerEnabledForExternalAttribution, bool isCustomerEnabledForConversionGoalAttributionModel = false)
        {
            if (!IsGoalTypeEligibleForAttributionModel(goal, isCustomerEnabledForExternalAttribution, isCustomerEnabledForConversionGoalAttributionModel, out var errorCode))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(errorCode.Value));
                return;
            }
        }

        private static void ValidateGoalRevenue(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, bool skipCurrencyCodeValidation)
        {
            var type = goal.Type;
            if (goal.Revenue.Type == EventTracking.GoalValueType.VariantValue &&
                    (type == EventTracking.GoalEntityType.ApplicationInstallGoal
                     || type == EventTracking.GoalEntityType.DurationGoal
                     || type == EventTracking.GoalEntityType.PageViewsPerVisitGoal
                     || type == EventTracking.GoalEntityType.InStoreVisitGoal))
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalValueType));
            }

            if (goal.Revenue.Type == EventTracking.GoalValueType.NoValue)
            {
                if (!(goal.Revenue.Value == null || goal.Revenue.Value == 0))
                {
                    itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalValue));
                }
            }
            else if (goal.Revenue.Value == null || goal.Revenue.Value < 0 || goal.Revenue.Value > SqlMoney.MaxValue.ToDecimal())
            {
                itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalValue));
            }

            ValidateGoalValueCurrencyCode(goal, itemErrors, skipCurrencyCodeValidation);
        }

        private static void ValidateAutoGoal(CustomerCallContext context, EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, IDictionary<int, bool> pilotOfCustomerDict)
        {

            bool autoConversionEnabled = false;
            pilotOfCustomerDict?.TryGetValue((int)CustomerFeatureFlag.AutoConversion, out autoConversionEnabled);

            if (autoConversionEnabled)
            {
                if (!VerifyGoalSource(goal))
                {
                    itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalSource));
                }
                if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
                {

                    if (goal.IsAutoGoal ?? false)
                    {

                        var eventGoal = goal as EventGoal;
                        if (eventGoal == null)
                        {
                            context.Logger.LogUserError($"Auto goal must be event type: {eventGoal.Name}");
                            itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalTypeForAutoGoal));
                        }
                        if (eventGoal.GoalCategory == null)
                        {
                            context.Logger.LogUserError($"Auto goal must have category: {eventGoal.Name}");
                            itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.MustHaveCategoryForAutoGoal));
                        }

                        var targetEventAction = EventTrackingHelper.GenerateEventActionForAutoGoal(eventGoal.GoalCategory);
                        if (eventGoal.Action != targetEventAction || eventGoal.ActionOperator != ExpressionOperator.EqualsTo)
                        {
                            context.Logger.LogUserError($"Invalid parameter ({nameof(EventGoal.Action)} or {nameof(EventGoal.ActionOperator)}) for Auto goal: {eventGoal.Name}");
                            itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidEventParameterForAutoGoal));
                        }

                        if (!string.IsNullOrEmpty(eventGoal.Category) || eventGoal.CategoryOperator != ExpressionOperator.NoExpression ||
                            !string.IsNullOrEmpty(eventGoal.Label) || eventGoal.LabelOperator != ExpressionOperator.NoExpression ||
                            eventGoal.Value != null || eventGoal.ValueOperator != ValueOperator.NoValue)
                        {
                            context.Logger.LogUserError($"Event goal's parameters not support for Auto goal: {eventGoal.Name}");
                            itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidEventParameterForAutoGoal));
                        }
                    }
                }
            }
            else
            {
                if (goal.IsAutoGoal.HasValue || goal.GoalSourceId.HasValue)
                {
                    itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.CustomerNotEligibleForAutoConversion));
                }
            }
        }

        private static void ValidateGoalValueCurrencyCode(EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, bool skipCurrencyCodeValidation)
        {
            if (DynamicConfigValues.EnableGoalNullableProperties)
            {
                // If changing status, just skip this validation.
                if (skipCurrencyCodeValidation)
                {
                    return;
                }

                if (IsAvailableForCurrencyCode(goal.Type.Value))
                {
                    if (goal.Revenue.CurrencyCode != null)
                    {
                        if (!CurrencyCode.Contains(goal.Revenue.CurrencyCode))
                        {
                            itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalValueCurrencyCode, $"Goal id: {goal.Id}, Goal currency code is {goal.Revenue.CurrencyCode}, Goal.Type is {goal.Type}; Goal.Revenue.Type is {goal.Revenue.Type.ToString()}"));
                        }
                    }
                    else
                    {
                        // When choosing "Don't assign a value" in Revenue setting. Currency code is null.
                        if ((goal.Revenue.Type == null || goal.Revenue.Type == GoalValueType.NoValue)
                            && goal.Revenue.Value == null)
                        {
                            return;
                        }

                        itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldNotBeNull, $"Goal id: {goal.Id}, Goal currency code is null (expected non-null), goal.Type is {goal.Type}; goal.Revenue.Type is {goal.Revenue.Type.ToString()}"));
                    }
                }
                else
                {
                    if (goal.Revenue.CurrencyCode != null)
                    {
                        itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldBeNull, $"Goal id: {goal.Id}, Goal currency code is not null (expected null), goal.Type is {goal.Type}"));
                    }
                }
            }
            else
            {
                if (goal.Type == EventTracking.GoalEntityType.InStoreTransactionGoal
                    || goal.Type == EventTracking.GoalEntityType.OfflineConversionGoal
                    || goal.Type == EventTracking.GoalEntityType.DestinationGoal
                    || goal.Type == EventTracking.GoalEntityType.EventGoal
                    || goal.Type == EventTracking.GoalEntityType.ProductConversionGoal
                    || goal.Type == EventTracking.GoalEntityType.InStoreVisitGoal)
                {
                    if (goal.Revenue.CurrencyCode == null && goal.Revenue.Type != EventTracking.GoalValueType.NoValue && !skipCurrencyCodeValidation)
                    {
                        itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldNotBeNull, $"Goal id: {goal.Id}, Goal currency code is null (expected non-null), goal.Type is {goal.Type}; goal.Revenue.Type is {goal.Revenue.Type.ToString()}"));
                    }
                    else if (goal.Revenue.CurrencyCode != null && !CurrencyCode.Contains(goal.Revenue.CurrencyCode))
                    {
                        itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalValueCurrencyCode, $"Goal id: {goal.Id}, Goal currency code is {goal.Revenue.CurrencyCode}, Goal.Type is {goal.Type}; Goal.Revenue.Type is {goal.Revenue.Type.ToString()}"));
                    }
                }
                else
                {
                    if (goal.Revenue.CurrencyCode != null && !skipCurrencyCodeValidation)
                    {
                        itemErrors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldBeNull, $"Goal id: {goal.Id}, Goal currency code is not null (expected null), goal.Type is {goal.Type}"));
                    }
                }
            }
        }

        private static bool IsGoalTypeEligibleForAttributionModel(EventTracking.Goal goal, bool isCustomerEnabledForExternalAttribution, bool isCustomerEnabledForConversionGoalAttributionModel, out CampaignManagementErrorCode? errorCode)
        {
            errorCode = null;

            if (goal.AttributionModelType.HasValue)
            {
                switch (goal.AttributionModelType)
                {
                    case AttributionModelType.Default:
                        return true;
                    case AttributionModelType.External:
                        if (!(goal is OfflineConversionGoal))
                        {
                            errorCode = CampaignManagementErrorCode.AttributionModelTypeNotApplicableToGoalType;
                            return false;
                        }
                        else if (!isCustomerEnabledForExternalAttribution)
                        {
                            errorCode = CampaignManagementErrorCode.CustomerNotEligibleForExternalAttribution;
                            return false;
                        }
                        return true;
                    case AttributionModelType.LastTouch:
                        if (!isCustomerEnabledForConversionGoalAttributionModel)
                        {
                            errorCode = CampaignManagementErrorCode.InvalidState;
                            return false;
                        }

                        if (EventTrackingHelper.IsUetBasedGoal(goal))
                        {
                            return true;
                        }

                        errorCode = CampaignManagementErrorCode.AttributionModelTypeNotApplicableToGoalType;
                        return false;
                    default:
                        errorCode = CampaignManagementErrorCode.AttributionModelTypeNotApplicableToGoalType;
                        return false;
                }
            }

            return true;
        }

        private static void ValidateSpecificGoal(CustomerCallContext context, EventTracking.Goal goal, List<CampaignManagementErrorDetail> itemErrors, IAdExtensionDomainDataStorage adExtensionDomainData, IDictionary<int, bool> PilotOfCustomerDict, ICheckPilotFlag pilotChecker)
        {
            switch (goal.Type)
            {
                case EventTracking.GoalEntityType.DestinationGoal:
                    DestinationGoalValidator.ValidateSpecificGoal(goal, itemErrors);
                    return;
                case EventTracking.GoalEntityType.DurationGoal:
                    DurationGoalValidator.ValidateSpecificGoal(goal, itemErrors);
                    return;
                case EventTracking.GoalEntityType.EventGoal:
                    EventGoalValidator.ValidateSpecificGoal(goal, itemErrors);
                    return;
                case EventTracking.GoalEntityType.PageViewsPerVisitGoal:
                    PageViewsPerVisitGoalValidator.ValidateSpecificGoal(goal, itemErrors);
                    return;
                case EventTracking.GoalEntityType.ApplicationInstallGoal:
                    ApplicationInstallGoalValidator.ValidateSpecificGoal(context, goal, itemErrors, adExtensionDomainData, pilotChecker);
                    return;
                case EventTracking.GoalEntityType.MultiStageGoal:
                    MultiStageGoalValidator.ValidateSpecificGoal(goal, itemErrors);
                    return;
                case EventTracking.GoalEntityType.InStoreTransactionGoal:
                    InStoreTransactionGoalValidator.ValidateSpecificGoal(context, goal, itemErrors, pilotChecker);
                    return;
                case EventTracking.GoalEntityType.ProductConversionGoal:
                    ProductConversionGoalValidator.ValidateSpecificGoal(context, goal, itemErrors, pilotChecker);
                    return;
                case EventTracking.GoalEntityType.SmartGoal:
                    SmartGoalValidator.ValidateSpecificGoal(context, goal, itemErrors, pilotChecker);
                    return;
                case EventTracking.GoalEntityType.InStoreVisitGoal:
                    InStoreVisitGoalValidator.ValidateSpecificGoal(context, goal, itemErrors, PilotOfCustomerDict);
                    return;
                default:
                    return;
            }
        }

        public static void ValidateExternalChannelsServabilityForUpdate(EventTracking.Goal[] goals, Dictionary<int, EventTracking.Goal> existingGoals, BatchResult<long> batchResult)
        {
            for (var index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                {
                    continue;
                }

                if (existingGoals[index].ServingExternalChannelCampaigns?.ContainsKey(ServingChannel.LinkedIn) != true)
                {
                    continue;
                }

                if (!EventTrackingHelper.IsServableInLinkedIn(goals[index], out var errorCodes))
                {
                    foreach(var errorCode in errorCodes)
                    {
                        batchResult.AddEntityError(index, errorCode);
                    }
                }
            }
        }

        public static void VerifyGoalCategoryForGoalType(EventTracking.Goal[] goals, BatchResult<long> batchResult, bool? skipGoalCategoryValidation = false)
        {
            //this logic is only for skip the request from adinsight
            if (skipGoalCategoryValidation.HasValue && skipGoalCategoryValidation == true)
            {
                return;
            }
            for (int index = 0; index < goals.Length; index++)
            {
                if (!VerifyGoalCategory(goals[index]))
                {
                    batchResult.AddEntityErrors(index, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalCategory) });
                }
                else
                {
                    if (!IsGoalCategoryInRightGoalType(goals[index]))
                    {
                        batchResult.AddEntityErrors(index, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidCategoryForGoalType) });
                    }
                }
            }
        }

        public static void VerifyIncomingGoalNamesDuplicate(EventTracking.Goal[] goals, BatchResult<long> batchResult)
        {
            var nameSet = new HashSet<string>();

            for (int i = 0; i < goals.Length; i++)
            {
                if (goals[i].Name == null) //for udpate scenario, name can be null if customer do not want to udpate goal name
                {
                    continue; 
                }
                if (nameSet.Contains(goals[i].Name.ToLower()))
                {
                    batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.DuplicateGoalName) });
                }
                else
                {
                    nameSet.Add(goals[i].Name.ToLower());
                }
            }
        }

        public static void VerifiedSameCategoryAndTagForAutoGoal(CustomerCallContext context, List<EventTracking.Goal> existingGoals, EventTracking.Goal[] proposalGoals , BatchResult<long> batchResult)
        {
            var tagAndCategoryDictForExistingGoal = new Dictionary<(long?, GoalCategory?), List<EventTracking.Goal>>();
            foreach (var goal in existingGoals)
            {
                var key = (goal.TagId, goal.GoalCategory);
                if (!tagAndCategoryDictForExistingGoal.TryGetValue(key, out var goals))
                {
                    goals = new List<EventTracking.Goal>();
                    tagAndCategoryDictForExistingGoal[key] = goals;
                }
                goals.Add(goal);
            }

            var tagAndCategoryDictForProposalGoals = new Dictionary<(long?, GoalCategory?), List<EventTracking.Goal>>();
            //check 1: can not have duplicate between new proposal goals and existing goals
            for (int i = 0; i < proposalGoals.Length; i++)
            {
                var newGoal = proposalGoals[i];

                (long?, GoalCategory?) keyForNewGoal = (newGoal.TagId, newGoal.GoalCategory);
                if(!tagAndCategoryDictForProposalGoals.TryGetValue(keyForNewGoal, out var goals))
                {
                    goals = new List<EventTracking.Goal>();
                    tagAndCategoryDictForProposalGoals[keyForNewGoal] = goals;
                }
                goals.Add(newGoal);

                if (newGoal.IsAutoGoal ?? false)
                {
                    if (tagAndCategoryDictForExistingGoal.ContainsKey(keyForNewGoal))
                    {
                        if (newGoal.Id == null) //add auto goal scenario
                        {
                            context.Logger.LogUserError($"Can not add AUTO goal: already exist goal with tagId={newGoal.TagId}, goalCategory={newGoal.GoalCategory}");
                            batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal) });
                        }
                        else //update auto goal scenario
                        {
                            foreach (var existingGoal in tagAndCategoryDictForExistingGoal[keyForNewGoal])
                            {
                                if (existingGoal.Id != newGoal.Id)
                                {
                                    //if other goals (whether auto or manual) has duplicate category and tag, we should block the update
                                    context.Logger.LogUserError($"Can not update AUTO goal {newGoal.Id} can no other goals with tagId={newGoal.TagId}, goalCategory={newGoal.GoalCategory}");
                                    batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal) });
                                    break;
                                }
                            }
                        }
                    }
                }
                else
                {
                    //check if already have auto goal use same <tag, goalCategory>
                    if (tagAndCategoryDictForExistingGoal.ContainsKey(keyForNewGoal))
                    {
                        foreach (var goal in tagAndCategoryDictForExistingGoal[keyForNewGoal])
                        {

                            if (goal.IsAutoGoal == true)
                            {
                                context.Logger.LogUserError($"Can not add/update MANUAL goal: has AUTO goal with tagId={newGoal.TagId}, goalCategory={newGoal.GoalCategory}");
                                batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal) });
                                break;
                            }
                        }
                    }
                }
            }

            //check 2, can not have duplicate for proposed goals in current request body.
            //Mainly for SOAP/Restful API scenario, which will attempt to add/update more than 1 goals in 1 API reqeust.
            for (int i = 0; i < proposalGoals.Length; i++)
            {            
                var newGoal = proposalGoals[i];
                (long?, GoalCategory?) tagAndCategoryForNewGoal = (newGoal.TagId, newGoal.GoalCategory);

                if (newGoal.IsAutoGoal ?? false)
                {
                    if (tagAndCategoryDictForProposalGoals.ContainsKey(tagAndCategoryForNewGoal)
                        && tagAndCategoryDictForProposalGoals[tagAndCategoryForNewGoal].Count > 1)
                    {
                        context.Logger.LogUserError($"Can not add or update AUTO goal: duplicate goals with tagId={newGoal.TagId}, goalCategory={newGoal.GoalCategory}");
                        batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal) });
                    }
                }
                else
                {
                    if (tagAndCategoryDictForProposalGoals.ContainsKey(tagAndCategoryForNewGoal)
                        && tagAndCategoryDictForProposalGoals[tagAndCategoryForNewGoal].Count > 1)
                    {
                        foreach (var goal in tagAndCategoryDictForProposalGoals[tagAndCategoryForNewGoal])
                        {
                            if (goal.IsAutoGoal == true)
                            {
                                context.Logger.LogUserError($"Can not add or update MANUAL goal: has AUTO goal with tagId={newGoal.TagId}, goalCategory={newGoal.GoalCategory}");
                                batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal) });
                                break;
                            }
                        }
                    }
                }
            }
        }

        public static void VerifyBoostRevenueUpdate(CustomerCallContext context, EventTracking.Goal[] goals, BatchResult<EventTracking.Goal> getGoalsResult, BatchResult<long> batchResult, IDictionary<int, bool> pilotOfCustomerDict, out List<(long, decimal?, decimal?)> goalRevenueChange)
        {
            goalRevenueChange = new List<(long, decimal?, decimal?)>();
            if (DynamicConfigValues.BoostGoalRevenueUpdateCheck && IsFeatureEnabled(pilotOfCustomerDict, (int)CustomerFeatureFlag.Boost))
            {
                int customerId = context.AdvertiserCustomerId;
                int userId = context.UserId;

                var existingRevenue = getGoalsResult.Entities.Values.Where(i => i.Id.HasValue).ToDictionary(i => (long)i.Id, i => i.Revenue?.Value);

                for (int i = 0; i < goals.Length; i++)
                {
                    if (goals[i].Id.HasValue)
                    { 
                        long goalId = goals[i].Id.Value;
                        if (existingRevenue.ContainsKey(goalId))
                        {
                            if (existingRevenue[goalId] != goals[i].Revenue?.Value)
                            {
                                var whitelist = DynamicConfigValues.GetValue("BoostGoalRevenueUpdateUserIdWhitelist", context.Request.OverrideConfigValuesFromTest, new ReadOnlyCollection<int>(new List<int>()));
                                if (whitelist.Contains(userId))
                                {
                                    context.Logger.LogInfo($"The user try to update the boost goal revenue. The userid is {userId}, the customerid is {customerId}, the goalid is {goalId}. The old revenue is {existingRevenue[goalId]}, the new revenue is {goals[i].Revenue?.Value}");
                                    goalRevenueChange.Add((goalId, existingRevenue[goalId], goals[i].Revenue?.Value));
                                }
                                else 
                                {
                                    context.Logger.LogInfo($"The user does not have permission to update the boost goal revenue. The userid is {userId}, the customerid is {customerId}, the goalid is {goalId}. The old revenue is {existingRevenue[goalId]}, the new revenue is {goals[i].Revenue?.Value}");
                                    batchResult.AddEntityErrors(i, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.NotAllowedToDoCurrentOperation) });
                                }
                            }

                        }
                    }
                }
            }
        }

        private static bool IsGoalCategoryInRightGoalType(EventTracking.Goal goal)
        {
            var result = true;
            var goalCategory = goal.GoalCategory;
            var goalType = goal.Type;

            switch (goalType)
            {
                case EventTracking.GoalEntityType.DestinationGoal:
                    GoalCategory[] goalCategoriesForDestinationGoal = {GoalCategory.Purchase, GoalCategory.AddToCart, GoalCategory.BeginCheckout, GoalCategory.Subcribe,
                                                                        GoalCategory.SubmitLeadForm, GoalCategory.BookAppointment, GoalCategory.Signup, GoalCategory.RequestQuote, 
                                                                        GoalCategory.GetDirections, GoalCategory.OutboundClick, GoalCategory.Contact, GoalCategory.PageView, GoalCategory.Other};
                    result = ((IList)goalCategoriesForDestinationGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.DurationGoal:
                    GoalCategory[] goalCategoriesForDurationGoal = {GoalCategory.Other};
                    result = ((IList)goalCategoriesForDurationGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.EventGoal:
                    GoalCategory[] goalCategoriesForEventGoal = {GoalCategory.Purchase, GoalCategory.AddToCart, GoalCategory.BeginCheckout, GoalCategory.Subcribe,
                                                                 GoalCategory.SubmitLeadForm, GoalCategory.BookAppointment, GoalCategory.Signup, GoalCategory.RequestQuote,
                                                                 GoalCategory.GetDirections, GoalCategory.OutboundClick, GoalCategory.Contact, GoalCategory.PageView, GoalCategory.Other};
                    result = ((IList)goalCategoriesForEventGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.PageViewsPerVisitGoal:
                    GoalCategory[] goalCategoriesForPageViewsPerVisitGoal = {GoalCategory.Other};
                    result = ((IList)goalCategoriesForPageViewsPerVisitGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.ApplicationInstallGoal:
                    GoalCategory[] goalCategoriesForApplicationInstallGoal = {GoalCategory.Download};
                    result = ((IList)goalCategoriesForApplicationInstallGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.MultiStageGoal:
                    result = true;
                    break;
                case EventTracking.GoalEntityType.OfflineConversionGoal:
                    GoalCategory[] goalCategoriesForOfflineConversionGoal = {GoalCategory.Purchase, GoalCategory.AddToCart, GoalCategory.BeginCheckout, GoalCategory.Subcribe,
                                                                 GoalCategory.SubmitLeadForm, GoalCategory.BookAppointment, GoalCategory.Signup, GoalCategory.RequestQuote,
                                                                 GoalCategory.GetDirections, GoalCategory.OutboundClick, GoalCategory.Contact, GoalCategory.PageView, GoalCategory.Other, GoalCategory.InStoreVisit};
                    result = ((IList)goalCategoriesForOfflineConversionGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.InStoreTransactionGoal:
                    GoalCategory[] goalCategoriesForInStoreTransactionGoal = { GoalCategory.Purchase };
                    result = ((IList)goalCategoriesForInStoreTransactionGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.ProductConversionGoal:
                    GoalCategory[] goalCategoriesForProductConversionGoal = {GoalCategory.Purchase};
                    result = ((IList)goalCategoriesForProductConversionGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.SmartGoal:
                    GoalCategory[] goalCategoriesForSmartGoal = { GoalCategory.Other };
                    result = ((IList)goalCategoriesForSmartGoal).Contains(goalCategory);
                    break;
                case EventTracking.GoalEntityType.InStoreVisitGoal:
                    GoalCategory[] goalCategoriesForInStoreVisitGoal = { GoalCategory.InStoreVisit };
                    result = ((IList)goalCategoriesForInStoreVisitGoal).Contains(goalCategory);
                    break;
                default:
                    result = false;
                    break;
            }

            return result;

        }

        public static void VerifyGoalCategoryExists(EventTracking.Goal[] goals, BatchResult<long> batchResult)
        {
            for (int index = 0; index < goals.Length; index++)
            {
                if (VerifyGoalCategoryExists(goals[index]))
                {
                    batchResult.AddEntityErrors(index, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.CustomerNotEligibleForGoalCategory) });
                }
            }
        }

        private static bool VerifyGoalCategoryExists(EventTracking.Goal goal)
        {
            var result = true;
            var goalCategory = goal.GoalCategory;
            if (goalCategory == null)
            {
                result =  false;
            }
            return result;
        }

        private static bool VerifyGoalCategory(EventTracking.Goal goal)
        {
            var result = true;
            var goalCategory = goal.GoalCategory;
            if (goalCategory == null) return false;
            result = System.Enum.IsDefined(typeof(GoalCategory), goalCategory);
            return result;
        }
        
        public static void VerifyGoalType(EventTracking.Goal[] goals, BatchResult<long> batchResult)
        {
            for (int index = 0; index < goals.Length ; index++)
            {
                if (!VerifyGoalType(goals[index]))
                {
                    batchResult.AddEntityErrors(index, new List<CampaignManagementErrorDetail>() { new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalEntityType) });
                }
            }
        }

        // These types have Concurrency Code.
        // Duration / PageView / AppInstall don't have currency code.
        // InStoreVisit and InStoreTransaction also have CurrencyCode but they only support customer level.
        public static bool IsAvailableForCurrencyCode(GoalEntityType type)
        {
            return type == EventTracking.GoalEntityType.DestinationGoal ||
                   type == EventTracking.GoalEntityType.EventGoal ||
                   type == EventTracking.GoalEntityType.OfflineConversionGoal ||
                   type == EventTracking.GoalEntityType.ProductConversionGoal ||
                   type == EventTracking.GoalEntityType.InStoreVisitGoal ||
                   type == EventTracking.GoalEntityType.InStoreTransactionGoal;
        }

        private static bool VerifyGoalType(EventTracking.Goal goal)
        {
            switch (goal.Type)
            {
                case EventTracking.GoalEntityType.DestinationGoal:
                    if (!(goal is EventTracking.DestinationGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.DurationGoal:
                    if (!(goal is EventTracking.DurationGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.EventGoal:
                    if (!(goal is EventTracking.EventGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.PageViewsPerVisitGoal:
                    if (!(goal is EventTracking.PageViewsPerVisitGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.ApplicationInstallGoal:
                    if (!(goal is EventTracking.ApplicationInstallGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.MultiStageGoal:
                    if (!(goal is EventTracking.MultiStageGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.OfflineConversionGoal:
                    if (!(goal is EventTracking.OfflineConversionGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.InStoreTransactionGoal:
                    if (!(goal is EventTracking.InStoreTransactionGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.ProductConversionGoal:
                    if (!(goal is EventTracking.ProductConversionGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.SmartGoal:
                    if (!(goal is EventTracking.SmartGoal))
                    {
                        return false;
                    }
                    break;
                case EventTracking.GoalEntityType.InStoreVisitGoal:
                    if (!(goal is EventTracking.InStoreVisitGoal))
                    {
                        return false;
                    }
                    break;
                default:
                    return false;
            }
            return true;
        }

        private static bool VerifyGoalSource(EventTracking.Goal goal)
        {
            var result = true;
            var goalSource = goal.GoalSourceId;
            if (goalSource == null) return true;
            result = System.Enum.IsDefined(typeof(GoalSource), goalSource);
            return result;
        }

        private static bool IsFeatureEnabled(IDictionary<int, bool> pilotOfCustomerDict, int featureId)
        {
            var isFeatureEnabled = false;

            if (pilotOfCustomerDict != null)
            {
                pilotOfCustomerDict.TryGetValue(featureId, out isFeatureEnabled);
            }

            return isFeatureEnabled;
        }
    }
}
