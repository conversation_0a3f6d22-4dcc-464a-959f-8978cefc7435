
package com.microsoft.bingads.v13.campaignmanagement;

import java.util.concurrent.Future;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.jws.soap.SOAPBinding;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.ws.AsyncHandler;
import jakarta.xml.ws.Response;


/**
 * This class was generated by the XML-WS Tools.
 * XML-WS Tools 4.0.1
 * Generated source version: 3.0
 * 
 */
@WebService(name = "ICampaignManagementService", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
@XmlSeeAlso({
    ObjectFactory.class
})
public interface ICampaignManagementService {


    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddCampaignsResponse>
     */
    @WebMethod(operationName = "AddCampaigns", action = "AddCampaigns")
    public Response<AddCampaignsResponse> addCampaignsAsync(
        @WebParam(name = "AddCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddCampaigns", action = "AddCampaigns")
    public Future<?> addCampaignsAsync(
        @WebParam(name = "AddCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignsRequest parameters,
        @WebParam(name = "AddCampaignsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddCampaignsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddCampaignsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddCampaigns", action = "AddCampaigns")
    @WebResult(name = "AddCampaignsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddCampaignsResponse addCampaigns(
        @WebParam(name = "AddCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetCampaignsByAccountId", action = "GetCampaignsByAccountId")
    public Response<GetCampaignsByAccountIdResponse> getCampaignsByAccountIdAsync(
        @WebParam(name = "GetCampaignsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignsByAccountId", action = "GetCampaignsByAccountId")
    public Future<?> getCampaignsByAccountIdAsync(
        @WebParam(name = "GetCampaignsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByAccountIdRequest parameters,
        @WebParam(name = "GetCampaignsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignsByAccountId", action = "GetCampaignsByAccountId")
    @WebResult(name = "GetCampaignsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignsByAccountIdResponse getCampaignsByAccountId(
        @WebParam(name = "GetCampaignsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignsByIdsResponse>
     */
    @WebMethod(operationName = "GetCampaignsByIds", action = "GetCampaignsByIds")
    public Response<GetCampaignsByIdsResponse> getCampaignsByIdsAsync(
        @WebParam(name = "GetCampaignsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignsByIds", action = "GetCampaignsByIds")
    public Future<?> getCampaignsByIdsAsync(
        @WebParam(name = "GetCampaignsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByIdsRequest parameters,
        @WebParam(name = "GetCampaignsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignsByIds", action = "GetCampaignsByIds")
    @WebResult(name = "GetCampaignsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignsByIdsResponse getCampaignsByIds(
        @WebParam(name = "GetCampaignsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignsResponse>
     */
    @WebMethod(operationName = "DeleteCampaigns", action = "DeleteCampaigns")
    public Response<DeleteCampaignsResponse> deleteCampaignsAsync(
        @WebParam(name = "DeleteCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteCampaigns", action = "DeleteCampaigns")
    public Future<?> deleteCampaignsAsync(
        @WebParam(name = "DeleteCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignsRequest parameters,
        @WebParam(name = "DeleteCampaignsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteCampaignsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteCampaigns", action = "DeleteCampaigns")
    @WebResult(name = "DeleteCampaignsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteCampaignsResponse deleteCampaigns(
        @WebParam(name = "DeleteCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateCampaignsResponse>
     */
    @WebMethod(operationName = "UpdateCampaigns", action = "UpdateCampaigns")
    public Response<UpdateCampaignsResponse> updateCampaignsAsync(
        @WebParam(name = "UpdateCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateCampaigns", action = "UpdateCampaigns")
    public Future<?> updateCampaignsAsync(
        @WebParam(name = "UpdateCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignsRequest parameters,
        @WebParam(name = "UpdateCampaignsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateCampaignsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateCampaignsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateCampaigns", action = "UpdateCampaigns")
    @WebResult(name = "UpdateCampaignsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateCampaignsResponse updateCampaigns(
        @WebParam(name = "UpdateCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetNegativeSitesByCampaignIdsResponse>
     */
    @WebMethod(operationName = "GetNegativeSitesByCampaignIds", action = "GetNegativeSitesByCampaignIds")
    public Response<GetNegativeSitesByCampaignIdsResponse> getNegativeSitesByCampaignIdsAsync(
        @WebParam(name = "GetNegativeSitesByCampaignIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByCampaignIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetNegativeSitesByCampaignIds", action = "GetNegativeSitesByCampaignIds")
    public Future<?> getNegativeSitesByCampaignIdsAsync(
        @WebParam(name = "GetNegativeSitesByCampaignIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByCampaignIdsRequest parameters,
        @WebParam(name = "GetNegativeSitesByCampaignIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetNegativeSitesByCampaignIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetNegativeSitesByCampaignIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetNegativeSitesByCampaignIds", action = "GetNegativeSitesByCampaignIds")
    @WebResult(name = "GetNegativeSitesByCampaignIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetNegativeSitesByCampaignIdsResponse getNegativeSitesByCampaignIds(
        @WebParam(name = "GetNegativeSitesByCampaignIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByCampaignIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetNegativeSitesToCampaignsResponse>
     */
    @WebMethod(operationName = "SetNegativeSitesToCampaigns", action = "SetNegativeSitesToCampaigns")
    public Response<SetNegativeSitesToCampaignsResponse> setNegativeSitesToCampaignsAsync(
        @WebParam(name = "SetNegativeSitesToCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToCampaignsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetNegativeSitesToCampaigns", action = "SetNegativeSitesToCampaigns")
    public Future<?> setNegativeSitesToCampaignsAsync(
        @WebParam(name = "SetNegativeSitesToCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToCampaignsRequest parameters,
        @WebParam(name = "SetNegativeSitesToCampaignsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetNegativeSitesToCampaignsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetNegativeSitesToCampaignsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetNegativeSitesToCampaigns", action = "SetNegativeSitesToCampaigns")
    @WebResult(name = "SetNegativeSitesToCampaignsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetNegativeSitesToCampaignsResponse setNegativeSitesToCampaigns(
        @WebParam(name = "SetNegativeSitesToCampaignsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToCampaignsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetConfigValueResponse>
     */
    @WebMethod(operationName = "GetConfigValue", action = "GetConfigValue")
    public Response<GetConfigValueResponse> getConfigValueAsync(
        @WebParam(name = "GetConfigValueRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConfigValueRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetConfigValue", action = "GetConfigValue")
    public Future<?> getConfigValueAsync(
        @WebParam(name = "GetConfigValueRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConfigValueRequest parameters,
        @WebParam(name = "GetConfigValueResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetConfigValueResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetConfigValueResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetConfigValue", action = "GetConfigValue")
    @WebResult(name = "GetConfigValueResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetConfigValueResponse getConfigValue(
        @WebParam(name = "GetConfigValueRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConfigValueRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBSCCountriesResponse>
     */
    @WebMethod(operationName = "GetBSCCountries", action = "GetBSCCountries")
    public Response<GetBSCCountriesResponse> getBSCCountriesAsync(
        @WebParam(name = "GetBSCCountriesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBSCCountriesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBSCCountries", action = "GetBSCCountries")
    public Future<?> getBSCCountriesAsync(
        @WebParam(name = "GetBSCCountriesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBSCCountriesRequest parameters,
        @WebParam(name = "GetBSCCountriesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBSCCountriesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBSCCountriesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBSCCountries", action = "GetBSCCountries")
    @WebResult(name = "GetBSCCountriesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBSCCountriesResponse getBSCCountries(
        @WebParam(name = "GetBSCCountriesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBSCCountriesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAdGroupsResponse>
     */
    @WebMethod(operationName = "AddAdGroups", action = "AddAdGroups")
    public Response<AddAdGroupsResponse> addAdGroupsAsync(
        @WebParam(name = "AddAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAdGroups", action = "AddAdGroups")
    public Future<?> addAdGroupsAsync(
        @WebParam(name = "AddAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupsRequest parameters,
        @WebParam(name = "AddAdGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAdGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAdGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAdGroups", action = "AddAdGroups")
    @WebResult(name = "AddAdGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAdGroupsResponse addAdGroups(
        @WebParam(name = "AddAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAdGroupsResponse>
     */
    @WebMethod(operationName = "DeleteAdGroups", action = "DeleteAdGroups")
    public Response<DeleteAdGroupsResponse> deleteAdGroupsAsync(
        @WebParam(name = "DeleteAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAdGroups", action = "DeleteAdGroups")
    public Future<?> deleteAdGroupsAsync(
        @WebParam(name = "DeleteAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupsRequest parameters,
        @WebParam(name = "DeleteAdGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAdGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAdGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAdGroups", action = "DeleteAdGroups")
    @WebResult(name = "DeleteAdGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAdGroupsResponse deleteAdGroups(
        @WebParam(name = "DeleteAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdGroupsByIdsResponse>
     */
    @WebMethod(operationName = "GetAdGroupsByIds", action = "GetAdGroupsByIds")
    public Response<GetAdGroupsByIdsResponse> getAdGroupsByIdsAsync(
        @WebParam(name = "GetAdGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdGroupsByIds", action = "GetAdGroupsByIds")
    public Future<?> getAdGroupsByIdsAsync(
        @WebParam(name = "GetAdGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByIdsRequest parameters,
        @WebParam(name = "GetAdGroupsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdGroupsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdGroupsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdGroupsByIds", action = "GetAdGroupsByIds")
    @WebResult(name = "GetAdGroupsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdGroupsByIdsResponse getAdGroupsByIds(
        @WebParam(name = "GetAdGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdGroupsByCampaignIdResponse>
     */
    @WebMethod(operationName = "GetAdGroupsByCampaignId", action = "GetAdGroupsByCampaignId")
    public Response<GetAdGroupsByCampaignIdResponse> getAdGroupsByCampaignIdAsync(
        @WebParam(name = "GetAdGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByCampaignIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdGroupsByCampaignId", action = "GetAdGroupsByCampaignId")
    public Future<?> getAdGroupsByCampaignIdAsync(
        @WebParam(name = "GetAdGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByCampaignIdRequest parameters,
        @WebParam(name = "GetAdGroupsByCampaignIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdGroupsByCampaignIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdGroupsByCampaignIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdGroupsByCampaignId", action = "GetAdGroupsByCampaignId")
    @WebResult(name = "GetAdGroupsByCampaignIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdGroupsByCampaignIdResponse getAdGroupsByCampaignId(
        @WebParam(name = "GetAdGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupsByCampaignIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAdGroupsResponse>
     */
    @WebMethod(operationName = "UpdateAdGroups", action = "UpdateAdGroups")
    public Response<UpdateAdGroupsResponse> updateAdGroupsAsync(
        @WebParam(name = "UpdateAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAdGroups", action = "UpdateAdGroups")
    public Future<?> updateAdGroupsAsync(
        @WebParam(name = "UpdateAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupsRequest parameters,
        @WebParam(name = "UpdateAdGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAdGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAdGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAdGroups", action = "UpdateAdGroups")
    @WebResult(name = "UpdateAdGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAdGroupsResponse updateAdGroups(
        @WebParam(name = "UpdateAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetNegativeSitesByAdGroupIdsResponse>
     */
    @WebMethod(operationName = "GetNegativeSitesByAdGroupIds", action = "GetNegativeSitesByAdGroupIds")
    public Response<GetNegativeSitesByAdGroupIdsResponse> getNegativeSitesByAdGroupIdsAsync(
        @WebParam(name = "GetNegativeSitesByAdGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByAdGroupIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetNegativeSitesByAdGroupIds", action = "GetNegativeSitesByAdGroupIds")
    public Future<?> getNegativeSitesByAdGroupIdsAsync(
        @WebParam(name = "GetNegativeSitesByAdGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByAdGroupIdsRequest parameters,
        @WebParam(name = "GetNegativeSitesByAdGroupIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetNegativeSitesByAdGroupIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetNegativeSitesByAdGroupIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetNegativeSitesByAdGroupIds", action = "GetNegativeSitesByAdGroupIds")
    @WebResult(name = "GetNegativeSitesByAdGroupIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetNegativeSitesByAdGroupIdsResponse getNegativeSitesByAdGroupIds(
        @WebParam(name = "GetNegativeSitesByAdGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeSitesByAdGroupIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetNegativeSitesToAdGroupsResponse>
     */
    @WebMethod(operationName = "SetNegativeSitesToAdGroups", action = "SetNegativeSitesToAdGroups")
    public Response<SetNegativeSitesToAdGroupsResponse> setNegativeSitesToAdGroupsAsync(
        @WebParam(name = "SetNegativeSitesToAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToAdGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetNegativeSitesToAdGroups", action = "SetNegativeSitesToAdGroups")
    public Future<?> setNegativeSitesToAdGroupsAsync(
        @WebParam(name = "SetNegativeSitesToAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToAdGroupsRequest parameters,
        @WebParam(name = "SetNegativeSitesToAdGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetNegativeSitesToAdGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetNegativeSitesToAdGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetNegativeSitesToAdGroups", action = "SetNegativeSitesToAdGroups")
    @WebResult(name = "SetNegativeSitesToAdGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetNegativeSitesToAdGroupsResponse setNegativeSitesToAdGroups(
        @WebParam(name = "SetNegativeSitesToAdGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetNegativeSitesToAdGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetGeoLocationsFileUrlResponse>
     */
    @WebMethod(operationName = "GetGeoLocationsFileUrl", action = "GetGeoLocationsFileUrl")
    public Response<GetGeoLocationsFileUrlResponse> getGeoLocationsFileUrlAsync(
        @WebParam(name = "GetGeoLocationsFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetGeoLocationsFileUrlRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetGeoLocationsFileUrl", action = "GetGeoLocationsFileUrl")
    public Future<?> getGeoLocationsFileUrlAsync(
        @WebParam(name = "GetGeoLocationsFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetGeoLocationsFileUrlRequest parameters,
        @WebParam(name = "GetGeoLocationsFileUrlResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetGeoLocationsFileUrlResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetGeoLocationsFileUrlResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetGeoLocationsFileUrl", action = "GetGeoLocationsFileUrl")
    @WebResult(name = "GetGeoLocationsFileUrlResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetGeoLocationsFileUrlResponse getGeoLocationsFileUrl(
        @WebParam(name = "GetGeoLocationsFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetGeoLocationsFileUrlRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAdsResponse>
     */
    @WebMethod(operationName = "AddAds", action = "AddAds")
    public Response<AddAdsResponse> addAdsAsync(
        @WebParam(name = "AddAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAds", action = "AddAds")
    public Future<?> addAdsAsync(
        @WebParam(name = "AddAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdsRequest parameters,
        @WebParam(name = "AddAdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAds", action = "AddAds")
    @WebResult(name = "AddAdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAdsResponse addAds(
        @WebParam(name = "AddAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAdsResponse>
     */
    @WebMethod(operationName = "DeleteAds", action = "DeleteAds")
    public Response<DeleteAdsResponse> deleteAdsAsync(
        @WebParam(name = "DeleteAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAds", action = "DeleteAds")
    public Future<?> deleteAdsAsync(
        @WebParam(name = "DeleteAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdsRequest parameters,
        @WebParam(name = "DeleteAdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAds", action = "DeleteAds")
    @WebResult(name = "DeleteAdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAdsResponse deleteAds(
        @WebParam(name = "DeleteAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdsByEditorialStatusResponse>
     */
    @WebMethod(operationName = "GetAdsByEditorialStatus", action = "GetAdsByEditorialStatus")
    public Response<GetAdsByEditorialStatusResponse> getAdsByEditorialStatusAsync(
        @WebParam(name = "GetAdsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByEditorialStatusRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdsByEditorialStatus", action = "GetAdsByEditorialStatus")
    public Future<?> getAdsByEditorialStatusAsync(
        @WebParam(name = "GetAdsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByEditorialStatusRequest parameters,
        @WebParam(name = "GetAdsByEditorialStatusResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdsByEditorialStatusResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdsByEditorialStatusResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdsByEditorialStatus", action = "GetAdsByEditorialStatus")
    @WebResult(name = "GetAdsByEditorialStatusResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdsByEditorialStatusResponse getAdsByEditorialStatus(
        @WebParam(name = "GetAdsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByEditorialStatusRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdsByIdsResponse>
     */
    @WebMethod(operationName = "GetAdsByIds", action = "GetAdsByIds")
    public Response<GetAdsByIdsResponse> getAdsByIdsAsync(
        @WebParam(name = "GetAdsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdsByIds", action = "GetAdsByIds")
    public Future<?> getAdsByIdsAsync(
        @WebParam(name = "GetAdsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByIdsRequest parameters,
        @WebParam(name = "GetAdsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdsByIds", action = "GetAdsByIds")
    @WebResult(name = "GetAdsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdsByIdsResponse getAdsByIds(
        @WebParam(name = "GetAdsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdsByAdGroupIdResponse>
     */
    @WebMethod(operationName = "GetAdsByAdGroupId", action = "GetAdsByAdGroupId")
    public Response<GetAdsByAdGroupIdResponse> getAdsByAdGroupIdAsync(
        @WebParam(name = "GetAdsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByAdGroupIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdsByAdGroupId", action = "GetAdsByAdGroupId")
    public Future<?> getAdsByAdGroupIdAsync(
        @WebParam(name = "GetAdsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByAdGroupIdRequest parameters,
        @WebParam(name = "GetAdsByAdGroupIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdsByAdGroupIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdsByAdGroupIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdsByAdGroupId", action = "GetAdsByAdGroupId")
    @WebResult(name = "GetAdsByAdGroupIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdsByAdGroupIdResponse getAdsByAdGroupId(
        @WebParam(name = "GetAdsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdsByAdGroupIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAdsResponse>
     */
    @WebMethod(operationName = "UpdateAds", action = "UpdateAds")
    public Response<UpdateAdsResponse> updateAdsAsync(
        @WebParam(name = "UpdateAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAds", action = "UpdateAds")
    public Future<?> updateAdsAsync(
        @WebParam(name = "UpdateAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdsRequest parameters,
        @WebParam(name = "UpdateAdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAds", action = "UpdateAds")
    @WebResult(name = "UpdateAdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAdsResponse updateAds(
        @WebParam(name = "UpdateAdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddKeywordsResponse>
     */
    @WebMethod(operationName = "AddKeywords", action = "AddKeywords")
    public Response<AddKeywordsResponse> addKeywordsAsync(
        @WebParam(name = "AddKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddKeywordsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddKeywords", action = "AddKeywords")
    public Future<?> addKeywordsAsync(
        @WebParam(name = "AddKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddKeywordsRequest parameters,
        @WebParam(name = "AddKeywordsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddKeywordsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddKeywordsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddKeywords", action = "AddKeywords")
    @WebResult(name = "AddKeywordsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddKeywordsResponse addKeywords(
        @WebParam(name = "AddKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddKeywordsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteKeywordsResponse>
     */
    @WebMethod(operationName = "DeleteKeywords", action = "DeleteKeywords")
    public Response<DeleteKeywordsResponse> deleteKeywordsAsync(
        @WebParam(name = "DeleteKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteKeywordsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteKeywords", action = "DeleteKeywords")
    public Future<?> deleteKeywordsAsync(
        @WebParam(name = "DeleteKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteKeywordsRequest parameters,
        @WebParam(name = "DeleteKeywordsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteKeywordsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteKeywordsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteKeywords", action = "DeleteKeywords")
    @WebResult(name = "DeleteKeywordsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteKeywordsResponse deleteKeywords(
        @WebParam(name = "DeleteKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteKeywordsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByEditorialStatusResponse>
     */
    @WebMethod(operationName = "GetKeywordsByEditorialStatus", action = "GetKeywordsByEditorialStatus")
    public Response<GetKeywordsByEditorialStatusResponse> getKeywordsByEditorialStatusAsync(
        @WebParam(name = "GetKeywordsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByEditorialStatusRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetKeywordsByEditorialStatus", action = "GetKeywordsByEditorialStatus")
    public Future<?> getKeywordsByEditorialStatusAsync(
        @WebParam(name = "GetKeywordsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByEditorialStatusRequest parameters,
        @WebParam(name = "GetKeywordsByEditorialStatusResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetKeywordsByEditorialStatusResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByEditorialStatusResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetKeywordsByEditorialStatus", action = "GetKeywordsByEditorialStatus")
    @WebResult(name = "GetKeywordsByEditorialStatusResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetKeywordsByEditorialStatusResponse getKeywordsByEditorialStatus(
        @WebParam(name = "GetKeywordsByEditorialStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByEditorialStatusRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByIdsResponse>
     */
    @WebMethod(operationName = "GetKeywordsByIds", action = "GetKeywordsByIds")
    public Response<GetKeywordsByIdsResponse> getKeywordsByIdsAsync(
        @WebParam(name = "GetKeywordsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetKeywordsByIds", action = "GetKeywordsByIds")
    public Future<?> getKeywordsByIdsAsync(
        @WebParam(name = "GetKeywordsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByIdsRequest parameters,
        @WebParam(name = "GetKeywordsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetKeywordsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetKeywordsByIds", action = "GetKeywordsByIds")
    @WebResult(name = "GetKeywordsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetKeywordsByIdsResponse getKeywordsByIds(
        @WebParam(name = "GetKeywordsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByAdGroupIdResponse>
     */
    @WebMethod(operationName = "GetKeywordsByAdGroupId", action = "GetKeywordsByAdGroupId")
    public Response<GetKeywordsByAdGroupIdResponse> getKeywordsByAdGroupIdAsync(
        @WebParam(name = "GetKeywordsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByAdGroupIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetKeywordsByAdGroupId", action = "GetKeywordsByAdGroupId")
    public Future<?> getKeywordsByAdGroupIdAsync(
        @WebParam(name = "GetKeywordsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByAdGroupIdRequest parameters,
        @WebParam(name = "GetKeywordsByAdGroupIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetKeywordsByAdGroupIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetKeywordsByAdGroupIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetKeywordsByAdGroupId", action = "GetKeywordsByAdGroupId")
    @WebResult(name = "GetKeywordsByAdGroupIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetKeywordsByAdGroupIdResponse getKeywordsByAdGroupId(
        @WebParam(name = "GetKeywordsByAdGroupIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetKeywordsByAdGroupIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateKeywordsResponse>
     */
    @WebMethod(operationName = "UpdateKeywords", action = "UpdateKeywords")
    public Response<UpdateKeywordsResponse> updateKeywordsAsync(
        @WebParam(name = "UpdateKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateKeywordsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateKeywords", action = "UpdateKeywords")
    public Future<?> updateKeywordsAsync(
        @WebParam(name = "UpdateKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateKeywordsRequest parameters,
        @WebParam(name = "UpdateKeywordsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateKeywordsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateKeywordsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateKeywords", action = "UpdateKeywords")
    @WebResult(name = "UpdateKeywordsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateKeywordsResponse updateKeywords(
        @WebParam(name = "UpdateKeywordsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateKeywordsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AppealEditorialRejectionsResponse>
     */
    @WebMethod(operationName = "AppealEditorialRejections", action = "AppealEditorialRejections")
    public Response<AppealEditorialRejectionsResponse> appealEditorialRejectionsAsync(
        @WebParam(name = "AppealEditorialRejectionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AppealEditorialRejectionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AppealEditorialRejections", action = "AppealEditorialRejections")
    public Future<?> appealEditorialRejectionsAsync(
        @WebParam(name = "AppealEditorialRejectionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AppealEditorialRejectionsRequest parameters,
        @WebParam(name = "AppealEditorialRejectionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AppealEditorialRejectionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AppealEditorialRejectionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AppealEditorialRejections", action = "AppealEditorialRejections")
    @WebResult(name = "AppealEditorialRejectionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AppealEditorialRejectionsResponse appealEditorialRejections(
        @WebParam(name = "AppealEditorialRejectionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AppealEditorialRejectionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetEditorialReasonsByIdsResponse>
     */
    @WebMethod(operationName = "GetEditorialReasonsByIds", action = "GetEditorialReasonsByIds")
    public Response<GetEditorialReasonsByIdsResponse> getEditorialReasonsByIdsAsync(
        @WebParam(name = "GetEditorialReasonsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetEditorialReasonsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetEditorialReasonsByIds", action = "GetEditorialReasonsByIds")
    public Future<?> getEditorialReasonsByIdsAsync(
        @WebParam(name = "GetEditorialReasonsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetEditorialReasonsByIdsRequest parameters,
        @WebParam(name = "GetEditorialReasonsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetEditorialReasonsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetEditorialReasonsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetEditorialReasonsByIds", action = "GetEditorialReasonsByIds")
    @WebResult(name = "GetEditorialReasonsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetEditorialReasonsByIdsResponse getEditorialReasonsByIds(
        @WebParam(name = "GetEditorialReasonsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetEditorialReasonsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAccountMigrationStatusesResponse>
     */
    @WebMethod(operationName = "GetAccountMigrationStatuses", action = "GetAccountMigrationStatuses")
    public Response<GetAccountMigrationStatusesResponse> getAccountMigrationStatusesAsync(
        @WebParam(name = "GetAccountMigrationStatusesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountMigrationStatusesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAccountMigrationStatuses", action = "GetAccountMigrationStatuses")
    public Future<?> getAccountMigrationStatusesAsync(
        @WebParam(name = "GetAccountMigrationStatusesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountMigrationStatusesRequest parameters,
        @WebParam(name = "GetAccountMigrationStatusesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAccountMigrationStatusesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAccountMigrationStatusesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAccountMigrationStatuses", action = "GetAccountMigrationStatuses")
    @WebResult(name = "GetAccountMigrationStatusesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAccountMigrationStatusesResponse getAccountMigrationStatuses(
        @WebParam(name = "GetAccountMigrationStatusesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountMigrationStatusesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetAccountPropertiesResponse>
     */
    @WebMethod(operationName = "SetAccountProperties", action = "SetAccountProperties")
    public Response<SetAccountPropertiesResponse> setAccountPropertiesAsync(
        @WebParam(name = "SetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAccountPropertiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetAccountProperties", action = "SetAccountProperties")
    public Future<?> setAccountPropertiesAsync(
        @WebParam(name = "SetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAccountPropertiesRequest parameters,
        @WebParam(name = "SetAccountPropertiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetAccountPropertiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetAccountPropertiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetAccountProperties", action = "SetAccountProperties")
    @WebResult(name = "SetAccountPropertiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetAccountPropertiesResponse setAccountProperties(
        @WebParam(name = "SetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAccountPropertiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAccountPropertiesResponse>
     */
    @WebMethod(operationName = "GetAccountProperties", action = "GetAccountProperties")
    public Response<GetAccountPropertiesResponse> getAccountPropertiesAsync(
        @WebParam(name = "GetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountPropertiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAccountProperties", action = "GetAccountProperties")
    public Future<?> getAccountPropertiesAsync(
        @WebParam(name = "GetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountPropertiesRequest parameters,
        @WebParam(name = "GetAccountPropertiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAccountPropertiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAccountPropertiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAccountProperties", action = "GetAccountProperties")
    @WebResult(name = "GetAccountPropertiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAccountPropertiesResponse getAccountProperties(
        @WebParam(name = "GetAccountPropertiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAccountPropertiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAdExtensionsResponse>
     */
    @WebMethod(operationName = "AddAdExtensions", action = "AddAdExtensions")
    public Response<AddAdExtensionsResponse> addAdExtensionsAsync(
        @WebParam(name = "AddAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdExtensionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAdExtensions", action = "AddAdExtensions")
    public Future<?> addAdExtensionsAsync(
        @WebParam(name = "AddAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdExtensionsRequest parameters,
        @WebParam(name = "AddAdExtensionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAdExtensionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAdExtensionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAdExtensions", action = "AddAdExtensions")
    @WebResult(name = "AddAdExtensionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAdExtensionsResponse addAdExtensions(
        @WebParam(name = "AddAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdExtensionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsByIdsResponse>
     */
    @WebMethod(operationName = "GetAdExtensionsByIds", action = "GetAdExtensionsByIds")
    public Response<GetAdExtensionsByIdsResponse> getAdExtensionsByIdsAsync(
        @WebParam(name = "GetAdExtensionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdExtensionsByIds", action = "GetAdExtensionsByIds")
    public Future<?> getAdExtensionsByIdsAsync(
        @WebParam(name = "GetAdExtensionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsByIdsRequest parameters,
        @WebParam(name = "GetAdExtensionsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdExtensionsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdExtensionsByIds", action = "GetAdExtensionsByIds")
    @WebResult(name = "GetAdExtensionsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdExtensionsByIdsResponse getAdExtensionsByIds(
        @WebParam(name = "GetAdExtensionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAdExtensionsResponse>
     */
    @WebMethod(operationName = "UpdateAdExtensions", action = "UpdateAdExtensions")
    public Response<UpdateAdExtensionsResponse> updateAdExtensionsAsync(
        @WebParam(name = "UpdateAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdExtensionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAdExtensions", action = "UpdateAdExtensions")
    public Future<?> updateAdExtensionsAsync(
        @WebParam(name = "UpdateAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdExtensionsRequest parameters,
        @WebParam(name = "UpdateAdExtensionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAdExtensionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAdExtensionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAdExtensions", action = "UpdateAdExtensions")
    @WebResult(name = "UpdateAdExtensionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAdExtensionsResponse updateAdExtensions(
        @WebParam(name = "UpdateAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdExtensionsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAdExtensionsResponse>
     */
    @WebMethod(operationName = "DeleteAdExtensions", action = "DeleteAdExtensions")
    public Response<DeleteAdExtensionsResponse> deleteAdExtensionsAsync(
        @WebParam(name = "DeleteAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAdExtensions", action = "DeleteAdExtensions")
    public Future<?> deleteAdExtensionsAsync(
        @WebParam(name = "DeleteAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsRequest parameters,
        @WebParam(name = "DeleteAdExtensionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAdExtensionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAdExtensionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAdExtensions", action = "DeleteAdExtensions")
    @WebResult(name = "DeleteAdExtensionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAdExtensionsResponse deleteAdExtensions(
        @WebParam(name = "DeleteAdExtensionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsEditorialReasonsResponse>
     */
    @WebMethod(operationName = "GetAdExtensionsEditorialReasons", action = "GetAdExtensionsEditorialReasons")
    public Response<GetAdExtensionsEditorialReasonsResponse> getAdExtensionsEditorialReasonsAsync(
        @WebParam(name = "GetAdExtensionsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsEditorialReasonsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdExtensionsEditorialReasons", action = "GetAdExtensionsEditorialReasons")
    public Future<?> getAdExtensionsEditorialReasonsAsync(
        @WebParam(name = "GetAdExtensionsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsEditorialReasonsRequest parameters,
        @WebParam(name = "GetAdExtensionsEditorialReasonsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdExtensionsEditorialReasonsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsEditorialReasonsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdExtensionsEditorialReasons", action = "GetAdExtensionsEditorialReasons")
    @WebResult(name = "GetAdExtensionsEditorialReasonsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdExtensionsEditorialReasonsResponse getAdExtensionsEditorialReasons(
        @WebParam(name = "GetAdExtensionsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsEditorialReasonsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetAdExtensionsAssociationsResponse>
     */
    @WebMethod(operationName = "SetAdExtensionsAssociations", action = "SetAdExtensionsAssociations")
    public Response<SetAdExtensionsAssociationsResponse> setAdExtensionsAssociationsAsync(
        @WebParam(name = "SetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAdExtensionsAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetAdExtensionsAssociations", action = "SetAdExtensionsAssociations")
    public Future<?> setAdExtensionsAssociationsAsync(
        @WebParam(name = "SetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAdExtensionsAssociationsRequest parameters,
        @WebParam(name = "SetAdExtensionsAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetAdExtensionsAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetAdExtensionsAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetAdExtensionsAssociations", action = "SetAdExtensionsAssociations")
    @WebResult(name = "SetAdExtensionsAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetAdExtensionsAssociationsResponse setAdExtensionsAssociations(
        @WebParam(name = "SetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAdExtensionsAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsAssociationsResponse>
     */
    @WebMethod(operationName = "GetAdExtensionsAssociations", action = "GetAdExtensionsAssociations")
    public Response<GetAdExtensionsAssociationsResponse> getAdExtensionsAssociationsAsync(
        @WebParam(name = "GetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdExtensionsAssociations", action = "GetAdExtensionsAssociations")
    public Future<?> getAdExtensionsAssociationsAsync(
        @WebParam(name = "GetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsAssociationsRequest parameters,
        @WebParam(name = "GetAdExtensionsAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdExtensionsAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionsAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdExtensionsAssociations", action = "GetAdExtensionsAssociations")
    @WebResult(name = "GetAdExtensionsAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdExtensionsAssociationsResponse getAdExtensionsAssociations(
        @WebParam(name = "GetAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionsAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAdExtensionsAssociationsResponse>
     */
    @WebMethod(operationName = "DeleteAdExtensionsAssociations", action = "DeleteAdExtensionsAssociations")
    public Response<DeleteAdExtensionsAssociationsResponse> deleteAdExtensionsAssociationsAsync(
        @WebParam(name = "DeleteAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAdExtensionsAssociations", action = "DeleteAdExtensionsAssociations")
    public Future<?> deleteAdExtensionsAssociationsAsync(
        @WebParam(name = "DeleteAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsAssociationsRequest parameters,
        @WebParam(name = "DeleteAdExtensionsAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAdExtensionsAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAdExtensionsAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAdExtensionsAssociations", action = "DeleteAdExtensionsAssociations")
    @WebResult(name = "DeleteAdExtensionsAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAdExtensionsAssociationsResponse deleteAdExtensionsAssociations(
        @WebParam(name = "DeleteAdExtensionsAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdExtensionsAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionIdsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetAdExtensionIdsByAccountId", action = "GetAdExtensionIdsByAccountId")
    public Response<GetAdExtensionIdsByAccountIdResponse> getAdExtensionIdsByAccountIdAsync(
        @WebParam(name = "GetAdExtensionIdsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionIdsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdExtensionIdsByAccountId", action = "GetAdExtensionIdsByAccountId")
    public Future<?> getAdExtensionIdsByAccountIdAsync(
        @WebParam(name = "GetAdExtensionIdsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionIdsByAccountIdRequest parameters,
        @WebParam(name = "GetAdExtensionIdsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdExtensionIdsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdExtensionIdsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdExtensionIdsByAccountId", action = "GetAdExtensionIdsByAccountId")
    @WebResult(name = "GetAdExtensionIdsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdExtensionIdsByAccountIdResponse getAdExtensionIdsByAccountId(
        @WebParam(name = "GetAdExtensionIdsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdExtensionIdsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddMediaResponse>
     */
    @WebMethod(operationName = "AddMedia", action = "AddMedia")
    public Response<AddMediaResponse> addMediaAsync(
        @WebParam(name = "AddMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddMediaRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddMedia", action = "AddMedia")
    public Future<?> addMediaAsync(
        @WebParam(name = "AddMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddMediaRequest parameters,
        @WebParam(name = "AddMediaResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddMediaResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddMediaResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddMedia", action = "AddMedia")
    @WebResult(name = "AddMediaResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddMediaResponse addMedia(
        @WebParam(name = "AddMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddMediaRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteMediaResponse>
     */
    @WebMethod(operationName = "DeleteMedia", action = "DeleteMedia")
    public Response<DeleteMediaResponse> deleteMediaAsync(
        @WebParam(name = "DeleteMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteMediaRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteMedia", action = "DeleteMedia")
    public Future<?> deleteMediaAsync(
        @WebParam(name = "DeleteMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteMediaRequest parameters,
        @WebParam(name = "DeleteMediaResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteMediaResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteMediaResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteMedia", action = "DeleteMedia")
    @WebResult(name = "DeleteMediaResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteMediaResponse deleteMedia(
        @WebParam(name = "DeleteMediaRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteMediaRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetMediaMetaDataByAccountIdResponse>
     */
    @WebMethod(operationName = "GetMediaMetaDataByAccountId", action = "GetMediaMetaDataByAccountId")
    public Response<GetMediaMetaDataByAccountIdResponse> getMediaMetaDataByAccountIdAsync(
        @WebParam(name = "GetMediaMetaDataByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetMediaMetaDataByAccountId", action = "GetMediaMetaDataByAccountId")
    public Future<?> getMediaMetaDataByAccountIdAsync(
        @WebParam(name = "GetMediaMetaDataByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByAccountIdRequest parameters,
        @WebParam(name = "GetMediaMetaDataByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetMediaMetaDataByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetMediaMetaDataByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetMediaMetaDataByAccountId", action = "GetMediaMetaDataByAccountId")
    @WebResult(name = "GetMediaMetaDataByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetMediaMetaDataByAccountIdResponse getMediaMetaDataByAccountId(
        @WebParam(name = "GetMediaMetaDataByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetMediaMetaDataByIdsResponse>
     */
    @WebMethod(operationName = "GetMediaMetaDataByIds", action = "GetMediaMetaDataByIds")
    public Response<GetMediaMetaDataByIdsResponse> getMediaMetaDataByIdsAsync(
        @WebParam(name = "GetMediaMetaDataByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetMediaMetaDataByIds", action = "GetMediaMetaDataByIds")
    public Future<?> getMediaMetaDataByIdsAsync(
        @WebParam(name = "GetMediaMetaDataByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByIdsRequest parameters,
        @WebParam(name = "GetMediaMetaDataByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetMediaMetaDataByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetMediaMetaDataByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetMediaMetaDataByIds", action = "GetMediaMetaDataByIds")
    @WebResult(name = "GetMediaMetaDataByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetMediaMetaDataByIdsResponse getMediaMetaDataByIds(
        @WebParam(name = "GetMediaMetaDataByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaMetaDataByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetMediaAssociationsResponse>
     */
    @WebMethod(operationName = "GetMediaAssociations", action = "GetMediaAssociations")
    public Response<GetMediaAssociationsResponse> getMediaAssociationsAsync(
        @WebParam(name = "GetMediaAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetMediaAssociations", action = "GetMediaAssociations")
    public Future<?> getMediaAssociationsAsync(
        @WebParam(name = "GetMediaAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaAssociationsRequest parameters,
        @WebParam(name = "GetMediaAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetMediaAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetMediaAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetMediaAssociations", action = "GetMediaAssociations")
    @WebResult(name = "GetMediaAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetMediaAssociationsResponse getMediaAssociations(
        @WebParam(name = "GetMediaAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetMediaAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAdGroupCriterionsByIdsResponse>
     */
    @WebMethod(operationName = "GetAdGroupCriterionsByIds", action = "GetAdGroupCriterionsByIds")
    public Response<GetAdGroupCriterionsByIdsResponse> getAdGroupCriterionsByIdsAsync(
        @WebParam(name = "GetAdGroupCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupCriterionsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAdGroupCriterionsByIds", action = "GetAdGroupCriterionsByIds")
    public Future<?> getAdGroupCriterionsByIdsAsync(
        @WebParam(name = "GetAdGroupCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupCriterionsByIdsRequest parameters,
        @WebParam(name = "GetAdGroupCriterionsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAdGroupCriterionsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAdGroupCriterionsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAdGroupCriterionsByIds", action = "GetAdGroupCriterionsByIds")
    @WebResult(name = "GetAdGroupCriterionsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAdGroupCriterionsByIdsResponse getAdGroupCriterionsByIds(
        @WebParam(name = "GetAdGroupCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAdGroupCriterionsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAdGroupCriterionsResponse>
     */
    @WebMethod(operationName = "AddAdGroupCriterions", action = "AddAdGroupCriterions")
    public Response<AddAdGroupCriterionsResponse> addAdGroupCriterionsAsync(
        @WebParam(name = "AddAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAdGroupCriterions", action = "AddAdGroupCriterions")
    public Future<?> addAdGroupCriterionsAsync(
        @WebParam(name = "AddAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupCriterionsRequest parameters,
        @WebParam(name = "AddAdGroupCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAdGroupCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAdGroupCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAdGroupCriterions", action = "AddAdGroupCriterions")
    @WebResult(name = "AddAdGroupCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAdGroupCriterionsResponse addAdGroupCriterions(
        @WebParam(name = "AddAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAdGroupCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAdGroupCriterionsResponse>
     */
    @WebMethod(operationName = "UpdateAdGroupCriterions", action = "UpdateAdGroupCriterions")
    public Response<UpdateAdGroupCriterionsResponse> updateAdGroupCriterionsAsync(
        @WebParam(name = "UpdateAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAdGroupCriterions", action = "UpdateAdGroupCriterions")
    public Future<?> updateAdGroupCriterionsAsync(
        @WebParam(name = "UpdateAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupCriterionsRequest parameters,
        @WebParam(name = "UpdateAdGroupCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAdGroupCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAdGroupCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws EditorialApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAdGroupCriterions", action = "UpdateAdGroupCriterions")
    @WebResult(name = "UpdateAdGroupCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAdGroupCriterionsResponse updateAdGroupCriterions(
        @WebParam(name = "UpdateAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAdGroupCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, EditorialApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAdGroupCriterionsResponse>
     */
    @WebMethod(operationName = "DeleteAdGroupCriterions", action = "DeleteAdGroupCriterions")
    public Response<DeleteAdGroupCriterionsResponse> deleteAdGroupCriterionsAsync(
        @WebParam(name = "DeleteAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAdGroupCriterions", action = "DeleteAdGroupCriterions")
    public Future<?> deleteAdGroupCriterionsAsync(
        @WebParam(name = "DeleteAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupCriterionsRequest parameters,
        @WebParam(name = "DeleteAdGroupCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAdGroupCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAdGroupCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAdGroupCriterions", action = "DeleteAdGroupCriterions")
    @WebResult(name = "DeleteAdGroupCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAdGroupCriterionsResponse deleteAdGroupCriterions(
        @WebParam(name = "DeleteAdGroupCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAdGroupCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyProductPartitionActionsResponse>
     */
    @WebMethod(operationName = "ApplyProductPartitionActions", action = "ApplyProductPartitionActions")
    public Response<ApplyProductPartitionActionsResponse> applyProductPartitionActionsAsync(
        @WebParam(name = "ApplyProductPartitionActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyProductPartitionActionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyProductPartitionActions", action = "ApplyProductPartitionActions")
    public Future<?> applyProductPartitionActionsAsync(
        @WebParam(name = "ApplyProductPartitionActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyProductPartitionActionsRequest parameters,
        @WebParam(name = "ApplyProductPartitionActionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyProductPartitionActionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyProductPartitionActionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyProductPartitionActions", action = "ApplyProductPartitionActions")
    @WebResult(name = "ApplyProductPartitionActionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyProductPartitionActionsResponse applyProductPartitionActions(
        @WebParam(name = "ApplyProductPartitionActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyProductPartitionActionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyHotelGroupActionsResponse>
     */
    @WebMethod(operationName = "ApplyHotelGroupActions", action = "ApplyHotelGroupActions")
    public Response<ApplyHotelGroupActionsResponse> applyHotelGroupActionsAsync(
        @WebParam(name = "ApplyHotelGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyHotelGroupActionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyHotelGroupActions", action = "ApplyHotelGroupActions")
    public Future<?> applyHotelGroupActionsAsync(
        @WebParam(name = "ApplyHotelGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyHotelGroupActionsRequest parameters,
        @WebParam(name = "ApplyHotelGroupActionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyHotelGroupActionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyHotelGroupActionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyHotelGroupActions", action = "ApplyHotelGroupActions")
    @WebResult(name = "ApplyHotelGroupActionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyHotelGroupActionsResponse applyHotelGroupActions(
        @WebParam(name = "ApplyHotelGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyHotelGroupActionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyAssetGroupListingGroupActionsResponse>
     */
    @WebMethod(operationName = "ApplyAssetGroupListingGroupActions", action = "ApplyAssetGroupListingGroupActions")
    public Response<ApplyAssetGroupListingGroupActionsResponse> applyAssetGroupListingGroupActionsAsync(
        @WebParam(name = "ApplyAssetGroupListingGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyAssetGroupListingGroupActionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyAssetGroupListingGroupActions", action = "ApplyAssetGroupListingGroupActions")
    public Future<?> applyAssetGroupListingGroupActionsAsync(
        @WebParam(name = "ApplyAssetGroupListingGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyAssetGroupListingGroupActionsRequest parameters,
        @WebParam(name = "ApplyAssetGroupListingGroupActionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyAssetGroupListingGroupActionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyAssetGroupListingGroupActionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyAssetGroupListingGroupActions", action = "ApplyAssetGroupListingGroupActions")
    @WebResult(name = "ApplyAssetGroupListingGroupActionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyAssetGroupListingGroupActionsResponse applyAssetGroupListingGroupActions(
        @WebParam(name = "ApplyAssetGroupListingGroupActionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyAssetGroupListingGroupActionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupListingGroupsByIdsResponse>
     */
    @WebMethod(operationName = "GetAssetGroupListingGroupsByIds", action = "GetAssetGroupListingGroupsByIds")
    public Response<GetAssetGroupListingGroupsByIdsResponse> getAssetGroupListingGroupsByIdsAsync(
        @WebParam(name = "GetAssetGroupListingGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupListingGroupsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAssetGroupListingGroupsByIds", action = "GetAssetGroupListingGroupsByIds")
    public Future<?> getAssetGroupListingGroupsByIdsAsync(
        @WebParam(name = "GetAssetGroupListingGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupListingGroupsByIdsRequest parameters,
        @WebParam(name = "GetAssetGroupListingGroupsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAssetGroupListingGroupsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupListingGroupsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAssetGroupListingGroupsByIds", action = "GetAssetGroupListingGroupsByIds")
    @WebResult(name = "GetAssetGroupListingGroupsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAssetGroupListingGroupsByIdsResponse getAssetGroupListingGroupsByIds(
        @WebParam(name = "GetAssetGroupListingGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupListingGroupsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBMCStoresByCustomerIdResponse>
     */
    @WebMethod(operationName = "GetBMCStoresByCustomerId", action = "GetBMCStoresByCustomerId")
    public Response<GetBMCStoresByCustomerIdResponse> getBMCStoresByCustomerIdAsync(
        @WebParam(name = "GetBMCStoresByCustomerIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBMCStoresByCustomerIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBMCStoresByCustomerId", action = "GetBMCStoresByCustomerId")
    public Future<?> getBMCStoresByCustomerIdAsync(
        @WebParam(name = "GetBMCStoresByCustomerIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBMCStoresByCustomerIdRequest parameters,
        @WebParam(name = "GetBMCStoresByCustomerIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBMCStoresByCustomerIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBMCStoresByCustomerIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBMCStoresByCustomerId", action = "GetBMCStoresByCustomerId")
    @WebResult(name = "GetBMCStoresByCustomerIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBMCStoresByCustomerIdResponse getBMCStoresByCustomerId(
        @WebParam(name = "GetBMCStoresByCustomerIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBMCStoresByCustomerIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddNegativeKeywordsToEntitiesResponse>
     */
    @WebMethod(operationName = "AddNegativeKeywordsToEntities", action = "AddNegativeKeywordsToEntities")
    public Response<AddNegativeKeywordsToEntitiesResponse> addNegativeKeywordsToEntitiesAsync(
        @WebParam(name = "AddNegativeKeywordsToEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNegativeKeywordsToEntitiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddNegativeKeywordsToEntities", action = "AddNegativeKeywordsToEntities")
    public Future<?> addNegativeKeywordsToEntitiesAsync(
        @WebParam(name = "AddNegativeKeywordsToEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNegativeKeywordsToEntitiesRequest parameters,
        @WebParam(name = "AddNegativeKeywordsToEntitiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddNegativeKeywordsToEntitiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddNegativeKeywordsToEntitiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddNegativeKeywordsToEntities", action = "AddNegativeKeywordsToEntities")
    @WebResult(name = "AddNegativeKeywordsToEntitiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddNegativeKeywordsToEntitiesResponse addNegativeKeywordsToEntities(
        @WebParam(name = "AddNegativeKeywordsToEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNegativeKeywordsToEntitiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetNegativeKeywordsByEntityIdsResponse>
     */
    @WebMethod(operationName = "GetNegativeKeywordsByEntityIds", action = "GetNegativeKeywordsByEntityIds")
    public Response<GetNegativeKeywordsByEntityIdsResponse> getNegativeKeywordsByEntityIdsAsync(
        @WebParam(name = "GetNegativeKeywordsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeKeywordsByEntityIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetNegativeKeywordsByEntityIds", action = "GetNegativeKeywordsByEntityIds")
    public Future<?> getNegativeKeywordsByEntityIdsAsync(
        @WebParam(name = "GetNegativeKeywordsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeKeywordsByEntityIdsRequest parameters,
        @WebParam(name = "GetNegativeKeywordsByEntityIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetNegativeKeywordsByEntityIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetNegativeKeywordsByEntityIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetNegativeKeywordsByEntityIds", action = "GetNegativeKeywordsByEntityIds")
    @WebResult(name = "GetNegativeKeywordsByEntityIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetNegativeKeywordsByEntityIdsResponse getNegativeKeywordsByEntityIds(
        @WebParam(name = "GetNegativeKeywordsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNegativeKeywordsByEntityIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteNegativeKeywordsFromEntitiesResponse>
     */
    @WebMethod(operationName = "DeleteNegativeKeywordsFromEntities", action = "DeleteNegativeKeywordsFromEntities")
    public Response<DeleteNegativeKeywordsFromEntitiesResponse> deleteNegativeKeywordsFromEntitiesAsync(
        @WebParam(name = "DeleteNegativeKeywordsFromEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteNegativeKeywordsFromEntitiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteNegativeKeywordsFromEntities", action = "DeleteNegativeKeywordsFromEntities")
    public Future<?> deleteNegativeKeywordsFromEntitiesAsync(
        @WebParam(name = "DeleteNegativeKeywordsFromEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteNegativeKeywordsFromEntitiesRequest parameters,
        @WebParam(name = "DeleteNegativeKeywordsFromEntitiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteNegativeKeywordsFromEntitiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteNegativeKeywordsFromEntitiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteNegativeKeywordsFromEntities", action = "DeleteNegativeKeywordsFromEntities")
    @WebResult(name = "DeleteNegativeKeywordsFromEntitiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteNegativeKeywordsFromEntitiesResponse deleteNegativeKeywordsFromEntities(
        @WebParam(name = "DeleteNegativeKeywordsFromEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteNegativeKeywordsFromEntitiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSharedEntitiesByAccountIdResponse>
     */
    @WebMethod(operationName = "GetSharedEntitiesByAccountId", action = "GetSharedEntitiesByAccountId")
    public Response<GetSharedEntitiesByAccountIdResponse> getSharedEntitiesByAccountIdAsync(
        @WebParam(name = "GetSharedEntitiesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSharedEntitiesByAccountId", action = "GetSharedEntitiesByAccountId")
    public Future<?> getSharedEntitiesByAccountIdAsync(
        @WebParam(name = "GetSharedEntitiesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesByAccountIdRequest parameters,
        @WebParam(name = "GetSharedEntitiesByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSharedEntitiesByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSharedEntitiesByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSharedEntitiesByAccountId", action = "GetSharedEntitiesByAccountId")
    @WebResult(name = "GetSharedEntitiesByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSharedEntitiesByAccountIdResponse getSharedEntitiesByAccountId(
        @WebParam(name = "GetSharedEntitiesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSharedEntitiesResponse>
     */
    @WebMethod(operationName = "GetSharedEntities", action = "GetSharedEntities")
    public Response<GetSharedEntitiesResponse> getSharedEntitiesAsync(
        @WebParam(name = "GetSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSharedEntities", action = "GetSharedEntities")
    public Future<?> getSharedEntitiesAsync(
        @WebParam(name = "GetSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesRequest parameters,
        @WebParam(name = "GetSharedEntitiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSharedEntitiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSharedEntitiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSharedEntities", action = "GetSharedEntities")
    @WebResult(name = "GetSharedEntitiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSharedEntitiesResponse getSharedEntities(
        @WebParam(name = "GetSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntitiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddSharedEntityResponse>
     */
    @WebMethod(operationName = "AddSharedEntity", action = "AddSharedEntity")
    public Response<AddSharedEntityResponse> addSharedEntityAsync(
        @WebParam(name = "AddSharedEntityRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSharedEntityRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddSharedEntity", action = "AddSharedEntity")
    public Future<?> addSharedEntityAsync(
        @WebParam(name = "AddSharedEntityRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSharedEntityRequest parameters,
        @WebParam(name = "AddSharedEntityResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddSharedEntityResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddSharedEntityResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddSharedEntity", action = "AddSharedEntity")
    @WebResult(name = "AddSharedEntityResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddSharedEntityResponse addSharedEntity(
        @WebParam(name = "AddSharedEntityRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSharedEntityRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetListItemsBySharedListResponse>
     */
    @WebMethod(operationName = "GetListItemsBySharedList", action = "GetListItemsBySharedList")
    public Response<GetListItemsBySharedListResponse> getListItemsBySharedListAsync(
        @WebParam(name = "GetListItemsBySharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetListItemsBySharedListRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetListItemsBySharedList", action = "GetListItemsBySharedList")
    public Future<?> getListItemsBySharedListAsync(
        @WebParam(name = "GetListItemsBySharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetListItemsBySharedListRequest parameters,
        @WebParam(name = "GetListItemsBySharedListResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetListItemsBySharedListResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetListItemsBySharedListResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetListItemsBySharedList", action = "GetListItemsBySharedList")
    @WebResult(name = "GetListItemsBySharedListResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetListItemsBySharedListResponse getListItemsBySharedList(
        @WebParam(name = "GetListItemsBySharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetListItemsBySharedListRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddListItemsToSharedListResponse>
     */
    @WebMethod(operationName = "AddListItemsToSharedList", action = "AddListItemsToSharedList")
    public Response<AddListItemsToSharedListResponse> addListItemsToSharedListAsync(
        @WebParam(name = "AddListItemsToSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddListItemsToSharedListRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddListItemsToSharedList", action = "AddListItemsToSharedList")
    public Future<?> addListItemsToSharedListAsync(
        @WebParam(name = "AddListItemsToSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddListItemsToSharedListRequest parameters,
        @WebParam(name = "AddListItemsToSharedListResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddListItemsToSharedListResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddListItemsToSharedListResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddListItemsToSharedList", action = "AddListItemsToSharedList")
    @WebResult(name = "AddListItemsToSharedListResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddListItemsToSharedListResponse addListItemsToSharedList(
        @WebParam(name = "AddListItemsToSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddListItemsToSharedListRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateSharedEntitiesResponse>
     */
    @WebMethod(operationName = "UpdateSharedEntities", action = "UpdateSharedEntities")
    public Response<UpdateSharedEntitiesResponse> updateSharedEntitiesAsync(
        @WebParam(name = "UpdateSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSharedEntitiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateSharedEntities", action = "UpdateSharedEntities")
    public Future<?> updateSharedEntitiesAsync(
        @WebParam(name = "UpdateSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSharedEntitiesRequest parameters,
        @WebParam(name = "UpdateSharedEntitiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateSharedEntitiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateSharedEntitiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateSharedEntities", action = "UpdateSharedEntities")
    @WebResult(name = "UpdateSharedEntitiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateSharedEntitiesResponse updateSharedEntities(
        @WebParam(name = "UpdateSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSharedEntitiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteListItemsFromSharedListResponse>
     */
    @WebMethod(operationName = "DeleteListItemsFromSharedList", action = "DeleteListItemsFromSharedList")
    public Response<DeleteListItemsFromSharedListResponse> deleteListItemsFromSharedListAsync(
        @WebParam(name = "DeleteListItemsFromSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteListItemsFromSharedListRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteListItemsFromSharedList", action = "DeleteListItemsFromSharedList")
    public Future<?> deleteListItemsFromSharedListAsync(
        @WebParam(name = "DeleteListItemsFromSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteListItemsFromSharedListRequest parameters,
        @WebParam(name = "DeleteListItemsFromSharedListResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteListItemsFromSharedListResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteListItemsFromSharedListResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteListItemsFromSharedList", action = "DeleteListItemsFromSharedList")
    @WebResult(name = "DeleteListItemsFromSharedListResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteListItemsFromSharedListResponse deleteListItemsFromSharedList(
        @WebParam(name = "DeleteListItemsFromSharedListRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteListItemsFromSharedListRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetSharedEntityAssociationsResponse>
     */
    @WebMethod(operationName = "SetSharedEntityAssociations", action = "SetSharedEntityAssociations")
    public Response<SetSharedEntityAssociationsResponse> setSharedEntityAssociationsAsync(
        @WebParam(name = "SetSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetSharedEntityAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetSharedEntityAssociations", action = "SetSharedEntityAssociations")
    public Future<?> setSharedEntityAssociationsAsync(
        @WebParam(name = "SetSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetSharedEntityAssociationsRequest parameters,
        @WebParam(name = "SetSharedEntityAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetSharedEntityAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetSharedEntityAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetSharedEntityAssociations", action = "SetSharedEntityAssociations")
    @WebResult(name = "SetSharedEntityAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetSharedEntityAssociationsResponse setSharedEntityAssociations(
        @WebParam(name = "SetSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetSharedEntityAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteSharedEntityAssociationsResponse>
     */
    @WebMethod(operationName = "DeleteSharedEntityAssociations", action = "DeleteSharedEntityAssociations")
    public Response<DeleteSharedEntityAssociationsResponse> deleteSharedEntityAssociationsAsync(
        @WebParam(name = "DeleteSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntityAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteSharedEntityAssociations", action = "DeleteSharedEntityAssociations")
    public Future<?> deleteSharedEntityAssociationsAsync(
        @WebParam(name = "DeleteSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntityAssociationsRequest parameters,
        @WebParam(name = "DeleteSharedEntityAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteSharedEntityAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteSharedEntityAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteSharedEntityAssociations", action = "DeleteSharedEntityAssociations")
    @WebResult(name = "DeleteSharedEntityAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteSharedEntityAssociationsResponse deleteSharedEntityAssociations(
        @WebParam(name = "DeleteSharedEntityAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntityAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSharedEntityAssociationsBySharedEntityIdsResponse>
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsBySharedEntityIds", action = "GetSharedEntityAssociationsBySharedEntityIds")
    public Response<GetSharedEntityAssociationsBySharedEntityIdsResponse> getSharedEntityAssociationsBySharedEntityIdsAsync(
        @WebParam(name = "GetSharedEntityAssociationsBySharedEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsBySharedEntityIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsBySharedEntityIds", action = "GetSharedEntityAssociationsBySharedEntityIds")
    public Future<?> getSharedEntityAssociationsBySharedEntityIdsAsync(
        @WebParam(name = "GetSharedEntityAssociationsBySharedEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsBySharedEntityIdsRequest parameters,
        @WebParam(name = "GetSharedEntityAssociationsBySharedEntityIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSharedEntityAssociationsBySharedEntityIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSharedEntityAssociationsBySharedEntityIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsBySharedEntityIds", action = "GetSharedEntityAssociationsBySharedEntityIds")
    @WebResult(name = "GetSharedEntityAssociationsBySharedEntityIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSharedEntityAssociationsBySharedEntityIdsResponse getSharedEntityAssociationsBySharedEntityIds(
        @WebParam(name = "GetSharedEntityAssociationsBySharedEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsBySharedEntityIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSharedEntityAssociationsByEntityIdsResponse>
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsByEntityIds", action = "GetSharedEntityAssociationsByEntityIds")
    public Response<GetSharedEntityAssociationsByEntityIdsResponse> getSharedEntityAssociationsByEntityIdsAsync(
        @WebParam(name = "GetSharedEntityAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsByEntityIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsByEntityIds", action = "GetSharedEntityAssociationsByEntityIds")
    public Future<?> getSharedEntityAssociationsByEntityIdsAsync(
        @WebParam(name = "GetSharedEntityAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsByEntityIdsRequest parameters,
        @WebParam(name = "GetSharedEntityAssociationsByEntityIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSharedEntityAssociationsByEntityIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSharedEntityAssociationsByEntityIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSharedEntityAssociationsByEntityIds", action = "GetSharedEntityAssociationsByEntityIds")
    @WebResult(name = "GetSharedEntityAssociationsByEntityIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSharedEntityAssociationsByEntityIdsResponse getSharedEntityAssociationsByEntityIds(
        @WebParam(name = "GetSharedEntityAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSharedEntityAssociationsByEntityIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteSharedEntitiesResponse>
     */
    @WebMethod(operationName = "DeleteSharedEntities", action = "DeleteSharedEntities")
    public Response<DeleteSharedEntitiesResponse> deleteSharedEntitiesAsync(
        @WebParam(name = "DeleteSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntitiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteSharedEntities", action = "DeleteSharedEntities")
    public Future<?> deleteSharedEntitiesAsync(
        @WebParam(name = "DeleteSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntitiesRequest parameters,
        @WebParam(name = "DeleteSharedEntitiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteSharedEntitiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteSharedEntitiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteSharedEntities", action = "DeleteSharedEntities")
    @WebResult(name = "DeleteSharedEntitiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteSharedEntitiesResponse deleteSharedEntities(
        @WebParam(name = "DeleteSharedEntitiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSharedEntitiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignSizesByAccountIdResponse>
     */
    @WebMethod(operationName = "GetCampaignSizesByAccountId", action = "GetCampaignSizesByAccountId")
    public Response<GetCampaignSizesByAccountIdResponse> getCampaignSizesByAccountIdAsync(
        @WebParam(name = "GetCampaignSizesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignSizesByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignSizesByAccountId", action = "GetCampaignSizesByAccountId")
    public Future<?> getCampaignSizesByAccountIdAsync(
        @WebParam(name = "GetCampaignSizesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignSizesByAccountIdRequest parameters,
        @WebParam(name = "GetCampaignSizesByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignSizesByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignSizesByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignSizesByAccountId", action = "GetCampaignSizesByAccountId")
    @WebResult(name = "GetCampaignSizesByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignSizesByAccountIdResponse getCampaignSizesByAccountId(
        @WebParam(name = "GetCampaignSizesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignSizesByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddCampaignCriterionsResponse>
     */
    @WebMethod(operationName = "AddCampaignCriterions", action = "AddCampaignCriterions")
    public Response<AddCampaignCriterionsResponse> addCampaignCriterionsAsync(
        @WebParam(name = "AddCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddCampaignCriterions", action = "AddCampaignCriterions")
    public Future<?> addCampaignCriterionsAsync(
        @WebParam(name = "AddCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignCriterionsRequest parameters,
        @WebParam(name = "AddCampaignCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddCampaignCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddCampaignCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddCampaignCriterions", action = "AddCampaignCriterions")
    @WebResult(name = "AddCampaignCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddCampaignCriterionsResponse addCampaignCriterions(
        @WebParam(name = "AddCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateCampaignCriterionsResponse>
     */
    @WebMethod(operationName = "UpdateCampaignCriterions", action = "UpdateCampaignCriterions")
    public Response<UpdateCampaignCriterionsResponse> updateCampaignCriterionsAsync(
        @WebParam(name = "UpdateCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateCampaignCriterions", action = "UpdateCampaignCriterions")
    public Future<?> updateCampaignCriterionsAsync(
        @WebParam(name = "UpdateCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignCriterionsRequest parameters,
        @WebParam(name = "UpdateCampaignCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateCampaignCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateCampaignCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateCampaignCriterions", action = "UpdateCampaignCriterions")
    @WebResult(name = "UpdateCampaignCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateCampaignCriterionsResponse updateCampaignCriterions(
        @WebParam(name = "UpdateCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateCampaignCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignCriterionsResponse>
     */
    @WebMethod(operationName = "DeleteCampaignCriterions", action = "DeleteCampaignCriterions")
    public Response<DeleteCampaignCriterionsResponse> deleteCampaignCriterionsAsync(
        @WebParam(name = "DeleteCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignCriterionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteCampaignCriterions", action = "DeleteCampaignCriterions")
    public Future<?> deleteCampaignCriterionsAsync(
        @WebParam(name = "DeleteCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignCriterionsRequest parameters,
        @WebParam(name = "DeleteCampaignCriterionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteCampaignCriterionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignCriterionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteCampaignCriterions", action = "DeleteCampaignCriterions")
    @WebResult(name = "DeleteCampaignCriterionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteCampaignCriterionsResponse deleteCampaignCriterions(
        @WebParam(name = "DeleteCampaignCriterionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignCriterionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignCriterionsByIdsResponse>
     */
    @WebMethod(operationName = "GetCampaignCriterionsByIds", action = "GetCampaignCriterionsByIds")
    public Response<GetCampaignCriterionsByIdsResponse> getCampaignCriterionsByIdsAsync(
        @WebParam(name = "GetCampaignCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignCriterionsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignCriterionsByIds", action = "GetCampaignCriterionsByIds")
    public Future<?> getCampaignCriterionsByIdsAsync(
        @WebParam(name = "GetCampaignCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignCriterionsByIdsRequest parameters,
        @WebParam(name = "GetCampaignCriterionsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignCriterionsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignCriterionsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignCriterionsByIds", action = "GetCampaignCriterionsByIds")
    @WebResult(name = "GetCampaignCriterionsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignCriterionsByIdsResponse getCampaignCriterionsByIds(
        @WebParam(name = "GetCampaignCriterionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignCriterionsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddBudgetsResponse>
     */
    @WebMethod(operationName = "AddBudgets", action = "AddBudgets")
    public Response<AddBudgetsResponse> addBudgetsAsync(
        @WebParam(name = "AddBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBudgetsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddBudgets", action = "AddBudgets")
    public Future<?> addBudgetsAsync(
        @WebParam(name = "AddBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBudgetsRequest parameters,
        @WebParam(name = "AddBudgetsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddBudgetsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddBudgetsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddBudgets", action = "AddBudgets")
    @WebResult(name = "AddBudgetsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddBudgetsResponse addBudgets(
        @WebParam(name = "AddBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBudgetsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateBudgetsResponse>
     */
    @WebMethod(operationName = "UpdateBudgets", action = "UpdateBudgets")
    public Response<UpdateBudgetsResponse> updateBudgetsAsync(
        @WebParam(name = "UpdateBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBudgetsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateBudgets", action = "UpdateBudgets")
    public Future<?> updateBudgetsAsync(
        @WebParam(name = "UpdateBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBudgetsRequest parameters,
        @WebParam(name = "UpdateBudgetsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateBudgetsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateBudgetsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateBudgets", action = "UpdateBudgets")
    @WebResult(name = "UpdateBudgetsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateBudgetsResponse updateBudgets(
        @WebParam(name = "UpdateBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBudgetsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteBudgetsResponse>
     */
    @WebMethod(operationName = "DeleteBudgets", action = "DeleteBudgets")
    public Response<DeleteBudgetsResponse> deleteBudgetsAsync(
        @WebParam(name = "DeleteBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBudgetsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteBudgets", action = "DeleteBudgets")
    public Future<?> deleteBudgetsAsync(
        @WebParam(name = "DeleteBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBudgetsRequest parameters,
        @WebParam(name = "DeleteBudgetsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteBudgetsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteBudgetsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteBudgets", action = "DeleteBudgets")
    @WebResult(name = "DeleteBudgetsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteBudgetsResponse deleteBudgets(
        @WebParam(name = "DeleteBudgetsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBudgetsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBudgetsByIdsResponse>
     */
    @WebMethod(operationName = "GetBudgetsByIds", action = "GetBudgetsByIds")
    public Response<GetBudgetsByIdsResponse> getBudgetsByIdsAsync(
        @WebParam(name = "GetBudgetsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBudgetsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBudgetsByIds", action = "GetBudgetsByIds")
    public Future<?> getBudgetsByIdsAsync(
        @WebParam(name = "GetBudgetsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBudgetsByIdsRequest parameters,
        @WebParam(name = "GetBudgetsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBudgetsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBudgetsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBudgetsByIds", action = "GetBudgetsByIds")
    @WebResult(name = "GetBudgetsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBudgetsByIdsResponse getBudgetsByIds(
        @WebParam(name = "GetBudgetsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBudgetsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignIdsByBudgetIdsResponse>
     */
    @WebMethod(operationName = "GetCampaignIdsByBudgetIds", action = "GetCampaignIdsByBudgetIds")
    public Response<GetCampaignIdsByBudgetIdsResponse> getCampaignIdsByBudgetIdsAsync(
        @WebParam(name = "GetCampaignIdsByBudgetIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBudgetIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignIdsByBudgetIds", action = "GetCampaignIdsByBudgetIds")
    public Future<?> getCampaignIdsByBudgetIdsAsync(
        @WebParam(name = "GetCampaignIdsByBudgetIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBudgetIdsRequest parameters,
        @WebParam(name = "GetCampaignIdsByBudgetIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignIdsByBudgetIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignIdsByBudgetIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignIdsByBudgetIds", action = "GetCampaignIdsByBudgetIds")
    @WebResult(name = "GetCampaignIdsByBudgetIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignIdsByBudgetIdsResponse getCampaignIdsByBudgetIds(
        @WebParam(name = "GetCampaignIdsByBudgetIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBudgetIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddBidStrategiesResponse>
     */
    @WebMethod(operationName = "AddBidStrategies", action = "AddBidStrategies")
    public Response<AddBidStrategiesResponse> addBidStrategiesAsync(
        @WebParam(name = "AddBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBidStrategiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddBidStrategies", action = "AddBidStrategies")
    public Future<?> addBidStrategiesAsync(
        @WebParam(name = "AddBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBidStrategiesRequest parameters,
        @WebParam(name = "AddBidStrategiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddBidStrategiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddBidStrategiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddBidStrategies", action = "AddBidStrategies")
    @WebResult(name = "AddBidStrategiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddBidStrategiesResponse addBidStrategies(
        @WebParam(name = "AddBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBidStrategiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateBidStrategiesResponse>
     */
    @WebMethod(operationName = "UpdateBidStrategies", action = "UpdateBidStrategies")
    public Response<UpdateBidStrategiesResponse> updateBidStrategiesAsync(
        @WebParam(name = "UpdateBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBidStrategiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateBidStrategies", action = "UpdateBidStrategies")
    public Future<?> updateBidStrategiesAsync(
        @WebParam(name = "UpdateBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBidStrategiesRequest parameters,
        @WebParam(name = "UpdateBidStrategiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateBidStrategiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateBidStrategiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateBidStrategies", action = "UpdateBidStrategies")
    @WebResult(name = "UpdateBidStrategiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateBidStrategiesResponse updateBidStrategies(
        @WebParam(name = "UpdateBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBidStrategiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteBidStrategiesResponse>
     */
    @WebMethod(operationName = "DeleteBidStrategies", action = "DeleteBidStrategies")
    public Response<DeleteBidStrategiesResponse> deleteBidStrategiesAsync(
        @WebParam(name = "DeleteBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBidStrategiesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteBidStrategies", action = "DeleteBidStrategies")
    public Future<?> deleteBidStrategiesAsync(
        @WebParam(name = "DeleteBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBidStrategiesRequest parameters,
        @WebParam(name = "DeleteBidStrategiesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteBidStrategiesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteBidStrategiesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteBidStrategies", action = "DeleteBidStrategies")
    @WebResult(name = "DeleteBidStrategiesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteBidStrategiesResponse deleteBidStrategies(
        @WebParam(name = "DeleteBidStrategiesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBidStrategiesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBidStrategiesByIdsResponse>
     */
    @WebMethod(operationName = "GetBidStrategiesByIds", action = "GetBidStrategiesByIds")
    public Response<GetBidStrategiesByIdsResponse> getBidStrategiesByIdsAsync(
        @WebParam(name = "GetBidStrategiesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBidStrategiesByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBidStrategiesByIds", action = "GetBidStrategiesByIds")
    public Future<?> getBidStrategiesByIdsAsync(
        @WebParam(name = "GetBidStrategiesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBidStrategiesByIdsRequest parameters,
        @WebParam(name = "GetBidStrategiesByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBidStrategiesByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBidStrategiesByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBidStrategiesByIds", action = "GetBidStrategiesByIds")
    @WebResult(name = "GetBidStrategiesByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBidStrategiesByIdsResponse getBidStrategiesByIds(
        @WebParam(name = "GetBidStrategiesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBidStrategiesByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetCampaignIdsByBidStrategyIdsResponse>
     */
    @WebMethod(operationName = "GetCampaignIdsByBidStrategyIds", action = "GetCampaignIdsByBidStrategyIds")
    public Response<GetCampaignIdsByBidStrategyIdsResponse> getCampaignIdsByBidStrategyIdsAsync(
        @WebParam(name = "GetCampaignIdsByBidStrategyIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBidStrategyIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetCampaignIdsByBidStrategyIds", action = "GetCampaignIdsByBidStrategyIds")
    public Future<?> getCampaignIdsByBidStrategyIdsAsync(
        @WebParam(name = "GetCampaignIdsByBidStrategyIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBidStrategyIdsRequest parameters,
        @WebParam(name = "GetCampaignIdsByBidStrategyIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetCampaignIdsByBidStrategyIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetCampaignIdsByBidStrategyIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetCampaignIdsByBidStrategyIds", action = "GetCampaignIdsByBidStrategyIds")
    @WebResult(name = "GetCampaignIdsByBidStrategyIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetCampaignIdsByBidStrategyIdsResponse getCampaignIdsByBidStrategyIds(
        @WebParam(name = "GetCampaignIdsByBidStrategyIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetCampaignIdsByBidStrategyIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAudienceGroupsResponse>
     */
    @WebMethod(operationName = "AddAudienceGroups", action = "AddAudienceGroups")
    public Response<AddAudienceGroupsResponse> addAudienceGroupsAsync(
        @WebParam(name = "AddAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudienceGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAudienceGroups", action = "AddAudienceGroups")
    public Future<?> addAudienceGroupsAsync(
        @WebParam(name = "AddAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudienceGroupsRequest parameters,
        @WebParam(name = "AddAudienceGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAudienceGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAudienceGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAudienceGroups", action = "AddAudienceGroups")
    @WebResult(name = "AddAudienceGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAudienceGroupsResponse addAudienceGroups(
        @WebParam(name = "AddAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudienceGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAudienceGroupsResponse>
     */
    @WebMethod(operationName = "UpdateAudienceGroups", action = "UpdateAudienceGroups")
    public Response<UpdateAudienceGroupsResponse> updateAudienceGroupsAsync(
        @WebParam(name = "UpdateAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudienceGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAudienceGroups", action = "UpdateAudienceGroups")
    public Future<?> updateAudienceGroupsAsync(
        @WebParam(name = "UpdateAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudienceGroupsRequest parameters,
        @WebParam(name = "UpdateAudienceGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAudienceGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAudienceGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAudienceGroups", action = "UpdateAudienceGroups")
    @WebResult(name = "UpdateAudienceGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAudienceGroupsResponse updateAudienceGroups(
        @WebParam(name = "UpdateAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudienceGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAudienceGroupsResponse>
     */
    @WebMethod(operationName = "DeleteAudienceGroups", action = "DeleteAudienceGroups")
    public Response<DeleteAudienceGroupsResponse> deleteAudienceGroupsAsync(
        @WebParam(name = "DeleteAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAudienceGroups", action = "DeleteAudienceGroups")
    public Future<?> deleteAudienceGroupsAsync(
        @WebParam(name = "DeleteAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupsRequest parameters,
        @WebParam(name = "DeleteAudienceGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAudienceGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAudienceGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAudienceGroups", action = "DeleteAudienceGroups")
    @WebResult(name = "DeleteAudienceGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAudienceGroupsResponse deleteAudienceGroups(
        @WebParam(name = "DeleteAudienceGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupsByIdsResponse>
     */
    @WebMethod(operationName = "GetAudienceGroupsByIds", action = "GetAudienceGroupsByIds")
    public Response<GetAudienceGroupsByIdsResponse> getAudienceGroupsByIdsAsync(
        @WebParam(name = "GetAudienceGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAudienceGroupsByIds", action = "GetAudienceGroupsByIds")
    public Future<?> getAudienceGroupsByIdsAsync(
        @WebParam(name = "GetAudienceGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupsByIdsRequest parameters,
        @WebParam(name = "GetAudienceGroupsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAudienceGroupsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAudienceGroupsByIds", action = "GetAudienceGroupsByIds")
    @WebResult(name = "GetAudienceGroupsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAudienceGroupsByIdsResponse getAudienceGroupsByIds(
        @WebParam(name = "GetAudienceGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAssetGroupsResponse>
     */
    @WebMethod(operationName = "AddAssetGroups", action = "AddAssetGroups")
    public Response<AddAssetGroupsResponse> addAssetGroupsAsync(
        @WebParam(name = "AddAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAssetGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAssetGroups", action = "AddAssetGroups")
    public Future<?> addAssetGroupsAsync(
        @WebParam(name = "AddAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAssetGroupsRequest parameters,
        @WebParam(name = "AddAssetGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAssetGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAssetGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAssetGroups", action = "AddAssetGroups")
    @WebResult(name = "AddAssetGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAssetGroupsResponse addAssetGroups(
        @WebParam(name = "AddAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAssetGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAssetGroupsResponse>
     */
    @WebMethod(operationName = "UpdateAssetGroups", action = "UpdateAssetGroups")
    public Response<UpdateAssetGroupsResponse> updateAssetGroupsAsync(
        @WebParam(name = "UpdateAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAssetGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAssetGroups", action = "UpdateAssetGroups")
    public Future<?> updateAssetGroupsAsync(
        @WebParam(name = "UpdateAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAssetGroupsRequest parameters,
        @WebParam(name = "UpdateAssetGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAssetGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAssetGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAssetGroups", action = "UpdateAssetGroups")
    @WebResult(name = "UpdateAssetGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAssetGroupsResponse updateAssetGroups(
        @WebParam(name = "UpdateAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAssetGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAssetGroupsResponse>
     */
    @WebMethod(operationName = "DeleteAssetGroups", action = "DeleteAssetGroups")
    public Response<DeleteAssetGroupsResponse> deleteAssetGroupsAsync(
        @WebParam(name = "DeleteAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAssetGroupsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAssetGroups", action = "DeleteAssetGroups")
    public Future<?> deleteAssetGroupsAsync(
        @WebParam(name = "DeleteAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAssetGroupsRequest parameters,
        @WebParam(name = "DeleteAssetGroupsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAssetGroupsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAssetGroupsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAssetGroups", action = "DeleteAssetGroups")
    @WebResult(name = "DeleteAssetGroupsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAssetGroupsResponse deleteAssetGroups(
        @WebParam(name = "DeleteAssetGroupsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAssetGroupsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsByIdsResponse>
     */
    @WebMethod(operationName = "GetAssetGroupsByIds", action = "GetAssetGroupsByIds")
    public Response<GetAssetGroupsByIdsResponse> getAssetGroupsByIdsAsync(
        @WebParam(name = "GetAssetGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAssetGroupsByIds", action = "GetAssetGroupsByIds")
    public Future<?> getAssetGroupsByIdsAsync(
        @WebParam(name = "GetAssetGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByIdsRequest parameters,
        @WebParam(name = "GetAssetGroupsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAssetGroupsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAssetGroupsByIds", action = "GetAssetGroupsByIds")
    @WebResult(name = "GetAssetGroupsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAssetGroupsByIdsResponse getAssetGroupsByIds(
        @WebParam(name = "GetAssetGroupsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsByCampaignIdResponse>
     */
    @WebMethod(operationName = "GetAssetGroupsByCampaignId", action = "GetAssetGroupsByCampaignId")
    public Response<GetAssetGroupsByCampaignIdResponse> getAssetGroupsByCampaignIdAsync(
        @WebParam(name = "GetAssetGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByCampaignIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAssetGroupsByCampaignId", action = "GetAssetGroupsByCampaignId")
    public Future<?> getAssetGroupsByCampaignIdAsync(
        @WebParam(name = "GetAssetGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByCampaignIdRequest parameters,
        @WebParam(name = "GetAssetGroupsByCampaignIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAssetGroupsByCampaignIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsByCampaignIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAssetGroupsByCampaignId", action = "GetAssetGroupsByCampaignId")
    @WebResult(name = "GetAssetGroupsByCampaignIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAssetGroupsByCampaignIdResponse getAssetGroupsByCampaignId(
        @WebParam(name = "GetAssetGroupsByCampaignIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsByCampaignIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsEditorialReasonsResponse>
     */
    @WebMethod(operationName = "GetAssetGroupsEditorialReasons", action = "GetAssetGroupsEditorialReasons")
    public Response<GetAssetGroupsEditorialReasonsResponse> getAssetGroupsEditorialReasonsAsync(
        @WebParam(name = "GetAssetGroupsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsEditorialReasonsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAssetGroupsEditorialReasons", action = "GetAssetGroupsEditorialReasons")
    public Future<?> getAssetGroupsEditorialReasonsAsync(
        @WebParam(name = "GetAssetGroupsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsEditorialReasonsRequest parameters,
        @WebParam(name = "GetAssetGroupsEditorialReasonsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAssetGroupsEditorialReasonsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAssetGroupsEditorialReasonsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAssetGroupsEditorialReasons", action = "GetAssetGroupsEditorialReasons")
    @WebResult(name = "GetAssetGroupsEditorialReasonsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAssetGroupsEditorialReasonsResponse getAssetGroupsEditorialReasons(
        @WebParam(name = "GetAssetGroupsEditorialReasonsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAssetGroupsEditorialReasonsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetAudienceGroupAssetGroupAssociationsResponse>
     */
    @WebMethod(operationName = "SetAudienceGroupAssetGroupAssociations", action = "SetAudienceGroupAssetGroupAssociations")
    public Response<SetAudienceGroupAssetGroupAssociationsResponse> setAudienceGroupAssetGroupAssociationsAsync(
        @WebParam(name = "SetAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAudienceGroupAssetGroupAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetAudienceGroupAssetGroupAssociations", action = "SetAudienceGroupAssetGroupAssociations")
    public Future<?> setAudienceGroupAssetGroupAssociationsAsync(
        @WebParam(name = "SetAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAudienceGroupAssetGroupAssociationsRequest parameters,
        @WebParam(name = "SetAudienceGroupAssetGroupAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetAudienceGroupAssetGroupAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetAudienceGroupAssetGroupAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetAudienceGroupAssetGroupAssociations", action = "SetAudienceGroupAssetGroupAssociations")
    @WebResult(name = "SetAudienceGroupAssetGroupAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetAudienceGroupAssetGroupAssociationsResponse setAudienceGroupAssetGroupAssociations(
        @WebParam(name = "SetAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetAudienceGroupAssetGroupAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAudienceGroupAssetGroupAssociationsResponse>
     */
    @WebMethod(operationName = "DeleteAudienceGroupAssetGroupAssociations", action = "DeleteAudienceGroupAssetGroupAssociations")
    public Response<DeleteAudienceGroupAssetGroupAssociationsResponse> deleteAudienceGroupAssetGroupAssociationsAsync(
        @WebParam(name = "DeleteAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupAssetGroupAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAudienceGroupAssetGroupAssociations", action = "DeleteAudienceGroupAssetGroupAssociations")
    public Future<?> deleteAudienceGroupAssetGroupAssociationsAsync(
        @WebParam(name = "DeleteAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupAssetGroupAssociationsRequest parameters,
        @WebParam(name = "DeleteAudienceGroupAssetGroupAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAudienceGroupAssetGroupAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAudienceGroupAssetGroupAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAudienceGroupAssetGroupAssociations", action = "DeleteAudienceGroupAssetGroupAssociations")
    @WebResult(name = "DeleteAudienceGroupAssetGroupAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAudienceGroupAssetGroupAssociationsResponse deleteAudienceGroupAssetGroupAssociations(
        @WebParam(name = "DeleteAudienceGroupAssetGroupAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudienceGroupAssetGroupAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse>
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds")
    public Response<GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse> getAudienceGroupAssetGroupAssociationsByAssetGroupIdsAsync(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds")
    public Future<?> getAudienceGroupAssetGroupAssociationsByAssetGroupIdsAsync(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest parameters,
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIds")
    @WebResult(name = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse getAudienceGroupAssetGroupAssociationsByAssetGroupIds(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse>
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds")
    public Response<GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse> getAudienceGroupAssetGroupAssociationsByAudienceGroupIdsAsync(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds")
    public Future<?> getAudienceGroupAssetGroupAssociationsByAudienceGroupIdsAsync(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest parameters,
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds", action = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds")
    @WebResult(name = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse getAudienceGroupAssetGroupAssociationsByAudienceGroupIds(
        @WebParam(name = "GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddAudiencesResponse>
     */
    @WebMethod(operationName = "AddAudiences", action = "AddAudiences")
    public Response<AddAudiencesResponse> addAudiencesAsync(
        @WebParam(name = "AddAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudiencesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddAudiences", action = "AddAudiences")
    public Future<?> addAudiencesAsync(
        @WebParam(name = "AddAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudiencesRequest parameters,
        @WebParam(name = "AddAudiencesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddAudiencesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddAudiencesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddAudiences", action = "AddAudiences")
    @WebResult(name = "AddAudiencesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddAudiencesResponse addAudiences(
        @WebParam(name = "AddAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddAudiencesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAudiencesResponse>
     */
    @WebMethod(operationName = "UpdateAudiences", action = "UpdateAudiences")
    public Response<UpdateAudiencesResponse> updateAudiencesAsync(
        @WebParam(name = "UpdateAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudiencesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAudiences", action = "UpdateAudiences")
    public Future<?> updateAudiencesAsync(
        @WebParam(name = "UpdateAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudiencesRequest parameters,
        @WebParam(name = "UpdateAudiencesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAudiencesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAudiencesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAudiences", action = "UpdateAudiences")
    @WebResult(name = "UpdateAudiencesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAudiencesResponse updateAudiences(
        @WebParam(name = "UpdateAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAudiencesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteAudiencesResponse>
     */
    @WebMethod(operationName = "DeleteAudiences", action = "DeleteAudiences")
    public Response<DeleteAudiencesResponse> deleteAudiencesAsync(
        @WebParam(name = "DeleteAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudiencesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteAudiences", action = "DeleteAudiences")
    public Future<?> deleteAudiencesAsync(
        @WebParam(name = "DeleteAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudiencesRequest parameters,
        @WebParam(name = "DeleteAudiencesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteAudiencesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteAudiencesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteAudiences", action = "DeleteAudiences")
    @WebResult(name = "DeleteAudiencesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteAudiencesResponse deleteAudiences(
        @WebParam(name = "DeleteAudiencesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteAudiencesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAudiencesByIdsResponse>
     */
    @WebMethod(operationName = "GetAudiencesByIds", action = "GetAudiencesByIds")
    public Response<GetAudiencesByIdsResponse> getAudiencesByIdsAsync(
        @WebParam(name = "GetAudiencesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudiencesByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAudiencesByIds", action = "GetAudiencesByIds")
    public Future<?> getAudiencesByIdsAsync(
        @WebParam(name = "GetAudiencesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudiencesByIdsRequest parameters,
        @WebParam(name = "GetAudiencesByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAudiencesByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAudiencesByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAudiencesByIds", action = "GetAudiencesByIds")
    @WebResult(name = "GetAudiencesByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAudiencesByIdsResponse getAudiencesByIds(
        @WebParam(name = "GetAudiencesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAudiencesByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyCustomerListItemsResponse>
     */
    @WebMethod(operationName = "ApplyCustomerListItems", action = "ApplyCustomerListItems")
    public Response<ApplyCustomerListItemsResponse> applyCustomerListItemsAsync(
        @WebParam(name = "ApplyCustomerListItemsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListItemsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyCustomerListItems", action = "ApplyCustomerListItems")
    public Future<?> applyCustomerListItemsAsync(
        @WebParam(name = "ApplyCustomerListItemsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListItemsRequest parameters,
        @WebParam(name = "ApplyCustomerListItemsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyCustomerListItemsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyCustomerListItemsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyCustomerListItems", action = "ApplyCustomerListItems")
    @WebResult(name = "ApplyCustomerListItemsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyCustomerListItemsResponse applyCustomerListItems(
        @WebParam(name = "ApplyCustomerListItemsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListItemsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyCustomerListUserDataResponse>
     */
    @WebMethod(operationName = "ApplyCustomerListUserData", action = "ApplyCustomerListUserData")
    public Response<ApplyCustomerListUserDataResponse> applyCustomerListUserDataAsync(
        @WebParam(name = "ApplyCustomerListUserDataRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListUserDataRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyCustomerListUserData", action = "ApplyCustomerListUserData")
    public Future<?> applyCustomerListUserDataAsync(
        @WebParam(name = "ApplyCustomerListUserDataRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListUserDataRequest parameters,
        @WebParam(name = "ApplyCustomerListUserDataResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyCustomerListUserDataResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyCustomerListUserDataResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyCustomerListUserData", action = "ApplyCustomerListUserData")
    @WebResult(name = "ApplyCustomerListUserDataResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyCustomerListUserDataResponse applyCustomerListUserData(
        @WebParam(name = "ApplyCustomerListUserDataRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyCustomerListUserDataRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetUetTagsByIdsResponse>
     */
    @WebMethod(operationName = "GetUetTagsByIds", action = "GetUetTagsByIds")
    public Response<GetUetTagsByIdsResponse> getUetTagsByIdsAsync(
        @WebParam(name = "GetUetTagsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetUetTagsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetUetTagsByIds", action = "GetUetTagsByIds")
    public Future<?> getUetTagsByIdsAsync(
        @WebParam(name = "GetUetTagsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetUetTagsByIdsRequest parameters,
        @WebParam(name = "GetUetTagsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetUetTagsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetUetTagsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetUetTagsByIds", action = "GetUetTagsByIds")
    @WebResult(name = "GetUetTagsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetUetTagsByIdsResponse getUetTagsByIds(
        @WebParam(name = "GetUetTagsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetUetTagsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddUetTagsResponse>
     */
    @WebMethod(operationName = "AddUetTags", action = "AddUetTags")
    public Response<AddUetTagsResponse> addUetTagsAsync(
        @WebParam(name = "AddUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddUetTagsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddUetTags", action = "AddUetTags")
    public Future<?> addUetTagsAsync(
        @WebParam(name = "AddUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddUetTagsRequest parameters,
        @WebParam(name = "AddUetTagsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddUetTagsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddUetTagsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddUetTags", action = "AddUetTags")
    @WebResult(name = "AddUetTagsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddUetTagsResponse addUetTags(
        @WebParam(name = "AddUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddUetTagsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateUetTagsResponse>
     */
    @WebMethod(operationName = "UpdateUetTags", action = "UpdateUetTags")
    public Response<UpdateUetTagsResponse> updateUetTagsAsync(
        @WebParam(name = "UpdateUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateUetTagsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateUetTags", action = "UpdateUetTags")
    public Future<?> updateUetTagsAsync(
        @WebParam(name = "UpdateUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateUetTagsRequest parameters,
        @WebParam(name = "UpdateUetTagsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateUetTagsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateUetTagsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateUetTags", action = "UpdateUetTags")
    @WebResult(name = "UpdateUetTagsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateUetTagsResponse updateUetTags(
        @WebParam(name = "UpdateUetTagsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateUetTagsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetConversionGoalsByIdsResponse>
     */
    @WebMethod(operationName = "GetConversionGoalsByIds", action = "GetConversionGoalsByIds")
    public Response<GetConversionGoalsByIdsResponse> getConversionGoalsByIdsAsync(
        @WebParam(name = "GetConversionGoalsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetConversionGoalsByIds", action = "GetConversionGoalsByIds")
    public Future<?> getConversionGoalsByIdsAsync(
        @WebParam(name = "GetConversionGoalsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByIdsRequest parameters,
        @WebParam(name = "GetConversionGoalsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetConversionGoalsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetConversionGoalsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetConversionGoalsByIds", action = "GetConversionGoalsByIds")
    @WebResult(name = "GetConversionGoalsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetConversionGoalsByIdsResponse getConversionGoalsByIds(
        @WebParam(name = "GetConversionGoalsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetConversionGoalsByTagIdsResponse>
     */
    @WebMethod(operationName = "GetConversionGoalsByTagIds", action = "GetConversionGoalsByTagIds")
    public Response<GetConversionGoalsByTagIdsResponse> getConversionGoalsByTagIdsAsync(
        @WebParam(name = "GetConversionGoalsByTagIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByTagIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetConversionGoalsByTagIds", action = "GetConversionGoalsByTagIds")
    public Future<?> getConversionGoalsByTagIdsAsync(
        @WebParam(name = "GetConversionGoalsByTagIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByTagIdsRequest parameters,
        @WebParam(name = "GetConversionGoalsByTagIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetConversionGoalsByTagIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetConversionGoalsByTagIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetConversionGoalsByTagIds", action = "GetConversionGoalsByTagIds")
    @WebResult(name = "GetConversionGoalsByTagIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetConversionGoalsByTagIdsResponse getConversionGoalsByTagIds(
        @WebParam(name = "GetConversionGoalsByTagIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionGoalsByTagIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddConversionGoalsResponse>
     */
    @WebMethod(operationName = "AddConversionGoals", action = "AddConversionGoals")
    public Response<AddConversionGoalsResponse> addConversionGoalsAsync(
        @WebParam(name = "AddConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddConversionGoals", action = "AddConversionGoals")
    public Future<?> addConversionGoalsAsync(
        @WebParam(name = "AddConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionGoalsRequest parameters,
        @WebParam(name = "AddConversionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddConversionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddConversionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddConversionGoals", action = "AddConversionGoals")
    @WebResult(name = "AddConversionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddConversionGoalsResponse addConversionGoals(
        @WebParam(name = "AddConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateConversionGoalsResponse>
     */
    @WebMethod(operationName = "UpdateConversionGoals", action = "UpdateConversionGoals")
    public Response<UpdateConversionGoalsResponse> updateConversionGoalsAsync(
        @WebParam(name = "UpdateConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateConversionGoals", action = "UpdateConversionGoals")
    public Future<?> updateConversionGoalsAsync(
        @WebParam(name = "UpdateConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionGoalsRequest parameters,
        @WebParam(name = "UpdateConversionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateConversionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateConversionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateConversionGoals", action = "UpdateConversionGoals")
    @WebResult(name = "UpdateConversionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateConversionGoalsResponse updateConversionGoals(
        @WebParam(name = "UpdateConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyOfflineConversionsResponse>
     */
    @WebMethod(operationName = "ApplyOfflineConversions", action = "ApplyOfflineConversions")
    public Response<ApplyOfflineConversionsResponse> applyOfflineConversionsAsync(
        @WebParam(name = "ApplyOfflineConversionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyOfflineConversions", action = "ApplyOfflineConversions")
    public Future<?> applyOfflineConversionsAsync(
        @WebParam(name = "ApplyOfflineConversionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionsRequest parameters,
        @WebParam(name = "ApplyOfflineConversionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyOfflineConversionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyOfflineConversionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyOfflineConversions", action = "ApplyOfflineConversions")
    @WebResult(name = "ApplyOfflineConversionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyOfflineConversionsResponse applyOfflineConversions(
        @WebParam(name = "ApplyOfflineConversionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyOfflineConversionAdjustmentsResponse>
     */
    @WebMethod(operationName = "ApplyOfflineConversionAdjustments", action = "ApplyOfflineConversionAdjustments")
    public Response<ApplyOfflineConversionAdjustmentsResponse> applyOfflineConversionAdjustmentsAsync(
        @WebParam(name = "ApplyOfflineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionAdjustmentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyOfflineConversionAdjustments", action = "ApplyOfflineConversionAdjustments")
    public Future<?> applyOfflineConversionAdjustmentsAsync(
        @WebParam(name = "ApplyOfflineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionAdjustmentsRequest parameters,
        @WebParam(name = "ApplyOfflineConversionAdjustmentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyOfflineConversionAdjustmentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyOfflineConversionAdjustmentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyOfflineConversionAdjustments", action = "ApplyOfflineConversionAdjustments")
    @WebResult(name = "ApplyOfflineConversionAdjustmentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyOfflineConversionAdjustmentsResponse applyOfflineConversionAdjustments(
        @WebParam(name = "ApplyOfflineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOfflineConversionAdjustmentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.ApplyOnlineConversionAdjustmentsResponse>
     */
    @WebMethod(operationName = "ApplyOnlineConversionAdjustments", action = "ApplyOnlineConversionAdjustments")
    public Response<ApplyOnlineConversionAdjustmentsResponse> applyOnlineConversionAdjustmentsAsync(
        @WebParam(name = "ApplyOnlineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOnlineConversionAdjustmentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "ApplyOnlineConversionAdjustments", action = "ApplyOnlineConversionAdjustments")
    public Future<?> applyOnlineConversionAdjustmentsAsync(
        @WebParam(name = "ApplyOnlineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOnlineConversionAdjustmentsRequest parameters,
        @WebParam(name = "ApplyOnlineConversionAdjustmentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<ApplyOnlineConversionAdjustmentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.ApplyOnlineConversionAdjustmentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "ApplyOnlineConversionAdjustments", action = "ApplyOnlineConversionAdjustments")
    @WebResult(name = "ApplyOnlineConversionAdjustmentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public ApplyOnlineConversionAdjustmentsResponse applyOnlineConversionAdjustments(
        @WebParam(name = "ApplyOnlineConversionAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        ApplyOnlineConversionAdjustmentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetOfflineConversionReportsResponse>
     */
    @WebMethod(operationName = "GetOfflineConversionReports", action = "GetOfflineConversionReports")
    public Response<GetOfflineConversionReportsResponse> getOfflineConversionReportsAsync(
        @WebParam(name = "GetOfflineConversionReportsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetOfflineConversionReportsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetOfflineConversionReports", action = "GetOfflineConversionReports")
    public Future<?> getOfflineConversionReportsAsync(
        @WebParam(name = "GetOfflineConversionReportsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetOfflineConversionReportsRequest parameters,
        @WebParam(name = "GetOfflineConversionReportsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetOfflineConversionReportsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetOfflineConversionReportsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetOfflineConversionReports", action = "GetOfflineConversionReports")
    @WebResult(name = "GetOfflineConversionReportsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetOfflineConversionReportsResponse getOfflineConversionReports(
        @WebParam(name = "GetOfflineConversionReportsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetOfflineConversionReportsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddLabelsResponse>
     */
    @WebMethod(operationName = "AddLabels", action = "AddLabels")
    public Response<AddLabelsResponse> addLabelsAsync(
        @WebParam(name = "AddLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddLabelsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddLabels", action = "AddLabels")
    public Future<?> addLabelsAsync(
        @WebParam(name = "AddLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddLabelsRequest parameters,
        @WebParam(name = "AddLabelsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddLabelsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddLabelsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddLabels", action = "AddLabels")
    @WebResult(name = "AddLabelsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddLabelsResponse addLabels(
        @WebParam(name = "AddLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddLabelsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteLabelsResponse>
     */
    @WebMethod(operationName = "DeleteLabels", action = "DeleteLabels")
    public Response<DeleteLabelsResponse> deleteLabelsAsync(
        @WebParam(name = "DeleteLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteLabels", action = "DeleteLabels")
    public Future<?> deleteLabelsAsync(
        @WebParam(name = "DeleteLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelsRequest parameters,
        @WebParam(name = "DeleteLabelsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteLabelsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteLabelsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteLabels", action = "DeleteLabels")
    @WebResult(name = "DeleteLabelsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteLabelsResponse deleteLabels(
        @WebParam(name = "DeleteLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateLabelsResponse>
     */
    @WebMethod(operationName = "UpdateLabels", action = "UpdateLabels")
    public Response<UpdateLabelsResponse> updateLabelsAsync(
        @WebParam(name = "UpdateLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateLabelsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateLabels", action = "UpdateLabels")
    public Future<?> updateLabelsAsync(
        @WebParam(name = "UpdateLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateLabelsRequest parameters,
        @WebParam(name = "UpdateLabelsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateLabelsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateLabelsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateLabels", action = "UpdateLabels")
    @WebResult(name = "UpdateLabelsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateLabelsResponse updateLabels(
        @WebParam(name = "UpdateLabelsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateLabelsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetLabelsByIdsResponse>
     */
    @WebMethod(operationName = "GetLabelsByIds", action = "GetLabelsByIds")
    public Response<GetLabelsByIdsResponse> getLabelsByIdsAsync(
        @WebParam(name = "GetLabelsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetLabelsByIds", action = "GetLabelsByIds")
    public Future<?> getLabelsByIdsAsync(
        @WebParam(name = "GetLabelsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelsByIdsRequest parameters,
        @WebParam(name = "GetLabelsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetLabelsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetLabelsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetLabelsByIds", action = "GetLabelsByIds")
    @WebResult(name = "GetLabelsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetLabelsByIdsResponse getLabelsByIds(
        @WebParam(name = "GetLabelsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SetLabelAssociationsResponse>
     */
    @WebMethod(operationName = "SetLabelAssociations", action = "SetLabelAssociations")
    public Response<SetLabelAssociationsResponse> setLabelAssociationsAsync(
        @WebParam(name = "SetLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetLabelAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SetLabelAssociations", action = "SetLabelAssociations")
    public Future<?> setLabelAssociationsAsync(
        @WebParam(name = "SetLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetLabelAssociationsRequest parameters,
        @WebParam(name = "SetLabelAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SetLabelAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SetLabelAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SetLabelAssociations", action = "SetLabelAssociations")
    @WebResult(name = "SetLabelAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SetLabelAssociationsResponse setLabelAssociations(
        @WebParam(name = "SetLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SetLabelAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteLabelAssociationsResponse>
     */
    @WebMethod(operationName = "DeleteLabelAssociations", action = "DeleteLabelAssociations")
    public Response<DeleteLabelAssociationsResponse> deleteLabelAssociationsAsync(
        @WebParam(name = "DeleteLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelAssociationsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteLabelAssociations", action = "DeleteLabelAssociations")
    public Future<?> deleteLabelAssociationsAsync(
        @WebParam(name = "DeleteLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelAssociationsRequest parameters,
        @WebParam(name = "DeleteLabelAssociationsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteLabelAssociationsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteLabelAssociationsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteLabelAssociations", action = "DeleteLabelAssociations")
    @WebResult(name = "DeleteLabelAssociationsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteLabelAssociationsResponse deleteLabelAssociations(
        @WebParam(name = "DeleteLabelAssociationsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteLabelAssociationsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetLabelAssociationsByEntityIdsResponse>
     */
    @WebMethod(operationName = "GetLabelAssociationsByEntityIds", action = "GetLabelAssociationsByEntityIds")
    public Response<GetLabelAssociationsByEntityIdsResponse> getLabelAssociationsByEntityIdsAsync(
        @WebParam(name = "GetLabelAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByEntityIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetLabelAssociationsByEntityIds", action = "GetLabelAssociationsByEntityIds")
    public Future<?> getLabelAssociationsByEntityIdsAsync(
        @WebParam(name = "GetLabelAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByEntityIdsRequest parameters,
        @WebParam(name = "GetLabelAssociationsByEntityIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetLabelAssociationsByEntityIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetLabelAssociationsByEntityIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetLabelAssociationsByEntityIds", action = "GetLabelAssociationsByEntityIds")
    @WebResult(name = "GetLabelAssociationsByEntityIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetLabelAssociationsByEntityIdsResponse getLabelAssociationsByEntityIds(
        @WebParam(name = "GetLabelAssociationsByEntityIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByEntityIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetLabelAssociationsByLabelIdsResponse>
     */
    @WebMethod(operationName = "GetLabelAssociationsByLabelIds", action = "GetLabelAssociationsByLabelIds")
    public Response<GetLabelAssociationsByLabelIdsResponse> getLabelAssociationsByLabelIdsAsync(
        @WebParam(name = "GetLabelAssociationsByLabelIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByLabelIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetLabelAssociationsByLabelIds", action = "GetLabelAssociationsByLabelIds")
    public Future<?> getLabelAssociationsByLabelIdsAsync(
        @WebParam(name = "GetLabelAssociationsByLabelIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByLabelIdsRequest parameters,
        @WebParam(name = "GetLabelAssociationsByLabelIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetLabelAssociationsByLabelIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetLabelAssociationsByLabelIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetLabelAssociationsByLabelIds", action = "GetLabelAssociationsByLabelIds")
    @WebResult(name = "GetLabelAssociationsByLabelIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetLabelAssociationsByLabelIdsResponse getLabelAssociationsByLabelIds(
        @WebParam(name = "GetLabelAssociationsByLabelIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetLabelAssociationsByLabelIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddExperimentsResponse>
     */
    @WebMethod(operationName = "AddExperiments", action = "AddExperiments")
    public Response<AddExperimentsResponse> addExperimentsAsync(
        @WebParam(name = "AddExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddExperimentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddExperiments", action = "AddExperiments")
    public Future<?> addExperimentsAsync(
        @WebParam(name = "AddExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddExperimentsRequest parameters,
        @WebParam(name = "AddExperimentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddExperimentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddExperimentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddExperiments", action = "AddExperiments")
    @WebResult(name = "AddExperimentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddExperimentsResponse addExperiments(
        @WebParam(name = "AddExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddExperimentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteExperimentsResponse>
     */
    @WebMethod(operationName = "DeleteExperiments", action = "DeleteExperiments")
    public Response<DeleteExperimentsResponse> deleteExperimentsAsync(
        @WebParam(name = "DeleteExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteExperimentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteExperiments", action = "DeleteExperiments")
    public Future<?> deleteExperimentsAsync(
        @WebParam(name = "DeleteExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteExperimentsRequest parameters,
        @WebParam(name = "DeleteExperimentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteExperimentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteExperimentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteExperiments", action = "DeleteExperiments")
    @WebResult(name = "DeleteExperimentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteExperimentsResponse deleteExperiments(
        @WebParam(name = "DeleteExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteExperimentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateExperimentsResponse>
     */
    @WebMethod(operationName = "UpdateExperiments", action = "UpdateExperiments")
    public Response<UpdateExperimentsResponse> updateExperimentsAsync(
        @WebParam(name = "UpdateExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateExperimentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateExperiments", action = "UpdateExperiments")
    public Future<?> updateExperimentsAsync(
        @WebParam(name = "UpdateExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateExperimentsRequest parameters,
        @WebParam(name = "UpdateExperimentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateExperimentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateExperimentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateExperiments", action = "UpdateExperiments")
    @WebResult(name = "UpdateExperimentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateExperimentsResponse updateExperiments(
        @WebParam(name = "UpdateExperimentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateExperimentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetExperimentsByIdsResponse>
     */
    @WebMethod(operationName = "GetExperimentsByIds", action = "GetExperimentsByIds")
    public Response<GetExperimentsByIdsResponse> getExperimentsByIdsAsync(
        @WebParam(name = "GetExperimentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetExperimentsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetExperimentsByIds", action = "GetExperimentsByIds")
    public Future<?> getExperimentsByIdsAsync(
        @WebParam(name = "GetExperimentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetExperimentsByIdsRequest parameters,
        @WebParam(name = "GetExperimentsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetExperimentsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetExperimentsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetExperimentsByIds", action = "GetExperimentsByIds")
    @WebResult(name = "GetExperimentsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetExperimentsByIdsResponse getExperimentsByIds(
        @WebParam(name = "GetExperimentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetExperimentsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetProfileDataFileUrlResponse>
     */
    @WebMethod(operationName = "GetProfileDataFileUrl", action = "GetProfileDataFileUrl")
    public Response<GetProfileDataFileUrlResponse> getProfileDataFileUrlAsync(
        @WebParam(name = "GetProfileDataFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetProfileDataFileUrlRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetProfileDataFileUrl", action = "GetProfileDataFileUrl")
    public Future<?> getProfileDataFileUrlAsync(
        @WebParam(name = "GetProfileDataFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetProfileDataFileUrlRequest parameters,
        @WebParam(name = "GetProfileDataFileUrlResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetProfileDataFileUrlResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetProfileDataFileUrlResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetProfileDataFileUrl", action = "GetProfileDataFileUrl")
    @WebResult(name = "GetProfileDataFileUrlResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetProfileDataFileUrlResponse getProfileDataFileUrl(
        @WebParam(name = "GetProfileDataFileUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetProfileDataFileUrlRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.SearchCompaniesResponse>
     */
    @WebMethod(operationName = "SearchCompanies", action = "SearchCompanies")
    public Response<SearchCompaniesResponse> searchCompaniesAsync(
        @WebParam(name = "SearchCompaniesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SearchCompaniesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "SearchCompanies", action = "SearchCompanies")
    public Future<?> searchCompaniesAsync(
        @WebParam(name = "SearchCompaniesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SearchCompaniesRequest parameters,
        @WebParam(name = "SearchCompaniesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<SearchCompaniesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.SearchCompaniesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "SearchCompanies", action = "SearchCompanies")
    @WebResult(name = "SearchCompaniesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public SearchCompaniesResponse searchCompanies(
        @WebParam(name = "SearchCompaniesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        SearchCompaniesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetFileImportUploadUrlResponse>
     */
    @WebMethod(operationName = "GetFileImportUploadUrl", action = "GetFileImportUploadUrl")
    public Response<GetFileImportUploadUrlResponse> getFileImportUploadUrlAsync(
        @WebParam(name = "GetFileImportUploadUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetFileImportUploadUrlRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetFileImportUploadUrl", action = "GetFileImportUploadUrl")
    public Future<?> getFileImportUploadUrlAsync(
        @WebParam(name = "GetFileImportUploadUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetFileImportUploadUrlRequest parameters,
        @WebParam(name = "GetFileImportUploadUrlResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetFileImportUploadUrlResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetFileImportUploadUrlResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetFileImportUploadUrl", action = "GetFileImportUploadUrl")
    @WebResult(name = "GetFileImportUploadUrlResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetFileImportUploadUrlResponse getFileImportUploadUrl(
        @WebParam(name = "GetFileImportUploadUrlRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetFileImportUploadUrlRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddImportJobsResponse>
     */
    @WebMethod(operationName = "AddImportJobs", action = "AddImportJobs")
    public Response<AddImportJobsResponse> addImportJobsAsync(
        @WebParam(name = "AddImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddImportJobsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddImportJobs", action = "AddImportJobs")
    public Future<?> addImportJobsAsync(
        @WebParam(name = "AddImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddImportJobsRequest parameters,
        @WebParam(name = "AddImportJobsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddImportJobsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddImportJobsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddImportJobs", action = "AddImportJobs")
    @WebResult(name = "AddImportJobsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddImportJobsResponse addImportJobs(
        @WebParam(name = "AddImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddImportJobsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetImportResultsResponse>
     */
    @WebMethod(operationName = "GetImportResults", action = "GetImportResults")
    public Response<GetImportResultsResponse> getImportResultsAsync(
        @WebParam(name = "GetImportResultsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportResultsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetImportResults", action = "GetImportResults")
    public Future<?> getImportResultsAsync(
        @WebParam(name = "GetImportResultsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportResultsRequest parameters,
        @WebParam(name = "GetImportResultsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetImportResultsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetImportResultsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetImportResults", action = "GetImportResults")
    @WebResult(name = "GetImportResultsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetImportResultsResponse getImportResults(
        @WebParam(name = "GetImportResultsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportResultsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetImportJobsByIdsResponse>
     */
    @WebMethod(operationName = "GetImportJobsByIds", action = "GetImportJobsByIds")
    public Response<GetImportJobsByIdsResponse> getImportJobsByIdsAsync(
        @WebParam(name = "GetImportJobsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportJobsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetImportJobsByIds", action = "GetImportJobsByIds")
    public Future<?> getImportJobsByIdsAsync(
        @WebParam(name = "GetImportJobsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportJobsByIdsRequest parameters,
        @WebParam(name = "GetImportJobsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetImportJobsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetImportJobsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetImportJobsByIds", action = "GetImportJobsByIds")
    @WebResult(name = "GetImportJobsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetImportJobsByIdsResponse getImportJobsByIds(
        @WebParam(name = "GetImportJobsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportJobsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteImportJobsResponse>
     */
    @WebMethod(operationName = "DeleteImportJobs", action = "DeleteImportJobs")
    public Response<DeleteImportJobsResponse> deleteImportJobsAsync(
        @WebParam(name = "DeleteImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteImportJobsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteImportJobs", action = "DeleteImportJobs")
    public Future<?> deleteImportJobsAsync(
        @WebParam(name = "DeleteImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteImportJobsRequest parameters,
        @WebParam(name = "DeleteImportJobsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteImportJobsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteImportJobsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteImportJobs", action = "DeleteImportJobs")
    @WebResult(name = "DeleteImportJobsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteImportJobsResponse deleteImportJobs(
        @WebParam(name = "DeleteImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteImportJobsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetImportEntityIdsMappingResponse>
     */
    @WebMethod(operationName = "GetImportEntityIdsMapping", action = "GetImportEntityIdsMapping")
    public Response<GetImportEntityIdsMappingResponse> getImportEntityIdsMappingAsync(
        @WebParam(name = "GetImportEntityIdsMappingRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportEntityIdsMappingRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetImportEntityIdsMapping", action = "GetImportEntityIdsMapping")
    public Future<?> getImportEntityIdsMappingAsync(
        @WebParam(name = "GetImportEntityIdsMappingRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportEntityIdsMappingRequest parameters,
        @WebParam(name = "GetImportEntityIdsMappingResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetImportEntityIdsMappingResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetImportEntityIdsMappingResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetImportEntityIdsMapping", action = "GetImportEntityIdsMapping")
    @WebResult(name = "GetImportEntityIdsMappingResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetImportEntityIdsMappingResponse getImportEntityIdsMapping(
        @WebParam(name = "GetImportEntityIdsMappingRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetImportEntityIdsMappingRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateImportJobsResponse>
     */
    @WebMethod(operationName = "UpdateImportJobs", action = "UpdateImportJobs")
    public Response<UpdateImportJobsResponse> updateImportJobsAsync(
        @WebParam(name = "UpdateImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateImportJobsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateImportJobs", action = "UpdateImportJobs")
    public Future<?> updateImportJobsAsync(
        @WebParam(name = "UpdateImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateImportJobsRequest parameters,
        @WebParam(name = "UpdateImportJobsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateImportJobsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateImportJobsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateImportJobs", action = "UpdateImportJobs")
    @WebResult(name = "UpdateImportJobsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateImportJobsResponse updateImportJobs(
        @WebParam(name = "UpdateImportJobsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateImportJobsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddVideosResponse>
     */
    @WebMethod(operationName = "AddVideos", action = "AddVideos")
    public Response<AddVideosResponse> addVideosAsync(
        @WebParam(name = "AddVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddVideosRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddVideos", action = "AddVideos")
    public Future<?> addVideosAsync(
        @WebParam(name = "AddVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddVideosRequest parameters,
        @WebParam(name = "AddVideosResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddVideosResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddVideosResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddVideos", action = "AddVideos")
    @WebResult(name = "AddVideosResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddVideosResponse addVideos(
        @WebParam(name = "AddVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddVideosRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteVideosResponse>
     */
    @WebMethod(operationName = "DeleteVideos", action = "DeleteVideos")
    public Response<DeleteVideosResponse> deleteVideosAsync(
        @WebParam(name = "DeleteVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteVideosRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteVideos", action = "DeleteVideos")
    public Future<?> deleteVideosAsync(
        @WebParam(name = "DeleteVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteVideosRequest parameters,
        @WebParam(name = "DeleteVideosResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteVideosResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteVideosResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteVideos", action = "DeleteVideos")
    @WebResult(name = "DeleteVideosResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteVideosResponse deleteVideos(
        @WebParam(name = "DeleteVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteVideosRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetVideosByIdsResponse>
     */
    @WebMethod(operationName = "GetVideosByIds", action = "GetVideosByIds")
    public Response<GetVideosByIdsResponse> getVideosByIdsAsync(
        @WebParam(name = "GetVideosByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetVideosByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetVideosByIds", action = "GetVideosByIds")
    public Future<?> getVideosByIdsAsync(
        @WebParam(name = "GetVideosByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetVideosByIdsRequest parameters,
        @WebParam(name = "GetVideosByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetVideosByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetVideosByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetVideosByIds", action = "GetVideosByIds")
    @WebResult(name = "GetVideosByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetVideosByIdsResponse getVideosByIds(
        @WebParam(name = "GetVideosByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetVideosByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateVideosResponse>
     */
    @WebMethod(operationName = "UpdateVideos", action = "UpdateVideos")
    public Response<UpdateVideosResponse> updateVideosAsync(
        @WebParam(name = "UpdateVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateVideosRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateVideos", action = "UpdateVideos")
    public Future<?> updateVideosAsync(
        @WebParam(name = "UpdateVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateVideosRequest parameters,
        @WebParam(name = "UpdateVideosResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateVideosResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateVideosResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateVideos", action = "UpdateVideos")
    @WebResult(name = "UpdateVideosResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateVideosResponse updateVideos(
        @WebParam(name = "UpdateVideosRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateVideosRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddCampaignConversionGoalsResponse>
     */
    @WebMethod(operationName = "AddCampaignConversionGoals", action = "AddCampaignConversionGoals")
    public Response<AddCampaignConversionGoalsResponse> addCampaignConversionGoalsAsync(
        @WebParam(name = "AddCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignConversionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddCampaignConversionGoals", action = "AddCampaignConversionGoals")
    public Future<?> addCampaignConversionGoalsAsync(
        @WebParam(name = "AddCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignConversionGoalsRequest parameters,
        @WebParam(name = "AddCampaignConversionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddCampaignConversionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddCampaignConversionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddCampaignConversionGoals", action = "AddCampaignConversionGoals")
    @WebResult(name = "AddCampaignConversionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddCampaignConversionGoalsResponse addCampaignConversionGoals(
        @WebParam(name = "AddCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddCampaignConversionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignConversionGoalsResponse>
     */
    @WebMethod(operationName = "DeleteCampaignConversionGoals", action = "DeleteCampaignConversionGoals")
    public Response<DeleteCampaignConversionGoalsResponse> deleteCampaignConversionGoalsAsync(
        @WebParam(name = "DeleteCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignConversionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteCampaignConversionGoals", action = "DeleteCampaignConversionGoals")
    public Future<?> deleteCampaignConversionGoalsAsync(
        @WebParam(name = "DeleteCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignConversionGoalsRequest parameters,
        @WebParam(name = "DeleteCampaignConversionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteCampaignConversionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteCampaignConversionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteCampaignConversionGoals", action = "DeleteCampaignConversionGoals")
    @WebResult(name = "DeleteCampaignConversionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteCampaignConversionGoalsResponse deleteCampaignConversionGoals(
        @WebParam(name = "DeleteCampaignConversionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteCampaignConversionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddDataExclusionsResponse>
     */
    @WebMethod(operationName = "AddDataExclusions", action = "AddDataExclusions")
    public Response<AddDataExclusionsResponse> addDataExclusionsAsync(
        @WebParam(name = "AddDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddDataExclusionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddDataExclusions", action = "AddDataExclusions")
    public Future<?> addDataExclusionsAsync(
        @WebParam(name = "AddDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddDataExclusionsRequest parameters,
        @WebParam(name = "AddDataExclusionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddDataExclusionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddDataExclusionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddDataExclusions", action = "AddDataExclusions")
    @WebResult(name = "AddDataExclusionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddDataExclusionsResponse addDataExclusions(
        @WebParam(name = "AddDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddDataExclusionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateDataExclusionsResponse>
     */
    @WebMethod(operationName = "UpdateDataExclusions", action = "UpdateDataExclusions")
    public Response<UpdateDataExclusionsResponse> updateDataExclusionsAsync(
        @WebParam(name = "UpdateDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateDataExclusionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateDataExclusions", action = "UpdateDataExclusions")
    public Future<?> updateDataExclusionsAsync(
        @WebParam(name = "UpdateDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateDataExclusionsRequest parameters,
        @WebParam(name = "UpdateDataExclusionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateDataExclusionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateDataExclusionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateDataExclusions", action = "UpdateDataExclusions")
    @WebResult(name = "UpdateDataExclusionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateDataExclusionsResponse updateDataExclusions(
        @WebParam(name = "UpdateDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateDataExclusionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteDataExclusionsResponse>
     */
    @WebMethod(operationName = "DeleteDataExclusions", action = "DeleteDataExclusions")
    public Response<DeleteDataExclusionsResponse> deleteDataExclusionsAsync(
        @WebParam(name = "DeleteDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteDataExclusionsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteDataExclusions", action = "DeleteDataExclusions")
    public Future<?> deleteDataExclusionsAsync(
        @WebParam(name = "DeleteDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteDataExclusionsRequest parameters,
        @WebParam(name = "DeleteDataExclusionsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteDataExclusionsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteDataExclusionsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteDataExclusions", action = "DeleteDataExclusions")
    @WebResult(name = "DeleteDataExclusionsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteDataExclusionsResponse deleteDataExclusions(
        @WebParam(name = "DeleteDataExclusionsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteDataExclusionsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetDataExclusionsByIdsResponse>
     */
    @WebMethod(operationName = "GetDataExclusionsByIds", action = "GetDataExclusionsByIds")
    public Response<GetDataExclusionsByIdsResponse> getDataExclusionsByIdsAsync(
        @WebParam(name = "GetDataExclusionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetDataExclusionsByIds", action = "GetDataExclusionsByIds")
    public Future<?> getDataExclusionsByIdsAsync(
        @WebParam(name = "GetDataExclusionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByIdsRequest parameters,
        @WebParam(name = "GetDataExclusionsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetDataExclusionsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetDataExclusionsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetDataExclusionsByIds", action = "GetDataExclusionsByIds")
    @WebResult(name = "GetDataExclusionsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetDataExclusionsByIdsResponse getDataExclusionsByIds(
        @WebParam(name = "GetDataExclusionsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetDataExclusionsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetDataExclusionsByAccountId", action = "GetDataExclusionsByAccountId")
    public Response<GetDataExclusionsByAccountIdResponse> getDataExclusionsByAccountIdAsync(
        @WebParam(name = "GetDataExclusionsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetDataExclusionsByAccountId", action = "GetDataExclusionsByAccountId")
    public Future<?> getDataExclusionsByAccountIdAsync(
        @WebParam(name = "GetDataExclusionsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByAccountIdRequest parameters,
        @WebParam(name = "GetDataExclusionsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetDataExclusionsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetDataExclusionsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetDataExclusionsByAccountId", action = "GetDataExclusionsByAccountId")
    @WebResult(name = "GetDataExclusionsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetDataExclusionsByAccountIdResponse getDataExclusionsByAccountId(
        @WebParam(name = "GetDataExclusionsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDataExclusionsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddSeasonalityAdjustmentsResponse>
     */
    @WebMethod(operationName = "AddSeasonalityAdjustments", action = "AddSeasonalityAdjustments")
    public Response<AddSeasonalityAdjustmentsResponse> addSeasonalityAdjustmentsAsync(
        @WebParam(name = "AddSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSeasonalityAdjustmentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddSeasonalityAdjustments", action = "AddSeasonalityAdjustments")
    public Future<?> addSeasonalityAdjustmentsAsync(
        @WebParam(name = "AddSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSeasonalityAdjustmentsRequest parameters,
        @WebParam(name = "AddSeasonalityAdjustmentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddSeasonalityAdjustmentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddSeasonalityAdjustmentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddSeasonalityAdjustments", action = "AddSeasonalityAdjustments")
    @WebResult(name = "AddSeasonalityAdjustmentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddSeasonalityAdjustmentsResponse addSeasonalityAdjustments(
        @WebParam(name = "AddSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddSeasonalityAdjustmentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateSeasonalityAdjustmentsResponse>
     */
    @WebMethod(operationName = "UpdateSeasonalityAdjustments", action = "UpdateSeasonalityAdjustments")
    public Response<UpdateSeasonalityAdjustmentsResponse> updateSeasonalityAdjustmentsAsync(
        @WebParam(name = "UpdateSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSeasonalityAdjustmentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateSeasonalityAdjustments", action = "UpdateSeasonalityAdjustments")
    public Future<?> updateSeasonalityAdjustmentsAsync(
        @WebParam(name = "UpdateSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSeasonalityAdjustmentsRequest parameters,
        @WebParam(name = "UpdateSeasonalityAdjustmentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateSeasonalityAdjustmentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateSeasonalityAdjustmentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateSeasonalityAdjustments", action = "UpdateSeasonalityAdjustments")
    @WebResult(name = "UpdateSeasonalityAdjustmentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateSeasonalityAdjustmentsResponse updateSeasonalityAdjustments(
        @WebParam(name = "UpdateSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateSeasonalityAdjustmentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteSeasonalityAdjustmentsResponse>
     */
    @WebMethod(operationName = "DeleteSeasonalityAdjustments", action = "DeleteSeasonalityAdjustments")
    public Response<DeleteSeasonalityAdjustmentsResponse> deleteSeasonalityAdjustmentsAsync(
        @WebParam(name = "DeleteSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSeasonalityAdjustmentsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteSeasonalityAdjustments", action = "DeleteSeasonalityAdjustments")
    public Future<?> deleteSeasonalityAdjustmentsAsync(
        @WebParam(name = "DeleteSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSeasonalityAdjustmentsRequest parameters,
        @WebParam(name = "DeleteSeasonalityAdjustmentsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteSeasonalityAdjustmentsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteSeasonalityAdjustmentsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteSeasonalityAdjustments", action = "DeleteSeasonalityAdjustments")
    @WebResult(name = "DeleteSeasonalityAdjustmentsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteSeasonalityAdjustmentsResponse deleteSeasonalityAdjustments(
        @WebParam(name = "DeleteSeasonalityAdjustmentsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteSeasonalityAdjustmentsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSeasonalityAdjustmentsByIdsResponse>
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByIds", action = "GetSeasonalityAdjustmentsByIds")
    public Response<GetSeasonalityAdjustmentsByIdsResponse> getSeasonalityAdjustmentsByIdsAsync(
        @WebParam(name = "GetSeasonalityAdjustmentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByIds", action = "GetSeasonalityAdjustmentsByIds")
    public Future<?> getSeasonalityAdjustmentsByIdsAsync(
        @WebParam(name = "GetSeasonalityAdjustmentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByIdsRequest parameters,
        @WebParam(name = "GetSeasonalityAdjustmentsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSeasonalityAdjustmentsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSeasonalityAdjustmentsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByIds", action = "GetSeasonalityAdjustmentsByIds")
    @WebResult(name = "GetSeasonalityAdjustmentsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSeasonalityAdjustmentsByIdsResponse getSeasonalityAdjustmentsByIds(
        @WebParam(name = "GetSeasonalityAdjustmentsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSeasonalityAdjustmentsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByAccountId", action = "GetSeasonalityAdjustmentsByAccountId")
    public Response<GetSeasonalityAdjustmentsByAccountIdResponse> getSeasonalityAdjustmentsByAccountIdAsync(
        @WebParam(name = "GetSeasonalityAdjustmentsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByAccountId", action = "GetSeasonalityAdjustmentsByAccountId")
    public Future<?> getSeasonalityAdjustmentsByAccountIdAsync(
        @WebParam(name = "GetSeasonalityAdjustmentsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByAccountIdRequest parameters,
        @WebParam(name = "GetSeasonalityAdjustmentsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSeasonalityAdjustmentsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSeasonalityAdjustmentsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSeasonalityAdjustmentsByAccountId", action = "GetSeasonalityAdjustmentsByAccountId")
    @WebResult(name = "GetSeasonalityAdjustmentsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSeasonalityAdjustmentsByAccountIdResponse getSeasonalityAdjustmentsByAccountId(
        @WebParam(name = "GetSeasonalityAdjustmentsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSeasonalityAdjustmentsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.CreateAssetGroupRecommendationResponse>
     */
    @WebMethod(operationName = "CreateAssetGroupRecommendation", action = "CreateAssetGroupRecommendation")
    public Response<CreateAssetGroupRecommendationResponse> createAssetGroupRecommendationAsync(
        @WebParam(name = "CreateAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateAssetGroupRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "CreateAssetGroupRecommendation", action = "CreateAssetGroupRecommendation")
    public Future<?> createAssetGroupRecommendationAsync(
        @WebParam(name = "CreateAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateAssetGroupRecommendationRequest parameters,
        @WebParam(name = "CreateAssetGroupRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<CreateAssetGroupRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.CreateAssetGroupRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "CreateAssetGroupRecommendation", action = "CreateAssetGroupRecommendation")
    @WebResult(name = "CreateAssetGroupRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public CreateAssetGroupRecommendationResponse createAssetGroupRecommendation(
        @WebParam(name = "CreateAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateAssetGroupRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.CreateResponsiveAdRecommendationResponse>
     */
    @WebMethod(operationName = "CreateResponsiveAdRecommendation", action = "CreateResponsiveAdRecommendation")
    public Response<CreateResponsiveAdRecommendationResponse> createResponsiveAdRecommendationAsync(
        @WebParam(name = "CreateResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveAdRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "CreateResponsiveAdRecommendation", action = "CreateResponsiveAdRecommendation")
    public Future<?> createResponsiveAdRecommendationAsync(
        @WebParam(name = "CreateResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveAdRecommendationRequest parameters,
        @WebParam(name = "CreateResponsiveAdRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<CreateResponsiveAdRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.CreateResponsiveAdRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "CreateResponsiveAdRecommendation", action = "CreateResponsiveAdRecommendation")
    @WebResult(name = "CreateResponsiveAdRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public CreateResponsiveAdRecommendationResponse createResponsiveAdRecommendation(
        @WebParam(name = "CreateResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveAdRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.CreateResponsiveSearchAdRecommendationResponse>
     */
    @WebMethod(operationName = "CreateResponsiveSearchAdRecommendation", action = "CreateResponsiveSearchAdRecommendation")
    public Response<CreateResponsiveSearchAdRecommendationResponse> createResponsiveSearchAdRecommendationAsync(
        @WebParam(name = "CreateResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveSearchAdRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "CreateResponsiveSearchAdRecommendation", action = "CreateResponsiveSearchAdRecommendation")
    public Future<?> createResponsiveSearchAdRecommendationAsync(
        @WebParam(name = "CreateResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveSearchAdRecommendationRequest parameters,
        @WebParam(name = "CreateResponsiveSearchAdRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<CreateResponsiveSearchAdRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.CreateResponsiveSearchAdRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "CreateResponsiveSearchAdRecommendation", action = "CreateResponsiveSearchAdRecommendation")
    @WebResult(name = "CreateResponsiveSearchAdRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public CreateResponsiveSearchAdRecommendationResponse createResponsiveSearchAdRecommendation(
        @WebParam(name = "CreateResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateResponsiveSearchAdRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.RefineAssetGroupRecommendationResponse>
     */
    @WebMethod(operationName = "RefineAssetGroupRecommendation", action = "RefineAssetGroupRecommendation")
    public Response<RefineAssetGroupRecommendationResponse> refineAssetGroupRecommendationAsync(
        @WebParam(name = "RefineAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineAssetGroupRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "RefineAssetGroupRecommendation", action = "RefineAssetGroupRecommendation")
    public Future<?> refineAssetGroupRecommendationAsync(
        @WebParam(name = "RefineAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineAssetGroupRecommendationRequest parameters,
        @WebParam(name = "RefineAssetGroupRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<RefineAssetGroupRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.RefineAssetGroupRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "RefineAssetGroupRecommendation", action = "RefineAssetGroupRecommendation")
    @WebResult(name = "RefineAssetGroupRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public RefineAssetGroupRecommendationResponse refineAssetGroupRecommendation(
        @WebParam(name = "RefineAssetGroupRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineAssetGroupRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.RefineResponsiveAdRecommendationResponse>
     */
    @WebMethod(operationName = "RefineResponsiveAdRecommendation", action = "RefineResponsiveAdRecommendation")
    public Response<RefineResponsiveAdRecommendationResponse> refineResponsiveAdRecommendationAsync(
        @WebParam(name = "RefineResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveAdRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "RefineResponsiveAdRecommendation", action = "RefineResponsiveAdRecommendation")
    public Future<?> refineResponsiveAdRecommendationAsync(
        @WebParam(name = "RefineResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveAdRecommendationRequest parameters,
        @WebParam(name = "RefineResponsiveAdRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<RefineResponsiveAdRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.RefineResponsiveAdRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "RefineResponsiveAdRecommendation", action = "RefineResponsiveAdRecommendation")
    @WebResult(name = "RefineResponsiveAdRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public RefineResponsiveAdRecommendationResponse refineResponsiveAdRecommendation(
        @WebParam(name = "RefineResponsiveAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveAdRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.RefineResponsiveSearchAdRecommendationResponse>
     */
    @WebMethod(operationName = "RefineResponsiveSearchAdRecommendation", action = "RefineResponsiveSearchAdRecommendation")
    public Response<RefineResponsiveSearchAdRecommendationResponse> refineResponsiveSearchAdRecommendationAsync(
        @WebParam(name = "RefineResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveSearchAdRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "RefineResponsiveSearchAdRecommendation", action = "RefineResponsiveSearchAdRecommendation")
    public Future<?> refineResponsiveSearchAdRecommendationAsync(
        @WebParam(name = "RefineResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveSearchAdRecommendationRequest parameters,
        @WebParam(name = "RefineResponsiveSearchAdRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<RefineResponsiveSearchAdRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.RefineResponsiveSearchAdRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "RefineResponsiveSearchAdRecommendation", action = "RefineResponsiveSearchAdRecommendation")
    @WebResult(name = "RefineResponsiveSearchAdRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public RefineResponsiveSearchAdRecommendationResponse refineResponsiveSearchAdRecommendation(
        @WebParam(name = "RefineResponsiveSearchAdRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        RefineResponsiveSearchAdRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetResponsiveAdRecommendationJobResponse>
     */
    @WebMethod(operationName = "GetResponsiveAdRecommendationJob", action = "GetResponsiveAdRecommendationJob")
    public Response<GetResponsiveAdRecommendationJobResponse> getResponsiveAdRecommendationJobAsync(
        @WebParam(name = "GetResponsiveAdRecommendationJobRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetResponsiveAdRecommendationJobRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetResponsiveAdRecommendationJob", action = "GetResponsiveAdRecommendationJob")
    public Future<?> getResponsiveAdRecommendationJobAsync(
        @WebParam(name = "GetResponsiveAdRecommendationJobRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetResponsiveAdRecommendationJobRequest parameters,
        @WebParam(name = "GetResponsiveAdRecommendationJobResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetResponsiveAdRecommendationJobResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetResponsiveAdRecommendationJobResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetResponsiveAdRecommendationJob", action = "GetResponsiveAdRecommendationJob")
    @WebResult(name = "GetResponsiveAdRecommendationJobResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetResponsiveAdRecommendationJobResponse getResponsiveAdRecommendationJob(
        @WebParam(name = "GetResponsiveAdRecommendationJobRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetResponsiveAdRecommendationJobRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateConversionValueRulesResponse>
     */
    @WebMethod(operationName = "UpdateConversionValueRules", action = "UpdateConversionValueRules")
    public Response<UpdateConversionValueRulesResponse> updateConversionValueRulesAsync(
        @WebParam(name = "UpdateConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateConversionValueRules", action = "UpdateConversionValueRules")
    public Future<?> updateConversionValueRulesAsync(
        @WebParam(name = "UpdateConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesRequest parameters,
        @WebParam(name = "UpdateConversionValueRulesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateConversionValueRulesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateConversionValueRulesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateConversionValueRules", action = "UpdateConversionValueRules")
    @WebResult(name = "UpdateConversionValueRulesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateConversionValueRulesResponse updateConversionValueRules(
        @WebParam(name = "UpdateConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateConversionValueRulesStatusResponse>
     */
    @WebMethod(operationName = "UpdateConversionValueRulesStatus", action = "UpdateConversionValueRulesStatus")
    public Response<UpdateConversionValueRulesStatusResponse> updateConversionValueRulesStatusAsync(
        @WebParam(name = "UpdateConversionValueRulesStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesStatusRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateConversionValueRulesStatus", action = "UpdateConversionValueRulesStatus")
    public Future<?> updateConversionValueRulesStatusAsync(
        @WebParam(name = "UpdateConversionValueRulesStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesStatusRequest parameters,
        @WebParam(name = "UpdateConversionValueRulesStatusResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateConversionValueRulesStatusResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateConversionValueRulesStatusResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateConversionValueRulesStatus", action = "UpdateConversionValueRulesStatus")
    @WebResult(name = "UpdateConversionValueRulesStatusResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateConversionValueRulesStatusResponse updateConversionValueRulesStatus(
        @WebParam(name = "UpdateConversionValueRulesStatusRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateConversionValueRulesStatusRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddConversionValueRulesResponse>
     */
    @WebMethod(operationName = "AddConversionValueRules", action = "AddConversionValueRules")
    public Response<AddConversionValueRulesResponse> addConversionValueRulesAsync(
        @WebParam(name = "AddConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionValueRulesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddConversionValueRules", action = "AddConversionValueRules")
    public Future<?> addConversionValueRulesAsync(
        @WebParam(name = "AddConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionValueRulesRequest parameters,
        @WebParam(name = "AddConversionValueRulesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddConversionValueRulesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddConversionValueRulesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddConversionValueRules", action = "AddConversionValueRules")
    @WebResult(name = "AddConversionValueRulesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddConversionValueRulesResponse addConversionValueRules(
        @WebParam(name = "AddConversionValueRulesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddConversionValueRulesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetConversionValueRulesByAccountIdResponse>
     */
    @WebMethod(operationName = "GetConversionValueRulesByAccountId", action = "GetConversionValueRulesByAccountId")
    public Response<GetConversionValueRulesByAccountIdResponse> getConversionValueRulesByAccountIdAsync(
        @WebParam(name = "GetConversionValueRulesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetConversionValueRulesByAccountId", action = "GetConversionValueRulesByAccountId")
    public Future<?> getConversionValueRulesByAccountIdAsync(
        @WebParam(name = "GetConversionValueRulesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByAccountIdRequest parameters,
        @WebParam(name = "GetConversionValueRulesByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetConversionValueRulesByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetConversionValueRulesByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetConversionValueRulesByAccountId", action = "GetConversionValueRulesByAccountId")
    @WebResult(name = "GetConversionValueRulesByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetConversionValueRulesByAccountIdResponse getConversionValueRulesByAccountId(
        @WebParam(name = "GetConversionValueRulesByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetConversionValueRulesByIdsResponse>
     */
    @WebMethod(operationName = "GetConversionValueRulesByIds", action = "GetConversionValueRulesByIds")
    public Response<GetConversionValueRulesByIdsResponse> getConversionValueRulesByIdsAsync(
        @WebParam(name = "GetConversionValueRulesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetConversionValueRulesByIds", action = "GetConversionValueRulesByIds")
    public Future<?> getConversionValueRulesByIdsAsync(
        @WebParam(name = "GetConversionValueRulesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByIdsRequest parameters,
        @WebParam(name = "GetConversionValueRulesByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetConversionValueRulesByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetConversionValueRulesByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetConversionValueRulesByIds", action = "GetConversionValueRulesByIds")
    @WebResult(name = "GetConversionValueRulesByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetConversionValueRulesByIdsResponse getConversionValueRulesByIds(
        @WebParam(name = "GetConversionValueRulesByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetConversionValueRulesByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddBrandKitsResponse>
     */
    @WebMethod(operationName = "AddBrandKits", action = "AddBrandKits")
    public Response<AddBrandKitsResponse> addBrandKitsAsync(
        @WebParam(name = "AddBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBrandKitsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddBrandKits", action = "AddBrandKits")
    public Future<?> addBrandKitsAsync(
        @WebParam(name = "AddBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBrandKitsRequest parameters,
        @WebParam(name = "AddBrandKitsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddBrandKitsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddBrandKitsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddBrandKits", action = "AddBrandKits")
    @WebResult(name = "AddBrandKitsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddBrandKitsResponse addBrandKits(
        @WebParam(name = "AddBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddBrandKitsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateBrandKitsResponse>
     */
    @WebMethod(operationName = "UpdateBrandKits", action = "UpdateBrandKits")
    public Response<UpdateBrandKitsResponse> updateBrandKitsAsync(
        @WebParam(name = "UpdateBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBrandKitsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateBrandKits", action = "UpdateBrandKits")
    public Future<?> updateBrandKitsAsync(
        @WebParam(name = "UpdateBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBrandKitsRequest parameters,
        @WebParam(name = "UpdateBrandKitsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateBrandKitsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateBrandKitsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateBrandKits", action = "UpdateBrandKits")
    @WebResult(name = "UpdateBrandKitsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateBrandKitsResponse updateBrandKits(
        @WebParam(name = "UpdateBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateBrandKitsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.DeleteBrandKitsResponse>
     */
    @WebMethod(operationName = "DeleteBrandKits", action = "DeleteBrandKits")
    public Response<DeleteBrandKitsResponse> deleteBrandKitsAsync(
        @WebParam(name = "DeleteBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBrandKitsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "DeleteBrandKits", action = "DeleteBrandKits")
    public Future<?> deleteBrandKitsAsync(
        @WebParam(name = "DeleteBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBrandKitsRequest parameters,
        @WebParam(name = "DeleteBrandKitsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<DeleteBrandKitsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.DeleteBrandKitsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "DeleteBrandKits", action = "DeleteBrandKits")
    @WebResult(name = "DeleteBrandKitsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public DeleteBrandKitsResponse deleteBrandKits(
        @WebParam(name = "DeleteBrandKitsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        DeleteBrandKitsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.CreateBrandKitRecommendationResponse>
     */
    @WebMethod(operationName = "CreateBrandKitRecommendation", action = "CreateBrandKitRecommendation")
    public Response<CreateBrandKitRecommendationResponse> createBrandKitRecommendationAsync(
        @WebParam(name = "CreateBrandKitRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateBrandKitRecommendationRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "CreateBrandKitRecommendation", action = "CreateBrandKitRecommendation")
    public Future<?> createBrandKitRecommendationAsync(
        @WebParam(name = "CreateBrandKitRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateBrandKitRecommendationRequest parameters,
        @WebParam(name = "CreateBrandKitRecommendationResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<CreateBrandKitRecommendationResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.CreateBrandKitRecommendationResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "CreateBrandKitRecommendation", action = "CreateBrandKitRecommendation")
    @WebResult(name = "CreateBrandKitRecommendationResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public CreateBrandKitRecommendationResponse createBrandKitRecommendation(
        @WebParam(name = "CreateBrandKitRecommendationRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        CreateBrandKitRecommendationRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.AddNewCustomerAcquisitionGoalsResponse>
     */
    @WebMethod(operationName = "AddNewCustomerAcquisitionGoals", action = "AddNewCustomerAcquisitionGoals")
    public Response<AddNewCustomerAcquisitionGoalsResponse> addNewCustomerAcquisitionGoalsAsync(
        @WebParam(name = "AddNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNewCustomerAcquisitionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "AddNewCustomerAcquisitionGoals", action = "AddNewCustomerAcquisitionGoals")
    public Future<?> addNewCustomerAcquisitionGoalsAsync(
        @WebParam(name = "AddNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNewCustomerAcquisitionGoalsRequest parameters,
        @WebParam(name = "AddNewCustomerAcquisitionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<AddNewCustomerAcquisitionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.AddNewCustomerAcquisitionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "AddNewCustomerAcquisitionGoals", action = "AddNewCustomerAcquisitionGoals")
    @WebResult(name = "AddNewCustomerAcquisitionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public AddNewCustomerAcquisitionGoalsResponse addNewCustomerAcquisitionGoals(
        @WebParam(name = "AddNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        AddNewCustomerAcquisitionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateNewCustomerAcquisitionGoalsResponse>
     */
    @WebMethod(operationName = "UpdateNewCustomerAcquisitionGoals", action = "UpdateNewCustomerAcquisitionGoals")
    public Response<UpdateNewCustomerAcquisitionGoalsResponse> updateNewCustomerAcquisitionGoalsAsync(
        @WebParam(name = "UpdateNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateNewCustomerAcquisitionGoalsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateNewCustomerAcquisitionGoals", action = "UpdateNewCustomerAcquisitionGoals")
    public Future<?> updateNewCustomerAcquisitionGoalsAsync(
        @WebParam(name = "UpdateNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateNewCustomerAcquisitionGoalsRequest parameters,
        @WebParam(name = "UpdateNewCustomerAcquisitionGoalsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateNewCustomerAcquisitionGoalsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateNewCustomerAcquisitionGoalsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateNewCustomerAcquisitionGoals", action = "UpdateNewCustomerAcquisitionGoals")
    @WebResult(name = "UpdateNewCustomerAcquisitionGoalsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateNewCustomerAcquisitionGoalsResponse updateNewCustomerAcquisitionGoals(
        @WebParam(name = "UpdateNewCustomerAcquisitionGoalsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateNewCustomerAcquisitionGoalsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetNewCustomerAcquisitionGoalsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetNewCustomerAcquisitionGoalsByAccountId", action = "GetNewCustomerAcquisitionGoalsByAccountId")
    public Response<GetNewCustomerAcquisitionGoalsByAccountIdResponse> getNewCustomerAcquisitionGoalsByAccountIdAsync(
        @WebParam(name = "GetNewCustomerAcquisitionGoalsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNewCustomerAcquisitionGoalsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetNewCustomerAcquisitionGoalsByAccountId", action = "GetNewCustomerAcquisitionGoalsByAccountId")
    public Future<?> getNewCustomerAcquisitionGoalsByAccountIdAsync(
        @WebParam(name = "GetNewCustomerAcquisitionGoalsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNewCustomerAcquisitionGoalsByAccountIdRequest parameters,
        @WebParam(name = "GetNewCustomerAcquisitionGoalsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetNewCustomerAcquisitionGoalsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetNewCustomerAcquisitionGoalsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetNewCustomerAcquisitionGoalsByAccountId", action = "GetNewCustomerAcquisitionGoalsByAccountId")
    @WebResult(name = "GetNewCustomerAcquisitionGoalsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetNewCustomerAcquisitionGoalsByAccountIdResponse getNewCustomerAcquisitionGoalsByAccountId(
        @WebParam(name = "GetNewCustomerAcquisitionGoalsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetNewCustomerAcquisitionGoalsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBrandKitsByAccountIdResponse>
     */
    @WebMethod(operationName = "GetBrandKitsByAccountId", action = "GetBrandKitsByAccountId")
    public Response<GetBrandKitsByAccountIdResponse> getBrandKitsByAccountIdAsync(
        @WebParam(name = "GetBrandKitsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByAccountIdRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBrandKitsByAccountId", action = "GetBrandKitsByAccountId")
    public Future<?> getBrandKitsByAccountIdAsync(
        @WebParam(name = "GetBrandKitsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByAccountIdRequest parameters,
        @WebParam(name = "GetBrandKitsByAccountIdResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBrandKitsByAccountIdResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBrandKitsByAccountIdResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBrandKitsByAccountId", action = "GetBrandKitsByAccountId")
    @WebResult(name = "GetBrandKitsByAccountIdResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBrandKitsByAccountIdResponse getBrandKitsByAccountId(
        @WebParam(name = "GetBrandKitsByAccountIdRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByAccountIdRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetBrandKitsByIdsResponse>
     */
    @WebMethod(operationName = "GetBrandKitsByIds", action = "GetBrandKitsByIds")
    public Response<GetBrandKitsByIdsResponse> getBrandKitsByIdsAsync(
        @WebParam(name = "GetBrandKitsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByIdsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetBrandKitsByIds", action = "GetBrandKitsByIds")
    public Future<?> getBrandKitsByIdsAsync(
        @WebParam(name = "GetBrandKitsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByIdsRequest parameters,
        @WebParam(name = "GetBrandKitsByIdsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetBrandKitsByIdsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetBrandKitsByIdsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetBrandKitsByIds", action = "GetBrandKitsByIds")
    @WebResult(name = "GetBrandKitsByIdsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetBrandKitsByIdsResponse getBrandKitsByIds(
        @WebParam(name = "GetBrandKitsByIdsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetBrandKitsByIdsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetClipchampTemplatesResponse>
     */
    @WebMethod(operationName = "GetClipchampTemplates", action = "GetClipchampTemplates")
    public Response<GetClipchampTemplatesResponse> getClipchampTemplatesAsync(
        @WebParam(name = "GetClipchampTemplatesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetClipchampTemplatesRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetClipchampTemplates", action = "GetClipchampTemplates")
    public Future<?> getClipchampTemplatesAsync(
        @WebParam(name = "GetClipchampTemplatesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetClipchampTemplatesRequest parameters,
        @WebParam(name = "GetClipchampTemplatesResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetClipchampTemplatesResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetClipchampTemplatesResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetClipchampTemplates", action = "GetClipchampTemplates")
    @WebResult(name = "GetClipchampTemplatesResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetClipchampTemplatesResponse getClipchampTemplates(
        @WebParam(name = "GetClipchampTemplatesRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetClipchampTemplatesRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSupportedClipchampAudioResponse>
     */
    @WebMethod(operationName = "GetSupportedClipchampAudio", action = "GetSupportedClipchampAudio")
    public Response<GetSupportedClipchampAudioResponse> getSupportedClipchampAudioAsync(
        @WebParam(name = "GetSupportedClipchampAudioRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedClipchampAudioRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSupportedClipchampAudio", action = "GetSupportedClipchampAudio")
    public Future<?> getSupportedClipchampAudioAsync(
        @WebParam(name = "GetSupportedClipchampAudioRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedClipchampAudioRequest parameters,
        @WebParam(name = "GetSupportedClipchampAudioResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSupportedClipchampAudioResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSupportedClipchampAudioResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSupportedClipchampAudio", action = "GetSupportedClipchampAudio")
    @WebResult(name = "GetSupportedClipchampAudioResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSupportedClipchampAudioResponse getSupportedClipchampAudio(
        @WebParam(name = "GetSupportedClipchampAudioRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedClipchampAudioRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetSupportedFontsResponse>
     */
    @WebMethod(operationName = "GetSupportedFonts", action = "GetSupportedFonts")
    public Response<GetSupportedFontsResponse> getSupportedFontsAsync(
        @WebParam(name = "GetSupportedFontsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedFontsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetSupportedFonts", action = "GetSupportedFonts")
    public Future<?> getSupportedFontsAsync(
        @WebParam(name = "GetSupportedFontsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedFontsRequest parameters,
        @WebParam(name = "GetSupportedFontsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetSupportedFontsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetSupportedFontsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetSupportedFonts", action = "GetSupportedFonts")
    @WebResult(name = "GetSupportedFontsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetSupportedFontsResponse getSupportedFonts(
        @WebParam(name = "GetSupportedFontsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetSupportedFontsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetHealthCheckResponse>
     */
    @WebMethod(operationName = "GetHealthCheck", action = "GetHealthCheck")
    public Response<GetHealthCheckResponse> getHealthCheckAsync(
        @WebParam(name = "GetHealthCheckRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetHealthCheckRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetHealthCheck", action = "GetHealthCheck")
    public Future<?> getHealthCheckAsync(
        @WebParam(name = "GetHealthCheckRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetHealthCheckRequest parameters,
        @WebParam(name = "GetHealthCheckResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetHealthCheckResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetHealthCheckResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetHealthCheck", action = "GetHealthCheck")
    @WebResult(name = "GetHealthCheckResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetHealthCheckResponse getHealthCheck(
        @WebParam(name = "GetHealthCheckRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetHealthCheckRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetDiagnosticsResponse>
     */
    @WebMethod(operationName = "GetDiagnostics", action = "GetDiagnostics")
    public Response<GetDiagnosticsResponse> getDiagnosticsAsync(
        @WebParam(name = "GetDiagnosticsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDiagnosticsRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetDiagnostics", action = "GetDiagnostics")
    public Future<?> getDiagnosticsAsync(
        @WebParam(name = "GetDiagnosticsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDiagnosticsRequest parameters,
        @WebParam(name = "GetDiagnosticsResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetDiagnosticsResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetDiagnosticsResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetDiagnostics", action = "GetDiagnostics")
    @WebResult(name = "GetDiagnosticsResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetDiagnosticsResponse getDiagnostics(
        @WebParam(name = "GetDiagnosticsRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetDiagnosticsRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.GetAnnotationOptOutResponse>
     */
    @WebMethod(operationName = "GetAnnotationOptOut", action = "GetAnnotationOptOut")
    public Response<GetAnnotationOptOutResponse> getAnnotationOptOutAsync(
        @WebParam(name = "GetAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAnnotationOptOutRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "GetAnnotationOptOut", action = "GetAnnotationOptOut")
    public Future<?> getAnnotationOptOutAsync(
        @WebParam(name = "GetAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAnnotationOptOutRequest parameters,
        @WebParam(name = "GetAnnotationOptOutResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<GetAnnotationOptOutResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.GetAnnotationOptOutResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "GetAnnotationOptOut", action = "GetAnnotationOptOut")
    @WebResult(name = "GetAnnotationOptOutResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public GetAnnotationOptOutResponse getAnnotationOptOut(
        @WebParam(name = "GetAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        GetAnnotationOptOutRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

    /**
     * 
     * @param parameters
     * @return
     *     returns jakarta.xml.ws.Response<com.microsoft.bingads.v13.campaignmanagement.UpdateAnnotationOptOutResponse>
     */
    @WebMethod(operationName = "UpdateAnnotationOptOut", action = "UpdateAnnotationOptOut")
    public Response<UpdateAnnotationOptOutResponse> updateAnnotationOptOutAsync(
        @WebParam(name = "UpdateAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAnnotationOptOutRequest parameters);

    /**
     * 
     * @param asyncHandler
     * @param parameters
     * @return
     *     returns java.util.concurrent.Future<? extends java.lang.Object>
     */
    @WebMethod(operationName = "UpdateAnnotationOptOut", action = "UpdateAnnotationOptOut")
    public Future<?> updateAnnotationOptOutAsync(
        @WebParam(name = "UpdateAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAnnotationOptOutRequest parameters,
        @WebParam(name = "UpdateAnnotationOptOutResponse", targetNamespace = "", partName = "asyncHandler")
        AsyncHandler<UpdateAnnotationOptOutResponse> asyncHandler);

    /**
     * 
     * @param parameters
     * @return
     *     returns com.microsoft.bingads.v13.campaignmanagement.UpdateAnnotationOptOutResponse
     * @throws AdApiFaultDetail_Exception
     * @throws ApiFaultDetail_Exception
     */
    @WebMethod(operationName = "UpdateAnnotationOptOut", action = "UpdateAnnotationOptOut")
    @WebResult(name = "UpdateAnnotationOptOutResponse", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
    public UpdateAnnotationOptOutResponse updateAnnotationOptOut(
        @WebParam(name = "UpdateAnnotationOptOutRequest", targetNamespace = "https://bingads.microsoft.com/CampaignManagement/v13", partName = "parameters")
        UpdateAnnotationOptOutRequest parameters)
        throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception
    ;

}
