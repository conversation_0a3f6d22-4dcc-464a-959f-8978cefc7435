﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- wait between retries on partition when there is no work -->
    <add key="TreeNotificationTaskIdlePollIntervalInSeconds" value="10"/>
    <add key="OfferTreeMapTaskIdlePollIntervalInSeconds" value="10"/>
    <add key="CampaignDbMonitorTaskIdlePollIntervalInSeconds" value="300"/>
    <add key="AzureDbMonitorTaskIdlePollIntervalInSeconds" value="300"/>
    <add key="IdleWaitIntervalInSeconds" value="10"/>
    <add key="MetaDataDbRefreshIntervalInSeconds" value="1800"/>
    <add key="MetaDataDbRetryIntervalInSeconds" value="5"/>
    <add key="RetryBaseDelayMillis" value="0"/>

    <add key="PercentAccountsUseAsyncPartitionedDao" value="0"/>
    <add key="UseShardedMetadata" value="true"/>

    <add key="EnableParallelFetch" value="false"/>

    <add key="OfferDbPartitionInfoCacheAgeInMinutes" value="30"/>
    <add key="MaxBackOffIntervalInSeconds" value="259200"/>
    <add key="OfferTaskRetryWaitInMinutes" value="5"/>

    <add key="ReadEntityIdWhileFetchingJob" value="true"/>

    <!-- Queue runtime -->
    <add key="IsQueueRuntimeEnabled" value="true"/>

    <add key="SystemUserId" value="0"/>

    <!-- AdgroupShardingOfflineTask -->
    <add key="AdgroupShardingOfflineTask_Enabled" value="true"/>
    <add key="OfflineTaskDurationBetweenExecutions" value="5"/>
    <add key="TreeNotificationTaskDurationBetweenExecutions" value="2"/>
    <add key="OfferTreeMapTaskDurationBetweenExecutions" value="2"/>

    <add key="OfflineTaskMaxInMemoryQueueSize" value="8"/>
    <add key="EnabledShardPartitions" value=""/>

    <add key="AdLimit_OpTypeEnabled" value="true"/>
    <add key="AdLimit_OpTypeBackoffInterval" value="5"/>

    <add key="KeywordLimit_OpTypeEnabled" value="true"/>
    <add key="KeywordLimit_OpTypeBackoffInterval" value="5"/>

    <add key="OrderLimit_OpTypeEnabled" value="true"/>
    <add key="OrderLimit_OpTypeBackoffInterval" value="5"/>

    <add key="AllEntityLimit_OpTypeEnabled" value="true"/>
    <add key="AllEntityLimit_OpTypeBackoffInterval" value="5"/>

    <add key="NegativeKeywordLimit_OpTypeEnabled" value="true"/>
    <add key="NegativeKeywordLimit_OpTypeBackoffInterval" value="5"/>

    <add key="OrderAudienceLimit_OpTypeEnabled" value="true"/>
    <add key="OrderAudienceLimit_OpTypeBackoffInterval" value="5"/>

    <add key="DSAAutoTargetLimit_OpTypeEnabled" value="true"/>
    <add key="DSAAutoTargetLimit_OpTypeBackoffInterval" value="5"/>

    <add key="OrderDelete_OpTypeEnabled" value="true"/>
    <add key="OrderDelete_OpTypeBackoffInterval" value="5"/>

    <add key="CampaignDelete_OpTypeEnabled" value="true"/>
    <add key="CampaignDelete_OpTypeBackoffInterval" value="5"/>

    <add key="CampaignActive_OpTypeEnabled" value="true"/>
    <add key="CampaignActive_OpTypeBackoffInterval" value="5"/>

    <add key="OpcCampaignId_OpTypeEnabled" value="true"/>
    <add key="OpcCampaignId_OpTypeBackoffInterval" value="5"/>

    <add key="OpcOrderId_OpTypeEnabled" value="true"/>
    <add key="OpcOrderId_OpTypeBackoffInterval" value="5"/>

    <add key="CampaignExpanding_PickUpBatchSize" value="2"/>
    <add key="CampaignExpanding_DBPersistenceBatchSize" value="4"/>

    <add key="MinimumBatchSizeToUseMultithreadingForGetBasicAdgroupInfo" value="25000"/>
    <add key="RetrieveEncryptionCertificateSettingsFromDatabase" value="false"/>
    <add key="UseDeltaLoadForAdvertiserCustomerCacheAfterInitialLoad" value="true"/>
    <add key="UseDataReaderAndDeltaLoadShardMap" value="false"/>
    <add key="UseBinarySearchToFindShardPartition" value="false"/>
    <add key="ShardedDeltaLoadDbTypes" value=""/>

    <add key="AdExOrderEditorialStatus_OpTypeEnabled" value="true"/>
    <add key="AdExOrderEditorialStatus_OpTypeBackoffInterval" value="5"/>
    <add key="NonShardedDbTypesUsingShardedHistogram" value=""/>

    <add key="SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeEnabled" value="true"/>
    <add key="SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval" value="5"/>

    <add key="LabelDelete_OpTypeEnabled" value="true"/>
    <add key="LabelDelete_OpTypeBackoffInterval" value="5"/>
    <add key="LabelDeleteJobConcurrency" value="4"/>

    <add key="LabelIdLevelRecount_OpTypeEnabled" value="true"/>
    <add key="LabelIdLevelRecount_OpTypeBackoffInterval" value="5"/>
    <add key="LabelIdLevelRecountJobConcurrency" value="4"/>

    <add key="AccountLevelLabelRecount_OpTypeEnabled" value="true"/>
    <add key="AccountLevelLabelRecount_OpTypeBackoffInterval" value="5"/>
    <add key="AccountLevelLabelRecountEnableMultiBatchUpdate" value="true"/>
    <add key="AccountLevelLabelRecountUpdateMaxCount" value="1000"/>

    <add key="AccountLevelLabelsPerEntityRecount_OpTypeEnabled" value="true"/>
    <add key="AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval" value="5"/>

    <add key="LabelIdLevelLabelsPerEntityRecount_OpTypeEnabled" value="true"/>
    <add key="LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval" value="5"/>
    <add key="LabelIdLevelLabelsPerEntityRecountJobConcurrency" value="4"/>
    <add key="LabelIdLevelLabelsPerEntityRecountEntityUpdateMaxCount" value="1000"/>

    <add key="AccountDeleteForPurge_OpTypeEnabled" value="true"/>
    <add key="AccountDeleteForPurge_OpTypeBackoffInterval" value="600"/>

    <add key="OrderEditorialStatusQueueProcessor_OpTypeEnabled" value="true"/>
    <!--Interval in seconds-->
    <add key="OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval" value="300"/>

    <add key="AdexOrderEditorialExpending_OpTypeEnabled" value="true"/>
    <add key="AdexOrderEditorialExpending_OpTypeBackoffInterval" value="5"/>
    <add key="AdexOrderEditorialExpendingDefaultCollectionSize" value="5000"/>
    <add key="AdexOrderEditorialExpendingMaxRowCount" value="250000"/>
    <add key="AdexOrderEditorialExpendingBatchRowCount" value="1000"/>
    <add key="AdexOrderEditorialExpendingRetryCount" value="3"/>
    <add key="AdexOrderEditorialExpendingRetryTimeSpanInSecond" value="3"/>
    <add key="AdexOrderEditorialExpendingConsumerCount" value="3"/>

    <add key="OrderEditorialQueueJobProcessorLoadBatchRowCount" value="1000"/>
    <add key="OrderEditorialQueueJobProcessorUpdateBatchRowCount" value="1000"/>

    <add key="SkipPerformanceMethodEnterForMapOffersTreeNodeService" value="true"/>
    <add key="PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService" value="00:00:00.010"/>

    <add key="UpdateCampaignActiveOnNegativeKeywordShard" value="true" />
    <add key="UpdateCampaignDeleteOnNegativeKeywordShard" value="true" />
    <add key="UpdateOrderDeleteOnNegativeKeywordShard" value="true" />

    <add key="LoadCertFromLocalEnabled" value="true" />

    <add key="BuildCustomerIdCacheForNKWMigration" value="false" />

    <add key="GetOfflineTaskCommandTimeoutInSeconds" value="55" />

    <add key="OfferTreeMapTask_DynamicInstances" value="4"/>
    <add key="TreeNotificationTask_DynamicInstances" value="5"/>

    <add key="UseNonCacheFlowInOfferTreeMapTask" value="false"/>

    <add key="EnableLoggingForTaskGetDetails" value="true" />
    <add key="EnableHotelBookingGoal" value="false"/>

    <add key="AimOrderLevelPrivacyCheck_OpTypeEnabled" value="false"/>
    <add key="AimOrderLevelPrivacyCheck_OpTypeBackoffInterval" value="5"/>
    <add key="AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount" value="3"/>
    <add key="AimOrderLevelPrivacyCheck_OpTypeIsScheduledTask" value="true"/>
    <add key="AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval" value="5"/>
    <add key="AimCampaignLevelPrivacyCheck_OpTypeEnabled" value="true"/>
    <add key="PrivacyCheckPendingJobRescheduleDelayInSec" value="10800"/>
    <add key="PrivacyCheckCompletedJobRescheduleDelayInSec" value="86400"/>
    <add key="PrivacyCheckCallAdInsightConcurrency" value="1"/>
    <add key="PrivacyCheckTimeoutInMillisecond" value="5000"/>

    <add key="AdInsightMtServiceUrl" value="http://localhost:10875/AdIntelligenceMTMockService.svc" />
    <add key="AdInsightODataEndpointUrl" value="http://appsdevdev.redmond.corp.microsoft.com:823/AdInsightMT/V2/Odata" />
    <add key="AdInsightWaitTimeBetweenPollInSeconds" value="1" />
    <add key="AdInsightMaxPollCount" value="5" />
    <add key="CcmtTokenExpireTimeInMinutes" value="30" />
    <add key="DefaultCcmtCustomerId" value="264388" />
    <add key="DefaultCcmtAccountId" value="256473" />

    <add key="PrimaryAuthenticatedAccountUsernameKey" value="CMMTSystemUser1Name" />
    <!--[SuppressMessage("Microsoft.Security", "CS002:SecretInNextLine", Justification="Not a real password")]-->
    <add key="PrimaryAuthenticatedAccountPasswordKey" value="CMMTSystemUser1Password" />

    <add key="SecondaryAuthenticatedAccountUsernameKey" value="CMMTSystemUser2Name" />
    <!--[SuppressMessage("Microsoft.Security", "CS002:SecretInNextLine", Justification="Not a real password")]-->
    <add key="SecondaryAuthenticatedAccountPasswordKey" value="CMMTSystemUser2Password" />

    <add key="FlipAuthenticatedAccount" value="false" />

    <add key="EnableParallelExecuteBSCDBSqlDataReader" value="false" />

    <add key="AudienceIdLevelRecount_OpTypeEnabled" value="true"/>
    <add key="AudienceIdLevelRecount_OpTypeBackoffInterval" value="5"/>
    <add key="AudienceIdLevelRecountJobConcurrency" value="4"/>

    <add key="AccountLevelAudienceRecount_OpTypeEnabled" value="true"/>
    <add key="AccountLevelAudienceRecount_OpTypeBackoffInterval" value="5"/>

    <add key="LicenseStockImage_OpTypeEnabled" value="false"/>
    <add key="LicenseStockImage_OpTypeIsScheduledTask" value="true"/>
    <add key="LicenseStockImage_OpTypeBackoffInterval" value="5"/>

    <add key="VideoDownload_OpTypeEnabled" value="true"/>
    <add key="VideoDownload_OpTypeIsScheduledTask" value="false"/>
    <add key="VideoDownload_OpTypeBackoffInterval" value="5"/>

    <add key="VideoDeleteCleanup_OpTypeEnabled" value="true"/>
    <add key="VideoDeleteCleanup_OpTypeIsScheduledTask" value="false"/>
    <add key="VideoDeleteCleanup_OpTypeBackoffInterval" value="5"/>

    <add key="VideoAdaptiveStreamingTranscode_OpTypeEnabled" value="true"/>
    <add key="VideoAdaptiveStreamingTranscode_OpTypeIsScheduledTask" value="true"/>
    <add key="VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval" value="5"/>

    <add key="VideoArmEndpoint" value="http://localhost:10875/dam.mock" />

      <add key="DefaultDeltaBackOff" value="1" />
    <add key="DefaultMaxRetryCount" value="3" />
    <add key="DefaultMaxExecutionTimeInSecondsForUploading" value="3600" />
    <add key="MaxDegreeOfParallelismForUploading" value="1" />
    <add key="DefaultSingleBlobUploadThresholdInBytes" value="********" />
    <add key="BlobAccessExpiryTimeInSeconds" value="120" />
    <add key="AzureBlobConnectionString" value="KeyVaultSecret:AzureBlobConnectionString" />

    <add key="AudienceDelete_OpTypeEnabled" value="true"/>
    <add key="AudienceDelete_OpTypeBackoffInterval" value="5"/>
    <add key="AudienceDeleteJobConcurrency" value="4"/>

    <add key="ImageMigrationEnabled" value="false"/>
    <add key="ImageAspectMigrationEnabled" value="false"/>
    <add key="TestCustomerForImageMigration" value=""/>
    <add key="TestCustomersForImageAspectMigration" value=""/>
    <add key="ImageAspectMigrationFeatureId" value="526"/>
    <add key="ImageAspectMigrationFeatureGA" value=""/>
    <add key="ImageMigrationWaitPerBatchMillisec" value="50"/>
    <add key="ImageMigrationWaitNoWorkMultiplier" value="15"/>
    <add key="ImageMigrationBatchSize" value="10"/>
    <add key="EnableImageReKey" value="true"/>

    <add key="ThumbnailUrlRegEx" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2"/>
    <add key="ObjectStoreRetryCount" value="20"/>
    <add key="ObjectStoreRetryDelayMilliseconds" value="100"/>
    <add key="ObjectStorePartnerName" value="Ads-DAM"/>
    <add key="ObjectStoreOldContainerName" value="ImageContainer"/>

    <add key="CampaignLevelOrderTargetSizeRecount_OpTypeEnabled" value="true"/>
    <add key="CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval" value="5"/>
    <add key="CampaignLevelTargetSizeRecountJobConcurrency" value="4"/>
    <add key="CampaignLevelTargetSizeRecountAccountConcurrency" value="10"/>

    <add key="CustomerLevelOrderItemRecount_OpTypeEnabled" value="true"/>
    <add key="CustomerLevelOrderItemRecount_OpTypeBackoffInterval" value="5"/>

    <add key="CampaignLevelTargetSizeRecount_OpTypeEnabled" value="true"/>
    <add key="CampaignLevelTargetSizeRecount_OpTypeBackoffInterval" value="5"/>
    <add key="CampaignLevelCampaignAndOrderTargetSizeRecountConcurrency" value="5"/>

    <add key="PauseSharedLibraryEntityAssociations_OpTypeEnabled" value="true"/>
    <add key="PauseSharedLibraryEntityAssociationsJobConcurrency" value="4"/>
    <add key="PauseSharedLibraryEntityAccountLevelConcurrency" value="2"/>
    <add key="PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval" value="5"/>

    <add key="PauseGoalForAccount_OpTypeEnabled" value="true"/>
    <add key="PauseGoalForAccount_OpTypeBackoffInterval" value="5"/>

    <add key="PauseAudienceAssociationForAccount_OpTypeEnabled" value="true"/>
    <add key="PauseAudienceAssociationForAccount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByCustomerGoalRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByCustomerGoalRecount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByCustomerRemarketingListRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval" value="5"/>

    <add key="AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled" value="true"/>
    <add key="AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByCustomerProductAudienceRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByAccountGoalRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByAccountGoalRecount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByAccountRemarketingListRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval" value="5"/>

    <add key="TagUsedByAccountProductAudienceRecount_OpTypeEnabled" value="true"/>
    <add key="TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval" value="5"/>

    <add key="AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled" value="true"/>
    <add key="AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval" value="5"/>
    <add key="AudienceLevelCampaignAudienceAssociationRecountJobConcurrency" value="4"/>
    <add key="AudienceLevelCampaignAudienceAssociationUsedByRecountEnable" value="true"/>

    <add key="AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled" value="true"/>
    <add key="AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval" value="5"/>
    <add key="AccountLevelCampaignAudienceAssociationUsedByRecountEnable" value="true"/>

    <add key="AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled" value="true"/>
    <add key="AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval" value="5"/>

    <add key="AccountCreationParallelTracking_OpTypeBackoffInterval" value="5"/>

    <add key="AccoutImageReLicense_OpTypeEnabled" value="false"/>
    <add key="AccoutImageReLicense_OpTypeBackoffInterval" value="5"/>

    <add key="StockImageRekey_OpTypeEnabled" value="false"/>
    <add key="StockImageRekey_OpTypeBackoffInterval" value="5"/>
    <add key="StockImageRekey_OpTypeIsScheduledTask" value="true"/>

    <add key="NewStockImageRekey_OpTypeEnabled" value="true"/>
    <add key="NewStockImageRekey_OpTypeBackoffInterval" value="5"/>
    <add key="NewStockImageRekey_OpTypeIsScheduledTask" value="true"/>

    <add key="AdImpressionTrackingUrl_OpTypeEnabled" value="true"/>
    <add key="AdImpressionTrackingUrl_OpTypeIsScheduledTask" value="true"/>
    <add key="AdImpressionTrackingUrl_OpTypeBackoffInterval" value="5"/>

    <add key="AdExtensionOfflineProcessing_OpTypeEnabled" value="true"/>
    <add key="AdExtensionOfflineProcessing_OpTypeBackoffInterval" value="5"/>
    <add key="AdExtensionOfflineProcessing_OpTypeIsScheduledTask" value="true"/>

    <add key="TagIdLevelRecountJobConcurrency" value="4"/>

    <add key="AdsByBingAutoApply_OpTypeIsScheduledTask" value="false" />
    <add key="AdsByBingAutoApply_OpTypeEnabled" value="false"/>
    <add key="AdsByBingAutoApply_OpTypeBackoffInterval" value="5"/>
    <add key="AdsByBingAutoApply_CompletedJobRescheduleDelayInSec" value="86400"/>
    <add key="AdsByBingAutoApplyNotificationsEnabled" value="true"/>
    <add key="MessageManagerServiceUrl" value="https://ClientCenterMT.redmond.corp.microsoft.com:6089/messagecenter/messagemanager/mt"/>

    <add key="SharedEntityUsedByRecount_OpTypeEnabled" value="true"/>
    <add key="SharedEntityUsedByRecount_OpTypeBackoffInterval" value="5"/>

    <add key="EnableInlineUpdateAudienceSize" value="100"/>
    <add key="MigrateTaskWithCustomerMigration" value="true"/>

    <add key="AccountImageRelicenseJobConcurrency" value="1"/>
    <add key="AccountImageRelicenseDelayInSec" value="60"/>

    <add key="NegativeKeywordCatalogMigration_OpTypeEnabled" value="false"/>
    <add key="NegativeKeywordCatalogMigration_OpTypeBackoffInterval" value="5"/>
    <add key="NegativeKeywordCatalogMigrationBatchRowCount" value="1000"/>
    <add key="PauseNegativeKeywordCatalogMigration" value="false"/>
	<add key="NegativeKeywordCatalogMigration_SleepPerBatchInms" value="500"/>

    <add key="AdExEditorialStatusByAccount_OpTypeEnabled" value="true"/>
    <add key="AdExEditorialStatusByAccount_OpTypeBackoffInterval" value="5"/>
    <add key="AdExEditorialStatusByCampaign_OpTypeEnabled" value="true"/>
    <add key="AdExEditorialStatusByCampaign_OpTypeBackoffInterval" value="5"/>

    <add key="EURestrictionCountryList" value="AT,BE,DK,FI,FR,DE,IE,IT,NL,ES,SE"/>
    <add key="PauseAdGroupAudienceAssociationForEURestrictionJobConcurrency" value="4"/>
    <add key="PauseAdGroupAudienceAssociationJobConcurrency" value="4"/>
    <add key="PauseAdGroupAudienceAssociationForEURestrictionBatchSize" value="100"/>
    <add key="PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled" value="true"/>
    <add key="PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval" value="5"/>
    <add key="PauseCampaignAudienceAssociationForEURestrictionJobConcurrency" value="4"/>
    <add key="PauseCampaignudienceAssociationJobConcurrency" value="4"/>
    <add key="PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled" value="true"/>
    <add key="PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval" value="5"/>
    <add key="PauseCampaignAudienceAssociationForEURestrictionTriggerOfflineTaskBatchSize" value="100"/>

    <add key="AppextensionsMetaDataSync_OpTypeEnabled" value="true"/>
    <add key="AppextensionsMetaDataSync_OpTypeBackoffInterval" value="5"/>
    <add key="AppextensionsMetaDataSync_OpTypeIsScheduledTask" value="true"/>
    <add key="AppInstallAdsMetaDataSync_OpTypeEnabled" value="true"/>
    <add key="AppInstallAdsMetaDataSync_OpTypeBackoffInterval" value="5"/>
    <add key="AppInstallAdsMetaDataSync_OpTypeIsScheduledTask" value="true"/>
    <add key="AppMetaDataSyncSlaHours" value="48"/>
    <add key="AppNotFoundRecheckAfterHours" value="72"/>
    <add key="AppSyncJobRescheduleDelayInSec" value="86400"/>
    <add key="AIALogId" value="77034633687481"/>

    <add key="CustomerSharedEntityInvalidAssociationCleanup_OpTypeEnabled" value="true"/>
    <add key="CustomerSharedEntityInvalidAssociationCleanup_OpTypeBackoffInterval" value="5"/>
    <add key="CustomerSharedEntityInvalidAssociationCleanupJobConcurrency" value="4"/>

    <add key="PublishSmartPage_OpTypeEnabled" value="true"/>
    <add key="PublishSmartPage_OpTypeBackoffInterval" value="5"/>
    <add key="PublishSmartPageJobConcurrency" value="1"/>

    <add key="SmartPageCustomDomainDnsSetup_OpTypeEnabled" value="true"/>
    <add key="SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval" value="5"/>
    <add key="SmartPageCustomDomainDnsSetupJobConcurrency" value="1"/>

    <add key="PublishSmartPagePreview_OpTypeEnabled" value="true"/>
    <add key="PublishSmartPagePreview_OpTypeBackoffInterval" value="5"/>
    <add key="PublishSmartPagePreviewJobConcurrency" value="1"/>
    
    <add key="CreateOrUpdateDraftCampaignForSmartPage_OpTypeEnabled" value="false"/>
    <add key="CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval" value="5"/>    
    <add key="CreateOrUpdateDraftCampaignForSmartPageJobConcurrency" value="1"/>

    <add key="McaODataApi" value="http://localhost:10877/Mca/V1"/>
    <add key="McaProvisioningServiceEndpoint" value="https://provisioningservice-test.azurewebsites.net"/>
    <add key="McaProvisioningSecret" value="KeyVaultSecret:McaProvisioningSecret"/>
    <add key="ProvisioningServiceCrawlerDelay" value="10"/>
    <add key="EnableProvisioningServiceCrawlerDelay" value="true"/>

    <add key="SmartPageLeadsTracking_OpTypeEnabled" value="true"/>
    <add key="SmartPageLeadsTracking_OpTypeBackoffInterval" value="5"/>
    <add key="SmartPageLeadsTrackingJobConcurrency" value="1"/>

    <add key="SetM365FlagForSmartPage_OpTypeEnabled" value="true"/>
    <add key="SetM365FlagForSmartPage_OpTypeBackoffInterval" value="5"/>
    <add key="SetM365FlagForSmartPageJobConcurrency" value="1"/>

    <add key="FreeUpSmartPageSubdomain_OpTypeEnabled" value="true"/>
    <add key="FreeUpSmartPageSubdomain_OpTypeBackoffInterval" value="5"/>

    <add key="MarkSmartPageEditorialRejected_OpTypeEnabled" value="true"/>
    <add key="MarkSmartPageEditorialRejected_OpTypeBackoffInterval" value="5"/>
    <add key="MarkSmartPageEditorialRejectedJobConcurrency" value="1"/>

    <add key="FreeUpSmartPageCustomDomain_OpTypeEnabled" value="true"/>
    <add key="FreeUpSmartPageCustomDomain_OpTypeBackoffInterval" value="5"/>
    <add key="FreeUpSmartPageCustomDomainJobConcurrency" value="1"/>

    <add key="SmartPageUpdateESCUrl_OpTypeEnabled" value="true"/>
    <add key="SmartPageUpdateESCUrl_OpTypeBackoffInterval" value="5"/>
    <add key="SmartPageUpdateESCUrlJobConcurrency" value="1"/>

    <add key="SmartPageCustomDomainPostUpdate_OpTypeEnabled" value="true"/>
    <add key="SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval" value="5"/>
    <add key="SmartPageCustomDomainPostUpdateJobConcurrency" value="1"/>

    <add key="SmartPageCustomDomainRefresh_OpTypeEnabled" value="true"/>
    <add key="SmartPageCustomDomainRefresh_OpTypeBackoffInterval" value="5"/>
    <add key="SmartPageCustomDomainRefreshJobConcurrency" value="1"/>

    <!-- Smart Page Blob Storage Settings -->
    <add key="SmartPageContainerName" value="$web"/>
    <add key="SmartPageDeltaBackOff" value="1" />
    <add key="SmartPageMaxRetryCount" value="3" />
    <add key="SmartPageMaxExecutionTimeInSecondsForUploading" value="3600" />
    <add key="SmartPageSingleBlobUploadThresholdInBytes" value="********" />
    <add key="SmartPageBlobAccessExpiryTimeInSeconds" value="120" />
    <add key="SmartPageMaxDegreeOfParallelismForUploading" value="1" />
    <add key="SmartPageBlobStorageConnectionString" value="KeyVaultSecret:SmartPageBlobStorageConnectionString" />
    <add key="SmartPageUseManagedIdentity" value="false" />
    <add key="SmartPageBlobStorageAccountName" value="smartpagemttestci" />
    <add key="SmartPageTableStorageAccountName" value="smartpagemttestci" />

    <add key="StaToExtaAutoApply_OpTypeEnabled" value="false"/>
    <add key="StaToExtaAutoApply_OpTypeBackoffInterval" value="5"/>
    <add key="StaToExtaAutoApplyOfflineTaskEnabled" value="false" />

    <add key="AdOfflineValidation_OpTypeEnabled" value="true"/>
    <add key="AdOfflineValidation_OpTypeIsScheduledTask" value="true"/>
    <add key="AdOfflineValidation_OpTypeBackoffInterval" value="5"/>

      <add key="AudienceAdSmartCropping_OpTypeEnabled" value="true"/>
      <add key="AudienceAdSmartCropping_OpTypeIsScheduledTask" value="false"/>
      <add key="AudienceAdSmartCropping_OpTypeBackoffInterval" value="5"/>

    <add key="TextAdAssetAutoGeneration_OpTypeEnabled" value="true"/>
    <add key="TextAdAssetAutoGeneration_OpTypeIsScheduledTask" value="false"/>
    <add key="TextAdAssetAutoGeneration_OpTypeBackoffInterval" value="5"/>

    <add key="AdVideoAssetsMetadataJsonFill_OpTypeEnabled" value="true"/>
    <add key="AdVideoAssetsMetadataJsonFill_OpTypeIsScheduledTask" value="false"/>
    <add key="AdVideoAssetsMetadataJsonFill_OpTypeBackoffInterval" value="5"/>

    <add key="AssetGroupVideoAssetMetadataJsonFill_OpTypeEnabled" value="true"/>
    <add key="AssetGroupVideoAssetMetadataJsonFill_OpTypeIsScheduledTask" value="false"/>
    <add key="AssetGroupVideoAssetMetadataJsonFill_OpTypeBackoffInterval" value="5"/>

    <add key="FeedStatusSync_OpTypeEnabled" value="true"/>
    <add key="FeedStatusSync_OpTypeBackoffInterval" value="5"/>

    <add key="FeedSize_OpTypeEnabled" value="true"/>
    <add key="FeedSize_OpTypeBackoffInterval" value="5"/>

    <!-- DB AAD Auth Settings -->
    <add key="EnableDatabaseAADAuth" value="false"/>
    <add key="AADAuthDatabaseName" value=""/>

    <add key="BMCInternalAPIEndpoint" value=""/>
    <add key="BMCInternalAPIEndpointV2" value=""/>
    <add key="BMCPublicAPIEndpoint" value=""/>
    <add key="UseNewMMCEndpointForFlyerCatalog" value="false"/>
    <add key="BMCBypassAuthToken" value="KeyVaultSecret:MMCBypassAuthToken" />

    <add key="BIBscConvergencePilotCustomers" value=""/>
    <add key="IsBIBscConvergencePilotDone" value="true"/>
    <add key="IsBIBscDataMigrationInProgress" value="true"/>

    <add key="LabelMccDelete_OpTypeEnabled" value="false"/>
    <add key="LabelMccDelete_OpTypeBackoffInterval" value="5"/>
    <add key="LabelMccDeleteJobConcurrency" value="4"/>

    <add key="LabelMccEntityLevelDelete_OpTypeEnabled" value="false"/>
    <add key="LabelMccEntityLevelDelete_OpTypeBackoffInterval" value="5"/>
    <add key="LabelMccEntityLevelDeleteJobConcurrency" value="4"/>

    <add key="EnableBiddingStrategyTypeForPrivacyCheck" value="false"/>

    <add key="MultiMediaAdsAutoApply_OpTypeEnabled" value="true"/>
    <add key="MultiMediaAdsAutoApply_OpTypeIsScheduledTask" value="false"/>
    <add key="MultiMediaAdsAutoApply_CompletedJobRescheduleDelayInSec" value="43200"/>
    <add key="MultiMediaAdsAutoApplyNotificationsEnabled" value="true"/>
    <add key="MultiMediaAdsAutoApply_OpTypeBackoffInterval" value="5"/>
      
    <add key="ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled" value="false"/>
    <add key="ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeIsScheduledTask" value="true"/>
    <add key="ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec" value="259200"/>
    <add key="ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled" value="false"/>
    <add key="ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval" value="5"/>
    <add key="MultiMediaAdsNotificationAccounts" value=""/>
    <add key="CampaignIdBatchSizeForLoadingAudienceAssociation" value="500"/>

    <add key="LabelMccIdLevelRecount_OpTypeEnabled" value="false"/>
    <add key="LabelMccIdLevelRecount_OpTypeBackoffInterval" value="5"/>
    <add key="LabelMccIdLevelRecountJobConcurrency" value="4"/>

    <add key="AccountLevelLabelMccRecount_OpTypeEnabled" value="false"/>
    <add key="AccountLevelLabelMccRecount_OpTypeBackoffInterval" value="5"/>
    <add key="AccountLevelLabelMccRecountUpdateMaxCount" value="1000"/>

    <add key="LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled" value="false"/>
    <add key="LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval" value="5"/>
    <add key="LabelMccIdLevelLabelsPerEntityRecountJobConcurrency" value="4"/>
    <add key="LabelMccIdLevelLabelsPerEntityRecountUpdateMaxCount" value="1000"/>

    <add key="EnableFeedTaskInCampaignEntityLibraryShard" value="true"/>

    <add key="MultiMediaAdsToTextAssets_OpTypeEnabled" value="false"/>
    <add key="MultiMediaAdsToTextAssets_OpTypeBackoffInterval" value="5"/>

    <add key="EnableTroubleshootingLogsForCampaignDailyCap" value="false"/>
    <add key="EnableOpcUpdateCallPerMainShard" value="true"/>
    <add key="EnableBudgetSyncV3" value="false"/>

    <add key="RSACustomizereOfflineDelete_OpTypeEnabled" value="true"/>
    <add key="RSACustomizereOfflineDelete_OpTypeBackoffInterval" value="5"/>
    <add key="EnableApplyRecBulkForMMAAutoApply" value="false"/>
	<add key="EnableAdInsightsApplyPilotForMMAAutoApply" value="true"/>

    <add key="TextAssetCountsAndLimitCheck_OpTypeEnabled" value="true"/>
    <add key="TextAssetCountsAndLimitCheck_OpTypeBackoffInterval" value="5"/>

    <add key="TextAssetBlobRefresh_OpTypeIsScheduledTask" value="true"/>
    <add key="TextAssetBlobRefresh_OpTypeEnabled" value="true"/>
    <add key="TextAssetBlobRefresh_OpTypeBackoffInterval" value="5"/>  
    <add key="TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec" value="86400"/>
    <add key="TextAssetBlobRefresh_OperationTypeRunInCI" value="false"/>
    <add key="ShardedTextAssetsNumThreshold" value="1000000"/>

    <add key="AdOfflineAdd_OpTypeEnabled" value="true"/>
    <add key="AdOfflineAdd_OperationTypeRunInCIOrSI" value="false"/>
    
    <add key="ImageBulkCropping_OpTypeEnabled" value="false"/>
    <add key="ImageBulkCropping_OpTypeBackoffInterval" value="5"/>
    <add key="ImageBulkCropping_OpTypeIsScheduledTask" value="true"/>
    <add key="ImageBulkCroppingDelayInSeconds" value="5"/>
    <add key="ImageBulkCroppingDevToken" value="KeyVaultSecret:ImageBulkCroppingDevToken"/>
    <add key="EnableImageBulkCroppingBulkUploadV2" value="true"/>
    <add key="BulkUploadInputFileContainerName" value="bulkuploadinputfiles"/>
    <add key="BulkUploadThrottleRequestsLimit" value="300" />
    <add key="BulkUploadThrottleTimeIntervalInMinutes" value="15" />

    <add key="EnablePredictiveTargeting_OpTypeEnabled" value="true"/>
    <add key="EnablePredictiveTargeting_OpTypeBackoffInterval" value="5"/>
    <add key="DisablePredictiveTargeting_OpTypeEnabled" value="true"/>
    <add key="DisablePredictiveTargeting_OpTypeBackoffInterval" value="5"/>

	  <!-- HotelTreeNodesTasks -->
	<add key="HotelTreeNotificationTask_Enabled" value="true"/>
	<add key="HotelTreeMapTask_Enabled" value="true"/>
  <add key="HotelTreeNotificationClickhouseTask_Enabled" value="true"/>
  <add key="HotelTreeMapClickhouseTask_Enabled" value="true"/>
    <add key="HotelTreeMapClickhouseTaskFetchSize" value="2"/>
	<add key="HotelTreeNodesTasksFailedJobInitialRetryIntervalMillis" value="1000"/>
	<add key="HotelTreeNodesTasksFailedJobMaxRetryIntervalSecs" value="120"/>
	<add key="HotelTreeNodesTasksMaxDequeueConcurrency" value="10"/>
	<add key="HotelTreeNodesTasksMaxInMemoryQueueSize" value="100"/>
	<add key="HotelTreeNotificationTaskFetchSize" value="2"/>
	<add key="HotelTreeNotificationTaskDBLockUpIntervalSecs" value="600"/>
  <add key="HotelTreeNotificationTaskParallelTasksLimit" value="4"/>
  <add key="HotelTreeNotificationTaskMaxRetryCount" value="5"/>
  <add key="HotelTreeNotificationTaskProcessBatchSize" value="250000"/>
    <add key="HotelTreeTaskThrottlerBackoffSeconds" value="30"/>

    <add key="HotelTreeMapClickhouseTask_DynamicInstances" value="4"/>
    <add key="HotelTreeMapClickhouseTask_ParallelFetchEnabled" value="true"/>
    
    <!-- Offer Clickhouse tasks -->
    <add key="OfferTreeMapClickhouseTask_Enabled" value="true"/>
    <add key="TreeNotificationClickhouseTask_Enabled" value="true"/>
    <add key="OfferTreeMapTasksMaxDequeueConcurrency" value="10"/> <!-- Old service infra seems to only run 1 of each task type at a time -->
    <add key="OfferTreeMapClickhouseTaskFetchSize" value="20"/>
    <add key="OfferTreeNotificationTaskMaxRetryCount" value="5"/>
    <add key="OfferTreeNotificationTaskParallelTasksLimit" value="80"/>
    <add key="OfferTreeNotificationTaskProcessBatchSize" value="1000000"/>
    <add key="OfferTreeMapTasksMaxInMemoryQueueSize" value="100"/>
    <add key="OfferTreeTaskThrottlerBackoffSeconds" value="30"/>

      <!-- Offer sql tasks -->
    <add key="OfferTreeMapTask_Enabled" value="true"/>
    <add key="TreeNotificationTask_Enabled" value="true"/>

    <add key="OfferTreeMapClickhouseTask_DynamicInstances" value="4"/>
    <add key="OfferTreeMapClickhouseTask_ParallelFetchEnabled" value="true"/>

    <add key="ClickhouseBlobConnectionString" value="UseDevelopmentStorage=true"/>
    <add key="ClickhouseBlobKeyVaultName" value="BIDataMartsKVMTSI"/>
    <add key="ClickhouseBlobContainerName" value="mapofferservice"/>
    <add key="ClickhouseBlobUseMIAuth" value="false"/>
    <add key="ClickhouseBlobMI" value=""/>
    <add key="ClickhouseBlobURI" value="http://127.0.0.1:10000/devstoreaccount1/"/>
    <add key="ClickhouseQuorumCount" value="1"/>
    <add key="ClickhouseBlobConfigConnectionString" value="UseLocalAuth=true"/>
    <add key="ClickhouseCsvStreamMemLimitationMB" value="2"/>
    <add key="ClickhouseFetchTaskAcquireLockRetryCnt" value="1"/>
    <add key="ClickhouseCsvStreamBatchSize" value="10000"/>
    <add key="ClickhouseTaskInsertCsv" value="true"/>
    <add key="ClickhouseTaskNewRequestId" value="true"/>
    <add key="ClickhouseTaskPersistLockOnAdGroup" value="true"/>
    <add key="ClickhouseTreeHierarchyMapParallelInsert" value="true"/>
    <add key="ClickhouseTaskPersistLockMod" value="20"/>
    <add key="ClickhouseTaskMaxProcessingMinutes" value="20"/>

    <add key="MigrateMMAUnderDSAAdGroup_OpTypeEnabled" value="true"/>
    <add key="MigrateMMAUnderDSAAdGroup_OpTypeBackoffInterval" value="5"/>

    <add key="AccountPilotMigrationProcessor_OpTypeEnabled" value="true"/>
    <add key="AccountPilotMigrationProcessor_OpTypeBackoffInterval" value="5"/>

    <add key="AssetGroupEditorialRollup_OpTypeEnabled" value="true"/>
    <add key="AssetGroupEditorialRollup_OpTypeBackoffInterval" value="5"/>
  
    <add key="AssetGroupAssetEditorialStatus_OpTypeEnabled" value="true"/>
    <add key="AssetGroupAssetEditorialStatus_OpTypeBackoffInterval" value="5"/>

	<add key="SmartShoppingToPerformanceMaxUpgrade_OpTypeEnabled" value="true"/>
	<add key="SmartShoppingToPerformanceMaxUpgrade_OpTypeBackoffInterval" value="5"/>

  	<add key="AccountLevelImageAnnotation_OpTypeEnabled" value="true"/>
    <add key="AccountLevelImageAnnotation_OpTypeIsScheduledTask" value="true"/>
    <add key="AccountLevelImageAnnotation_OpTypeBackoffInterval" value="5"/>

    <add key="AssetLevelImageAnnotation_OpTypeEnabled" value="true"/>
    <add key="AssetLevelImageAnnotation_OpTypeIsScheduledTask" value="true"/>
    <add key="AssetLevelImageAnnotation_OpTypeBackoffInterval" value="5"/>

    <add key="CroppingTypeForImageAdExtensionBackfill_OpTypeEnabled" value="true"/>
    <add key="CroppingTypeForImageAdExtensionBackfill_OpTypeIsScheduledTask" value="true"/>
    <add key="CroppingTypeForImageAdExtensionBackfill_OpTypeBackoffInterval" value="5"/>

    <!-- BingPlacesMSATask-->  
    <add key="BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled" value="true"/>
    <add key="BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval" value="5"/>
    <add key="BingPlacesRetryPolicy" value="maxCount:3,initialInterval:2,maxInterval:30"/>
    <add key="BingPlacesMSASignUpClientId" value="microsoft_ads" />
    <add key="BingPlacesMSASignup" value ="true" />
    <add key="BingPlacesMSAKeyVaultName" value="MSABPSI-KV" />
    <add key="BingPlacesMSAKeyVaultRunAs" value="RunAs=Developer; DeveloperTool=AzureCli" />
    <add key="BingPlacesApiRootUrl" value ="https://bptestwebsite.cloudapp.net/api/bp" />
    
    <!-- Clickhouse -->
    <add key="ClickhousePortNumber" value="9123"/>
    <add key="ClickhouseProtocol" value="http"/>

    <!-- MCA -->
    <add key="PauseAllMcaCampaigns_OpTypeEnabled" value="true"/>
    <add key="PauseAllMcaCampaigns_OpTypeBackoffInterval" value="5"/>
    <add key="PauseAllMcaCampaignsJobConcurrency" value="10"/>
    <add key="PauseAllMcaCampaigns_OpTypeMaxRetryCount" value="3"/>
    <add key="ESCAccountMigration_OpTypeEnabled" value="true"/>
    <add key="ESCAccountMigration_OpTypeBackoffInterval" value="5"/>
    <add key="ESCAccountMigrationJobConcurrency" value="10"/>
    <add key="ESCAccountMigration_OpTypeMaxRetryCount" value="3"/>
    <add key="ESCAccountMigrationJobTimeoutInSeconds" value="120"/>
      
    <add key="VideoDataBackfill_OpTypeEnabled" value="true"/>
    <add key="VideoDataBackfill_OpTypeBackoffInterval" value="5"/>
    <add key="VideoDataBackfillJobConcurrency" value="10"/>
    <add key="VideoDataBackfill_OpTypeMaxRetryCount" value="3"/>
    <add key="VideoDataBackfillJobTimeoutInSeconds" value="120"/>

    <add key="DSAToPMaxIDMappingProcessor_OpTypeEnabled" value="true"/>
    <add key="DSAToPMaxIDMappingProcessor_OpTypeBackoffInterval" value="5"/>
      
    <add key="SSOBlockingMigration_OpTypeIsScheduledTask" value="true"/>
    <add key="SSOBlockingMigration_OpTypeEnabled" value="false"/>
    <add key="SSOBlockingMigration_OpTypeBackoffInterval" value="5"/>
    <add key="SSOBlockingMigrationMaxRunTime" value="00:00:30"/>
    <add key="SSOBlockingMigration_CompletedJobRescheduleDelayInSec" value ="2592000" />

    <add key="DSAToPMaxMigrationProcessor_OpTypeEnabled" value="true"/>
    <add key="DSAToPMaxMigrationProcessor_OpTypeBackoffInterval" value="5"/>
    <add key="DSAToPMaxMigrationProcessor_OpTypeIsScheduledTask" value="false"/>
    <add key="DSAToPMaxMigrationTest" value="false"/>
    <add key="DSAToPMaxMigrationJobDelaySeconds" value="0"/>
    <add key="DSAToPMaxMigrationJobRescheduleDelaySeconds" value="0"/>
    <add key="DSAToPMaxMigrationAggServiceCallWaitSeconds" value="0"/>

    <add key="UpdateAIGCSearchIndexProcessor_OpTypeEnabled" value="true"/>
    <add key="UpdateAIGCSearchIndexProcessor_OpTypeIsScheduledTask" value="false"/>
    <add key="UpdateAIGCSearchIndexProcessor_OpTypeBackoffInterval" value="5"/>
    
    <add key="VideoClipchampConversionProcessor_OpTypeEnabled" value="true"/>
    <add key="VideoClipchampConversionProcessor_OpTypeIsScheduledTask" value="false"/>
    <add key="VideoClipchampConversionProcessor_OpTypeBackoffInterval" value="5"/>

      <add key="AccountsActiveProcessor_OpTypeEnabled" value="true"/>
      <add key="AccountsActiveProcessor_OpTypeIsScheduledTask" value="false"/>
      <add key="AccountsActiveProcessor_OpTypeBackoffInterval" value="5"/>

    <add key="LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled" value="false"/>
    <add key="LogoAdExtensionAutoGenerationProcessor_OpTypeBackoffInterval" value="5"/>
    <add key="LogoAdExtensionAutoGenerationProcessorMaxRunTime" value="00:00:30"/>
    <add key="LogoAdExtensionAutoGenerationProcessorMaxDaysForMonitoring" value = "180"/>
    <add key="LogoAdExtensionAutoGenerationProcessorDailyScheduleStartOffset" value = "01.00:00:00.000"/>
    <add key="LogoAdExtensionAutoGenerationProcessorUseRandomDailySchedule" value = "false"/>
    <add key="LogoAdExtensionAutoGenerationProcessorRandomDailyScheduleWindow" value = "04:00:00.000"/>

    <!-- Import -->
    <add key="UpdateExternalCampaignIdMapDetailByTaskStatusConcurrency" value="1"/>
    <add key="UpdateExternalCampaignIdMapDetailByTaskStatus_OpTypeEnabled" value="true"/>
    <add key="UpdateExternalCampaignIdMapDetailByTaskStatus_OpTypeIsScheduledTask" value="false"/>
    <add key="UpdateExternalCampaignIdMapDetailByTaskStatus_OpTypeBackoffInterval" value="5"/>
  
    <add key="CCPilotPropagation_OpTypeEnabled" value="false"/>
  </appSettings>

  <!--UseDeltaLoadForAdvertiserCustomerCacheAfterInitialLoad-->
  <?ap-config target="/configuration/appSettings/add[@key='UseDeltaLoadForAdvertiserCustomerCacheAfterInitialLoad']/@value" value="false" when="@environment='Redmond-CI'" ?>

  <!-- UseBinarySearchToFindShardPartition -->
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseBinarySearchToFindShardPartition']/@value" value="false" when="@environment='Redmond-CI'" ?>

  <!-- UseDataReaderAndDeltaLoadShardMap -->
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseDataReaderAndDeltaLoadShardMap']/@value" value="false" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApplyOfflineTaskEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StaToExtaAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LicenseStockImage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <!-- ShardedDeltaLoadDbTypes -->
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedDeltaLoadDbTypes']/@value" value="CampaignAdGroupShard,CampaignDB,CampaignNegativeKeywordShard,VerticalDB" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="true" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="true" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetrieveEncryptionCertificateSettingsFromDatabase']/@value" value="false" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="true" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="true" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B' and @aks='true'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsQueueRuntimeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNonCacheFlowInOfferTreeMapTask']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="0" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="0" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RetryBaseDelayMillis']/@value" value="0" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="900" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskIdlePollIntervalInSeconds']/@value" value="10" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="10" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="30" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IdleWaitIntervalInSeconds']/@value" value="10" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferDbPartitionInfoCacheAgeInMinutes']/@value" value="30" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MaxBackOffIntervalInSeconds']/@value" value="259200" when="@environment='Redmond-CI'" ?>

  <!--OfferTaskRetryWaitInMinutes -->
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTaskRetryWaitInMinutes']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="0" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfflineTaskDurationBetweenExecutions']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTaskDurationBetweenExecutions']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnabledShardPartitions']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdgroupShardingOfflineTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='KeywordLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AllEntityLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderAudienceLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAAutoTargetLimit_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'"  ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignActive_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcCampaignId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OpcOrderId_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SitelinksAdExtensionMigrationAdExOrderEditorialStatus_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <!-- CampaignExpanding generic options -->
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_PickUpBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="4" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="4" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="4" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="4" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignExpanding_DBPersistenceBatchSize']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <!-- NonShardedDbTypesUsingShardedHistogram -->
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NonShardedDbTypesUsingShardedHistogram']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SkipPerformanceMethodEnterForMapOffersTreeNodeService']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PerformanceLoggingThresholdTimeSpanForMapOffersTreeNodeService']/@value" value="00:00:00.010" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <!-- AccountDeleteForPurge -->
  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountDeleteForPurge_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelExecuteBSCDBSqlDataReader']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BuildCustomerIdCacheForNKWMigration']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordLimit_OpTypeBackoffInterval']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdexOrderEditorialExpending_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimCampaignLevelPrivacyCheck_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="10" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AimOrderLevelPrivacyCheck_OpTypeMaxRetryCount']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="3600" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckPendingJobRescheduleDelayInSec']/@value" value="10800" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PrivacyCheckCompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBiddingStrategyTypeForPrivacyCheck']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FlipAuthenticatedAccount']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://localhost:10875/AdIntelligenceMTMockService.svc" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://localhost:1901/AdIntelligenceMTMockService.svc" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-offline.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://appsdevdev.redmond.corp.microsoft.com:823/AdInsightMT/V2/Odata" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightODataEndpointUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/Odata" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightWaitTimeBetweenPollInSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdInsightMaxPollCount']/@value" value="150" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://messagectr.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://simessagecenter.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://simessagecenter.trafficmanager.net:6089/messagecenter/messagemanager/mt" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MessageManagerServiceUrl']/@value" value="https://ClientCenterMT.redmond.corp.microsoft.com:6089/messagecenter/messagemanager/mt" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceIdLevelRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceDelete_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDevToken']/@value" value="KeyVaultSecret:ImageBulkCroppingDevToken" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCropping_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageBulkCroppingDelayInSeconds']/@value" value="30" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByCustomerProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountGoalRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountRemarketingListRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TagUsedByAccountProductAudienceRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelAudienceUsedbyAdGroupRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SharedEntityUsedByRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^http(s)?:\/\/(.+):(\d+)\/dam.mock\/(?&lt;imageKey&gt;.+)" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^http(s)?:\/\/(.+):(\d+)\/dam.mock\/(?&lt;imageKey&gt;.+)" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing-exp\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing-exp\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ThumbnailUrlRegEx']/@value" value="^https:\/\/www\.bing\.com\/th\?id=OADD2\.(?&lt;imageKey&gt;.+)&amp;pid=21\.2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageAspectMigrationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="168985221" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TestCustomerForImageMigration']/@value" value="168985221" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="100" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationWaitPerBatchMillisec']/@value" value="2000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ImageMigrationBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='StockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NewStockImageRekey_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetLevelImageAnnotation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdImpressionTrackingUrl_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OrderEditorialStatusQueueProcessor_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CampaignLevelOrderTargetSizeRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseSharedLibraryEntityAssociations_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseGoalForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAudienceAssociationForAccount_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAdGroupAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseCampaignAudienceAssociationForEURestriction_OpTypeBackoffInterval']/@value" value="180" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AudienceLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelCampaignAudienceAssociationUsedByRecountEnable']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdsByBingAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>   
    
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsAutoApply_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="20" when="@environment='Redmond-CI'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAsset_CompletedJobRescheduleDelayInSec']/@value" value="259200" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='ResponsiveSearchAdsOptInAutoApplyAutoGenAssetNotificationsEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccoutImageReLicense_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogMigration_OpTypeEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdExEditorialStatusByAccount_OpTypeBackoffInterval']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppextensionsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AppInstallAdsMetaDataSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainDnsSetup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CreateOrUpdateDraftCampaignForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageLeadsTracking_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SetM365FlagForSmartPage_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUpdateESCUrl_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainPostUpdate_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageCustomDomainRefresh_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="http://localhost:10877/Mca/V1" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="http://localhost:18080/ODataApi/Mca/V1" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://tip.ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaODataApi']/@value" value="https://tip.ui.ads.microsoft.com/ODataApi/Mca/V1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-test.azurewebsites.net" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-test.azurewebsites.net" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-test.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-test.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='McaProvisioningServiceEndpoint']/@value" value="https://provisioningservice-prod.azurewebsites.net" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="10" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="10" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ProvisioningServiceCrawlerDelay']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FreeUpSmartPageSubdomain_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MarkSmartPageEditorialRejected_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageUseManagedIdentity']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpagetest" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="smartpagemttestci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpagetest" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpagetest" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageBlobStorageAccountName']/@value" value="bingadssmartpage" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemttest" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemttestci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemttest" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemttest" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SmartPageTableStorageAccountName']/@value" value="smartpagemt" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableDatabaseAADAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="Campaign_MainShard_P101~P102;Campaign_MainShard_P104~P106;Campaign_MainShard_P110~P111;Campaign_AdGroupShard_P1001;Campaign_AdGroupShard_P1065~P1080;Campaign_AdGroupShard_P1085~P1100;VerticalDB_P1~P999;Campaign_LibraryShard_P201~P209;AdvertiserBIBSC_P1~P16;AdvertiserBI_P1~P16;AdvertiserBIAC_P1~P3;AuditHistoryBI_P1~P4;AdCenter_CampaignBulkDB_P1~P6;AdCenter_Metadata;Adcenter_Billing_P1~P9999;BscOfferDB;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="Campaign_MainShard_P101~P102;Campaign_MainShard_P104~P106;Campaign_MainShard_P110~P111;Campaign_AdGroupShard_P1001;Campaign_AdGroupShard_P1065~P1080;Campaign_AdGroupShard_P1085~P1100;VerticalDB_P1~P999;Campaign_LibraryShard_P201~P209;AdvertiserBIBSC_P1~P16;AdvertiserBI_P1~P16;AdvertiserBIAC_P1~P3;AuditHistoryBI_P1~P4;AdCenter_CampaignBulkDB_P1~P6;AdCenter_Metadata;Adcenter_Billing_P1~P9999;BscOfferDB;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;AuditHistoryBI_P1~P999;AdvertiserBIBSC_P1~P999;AdvertiserBIAC_P1~P999;VerticalDB_P1~P999;Campaign_LibraryShard_P1~P999;AdCenter_CampaignBulkDB_P1~P999;AdvertiserBI_P1~P999;BIElasticQuery;AdCenter_Metadata;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;AuditHistoryBI_P1~P999;AdvertiserBIBSC_P1~P999;AdvertiserBIAC_P1~P999;VerticalDB_P1~P999;Campaign_LibraryShard_P1~P999;AdCenter_CampaignBulkDB_P1~P999;AdvertiserBI_P1~P999;BIElasticQuery;AdCenter_Metadata;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="AuditHistoryBI_P1~P36;AdvertiserBIBSC_P1~P16;AdvertiserBIAC_P1~P16;Campaign_LibraryShard_P1~P999;AdvertiserBI_P1~P999;AdCenter_CampaignBulkDB_P1~P999;Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;VerticalDB_P1~P999;AdCenter_Metadata;BIElasticQuery;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="AuditHistoryBI_P1~P36;AdvertiserBIBSC_P1~P16;AdvertiserBIAC_P1~P16;Campaign_LibraryShard_P1~P999;AdvertiserBI_P1~P999;AdCenter_CampaignBulkDB_P1~P999;Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;VerticalDB_P1~P999;AdCenter_Metadata;BIElasticQuery;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="AuditHistoryBI_P1~P36;AdvertiserBIBSC_P1~P16;AdvertiserBIAC_P1~P16;Campaign_LibraryShard_P1~P999;AdvertiserBI_P1~P999;AdCenter_CampaignBulkDB_P1~P999;Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;VerticalDB_P1~P999;AdCenter_Metadata;BIElasticQuery;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AADAuthDatabaseName']/@value" value="AuditHistoryBI_P1~P36;AdvertiserBIBSC_P1~P16;AdvertiserBIAC_P1~P16;Campaign_LibraryShard_P1~P999;AdvertiserBI_P1~P999;AdCenter_CampaignBulkDB_P1~P999;Campaign_AdGroupShard_P1~P9999;Campaign_MainShard_P1~P999;VerticalDB_P1~P999;AdCenter_Metadata;BIElasticQuery;Adcenter_Billing_P1~P9999;BscOfferDB_P0~P9999;AdsPubCenter_PS_P1~P4;" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="http://localhost:10875/dam.mock" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="http://localhost:1901/dam.mock" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoArmEndpoint']/@value" value="https://management.azure.com" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDownload_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoDeleteCleanup_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoAdaptiveStreamingTranscode_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://bmcapi-int.trafficmanager.net" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://bmcapi-int.trafficmanager.net" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpoint']/@value" value="http://api-private.merchantcenter.bingads.glbdns2.microsoft.com" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads-int.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads-int.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCInternalAPIEndpointV2']/@value" value="https://mmcapi.ads.microsoft.com/api/v1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UseNewMMCEndpointForFlyerCatalog']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://intsu1.content.api.sandbox.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://intsu1.content.api.sandbox.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BMCPublicAPIEndpoint']/@value" value="https://content.api.bingads.microsoft.com/shopping/v9.1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_DynamicInstances']/@value" value="50" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_DynamicInstances']/@value" value="80" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_DynamicInstances']/@value" value="20" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_DynamicInstances']/@value" value="20" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsNotificationAccounts']/@value" value="-1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineValidation_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccEntityLevelDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AccountLevelLabelMccRecount_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LabelMccIdLevelLabelsPerEntityRecount_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedStatusSync_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='FeedSize_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BIBscConvergencePilotCustomers']/@value" value="256234972" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BIBscConvergencePilotCustomers']/@value" value="256234972" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BIBscConvergencePilotCustomers']/@value" value="256234972" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscConvergencePilotDone']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscDataMigrationInProgress']/@value" value="True" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscDataMigrationInProgress']/@value" value="True" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='IsBIBscDataMigrationInProgress']/@value" value="True" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableFeedTaskInCampaignEntityLibraryShard']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppMetaDataSyncSlaHours']/@value" value="0.01" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AppNotFoundRecheckAfterHours']/@value" value="0.01" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='MultiMediaAdsToTextAssets_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableParallelFetch']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="5" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PublishSmartPagePreview_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='RSACustomizereOfflineDelete_OpTypeBackoffInterval']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableBudgetSyncV3']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='EnableApplyRecBulkForMMAAutoApply']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="120" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_CompletedJobRescheduleDelayInSec']/@value" value="86400" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="10" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ShardedTextAssetsNumThreshold']/@value" value="1000000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TextAssetBlobRefresh_OperationTypeRunInCI']/@value" value="true" when="@environment='Redmond-CI'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineAdd_OperationTypeRunInCIOrSI']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineAdd_OperationTypeRunInCIOrSI']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AdOfflineAdd_OperationTypeRunInCIOrSI']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AssetGroupEditorialRollup_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://bptestwebsite.cloudapp.net/api/bp" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://bptestwebsite.cloudapp.net/api/bp" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://bptestwebsite.cloudapp.net/api/bp" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://bptestwebsite.cloudapp.net/api/bp" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesApiRootUrl']/@value" value="https://www.bingplaces.com/api/bp" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="true" when="@environment='Local'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="true" when="@environment='Redmond-CI'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationTest']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="0" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="0" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobDelaySeconds']/@value" value="600" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="15" when="@environment='Local'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="15" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="15" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="15" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationJobRescheduleDelaySeconds']/@value" value="3600" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="0" when="@environment='Local'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="0" when="@environment='Redmond-CI'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
    <?ap-config target="/configuration/appSettings/add[@key='DSAToPMaxMigrationAggServiceCallWaitSeconds']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>

    <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPSI-KV" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPSI-KV" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultName']/@value" value="MSABPPROD-KV" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=644646e3-7089-4e32-b417-24edfa15f2f9" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=644646e3-7089-4e32-b417-24edfa15f2f9" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesMSAKeyVaultRunAs']/@value" value="RunAs=App;AppId=11cf2fcc-99c8-43e2-b99a-6e9bb129abea" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='BingPlacesCreateOrClaimBusinessListing_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="30" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="30" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeTaskThrottlerBackoffSeconds']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="9123" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhousePortNumber']/@value" value="8443" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="http" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseProtocol']/@value" value="https" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapClickhouseTask_Enabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobUseMIAuth']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobMI']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://advbichdatastorageci.blob.core.windows.net/" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://advbichdatastorageci.blob.core.windows.net/" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferservice.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferservice.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobURI']/@value" value="https://mapofferserviceprod.blob.core.windows.net/" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseQuorumCount']/@value" value="2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="UseDevelopmentStorage=true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConnectionString']/@value" value="KeyVaultSecret:ClickhouseMapOfferServiceBlobConnectionString" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="UseLocalAuth=true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ClientId=cf627068-a4da-44b7-9d67-c9fee3ce6653;TenantId=72f988bf-86f1-41af-91ab-2d7cd011db47;ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ClickhouseBlobConfigConnectionString']/@value" value="ManagedIdentityId=45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='PauseAllMcaCampaigns_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="2" when="@environment='devdev'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='ESCAccountMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="2" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="5" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_OpTypeBackoffInterval']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="20" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='SSOBlockingMigration_CompletedJobRescheduleDelayInSec']/@value" value="2592000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorMaxDaysForMonitoring']/@value" value="4" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorMaxDaysForMonitoring']/@value" value="4" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorDailyScheduleStartOffset']/@value" value="00:00:00.000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorDailyScheduleStartOffset']/@value" value="00:00:00.000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorUseRandomDailySchedule']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='LogoAdExtensionAutoGenerationProcessorUseRandomDailySchedule']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMILocal" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMICI"  when="@environment='Redmond-CI' and @tenant='CORP'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionString" when="@environment='Redmond-CI' and @tenant='PME'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='UpdateAIGCSearchIndexProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='VideoClipchampConversionProcessor_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>    
    
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='CCPilotPropagation_OpTypeEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>


  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='HotelTreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='OfferTreeMapTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
  <?ap-config target="/configuration/appSettings/add[@key='TreeNotificationTask_Enabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>

</configuration>
