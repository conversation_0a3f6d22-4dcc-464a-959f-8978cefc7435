using System;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public partial class ColumnMap
    {
        public static Dictionary<string, string> ClickhouseDataColumns = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "aimreachmodifieddtim","AIMReachModifiedDTim" },
            { "aimreachsize","AIMReachSize" },
            { "aimtargetversion","AIMTargetVersion" },
            { "absolutetopimpressioncnt","AbsoluteTopImpressionCnt" },
            { "accountexclusionid","AccountExclusionId" },
            { "accountid","AccountId" },
            { "accountinfo","AccountInfo" },
            { "accountname","AccountName" },
            { "accountnumber","AccountNumber" },
            { "accountpropertyid","AccountPropertyId" },
            { "accountpropertytypeid","AccountPropertyTypeId" },
            { "accountstatusname","AccountStatusName" },
            { "accounttypeid","AccountTypeId" },
            { "actiondate","ActionDate" },
            { "actionflag","ActionFlag" },
            { "actiontype","ActionType" },
            { "adassetassociationtypeid","AdAssetAssociationTypeId" },
            { "adassetid","AdAssetId" },
            { "adassetpintypeid","AdAssetPinTypeId" },
            { "adblockid","AdBlockId" },
            { "adblockname","AdBlockName" },
            { "adclickcnt","AdClickCnt" },
            { "addataid","AdDataId" },
            { "addatapropertyid","AdDataPropertyId" },
            { "addatapropertyname","AdDataPropertyName" },
            { "addatapropertyvalue","AdDataPropertyValue" },
            { "addescription","AdDescription" },
            { "addescription2","AdDescription2" },
            { "adextimpressioncnt","AdExtImpressionCnt" },
            { "adextensiongroupid","AdExtensionGroupId" },
            { "adextensionid","AdExtensionId" },
            { "adextensionitemid","AdExtensionItemId" },
            { "adextensionitemlineitemid","AdExtensionItemLineItemId" },
            { "adextensionlineitemid","AdExtensionLineItemId" },
            { "adextensionname","AdExtensionName" },
            { "adextensionpropertyid","AdExtensionPropertyId" },
            { "adextensionpropertyvalue","AdExtensionPropertyValue" },
            { "adextensiontypeattributebitvalue","AdExtensionTypeAttributeBitValue" },
            { "adextensiontypeattributename","AdExtensionTypeAttributeName" },
            { "adextensiontypeid","AdExtensionTypeId" },
            { "adextensiontypename","AdExtensionTypeName" },
            { "adextensionversion","AdExtensionVersion" },
            { "adextensionversionedid","AdExtensionVersionedId" },
            { "adgroupid","AdGroupId" },
            { "adgroupstatusname","AdGroupStatusName" },
            { "adgrouptypeid","AdGroupTypeId" },
            { "adid","AdId" },
            { "adimpressioncnt","AdImpressionCnt" },
            { "adlandingpageurlid","AdLandingPageUrlId" },
            { "adlanguage","AdLanguage" },
            { "adlayoutid","AdLayoutId" },
            { "admetrixcountryid","AdMetrixCountryId" },
            { "adname","AdName" },
            { "adposition","AdPosition" },
            { "adscenariotype","AdScenarioType" },
            { "adscheduleusesearchertimezone","AdScheduleUseSearcherTimeZone" },
            { "adslot","AdSlot" },
            { "adslotid","AdSlotId" },
            { "adstrengthjson","AdStrengthJSON" },
            { "adtitle","AdTitle" },
            { "adtitle1","AdTitle1" },
            { "adtitle2","AdTitle2" },
            { "adtitle3","AdTitle3" },
            { "adtypeid","AdTypeId" },
            { "adtypename","AdTypeName" },
            { "adunitid","AdUnitId" },
            { "adddeletetrackenable","AddDeleteTrackEnable" },
            { "additionalvalue","AdditionalValue" },
            { "addressid","AddressId" },
            { "addressline1","AddressLine1" },
            { "addressline2","AddressLine2" },
            { "adjustmenttype","AdjustmentType" },
            { "adminregioncode","AdminRegionCode" },
            { "advancedbookingwindow","AdvancedBookingWindow" },
            { "advertiserclicks","AdvertiserClicks" },
            { "advertisercustomerid","AdvertiserCustomerId" },
            { "advertiserhotelid","AdvertiserHotelId" },
            { "advertiserid","AdvertiserId" },
            { "advertiserreportedrevenue","AdvertiserReportedRevenue" },
            { "advertiserreportedrevenueadjustment","AdvertiserReportedRevenueAdjustment" },
            { "advertiserverticalitemid","AdvertiserVerticalItemId" },
            { "advertisingchanneltypeid","AdvertisingChannelTypeId" },
            { "advertisingsubchanneltypeid","AdvertisingSubChannelTypeId" },
            { "agebucketid","AgeBucketId" },
            { "agencycommissionpct","AgencyCommissionPct" },
            { "agencycontactname","AgencyContactName" },
            { "agencycustomerid","AgencyCustomerId" },
            { "alexid","AlexId" },
            { "allconversioncnt","AllConversionCnt" },
            { "allconversioncredit","AllConversionCredit" },
            { "allowmultipleassociations","AllowMultipleAssociations" },
            { "analyticsoptinid","AnalyticsOptInId" },
            { "answer","Answer" },
            { "appbundle","AppBundle" },
            { "appname","AppName" },
            { "appstoreurl","AppStoreUrl" },
            { "applicationid","ApplicationId" },
            { "archivedtim","ArchiveDTim" },
            { "archiveddtim","ArchivedDTim" },
            { "areacode","AreaCode" },
            { "assetassociationtypeid","AssetAssociationTypeId" },
            { "assetbasedentityassociationtypeid","AssetBasedEntityAssociationTypeId" },
            { "assetgroupdataid","AssetGroupDataId" },
            { "assetgroupdatapropertyid","AssetGroupDataPropertyId" },
            { "assetgroupdatapropertyvalue","AssetGroupDataPropertyValue" },
            { "assetgroupid","AssetGroupId" },
            { "assetgroupsearchtheme","AssetGroupSearchTheme" },
            { "assetgroupsearchthemeid","AssetGroupSearchThemeId" },
            { "assetgroupstatusname","AssetGroupStatusName" },
            { "assetid","AssetId" },
            { "assetschemaid","AssetSchemaId" },
            { "assetschemaname","AssetSchemaName" },
            { "assetschematypeid","AssetSchemaTypeId" },
            { "assetschemaversionid","AssetSchemaVersionId" },
            { "assettext","AssetText" },
            { "assistcnt","AssistCnt" },
            { "assistcount","AssistCount" },
            { "associationid","AssociationId" },
            { "attribute","Attribute" },
            { "attribute2","Attribute2" },
            { "attributedescription","AttributeDescription" },
            { "attributeid","AttributeId" },
            { "attributename","AttributeName" },
            { "attributevaluesjson","AttributeValuesJSON" },
            { "auctionlosttobudgetabstopcnt","AuctionLostToBudgetAbsTopCnt" },
            { "auctionlosttobudgetcnt","AuctionLostToBudgetCnt" },
            { "auctionlosttobudgettopcnt","AuctionLostToBudgetTopCnt" },
            { "auctionlosttolandpagerelevancecnt","AuctionLostToLandPageRelevanceCnt" },
            { "auctionlosttolowadqualitycnt","AuctionLostToLowAdQualityCnt" },
            { "auctionlosttominbidcnt","AuctionLostToMinBidCnt" },
            { "auctionlosttoothercnt","AuctionLostToOtherCnt" },
            { "auctionlosttorankabstopcnt","AuctionLostToRankAbsTopCnt" },
            { "auctionlosttorankaggcnt","AuctionLostToRankAggCnt" },
            { "auctionlosttorankcnt","AuctionLostToRankCnt" },
            { "auctionlosttoranktopcnt","AuctionLostToRankTopCnt" },
            { "auctionparticipantclickcnt","AuctionParticipantClickCnt" },
            { "auctionparticipantcnt","AuctionParticipantCnt" },
            { "auctionwoncnt","AuctionWonCnt" },
            { "audiencegroupid","AudienceGroupId" },
            { "audienceid","AudienceId" },
            { "audiencename","AudienceName" },
            { "audiencetype","AudienceType" },
            { "audiencetypeid","AudienceTypeId" },
            { "auditdtim","AuditDtim" },
            { "authoringupdatedtime","AuthoringUpdatedTime" },
            { "autotarget","AutoTarget" },
            { "autotargetcriterion","AutoTargetCriterion" },
            { "autotargetdataid","AutoTargetDataId" },
            { "autotargetid","AutoTargetId" },
            { "autotargetstatusname","AutoTargetStatusName" },
            { "availability","Availability" },
            { "avgposfp","AvgPosFP" },
            { "avgposml","AvgPosML" },
            { "avgposml1","AvgPosML1" },
            { "bptimestamp","BPTimeStamp" },
            { "bscauctionparticipation","BSCAuctionParticipation" },
            { "backendaccountid","BackendAccountId" },
            { "backuppaymentinstrid","BackupPaymentInstrId" },
            { "basecampaignid","BaseCampaignId" },
            { "basecityid","BaseCityId" },
            { "baseprice","BasePrice" },
            { "batchgroupid","BatchGroupId" },
            { "batchgroupname","BatchGroupName" },
            { "batchid","BatchId" },
            { "batchsize","BatchSize" },
            { "benchmarkbid_denominator","BenchmarkBid_Denominator" },
            { "benchmarkbid_numerator","BenchmarkBid_Numerator" },
            { "benchmarkctr_denominator","BenchmarkCTR_Denominator" },
            { "benchmarkctr_numerator","BenchmarkCTR_Numerator" },
            { "bid","Bid" },
            { "bidamount","BidAmount" },
            { "bidboost","BidBoost" },
            { "bidstrategyid","BidStrategyId" },
            { "bidstrategyname","BidStrategyName" },
            { "bidsuggestionid","BidSuggestionId" },
            { "bidtypeid","BidTypeId" },
            { "bidvalue","BidValue" },
            { "biddedmatchtypeid","BiddedMatchTypeId" },
            { "biddingschemeid","BiddingSchemeId" },
            { "billtocustomerid","BillToCustomerId" },
            { "billingcomment","BillingComment" },
            { "billingretrydtim","BillingRetryDtim" },
            { "billingretryid","BillingRetryId" },
            { "billingsuccesscount","BillingSuccessCount" },
            { "bingentityid","BingEntityId" },
            { "binggeotypename","BingGeoTypeName" },
            { "blobid","BlobId" },
            { "bookedadvancedbookingwindow","BookedAdvancedBookingWindow" },
            { "bookedcheckindate","BookedCheckInDate" },
            { "bookingdate","BookingDate" },
            { "bookingreferencenumber","BookingReferenceNumber" },
            { "brand","Brand" },
            { "brandlistid","BrandListId" },
            { "brandlistname","BrandListName" },
            { "brandlistnamehash","BrandListNameHash" },
            { "broadcastyearnum","BroadCastYearNum" },
            { "broadmatchofferamt","BroadMatchOfferAmt" },
            { "broadcastmonthid","BroadcastMonthId" },
            { "broadcastmonthname","BroadcastMonthName" },
            { "broadcastmonthnum","BroadcastMonthNum" },
            { "broadcastweekid","BroadcastWeekId" },
            { "broadcastweeknuminyear","BroadcastWeekNumInYear" },
            { "budgetamt","BudgetAmt" },
            { "budgetfilteredimpressioncnt","BudgetFilteredImpressionCnt" },
            { "budgetid","BudgetId" },
            { "budgetmanagementcapable","BudgetManagementCapable" },
            { "budgetname","BudgetName" },
            { "budgetnamehashcode","BudgetNameHashCode" },
            { "budgetpauseeffectivedtim","BudgetPauseEffectiveDTim" },
            { "budgetpausetypeid","BudgetPauseTypeId" },
            { "budgettypeid","BudgetTypeId" },
            { "bundleid","BundleId" },
            { "businessdesc","BusinessDesc" },
            { "businessid","BusinessId" },
            { "businesslocationid","BusinessLocationId" },
            { "businessname","BusinessName" },
            { "businesstypeid","BusinessTypeId" },
            { "businesswebsite","BusinessWebSite" },
            { "cci_order_list","CCI_Order_List" },
            { "corvrating","CORVRating" },
            { "cpc","CPC" },
            { "cpclickrating","CPClickRating" },
            { "cpm","CPM" },
            { "cqbrrating","CQBRRating" },
            { "cqualityimpact","CQualityImpact" },
            { "cqualityscore","CQualityScore" },
            { "cta","CTA" },
            { "callduration","CallDuration" },
            { "callendreason","CallEndReason" },
            { "callendstatus","CallEndStatus" },
            { "callendtime","CallEndTime" },
            { "callid","CallId" },
            { "callstarttime","CallStartTime" },
            { "calltoactionassetid","CallToActionAssetId" },
            { "calltoactionid","CallToActionId" },
            { "calltoactiontext","CallToActionText" },
            { "campaigndesc","CampaignDesc" },
            { "campaignexclusionid","CampaignExclusionId" },
            { "campaignfeaturebitmask","CampaignFeatureBitMask" },
            { "campaignfiltergroupid","CampaignFilterGroupId" },
            { "campaignflag","CampaignFlag" },
            { "campaignhotelid","CampaignHotelId" },
            { "campaignid","CampaignId" },
            { "campaignlifecyclestatusid","CampaignLifeCycleStatusId" },
            { "campaignname","CampaignName" },
            { "campaignsettingassociationid","CampaignSettingAssociationId" },
            { "campaignsettingid","CampaignSettingId" },
            { "campaignsettingpropertyid","CampaignSettingPropertyId" },
            { "campaignsettingpropertyname","CampaignSettingPropertyName" },
            { "campaignsettingpropertyvalue","CampaignSettingPropertyValue" },
            { "campaignstatusname","CampaignStatusName" },
            { "campaigntypeid","CampaignTypeId" },
            { "campaigntypesfilter","CampaignTypesFilter" },
            { "candisplayimpressioncount","CanDisplayImpressionCount" },
            { "cashbacktypeid","CashbackTypeId" },
            { "category","Category" },
            { "categoryid","CategoryId" },
            { "categorylist","CategoryList" },
            { "categoryname","CategoryName" },
            { "changetypeid","ChangeTypeId" },
            { "channeltype","ChannelType" },
            { "channeltypeid","ChannelTypeId" },
            { "checkindate","CheckInDate" },
            { "city","City" },
            { "cityid","CityId" },
            { "citylcid","CityLCID" },
            { "citylatitude","CityLatitude" },
            { "citylocationid","CityLocationId" },
            { "citylongitude","CityLongitude" },
            { "cityname","CityName" },
            { "clickcnt","ClickCnt" },
            { "clickfp","ClickFP" },
            { "clickml","ClickML" },
            { "clickml1","ClickML1" },
            { "clicksumofsquare","ClickSumOfSquare" },
            { "clicktimesconversion","ClickTimesConversion" },
            { "clicktimesconversioncredit","ClickTimesConversionCredit" },
            { "clicktimesimpression","ClickTimesImpression" },
            { "clicktimesrevenue","ClickTimesRevenue" },
            { "clicktypeclickcnt","ClickTypeClickCnt" },
            { "clicktypeconversioncnt","ClickTypeConversionCnt" },
            { "clicktypeconversioncredit","ClickTypeConversionCredit" },
            { "clicktypeid","ClickTypeId" },
            { "clicktypeimpressioncnt","ClickTypeImpressionCnt" },
            { "clicks","Clicks" },
            { "clock24hour","Clock24Hour" },
            { "code","Code" },
            { "collectionid","CollectionId" },
            { "combinationflag","CombinationFlag" },
            { "completedviewcnt","CompletedViewCnt" },
            { "compressbillingflag","CompressBillingFlag" },
            { "compressedbilling","CompressedBilling" },
            { "condition","Condition" },
            { "contactinfoid","ContactInfoId" },
            { "content","Content" },
            { "contentmatchofferamt","ContentMatchOfferAmt" },
            { "conversioncnt","ConversionCnt" },
            { "conversioncount","ConversionCount" },
            { "conversioncredit","ConversionCredit" },
            { "conversioncreditsumofsquare","ConversionCreditSumOfSquare" },
            { "conversioncredittimesrevenue","ConversionCreditTimesRevenue" },
            { "conversionenabledclickcnt","ConversionEnabledClickCnt" },
            { "conversionenabledtotalamount","ConversionEnabledTotalAmount" },
            { "conversionlag","ConversionLag" },
            { "conversionrateadjustmentpct","ConversionRateAdjustmentPct" },
            { "conversionsumofsquare","ConversionSumOfSquare" },
            { "conversiontimesrevenue","ConversionTimesRevenue" },
            { "conversionvaluecnt","ConversionValueCnt" },
            { "conversionvaluesumofsquare","ConversionValueSumOfSquare" },
            { "conversionvaluetimesrevenue","ConversionValueTimesRevenue" },
            { "cooperativeclickcnt","CooperativeClickCnt" },
            { "cooperativeconversioncnt","CooperativeConversionCnt" },
            { "cooperativeconversioncredit","CooperativeConversionCredit" },
            { "cooperativeimpressioncnt","CooperativeImpressionCnt" },
            { "costfp","CostFP" },
            { "costml","CostML" },
            { "costml1","CostML1" },
            { "countrycode","CountryCode" },
            { "countrycode3","CountryCode3" },
            { "countryisoname","CountryISOName" },
            { "countryid","CountryId" },
            { "countrylatitude","CountryLatitude" },
            { "countrylocationid","CountryLocationId" },
            { "countrylongname","CountryLongName" },
            { "countrylongitude","CountryLongitude" },
            { "countryname","CountryName" },
            { "countrynumber","CountryNumber" },
            { "countylatitude","CountyLatitude" },
            { "countylongitude","CountyLongitude" },
            { "countyname","CountyName" },
            { "createprocessid","CreateProcessId" },
            { "createdbyuserid","CreatedByUserId" },
            { "createddtim","CreatedDTim" },
            { "createddate","CreatedDate" },
            { "createdtime","CreatedTime" },
            { "createdutcdatetime","CreatedUTCDateTime" },
            { "creditlimit","CreditLimit" },
            { "criterionname","CriterionName" },
            { "currencycode","CurrencyCode" },
            { "currencyid","CurrencyId" },
            { "currencyname","CurrencyName" },
            { "currentmonthlyrate","CurrentMonthlyRate" },
            { "customlabel0","CustomLabel0" },
            { "customlabel1","CustomLabel1" },
            { "customlabel2","CustomLabel2" },
            { "customlabel3","CustomLabel3" },
            { "customlabel4","CustomLabel4" },
            { "customparameters","CustomParameters" },
            { "customsegmentid","CustomSegmentId" },
            { "customsegmentlineitemid","CustomSegmentLineItemId" },
            { "customsegmentname","CustomSegmentName" },
            { "customerid","CustomerId" },
            { "customernumber","CustomerNumber" },
            { "customertypeid","CustomerTypeId" },
            { "ddafulladvertiserreportedrevenue","DDAFullAdvertiserReportedRevenue" },
            { "ddafullconversioncredit","DDAFullConversionCredit" },
            { "ddafullviewadvertiserreportedrevenue","DDAFullViewAdvertiserReportedRevenue" },
            { "ddafullviewthroughconversion","DDAFullViewThroughConversion" },
            { "daily","Daily" },
            { "dailybudget","DailyBudget" },
            { "dailybudgetamt","DailyBudgetAmt" },
            { "dailybudgetamtmodifieddtim","DailyBudgetAmtModifiedDTim" },
            { "datablobpath","DataBlobPath" },
            { "dataforreport","DataForReport" },
            { "datarowcount","DataRowCount" },
            { "datavalue","DataValue" },
            { "datekey","DateKey" },
            { "datetype","DateType" },
            { "day1","Day1" },
            { "day100","Day100" },
            { "day1095","Day1095" },
            { "day15","Day15" },
            { "day30","Day30" },
            { "day365","Day365" },
            { "dayid","DayId" },
            { "daynuminmonth","DayNumInMonth" },
            { "daynuminyear","DayNumInYear" },
            { "dayofweek","DayOfWeek" },
            { "dayofweek1","DayOfWeek1" },
            { "dayofweek104","DayOfWeek104" },
            { "dayofweek156","DayOfWeek156" },
            { "dayofweek20","DayOfWeek20" },
            { "dayofweek4","DayOfWeek4" },
            { "dayofweek52","DayOfWeek52" },
            { "daylightsavingflag","DaylightSavingFlag" },
            { "dbtypeid","DbTypeId" },
            { "defaultbroadmatchofferamt","DefaultBroadMatchOfferAmt" },
            { "defaultcontentmatchofferamt","DefaultContentMatchOfferAmt" },
            { "defaultexactmatchofferamt","DefaultExactMatchOfferAmt" },
            { "defaultphrasematchofferamt","DefaultPhraseMatchOfferAmt" },
            { "defaultuserid","DefaultUserId" },
            { "deliverychanneltypeid","DeliveryChannelTypeId" },
            { "deliveryformatid","DeliveryFormatId" },
            { "demandtypeid","DemandTypeId" },
            { "deployerlock","DeployerLock" },
            { "depth","Depth" },
            { "derivedcountryid","DerivedCountryId" },
            { "derivedlocationid","DerivedLocationId" },
            { "description","Description" },
            { "description1assetid","Description1AssetId" },
            { "description2assetid","Description2AssetId" },
            { "descriptionassetid","DescriptionAssetId" },
            { "destinationtablename","DestinationTableName" },
            { "destinationurl","DestinationURL" },
            { "devtoken","DevToken" },
            { "deviceosid2","DeviceOSID2" },
            { "deviceosid","DeviceOSId" },
            { "devicepreference","DevicePreference" },
            { "devicetypeid","DeviceTypeId" },
            { "devicetypename","DeviceTypeName" },
            { "devicetypesfilter","DeviceTypesFilter" },
            { "displaycategoryname","DisplayCategoryName" },
            { "displayurl","DisplayURL" },
            { "distributionchannelid","DistributionChannelId" },
            { "distributionchannelname","DistributionChannelName" },
            { "domain","Domain" },
            { "domainname","DomainName" },
            { "domaintypeid","DomainTypeId" },
            { "downloadtimeinsecond","DownloadTimeInSecond" },
            { "downloads","Downloads" },
            { "dynamicattributecolumn","DynamicAttributeColumn" },
            { "emauctionparticipation","EMAuctionParticipation" },
            { "emauctionwon","EMAuctionWon" },
            { "editorialstatusid","EditorialStatusId" },
            { "effectiveeditorialstatusid","EffectiveEditorialStatusId" },
            { "effectiveenddatetime","EffectiveEndDateTime" },
            { "effectivestartdatetime","EffectiveStartDateTime" },
            { "elementdestinationurl","ElementDestinationURL" },
            { "elementid","ElementId" },
            { "elementname","ElementName" },
            { "elementtitle","ElementTitle" },
            { "elementtypeid","ElementTypeId" },
            { "eligibleimpressioncnt","EligibleImpressionCnt" },
            { "email","Email" },
            { "enddate","EndDate" },
            { "enddatekey","EndDateKey" },
            { "endoffset","EndOffset" },
            { "endtime","EndTime" },
            { "englishname","EnglishName" },
            { "entityid","EntityId" },
            { "entitylevelestimatedincreaseincpa","EntityLevelEstimatedIncreaseInCPA" },
            { "entitylevelestimatedincreaseincpc","EntityLevelEstimatedIncreaseInCPC" },
            { "entityname","EntityName" },
            { "entitytype","EntityType" },
            { "entityvalue","EntityValue" },
            { "entityvalueid","EntityValueId" },
            { "errorblobpath","ErrorBLOBPath" },
            { "errormessage","ErrorMessage" },
            { "estimatedconversioncredit","EstimatedConversionCredit" },
            { "estimatedincreaseinclicks","EstimatedIncreaseInClicks" },
            { "estimatedincreaseinconversions","EstimatedIncreaseInConversions" },
            { "estimatedincreaseincost","EstimatedIncreaseInCost" },
            { "estimatedincreaseinimpressions","EstimatedIncreaseInImpressions" },
            { "eventid","EventId" },
            { "eventtime","EventTime" },
            { "exactmatchofferamt","ExactMatchOfferAmt" },
            { "excludesyndicatedsearch","ExcludeSyndicatedSearch" },
            { "exclusionflag","ExclusionFlag" },
            { "exclusionsubtypeid","ExclusionSubTypeId" },
            { "exclusiontypeid","ExclusionTypeId" },
            { "exclusionvalue","ExclusionValue" },
            { "executionendtime","ExecutionEndTime" },
            { "executionstarttime","ExecutionStartTime" },
            { "experimentcampaignid","ExperimentCampaignId" },
            { "experimentid","ExperimentId" },
            { "experimentname","ExperimentName" },
            { "experimenttype","ExperimentType" },
            { "extendedcost","ExtendedCost" },
            { "extensionbitmask","ExtensionBitMask" },
            { "externaleffectiveeditorialstatusid","ExternalEffectiveEditorialStatusId" },
            { "extractenddatekey","ExtractEndDateKey" },
            { "factgroup","FactGroup" },
            { "factgroupname","FactGroupName" },
            { "facttablename","FactTableName" },
            { "featuremask","FeatureMask" },
            { "feedattributedatatypeid","FeedAttributeDataTypeId" },
            { "feedattributeid","FeedAttributeId" },
            { "feedid","FeedId" },
            { "feeditemid","FeedItemId" },
            { "feedlabel","FeedLabel" },
            { "feedorigin","FeedOrigin" },
            { "filterclickcnt","FilterClickCnt" },
            { "finalappurl","FinalAppUrl" },
            { "finalmobileurl","FinalMobileUrl" },
            { "finalurl","FinalURL" },
            { "finalurlsuffix","FinalUrlSuffix" },
            { "financialstatusid","FinancialStatusId" },
            { "firstlaunches","FirstLaunches" },
            { "firstlevelcategory","FirstLevelCategory" },
            { "firstname","FirstName" },
            { "fiscalmonthid","FiscalMonthId" },
            { "fiscalmonthname","FiscalMonthName" },
            { "fiscalmonthnum","FiscalMonthNum" },
            { "fiscalquarternum","FiscalQuarterNum" },
            { "fiscalweekid","FiscalWeekId" },
            { "fiscalweeknuminyear","FiscalWeekNumInYear" },
            { "fiscalyearnum","FiscalYearNum" },
            { "forcesequence","ForceSequence" },
            { "fractionalpart","FractionalPart" },
            { "fraudstatusid","FraudStatusId" },
            { "frontendaccountid","FrontendAccountId" },
            { "fulladvertiserreportedrevenue","FullAdvertiserReportedRevenue" },
            { "fulladvertiserreportedrevenueadjustment","FullAdvertiserReportedRevenueAdjustment" },
            { "fullclicktimesconversion","FullClickTimesConversion" },
            { "fullclicktimesconversioncredit","FullClickTimesConversionCredit" },
            { "fullconversioncnt","FullConversionCnt" },
            { "fullconversioncredit","FullConversionCredit" },
            { "fullconversioncreditsumofsquare","FullConversionCreditSumOfSquare" },
            { "fullconversioncredittimesrevenue","FullConversionCreditTimesRevenue" },
            { "fullconversionsumofsquare","FullConversionSumOfSquare" },
            { "fullconversiontimesrevenue","FullConversionTimesRevenue" },
            { "fullconversionvaluecnt","FullConversionValueCnt" },
            { "fullconversionvaluesumofsquare","FullConversionValueSumOfSquare" },
            { "fullconversionvaluetimesrevenue","FullConversionValueTimesRevenue" },
            { "fullviewadvertiserreportedrevenue","FullViewAdvertiserReportedRevenue" },
            { "fullviewadvertiserreportedrevenueadjustment","FullViewAdvertiserReportedRevenueAdjustment" },
            { "fullviewconversioncnt","FullViewConversionCnt" },
            { "fullviewconversioncredit","FullViewConversionCredit" },
            { "fullviewconversioncreditsumofsquare","FullViewConversionCreditSumOfSquare" },
            { "genderid","GenderId" },
            { "geocodestatusid","GeoCodeStatusId" },
            { "geolocationid","GeoLocationId" },
            { "geolocationid2","GeoLocationId2" },
            { "geolocationtypeid","GeoLocationTypeId" },
            { "geolocationtypename","GeoLocationTypeName" },
            { "globalofferid","GlobalOfferId" },
            { "goalcategory","GoalCategory" },
            { "goalconversiontypeid","GoalConversionTypeId" },
            { "goalid","GoalId" },
            { "goalidfilterclickcnt","GoalIdFilterClickCnt" },
            { "goalname","GoalName" },
            { "goalstatusid","GoalStatusId" },
            { "goaltypeid","GoalTypeId" },
            { "goalvalue","GoalValue" },
            { "goalvaluesourceid","GoalValueSourceId" },
            { "gregoriandate","GregorianDate" },
            { "groupname","GroupName" },
            { "gtin","Gtin" },
            { "horvrating","HORVRating" },
            { "hpclickrating","HPClickRating" },
            { "hqbrrating","HQBRRating" },
            { "hqualityscore","HQualityScore" },
            { "hascategory","HasCategory" },
            { "hashcode","HashCode" },
            { "hashvalue","HashValue" },
            { "headline1assetid","Headline1AssetId" },
            { "headline2assetid","Headline2AssetId" },
            { "headline3assetid","Headline3AssetId" },
            { "headlineassetid","HeadlineAssetId" },
            { "hierarchyid","HierarchyId" },
            { "hotellistingid","HotelListingId" },
            { "hotellistingpropertydatavalue","HotelListingPropertyDataValue" },
            { "hotellistingpropertyid","HotelListingPropertyId" },
            { "hotellistingtreesettingassociationid","HotelListingTreeSettingAssociationId" },
            { "hotellistingtreesettingid","HotelListingTreeSettingId" },
            { "hotellistingtreesettingname","HotelListingTreeSettingName" },
            { "hotellistingtreesettingparentpropertyid","HotelListingTreeSettingParentPropertyId" },
            { "hotellistingtreesettingpropertyid","HotelListingTreeSettingPropertyId" },
            { "hotellistingtreesettingpropertyname","HotelListingTreeSettingPropertyName" },
            { "hotellistingtreesettingpropertyvalue","HotelListingTreeSettingPropertyValue" },
            { "hotelname","HotelName" },
            { "hournum","HourNum" },
            { "icel1id","ICEL1Id" },
            { "icel2id","ICEL2Id" },
            { "icel3id","ICEL3Id" },
            { "id","Id" },
            { "imageid","ImageId" },
            { "impfp","ImpFP" },
            { "impml","ImpML" },
            { "impml1","ImpML1" },
            { "impressioncnt","ImpressionCnt" },
            { "impressionsumofsquare","ImpressionSumOfSquare" },
            { "impressionwithpositioncnt","ImpressionWithPositionCnt" },
            { "incomingpublisherwebsitecountry","IncomingPublisherWebSiteCountry" },
            { "incrementpct","IncrementPct" },
            { "incrementalbudgetamt","IncrementalBudgetAmt" },
            { "industryid","IndustryId" },
            { "info","Info" },
            { "installcnt","InstallCnt" },
            { "intentionmask","IntentionMask" },
            { "invalidclickcnt","InvalidClickCnt" },
            { "invalidconversioncnt","InvalidConversionCnt" },
            { "invalidconversioncredit","InvalidConversionCredit" },
            { "invalidgeneralclickcnt","InvalidGeneralClickCnt" },
            { "invalidimpressioncnt","InvalidImpressionCnt" },
            { "isaimtarget","IsAIMTarget" },
            { "isactive","IsActive" },
            { "isactiveflag","IsActiveFlag" },
            { "isadgroupexclusion","IsAdGroupExclusion" },
            { "isbase","IsBase" },
            { "isbusinesslocation","IsBusinessLocation" },
            { "isconversiongoalset","IsConversionGoalSet" },
            { "isdaily","IsDaily" },
            { "isdefault","IsDefault" },
            { "isdeleted","IsDeleted" },
            { "isdomainproperty","IsDomainProperty" },
            { "isenable","IsEnable" },
            { "isexcluded","IsExcluded" },
            { "isexclusionlist","IsExclusionList" },
            { "isexclusive","IsExclusive" },
            { "isexclusivebitmask","IsExclusiveBitMask" },
            { "isglobalstore","IsGlobalStore" },
            { "isimmediateparent","IsImmediateParent" },
            { "isinclusionlist","IsInclusionList" },
            { "islandingpage","IsLandingPage" },
            { "islastbatch","IsLastBatch" },
            { "isleaf","IsLeaf" },
            { "islibrary","IsLibrary" },
            { "islocal","IsLocal" },
            { "ismsanswfrecord","IsMSANSWFRecord" },
            { "ismainconversiongoal","IsMainConversionGoal" },
            { "isnegative","IsNegative" },
            { "isofflinecall","IsOfflineCall" },
            { "isother","IsOther" },
            { "ispageurl","IsPageUrl" },
            { "isperfbased","IsPerfBased" },
            { "isperfmax","IsPerfMax" },
            { "isprocessed","IsProcessed" },
            { "istpan","IsTPAN" },
            { "istargetkeywordspecified","IsTargetKeyWordSpecified" },
            { "istargetable","IsTargetable" },
            { "jobcompletedtime","JobCompletedTime" },
            { "jobfullname","JobFullName" },
            { "jobid","JobId" },
            { "jobname","JobName" },
            { "jobnextruntime","JobNextRunTime" },
            { "jobstarttime","JobStartTime" },
            { "jsonattributehandlername","JsonAttributeHandlerName" },
            { "jsonattributeschemaid","JsonAttributeSchemaId" },
            { "kvexpression","KVExpression" },
            { "keyword","Keyword" },
            { "keywordorderid","KeywordOrderId" },
            { "keywordorderitemid","KeywordOrderItemId" },
            { "keywordordername","KeywordOrderName" },
            { "keywordstatusname","KeywordStatusName" },
            { "lcafulladvertiserreportedrevenue","LCAFullAdvertiserReportedRevenue" },
            { "lcafullconversioncredit","LCAFullConversionCredit" },
            { "lcafullviewadvertiserreportedrevenue","LCAFullViewAdvertiserReportedRevenue" },
            { "lcafullviewthroughconversion","LCAFullViewThroughConversion" },
            { "lcid","LCID" },
            { "les_guid","LES_GUID" },
            { "labelcolor","LabelColor" },
            { "labeldescription","LabelDescription" },
            { "labelid","LabelId" },
            { "labelmccid","LabelMccId" },
            { "labelname","LabelName" },
            { "labelnamehashcode","LabelNameHashcode" },
            { "labels","Labels" },
            { "landingpagetitle","LandingPageTitle" },
            { "languagecode","LanguageCode" },
            { "languagecommonname","LanguageCommonName" },
            { "languageid","LanguageId" },
            { "languagelocaleid","LanguageLocaleId" },
            { "languagelocalename","LanguageLocaleName" },
            { "languagename","LanguageName" },
            { "lastloadcompletetime","LastLoadCompleteTime" },
            { "lastmodifiedby","LastModifiedBy" },
            { "lastname","LastName" },
            { "latitude","Latitude" },
            { "leafnodeid","LeafNodeId" },
            { "lengthofstay","LengthOfStay" },
            { "level","Level" },
            { "lifecyclestatusid","LifeCycleStatusId" },
            { "limitamt","LimitAmt" },
            { "lineitemid","LineItemId" },
            { "listitemcount","ListItemCount" },
            { "listinggrouppropertydatavalue","ListingGroupPropertyDataValue" },
            { "listinggrouppropertyid","ListingGroupPropertyId" },
            { "loaddatekey","LoadDateKey" },
            { "loadtime","LoadTime" },
            { "localstorecode","LocalStoreCode" },
            { "locationdatasourceid","LocationDataSourceId" },
            { "locationdescription","LocationDescription" },
            { "locationid","LocationId" },
            { "locationversionid","LocationVersionId" },
            { "lockstatusid","LockStatusId" },
            { "loginuserid","LoginUserId" },
            { "loginusername","LoginUserName" },
            { "logoimageid","LogoImageId" },
            { "longheadline","LongHeadline" },
            { "longheadlineassetid","LongHeadlineAssetId" },
            { "longitude","Longitude" },
            { "lookbackwindowinminutes","LookbackWindowinMinutes" },
            { "mmabidadjustment","MMABidAdjustment" },
            { "msalexandriageoentitynbr","MSAlexandriaGeoEntityNbr" },
            { "msregionkeytxt","MSRegionKeyTxt" },
            { "mssalescountrycode","MSSalesCountryCode" },
            { "mssalesregionname","MSSalesRegionName" },
            { "manualcpc","ManualCpc" },
            { "manualcpi","ManualCpi" },
            { "maptableid","MapTableId" },
            { "maptype","MapType" },
            { "mappingid","MappingId" },
            { "marketid","MarketId" },
            { "matchtypeid","MatchTypeId" },
            { "matchedoffercount","MatchedOfferCount" },
            { "maxarchivedtim","MaxArchiveDTim" },
            { "maxarchiveddtim","MaxArchivedDTim" },
            { "maxbatchid","MaxBatchId" },
            { "maxcpc","MaxCPC" },
            { "maxcpclick","MaxCPClick" },
            { "maxcontentcpc","MaxContentCPC" },
            { "maxdatekey","MaxDateKey" },
            { "maxloadtime","MaxLoadTime" },
            { "maxmodifieddtim","MaxModifiedDTim" },
            { "maxmodifieddate","MaxModifiedDate" },
            { "maxmodifieddatetime","MaxModifiedDateTime" },
            { "maxprocessedtime","MaxProcessedTime" },
            { "maxretrycount","MaxRetryCount" },
            { "maxrowid","MaxRowId" },
            { "maxsearchcpc","MaxSearchCPC" },
            { "maximumbid","MaximumBid" },
            { "mediumid","MediumId" },
            { "mediumname","MediumName" },
            { "merchantid","MerchantId" },
            { "merchantproductid","MerchantProductId" },
            { "message","Message" },
            { "metroareadesc","MetroAreaDesc" },
            { "metroareaid","MetroAreaId" },
            { "metroarealatitude","MetroAreaLatitude" },
            { "metroarealongitude","MetroAreaLongitude" },
            { "metroareaname","MetroAreaName" },
            { "metrolcid","MetroLCID" },
            { "mindatekey","MinDateKey" },
            { "minfpbid","MinFPBid" },
            { "minfpbidinaccountcurrency","MinFPBidInAccountCurrency" },
            { "minml1bid","MinML1Bid" },
            { "minml1bidinaccountcurrency","MinML1BidInAccountCurrency" },
            { "minmlbid","MinMLBid" },
            { "minmlbidinaccountcurrency","MinMLBidInAccountCurrency" },
            { "mobileapplicationname","MobileApplicationName" },
            { "modelstarttime","ModelStartTime" },
            { "modificationdate","ModificationDate" },
            { "modifiedby","ModifiedBy" },
            { "modifiedbyuserid","ModifiedByUserId" },
            { "modifieddtim","ModifiedDTim" },
            { "modifieddate","ModifiedDate" },
            { "modifieddatetime","ModifiedDateTime" },
            { "monthid","MonthId" },
            { "monthname","MonthName" },
            { "monthnum","MonthNum" },
            { "monthstartdate","MonthStartDate" },
            { "monthly","Monthly" },
            { "monthlyanniversaryday","MonthlyAnniversaryDay" },
            { "monthlybudget","MonthlyBudget" },
            { "monthlybudgetamt","MonthlyBudgetAmt" },
            { "monthlybudgetmodifieddtim","MonthlyBudgetModifiedDtim" },
            { "mostspecificlocation","MostSpecificLocation" },
            { "mpn","Mpn" },
            { "name","Name" },
            { "nativebidadjustment","NativeBidAdjustment" },
            { "negativekeyword","NegativeKeyword" },
            { "negativekeywordcatalogid","NegativeKeywordCatalogId" },
            { "negativekeywordcount","NegativeKeywordCount" },
            { "negativekeywordid","NegativeKeywordId" },
            { "negativekeywordlistid","NegativeKeywordListId" },
            { "negativekeywordlistlineitemid","NegativeKeywordListLineItemId" },
            { "negativekeywordlistname","NegativeKeywordListName" },
            { "negativekeywordlisttypeid","NegativeKeywordListTypeId" },
            { "neighborhood","Neighborhood" },
            { "neighborhoodlatitude","NeighborhoodLatitude" },
            { "neighborhoodlongitude","NeighborhoodLongitude" },
            { "netterm","NetTerm" },
            { "networkid","NetworkId" },
            { "networkname","NetworkName" },
            { "networkoptionid","NetworkOptionId" },
            { "newcustomeracquisitiongoalid","NewCustomerAcquisitionGoalId" },
            { "newcustomerconversioncredit","NewCustomerConversionCredit" },
            { "newcustomercount","NewCustomerCount" },
            { "newcustomerrevenue","NewCustomerRevenue" },
            { "newvalue","NewValue" },
            { "nextactiontime","NextActionTime" },
            { "nodeid","NodeId" },
            { "nodeidpath","NodeIdPath" },
            { "nodepath","NodePath" },
            { "notificationflagbitmask","NotificationFlagBitmask" },
            { "orvrating","ORVRating" },
            { "osdetailversion","OSDetailVersion" },
            { "osgroupcode","OSGroupCode" },
            { "osgroupname","OSGroupName" },
            { "osid","OSId" },
            { "osname","OSName" },
            { "observedconversioncredit","ObservedConversionCredit" },
            { "offercategoryl1","OfferCategoryL1" },
            { "offercategoryl2","OfferCategoryL2" },
            { "offercategoryl3","OfferCategoryL3" },
            { "offercategoryl4","OfferCategoryL4" },
            { "offercategoryl5","OfferCategoryL5" },
            { "offercategoryl6","OfferCategoryL6" },
            { "offercategoryl7","OfferCategoryL7" },
            { "offerid","OfferId" },
            { "offlinephonecallcnt","OfflinePhoneCallCnt" },
            { "offlinephonecost","OfflinePhoneCost" },
            { "oldvalue","OldValue" },
            { "onlinephonecallcnt","OnlinePhoneCallCnt" },
            { "onlinephonecost","OnlinePhoneCost" },
            { "op1name","Op1Name" },
            { "op2name","Op2Name" },
            { "op3name","Op3Name" },
            { "orderflag","OrderFlag" },
            { "orderid","OrderId" },
            { "orderitemdataid","OrderItemDataId" },
            { "orderitemdatapropertyid","OrderItemDataPropertyId" },
            { "orderitemdatapropertyname","OrderItemDataPropertyName" },
            { "orderitemdatapropertyvalue","OrderItemDataPropertyValue" },
            { "orderitemid","OrderItemId" },
            { "ordername","OrderName" },
            { "orderproducttreesettingid","OrderProductTreeSettingId" },
            { "orderproducttreesettingparentpropertyid","OrderProductTreeSettingParentPropertyId" },
            { "orderproducttreesettingpropertyid","OrderProductTreeSettingPropertyId" },
            { "orderproducttreesettingpropertyname","OrderProductTreeSettingPropertyName" },
            { "ordersettingassociationid","OrderSettingAssociationId" },
            { "ordersettingid","OrderSettingId" },
            { "ordersettingpropertyid","OrderSettingPropertyId" },
            { "ordersettingpropertyname","OrderSettingPropertyName" },
            { "ordersettingpropertyvalue","OrderSettingPropertyValue" },
            { "orderable","Orderable" },
            { "originaladdatapropertyvalue","OriginalAdDataPropertyValue" },
            { "originalcampaignsettingpropertyvalue","OriginalCampaignSettingPropertyValue" },
            { "originalmediaheight","OriginalMediaHeight" },
            { "originalmediawidth","OriginalMediaWidth" },
            { "originalosdetail","OriginalOSDetail" },
            { "originalosdetailversion","OriginalOSDetailVersion" },
            { "originalosgroup","OriginalOSGroup" },
            { "originalorderitemdatapropertyvalue","OriginalOrderItemDataPropertyValue" },
            { "originalordersettingpropertyvalue","OriginalOrderSettingPropertyValue" },
            { "originalpropertyvalue","OriginalPropertyValue" },
            { "otheraddressid","OtherAddressId" },
            { "outputtable","OutputTable" },
            { "ownercustomerid","OwnerCustomerId" },
            { "pclickrating","PClickRating" },
            { "pkcheckenabled","PKCheckEnabled" },
            { "pk_column_list","PK_Column_List" },
            { "pagepositionid","PagePositionId" },
            { "pagepositionid2","PagePositionId2" },
            { "pagetype","PageType" },
            { "param1","Param1" },
            { "param2","Param2" },
            { "param3","Param3" },
            { "parameterdesc","ParameterDesc" },
            { "parametername","ParameterName" },
            { "parametertype","ParameterType" },
            { "parametervalue_current","ParameterValue_Current" },
            { "parametervalue_default","ParameterValue_Default" },
            { "parametervalue_new","ParameterValue_New" },
            { "parentcategoryid","ParentCategoryId" },
            { "parentid","ParentId" },
            { "parentnodeid","ParentNodeId" },
            { "parentpropertyid","ParentPropertyId" },
            { "parenttargettypeid","ParentTargetTypeId" },
            { "parenttargetvalueid","ParentTargetValueId" },
            { "partitionid","PartitionId" },
            { "partitionkey","PartitionKey" },
            { "partitionkeyid","PartitionKeyId" },
            { "partnerclick","PartnerClick" },
            { "partnereligibleimpression","PartnerEligibleImpression" },
            { "partnerimpression","PartnerImpression" },
            { "partnermissedimpression","PartnerMissedImpression" },
            { "partnermissedimpressioninsufficientbid","PartnerMissedImpressionInsufficientBid" },
            { "partnermissedimpressionnobid","PartnerMissedImpressionNoBid" },
            { "partnermissedimpressionnotax","PartnerMissedImpressionNoTax" },
            { "partnermissedimpressionother","PartnerMissedImpressionOther" },
            { "partnermissedimpressionspendingcapreached","PartnerMissedImpressionSpendingCapReached" },
            { "path1","Path1" },
            { "path2","Path2" },
            { "pausestatus","PauseStatus" },
            { "pausestatusid","PauseStatusId" },
            { "paymentoptionid","PaymentOptionId" },
            { "percent25viewcnt","Percent25ViewCnt" },
            { "percent50viewcnt","Percent50ViewCnt" },
            { "percent75viewcnt","Percent75ViewCnt" },
            { "percentbid","PercentBid" },
            { "percentmaxcpc","PercentMaxCpc" },
            { "performancelabelbestcnt","PerformanceLabelBestCnt" },
            { "performancelabelgoodcnt","PerformanceLabelGoodCnt" },
            { "performancelabellearningcnt","PerformanceLabelLearningCnt" },
            { "performancelabellowcnt","PerformanceLabelLowCnt" },
            { "performancelabelunratedcnt","PerformanceLabelUnratedCnt" },
            { "personemail","PersonEmail" },
            { "personfirstname","PersonFirstName" },
            { "personid","PersonId" },
            { "personjobtitle","PersonJobTitle" },
            { "personlastname","PersonLastName" },
            { "personmiddleinitial","PersonMiddleInitial" },
            { "personname","PersonName" },
            { "phonecost","PhoneCost" },
            { "phoneextensioncountry","PhoneExtensionCountry" },
            { "phoneextensionnumber","PhoneExtensionNumber" },
            { "phoneimpressioncnt","PhoneImpressionCnt" },
            { "phonenumber","PhoneNumber" },
            { "phrasematchofferamt","PhraseMatchOfferAmt" },
            { "pilotflag","PilotFlag" },
            { "pinnedbitmask","PinnedBitMask" },
            { "pinnedcount","PinnedCount" },
            { "pinnedposition1cnt","PinnedPosition1Cnt" },
            { "pinnedposition2cnt","PinnedPosition2Cnt" },
            { "pinnedposition3cnt","PinnedPosition3Cnt" },
            { "placementid","PlacementId" },
            { "placementname","PlacementName" },
            { "policymask","PolicyMask" },
            { "position","Position" },
            { "positionsumofsquare","PositionSumOfSquare" },
            { "postprocessingcmd","PostProcessingCmd" },
            { "postprocessorname","PostProcessorName" },
            { "postalcode","PostalCode" },
            { "postalcodeid","PostalCodeId" },
            { "postalcodelatitude","PostalCodeLatitude" },
            { "postalcodelongitude","PostalCodeLongitude" },
            { "preprocessorname","PreProcessorName" },
            { "preferredbilltopaymentinstrid","PreferredBillToPaymentInstrId" },
            { "preferredcurrencyid","PreferredCurrencyId" },
            { "preferredlanguageid","PreferredLanguageId" },
            { "preferreduserid","PreferredUserId" },
            { "price","Price" },
            { "pricingmodelid","PricingModelId" },
            { "pricingmodelname","PricingModelName" },
            { "primaryuse","PrimaryUse" },
            { "priority","Priority" },
            { "privacycheckstatusid","PrivacyCheckStatusId" },
            { "processgroupid","ProcessGroupId" },
            { "processid","ProcessId" },
            { "processserviceinstance","ProcessServiceInstance" },
            { "processtimeinsecond","ProcessTimeInSecond" },
            { "processeddate","ProcessedDate" },
            { "processedtime","ProcessedTime" },
            { "processorname","ProcessorName" },
            { "productboughttitle","ProductBoughtTitle" },
            { "productcategoryid","ProductCategoryId" },
            { "productgroupoperatorid","ProductGroupOperatorId" },
            { "productgrouppropertydatavalue","ProductGroupPropertyDataValue" },
            { "productgrouppropertyid","ProductGroupPropertyId" },
            { "productgrouppropertyname","ProductGroupPropertyName" },
            { "productid","ProductId" },
            { "productofferid","ProductOfferId" },
            { "productofferids","ProductOfferIds" },
            { "productsiteid","ProductSiteId" },
            { "producttypel1","ProductTypeL1" },
            { "producttypel2","ProductTypeL2" },
            { "producttypel3","ProductTypeL3" },
            { "producttypel4","ProductTypeL4" },
            { "producttypel5","ProductTypeL5" },
            { "producttypel6","ProductTypeL6" },
            { "producttypel7","ProductTypeL7" },
            { "properties","Properties" },
            { "propertyid","PropertyId" },
            { "propertytarget","PropertyTarget" },
            { "propertyurl","PropertyUrl" },
            { "propertyvalue","PropertyValue" },
            { "providerid","ProviderId" },
            { "pubaccountid","PubAccountId" },
            { "pubprocessid","PubProcessId" },
            { "publicationperiodendtime","PublicationPeriodEndTime" },
            { "publishercountryid","PublisherCountryId" },
            { "publisherid","PublisherId" },
            { "purchasedgtin","PurchasedGtin" },
            { "purchases","Purchases" },
            { "qbrrating","QBRRating" },
            { "qualitybandid","QualityBandId" },
            { "qualityimpact","QualityImpact" },
            { "qualityscore","QualityScore" },
            { "quantitybought","QuantityBought" },
            { "quarternum","QuarterNum" },
            { "quarterstartdate","QuarterStartDate" },
            { "quarterly","Quarterly" },
            { "query","Query" },
            { "querygeolocationid","QueryGeoLocationId" },
            { "queryhash","QueryHash" },
            { "querylocationid","QueryLocationId" },
            { "questionid","QuestionId" },
            { "queueviewname","QueueViewName" },
            { "quovacityname","QuovaCityName" },
            { "quovasubgeographyname","QuovaSubGeographyName" },
            { "radius","Radius" },
            { "rankfilteredimpressioncnt","RankFilteredImpressionCnt" },
            { "reach10d","Reach10d" },
            { "reach11d","Reach11d" },
            { "reach12d","Reach12d" },
            { "reach13d","Reach13d" },
            { "reach14d","Reach14d" },
            { "reach15d","Reach15d" },
            { "reach16d","Reach16d" },
            { "reach17d","Reach17d" },
            { "reach18d","Reach18d" },
            { "reach19d","Reach19d" },
            { "reach1d","Reach1d" },
            { "reach20d","Reach20d" },
            { "reach21d","Reach21d" },
            { "reach22d","Reach22d" },
            { "reach23d","Reach23d" },
            { "reach24d","Reach24d" },
            { "reach25d","Reach25d" },
            { "reach26d","Reach26d" },
            { "reach27d","Reach27d" },
            { "reach28d","Reach28d" },
            { "reach29d","Reach29d" },
            { "reach2d","Reach2d" },
            { "reach30d","Reach30d" },
            { "reach3d","Reach3d" },
            { "reach4d","Reach4d" },
            { "reach5d","Reach5d" },
            { "reach6d","Reach6d" },
            { "reach7d","Reach7d" },
            { "reach8d","Reach8d" },
            { "reach9d","Reach9d" },
            { "readyforprod","ReadyForProd" },
            { "region","Region" },
            { "relationshipid","RelationshipId" },
            { "relativeposition","RelativePosition" },
            { "reorderdata","ReorderData" },
            { "reportservicelevelid","ReportServiceLevelId" },
            { "reprocessoncurrentexecution","ReprocessOnCurrentExecution" },
            { "responseid","ResponseId" },
            { "resultblobpath","ResultBlobPath" },
            { "resultrowcount","ResultRowCount" },
            { "retailstoreid","RetailStoreId" },
            { "retries","Retries" },
            { "retrycount","RetryCount" },
            { "revenuesumofsquare","RevenueSumOfSquare" },
            { "rollupfromsamegrain","RollupFromSameGrain" },
            { "rootdir","RootDir" },
            { "rootnodeid","RootNodeId" },
            { "routingid","RoutingId" },
            { "rowcnt","RowCnt" },
            { "rowcount","RowCount" },
            { "rowid","RowId" },
            { "runid","RunId" },
            { "sosid","SOSId" },
            { "sosname","SOSName" },
            { "salescnt","SalesCnt" },
            { "saleshousecommissionpct","SalesHouseCommissionPct" },
            { "saleshousecustomerid","SalesHouseCustomerId" },
            { "saleslocationcode","SalesLocationCode" },
            { "samesectionctr","SameSectionCTR" },
            { "samesectionclickcnt","SameSectionClickCnt" },
            { "samesectioncnt","SameSectionCnt" },
            { "samesectionimpressioncnt","SameSectionImpressionCnt" },
            { "schedule","Schedule" },
            { "scheduleintervaltype","ScheduleIntervalType" },
            { "scheduleintervalvalue","ScheduleIntervalValue" },
            { "schedulestarttime","ScheduleStartTime" },
            { "schema","Schema" },
            { "schemaid","SchemaId" },
            { "searchabstoppositionsumofsquare","SearchAbsTopPositionSumOfSquare" },
            { "searchphrase","SearchPhrase" },
            { "searchtoppositionsumofsquare","SearchTopPositionSumOfSquare" },
            { "searchuniqueimpressioncnt","SearchUniqueImpressionCnt" },
            { "searchuniqueimpressionsumofsquare","SearchUniqueImpressionSumOfSquare" },
            { "seasonalityadjustmentdescription","SeasonalityAdjustmentDescription" },
            { "seasonalityadjustmentid","SeasonalityAdjustmentId" },
            { "seasonalityadjustmentname","SeasonalityAdjustmentName" },
            { "secondlevelcategory","SecondLevelCategory" },
            { "secondaryclickcnt","SecondaryClickCnt" },
            { "sellablenodeid","SellableNodeId" },
            { "sellablenodename","SellableNodeName" },
            { "sellername","SellerName" },
            { "servedimpressioncnt","ServedImpressionCnt" },
            { "servedrevenueamount","ServedRevenueAmount" },
            { "servicelevelid","ServiceLevelId" },
            { "servicetaskid","ServiceTaskId" },
            { "shardid","ShardId" },
            { "sharedproperties","SharedProperties" },
            { "shoppingabstoppositionsumofsquare","ShoppingAbsTopPositionSumOfSquare" },
            { "shoppinguniqueimpressioncnt","ShoppingUniqueImpressionCnt" },
            { "shoppinguniqueimpressionsumofsquare","ShoppingUniqueImpressionSumOfSquare" },
            { "shortheadline","ShortHeadline" },
            { "siteexclusionid","SiteExclusionId" },
            { "siteexclusionlistid","SiteExclusionListId" },
            { "siteexclusionlistitemdataid","SiteExclusionListItemDataId" },
            { "siteexclusionlistitemid","SiteExclusionListItemId" },
            { "siteexclusionlistname","SiteExclusionListName" },
            { "sitetype","SiteType" },
            { "siteurl","SiteUrl" },
            { "skippedviewcnt","SkippedViewCnt" },
            { "slotid","SlotId" },
            { "smartkeyword","SmartKeyword" },
            { "smartlistingid","SmartListingId" },
            { "smartlistingtypeid","SmartListingTypeId" },
            { "soldtopaymentinstrid","SoldToPaymentInstrId" },
            { "source","Source" },
            { "sourcehotelid","SourceHotelId" },
            { "sourceid","SourceId" },
            { "sourceisview","SourceIsView" },
            { "sourcelocationid","SourceLocationId" },
            { "sourcesystem","SourceSystem" },
            { "sourcetablename","SourceTableName" },
            { "sourcetrackingid","SourceTrackingId" },
            { "sourceverticalitemid","SourceVerticalItemId" },
            { "spendlimitid","SpendLimitId" },
            { "spendlimitlevelactiveflag","SpendLimitLevelActiveFlag" },
            { "spendlimitlevelid","SpendLimitLevelId" },
            { "stage1cnt","Stage1Cnt" },
            { "stage2cnt","Stage2Cnt" },
            { "stage3cnt","Stage3Cnt" },
            { "stage4cnt","Stage4Cnt" },
            { "stage5cnt","Stage5Cnt" },
            { "starrating","StarRating" },
            { "startdate","StartDate" },
            { "startoffset","StartOffset" },
            { "startrunid","StartRunId" },
            { "starttime","StartTime" },
            { "startedviewcnt","StartedViewCnt" },
            { "statelocationid","StateLocationId" },
            { "stateorprovince","StateOrProvince" },
            { "status","Status" },
            { "statusid","StatusId" },
            { "storeid","StoreId" },
            { "storetype","StoreType" },
            { "subcategory","SubCategory" },
            { "subgeographycode","SubGeographyCode" },
            { "subgeographyid","SubGeographyId" },
            { "subgeographylcid","SubGeographyLCID" },
            { "subgeographylatitude","SubGeographyLatitude" },
            { "subgeographylongitude","SubGeographyLongitude" },
            { "subgeographyname","SubGeographyName" },
            { "subgeographytype","SubGeographyType" },
            { "submatchtypeid","SubMatchTypeId" },
            { "subscriptions","Subscriptions" },
            { "suggestbidadjustmentmultiplier","SuggestBidAdjustmentMultiplier" },
            { "systemlimitlevelid","SystemLimitLevelId" },
            { "tableid","TableId" },
            { "tablename","TableName" },
            { "tagid","TagId" },
            { "tagline","Tagline" },
            { "targetadpositiontype","TargetAdPositionType" },
            { "targetaudienceid","TargetAudienceId" },
            { "targetcpa","TargetCPA" },
            { "targetcampaign","TargetCampaign" },
            { "targetcampaignid","TargetCampaignId" },
            { "targetcompanyid","TargetCompanyId" },
            { "targetcompanysizeid","TargetCompanySizeId" },
            { "targetcostpersale","TargetCostPerSale" },
            { "targetcountries","TargetCountries" },
            { "targetgroupdetailid","TargetGroupDetailId" },
            { "targetgroupid","TargetGroupId" },
            { "targetimpressionshare","TargetImpressionShare" },
            { "targetindustryid","TargetIndustryId" },
            { "targetjobfunctionid","TargetJobFunctionId" },
            { "targetkeywordmatchtype","TargetKeywordMatchType" },
            { "targetkeywordtext","TargetKeywordText" },
            { "targetlevel","TargetLevel" },
            { "targetlocation","TargetLocation" },
            { "targetlocationid","TargetLocationId" },
            { "targetlocationrestriction","TargetLocationRestriction" },
            { "targetorderid","TargetOrderId" },
            { "targetordername","TargetOrderName" },
            { "targetroas","TargetRoas" },
            { "targetsg","TargetSG" },
            { "targetshardgroup","TargetShardGroup" },
            { "targettablename","TargetTableName" },
            { "targettypebitmask","TargetTypeBitMask" },
            { "targettypeid","TargetTypeId" },
            { "targettypename","TargetTypeName" },
            { "targetvalueid","TargetValueId" },
            { "targetvaluename","TargetValueName" },
            { "targetedlocation","TargetedLocation" },
            { "targetedlocationtypeid","TargetedLocationTypeId" },
            { "taskid","TaskId" },
            { "taskname","TaskName" },
            { "taskstatus","TaskStatus" },
            { "tasktype","TaskType" },
            { "textequal","TextEqual" },
            { "threadcnt","ThreadCnt" },
            { "threadid","ThreadId" },
            { "thumbnailurl","ThumbnailUrl" },
            { "tileid","TileId" },
            { "timedelta","TimeDelta" },
            { "timezone","TimeZone" },
            { "timezoneid","TimeZoneId" },
            { "timestamp","Timestamp" },
            { "title","Title" },
            { "todatekey","ToDateKey" },
            { "topimpressioncnt","TopImpressionCnt" },
            { "toplevelcategory","TopLevelCategory" },
            { "topvsothername","TopVSOtherName" },
            { "topicid","TopicId" },
            { "topicname","TopicName" },
            { "totalamount","TotalAmount" },
            { "totalamountusd","TotalAmountUSD" },
            { "totalbookednights","TotalBookedNights" },
            { "totalbounces","TotalBounces" },
            { "totalconversioncnt","TotalConversionCnt" },
            { "totalconversioncredit","TotalConversionCredit" },
            { "totalconversioncreditsumofsquare","TotalConversionCreditSumOfSquare" },
            { "totalconversionsumofsquare","TotalConversionSumOfSquare" },
            { "totalcost","TotalCost" },
            { "totalduration","TotalDuration" },
            { "totalpages","TotalPages" },
            { "totalpartnerclick","TotalPartnerClick" },
            { "totalposition","TotalPosition" },
            { "totalprice","TotalPrice" },
            { "totalsearchabstopposition","TotalSearchAbsTopPosition" },
            { "totalsearchtopposition","TotalSearchTopPosition" },
            { "totalshoppingabstopposition","TotalShoppingAbsTopPosition" },
            { "totalslotposition","TotalSlotPosition" },
            { "totalvisits","TotalVisits" },
            { "totalwatchtime","TotalWatchTime" },
            { "trackconversion","TrackConversion" },
            { "trackhistory","TrackHistory" },
            { "trackingid","TrackingId" },
            { "trackingtemplate","TrackingTemplate" },
            { "trafficsplit","TrafficSplit" },
            { "treeversion","TreeVersion" },
            { "trustratingid","TrustRatingId" },
            { "undofailedentityvalueidcount","UndoFailedEntityValueIdCount" },
            { "undoid","UndoId" },
            { "undonedate","UndoneDate" },
            { "undoneentityvalueidcount","UndoneEntityValueIdCount" },
            { "unicodedestinationurl","UnicodeDestinationURL" },
            { "uniqueconversioncnt","UniqueConversionCnt" },
            { "uniqueconversioncredit","UniqueConversionCredit" },
            { "unknowncustomerconversioncredit","UnknownCustomerConversionCredit" },
            { "unknowncustomerrevenue","UnknownCustomerRevenue" },
            { "updatedtime","UpdatedTime" },
            { "url","Url" },
            { "usedbyassetgroupcount","UsedByAssetGroupCount" },
            { "usedbycount","UsedByCount" },
            { "userdim1id","UserDim1Id" },
            { "userdim2id","UserDim2Id" },
            { "userdim3id","UserDim3Id" },
            { "userdim4id","UserDim4Id" },
            { "userdim5id","UserDim5Id" },
            { "userid","UserId" },
            { "username","UserName" },
            { "version","Version" },
            { "verticalcampaignid","VerticalCampaignId" },
            { "verticalid","VerticalId" },
            { "verticalitemgroupid","VerticalItemGroupId" },
            { "verticalitemid","VerticalItemId" },
            { "verticalitemname","VerticalItemName" },
            { "verticallistingid","VerticalListingId" },
            { "videoid","VideoId" },
            { "viewcnt","ViewCnt" },
            { "viewthroughlookbackwindowinminutes","ViewThroughLookbackWindowinMinutes" },
            { "viewedimpressioncnt","ViewedImpressionCnt" },
            { "volume","Volume" },
            { "websitecountry","WebsiteCountry" },
            { "websitecoverage","WebsiteCoverage" },
            { "weekdayname","WeekDayName" },
            { "weekid","WeekId" },
            { "weeknuminyear","WeekNumInYear" },
            { "weekstartdate","WeekStartDate" },
            { "weekly","Weekly" },
            { "wordbreakerlcid","WordBreakerLCID" },
            { "yearnum","YearNum" },
            { "yearly","Yearly" },
            { "ziporpostalcode","ZipOrPostalCode" }
        };
    }
}
