﻿$targetPath = "."
$fileExtensions = @("*.sql", "*.cs", "*.md", "*.ps1", "*.py", "*.json", "*.config")  #
Get-ChildItem -Path $targetPath -Include $fileExtensions -Recurse -File | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $newContent = $content -replace "(?<!\r)\n", "`r`n"
    if ($content -ne $newContent) {
        $newContent | Set-Content -Path $_.FullName -NoNewline
        Write-Host "✅ : $($_.FullName)" -ForegroundColor Cyan
    } else {
        Write-Host "⏩ : $($_.FullName)" -ForegroundColor Gray
    }
}
Write-Host "`n✅ all don" -ForegroundColor Green