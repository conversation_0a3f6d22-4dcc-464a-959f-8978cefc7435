﻿namespace CampaignMangementUnitTests.V13.Operation.Translation
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.Api.Operations.ErrorCodeTranslator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ExtensionMethods;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.VisualStudio.TestTools.UnitTesting;

    [TestClass]
    public class V13_ErrorMappingTest : CampaignUnitTestBase
    {
        public TestContext TestContext { get; set; }

        [CssProjectStructure("vstfs:///Classification/Node/4ae2789d-4496-4ce1-a2c8-cbe69ceb4fe6"), TestMethod]
        public void MTErrorMappingTest()
        {
            var errorsExcludedFromAPIMapping = new List<CampaignManagementErrorCode>
            {
                CampaignManagementErrorCode.InvalidFileExtension,
                CampaignManagementErrorCode.AccountNotEnabledForResponsiveAdAssetPerformance,
                CampaignManagementErrorCode.DuplicateAssetsUnderFolder,
                CampaignManagementErrorCode.SendConsentModeEmailFailed,
                CampaignManagementErrorCode.ConsentModeObjectStoreFailed,
                CampaignManagementErrorCode.AccountNotEnabledForAssetLibraryV2,
                CampaignManagementErrorCode.AccountNotEnabledForAssetLibraryV3,
                CampaignManagementErrorCode.AccountNotEnabledForAssetLibraryV4,
                CampaignManagementErrorCode.AccountNotEnabledForAssetLibraryAIGC,
                CampaignManagementErrorCode.GenerateImagesPromptLengthInvalid,
                CampaignManagementErrorCode.GenerateImagesRequestCountLimitExceeded,
                CampaignManagementErrorCode.GenerateImagesPromptContentInvalid,
                CampaignManagementErrorCode.GenerateImagesAIGCNoRecommendation,
                CampaignManagementErrorCode.GenerateImagesAIGCNoRecommendationWithBrand,
                CampaignManagementErrorCode.GenerateImagesAIGCInvalidImageSize,
                CampaignManagementErrorCode.GenerateImagesAIGCInvalidImageContent,
                CampaignManagementErrorCode.GenerateImageAIGCInvalidUserPrompt,
                CampaignManagementErrorCode.GenerateImageAIGCInvalidGeneratedImage,
                CampaignManagementErrorCode.GenerateImagesPuidInvalid,
                CampaignManagementErrorCode.FolderCanNotMoveToTarget,
                CampaignManagementErrorCode.InvalidParentFolderId,
                CampaignManagementErrorCode.DuplicateFolderName,
                CampaignManagementErrorCode.InvalidFolderId,
                CampaignManagementErrorCode.FolderHasAssets,
                CampaignManagementErrorCode.FolderHasSubFolders,
                CampaignManagementErrorCode.FolderLimitExceededPerAccount,
                CampaignManagementErrorCode.InvalidFolderName,
                CampaignManagementErrorCode.DatabaseTimeout,
                CampaignManagementErrorCode.EntityTypeInvalid,
                CampaignManagementErrorCode.InvalidEnumValue,
                CampaignManagementErrorCode.DateTooOld,
                CampaignManagementErrorCode.CustomerBatchLimitExceeded,
                CampaignManagementErrorCode.EntityIdsNotPassed,
                CampaignManagementErrorCode.NegativeKeywordPartialError,
                CampaignManagementErrorCode.EntityIdInvalid,
                CampaignManagementErrorCode.EntityIdDuplicate,
                CampaignManagementErrorCode.InvalidGridFilterMetadataVersion,
                CampaignManagementErrorCode.CampaignAdvertiserChannelTypeShouldNotBeSet,
                CampaignManagementErrorCode.CampaignIdRequiredWhenAdgroupIdPassed,
                CampaignManagementErrorCode.CampaignProductFiltersAlreadyExist,
                CampaignManagementErrorCode.MaxAdGroupIdCountExceeded,
                CampaignManagementErrorCode.MaxTopNExceeded,
                CampaignManagementErrorCode.InvalidTopN,
                CampaignManagementErrorCode.AppInstallAdUpdateAppPlatformChanged,
                CampaignManagementErrorCode.AppInstallAdUpdateAppStoreIdChanged,
                CampaignManagementErrorCode.AppInstallAdAppMetaMaturityRatingTooMuchText,
                CampaignManagementErrorCode.AppMetadataIsNullOrEmpty,
                CampaignManagementErrorCode.AppInstallAdIsNotEnabled,
                CampaignManagementErrorCode.NoHeadlinesQualifiedForMMA,
                CampaignManagementErrorCode.RequiredAppMetadataPropertyIsNullOrEmpty,
                CampaignManagementErrorCode.InvalidAudienceTarget,
                CampaignManagementErrorCode.DuplicateAudienceTarget,
                CampaignManagementErrorCode.TargetsAudienceBidBatchLimitExceeded,
                CampaignManagementErrorCode.InvalidAudienceTargetBidAdjustment,
                CampaignManagementErrorCode.InvalidGeolocationsFileVersion,
                CampaignManagementErrorCode.InvalidLanguageLocale,
                CampaignManagementErrorCode.ImportCampaignInvalidSalesCountry,
                CampaignManagementErrorCode.ImportCampaignInvalidProductSalesChannel,
                CampaignManagementErrorCode.ImportOtherProductGroupWithinAdGroupHasError,
                CampaignManagementErrorCode.ImportPluginTimeOut,
                CampaignManagementErrorCode.FileImportTimeOut,
                CampaignManagementErrorCode.ImportTimedOutDuringBulkUpload,
                CampaignManagementErrorCode.ImportPluginThrowExceptionDuringProcess,
                CampaignManagementErrorCode.ImportPluginAnotherImportIsRunning,
                CampaignManagementErrorCode.ImportPluginImportIdIsNull,
                CampaignManagementErrorCode.ImportPluginImportIsCanceledByUser,
                CampaignManagementErrorCode.ImportPluginImportIsCanceledByOtherReason,
                CampaignManagementErrorCode.ImportPluginImportServiceCallTimeOut,
                CampaignManagementErrorCode.FileImportCommitTaskInitialImportStatusIsNotExpected,
                CampaignManagementErrorCode.InvalidPaginationLimits,
                CampaignManagementErrorCode.InvalidCultureInfoLCID,
                CampaignManagementErrorCode.UpdateFraudStatusRequestItemsIsNullOrEmpty,
                CampaignManagementErrorCode.ImageAdExtensionTooManyImages,
                CampaignManagementErrorCode.ImageAdExtensionImageMediaIdsNullOrEmpty,
                CampaignManagementErrorCode.AppAdExtensionVersionTooLong,
                CampaignManagementErrorCode.AppAdExtensionVersionInvalid,
                CampaignManagementErrorCode.AppAdExtensionDescriptionTooLong,
                CampaignManagementErrorCode.AppAdExtensionDescriptionInvalid,
                CampaignManagementErrorCode.AppAdExtensionMediaUrlTooLong,
                CampaignManagementErrorCode.AppAdExtensionMediaUrlInvalid,
                CampaignManagementErrorCode.AppAdExtensionAltTextTooLong,
                CampaignManagementErrorCode.AppAdExtensionAltTextInvalid,
                CampaignManagementErrorCode.AppAdExtensionUserRatingTooLong,
                CampaignManagementErrorCode.AppAdExtensionUserRatingInvalid,
                CampaignManagementErrorCode.AppAdExtensionMaturityRatingTooLong,
                CampaignManagementErrorCode.AppAdExtensionMaturityRatingInvalid,
                CampaignManagementErrorCode.AppAdExtensionAppStoreIdAppPlatformUpdateNotAllowed,
                CampaignManagementErrorCode.AddOrUpdateAcrossMultipleAdGroupsNotSupported,
                CampaignManagementErrorCode.ProductPartitionIsNull,
                CampaignManagementErrorCode.ProductDimensionIsNull,
                CampaignManagementErrorCode.ProductConditionOperandNotSupported,
                CampaignManagementErrorCode.ParentProductConditionMissing,
                CampaignManagementErrorCode.TooFewProductPartitionsForSubdivision,
                CampaignManagementErrorCode.ProductPartitionTypeUnitMustBeLeaf,
                CampaignManagementErrorCode.ProductPartitionTypeSubdivisionMustBeBiddable,
                CampaignManagementErrorCode.InvalidOrDuplicateProductPartitionsInSubdivision,
                CampaignManagementErrorCode.AddOrUpdateAcrossMultipleCriterionTypesNotSupported,
                CampaignManagementErrorCode.ProductPartitionDestinationUrlTooLong,
                CampaignManagementErrorCode.ProductPartitionParam1TooLong,
                CampaignManagementErrorCode.ProductPartitionParam2TooLong,
                CampaignManagementErrorCode.ProductPartitionParam3TooLong,
                CampaignManagementErrorCode.AllCriterionsMustHaveSameType,
                CampaignManagementErrorCode.AllCriterionsMustHaveSameBidType,
                CampaignManagementErrorCode.AllNullCriterionTypesNotAllowedOnCreate,
                CampaignManagementErrorCode.AdGroupCriterionDuplicateAdGroupId,
                CampaignManagementErrorCode.InvalidAdGroupCriterionCriterionBidType,
                CampaignManagementErrorCode.ProductPartitionDoesNotExist,
                CampaignManagementErrorCode.ProductGroupTooLong,
                CampaignManagementErrorCode.UploadFileRowCountExceeded,
                CampaignManagementErrorCode.UploadFileFormatNotSupported,
                CampaignManagementErrorCode.BulkEditActionCannotApplyToField,
                CampaignManagementErrorCode.BulkEditActionsForDifferentEntities,
                CampaignManagementErrorCode.BulkEditActionNotApplicable,
                CampaignManagementErrorCode.BulkEditParametersInvalid,
                CampaignManagementErrorCode.BulkEditDescriptionInvalid,
                CampaignManagementErrorCode.RelevanceOverrideAndRankingPenaltyIsNull,
                CampaignManagementErrorCode.CustomerNotAllowedToManipulateBlobNegativeKeywords,
                CampaignManagementErrorCode.TagsNotPassed,
                CampaignManagementErrorCode.InvalidTagName,
                CampaignManagementErrorCode.InvalidTagDescription,
                CampaignManagementErrorCode.InvalidTagTrackingCode,
                CampaignManagementErrorCode.InvalidTagId,
                CampaignManagementErrorCode.InvalidTagStatus,
                CampaignManagementErrorCode.TagIdsNotPassed,
                CampaignManagementErrorCode.DuplicateTagId,
                CampaignManagementErrorCode.TagsBatchSizeExceesdLimit,
                CampaignManagementErrorCode.TagIdDoesNotExist,
                CampaignManagementErrorCode.OnlyStatusCanBeUpdatedForLegacyTags,
                CampaignManagementErrorCode.TagWithSameNameAlreadyExistsUnderCustomer,
                CampaignManagementErrorCode.TagDoesNotExistForPassedGoal,
                CampaignManagementErrorCode.GoalWithSameNameAlreadyExistsUnderTag,
                CampaignManagementErrorCode.GoalsNotPassed,
                CampaignManagementErrorCode.GoalNameNullOrEmpty,
                CampaignManagementErrorCode.GoalNameHasInvalidCharacters,
                CampaignManagementErrorCode.GoalNameIsTooLong,
                CampaignManagementErrorCode.InvalidGoalLookbackWindow,
                CampaignManagementErrorCode.GoalIdsNotPassed,
                CampaignManagementErrorCode.GoalIsReadOnly,
                CampaignManagementErrorCode.GoalsBatchSizeExceedsLimit,
                CampaignManagementErrorCode.InvalidGoalStatus,
                CampaignManagementErrorCode.InvalidGoalEntityType,
                CampaignManagementErrorCode.InvalidGoalValue,
                CampaignManagementErrorCode.InvalidDestinationGoalExpressionOperator,
                CampaignManagementErrorCode.DestinationGoalUrlStringNullOrEmpty,
                CampaignManagementErrorCode.DestinationGoalUrlStringHasInvalidCharacters,
                CampaignManagementErrorCode.DestinationGoalUrlStringTooLong,
                CampaignManagementErrorCode.InvalidDurationGoalDuration,
                CampaignManagementErrorCode.InvalidDurationGoalValueOperator,
                CampaignManagementErrorCode.InvalidEventGoalCategory,
                CampaignManagementErrorCode.InvalidEventGoalCategoryOperator,
                CampaignManagementErrorCode.InvalidEventGoalAction,
                CampaignManagementErrorCode.InvalidEventGoalActionOperator,
                CampaignManagementErrorCode.InvalidEventGoalLabel,
                CampaignManagementErrorCode.InvalidEventGoalLabelOperator,
                CampaignManagementErrorCode.InvalidEventGoalValue,
                CampaignManagementErrorCode.InvalidEventGoalValueOperator,
                CampaignManagementErrorCode.InvalidIntentOption,
                CampaignManagementErrorCode.InvalidPageViewsPerVisitValueOperator,
                CampaignManagementErrorCode.InvalidPageViews,
                CampaignManagementErrorCode.InvalidGoalId,
                CampaignManagementErrorCode.DuplicateGoalId,
                CampaignManagementErrorCode.AppInstalGoalAppPlatformNullOrEmpty,
                CampaignManagementErrorCode.AppInstalGoalAppPlatformInvalid,
                CampaignManagementErrorCode.AppInstalGoalAppStoreIdNullOrEmpty,
                CampaignManagementErrorCode.AppInstalGoalAppStoreIdHasInvalidCharacters,
                CampaignManagementErrorCode.AppInstalGoalAppStoreIdTooLong,
                CampaignManagementErrorCode.AttributionModelTypeNotApplicableToGoalType,
                CampaignManagementErrorCode.AdExtensionItemIdInvalid,
                CampaignManagementErrorCode.InvalidExclusionTypeIdOrSubTypeId,
                CampaignManagementErrorCode.AnnotationOptOutTicketNumberTooLong,
                CampaignManagementErrorCode.AnnotationOptOutJustificationTextTooLong,
                CampaignManagementErrorCode.AnnotationOptOutDisplayUrlTooLong,
                CampaignManagementErrorCode.AnnotationOptOutBatchLimitExceeded,
                CampaignManagementErrorCode.AnnotationOptOutCollectionNullOrEmpty,
                CampaignManagementErrorCode.AnnotationOptOutJustificationTextNullOrEmpty,
                CampaignManagementErrorCode.AnnotationOptOutTicketNumberNullOrEmpty,
                CampaignManagementErrorCode.CustomerNotInAutomatedExtensionPilot,
                CampaignManagementErrorCode.AudiencesNotPassed,
                CampaignManagementErrorCode.AudienceBatchSizeExceedsLimit,
                CampaignManagementErrorCode.InvalidAudienceStatus,
                CampaignManagementErrorCode.InvalidAudienceDescription,
                CampaignManagementErrorCode.InvalidRemessagingAudience,
                CampaignManagementErrorCode.MdsServiceError,
                CampaignManagementErrorCode.DuplicateAudienceName,
                CampaignManagementErrorCode.CallToActionNotSupported,
                CampaignManagementErrorCode.CallToActionCallToActionLanguagePairNotSupported,
                CampaignManagementErrorCode.CallToActionTextInvalid,
                CampaignManagementErrorCode.InvalidAudienceTagId,
                CampaignManagementErrorCode.InvalidAudienceLookbackWindow,
                CampaignManagementErrorCode.AudienceCannotBeDeletedDueToExistingAssociation,
                CampaignManagementErrorCode.InvalidAudienceCustomEventValue,
                CampaignManagementErrorCode.InvalidAudienceCustomEventExression,
                CampaignManagementErrorCode.GoogleSyncUserIsNotAuthorized,
                CampaignManagementErrorCode.GoogleSyncInvalidCredentials,
                CampaignManagementErrorCode.GoogleSyncNotAdWordsUser,
                CampaignManagementErrorCode.GoogleSyncOAuthTokenRevoked,
                CampaignManagementErrorCode.GoogleSyncInvalidCustomerId,
                CampaignManagementErrorCode.GoogleSyncNone,
                CampaignManagementErrorCode.GoogleSyncOperationCanceled,
                CampaignManagementErrorCode.GoogleSyncUnknownError,
                CampaignManagementErrorCode.GoogleSyncWebExceptionNameResolutionFailure,
                CampaignManagementErrorCode.GoogleSyncWebExceptionProtocolError,
                CampaignManagementErrorCode.GoogleSyncWebExceptionKeepAlive,
                CampaignManagementErrorCode.GoogleSyncWebExceptionTimeout,
                CampaignManagementErrorCode.GoogleSyncWebExceptionProxyNameResolutionFailure,
                CampaignManagementErrorCode.GoogleSyncWebExceptionRequestProhibitedByProxy,
                CampaignManagementErrorCode.GoogleSyncWebExceptionOther,
                CampaignManagementErrorCode.GoogleSyncWebExceptionProxyAuthenticationRequired,
                CampaignManagementErrorCode.GoogleSyncQuotaNotAvailable,
                CampaignManagementErrorCode.GoogleSyncApiInternalError,
                CampaignManagementErrorCode.GoogleSyncSizeLimitError,
                CampaignManagementErrorCode.GoogleSyncTooMuchDataToTransmit,
                CampaignManagementErrorCode.GoogleSyncInvalidParameter,
                CampaignManagementErrorCode.GoogleSyncAccountNotSetUp,
                CampaignManagementErrorCode.GoogleSyncOAuth2RefreshTokenMissing,
                CampaignManagementErrorCode.GoogleSyncFeedItemHasInvalidCharacter,
                CampaignManagementErrorCode.GoogleTagManagerWorkspaceCouldNotBeCreated,
                CampaignManagementErrorCode.GoogleTagManagerPublishFailed,
                CampaignManagementErrorCode.GoogleTagManagerImportFailed,
                CampaignManagementErrorCode.GoogleTagManagerTagCreationFailed,
                CampaignManagementErrorCode.GoogleTagManagerTriggerCreationFailed,
                CampaignManagementErrorCode.GoogleTagManagerGetContainerVersionFailed,
                CampaignManagementErrorCode.GoogleTagManagerCreateVariableFailed,
                CampaignManagementErrorCode.GoogleTagManagerScheduledTaskExisted,
                CampaignManagementErrorCode.CustomerNotEnabledForGTMImport,
                CampaignManagementErrorCode.FacebookSyncTokenExpired,
                CampaignManagementErrorCode.FacebookSyncPermissionDenied,
                CampaignManagementErrorCode.FacebookSyncThrottlingLimitReached,
                CampaignManagementErrorCode.FacebookSyncApiInternalError,
                CampaignManagementErrorCode.PinterestSyncTokenExpired,
                CampaignManagementErrorCode.PinterestSyncPermissionDenied,
                CampaignManagementErrorCode.PinterestSyncThrottlingLimitReached,
                CampaignManagementErrorCode.PinterestSyncApiInternalError,
                CampaignManagementErrorCode.BingPlacesInvalidCredentials,
                CampaignManagementErrorCode.AccountNotEligibleForMSABingPlacesIntegration,
                CampaignManagementErrorCode.BingPlacesAPIInternalError,
                CampaignManagementErrorCode.BingPlacesMSAInvalidServiceTokenError,
                CampaignManagementErrorCode.BingPlacesMSAInvalidUserTokenError,
                CampaignManagementErrorCode.BingPlacesMSAAPIInvalidUserInputError,
                CampaignManagementErrorCode.BingPlacesMSABusinessListingForSearchQueryCouldNotBeFound,
                CampaignManagementErrorCode.BingPlacesMSAInternalError,
                CampaignManagementErrorCode.BingPlacesMSAInvalidAccessTokenError,
                CampaignManagementErrorCode.BingPlacesMSAInvalidAccountPropertyError,
                CampaignManagementErrorCode.BingPlacesMSACreateBusinessFailed,
                CampaignManagementErrorCode.BingPlacesMSAClaimBusinessFailed,
                CampaignManagementErrorCode.BingPlacesMSABusinessNameNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSAAddressLine1NullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSACityNameNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSAZipCodeNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSAStateIdNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSACountryCodeNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSAYpidNullOrEmpty,
                CampaignManagementErrorCode.BingPlacesMSAMarketNullOrEmpty,
                CampaignManagementErrorCode.AmazonImportInvalidCredentials,
                CampaignManagementErrorCode.GoogleMyBusinessImportUserError,
                CampaignManagementErrorCode.GoogleMyBusinessTransientError,
                CampaignManagementErrorCode.GoogleMyBusinessUnknownError,
                CampaignManagementErrorCode.AllNullCampaignCriterionTypesNotAllowedOnCreate,
                CampaignManagementErrorCode.InvalidCampaignCriterionType,
                CampaignManagementErrorCode.ProductCampaignCriterionCrierionIsNullOrEmpty,
                CampaignManagementErrorCode.ProductCampaignCriterionTooManyConditions,
                CampaignManagementErrorCode.MultiAccountBulkUploadFileSizeExceeded,
                CampaignManagementErrorCode.MultiAccountBulkUploadAccountIdColumnNotFound,
                CampaignManagementErrorCode.MultiAccountBulkUploadZipFileEmpty,
                CampaignManagementErrorCode.MultiAccountBulkUploadMultipleFilesInZipFile,
                CampaignManagementErrorCode.OfflineConversionUploadFileSizeExceeded,
                CampaignManagementErrorCode.OfflineConversionUploadAccountIdColumnNotFound,
                CampaignManagementErrorCode.OfflineConversionUploadZipFileEmpty,
                CampaignManagementErrorCode.OfflineConversionUploadMultipleFilesInZipFile,
                CampaignManagementErrorCode.OfflineConversionFileFormatInvalid,
                CampaignManagementErrorCode.OfflineConversionTimezoneInvalid,
                CampaignManagementErrorCode.OfflineConversionHeadersRowNotFound,
                CampaignManagementErrorCode.OfflineConversionColumnMicrosoftClickIdNotFound,
                CampaignManagementErrorCode.OfflineConversionColumnConversionNameNotFound,
                CampaignManagementErrorCode.OfflineConversionColumnConversionTimeNotFound,
                CampaignManagementErrorCode.OfflineConversionColumnConversionValueNotFound,
                CampaignManagementErrorCode.OfflineConversionColumnConversionCurrencyNotFound,
                CampaignManagementErrorCode.OfflineConversionTimeNullOrEmpty,
                CampaignManagementErrorCode.OfflineConversionTimeZoneUnspecified,
                CampaignManagementErrorCode.AnActiveRuleAlreadyExists,
                CampaignManagementErrorCode.ParentMappedTargetLocation,
                CampaignManagementErrorCode.BulkApiNotEnabledForPilot,
                CampaignManagementErrorCode.LanguageNotSupported,
                CampaignManagementErrorCode.MixedEntityTypesNotAllowed,
                CampaignManagementErrorCode.ParentCampaignIdInvalid,
                CampaignManagementErrorCode.ParentAdGroupIdInvalid,
                CampaignManagementErrorCode.FlexTagScriptShouldBeNullOrEmptyOnAddAndUpdate,
                CampaignManagementErrorCode.CampaignAnalyticsGoalNameShouldBeNullOrEmptyOnAddAndUpdate,
                CampaignManagementErrorCode.GoalStagesNotPassed,
                CampaignManagementErrorCode.GoalStagesCountExceedsLimit,
                CampaignManagementErrorCode.InvalidGoalStageType,
                CampaignManagementErrorCode.MultiStageGoalHasUnmatchedEventAttributes,
                CampaignManagementErrorCode.MultiStageGoalHasUnmatchedDestinationUrlAttributes,
                CampaignManagementErrorCode.CustomParametersNotFlattenedToDestinationUrl,
                CampaignManagementErrorCode.CannotUpdateBudgetAndCampaignInSameCall,
                CampaignManagementErrorCode.CampaignIsUsingSharedBudget,
                CampaignManagementErrorCode.AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut,
                CampaignManagementErrorCode.OnlyCampaignLevelNegativeDsaTargetRequestAllowed,
                CampaignManagementErrorCode.InvalidGoalValueType,
                CampaignManagementErrorCode.GoalEntityTypeNotInTypeFilter,
                CampaignManagementErrorCode.InvalidAppInstallGoalScope,
                CampaignManagementErrorCode.InvalidConversionCountType,
                CampaignManagementErrorCode.EventGoalExpressionWithOperatorNotPassed,
                CampaignManagementErrorCode.IllegalCampaignCriterionType,
                CampaignManagementErrorCode.DynamicSearchAdPath1Invalid,
                CampaignManagementErrorCode.DynamicSearchAdPath1TooLong,
                CampaignManagementErrorCode.DynamicSearchAdPath2Invalid,
                CampaignManagementErrorCode.DynamicSearchAdPath2TooLong,
                CampaignManagementErrorCode.DynamicSearchAdPath2SetWithoutPath1,
                CampaignManagementErrorCode.InvalidGoalValueCurrencyCode,
                CampaignManagementErrorCode.GoalValueCurrencyCodeShouldBeNull,
                CampaignManagementErrorCode.LocationIntentCriterionCannotBeDeleted,
                CampaignManagementErrorCode.ImportInvalidDeviceTargetBidAdjustment,
                CampaignManagementErrorCode.ImportInvalidExcelFileFormatHavingMultipleWorkSheets,
                CampaignManagementErrorCode.CanSetBidTargetToSiteOnlyForContentAdGroups,
                CampaignManagementErrorCode.SitePlacementsNotPassed,
                CampaignManagementErrorCode.SitePlacementUpdateEmpty,
                CampaignManagementErrorCode.CannotAddSiteToAdGroupTypeMismatch,
                CampaignManagementErrorCode.SitePlacementIdInvalid,
                CampaignManagementErrorCode.SitePlacementAlreadyExists,
                CampaignManagementErrorCode.SitePlacementIdDoesNotBelongToAdGroupId,
                CampaignManagementErrorCode.SitePlacementIsDeleted,
                CampaignManagementErrorCode.SitePlacementInInvalidStatus,
                CampaignManagementErrorCode.DuplicateSitePlacement,
                CampaignManagementErrorCode.InvalidPlacementId,
                CampaignManagementErrorCode.ExpandedTextAdDisplayUrlDomainTooLong,
                CampaignManagementErrorCode.ExpandedTextAdDisplayUrlDomainInvalid,
                CampaignManagementErrorCode.ExpandedTextAdDisplayUrlMissing,
                CampaignManagementErrorCode.OperationCancelled,
                CampaignManagementErrorCode.AdGroupMediumNotAllowedForTPCAds,
                CampaignManagementErrorCode.ImageAdUpdateHeightWidthRequiredWithAssetId,
                CampaignManagementErrorCode.ImageAdUpdateAssetIDRequiredWithHeightWidth,
                CampaignManagementErrorCode.ImageAdAssetIdInvalid,
                CampaignManagementErrorCode.MaxImageAdsLimitExceededForCustomer,
                CampaignManagementErrorCode.TPCAdAssetIdInvalid,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForAccountAssociation,
                CampaignManagementErrorCode.SegmentIdIsNull,
                CampaignManagementErrorCode.InvalidPartnerName,
                CampaignManagementErrorCode.AudienceTypeNotSupport,
                CampaignManagementErrorCode.CustomerNotEligibleForHotelAds,
                CampaignManagementErrorCode.TagIsReadOnly,
                CampaignManagementErrorCode.TooManyValuesInRequest,
                CampaignManagementErrorCode.TooManyValues,
                CampaignManagementErrorCode.DuplicateValues,
                CampaignManagementErrorCode.InvalidBidMultiplier,
                CampaignManagementErrorCode.ImmutableProperty,
                CampaignManagementErrorCode.InvalidSubAccount,
                CampaignManagementErrorCode.SubAccountWithNameAlreadyExists,
                CampaignManagementErrorCode.VerticalCampaignNotExist,
                CampaignManagementErrorCode.VerticalCampaignIsDeleted,
                CampaignManagementErrorCode.MaxActiveVerticalCampaignsLimitReached,
                CampaignManagementErrorCode.VerticalItemNotExist,
                CampaignManagementErrorCode.VerticalItemIsDeleted,
                CampaignManagementErrorCode.NoDefaultVerticalItemGroupExists,
                CampaignManagementErrorCode.VerticalItemGroupNotExist,
                CampaignManagementErrorCode.DuplicateSegmentId,
                CampaignManagementErrorCode.InvalidActionType,
                CampaignManagementErrorCode.DuplicateProductAudience,
                CampaignManagementErrorCode.AudienceIdNotMatchDeliveryChannelFilter,
                CampaignManagementErrorCode.AudienceDeliveryChannelNotMatchCampaignType,
                CampaignManagementErrorCode.InvalidHotelGroup,
                CampaignManagementErrorCode.HotelGroupWithNameAlreadyExists,
                CampaignManagementErrorCode.VerticalItemGroupIsDeleted,
                CampaignManagementErrorCode.DefaultVerticalItemGroupNotAllowedToUpdate,
                CampaignManagementErrorCode.ImportEmptyAccountName,
                CampaignManagementErrorCode.DuplicateHotelId,
                CampaignManagementErrorCode.UnsupportedAssociationType,
                CampaignManagementErrorCode.TooMuchKeywordDataToDownload,
                CampaignManagementErrorCode.TooMuchSearchTermDataToDownload,
                CampaignManagementErrorCode.TooMuchAudienceDataToDownload,
                CampaignManagementErrorCode.CloudImportTimeOut,
                CampaignManagementErrorCode.HotelGroupHasActiveAssociations,
                CampaignManagementErrorCode.ImportMismatchedAgeRangeTarget,
                CampaignManagementErrorCode.ImportEntityDemandLimitReached,
                CampaignManagementErrorCode.InvalidTipId,
                CampaignManagementErrorCode.ClarityEventNotFound,
                CampaignManagementErrorCode.CustomerNotEnabledForCountyTargets,
                CampaignManagementErrorCode.ImportFailedPreserveImportStatus,
                CampaignManagementErrorCode.CampaignInvalidShoppingCampaignSubType,
                CampaignManagementErrorCode.BidUpdatingNotAllowedForAutoBiddingKeyword,
                CampaignManagementErrorCode.BidUpdatingNotAllowedForAutoBiddingAdGroup,
                CampaignManagementErrorCode.BidUpdatingNotAllowedForAutoBiddingProductGroup,
                CampaignManagementErrorCode.CampaignCoOpCampaignShouldNotBeReturned,
                CampaignManagementErrorCode.InvalidProfileTargetBidAdjustment,
                CampaignManagementErrorCode.AdDisplayUrlDomainExtractionFailed,
                CampaignManagementErrorCode.ImportSubmitAlreadyInProgress,
                CampaignManagementErrorCode.BidUpdatingNotAllowedForAutoBiddingAutoTargets,
                CampaignManagementErrorCode.CustomerNotEligibleForFeedService,
                CampaignManagementErrorCode.NoEntityExistsInRequest,
                CampaignManagementErrorCode.DuplicateFeedMappingId,
                CampaignManagementErrorCode.DuplicateFeedTypeMapping,
                CampaignManagementErrorCode.DuplicateFeedAttributeId,
                CampaignManagementErrorCode.InvalidFeedAssociationType,
                CampaignManagementErrorCode.DuplicateFeedAssociation,
                CampaignManagementErrorCode.InvalidSearchBids,
                CampaignManagementErrorCode.InvalidContentBid,
                CampaignManagementErrorCode.InvalidColumnHeaderInFeedUploadFile,
                CampaignManagementErrorCode.BlankOrEmptyColumnHeaderInFeedUploadFile,
                CampaignManagementErrorCode.DuplicateColumnHeadersInFeedUploadFile,
                CampaignManagementErrorCode.RequiredColumnHeaderMissingInFeedUploadFile,
                CampaignManagementErrorCode.IncompatibleColumnHeaderInFeedUploadFile,
                CampaignManagementErrorCode.FeedUploadTaskTimeout,
                CampaignManagementErrorCode.InvalidFeedUploadFile,
                CampaignManagementErrorCode.InvalidFeedUploadFileExtension,
                CampaignManagementErrorCode.MultipleEntriesNotAllowdInZipfile,
                CampaignManagementErrorCode.MultipleWorksheetsNotAllowdInFeedUploadFile,
                CampaignManagementErrorCode.InvalidFeedUploadZippedFile,
                CampaignManagementErrorCode.FileFetchConnectionError,
                CampaignManagementErrorCode.FileFetchAuthenticationError,
                CampaignManagementErrorCode.FileFetchFilePathNotFound,
                CampaignManagementErrorCode.FileFetchFailedToOpenFile,
                CampaignManagementErrorCode.FileFetchPermissionLevelError,
                CampaignManagementErrorCode.FileFetchNoSuchHost,
                CampaignManagementErrorCode.CoOpSettingBidOptionInvalid,
                CampaignManagementErrorCode.CoOpSettingBidBoostValueInvalid,
                CampaignManagementErrorCode.CoOpSettingBidMaxValueInvalid,
                CampaignManagementErrorCode.CustomerNotEnableForInHousePromotion,
                CampaignManagementErrorCode.MissingFeedAttributeColumnInFeedUploadFile,
                CampaignManagementErrorCode.DuplicateFeedItemRowInFile,
                CampaignManagementErrorCode.DuplicateFeedItemRowWithSameKeyAttributes,
                CampaignManagementErrorCode.InvalidRowInFeedUploadFile,
                CampaignManagementErrorCode.FeedAssociationDoesNotExists,
                CampaignManagementErrorCode.DuplicateFeedMappingForOneFeedType,
                CampaignManagementErrorCode.InvalidFeedAttributeId,
                CampaignManagementErrorCode.KeyFeedItemAttributeValueConfliction,
                CampaignManagementErrorCode.FeedAssociationLimitationReached,
                CampaignManagementErrorCode.DuplicateFeedUploadHistroyRecord,
                CampaignManagementErrorCode.FeedUploadFileRowCountLimitationExceed,
                CampaignManagementErrorCode.DuplicateFeedItemIdInFeedUpdateFile,
                CampaignManagementErrorCode.InvalidActionInFeedUpdateFile,
                CampaignManagementErrorCode.FeedItemIdIsNotAllowedForCreation,
                CampaignManagementErrorCode.CoOpAdGroupShouldNotBeReturned,
                CampaignManagementErrorCode.FeedUploadHistoryNotFound,
                CampaignManagementErrorCode.OperationOnInvalidFeedUploadApplyStatus,
                CampaignManagementErrorCode.DuplicateApplyOperationOnFeedUpload,
                CampaignManagementErrorCode.RejectOngoingApplyOperationOnFeedUpload,
                CampaignManagementErrorCode.InvalidFeedUploadHistory,
                CampaignManagementErrorCode.OriginalFeedAttributesCannotBeDeleted,
                CampaignManagementErrorCode.PerformanceTargetTemplatesEntityLimitExceeded,
                CampaignManagementErrorCode.PerformanceTargetTemplateNameDuplicate,
                CampaignManagementErrorCode.PerformanceTargetTemplateListIsNullOrEmpty,
                CampaignManagementErrorCode.PerformanceTargetTemplateIsNull,
                CampaignManagementErrorCode.PerformanceTargetTypeInvalid,
                CampaignManagementErrorCode.PerformanceTargetTemplateIdShouldBeNullOnAdd,
                CampaignManagementErrorCode.PerformanceTargetTemplateNameInvalid,
                CampaignManagementErrorCode.PerformanceTargetTemplateNameLengthExceeded,
                CampaignManagementErrorCode.PerformanceTargetTemplateStartDateIsNull,
                CampaignManagementErrorCode.PerformanceTargetTemplateEndDateIsNull,
                CampaignManagementErrorCode.PeriodPerformanceTargetTemplateStartDateShouldBeNull,
                CampaignManagementErrorCode.PeriodPerformanceTargetTemplateEndDateShouldBeNull,
                CampaignManagementErrorCode.PerformanceTargetTemplateDateRangeInvalid,
                CampaignManagementErrorCode.PerformanceTargetMetricListIsNullOrEmpty,
                CampaignManagementErrorCode.PerformanceTargetMetricTypeInvalid,
                CampaignManagementErrorCode.PerformanceTargetMetricTypeDuplicate,
                CampaignManagementErrorCode.InvalidTimeZone,
                CampaignManagementErrorCode.PerformanceTargetPilotNotEnabledForCustomer,
                CampaignManagementErrorCode.PerformanceTargetTemplateIdInvalid,
                CampaignManagementErrorCode.PerformanceTargetTypeUpdateNotSupported,
                CampaignManagementErrorCode.OnlyOneProductScopeCampaignCriterionIsAllowed,
                CampaignManagementErrorCode.UnSupportedProductTypeForCampaignProductFilter,
                CampaignManagementErrorCode.PerformanceTargetTemplateDoesNotExist,
                CampaignManagementErrorCode.PerformanceTargetTemplateIsDeleted,
                CampaignManagementErrorCode.PerformanceTargetTemplateIdDuplicate,
                CampaignManagementErrorCode.PerformanceTargetTemplateIsPaused,
                CampaignManagementErrorCode.PerformanceTargetTemplateStatusInvalid,
                CampaignManagementErrorCode.PerformanceTargetTemplateUpdateEmpty,
                CampaignManagementErrorCode.CampaignsPerPerformanceTargetLimitExceeded,
                CampaignManagementErrorCode.MultiAccountBulkUploadAccountCountExceeded,
                CampaignManagementErrorCode.SortExpressionColumnIsNotSupported,
                CampaignManagementErrorCode.ProductPartitionNodeBidBelowServing,
                CampaignManagementErrorCode.InvalidImportPayload,
                CampaignManagementErrorCode.NoAvailableFeedMapping,
                CampaignManagementErrorCode.PageFeedUrlShouldBeOneOfKeys,
                CampaignManagementErrorCode.MITaskTimeOut,
                CampaignManagementErrorCode.MaxFeedIdCountExceeded,
                CampaignManagementErrorCode.PerformanceTargetSegmentationNotSupported,
                CampaignManagementErrorCode.ParentMappedInMarketAudienceTarget,
                CampaignManagementErrorCode.AttributeReferencedInAd,
                CampaignManagementErrorCode.InvalidCampaignCriterionStatus,
                CampaignManagementErrorCode.StartDateComesAfterEndDate,
                CampaignManagementErrorCode.MissingTargetKeywordMatchType,
                CampaignManagementErrorCode.HavingBothTargetKeywordAndTargetKeywordText,
                CampaignManagementErrorCode.UpdateCampaignTypeAsSearchForExistingDSACampaignNotAllowed,
                CampaignManagementErrorCode.UpdateCampaignTypeAsDSAForExistingSearchCampaignNotAllowed,
                CampaignManagementErrorCode.TextAssetDoesNotExist,
                CampaignManagementErrorCode.TextAssetsNotPassed,
                CampaignManagementErrorCode.TextAssetLimitReachedForAccount,
                CampaignManagementErrorCode.HavingBothTargetLocationIdAndTargetLocation,
                CampaignManagementErrorCode.UserTokenInvalid,
                CampaignManagementErrorCode.OnlyAllowedToChangeAssetText,
                CampaignManagementErrorCode.BidStrategyUnchangedBecauseNoEnoughConversions,
                CampaignManagementErrorCode.CampaignAlreadyHasAdGroupAudienceCriterion,
                CampaignManagementErrorCode.AdGroupAlreadyHasCampaignAudienceCriterion,
                CampaignManagementErrorCode.AIMCampaignLevelAudienceTargetingNotEnabled,
                CampaignManagementErrorCode.DuplicateCampaignCriterionAudienceAssociation,
                CampaignManagementErrorCode.IllegalAudienceAssociationConversionFromExclusion,
                CampaignManagementErrorCode.CustomerNotEligibleForSharedLibrary,
                CampaignManagementErrorCode.SmartCampaignNotEnabledForAccount,
                CampaignManagementErrorCode.DuplicateBusinessId,
                CampaignManagementErrorCode.BusinessIdInvalid,
                CampaignManagementErrorCode.BusinessIdsNotPassed,
                CampaignManagementErrorCode.SmartListingsNotPassed,
                CampaignManagementErrorCode.InvalidSmartListingCategoryCount,
                CampaignManagementErrorCode.SmartListingProductOrServiceMaxLimitReached,
                CampaignManagementErrorCode.BusinessesNotPassed,
                CampaignManagementErrorCode.SmartCampaignNotEnabled,
                CampaignManagementErrorCode.SmartCampaignNotEnabledForCustomer,
                CampaignManagementErrorCode.DuplicateProductOrService,
                CampaignManagementErrorCode.InvalidPublisherCountryForLanguage,
                CampaignManagementErrorCode.CustomerNotEligibleForEnhancedResponsiveAd,
                CampaignManagementErrorCode.ResponsiveAdInvalidImage,
                CampaignManagementErrorCode.ResponsiveAdDuplicateImage,
                CampaignManagementErrorCode.ResponsiveAdRequiredImageMissing,
                CampaignManagementErrorCode.GoogleSyncTwoStepVerificationNotEnrolled,
                CampaignManagementErrorCode.EntityExclusionBatchLimitExceeded,
                CampaignManagementErrorCode.EntityExclusionBatchLimitExceeded,
                CampaignManagementErrorCode.CampaignCriterionAudienceAssociationNotExist,
                CampaignManagementErrorCode.ImportShoppingCampaignHaveNoSetting,
                CampaignManagementErrorCode.ImportShoppingCampaignPriorityInvalid,
                CampaignManagementErrorCode.UnSupportSmartShoppingCampaignPriorityAndChangeToHighPriority,
                CampaignManagementErrorCode.CampaignDescriptionHasInvalidChars,
                CampaignManagementErrorCode.CampaignDescriptionMissing,
                CampaignManagementErrorCode.CampaignDescriptionTooLong,
                CampaignManagementErrorCode.CannotSpecifyDatatypeOfBuiltInAttributeInHeader,
                CampaignManagementErrorCode.AllLanguageNotSupportedForSmartCampaign,
                CampaignManagementErrorCode.MultiLanguageNotSupportedForSmartCampaign,
                CampaignManagementErrorCode.SharedBudgetNotSupportedForSmartCampaign,
                CampaignManagementErrorCode.MaxCpcShouldBeNullForSmartCampaign,
                CampaignManagementErrorCode.EntityNotAllowedForSmartCampaign,
                CampaignManagementErrorCode.SmartCampaignSubtypeNotMatchCampaignType,
                CampaignManagementErrorCode.BusinessNameInvalid,
                CampaignManagementErrorCode.BusinessWebsiteInvalid,
                CampaignManagementErrorCode.SearchPhrasesNullOrEmpty,
                CampaignManagementErrorCode.SearchPhraseDuplicate,
                CampaignManagementErrorCode.SearchPhraseAlreadyPaused,
                CampaignManagementErrorCode.SearchPhraseAlreadyActive,
                CampaignManagementErrorCode.SearchPhraseInvalidStatus,
                CampaignManagementErrorCode.SearchPhraseInvalidText,
                CampaignManagementErrorCode.PausedSearchPhrasesAccountLimitExceeded,
                CampaignManagementErrorCode.SharedEntityDoesNotBelongToTheCustomer,
                CampaignManagementErrorCode.SharedEntityIsDeleted,
                CampaignManagementErrorCode.SharedEntityDoesNotExist,
                CampaignManagementErrorCode.SharedEntityIsNotSharedToTheCustomerOrAccount,
                CampaignManagementErrorCode.InvalidCriterions,
                CampaignManagementErrorCode.CustomerNotEligibleForCustomerMatch,
                CampaignManagementErrorCode.CustomerNotEligibleForCustomerMatchCRM,
                CampaignManagementErrorCode.InvalidCustomerListActionType,
                CampaignManagementErrorCode.InvalidCustomerListFileIdentifier,
                CampaignManagementErrorCode.BingPlacesImportUserError,
                CampaignManagementErrorCode.BingPlacesImportOtherError,
                CampaignManagementErrorCode.InvalidFeedPhoneNumber,
                CampaignManagementErrorCode.InvalidFeedYear,
                CampaignManagementErrorCode.InvalidFeedEmailAddress,
                CampaignManagementErrorCode.InvalidDynamicDataFeedRatings,
                CampaignManagementErrorCode.InvalidDynamicDataFeedHotelStarRating,
                CampaignManagementErrorCode.InvalidDynamicDataFeedFuelType,
                CampaignManagementErrorCode.InvalidDynamicDataFeedVehicleState,
                CampaignManagementErrorCode.InvalidDynamicDataFeedMileageUnit,
                CampaignManagementErrorCode.InvalidDynamicDataFeedTransmission,
                CampaignManagementErrorCode.InvalidDynamicDataFeedBodyStyle,
                CampaignManagementErrorCode.InvalidDynamicDataFeedDriveTrain,
                CampaignManagementErrorCode.InvalidDynamicDataFeedCondition,
                CampaignManagementErrorCode.InvalidDynamicDataAutoAggregateFeedMetric,
                CampaignManagementErrorCode.InvalidDynamicDataFeedDuration,
                CampaignManagementErrorCode.DynamicDataExtensionFeedIdInvalid,
                CampaignManagementErrorCode.DynamicDataExtensionFeedIdAlreadyUsed,
                CampaignManagementErrorCode.DynamicDataExtensionFeedDataUpdateNotAllowed,
                CampaignManagementErrorCode.DynamicDataExtensionInvalidFeedType,
                CampaignManagementErrorCode.CustomerNotEligibleForDisclaimerAds,
                CampaignManagementErrorCode.ExperimentEventInvalid,
                CampaignManagementErrorCode.ExperimentEventBatchLimitExceeded,
                CampaignManagementErrorCode.ExperimentEventTypeIsInvalid,
                CampaignManagementErrorCode.ExperimentOrBaseCampaignWasModified,
                CampaignManagementErrorCode.ExperimentEventWasModified,
                CampaignManagementErrorCode.BaseCampaignHadOtherUnendedEvents,
                CampaignManagementErrorCode.FeedScheduleAlreadyExists,
                CampaignManagementErrorCode.InvalidProductConversionGoal,
                CampaignManagementErrorCode.FeedUploadScheduleUnableToFetchFile,
                CampaignManagementErrorCode.AdExtensionIdToSubAccountIdAssociationsNotPassed,
                CampaignManagementErrorCode.AdExtensionIdToSubAccountIdAssociationNotPassed,
                CampaignManagementErrorCode.DuplicateAdExtensionIdToSubAccountIdAssociation,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForSubAccountAssociation,
                CampaignManagementErrorCode.AdExtensionIdToHotelGroupIdAssociationsNotPassed,
                CampaignManagementErrorCode.AdExtensionIdToHotelGroupIdAssociationNotPassed,
                CampaignManagementErrorCode.DuplicateAdExtensionIdToHotelGroupIdAssociation,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForHotelGroupAssociation,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForBIData,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForSubAccountOrHotelGroupAssociationsBySubAccount,
                CampaignManagementErrorCode.InvalidAdExtensionTypeForHotelGroupAssociationsByHotelGroup,
                CampaignManagementErrorCode.FeedUploadScheduleUsernameRequiredForPassword,
                CampaignManagementErrorCode.FeedUploadScheduleUsernamePasswordHaveInvalidCharacters,
                CampaignManagementErrorCode.AssociationsLimitExceededPerAdExtensionTypeForEntity,
                CampaignManagementErrorCode.InvalidStockImageVendor,
                CampaignManagementErrorCode.EntitiesNotPassed,
                CampaignManagementErrorCode.EntityLimitExceeded,
                CampaignManagementErrorCode.InvalidStockImageId,
                CampaignManagementErrorCode.AssetIsNotStockImageAsset,
                CampaignManagementErrorCode.UnableToLicenseAsset,
                CampaignManagementErrorCode.FeedUploadScheduleInvalidUrl,
                CampaignManagementErrorCode.ShoppingSmartAdsBidAdjustmentNotSupported,
                CampaignManagementErrorCode.ShoppingSmartAdsEntityNotSupported,
                CampaignManagementErrorCode.OnlyOneActiveNodeIsAllowed,
                CampaignManagementErrorCode.CustomerNotInGoogleImportApiPilot,
                CampaignManagementErrorCode.CustomerNotInFileImportApiPilot,
                CampaignManagementErrorCode.TaskNameInvalid,
                CampaignManagementErrorCode.TaskSchedulingInvalid,
                CampaignManagementErrorCode.AccountReparentingValidationFailure,
                CampaignManagementErrorCode.InvalidAudienceSets,
                CampaignManagementErrorCode.UnableToDownloadAssetFromUrl,
                CampaignManagementErrorCode.AudienceShouldNotBeAccountLevel,
                CampaignManagementErrorCode.FeedItemIdNotGenerated,
                CampaignManagementErrorCode.FeedItemBlobIdNotGenerated,
                CampaignManagementErrorCode.FeedItemBlobBlobStorageError,
                CampaignManagementErrorCode.InvalidFeedObject,
                CampaignManagementErrorCode.SmartShoppingUpdateInvalidDeleteItIfWantImportSuccess,
                CampaignManagementErrorCode.NoteCreationPartiallySucceeded,
                CampaignManagementErrorCode.InvalidDuration,
                CampaignManagementErrorCode.VideoOverweight,
                CampaignManagementErrorCode.DuplicateVideo,
                CampaignManagementErrorCode.VideoTranscodingError,
                CampaignManagementErrorCode.InvalidVideoStatusForThisOperation,
                CampaignManagementErrorCode.AccountNotEnabledForVideoRepair,
                CampaignManagementErrorCode.VideoDownloadNotAllowed,
                CampaignManagementErrorCode.ImpressionTrackingUrlInvalid,
                CampaignManagementErrorCode.BlobPurgeWatermarkNotUpdate,
                CampaignManagementErrorCode.BlobSoftDeleteNonzeroRefCount,
                CampaignManagementErrorCode.CompactJobSetFailureOrTimeout,
                CampaignManagementErrorCode.LBStartedDuringCompactJob,
                CampaignManagementErrorCode.CompactJobSetInvalidFeedItemId,
                CampaignManagementErrorCode.InvalidDynamicDataFeedHealthInsuranceOrganizationCategory,
                CampaignManagementErrorCode.ImpressionTrackingUrlsExceedMaxCount,
                CampaignManagementErrorCode.StockImageLimitExceededPerCustomer,
                CampaignManagementErrorCode.AutomaticallyAssociateUETTag,
                CampaignManagementErrorCode.InvalidSeedAudienceId,
                CampaignManagementErrorCode.SmartPageLimitExceeded,
                CampaignManagementErrorCode.SmartPageListIsNullOrEmpty,
                CampaignManagementErrorCode.SmartPageMissingSiteSuffix,
                CampaignManagementErrorCode.SmartPageSiteSuffixAlreadyExists,
                CampaignManagementErrorCode.SmartPageInvalidSiteSuffix,
                CampaignManagementErrorCode.SmartPageIsNull,
                CampaignManagementErrorCode.SmartPageInvalidStatus,
                CampaignManagementErrorCode.SmartPageInvalidSubdomain,
                CampaignManagementErrorCode.CustomerNotInPilotForSmartPage,
                CampaignManagementErrorCode.SmartPageSubdomainIsNotAvailable,
                CampaignManagementErrorCode.SmartPagePreviewIsNotAvailable,
                CampaignManagementErrorCode.SmartPageInvalidCoverImageUrl,
                CampaignManagementErrorCode.SmartPageInvalidGalleryImageUrl,
                CampaignManagementErrorCode.SmartPageInvalidLogoImageUrl,
                CampaignManagementErrorCode.SmartPageGalleryImagesLimitExceeded,
                CampaignManagementErrorCode.SmartPageInvalidSocialUrl,
                CampaignManagementErrorCode.SmartPageMissingDayInBusinessHours,
                CampaignManagementErrorCode.SmartPageInvalidBusinessHours,
                CampaignManagementErrorCode.SmartPageIncorrectSocialUrlFormat,
                CampaignManagementErrorCode.SmartPageServicesLimitExceeded,
                CampaignManagementErrorCode.SmartPageServicesInvalidName,
                CampaignManagementErrorCode.SmartPageServicesInvalidDescription,
                CampaignManagementErrorCode.SmartPageUpdateNotSupported,
                CampaignManagementErrorCode.SmartPageGetFbPageUserError,
                CampaignManagementErrorCode.SmartPageGetFbPageApiError,
                CampaignManagementErrorCode.SmartPageIdIsInvalid,
                CampaignManagementErrorCode.SmartPageConcurrentUpdatingIsNotAllowed,
                CampaignManagementErrorCode.SmartPageIncompleteInfoProvided,
                CampaignManagementErrorCode.SmartPageTableStorageError,
                CampaignManagementErrorCode.SmartPageAssociationListIsNullOrEmpty,
                CampaignManagementErrorCode.SmartPageAssociationInvalidStatus,
                CampaignManagementErrorCode.DuplicateSmartPageAssociation,
                CampaignManagementErrorCode.SmartPageAssociationInvalidType,
                CampaignManagementErrorCode.SmartPageSubdomainExceedsMaxLength,
                CampaignManagementErrorCode.SmartPageFbImportParamMissing,
                CampaignManagementErrorCode.SmartPageFbImportNoTokenFoundInKeyVault,
                CampaignManagementErrorCode.SmartPageSubdomainChangeIsNotAllowed,
                CampaignManagementErrorCode.SmartPageInvalidAction,
                CampaignManagementErrorCode.SmartPageAnalyticsTagIdCannotBeUpdated,
                CampaignManagementErrorCode.SmartPageUrlIsRejectedByEditorial,
                CampaignManagementErrorCode.SmartPageGetFbPageOAuthError,
                CampaignManagementErrorCode.SmartPageCustomDomainIsNotAvailable,
                CampaignManagementErrorCode.SmartPagePreviousCustomDomainIsNotFreedUp,
                CampaignManagementErrorCode.SmartPagePreviousSubdomainIsNotFreedUp,
                CampaignManagementErrorCode.SmartPageM365AdminApiError,
                CampaignManagementErrorCode.SmartPageM365OAuthError,
                CampaignManagementErrorCode.SmartPageInvalidM365TokenInKeyVault,
                CampaignManagementErrorCode.SmartPageM365TokenIsNullInKeyVault,
                CampaignManagementErrorCode.SmartPageUpdateDomainWithin24HoursNotAllow,
                CampaignManagementErrorCode.SmartPageDnsARecordConfliction,
                CampaignManagementErrorCode.SmartPageDnsCNAMERecordConfliction,
                CampaignManagementErrorCode.SmartPageACMEClientError,
                CampaignManagementErrorCode.SmartPageCustomDomainVerifyError,
                CampaignManagementErrorCode.SmartPageCustomDomainSaveCertToKeyVaultError,
                CampaignManagementErrorCode.SmartPageCustomDomainDeleteCertFromKeyVaultError,
                CampaignManagementErrorCode.SmartPageCustomDomainSaveNginxConfigError,
                CampaignManagementErrorCode.SmartPageCustomDomainUpdateMappingError,
                CampaignManagementErrorCode.AzureMediaServiceError,
                CampaignManagementErrorCode.VideoBlobStorageError,
                CampaignManagementErrorCode.ImpressionTrackingUrlInaccessible,
                CampaignManagementErrorCode.AccountCustomColumnLimitExceeded,
                CampaignManagementErrorCode.CustomerCustomColumnLimitExceeded,
                CampaignManagementErrorCode.CustomColumnIdIsInvalid,
                CampaignManagementErrorCode.CustomColumnIsUnsupported,
                CampaignManagementErrorCode.CustomColumnIsInvalid,
                CampaignManagementErrorCode.CustomColumnExpressionIsTooLong,
                CampaignManagementErrorCode.SmartGoalShouldBeAccountLevel,
                CampaignManagementErrorCode.SmartGoalShouldBeOnlyOne,
                CampaignManagementErrorCode.SmartGoalCouldNotBeEditInSomeParameters,
                CampaignManagementErrorCode.SmartGoalCouldNotBeCreatedByCustomer,
                CampaignManagementErrorCode.AccountNotEligibleForSmartGoal,
                CampaignManagementErrorCode.UrlNotAccessible,
                CampaignManagementErrorCode.BlockedByCloudflare,
                CampaignManagementErrorCode.UrlSSLVerificationFailed,
                CampaignManagementErrorCode.UrlScanFailed,
                CampaignManagementErrorCode.VideoDownloadError,
                CampaignManagementErrorCode.VideoSourceIsNull,
                CampaignManagementErrorCode.VideoUrlDataIsNull,
                CampaignManagementErrorCode.VideoDescriptionDataIsNull,
                CampaignManagementErrorCode.VideoUrlTextTooLong,
                CampaignManagementErrorCode.VideoDescriptionTextTooLong,
                CampaignManagementErrorCode.VideoSourceLimitExceeded,
                CampaignManagementErrorCode.CampaignServiceAdvertisingChannelTypeIdInvalid,
                CampaignManagementErrorCode.ResponsiveAdInvalidVideo,
                CampaignManagementErrorCode.ResponsiveAdDuplicateVideo,
                CampaignManagementErrorCode.ResponsiveAdRequiredVideoMissing,
                CampaignManagementErrorCode.CampaignBudgetIsMissingOrNotCalculable,
                CampaignManagementErrorCode.BidStrategyUpdatedButBidNotEnabledForUpdate,
                CampaignManagementErrorCode.InvalidPlaceholderStoreId,
                CampaignManagementErrorCode.AveragePositionIsDeprecated,
                CampaignManagementErrorCode.VerifiedTrackingDataInvalid,
                CampaignManagementErrorCode.InvalidImportRecommendationId,
                CampaignManagementErrorCode.OnlyOneInStoreVisitGoalBeAllowedPerCustomer,
                CampaignManagementErrorCode.InvalidStoreVisitsConversionGoal,
                CampaignManagementErrorCode.InStoreVisitGoalShoudBeExcludeFromBidding,
                CampaignManagementErrorCode.InStoreVisitGoalShouldBeAcrossAllAccounts,
                CampaignManagementErrorCode.UnsupportedAdExtensionPropertyValue,
                CampaignManagementErrorCode.DuplicateVideoResource,
                CampaignManagementErrorCode.InvalidVideoResource,
                CampaignManagementErrorCode.InvalidIsClarityTagCheckBoxValue,
                CampaignManagementErrorCode.UpdateFailedForIsClarityTagEnabled,
                CampaignManagementErrorCode.DeleteFailedForClarityProject,
                CampaignManagementErrorCode.InvalidVideoEditingParam,
                CampaignManagementErrorCode.AccountNotEnabledForVideoAdEditor,
                CampaignManagementErrorCode.AccountNotEnabledForVideoAdsEditorCropClip,
                CampaignManagementErrorCode.AccountNotEnabledForVideoAdsEditorCaption,
                CampaignManagementErrorCode.AccountNotEligibleForDynamicDescription,
                CampaignManagementErrorCode.CrossAccountPortfolioBidStrategyAreNotSupported,
                CampaignManagementErrorCode.BusinessAttributesValueInvalid,
                CampaignManagementErrorCode.AccountNotEnabledForBusinessAttributes,
                CampaignManagementErrorCode.SystemGeneratedListCouldNotBeDeleted,
                CampaignManagementErrorCode.SystemGeneratedListIsReadOnly,
                CampaignManagementErrorCode.SystemGeneratedListCouldNotBeAdded,
                CampaignManagementErrorCode.CustomerNotEligibleForSystemGeneratedAudience,
                CampaignManagementErrorCode.CustomerNotEligibleForEnhancedProductAdsFilter,
                CampaignManagementErrorCode.ProductConditionOperatorInvalid,
                CampaignManagementErrorCode.ProductConditionOperatorNotSupportedForThisCampaignType,
                CampaignManagementErrorCode.ProductPartitionExcludedDueToNoActiveAds,
                CampaignManagementErrorCode.NoGtinMappingFound,
                CampaignManagementErrorCode.AmazonSyncThrottlingLimitReached,
                CampaignManagementErrorCode.AmazonSyncApiInternalError,
                CampaignManagementErrorCode.AmazonSyncAccessDenied,
                CampaignManagementErrorCode.InvalidPpsTaskType,
                CampaignManagementErrorCode.PpsReconciliationReportUploadFileSizeExceeded,
                CampaignManagementErrorCode.PpsReconciliationReportUploadBlobDownloadError,
                CampaignManagementErrorCode.PpsReconciliationReportUploadBlobUploadError,
                CampaignManagementErrorCode.PpsReconciliationReportUploadZipFileEmpty,
                CampaignManagementErrorCode.PpsReconciliationReportFileFormatInvalid,
                CampaignManagementErrorCode.PpsReconciliationReportFileMissingHeaderRow,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelIDNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelNameNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelAddressNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelCityNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelStateRegionNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelPostalCodeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelCountryCodeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnHotelPhoneNumberNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingReferenceNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingDateandTimeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnCheckinDateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnCheckoutDateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnNumberofRoomsNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnNumberofGuestsNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingRevenueNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingRevenueCurrencyNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingRevenueCurrencytoBillingCurrencyConversionRateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBookingStatusNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnCommissionNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnCommissionCurrencyNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnCommissionCurrencytoBillingCurrencyConversionRateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnPaymentDateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnPaymentStatusNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportServiceFeeColumnsPartiallyNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportParseFailedInvalidValuesInDataRows,
                CampaignManagementErrorCode.PpsReconciliationReportColumnServiceFeeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnServiceFeeCurrencyNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnServiceFeeCurrencytoBillingCurrencyConversionRateNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnTotalPaymentNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnBillingCurrencyNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnProcessedDateTimeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnStatusNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnErrorNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnAccountIdNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportColumnUploadDateTimeNotFound,
                CampaignManagementErrorCode.PpsReconciliationReportProcessedAccountIdsMismatch,
                CampaignManagementErrorCode.PpsReconciliationReportBlobStorageError,
                CampaignManagementErrorCode.PpsReconciliationReportTaskCreationError,
                CampaignManagementErrorCode.PpsReconciliationReportInvalidProcessedDateTimeFormat,
                CampaignManagementErrorCode.PpsReconciliationReportEOError,
                CampaignManagementErrorCode.RSAAdCustomizerPilotNotEnabledForAccount,
                CampaignManagementErrorCode.AccountNotEnabledForHotelCampaign,
                CampaignManagementErrorCode.CampaignIsNotOfTypeHotel,
                CampaignManagementErrorCode.InvalidHotelListingType,
                CampaignManagementErrorCode.AdGroupCriterionHotelListingIsNull,
                CampaignManagementErrorCode.InvalidHotelListingOperand,
                CampaignManagementErrorCode.FinalUrlAndMobileUrlNotAllowedForHotelListing,
                CampaignManagementErrorCode.DuplicateRootNodeForHotelListingTree,
                CampaignManagementErrorCode.ParentHotelListingGroupNodeDoesNotExist,
                CampaignManagementErrorCode.HeightOfHotelListingTreeExceeededLimit,
                CampaignManagementErrorCode.HotelListingOperandUnderSubDivisionMustBeSame,
                CampaignManagementErrorCode.DuplicateHotelListing,
                CampaignManagementErrorCode.InvalidHotelListingHierarchy,
                CampaignManagementErrorCode.HotelListingGroupLimitExceededForAdGroup,
                CampaignManagementErrorCode.InvalidHotelListingAttribute,
                CampaignManagementErrorCode.HotelListingAttributeNullOrEmpty,
                CampaignManagementErrorCode.HotelListingAttributeTooLong,
                CampaignManagementErrorCode.InvalidAdGroupCriterionRateBidValue,
                CampaignManagementErrorCode.HotelListingEverythingElseMissing,
                CampaignManagementErrorCode.InvalidLocationIdForHotelListing,
                CampaignManagementErrorCode.InvalidLocationNodeMissingParentLocation,
                CampaignManagementErrorCode.InvalidLocationNodeInvalidParentLocation,
                CampaignManagementErrorCode.InvalidLocationNodeMissingParentOperandNode,
                CampaignManagementErrorCode.InvalidLocationNodeSubLocationAttachedToEENode,
                CampaignManagementErrorCode.InvalidLocationIdForOperandType,
                CampaignManagementErrorCode.InvalidBidTypeForCampaignBiddingScheme,
                CampaignManagementErrorCode.CampaignAssociationsLimitExceeded,
                CampaignManagementErrorCode.InvalidSeasonalityAdjustmentId,
                CampaignManagementErrorCode.InvalidAssociation,
                CampaignManagementErrorCode.SeasonalityAdjustmentTimestampMismatch,
                CampaignManagementErrorCode.RSAAdCustomizerAttributeTypeChangedInUpdate,
                CampaignManagementErrorCode.RSAAdCustomizerAttributeCountMoreThanLimit,
                CampaignManagementErrorCode.RSAAdCustomizerInvalidAttributeType,
                CampaignManagementErrorCode.CustomerNotEligibleForRemarketingListBasedParameters,
                CampaignManagementErrorCode.AttributeNameLengthExceeded,
                CampaignManagementErrorCode.WebpageCriterionWebpageConditionOperatorInvalid,
                CampaignManagementErrorCode.WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain,
                CampaignManagementErrorCode.AccountIsNotInPilotForWebpageCriterionWebpageConditionOperatorOrUrlEquals,
                CampaignManagementErrorCode.InvalidDynamicDataFeedToursAndActivitiesPriceType,
                CampaignManagementErrorCode.InvalidAdcustomizerAttributeId,
                CampaignManagementErrorCode.CustomerNotEligibleForM365Integration,
                CampaignManagementErrorCode.ResponsiveAdInvalidHeadlines,
                CampaignManagementErrorCode.ResponsiveAdInvalidLongHeadlines,
                CampaignManagementErrorCode.ResponsiveAdInvalidDescriptions,
                CampaignManagementErrorCode.AssetCropSettingInvalid,
                CampaignManagementErrorCode.AssetPlacementSettingInvalid,
                CampaignManagementErrorCode.AssetHasCropTaskInProgress,
                CampaignManagementErrorCode.MediaLibraryNotEnabledForPilot,
                CampaignManagementErrorCode.McaAccountTierInvalid,
                CampaignManagementErrorCode.AttributeNameMissing,
                CampaignManagementErrorCode.EmptySmartListingText,
                CampaignManagementErrorCode.SmartListingTextTooLong,
                CampaignManagementErrorCode.InvalidDynamicDataFeedNumOfReviews,
                CampaignManagementErrorCode.DatabaseReadOnly,
                CampaignManagementErrorCode.PinterestImportInvalidCredentials,
                CampaignManagementErrorCode.PinterestSyncApiInternalError,
                CampaignManagementErrorCode.PinterestSyncConflict,
                CampaignManagementErrorCode.PinterestSyncImportNotFound,
                CampaignManagementErrorCode.PinterestSyncInvalidParams,
                CampaignManagementErrorCode.PinterestSyncPermissionDenied,
                CampaignManagementErrorCode.PinterestSyncThrottlingLimitReached,
                CampaignManagementErrorCode.PinterestSyncTokenExpired,
                CampaignManagementErrorCode.InvalidDynamicDataFeedSponsored,
                CampaignManagementErrorCode.InvalidDynamicDataFeedLocationTargetingScope,
                CampaignManagementErrorCode.InvalidLeadGenSetting,
                CampaignManagementErrorCode.CampaignLeadGenSettingIsImmutable,
                CampaignManagementErrorCode.LeadGenCampaignOnlyAllowCPM,
                CampaignManagementErrorCode.CustomerIsNotAllowedForHotSpots,
                CampaignManagementErrorCode.InvalidHotSpot,
                CampaignManagementErrorCode.HotSpotMissingRequiredField,
                CampaignManagementErrorCode.InvalidGlyph,
                CampaignManagementErrorCode.HotSpotsHasDuplication,
                CampaignManagementErrorCode.InvalidPlacement,
                CampaignManagementErrorCode.DuplicatePlacement,
                CampaignManagementErrorCode.InvalidBoostAnchors,
                CampaignManagementErrorCode.UnsupportedCampaignTypeForBoostAccount,
                CampaignManagementErrorCode.BidAdjustmentNotSupportedForBoostAccount,
                CampaignManagementErrorCode.OptimizedTargetingMustBeTurnOnForBoost,
                CampaignManagementErrorCode.InvalidImportPreferenceBulkUpdateTaskType,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateUploadFileSizeExceeded,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateBlobDownloadError,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateBlobUploadError,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateFileFormatInvalid,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateFileMissingHeaderRow,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateColumnTaskIdNotFound,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateColumnAccountIdNotFound,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateColumnPreferenceNotFound,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateColumnImportTypeNotFound,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateParseFailedInvalidValuesInDataRows,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateFileIsEmpty,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateSmartImportWillChangeToExpertImport,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateBlobDownloadError,
                CampaignManagementErrorCode.ImportPreferenceBulkUpdateBlobUploadError,
                CampaignManagementErrorCode.SmartCamapignCannotBeAssociatedWithAudience,
                CampaignManagementErrorCode.CantDistinguishUploadedConversionType,
                CampaignManagementErrorCode.UploadedConversionFileHeadersRowNotFound,
                CampaignManagementErrorCode.OnlineConversionInvalidHeader,
                CampaignManagementErrorCode.OnlineConversionTransactionIdNotFound,
                CampaignManagementErrorCode.OnlineConversionConversionTimeNotExpected,
                CampaignManagementErrorCode.OnlineConversionCanOnlyBeAdjusted,
                CampaignManagementErrorCode.CantGetRowCountFromUploadedFile,
                CampaignManagementErrorCode.UploadedConversionFileTimeZoneInvalid,
                CampaignManagementErrorCode.UploadedConversionFileIsEmpty,
                CampaignManagementErrorCode.InvalidBoostPlacementSetting,
                CampaignManagementErrorCode.AccountNotEligibleForBoostTargeting,
                CampaignManagementErrorCode.StockImageServiceError,
                CampaignManagementErrorCode.ImmutableBiddingScheme,
                CampaignManagementErrorCode.GoalLevelCannotBeChanged,
                CampaignManagementErrorCode.InvalidDynamicDataFeedSponsor,
                CampaignManagementErrorCode.EligibleTargetNeededForOptimizedTargetingOptout,
                CampaignManagementErrorCode.UnsupportedLanguage,
                CampaignManagementErrorCode.LanguageMismatchUserAd,
                CampaignManagementErrorCode.InvalidContextualTargetBidAdjustment,
                CampaignManagementErrorCode.InvalidGenreTargetBidAdjustment,
                CampaignManagementErrorCode.CustomerNotEligibleForDeletingUETTags,
                CampaignManagementErrorCode.TagCannotBeDeletedForHasSharedLibrary,
                CampaignManagementErrorCode.TagCannotBeDeletedForHasAudience,
                CampaignManagementErrorCode.TagCannotBeDeletedForHasGoal,
                CampaignManagementErrorCode.LeadFormExtensionNotFound,
                CampaignManagementErrorCode.OnlyMaxClicksBiddingSchemeForLeadFormExtension,
                CampaignManagementErrorCode.MissingMcaPerfData,
                CampaignManagementErrorCode.AccountNotEligibleForFrequencyCap,
                CampaignManagementErrorCode.InvalidCampaignSubtypeForFrequencyCap,
                CampaignManagementErrorCode.InvalidFrequencyCapSettings,
                CampaignManagementErrorCode.AccountNotEnabledForSearchCampaignPredictiveTargeting,
                CampaignManagementErrorCode.AccountNotEnabledForCampaignAutomatedCallToActionOptOut,
                CampaignManagementErrorCode.CampaignAutomatedCallToActionOptOutNotAllowedForCampaignType,
                CampaignManagementErrorCode.CampaignCallToActionOptOutNotAllowedForCampaignType,
                CampaignManagementErrorCode.AccountNotEnabledForCampaignCallToActionOptOut,
                CampaignManagementErrorCode.LogoExtensionLogoTooSmall,
                CampaignManagementErrorCode.LogoExtensionLogoTooLarge,
                CampaignManagementErrorCode.LogoExtensionLogoNotSquare,
                CampaignManagementErrorCode.AdditionalSegmentNotAllowedWithVideoAdsSegmentation,
                CampaignManagementErrorCode.AdditionalSegmentNotAllowedWithCountryOfSaleSegmentation,
                CampaignManagementErrorCode.IsWebInsightsEnabledMissing,
                CampaignManagementErrorCode.UnauthorizedOrNotFoundForWebInsights,
                CampaignManagementErrorCode.ClarityServiceError,
                CampaignManagementErrorCode.GetTileDataError,
                CampaignManagementErrorCode.GetTotalSessionsTileError,
                CampaignManagementErrorCode.GetQuickBacksTileError,
                CampaignManagementErrorCode.GetAverageActiveTileError,
                CampaignManagementErrorCode.GetDeviceTileError,
                CampaignManagementErrorCode.GetCountryTileError,
                CampaignManagementErrorCode.GetPageUrlTileError,
                CampaignManagementErrorCode.VideoDurationIsInvalidForDeal,
                CampaignManagementErrorCode.WebInsightsEnabledTimeMissing,
                CampaignManagementErrorCode.CustomerDoesNotHaveAnyAccounts,
                CampaignManagementErrorCode.QueueMutliChannelAccountStatusError,
                CampaignManagementErrorCode.NotSupportAudienceSharedLibrary,
                CampaignManagementErrorCode.OneOrMoreGooglePlaceIdsCouldNotBeResolved,
                CampaignManagementErrorCode.CampaignNotUseMaxClick,
                CampaignManagementErrorCode.NotEligibleToSwitchBackBiddingScheme,
                CampaignManagementErrorCode.VcClientConnectionError,
                CampaignManagementErrorCode.VcClientDirectoryExists,
                CampaignManagementErrorCode.VcClientDownloadError,
                CampaignManagementErrorCode.VcClientGetDirectoryInfo,
                CampaignManagementErrorCode.VcClientStreamExistsError,
                CampaignManagementErrorCode.ReadSummaryBlobFailed,
                CampaignManagementErrorCode.NoMappedMicrosoftAppFound,
                CampaignManagementErrorCode.DuplicateCampaignForTheSameMicrosoftApp,
                CampaignManagementErrorCode.MSANAWFMetricsPilotNotEnabled,
                CampaignManagementErrorCode.VideoMimeTypeCannotResolve,
                CampaignManagementErrorCode.UnsupportedDealFilter,
                CampaignManagementErrorCode.UnsupportedDealSorter,
                CampaignManagementErrorCode.InputOutputCountMismatch,
                CampaignManagementErrorCode.LogoExtensionMissingBusinessLogo,
                CampaignManagementErrorCode.LogoExtensionMissingBusinessName,
                CampaignManagementErrorCode.ConversionValueRuleEnabled,
                CampaignManagementErrorCode.ExceedsMaximumNumberOfRules,
                CampaignManagementErrorCode.RuleIdNotFound,
                CampaignManagementErrorCode.AudienceTypeMismatch,
                CampaignManagementErrorCode.ConditionOverlap,
                CampaignManagementErrorCode.LocationHierarchyIssue,
                CampaignManagementErrorCode.AccountNotEnabledForBroadMatchOnlyCampaign,
                CampaignManagementErrorCode.BidStrategyNotSupportedForBroadMatchOnlyCampaign,
                CampaignManagementErrorCode.UnsupportMatchTypeForBroadMatchOnlyCampaign,
                CampaignManagementErrorCode.InvalidPredictiveMatchingSetting,
                CampaignManagementErrorCode.EntityIsEmptyOrNull,
                CampaignManagementErrorCode.EmptyPropertyNotAllowed,
                CampaignManagementErrorCode.CurrencyCodeShouldNotBeNullForAdd,
                CampaignManagementErrorCode.PrimaryConditionShouldNotBeNull,
                CampaignManagementErrorCode.DuplicatedRuleId,
                CampaignManagementErrorCode.LocationTypeMismatch,
                CampaignManagementErrorCode.ConditionTypeNotAllowed,
                CampaignManagementErrorCode.LocationHierarchyIssue,
                CampaignManagementErrorCode.DuplicateRuleName,
                CampaignManagementErrorCode.AIGCException,
                CampaignManagementErrorCode.AssetAIEnhancementOptoutAcceptedValueInvalid,
                CampaignManagementErrorCode.AudienceCannotBeDeletedDueToConversionValueRule,
                CampaignManagementErrorCode.CNResellerCodeDoesNotExist,
                CampaignManagementErrorCode.TagCannotBeDeletedDueToExistingSystemGeneratedListAssociation,
                CampaignManagementErrorCode.GeneratePuidInfoError,
                CampaignManagementErrorCode.VideoAdsMetricsPilotNotEnabled,
                CampaignManagementErrorCode.PMaxMetricsNotEnabled,
                CampaignManagementErrorCode.SystemGeneratedAdExtensionCouldNotBeAssociated,
                CampaignManagementErrorCode.SystemGeneratedAdExtensionCouldNotBeModified,
                CampaignManagementErrorCode.HotelCampaignDeprecated,
                CampaignManagementErrorCode.CustomSegmentNotPassed,
                CampaignManagementErrorCode.CustomSegmentBatchSizeExceedsLimit,
                CampaignManagementErrorCode.InvalidCustomSegmentName,
                CampaignManagementErrorCode.CustomSegmentCatalogIsTooLarge,
                CampaignManagementErrorCode.CustomSegmentCatalogIsEmpty,
                CampaignManagementErrorCode.CustomSegmentCannotFindCatalog,
                CampaignManagementErrorCode.CustomerIsNotEligibleForKeywordTargeting,
                CampaignManagementErrorCode.CustomerIsNotEligibleForImpressionBasedRemarketingList,
                CampaignManagementErrorCode.CustomerCannotOperateImpressionBasedRemarketingList,
                CampaignManagementErrorCode.InvalidEntityTypeForImpressionBasedRemarketingList,
                CampaignManagementErrorCode.InvalidEntityIdForImpressionBasedRemarketingList,
                CampaignManagementErrorCode.ImpressionBasedRemarketingListCanOnlyBeEditedByCreator,
                CampaignManagementErrorCode.ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator,
                CampaignManagementErrorCode.AssetDescriptionTooLong,
                CampaignManagementErrorCode.InvalidROIForAsset,
                CampaignManagementErrorCode.VideoMetadataSaveToBlobFailed,
                CampaignManagementErrorCode.CallToActionAriaTextInvalid,
                CampaignManagementErrorCode.CallToActionAriaTextNotSupported,
                CampaignManagementErrorCode.DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList,
                CampaignManagementErrorCode.AutoGoalEnabledValueInvalid,
                CampaignManagementErrorCode.CustomerNotEligibleForAutoConversion,
                CampaignManagementErrorCode.InvalidGoalSource,
                CampaignManagementErrorCode.GoalTagCannotBeChanged,
                CampaignManagementErrorCode.GoalCategoryCannotBeChanged,
                CampaignManagementErrorCode.AccountNotEnabledForPerformanceMaxCampaignAssetGroupSearchTheme,
                CampaignManagementErrorCode.FreeStockImageDownloadNotAllowed,
                CampaignManagementErrorCode.UsingTruncatedHeadlinesForMMA,
                CampaignManagementErrorCode.SearchThemeEntityLimitExceeded,
                CampaignManagementErrorCode.TooLongSearchTheme,
                CampaignManagementErrorCode.SearchThemeNameMissing,
                CampaignManagementErrorCode.SearchThemeNameHasInvalidChars,
                CampaignManagementErrorCode.DuplicateSearchThemeName,
                CampaignManagementErrorCode.AccountNotEnabledForDoubleVerifyMeasurement,
                CampaignManagementErrorCode.InvalidDoubleVerifyLinkToken,
                CampaignManagementErrorCode.ImageAIUpScaleFailed,
                CampaignManagementErrorCode.UnableToPerformDoubleVerifyAdvertiserLink,
                CampaignManagementErrorCode.UnableToPerformDoubleVerifyAccountUnlink,
                CampaignManagementErrorCode.CustomerNotBePilotedForUpliftReport,
                CampaignManagementErrorCode.GenerateUpliftReportFail,
                CampaignManagementErrorCode.BlobUploadFailedForUpliftReport,
                CampaignManagementErrorCode.GetUpliftReportStatusFail,
                CampaignManagementErrorCode.RemappedGoogleNegativeContentLabelToBlockedSegments,
                CampaignManagementErrorCode.XandrInvestServiceError,
                CampaignManagementErrorCode.AccountIdCannotBeNullForUPUetLinking,
                CampaignManagementErrorCode.UniversalPixelUuidCannotBeNull,
                CampaignManagementErrorCode.UniversalPixelCannotLinkToTwoTags,
                CampaignManagementErrorCode.UPUetLinkFailed,
                CampaignManagementErrorCode.UPUetUnlinkFailed,
                CampaignManagementErrorCode.OneUPUetLinkAtOneTime,
                CampaignManagementErrorCode.TargetCpaLowerThanDailyTargetBudgetAmount,
                CampaignManagementErrorCode.PerformanceMaxAssetGroupFinalURLExtractTrackingParametersEnabled,
                CampaignManagementErrorCode.UniversalPixelNotFound,
                CampaignManagementErrorCode.XandrMemberNotFoundOrInvalid,
                CampaignManagementErrorCode.XandrAdvertiserNotFoundOrInvalid,
                CampaignManagementErrorCode.XandrTokenNotFoundOrInvalid,
                CampaignManagementErrorCode.AccountNotMappedToXandr,
                CampaignManagementErrorCode.CustomerNotMappedToXandr,
                CampaignManagementErrorCode.AssetGroupUrlTargetDuplicated,
                CampaignManagementErrorCode.AssetGroupUrlTargetValueDuplicated,
                CampaignManagementErrorCode.AssetGroupUrlTargetConditionInvalid,
                CampaignManagementErrorCode.AssetGroupUrlTargetOperatorInvalid,
                CampaignManagementErrorCode.AssetGroupUrlTargetValueInvalid,
                CampaignManagementErrorCode.AssetGroupUrlTargetInvalid,
                CampaignManagementErrorCode.CampaignSkippedDueToUserPreference,
                CampaignManagementErrorCode.AdsSkippedDueToUserPreference,
                CampaignManagementErrorCode.WriteConsentModeDecisionFailed,
                CampaignManagementErrorCode.ConsentModeDecisionAlreadyExist,
                CampaignManagementErrorCode.CampaignLifetimeBudgetAmountIsAboveLimit, 
                CampaignManagementErrorCode.PerformanceMaxEnhanceHTTPFinalURLEnabled,
                CampaignManagementErrorCode.EntityCountExceededForImpressionBasedRemarketingList,
                CampaignManagementErrorCode.GoalApplicationPlatformCannotBeChanged,
                CampaignManagementErrorCode.GoalApplicationStoreIdCannotBeChanged,
                CampaignManagementErrorCode.InvalidColumn,
                CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal,
        };

            var errorsNotMappedInApi = new List<CampaignManagementErrorCode>();

            var mtErrorsForApiMapping =
                Enum.GetValues(typeof(CampaignManagementErrorCode))
                    .Cast<CampaignManagementErrorCode>()
                    .Except(errorsExcludedFromAPIMapping);

            foreach (CampaignManagementErrorCode campaignMTErrorCodeEnum in mtErrorsForApiMapping)
            {
                try
                {
                    if (!CampaignManagmentMTErrorMapping.ApiErrorExists(campaignMTErrorCodeEnum))
                    {
                        errorsNotMappedInApi.Add(campaignMTErrorCodeEnum);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine("Error mapping fails for Campaign MT error code: {0} ({1}) with exception {2}", campaignMTErrorCodeEnum, (int)campaignMTErrorCodeEnum, ex.Message);
                    Assert.Fail("Error mapping fails for Campaign MT error codes");
                }
            }

            Assert.IsFalse(
                errorsNotMappedInApi.Any(),
                @"API Error mapping is missing for Campaign MT errors below. Either fix API mapping or update this test collection [errorsExcludedFromAPiMapping] to exclude these errors from test:
{0}",
                errorsNotMappedInApi.ToDelimitedString(err => "CampaignManagementErrorCode." + err, ",\r\n", maxCount: 300));
        }
    }
}
