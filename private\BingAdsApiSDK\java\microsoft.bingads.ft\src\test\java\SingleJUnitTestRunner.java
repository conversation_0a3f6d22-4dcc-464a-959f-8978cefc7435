import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


import org.junit.runner.JUnitCore;
import org.junit.runner.Request;
import org.junit.runner.Result;
import org.junit.runner.notification.Failure;

import com.microsoft.bingads.internal.ServiceUtils;

/**
 * SingleJunitTestRunner Implement calling the JUnit test by method For example:
 * java -cp classpth SingleJunitTestRunner className#methodName
 */
public class SingleJUnitTestRunner {
    public static void main(String[] args)
            throws ClassNotFoundException, IllegalAccessException, IllegalArgumentException, InvocationTargetException,
            NoSuchMethodException, SecurityException, ExecutionException, InterruptedException {
        ExecutorService pool = Executors.newSingleThreadExecutor();

        try {
            String[] classAndMethod = args[0].split("#");

            System.out
                    .println(String.format("+++++ test started. PropertyFile: %s; className: %s; methodName: %s +++++",
                            ServiceUtils.getPropertyFileName(), classAndMethod[0], classAndMethod[1]));

			try
			{
				System.out.println("Set Trace On");
                System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
			}
			catch (Exception e)
			{
				System.out.println("Hit exception when init message handler");
			}
			catch (Error err)
			{
				System.out.println("Hit error when init message handler");
			}
            System.out.println(String.format("start to load className: %s methodName: %s",
                            classAndMethod[0], classAndMethod[1]));
            Request request = Request.method(Class.forName(classAndMethod[0]), classAndMethod[1]);
            System.out.println(String.format("successfully load className: %s methodName: %s",
                            classAndMethod[0], classAndMethod[1]));
             JUnitCore jc = new JUnitCore();

            System.out.println("submit job to run JUnit request");
            Future<Result> future = pool.submit(new Callable<Result>() {
                @Override
                public Result call() throws Exception {
                    return jc.run(request);
                }

            });

            System.out.println("wait for completeness");
            // Result result = jc.run(request);
            Result result = future.get(600, TimeUnit.SECONDS);
            if (result.getFailureCount() > 0) {
                for (Failure f : result.getFailures()) {
                    System.out.println("trace:" + f.getTrace());
                    System.out.println("message: " + f.getMessage());
                    System.out.println("Description: " + f.getDescription());
                }
                System.out.println("Test Failed, FailureCount: " + result.getFailureCount());
            } else {
                System.out.println("Test Passed");
            }
        } catch (TimeoutException ex) {
            System.out.println("Test Failed, TimeOut!");
        } catch (Throwable ex) {
            System.out.println("Test Failed! " + ex.getMessage() );
        } finally {
            pool.shutdown();
            System.exit(0);
        }
    }
}
