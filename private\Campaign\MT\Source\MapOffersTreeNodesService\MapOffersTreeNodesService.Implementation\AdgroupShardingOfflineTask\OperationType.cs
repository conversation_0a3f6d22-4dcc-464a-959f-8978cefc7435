﻿namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask
{
    internal enum OperationType
    {
        Migration = 0,

        // Don't change below numbers. They have to be consistent with DB OperationType table
        KeywordLimit = 1,
        AdLimit = 2,
        OrderLimit = 3,
        OrderDelete = 4,
        OpcCampaignId = 5,
        OpcOrderId = 6,
        AdExOrderEditorialStatus = 8,
        CampaignDelete = 10,
        AllEntityLimit = 11,
        CampaignActive = 12,
        OrderAudienceLimit = 13,
        // CustomerAllEntityLimit = 14, This is deprecated and not in use
        // NonLibraryTargetGroupAssociation = 15, This is deprecated and not in use 
        LabelDelete = 16,
        LabelIdLevelRecount = 17,
        AccountLevelLabelRecount = 18,
        //SitelinksAdExtensionMigrationAdExOrderEditorialStatus = 19, //  SiteLink Migration is complete
        // CampaignMetadataMigration = 20, This is deprecated and not in use
        AccountLevelLabelsPerEntityRecount = 21,
        LabelIdLevelLabelsPerEntityRecount = 22,
        AccountDeleteForPurge = 24,
        OrderEditorialStatusQueueProcessor = 25,
        NegativeKeywordLimit = 26,
        AdexOrderEditorialExpending = 28,
        AimOrderLevelPrivacyCheck = 29,
        AudienceIdLevelRecount = 30,
        AccountLevelAudienceRecount = 31,
        AudienceDelete = 32,
        CampaignLevelOrderTargetSizeRecount = 33,
        // CustomerLevelOrderItemRecount = 34, This task is no longer required due to change in Account reparenting design
        // CampaignLevelTargetSizeRecount = 36, // this is deprecated and not in use
        PauseSharedLibraryEntityAssociations = 38,
        TagUsedByCustomerGoalRecount = 39,
        TagUsedByCustomerRemarketingListRecount = 40,
        AudienceLevelUsedbyAdGroupRecount = 41,
        TagUsedByCustomerProductAudienceRecount = 42,
        SharedEntityUsedByRecount = 43,
        PauseGoalForAccount = 44,
        PauseAudienceAssociationForAccount = 45,
        AdsByBingAutoApply = 46,
        TagUsedByAccountGoalRecount = 47,
        TagUsedByAccountRemarketingListRecount = 48,
        TagUsedByAccountProductAudienceRecount = 49,
        AccountLevelAudienceUsedbyAdGroupRecount = 50,
        AimCampaignLevelPrivacyCheck = 51,
        AudienceLevelCampaignAudienceAssociationRecount = 52,
        AccountLevelCampaignAudienceAssociationRecount = 53,

        AccoutImageReLicense = 61,
        LicenseStockImage = 62,
        StockImageRekey = 63,
        NegativeKeywordCatalogMigration = 64,
        CustomerSharedEntityInvalidAssociationCleanup = 65,
        AdExEditorialStatusByAccount = 66,
        AppextensionsMetaDataSync = 67,
        AppInstallAdsMetaDataSync = 68,
        StaToExtaAutoApply = 69,
        VideoAdaptiveStreamingTranscode = 70,
        AdExEditorialStatusByCampaign = 71,
        PublishSmartPage = 72,
        PauseAdGroupAudienceAssociationForEURestriction = 73,
        PauseCampaignAudienceAssociationForEURestriction = 74,
        VideoDeleteCleanup = 75,
        AdImpressionTrackingUrl = 76,
        FreeUpSmartPageSubdomain = 77,
        AdExtensionOfflineProcessing = 80,
        MarkSmartPageEditorialRejected = 81,
        VideoDownload = 82,
        DSAAutoTargetLimit = 83,
        LabelMccDelete = 84,
        LabelMccEntityLevelDelete = 86,
        MultiMediaAdsAutoApply = 87,
        AdOfflineValidation = 88,
        LabelMccIdLevelRecount = 89,
        FeedSize = 90,
        FeedStatusSync = 91,
        AccountLevelLabelMccRecount = 93,
        LabelMccIdLevelLabelsPerEntityRecount = 94,
        MultiMediaAdsToTextAssets = 95,
        PublishSmartPagePreview = 96,
        CreateOrUpdateDraftCampaignForSmartPage = 98,
        SmartPageLeadsTracking = 99,
        SetM365FlagForSmartPage = 101,
        RSACustomizereOfflineDelete = 102,
        TextAssetCountsAndLimitCheck = 103,
        AdAssetCounts = 104,
        TextAssetBlobRefresh = 105,
        NewStockImageRekey = 106,
        ImageBulkCropping = 107,
        SmartPageCustomDomainDnsSetup = 109,
        SmartPageCustomDomainPostUpdate = 110,
        FreeUpSmartPageCustomDomain = 111,
        MigrateMMAUnderDSAAdGroup = 112,
        AccountPilotMigrationProcessor=114,
        SmartPageCustomDomainRefresh = 116,
        SmartPageUpdateESCUrl = 117,
        EnablePredictiveTargeting = 124,
        DisablePredictiveTargeting = 125,
        AssetGroupEditorialRollup = 126,
        AssetGroupAssetEditorialStatus = 128,
        SmartShoppingToPerformanceMaxUpgrade = 129,
        BingPlacesCreateOrClaimBusinessListing = 131,
        AudienceAdSmartCropping = 132,
        PauseAllMcaCampaigns = 135,
        AccountLevelImageAnnotation = 136,
        AssetLevelImageAnnotation = 137,
        ESCAccountMigration = 138,
        TextAdAssetAutoGeneration = 139,
        VideoDataBackfill = 142,
        DSAToPMaxIDMappingProcessor = 140,
        DSAToPMaxMigrationProcessor = 141,
        SSOBlockingMigration = 143,
        ResponsiveSearchAdsOptInAutoApplyAutoGenAsset = 144,
        AdVideoAssetsMetadataJsonFill = 145,
        CroppingTypeForImageAdExtensionBackfill = 147,
        UpdateAIGCSearchIndexProcessor = 148,
        LogoAdExtensionAutoGenerationProcessor = 149,
        UpdateExternalCampaignIdMapDetailByTaskStatus = 150,
        AssetGroupVideoAssetMetadataJsonFill = 152,
        AdOfflineAdd = 153,
        CCPilotPropagation = 154,
        VideoClipchampConversionProcessor = 155,
        AccountsActiveProcessor = 156
    }

    internal enum EmittingDbType
    {
        Main,
        Shard,
        Both,
        LibraryShard,
        SharedLibraryShard,
        CampaignEntityShard,
        MainAndCampaignEntityShard
    }
}
