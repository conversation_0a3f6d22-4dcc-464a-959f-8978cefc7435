[{"scriptPath": "./adjustHPASettings.sh", "displayName": "Auto-Scaling: Adjust HPA setting"}, {"scriptPath": "./getDeployments.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "Deployments: Get Deployments"}, {"scriptPath": "./getPods.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "Pods: Get Pods"}, {"scriptPath": "./getRollouts.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "ArgoRollouts: <PERSON>"}, {"scriptPath": "./restartTargetDeployment.sh", "displayName": "Deployments: Restart target deployment"}, {"scriptPath": "./restartAllDeployment.sh", "displayName": "Deployments: Restart all deployments"}, {"scriptPath": "./scaleDeployment.sh", "displayName": "Deployments: Scale up/down a deployment when No HPA is set"}, {"scriptPath": "./restartTargetArgoRollouts.sh", "displayName": "ArgoRollouts: Restart target argo-rollouts"}, {"scriptPath": "./restartAllArgoRollouts.sh", "displayName": "ArgoRollouts: <PERSON><PERSON> all argo-rollouts"}, {"scriptPath": "./scaleArgoRollout.sh", "displayName": "ArgoRollouts: Scale up/down an Argo Rollout when No HPA is set"}, {"scriptPath": "./restartTargetDaemonset.sh", "displayName": "Daemonsets: Restart target Daemon<PERSON>"}, {"scriptPath": "./getCronjob.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "Cronjob: <PERSON>"}, {"scriptPath": "./adhocCronjob.sh", "displayName": "Cronjob: Run cronjob on-demand just once"}, {"scriptPath": "./deleteBadWorkerNode.sh", "alertNames": ["worker_node_not_ready_not_reachable"], "displayName": "VMs: Delete bad worker node"}, {"scriptPath": "./isolateWorkerNodeFromScheduling.sh", "alertNames": ["WorkerNodeFailure"], "displayName": "VMs: Isolate a worker node from scheduling"}, {"scriptPath": "./putWorkerNodeBackToScheduling.sh", "displayName": "VMs: Put worker node back for scheduling"}, {"scriptPath": "./analysisPodRestartCause.sh", "exclusiveExec": false, "writeOperation": false, "alertNames": ["Restarts", "container_restart"], "displayName": "Alerts: Analysis pod restart", "hideFromUI": true}, {"scriptPath": "./analysisOOMKilled.sh", "exclusiveExec": false, "writeOperation": false, "alertNames": ["OOMKilled", "container_oom"], "displayName": "Alerts: Analysis pod OOMKilled", "hideFromUI": true}, {"scriptPath": "./analysisResourceUsageCPU.sh", "exclusiveExec": false, "writeOperation": false, "alertNames": ["HighCPU", "container_high_cpu"], "displayName": "Alerts: Analysis pod HighCPU", "hideFromUI": true}, {"scriptPath": "./analysisResourceUsageMemory_AutoMitigate.sh", "exclusiveExec": true, "writeOperation": true, "alertNames": ["HighMem", "container_high_memory_too_long", "dotnet_process_high_memory_too_long"], "displayName": "Alerts: Analysis pod HighMem and auto mitigate", "hideFromUI": true}, {"scriptPath": "./analysisImagePullBackoff.sh", "alertNames": ["container_start_failure_imagepull"], "displayName": "Alerts: Analysis ImagePull failure for pod start", "hideFromUI": true}, {"scriptPath": "./takePodBackToService.sh", "displayName": "Pods: Take pods Back to Service"}, {"scriptPath": "./takePodOutOfService.sh", "displayName": "Pods: Take pods Out of Service"}, {"scriptPath": "./deleteBadPods.sh", "displayName": "Pods: Delete bad pods"}, {"scriptPath": "./getMemoryDumpFileList.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "Adhoc: Get a list for existing memory dump files"}, {"scriptPath": "./getPodsLogs.sh", "exclusiveExec": false, "writeOperation": false, "displayName": "Pods: Get Logs for target pods"}, {"scriptPath": "./execCmdForPods.sh", "displayName": "Pods: Execute bash cmd inside target pods"}, {"scriptPath": "./analysisFailedPod.sh", "alertNames": ["kube-pod-status-phase-failed", "kube-pod-status-phase-failed-perNamespace"], "displayName": "Alerts: Analysis failed pod and auto mitigate if possible", "hideFromUI": true}, {"scriptPath": "./setDeploymentImageTag.sh", "displayName": "Deployment: set new Image Tag"}, {"scriptPath": "./submitPrometheusAlertRecord.sh", "alertNames": ["container_high_cpu_too_long", "container_high_threadcount", "pod_not_ready_ratio_high", "prometheus_storage_usage_too_high"], "exclusiveExec": false, "writeOperation": false, "displayName": "Alerts: Submit generic Prometheus alert audit record", "hideFromUI": true}]