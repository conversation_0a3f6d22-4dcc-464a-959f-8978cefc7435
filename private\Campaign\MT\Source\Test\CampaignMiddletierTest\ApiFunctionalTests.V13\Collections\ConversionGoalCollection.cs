﻿namespace CampaignTest.ApiFunctionalTests.Collections
{
    using CampaignMiddleTierTest.Framework.Utilities;
    using CampaignTest.ApiFunctionalTests;
    using Microsoft.BingAds.CampaignManagement;
    using Microsoft.Test.MaDLybZ;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using CustomerInfo = CampaignMiddleTierTest.Framework.CustomerInfo;
    using FieldNames = CampaignTest.Framework.FieldNames;
    using MethodNames = CampaignTest.Framework.MethodNames;
    using ResponseValidator = CampaignTest.ApiFunctionalTests.Validators.ResponseValidator;
    using API = Microsoft.BingAds.CampaignManagement;
    using Newtonsoft.Json;
    using ApiHelper = CampaignMiddleTierTest.Framework.ApiHelper;
    using System.Net.Http;
    using ApiVersion = CampaignMiddleTierTest.Framework.ApiVersion;
    using System.Net;

    public class ConversionGoalCollection : CollectionBase
    {
        private IList<ConversionGoal> goals;

        public IList<ConversionGoal> Goals
        {
            get { return goals; }
            set { goals = value; }
        }

        public long[] Ids
        {
            get
            {
                var ret = (from c in this.goals select c.Id.Value);
                return ret.ToArray();
            }
        }

        public ConversionGoalCollection(IList<ConversionGoal> goals)
        {
            this.goals = goals;
        }

        public ConversionGoalCollection(int num, ConversionGoalType type, long? tagId, params ProductionOption[] options)
        {
            this.goals = new List<ConversionGoal>();
            while (num > 0)
            {
                var goal = CreateGoal(type, options);
                if (!goals.Any(ret => { return ret.Name.Equals(goal.Name, StringComparison.InvariantCultureIgnoreCase); }))
                {
                    if (goal.Type != ConversionGoalType.AppInstall && goal.Type != ConversionGoalType.OfflineConversion && goal.Type != ConversionGoalType.InStoreTransaction)
                    {
                        goal.TagId = tagId;
                    }
                    //add a required column goal category for ga
                    setGoalCategoryByGoalType(type, goal);
                    goals.Add(goal);
                    num--;
                }
            }
        }

        private void setGoalCategoryByGoalType(ConversionGoalType goalType, ConversionGoal goal)
        {
            if (goalType == ConversionGoalType.Duration || goalType == ConversionGoalType.PagesViewedPerVisit)
            {
                goal.GoalCategory = ConversionGoalCategory.Other;
            }
            else if (goalType == ConversionGoalType.AppInstall)
            {
                goal.GoalCategory = ConversionGoalCategory.Download;
            }
            else if (goalType == ConversionGoalType.InStoreTransaction)
            {
                goal.GoalCategory = ConversionGoalCategory.Purchase;
            }
            else if (goalType == ConversionGoalType.Url || goalType == ConversionGoalType.OfflineConversion)
            {
                goal.GoalCategory = ConversionGoalCategory.Other;
            }
            else
            {
                goal.GoalCategory = ConversionGoalCategory.Other;
            }
        }

        public ConversionGoalCollection(ConversionGoalType[] types, long? tagId, params ProductionOption[] options)
        {
            this.goals = new List<ConversionGoal>();

            foreach (var type in types)
            {
                bool added = false;
                while (!added)
                {
                    var goal = CreateGoal(type, options);
                    if (!goals.Any(ret => { return ret.Name.Equals(goal.Name, StringComparison.InvariantCultureIgnoreCase); }))
                    {
                        if (type != ConversionGoalType.AppInstall && goal.Type != ConversionGoalType.OfflineConversion && goal.Type != ConversionGoalType.InStoreTransaction)
                        {
                            goal.TagId = tagId;
                        }
                        //add a required column goal category for ga
                        setGoalCategoryByGoalType(type, goal);
                        goals.Add(goal);
                        added = true;
                    }
                }
            }
        }

        private ConversionGoal CreateGoal(ConversionGoalType type, params ProductionOption[] options)
        {
            if (type == ConversionGoalType.Url)
            {
                return ApiTestSetting.Factories.ApiURLGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.Duration)
            {
                return ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.PagesViewedPerVisit)
            {
                return ApiTestSetting.Factories.ApiPageViewsPerVisitGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.Event)
            {
                return ApiTestSetting.Factories.ApiEventGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.AppInstall)
            {
                return ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.OfflineConversion)
            {
                return ApiTestSetting.Factories.ApiOfflineConversionGoalFactory.Produce(options);
            }
            else if (type == ConversionGoalType.InStoreTransaction)
            {
                return ApiTestSetting.Factories.ApiInStoreTransactionGoalFactory.Produce(options);
            }
            return null;
        }

        public object Add(CustomerInfo cInfo = null, ConversionGoal[] apiGoals = null, int? accountId = null)
        {
            if (accountId.HasValue)
            {
                this.AccountId = accountId.Value;
            }
            object request = CreateApiRequest(MethodNames.AddConversionGoals, cInfo);

            if (apiGoals == null)
            {
                apiGoals = this.goals.ToArray();
            }

            //set data to request   
            if (this.goals.Count > 0)
                SetFieldArrayValues(request, FieldNames.ConversionGoalField_ConversionGoals, apiGoals);

            //call service
            return CallService(MethodNames.AddConversionGoals, request);
        }

        public object Add_RewriteId(CustomerInfo cInfo = null, int? accountId = null)
        {
            object response = Add(cInfo, accountId: accountId);
            ResponseValidator.ValidateAPISuccess(response);
            List<object> goalIds = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoalIds);
            int index = 0;
            goalIds.ForEach(x =>
            {
                this.goals[index++].Id = (long)x;
            });
            return response;
        }

        public object Add_Success(CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null, bool isEnabledForAttributionModel = false, int? accountId = null)
        {
            object response = Add_RewriteId(cInfo, accountId);
            var type = GetConversionGoalType();
            List<object> getConversionGoalCollection = Get_Success(GetGoalIds(), type, cInfo, ReturnAdditionalFields);
            AreEqualInSequence(getConversionGoalCollection, ReturnAdditionalFields: ReturnAdditionalFields, isEnabledForAttributionModel: isEnabledForAttributionModel);
            return response;
        }

        public object Add_Fail(CustomerInfo cInfo = null, ConversionGoal[] apiGoals = null)
        {
            object response = Add(cInfo, apiGoals: apiGoals);

            ResponseValidator.ValidateAPIPatialError(response, index: 0, expectedErrorCode: 44944); 
            return response;
        }

        public object Update_Fail(CustomerInfo cInfo = null, ConversionGoal[] apiGoals = null)
        {
            object response = Update(cInfo, apiGoals: apiGoals);

            ResponseValidator.ValidateAPIPatialError(response, index: 0, expectedErrorCode: 44944);
            return response;
        }

        public static dynamic PostGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal request");
            }

            var postUrl = string.Format(ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})" + "/Goals", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", "@odata.type");
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic CreateDestinationGoal(long tagId)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.DestinationGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "DestinationGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.ViewThroughLookbackWindowinMinutes = 1440;
            goal.GoalCategory = "Other";
            //destinationGoal specific properties
            goal.UrlString = "testUrl";
            goal.Operator = "EqualsTo";

            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateOfflineGoal(bool isEnhancedConversionsEnabled = false)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.OfflineConversionGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "OfflineConversionGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = false;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = "Other";
            goal.ConversionWindowInMinutes = 7200;
            goal.IsEnhancedConversionsEnabled = isEnhancedConversionsEnabled;
            return goal;
        }

        public object Add_Success_For_GoalCategory(CustomerInfo cInfo = null)
        {
            object response = Add_RewriteId(cInfo);
            var type = GetConversionGoalType();
            List<object> getConversionGoalCollection = Get_Success(GetGoalIds(), type, cInfo);
            AreEqualInSequenceForGoalcagegory(getConversionGoalCollection);
            return response;
        }

        public object Update(CustomerInfo cInfo = null, ConversionGoal[] apiGoals = null, Dictionary<string, string> overrideConfigValuesFromTest = null)
        {
            object request = CreateApiRequest(MethodNames.UpdateConversionGoals, cInfo, overrideConfigValuesFromTest);

            if (apiGoals == null)
            {
                apiGoals = this.goals.ToArray();
            }

            //set data to request  
            if (this.goals.Count > 0)
                SetFieldArrayValues(request, FieldNames.ConversionGoalField_ConversionGoals, apiGoals);

            //call service
            return CallService(MethodNames.UpdateConversionGoals, request);
        }

        public object Update_Success(CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null, Action<List<ConversionGoal>> additionalAssert = null, bool isEnabledForAttributionModel = false)
        {
            object response = Update(cInfo);
            ResponseValidator.ValidateAPISuccess(response);
            var type = GetConversionGoalType();
            List<object> getConversionGoalCollection = Get_Success(GetGoalIds(), type, cInfo, ReturnAdditionalFields);
            AreEqualInSequence(getConversionGoalCollection, true, ReturnAdditionalFields, isEnabledForAttributionModel: isEnabledForAttributionModel);
            if (additionalAssert != null)
            {
                additionalAssert.Invoke(getConversionGoalCollection.Cast<ConversionGoal>().ToList());
            }
            return response;
        }

        public List<object> Get_Success(long[] goalIds, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            return Get_Success(goalIds, ConversionGoalType.InStoreTransaction | ConversionGoalType.OfflineConversion | ConversionGoalType.AppInstall | ConversionGoalType.Duration | ConversionGoalType.Event | ConversionGoalType.PagesViewedPerVisit | ConversionGoalType.Url, cInfo, ReturnAdditionalFields);

        }

        public List<object> Get_Success(long[] goalIds, ConversionGoalType type, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            object response = Get(goalIds, type, cInfo, ReturnAdditionalFields);
            ResponseValidator.ValidateAPISuccess(response);
            return ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
        }

        public object Get(long[] goalIds, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            return Get(goalIds, ConversionGoalType.InStoreTransaction | ConversionGoalType.OfflineConversion | ConversionGoalType.AppInstall | ConversionGoalType.Duration | ConversionGoalType.Event | ConversionGoalType.PagesViewedPerVisit | ConversionGoalType.Url, cInfo, ReturnAdditionalFields);
        }

        public object Get(long[] goalIds, ConversionGoalType type, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            object request = CreateApiRequest(MethodNames.GetConversionGoalsByIds, cInfo);

            if (goalIds != null)
                SetFieldArrayValues(request, FieldNames.ConversionGoalField_ConversionGoalIds, Array.ConvertAll(goalIds, o => (object)o));

            SetFieldValue(request, FieldNames.ConversionGoalField_ConversionGoalTypes, (object)type);

            if (ReturnAdditionalFields.HasValue)
            {
                SetFieldValue(request, FieldNames.ConversionGoal_ReturnAdditionalFields, (object)ReturnAdditionalFields.Value);
            }
            //call service
            return CallService(MethodNames.GetConversionGoalsByIds, request);
        }

        public List<object> Get_ByTagIds_Success(long[] tagIds, CustomerInfo cInfo = null)
        {
            return Get_ByTagIds_Success(tagIds, ConversionGoalType.InStoreTransaction | ConversionGoalType.OfflineConversion | ConversionGoalType.AppInstall | ConversionGoalType.Duration | ConversionGoalType.Event | ConversionGoalType.PagesViewedPerVisit | ConversionGoalType.Url, cInfo);
        }

        public List<object> Get_ByTagIds_Success(long[] tagIds, ConversionGoalType type, CustomerInfo cInfo = null)
        {
            object response = Get_ByTagIds(tagIds, type, cInfo);
            ResponseValidator.ValidateAPISuccess(response);
            return ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
        }

        public object Get_ByTagIds(long[] tagIds, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            return Get_ByTagIds(tagIds, ConversionGoalType.InStoreTransaction | ConversionGoalType.OfflineConversion | ConversionGoalType.AppInstall | ConversionGoalType.Duration | ConversionGoalType.Event | ConversionGoalType.PagesViewedPerVisit | ConversionGoalType.Url, cInfo, ReturnAdditionalFields);
        }

        public object Get_ByTagIds(long[] tagIds, ConversionGoalType type, CustomerInfo cInfo = null, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null)
        {
            object request = CreateApiRequest(MethodNames.GetConversionGoalsByTagIds, cInfo);

            if (tagIds != null)
                SetFieldArrayValues(request, FieldNames.ConversionGoalField_TagIds, Array.ConvertAll(tagIds, o => (object)o));

            SetFieldValue(request, FieldNames.ConversionGoalField_ConversionGoalTypes, (object)type);

            if (ReturnAdditionalFields.HasValue)
            {
                SetFieldValue(request, FieldNames.ConversionGoal_ReturnAdditionalFields, (object)ReturnAdditionalFields.Value);
            }

            //call service
            return CallService(MethodNames.GetConversionGoalsByTagIds, request);
        }

        public long[] GetGoalIds()
        {
            HashSet<long> ids = new HashSet<long>();
            foreach (var goal in goals)
            {
                ids.Add(goal.Id.Value);
            }
            return ids.ToArray();
        }

        public long[] GetGoalTagIds()
        {
            HashSet<long> ids = new HashSet<long>();
            foreach (var goal in goals)
            {
                ids.Add(goal.TagId.Value);
            }
            return ids.ToArray();
        }

        private ConversionGoalType GetConversionGoalType()
        {
            ConversionGoalType type = 0;
            foreach (var goal in goals)
            {
                if (goal is UrlGoal)
                {
                    type = type | ConversionGoalType.Url;
                }
                else if (goal is DurationGoal)
                {
                    type = type | ConversionGoalType.Duration;
                }
                else if (goal is PagesViewedPerVisitGoal)
                {
                    type = type | ConversionGoalType.PagesViewedPerVisit;
                }
                else if (goal is EventGoal)
                {
                    type = type | ConversionGoalType.Event;
                }
                else if (goal is AppInstallGoal)
                {
                    type = type | ConversionGoalType.AppInstall;
                }
                else if (goal is OfflineConversionGoal)
                {
                    type = type | ConversionGoalType.OfflineConversion;
                }
                else if (goal is InStoreTransactionGoal)
                {
                    type = type | ConversionGoalType.InStoreTransaction;
                }
            }
            return type;
        }

        public void AreEqualInSequence(List<object> list, Boolean isUpdate = false, API.ConversionGoalAdditionalField? ReturnAdditionalFields = null, bool isEnabledForAttributionModel = false)
        {
            Assert.AreEqual(goals.Count, list.Count);

            for (int i = 0; i < goals.Count(); i++)
            {
                var actual = list[i];
                var expected = goals[i];
                AreEqual(expected, actual, isUpdate, ReturnAdditionalFields, isEnabledForAttributionModel);
            }
        }

        public void AreEqualInSequenceForGoalcagegory(List<object> list, Boolean isUpdate = false)
        {
            Assert.AreEqual(goals.Count, list.Count);

            for (int i = 0; i < goals.Count(); i++)
            {
                var actual = list[i];
                var expected = goals[i];
                AreEqualForGoalCategory(expected, actual, isUpdate);
            }
        }

        public void GoalCollectionAreContained(List<object> fullList)
        {
            for (int i = 0; i < goals.Count(); i++)
            {
                var expected = goals[i];
                var actual = fullList.FirstOrDefault(x => ((ConversionGoal)x).Name.Equals(((ConversionGoal)expected).Name));
                AreEqual(expected, actual);
            }
        }

        public void GoalCollectionContains(List<object> subList)
        {
            for (int i = 0; i < subList.Count(); i++)
            {
                var actual = subList[i];
                var expected = goals.FirstOrDefault(x => ((ConversionGoal)x).Name.Equals(((ConversionGoal)actual).Name));
                AreEqual(expected, actual);
            }
        }

        public void AreEqual(List<object> list)
        {
            Assert.AreEqual(goals.Count, list.Count);

            for (int i = 0; i < goals.Count(); i++)
            {
                var actual = list[i];
                var expected = goals.FirstOrDefault(x => x.Name.Equals(((ConversionGoal)actual).Name));
                AreEqual(expected, actual);
            }
        }


        public void AreEqual(ConversionGoal expected,
            object actualGoal,
            Boolean isUpdate = false,
            API.ConversionGoalAdditionalField? ReturnAdditionalFields = null,
            bool isEnabledForAttributionModel = false)
        {
            if (expected == null)
            {
                Assert.IsNull(actualGoal);
            }            
            Assert.IsNotNull(actualGoal);

            var goal = actualGoal as ConversionGoal;

            Assert.AreEqual(expected.Id, goal.Id);
            if (!isUpdate || expected.Name != null)
            {
                Assert.AreEqual(expected.Name, goal.Name);
            }
            Assert.IsNotNull(goal.TrackingStatus);
            if (expected.Type == null)
            {
                if (expected is UrlGoal)
                {
                    expected.Type = ConversionGoalType.Url;
                }
                else if (expected is DurationGoal)
                {
                    expected.Type = ConversionGoalType.Duration;
                }
                else if (expected is PagesViewedPerVisitGoal)
                {
                    expected.Type = ConversionGoalType.PagesViewedPerVisit;
                }
                else if (expected is EventGoal)
                {
                    expected.Type = ConversionGoalType.Event;
                }
                else if (expected is AppInstallGoal)
                {
                    expected.Type = ConversionGoalType.AppInstall;
                }
                else if (expected is OfflineConversionGoal)
                {
                    expected.Type = ConversionGoalType.OfflineConversion;
                }
                else if (expected is InStoreTransactionGoal)
                {
                    expected.Type = ConversionGoalType.InStoreTransaction;
                }
            }
            Assert.AreEqual(expected.Type, goal.Type);
            if (!(expected is AppInstallGoal || expected is OfflineConversionGoal || expected is InStoreTransactionGoal))
            {
                Assert.AreEqual(expected.TagId, goal.TagId);
            }
            else
            {
                Assert.IsNull(goal.TagId);
            }
            if (expected.Status != null)
            {
                Assert.AreEqual(expected.Status, goal.Status);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalStatus.Active, goal.Status);
            }
            if (expected.Scope != null)
            {
                Assert.AreEqual(expected.Scope, goal.Scope);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(EntityScope.Customer, goal.Scope);
            }
            if (expected.CountType != null)
            {
                Assert.AreEqual(expected.CountType, goal.CountType);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalCountType.All, goal.CountType);
            }
            if (expected.Revenue != null)
            {
                if (expected.Revenue.Type != null)
                {
                    Assert.AreEqual(expected.Revenue.Type, goal.Revenue.Type);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(ConversionGoalRevenueType.NoValue, goal.Revenue.Type);
                }

                if (expected.Revenue.Value != null)
                {
                    Assert.IsTrue(Math.Abs(expected.Revenue.Value.Value - goal.Revenue.Value.Value) <= 0.01M);
                }
                else if (!isUpdate)
                {
                    if (goal.Revenue.Type == ConversionGoalRevenueType.NoValue)
                    {
                        Assert.IsNull(goal.Revenue.Value);
                    }
                    else
                    {
                        Assert.AreEqual(0, goal.Revenue.Value);
                    }
                }
                if (goal.Scope == EntityScope.Account
                    && (goal.Type == ConversionGoalType.Url ||
                        goal.Type == ConversionGoalType.Event)
                    || goal.Type == ConversionGoalType.OfflineConversion)
                {
                    if (!isUpdate)//add goal
                    {
                        if (expected.Revenue.Type == ConversionGoalRevenueType.NoValue
                            || expected.Revenue.Value == null)
                            Assert.IsNull(goal.Revenue.CurrencyCode);
                        else if (expected.Revenue.CurrencyCode == null)
                            Assert.IsNotNull(goal.Revenue.CurrencyCode);
                        else
                            Assert.AreEqual(expected.Revenue.CurrencyCode, goal.Revenue.CurrencyCode);
                    }
                    else//update goal
                    {
                        if (expected.Revenue.CurrencyCode == null)
                        {
                            if (goal.Revenue.Type != ConversionGoalRevenueType.NoValue)
                                Assert.IsNotNull(goal.Revenue.CurrencyCode);
                        }
                        else
                            Assert.AreEqual(expected.Revenue.CurrencyCode, goal.Revenue.CurrencyCode);
                    }
                }
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalRevenueType.NoValue, goal.Revenue.Type);
                Assert.IsNull(goal.Revenue.Value);
            }
            if (expected.ConversionWindowInMinutes != null)
            {
                Assert.AreEqual(expected.ConversionWindowInMinutes, goal.ConversionWindowInMinutes);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(43200, goal.ConversionWindowInMinutes);
            }

            if (!isEnabledForAttributionModel)
            {
                Assert.IsNull(goal.AttributionModelType, "the AttributionModelType should be null");
            }
            else if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.AttributionModelType))
            {
                if (expected.AttributionModelType.HasValue)
                {
                    if (isUetBasedGoal(expected))
                    {
                        Assert.AreEqual(expected.AttributionModelType, goal.AttributionModelType);
                    }
                    else
                    {
                        Assert.IsNull(goal.AttributionModelType, "the AttributionModelType should be null for non-uet based goals");
                    }
                }
                else if (!isUpdate)
                {
                    if (isUetBasedGoal(expected))
                    {
                        // default value is LastClick
                        Assert.AreEqual(AttributionModelType.LastClick, goal.AttributionModelType, "the default value of AttributionModelType should be set as LastClick");
                    }
                    else
                    {
                        Assert.IsNull(goal.AttributionModelType, "the AttributionModelType should be null for non-uet based goals and customer not in pilot");
                    }
                }
            }
            else
            {
                Assert.IsNull(goal.AttributionModelType, "the AttributionModelType should be null if ConversionGoalAdditionalField.AttributionModelType is not set");
            }

            if (expected.Type == ConversionGoalType.Url)
            {
                var expectedUrlGoal = expected as UrlGoal;
                var urlGoal = goal as UrlGoal;
                Assert.AreEqual(expectedUrlGoal.UrlExpression, urlGoal.UrlExpression);
                if (expectedUrlGoal.UrlOperator != null)
                {
                    Assert.AreEqual(expectedUrlGoal.UrlOperator, urlGoal.UrlOperator);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(ExpressionOperator.Equals, urlGoal.UrlOperator);
                }
            }
            else if (expected.Type == ConversionGoalType.Duration)
            {
                var expectedDurationGoal = expected as DurationGoal;
                var durationGoal = goal as DurationGoal;
                if (expectedDurationGoal.MinimumDurationInSeconds != null)
                {
                    Assert.AreEqual(expectedDurationGoal.MinimumDurationInSeconds, durationGoal.MinimumDurationInSeconds);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(0, durationGoal.MinimumDurationInSeconds);
                }
            }
            else if (expected.Type == ConversionGoalType.PagesViewedPerVisit)
            {
                var expectedPageGoal = expected as PagesViewedPerVisitGoal;
                var pageGoal = goal as PagesViewedPerVisitGoal;
                if (expectedPageGoal.MinimumPagesViewed != null)
                {
                    Assert.AreEqual(expectedPageGoal.MinimumPagesViewed, pageGoal.MinimumPagesViewed);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(0, pageGoal.MinimumPagesViewed);
                }
            }
            else if (expected.Type == ConversionGoalType.Event)
            {
                var expectedEventGoal = expected as EventGoal;
                var eventGoal = goal as EventGoal;
                if (expectedEventGoal.Value == null)
                {
                    Assert.IsNull(eventGoal.Value);
                }
                else if (!isUpdate)
                {
                    Assert.IsTrue(Math.Abs(expectedEventGoal.Value.Value - eventGoal.Value.Value) <= 0.01M);
                }
                if (expectedEventGoal.ValueOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.ValueOperator, eventGoal.ValueOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.Value == null)
                    {
                        Assert.IsNull(eventGoal.ValueOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ValueOperator.Equals, eventGoal.ValueOperator);
                    }
                }
                Assert.AreEqual(expectedEventGoal.CategoryExpression, eventGoal.CategoryExpression);
                if (expectedEventGoal.CategoryOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.CategoryOperator, eventGoal.CategoryOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.CategoryExpression.NullOrEmpty())
                    {
                        Assert.IsNull(eventGoal.CategoryOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ExpressionOperator.Equals, eventGoal.CategoryOperator);
                    }
                }
                Assert.AreEqual(expectedEventGoal.LabelExpression, eventGoal.LabelExpression);
                if (expectedEventGoal.LabelOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.LabelOperator, eventGoal.LabelOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.LabelExpression.NullOrEmpty())
                    {
                        Assert.IsNull(eventGoal.LabelOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ExpressionOperator.Equals, eventGoal.LabelOperator);
                    }
                }

                if (eventGoal.IsAutoGoal ?? false)
                {
                    Assert.AreEqual(eventGoal.ActionExpression, GenerateEventActionForAutoGoal(expectedEventGoal.GoalCategory));
                    Assert.AreEqual(eventGoal.ActionOperator, ExpressionOperator.Equals);
                }
                else
                {
                    Assert.AreEqual(expectedEventGoal.ActionExpression, eventGoal.ActionExpression);
                    if (expectedEventGoal.ActionOperator != null)
                    {
                        Assert.AreEqual(expectedEventGoal.ActionOperator, eventGoal.ActionOperator);
                    }
                    else if (!isUpdate)
                    {
                        if (expectedEventGoal.ActionExpression.NullOrEmpty())
                        {
                            Assert.IsNull(eventGoal.ActionOperator);
                        }
                        else
                        {
                            Assert.AreEqual(ExpressionOperator.Equals, eventGoal.ActionOperator);
                        }
                    }
                }
            }
            else if (expected.Type == ConversionGoalType.AppInstall)
            {
                var expectedAppGoal = expected as AppInstallGoal;
                var appGoal = goal as AppInstallGoal;
                if (!expectedAppGoal.AppPlatform.NullOrEmptyOrAllSpace())
                {
                    Assert.AreEqual(expectedAppGoal.AppPlatform, appGoal.AppPlatform);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual("Windows Phone", appGoal.AppPlatform);
                }
                Assert.AreEqual(expectedAppGoal.AppStoreId, appGoal.AppStoreId);
            }
        }

        public void AreEqualForGoalCategory(ConversionGoal expected, object actualGoal, Boolean isUpdate = false)
        {
            if (expected == null)
            {
                Assert.IsNull(actualGoal);
            }
            Assert.IsNotNull(actualGoal);

            var goal = actualGoal as ConversionGoal;

            Assert.AreEqual(expected.Id, goal.Id);
            Assert.AreEqual(expected.GoalCategory, goal.GoalCategory);
            if (!isUpdate || expected.Name != null)
            {
                Assert.AreEqual(expected.Name, goal.Name);
            }
            Assert.IsNotNull(goal.TrackingStatus);
            if (expected.Type == null)
            {
                if (expected is UrlGoal)
                {
                    expected.Type = ConversionGoalType.Url;
                }
                else if (expected is DurationGoal)
                {
                    expected.Type = ConversionGoalType.Duration;
                }
                else if (expected is PagesViewedPerVisitGoal)
                {
                    expected.Type = ConversionGoalType.PagesViewedPerVisit;
                }
                else if (expected is EventGoal)
                {
                    expected.Type = ConversionGoalType.Event;
                }
                else if (expected is AppInstallGoal)
                {
                    expected.Type = ConversionGoalType.AppInstall;
                }
                else if (expected is OfflineConversionGoal)
                {
                    expected.Type = ConversionGoalType.OfflineConversion;
                }
                else if (expected is InStoreTransactionGoal)
                {
                    expected.Type = ConversionGoalType.InStoreTransaction;
                }
            }
            Assert.AreEqual(expected.Type, goal.Type);
            if (!(expected is AppInstallGoal || expected is OfflineConversionGoal || expected is InStoreTransactionGoal))
            {
                Assert.AreEqual(expected.TagId, goal.TagId);
            }
            else
            {
                Assert.IsNull(goal.TagId);
            }
            if (expected.Status != null)
            {
                Assert.AreEqual(expected.Status, goal.Status);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalStatus.Active, goal.Status);
            }
            if (expected.Scope != null)
            {
                Assert.AreEqual(expected.Scope, goal.Scope);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(EntityScope.Customer, goal.Scope);
            }
            if (expected.CountType != null)
            {
                Assert.AreEqual(expected.CountType, goal.CountType);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalCountType.All, goal.CountType);
            }
            if (expected.Revenue != null)
            {
                if (expected.Revenue.Type != null)
                {
                    Assert.AreEqual(expected.Revenue.Type, goal.Revenue.Type);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(ConversionGoalRevenueType.NoValue, goal.Revenue.Type);
                }

                if (expected.Revenue.Value != null)
                {
                    Assert.IsTrue(Math.Abs(expected.Revenue.Value.Value - goal.Revenue.Value.Value) <= 0.01M);
                }
                else if (!isUpdate)
                {
                    if (goal.Revenue.Type == ConversionGoalRevenueType.NoValue)
                    {
                        Assert.IsNull(goal.Revenue.Value);
                    }
                    else
                    {
                        Assert.AreEqual(0, goal.Revenue.Value);
                    }
                }
                if (goal.Scope == EntityScope.Account
                    && (goal.Type == ConversionGoalType.Url ||
                        goal.Type == ConversionGoalType.Event)
                    || goal.Type == ConversionGoalType.OfflineConversion)
                {
                    if (!isUpdate)//add goal
                    {
                        if (expected.Revenue.Type == ConversionGoalRevenueType.NoValue
                            || expected.Revenue.Value == null)
                            Assert.IsNull(goal.Revenue.CurrencyCode);
                        else if (expected.Revenue.CurrencyCode == null)
                            Assert.IsNotNull(goal.Revenue.CurrencyCode);
                        else
                            Assert.AreEqual(expected.Revenue.CurrencyCode, goal.Revenue.CurrencyCode);
                    }
                    else//update goal
                    {
                        if (expected.Revenue.CurrencyCode == null)
                        {
                            if (goal.Revenue.Type != ConversionGoalRevenueType.NoValue)
                                Assert.IsNotNull(goal.Revenue.CurrencyCode);
                        }
                        else
                            Assert.AreEqual(expected.Revenue.CurrencyCode, goal.Revenue.CurrencyCode);
                    }
                }
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(ConversionGoalRevenueType.NoValue, goal.Revenue.Type);
                Assert.IsNull(goal.Revenue.Value);
            }
            if (expected.ConversionWindowInMinutes != null)
            {
                Assert.AreEqual(expected.ConversionWindowInMinutes, goal.ConversionWindowInMinutes);
            }
            else if (!isUpdate)
            {
                Assert.AreEqual(43200, goal.ConversionWindowInMinutes);
            }

            if (expected.Type == ConversionGoalType.Url)
            {
                var expectedUrlGoal = expected as UrlGoal;
                var urlGoal = goal as UrlGoal;
                Assert.AreEqual(expectedUrlGoal.UrlExpression, urlGoal.UrlExpression);
                if (expectedUrlGoal.UrlOperator != null)
                {
                    Assert.AreEqual(expectedUrlGoal.UrlOperator, urlGoal.UrlOperator);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(ExpressionOperator.Equals, urlGoal.UrlOperator);
                }
            }
            else if (expected.Type == ConversionGoalType.Duration)
            {
                var expectedDurationGoal = expected as DurationGoal;
                var durationGoal = goal as DurationGoal;
                if (expectedDurationGoal.MinimumDurationInSeconds != null)
                {
                    Assert.AreEqual(expectedDurationGoal.MinimumDurationInSeconds, durationGoal.MinimumDurationInSeconds);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(0, durationGoal.MinimumDurationInSeconds);
                }
            }
            else if (expected.Type == ConversionGoalType.PagesViewedPerVisit)
            {
                var expectedPageGoal = expected as PagesViewedPerVisitGoal;
                var pageGoal = goal as PagesViewedPerVisitGoal;
                if (expectedPageGoal.MinimumPagesViewed != null)
                {
                    Assert.AreEqual(expectedPageGoal.MinimumPagesViewed, pageGoal.MinimumPagesViewed);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual(0, pageGoal.MinimumPagesViewed);
                }
            }
            else if (expected.Type == ConversionGoalType.Event)
            {
                var expectedEventGoal = expected as EventGoal;
                var eventGoal = goal as EventGoal;
                if (expectedEventGoal.Value == null)
                {
                    Assert.IsNull(eventGoal.Value);
                }
                else if (!isUpdate)
                {
                    Assert.IsTrue(Math.Abs(expectedEventGoal.Value.Value - eventGoal.Value.Value) <= 0.01M);
                }
                if (expectedEventGoal.ValueOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.ValueOperator, eventGoal.ValueOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.Value == null)
                    {
                        Assert.IsNull(eventGoal.ValueOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ValueOperator.Equals, eventGoal.ValueOperator);
                    }
                }
                Assert.AreEqual(expectedEventGoal.CategoryExpression, eventGoal.CategoryExpression);
                if (expectedEventGoal.CategoryOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.CategoryOperator, eventGoal.CategoryOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.CategoryExpression.NullOrEmpty())
                    {
                        Assert.IsNull(eventGoal.CategoryOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ExpressionOperator.Equals, eventGoal.CategoryOperator);
                    }
                }
                Assert.AreEqual(expectedEventGoal.LabelExpression, eventGoal.LabelExpression);
                if (expectedEventGoal.LabelOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.LabelOperator, eventGoal.LabelOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.LabelExpression.NullOrEmpty())
                    {
                        Assert.IsNull(eventGoal.LabelOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ExpressionOperator.Equals, eventGoal.LabelOperator);
                    }
                }
                Assert.AreEqual(expectedEventGoal.ActionExpression, eventGoal.ActionExpression);
                if (expectedEventGoal.ActionOperator != null)
                {
                    Assert.AreEqual(expectedEventGoal.ActionOperator, eventGoal.ActionOperator);
                }
                else if (!isUpdate)
                {
                    if (expectedEventGoal.ActionExpression.NullOrEmpty())
                    {
                        Assert.IsNull(eventGoal.ActionOperator);
                    }
                    else
                    {
                        Assert.AreEqual(ExpressionOperator.Equals, eventGoal.ActionOperator);
                    }
                }
            }
            else if (expected.Type == ConversionGoalType.AppInstall)
            {
                var expectedAppGoal = expected as AppInstallGoal;
                var appGoal = goal as AppInstallGoal;
                if (!expectedAppGoal.AppPlatform.NullOrEmptyOrAllSpace())
                {
                    Assert.AreEqual(expectedAppGoal.AppPlatform, appGoal.AppPlatform);
                }
                else if (!isUpdate)
                {
                    Assert.AreEqual("Windows Phone", appGoal.AppPlatform);
                }
                Assert.AreEqual(expectedAppGoal.AppStoreId, appGoal.AppStoreId);
            }
        }

        private static bool isUetBasedGoal(ConversionGoal goal)
        {
            return (goal is DurationGoal) ||
                // below uet based goals haven't been exposed to API
                //(goal is DestinationGoal) ||
                //(goal is PageViewsPerVisitGoal) ||
                //(goal is ProductConversionGoal) ||
                //(goal is SmartGoal) ||
                (goal is EventGoal);
        }        

        public static string GenerateEventActionForAutoGoal(ConversionGoalCategory? goalCategory)
        {
            string actionHeader = "AutoEvent_";
            switch (goalCategory)
            {
                case ConversionGoalCategory.Purchase:
                    return actionHeader + "purchase";
                case ConversionGoalCategory.AddToCart:
                    return actionHeader + "add_to_cart";
                case ConversionGoalCategory.BeginCheckout:
                    return actionHeader + "begin_checkout";
                case ConversionGoalCategory.Subscribe:
                    return actionHeader + "subscribe";
                case ConversionGoalCategory.SubmitLeadForm:
                    return actionHeader + "submit_form";
                case ConversionGoalCategory.BookAppointment:
                    return actionHeader + "book_appointment";
                case ConversionGoalCategory.Signup:
                    return actionHeader + "sign_up";
                case ConversionGoalCategory.RequestQuote:
                    return actionHeader + "request_quote";
                case ConversionGoalCategory.GetDirections:
                    return actionHeader + "look_at_directions";
                case ConversionGoalCategory.OutboundClick:
                    return actionHeader + "outbound_click";
                case ConversionGoalCategory.Contact:
                    return actionHeader + "contact_us";
                default:
                    return string.Empty; //unsupported category for auto goal
            }
        }
    }

}
