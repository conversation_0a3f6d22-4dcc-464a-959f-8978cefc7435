﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.EventTracking
{
    using CampaignServiceCommon.Common.Xandr;
    using Dao.SharedLibrary;
    using DAO;
    using Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.SharedLibrary;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Audience;
    using Microsoft.AdCenter.Shared.Async;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities.Internal;
    using Microsoft.Advertiser.ClientCenter.MT.Proxy;
    using Microsoft.BingAds.Utils;
    using Shared.MT;
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using EventTracking = Entities.EventTracking;

    public partial class EventTrackingEO
    {
        private readonly IEventTrackingDao dao;
        private readonly ISharedLibraryDao sharedLibraryDao;
        private readonly TagValidator tagValidator = new TagValidator();

        private readonly GoalValidator goalValidator;
        private readonly IAudienceProvider audienceProvider;
        private readonly IAccountPropertiesEO accountPropertiesEO;
        private readonly IAudienceEO audienceEo;

        private readonly IOfflineTaskDao offlineTaskDao;

        private readonly IClarityEventTrackingEo clarityEventTrackingEo;

        private readonly ICampaignInfoGetter campaignInfoGetter;

        private static readonly IObjectStoreClientProvider objectStoreClientProvider;

        private readonly IExternalEntityMappingDao externalEntityMappingDao;
        private readonly IXandrClientFactory _xandrClientFactory;
        private readonly ICustomerFetchHelper customerFetchHelper;

        public EventTrackingEO(IEventTrackingDao dao, 
            IAdExtensionDomainDataStorage adextensionDomainDataStorage, 
            IAccountPropertiesEO accountPropertiesEO, 
            ICheckPilotFlag pilotChecker, 
            IAudienceProvider audienceProvider, 
            ISharedLibraryDao sharedLibraryDao, 
            IAudienceEO audienceEo,
            IOfflineTaskDao offlineTaskDao,
            IClarityEventTrackingEo clarityEventTrackingEo,
            ICampaignInfoGetter campaignInfoGetter,
            IExternalEntityMappingDao externalEntityMappingDao,
            IXandrClientFactory xandrClientFactory,
            ICustomerFetchHelper customerFetchHelper)
        {
            this.dao = dao;
            this.goalValidator = new GoalValidator(adextensionDomainDataStorage, pilotChecker);
            this.audienceProvider = audienceProvider;
            this.sharedLibraryDao = sharedLibraryDao;
            this.accountPropertiesEO = accountPropertiesEO;
            this.audienceEo = audienceEo;
            this.offlineTaskDao = offlineTaskDao;
            this.clarityEventTrackingEo = clarityEventTrackingEo;
            this.campaignInfoGetter = campaignInfoGetter;
            this.externalEntityMappingDao = externalEntityMappingDao;
            this._xandrClientFactory = xandrClientFactory;
            this.customerFetchHelper = customerFetchHelper;
        }

        static EventTrackingEO()
        {
            if(DynamicConfigValues.ObjectStoreClient == "MemoryMock")
            {
                objectStoreClientProvider = new MemoryMockObjectStoreClientProvider();
            }
            else
            {
                objectStoreClientProvider = new ObjectStoreClientProvider();
            }
        }

        private void ValidateCustomerId<T>(CampaignManagementRequest request, BatchResult<T> batchResult)
        {
            if (request.AdvertiserCustomerId == null || request.AdvertiserCustomerId <= 0)
            {
                batchResult.AddError(CampaignManagementErrorCode.CustomerIdRequired);
            }
        }

        private void ValidateCustomerId<T>(CampaignManagementRequest request, Result<T> result)
        {
            if (request.AdvertiserCustomerId == null || request.AdvertiserCustomerId <= 0)
            {
                result.AddError(CampaignManagementErrorCode.CustomerIdRequired);
            }
        }

        private void ValidateCustomerId<T>(CampaignManagementRequest request, ResultOneEntity<T> result)
        {
            if (request.AdvertiserCustomerId == null || request.AdvertiserCustomerId <= 0)
            {
                result.AddError(CampaignManagementErrorCode.CustomerIdRequired);
            }
        }

        private void ValidateAccountId<T>(CampaignManagementRequest request, BatchResult<T> batchResult)
        {
            if (request.CustomerAccountId == null || request.CustomerAccountId <= 0)
            {
                batchResult.AddError(CampaignManagementErrorCode.AccountIdInvalid);
            }
        }

        private void ValidateAccountId<T>(CampaignManagementRequest request, ResultOneEntity<T> result)
        {
            if (request.CustomerAccountId == null || request.CustomerAccountId <= 0)
            {
                result.AddError(CampaignManagementErrorCode.AccountIdInvalid);
            }
        }

        private bool TryGetLoginCustomerId<T>(CustomerCallContext context, BatchResult<T> batchResult, out int loginCustomerId, int? rootCustomerId = null)
        {
            loginCustomerId = -1;
            var cid = ClientCenterFacade.CustomerHierarchy.GetLoginCustomerIdByUserId(context.UserId.ToString(), context.Request.SecurityTicket.SecurityTicketId, context.Logger, out bool isException);
            if (cid == null)
            {
                if (isException)
                {
                    batchResult.AddError(CampaignManagementErrorCode.InternalError);
                }
                else
                {
                    context.Logger.LogUserError($"GetLoginCustomerIdByUserId call to ClientCenter failed. Could not get login Customer Id with userId {context.UserId.ToString()} due to PermissionDenied.");
                    batchResult.AddError(CampaignManagementErrorCode.PermissionDenied);
                }
                return false;
            }
            if (cid.Value == 0)
            {
                cid = rootCustomerId ?? context.AdvertiserCustomerId;//Use root customer or context customer to get hierarchy if it is internal user
            }
            loginCustomerId = cid.Value;
            return true;
        }

        private bool ValidateAgencyLinkForGoal<T>(CampaignManagementRequest request, EventTracking.Goal[] goals, BatchResult<T> batchResult, ILogShared logger)
        {
            if (!goals.OrEmpty().Any(a => a.IsAccountLevel == false)) // validate customer level entity only
            {
                return true;
            }
            return ValidateAgencyLink(request, batchResult, logger);
        }

        private bool ValidateAgencyLink<T>(CampaignManagementRequest request, BatchResult<T> batchResult, ILogShared logger)
        {
            if (!request.CustomerAccountId.HasValue) // agency will not use customer context
            {
                return true;
            }

            try
            {
                using (logger.AcquireCommonPerfLogger("GetAccountCustomerRelationsByUserToken"))
                {
                    var linkTypeResp = ClientCenterFacade.ClientCenterCaller.Instance.GetAccountCustomerRelationsByUserToken(logger, new GetAccountCustomerRelationsByUserTokenRequest
                    {
                        AccountId = (int)request.CustomerAccountId.Value,
                        UserToken = request.SecurityTicket.SecurityTicketId
                    });
                    if (linkTypeResp == null || linkTypeResp.AccountCustomerPropogatedRelations == null || linkTypeResp.AccountCustomerPropogatedRelations.Count == 0)
                    {
                        batchResult.AddError(CampaignManagementErrorCode.InternalError);
                        return false;
                    }

                    if (linkTypeResp.AccountCustomerPropogatedRelations.Count > 1)
                    {
                        // multi user case, reject if no user has standard link
                        if (!linkTypeResp.AccountCustomerPropogatedRelations.Any(r => r.AccountLinkPermissionType == AccountLinkPermissionType.Standard))
                        {
                            batchResult.AddError(CampaignManagementErrorCode.PermissionDenied);
                            return false;
                        }
                    }
                    else
                    {
                        // single user case, block write operations for AccountCampaignManagement at campaign side, CCMT will block write actions for Billing, Readonly and Unknown
                        if (linkTypeResp.AccountCustomerPropogatedRelations.First().AccountLinkPermissionType == AccountLinkPermissionType.AccountCampaignManagement)
                        {
                            batchResult.AddError(CampaignManagementErrorCode.PermissionDenied);
                            return false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                SafeSharedLog.LogApplicationError(logger, "ClientCenter returned exception in GetAccountCustomerRelation.", ex.ToString());
                batchResult.AddError(CampaignManagementErrorCode.InternalError);
                return false;
            }
            return true;
        }

        private void InlineUpdateUsedBy(BasicCallContext basicCallContext, List<Tuple<List<Entities.EventTracking.Goal>, bool>> goalsWithOperation, HashSet<long> sharedTagIds)
        {
            var usedByInfoByTagId = new Dictionary<long, UsedByInfo>();

            var customerGoalCountByTagId = new Dictionary<long, int>();
            var accountGoalCountByTagId = new Dictionary<long, int>();
            foreach (var goalWithOperation in goalsWithOperation)
            {
                var goals = goalWithOperation.Item1;
                var isDecreaseOperation = goalWithOperation.Item2;

                var goalsByTagId = goals.Where(e => e.TagId.HasValue).GroupBy(e => e.TagId.Value)
                    .ToDictionary(k => k.Key, k => k.ToList());
                GenerateUsedByInfo(basicCallContext, goalsByTagId, usedByInfoByTagId, isDecreaseOperation, sharedTagIds);
            }
            var usedByInfoList = usedByInfoByTagId.Values.ToList();

            if (usedByInfoList.Count > 0)
            {
                Task.Factory.StartNew(
                    () => sharedLibraryDao.UpdateUsedBy(basicCallContext, usedByInfoList, true),
                    TaskHelper.GetTaskCreationOptions()).Ignore(basicCallContext.Logger, TraceLevel.Warning);
            }
        }

        private void GenerateUsedByInfo(BasicCallContext context, Dictionary<long, List<EventTracking.Goal>> goalsByTagId, Dictionary<long, UsedByInfo> usedByInfoByTagId, bool isDeleteOperation, HashSet<long> sharedTagIds)
        {
            var customerEntity = new AccountEntity { CustomerId = context.AdvertiserCustomerId };
            var accountEntity = new AccountEntity { CustomerId = context.AdvertiserCustomerId, AccountId = (int)context.AccountId.Value };

            foreach (var goalByTagId in goalsByTagId)
            {
                var tagId = goalByTagId.Key;
                var goals = goalByTagId.Value;

                if (!sharedTagIds.Contains(tagId))
                {
                    continue;
                }

                var usedByCountForCustomer = (goals?.Where(g => g.IsAccountLevel == false)?.Count() ?? 0)
                    * (isDeleteOperation ? -1 : 1);
                var usedByCountForAccounts = (goals?.Where(g => g.IsAccountLevel == true)?.Count() ?? 0)
                    * (isDeleteOperation ? -1 : 1);

                if (usedByCountForCustomer == 0 && usedByCountForAccounts == 0)
                {
                    continue;
                }

                Dictionary<AccountEntity, int> usedByAccount = null;
                if (usedByInfoByTagId.ContainsKey(tagId))
                {
                    usedByAccount = usedByInfoByTagId[tagId].UsedByAccounts;
                }
                else
                {
                    usedByAccount = new Dictionary<AccountEntity, int>();
                    var usedByInfo = new UsedByInfo
                    {
                        EntityId = tagId,
                        EntityType = SharedLibraryEntityType.UETTag,
                        UsedByType = UsedByType.UETTagAssociateToConversionGoal,
                        UsedByAccounts = usedByAccount
                    };
                    usedByInfoByTagId[tagId] = usedByInfo;
                }

                if (usedByCountForCustomer != 0)
                {
                    usedByAccount[customerEntity] = usedByAccount.ContainsKey(customerEntity)
                        ? usedByAccount[customerEntity] += usedByCountForCustomer
                        : usedByCountForCustomer;
                    if (usedByAccount[customerEntity] == 0)
                    {
                        usedByAccount.Remove(customerEntity);
                    }
                }
                if (usedByCountForAccounts != 0)
                {
                    usedByAccount[accountEntity] = usedByAccount.ContainsKey(accountEntity)
                        ? usedByAccount[accountEntity] += usedByCountForAccounts
                        : usedByCountForAccounts;
                    if (usedByAccount[accountEntity] == 0)
                    {
                        usedByAccount.Remove(accountEntity);
                    }
                }
                if (usedByAccount.Count == 0)
                {
                    usedByInfoByTagId.Remove(tagId);
                }
            }
        }

        private bool HasSharingScope(EventTracking.Tag tag)
        {
            return tag.SharingScope == null || //Tag has sharing scope but user doesn't have permission to access them
                   tag.SharingScope.Customers != null && tag.SharingScope.Customers.Any();
        }

        private List<UsedByInfo> GenerateUsedByDetails(List<UsedByInfo> originalUsedByInfos, List<AccountItem> usedByCustomers, Dictionary<int, int> scrolledAccountIdsToCustomerIds)
        {
            var usedByDetails = new List<UsedByInfo>();
            var usedByCustomerIds = usedByCustomers.Select(e => e.Id).ToHashSet();

            foreach (var usedByInfo in originalUsedByInfos)
            {
                var usedByAccountsInfo = new Dictionary<AccountEntity, int>();
                foreach (var usedByAccount in usedByInfo.UsedByAccounts)
                {
                    if (usedByAccount.Key.AccountId.HasValue &&
                        !usedByAccount.Key.CustomerId.HasValue &&
                        usedByAccount.Value > 0)
                    {
                        if (scrolledAccountIdsToCustomerIds.ContainsKey(usedByAccount.Key.AccountId.Value) &&
                            usedByCustomerIds.Contains(scrolledAccountIdsToCustomerIds[usedByAccount.Key.AccountId.Value]))
                        {
                            var accountEntity = new AccountEntity
                            {
                                CustomerId = scrolledAccountIdsToCustomerIds[usedByAccount.Key.AccountId.Value],
                                AccountId = null
                            };
                            if (usedByAccountsInfo.ContainsKey(accountEntity))
                            {
                                usedByAccountsInfo[accountEntity] += usedByAccount.Value;
                            }
                            else
                            {
                                usedByAccountsInfo.Add(accountEntity, usedByAccount.Value);
                            }
                        }
                    }
                    else if (!usedByAccount.Key.AccountId.HasValue &&
                             usedByAccount.Key.CustomerId.HasValue &&
                             usedByAccount.Value > 0)
                    {
                        if (usedByCustomerIds.Contains(usedByAccount.Key.CustomerId.Value))
                        {
                            if (usedByAccountsInfo.ContainsKey(usedByAccount.Key))
                            {
                                usedByAccountsInfo[usedByAccount.Key] += usedByAccount.Value;
                            }
                            else
                            {
                                usedByAccountsInfo.Add(usedByAccount.Key, usedByAccount.Value);
                            }
                        }
                    }
                }

                if (usedByAccountsInfo.Any())
                {
                    usedByDetails.Add(new UsedByInfo
                    {
                        EntityId = usedByInfo.EntityId,
                        EntityType = usedByInfo.EntityType,
                        UsedByType = usedByInfo.UsedByType,
                        UsedByAccounts = usedByAccountsInfo
                    });
                }
            }

            return usedByDetails;
        }
        private void GetAccountPilotingFeatures(CustomerCallContext context, out IDictionary<int, bool> PilotOfAccountDict)
        {
            bool isAccountEnabledMobileAppCampaignConversionGoal = Pilot.Instance.IsMobileAppCampaignConversionGoalEnabledForAccount(context.Logger, context.Request);

            PilotOfAccountDict = new Dictionary<int, bool>()
            {
                [(int)AccountFeatureFlag.MobileAppCampaignConversionGoal] = isAccountEnabledMobileAppCampaignConversionGoal
            };
        }

        private void GetPilotingFeatures(CustomerCallContext context, out IDictionary<int, bool> PilotOfCustomerDict)
        {
            bool isCustomerEnabledForInStoreVisitGoal = goalValidator.IsCustomerEnabledForInStoreVisitGoal(context);
            bool isCustomerEnabledEnhancedConversions = Pilot.Instance.IsEnabledForEnhancedConversions(context.Logger, context.Request);
            bool isCustomerEnabledAutoConversion = goalValidator.IsCustomerEnabledForAutoConversion(context);
            bool isCustomerEnabledForBoost = goalValidator.IsCustomerEnabledForBoost(context);

            PilotOfCustomerDict = new Dictionary<int, bool>()
            {
                [(int)CustomerFeatureFlag.InStoreVisitConversion] = isCustomerEnabledForInStoreVisitGoal,
                [(int)CustomerFeatureFlag.EnhancedConversions] = isCustomerEnabledEnhancedConversions,
                [(int)CustomerFeatureFlag.AutoConversion] = isCustomerEnabledAutoConversion,
                [(int)CustomerFeatureFlag.Boost] = isCustomerEnabledForBoost,
            };
        }

        private bool IsFeatureEnabled(IDictionary<int, bool> pilotOfCustomerDict, int featureId)
        {
            var isFeatureEnabled = false;

            if (pilotOfCustomerDict != null)
            {
                pilotOfCustomerDict.TryGetValue(featureId, out isFeatureEnabled);
            }

            return isFeatureEnabled;
        }

        private int? ProvisionClarityWithRetry(ILogAdvertiserCampaign logger, Tag tag, UETProvisionParameters parameter)
        {
            var methodName = $"{nameof(ProvisionClarityWithRetry)}";

            var maxRetryCount = DynamicConfigValues.UETRetryPolicyMaxCount;
            var initialInterval = DynamicConfigValues.UETRetryPolicyInitialInterval;
            var retryIncrementInterval = DynamicConfigValues.UETRetryPolicyIncrement;

            var httpCode = new Result<int?>();

            for (var attempted = 0; attempted < maxRetryCount; attempted++)
            {
                try
                {
                    if (attempted > 0)
                    {
                        logger.LogInfo($"{methodName}: Failed while calling Provision Clarity API, CurrentRetryCount: {attempted}");

                        Thread.Sleep(TimeSpan.FromSeconds(initialInterval + (attempted - 1) * retryIncrementInterval));
                    }

                    var provisonTask = ClarityAPIClient.provisionTagInClarity(parameter, logger);
                    if (provisonTask.Result != null && (provisonTask.Result == (int)HttpStatusCode.Created || provisonTask.Result == (int)HttpStatusCode.OK))
                    {
                        return provisonTask.Result;
                    }
                    else
                    {
                        logger.LogError($"{methodName}: Failed while calling Provision Clarity API with following code: {provisonTask.Result}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError($"{methodName}: Failed while calling Provision Clarity API with following exception: {ex}");
                }
            }

            return null;
        }

        private int? OptInOutClarityWithRetry(ILogAdvertiserCampaign logger, Tag tag, UETOptInOutParameters parameter)
        {
            var methodName = $"{nameof(OptInOutClarityWithRetry)}";

            var maxRetryCount = DynamicConfigValues.UETRetryPolicyMaxCount;
            var initialInterval = DynamicConfigValues.UETRetryPolicyInitialInterval;
            var retryIncrementInterval = DynamicConfigValues.UETRetryPolicyIncrement;

            var httpCode = new Result<int?>();

            for (var attempted = 0; attempted < maxRetryCount; attempted++)
            {
                try
                {
                    if (attempted > 0)
                    {
                        logger.LogInfo($"{methodName}: Failed while calling Opt In Out Clarity API, CurrentRetryCount: {attempted}");

                        Thread.Sleep(TimeSpan.FromSeconds(initialInterval + (attempted - 1) * retryIncrementInterval));
                    }

                    var optInOutTask = ClarityAPIClient.optInOutTagInClarity(parameter, logger);
                    if (optInOutTask.Result != null && (optInOutTask.Result == (int)HttpStatusCode.OK || optInOutTask.Result == (int)HttpStatusCode.NotFound || optInOutTask.Result == (int)HttpStatusCode.Forbidden))
                    {
                        return optInOutTask.Result;
                    }
                    else
                    {
                        logger.LogError($"{methodName}: Failed while calling Opt In Out Clarity API with following code: {optInOutTask.Result}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError($"{methodName}: Failed while calling Opt In Out Clarity API with following exception: {ex}");
                }
            }

            return null;
        }

        private int? DeleteClarityProjectWithRetry(ILogAdvertiserCampaign logger, ClarityDeleteParameters parameter)
        {
            var methodName = $"{nameof(DeleteClarityProjectWithRetry)}";

            var maxRetryCount = DynamicConfigValues.UETRetryPolicyMaxCount;
            var initialInterval = DynamicConfigValues.UETRetryPolicyInitialInterval;
            var retryIncrementInterval = DynamicConfigValues.UETRetryPolicyIncrement;

            var httpCode = new Result<int?>();

            for (var attempted = 0; attempted < maxRetryCount; attempted++)
            {
                try
                {
                    if (attempted > 0)
                    {
                        logger.LogInfo($"{methodName}: Failed while calling Clarity Project Delete API, CurrentRetryCount: {attempted}");

                        Thread.Sleep(TimeSpan.FromSeconds(initialInterval + (attempted - 1) * retryIncrementInterval));
                    }

                    var optInOutTask = ClarityAPIClient.DeleteClarityProject(parameter, logger);
                    if (optInOutTask.Result != null && (optInOutTask.Result == (int)HttpStatusCode.OK || optInOutTask.Result == (int)HttpStatusCode.NotFound))
                    {
                        return optInOutTask.Result;
                    }
                    else
                    {
                        logger.LogError($"{methodName}: Failed while calling Clarity Project Delete API with following code: {optInOutTask.Result}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError($"{methodName}: Failed while calling Clarity Project Delete API with following exception: {ex}");
                }
            }

            return null;
        }

        private async Task BackfillInvestUniversalPixelAsync(CustomerCallContext context, BatchResult<Tag> batchResult)
        {
            if (!DynamicConfigValues.EnableUPUetLink || batchResult.HasOperationErrors)
            {
                return;
            }

            var tagIds = new Dictionary<long, int>();
            foreach (var entity in batchResult.Entities)
            {
                tagIds.TryAdd(entity.Value.Id.Value, entity.Key);
            }

            if (tagIds.Count == 0)
            {
                return;
            }

            var getMappingResult = externalEntityMappingDao.GetExternalEntityMappingByEntityIds(
                context,
                MappingEntityType.Tag,
                new LineItemContainer<long>(tagIds.Keys.ToList(), null));

            if (getMappingResult.Entities.Count == 0)
            {
                return;
            }

            var getUPResult = await GetInvestUniversalPixelAsync(context, false);
            if (getUPResult.Failed)
            {
                batchResult.AddErrors(getUPResult.Errors);
                return;
            }

            var upDict = getUPResult.Entities.ToDictionary(e => e.Uuid, e => e);
            foreach (var mapping in getMappingResult.Entities)
            {
                if (upDict.TryGetValue(mapping.Value.ExternalEntityId, out var up))
                {
                    batchResult.Entities[tagIds[mapping.Value.EntityId]].InvestUniversalPixel = new InvestUniversalPixel
                    {
                        Id = up.Id,
                        Name = up.Name,
                        Uuid = up.Uuid
                    };
                }
                else
                {
                    batchResult.Entities[tagIds[mapping.Value.EntityId]].InvestUniversalPixel = new InvestUniversalPixel
                    {
                        Id = InvalidInvestUPId,
                        Name = InvalidInvestUPName,
                        Uuid = mapping.Value.ExternalEntityId
                    };
                }

            }
        }
    }
}
