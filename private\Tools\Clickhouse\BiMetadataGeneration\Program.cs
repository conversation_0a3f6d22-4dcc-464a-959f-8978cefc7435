﻿using Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse;
using Microsoft.AdCenter.Advertiser.BIDatamart.ClickhouseLocalTest;

/// <summary>
/// Entry point for the BiMetadataGeneration tool.
/// This program generates and validates metadata for Clickhouse BI data columns, BI data groups, query metadata, 
/// and columns for queries. It also generates corresponding C# code files for these metadata definitions.
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        // Get the current working directory.
        string curFolder = Directory.GetCurrentDirectory();

        // Extract the base folder path for the Clickhouse Query Builder Metadata.
        int idx = curFolder.IndexOf(@"\Tools\");
        string queryBuilderMetadataFolder = curFolder.Substring(0, idx) + @"\Common\Clickhouse\ClickhouseQueryBuilder\Metadata\";

        // Path to the JSON file containing all Clickhouse BI data columns.
        string allColumnsJsonFilePath = @".\AllCHBiDataColumns.json";

        // Generate a dictionary of all Clickhouse BI data columns from the JSON file.
        Dictionary<string, string> allCHBiDataColumns = BiMetadataGenerator.GenerateAllCHBiDataColumns(allColumnsJsonFilePath);

        // Validate the generated Clickhouse BI data columns.
        if (!BiMetadataValidator.ValdateAllCHBiDataColumns(allCHBiDataColumns, allColumnsJsonFilePath))
        {
            Console.WriteLine("AllCHBiDataColumns generation is wrong, please check and correct");
            return;
        }

        // Path to the C# file where Clickhouse data columns code will be generated.
        string chColumnsCodeFilePath = queryBuilderMetadataFolder + "ClickhouseDataColumns.cs";

        // Generate C# code for Clickhouse data columns.
        BiMetadataGenerator.GenerateCodeForCHDataColumns(allCHBiDataColumns, chColumnsCodeFilePath);

        // Path to the JSON file containing BI data groups.
        string biDataGroupsJsonFilePath = queryBuilderMetadataFolder + "BiDataGroup.json";

        // Generate a dictionary of BI data groups from the JSON file.
        Dictionary<BiDataCategory, BiDataGroup> biDataGroups = await BiMetadataGenerator.GenerateBiDataGroups(biDataGroupsJsonFilePath, allCHBiDataColumns);

        // Validate the generated BI data groups.
        if (!BiMetadataValidator.ValidateBiDataGroups(biDataGroups, biDataGroupsJsonFilePath))
        {
            Console.WriteLine("BiDataGroups generation is wrong, please check and correct");
            return;
        }

        // Path to the JSON file containing query metadata.
        string queryMetadatasJsonFilePath = queryBuilderMetadataFolder + "QueryMetadata.json";

        // Generate a dictionary of query metadata from the JSON file.
        Dictionary<ClickhouseQueryType, QueryMetadata> queryMetadatas = BiMetadataGenerator.GenerateQueryMetadatas(queryMetadatasJsonFilePath, allCHBiDataColumns);

        // Validate the generated query metadata.
        if (!BiMetadataValidator.ValidateQueryMetadata(queryMetadatas, queryMetadatasJsonFilePath))
        {
            Console.WriteLine("QueryMetadatas generation is wrong, please check and correct");
            return;
        }

        // Path to the C# file where column default values code will be generated.
        string columnDefaultValuesCodePath = queryBuilderMetadataFolder + "ColumnDefaultValues.cs";

        // Generate C# code for column default values.
        BiMetadataGenerator.GenerateCodeForColumnDefaultValues(columnDefaultValuesCodePath);

        // Path to the JSON file containing columns for queries.
        string columnsForQueriesJsonFilePath = curFolder.Substring(0, idx) + @"\Common\Clickhouse\ClickhouseQueryBuilderLocalTests\ColumnsForQueries.json";

        // Generate a dictionary of columns for queries from the JSON file.
        Dictionary<ClickhouseQueryType, ReportColumns> columnsForQueries = BiMetadataGenerator.GenerateColumnsForQueries(columnsForQueriesJsonFilePath, biDataGroups, queryMetadatas);

        // Validate the generated columns for queries.
        if (!BiMetadataValidator.ValidateColumnsForQueries(columnsForQueriesJsonFilePath, columnsForQueries))
        {
            Console.WriteLine("ColumnsForQueries generation is wrong, please check and correct");
            return;
        }

        // Indicate successful metadata generation.
        Console.WriteLine("Generate Bi Metadata successfully!");
    }
}
