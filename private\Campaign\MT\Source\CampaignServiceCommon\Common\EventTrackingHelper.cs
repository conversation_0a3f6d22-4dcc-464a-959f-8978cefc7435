﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common.EventTracking
{
    using System.Collections.Generic;
    using Goal = Entities.EventTracking.Goal;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using System.Collections.ObjectModel;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;

    public class EventTrackingHelper
    {
        private static int maxLookbackWindowDaysForLinkedIn = 30;
        private const int MinituesPerDay = 1440;

        private static List<GoalEntityType> _uetBasedGoalEntityTypes = new List<GoalEntityType>()
        {
                GoalEntityType.DestinationGoal,
                GoalEntityType.DurationGoal,
                GoalEntityType.PageViewsPerVisitGoal,
                GoalEntityType.EventGoal,
                GoalEntityType.ProductConversionGoal,
                GoalEntityType.SmartGoal
        };

        public static ReadOnlyCollection<GoalEntityType> UetBasedGoalEntityTypes
        {
            get { return _uetBasedGoalEntityTypes.AsReadOnly(); }
        }

        public static int ComputeWindowMinutes(int? Days, int? Hours, int? Minutes)
        {
            return ComputeWindowMinutes(Days ?? 0, Hours ?? 0, Minutes ?? 0);
        }

        public static int ComputeWindowMinutes(int Days, int Hours, int Minutes)
        {
            return Days * 1440 + Hours * 60 + Minutes;
        }

        public static int? ComputeWindowMinutesAllowNull(int? Days, int? Hours, int? Minutes)
        {
            if (!Days.HasValue && !Hours.HasValue && !Minutes.HasValue)
            {
                return null;
            }

            return ComputeWindowMinutes(Days, Hours, Minutes);
        }

        public static bool IsUetBasedGoal(Goal goal)
        {
            return goal.Type.HasValue && _uetBasedGoalEntityTypes.Contains(goal.Type.Value);
        }

        public static bool IsServableInLinkedIn(Goal goal, out List<CampaignManagementErrorCode> errorCodes)
        {
            errorCodes = new List<CampaignManagementErrorCode>();

            if (goal == null)
            {
                errorCodes.Add(CampaignManagementErrorCode.GoalsNotPassed);
                return false;
            }

            if (DynamicConfigValues.EnableLinkedInOfflineConversionGoal)
            {
                if (!(goal is DestinationGoal || goal is EventGoal || goal is OfflineConversionGoal))
                {

                    errorCodes.Add(CampaignManagementErrorCode.NotSupportedConversionGoalTypeForLinkedInCampaign);
                }
            }
            else
            {
                if (!(goal is DestinationGoal || goal is EventGoal))
                {

                    errorCodes.Add(CampaignManagementErrorCode.NotSupportedConversionGoalTypeForLinkedInCampaign);
                }
            }

            if (!goal.IsAccountLevel)
            {
                errorCodes.Add(CampaignManagementErrorCode.NotSupportedConversionGoalScopeForLinkedInCampaign);
            }

            if (goal.ConversionCountType != ConversionCountType.All)
            {
                errorCodes.Add(CampaignManagementErrorCode.NotSupportedGoalCountTypeForLinkedInCampaign);
            }

            if (goal.ExcludeFromBidding == true)
            {
                errorCodes.Add(CampaignManagementErrorCode.NotSupportedExcludeFromBiddingValueForLinkedInCampaign);
            }

            if (goal.AttributionModelType != null && goal.AttributionModelType != AttributionModelType.Default)
            {
                errorCodes.Add(CampaignManagementErrorCode.NotSupportedGoalAttributionModelForLinkedInCampaign);
            }

            var lookbackWindowMinutes = ComputeWindowMinutesAllowNull(goal.LookbackWindowDays, goal.LookbackWindowHours, goal.LookbackWindowMinutes);

            // For offline conversion goals, we only support 1, 7, and 30 days lookback windows.
            if (goal is OfflineConversionGoal offlineConversionGoal)
            {
                if (lookbackWindowMinutes != 1 * MinituesPerDay
                    && lookbackWindowMinutes != 7 * MinituesPerDay
                    && lookbackWindowMinutes != 30 * MinituesPerDay)
                {
                    errorCodes.Add(CampaignManagementErrorCode.NotSupportedGoalClickLookbackWindowForLinkedInCampaign);
                }
            }
            else if (lookbackWindowMinutes > maxLookbackWindowDaysForLinkedIn * MinituesPerDay)
            {
                errorCodes.Add(CampaignManagementErrorCode.NotSupportedGoalClickLookbackWindowForLinkedInCampaign);
            }

            return errorCodes.Count == 0;
        }

        public static string GenerateEventActionForAutoGoal(GoalCategory? goalCategory)
        {
            string actionHeader = "AutoEvent_";
            switch (goalCategory) {
                case GoalCategory.Purchase:
                    return actionHeader + "purchase";
                case GoalCategory.AddToCart:
                    return actionHeader + "add_to_cart";
                case GoalCategory.BeginCheckout:
                    return actionHeader + "begin_checkout";
                case GoalCategory.Subcribe:
                    return actionHeader + "subscribe";
                case GoalCategory.SubmitLeadForm:
                    return actionHeader + "submit_form";
                case GoalCategory.BookAppointment:
                    return actionHeader + "book_appointment";
                case GoalCategory.Signup:
                    return actionHeader + "sign_up";
                case GoalCategory.RequestQuote:
                    return actionHeader + "request_quote";
                case GoalCategory.GetDirections:
                    return actionHeader + "look_at_directions";
                case GoalCategory.OutboundClick:
                    return actionHeader + "outbound_click";
                case GoalCategory.Contact:
                    return actionHeader + "contact_us";
                default:
                    return string.Empty; //unsupported category for auto goal
            }
        }
    }
}

