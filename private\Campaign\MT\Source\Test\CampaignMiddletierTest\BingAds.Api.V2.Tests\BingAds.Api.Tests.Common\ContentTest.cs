﻿#if V2
namespace Microsoft.Advertising.Advertiser.Api.V2
#endif

#if V2
{
    using AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.MiddletierTestObjects;
    using CampaignTest.Framework.Constants;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Linq;
    using System.Net.Http;
    using VisualStudio.TestTools.UnitTesting;
    using TestCategoryNames = CampaignTest.Framework.TestCategoryNames;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages;
    using Microsoft.Data.SqlClient;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.Test.Combinatorics;

    [TestClass]
    public sealed class ContentTest : CampaignTestBase
    {
        private const string TypeProperty = "@odata.type";
        private const string TestPrefix = Constants.BingAdsInternalTestPrefix;
        private static Dictionary<long, string> TopicId2Name = new Dictionary<long, string>()
        {
            { 1, "Games" },
            { 2, "Shopping" },
            { 3, "Business & Industrial" },
            { 4, "Sports" },
            { 5, "Home & Garden" },
            { 6, "News" },
            { 7, "Travel & Transportation" },
            { 8, "Finance" },
            { 9, "Health" },
            { 10, "Jobs & Education" },
            { 11, "Arts & Entertainment" },
            { 12, "Autos & Vehicles" },
            { 13, "Computers & Electronics" },
            { 14, "Real Estate" },
            { 15, "Hobbies & Leisure" },
            { 16, "Beauty & Fitness" },
            { 17, "Internet & Telecom" },
            { 18, "Books & Literature" },
            { 19, "Law & Government" },
            { 20, "Food & Drink" },
            { 21, "People & Society" },
            { 22, "Online Communities" },
            { 23, "Science" },
            { 24, "Pets & Animals" },
            { 25, "Reference" },
            { 26, "World Localities" }
        };
        private static Dictionary<long, string> TopicId2FranceName = new Dictionary<long, string>()
        {
            { 1, "Les jeux" },
            { 2, "Le Shopping" },
            { 3, "Affaires et industriel" },
            { 4, "Les Sports" },
            { 5, "Maison & jardin" },
            { 6, "Les nouvelles" },
            { 7, "Voyage et transport" },
            { 8, "finances" },
            { 9, "La santé" },
            { 10, "Emplois & éducation" },
            { 11, "Arts et divertissement" },
            { 12, "Autos et véhicules" },
            { 13, "Informatique et électronique" },
            { 14, "Real Estate" },
            { 15, "Hobbies & Leisure" },
            { 16, "Beauty & Fitness" },
            { 17, "Internet & Telecom" },
            { 18, "Books & Literature" },
            { 19, "Law & Government" },
            { 20, "Nourriture et boisson" },
            { 21, "Personnes et société" },
            { 22, "Communautés en ligne" },
            { 23, "La Science" },
            { 24, "Animaux & animaux" },
            { 25, "Référence" },
            { 26, "Localités du monde" }
        };

        private static Dictionary<long, string> PlacementId2Name = new Dictionary<long, string>()
        {
            { 4, "MSN - Finance" },
            { 7, "MSN - Health" },
            { 12, "Microsoft Edge New Tab Page" },
            { 13, "Productivity" },
            { 14, "Gaming" },
            { 15, "MSN" }
        };
        private static Dictionary<long, string> sortedTopics = new Dictionary<long, string>();


        private static string TopicAccountUrl
        {
            get
            {
                return ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/Topics";
            }
        }

        private static string PlacementAccountUrl
        {
            get
            {
                return ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/ContentPlacements";
            }
        }

        [ClassInitialize]
        public static void Initialize(TestContext context)
        {
            sortedTopics = TopicId2Name.OrderBy(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory(TestCategoryNames.Functional)]
        [Owner(TestOwners.Audience)]
        public void GetTopics()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, false, true, AccountPilotFeatures.TopicTargeting);

            var getQuery = string.Format(TopicAccountUrl, customerInfo.CustomerId, customerInfo.AccountIds[0]);
            var getResponse = GetContentssByAccountId(customerInfo, getQuery);
            Assert.AreEqual(sortedTopics.Count, getResponse.value.Count);
            for (int i=0; i < getResponse.value.Count; i++)
            {
                Assert.AreEqual(sortedTopics.ElementAt(i).Key, getResponse.value[i].TopicId.Value);
                Assert.AreEqual(sortedTopics.ElementAt(i).Value, getResponse.value[i].TopicName.Value);
                Assert.IsNull(getResponse.value[i].ParentId.Value);
            }
        }

        [Ignore]
        [TestMethod]
        [Priority(0)]
        [TestCategory(TestCategoryNames.Functional)]
        [Owner(TestOwners.Audience)]
        public void GetTopics_Localization_CIOnly()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, false, true, AccountPilotFeatures.TopicTargeting);

            var commandStr = """
                IF NOT EXISTS (Select * from TopicLocalizedName where topicId = 1 and lcid = 1032)
                    INSERT INTO TopicLocalizedName VALUES (1, 1032, 'spiel', GETDATE(), GETDATE());
                IF NOT EXISTS (Select * from TopicLocalizedName where topicId = 2 and lcid = 1032)
                    INSERT INTO TopicLocalizedName VALUES (2, 1032, 'einkaufen', GETDATE(), GETDATE());
                """;
            for (int i =0; i<TopicId2FranceName.Count; i++)
            {
                var topicName = TopicId2FranceName.ElementAt(i).Value;
                commandStr += string.Format("IF NOT EXISTS (Select * from TopicLocalizedName where topicId = {0} and lcid = 1036) INSERT INTO TopicLocalizedName VALUES ({0}, 1036, '{1}', GETDATE(), GETDATE());", TopicId2FranceName.ElementAt(i).Key, topicName);
            }

            var dataSet = ExecutionDB(commandStr, customerInfo.CustomerId, customerInfo.AccountIds[0]);

            // 1036, we expect get all france topics
            var getQuery = string.Format(TopicAccountUrl, customerInfo.CustomerId, customerInfo.AccountIds[0]);
            var getResponse = GetContentssByAccountId(customerInfo, getQuery, lcidString: "1036");
            var franceTopics = TopicId2FranceName.OrderBy(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            Assert.AreEqual(franceTopics.Count, getResponse.value.Count);
            for (int i = 0; i < getResponse.value.Count; i++)
            {
                Assert.AreEqual(franceTopics.ElementAt(i).Key, getResponse.value[i].TopicId.Value);
                Assert.AreEqual(franceTopics.ElementAt(i).Value, getResponse.value[i].TopicName.Value);
                Assert.IsNull(getResponse.value[i].ParentId.Value);
            }

            // 1036, we expect get 2 Chinese topics, and 24 English topics
            getResponse = GetContentssByAccountId(customerInfo, getQuery, lcidString: "1032");
            Assert.AreEqual(26, getResponse.value.Count);
            for (int i = 0; i < getResponse.value.Count; i++)
            {
                var topicId = getResponse.value[i].TopicId.Value;
                if (topicId == 1)
                {
                    Assert.AreEqual("spiel", getResponse.value[i].TopicName.Value);
                }
                else if (topicId == 2)
                {
                    Assert.AreEqual("einkaufen", getResponse.value[i].TopicName.Value);
                }
                else
                {
                    Assert.AreEqual(TopicId2Name[topicId], getResponse.value[i].TopicName.Value);
                }                
                Assert.IsNull(getResponse.value[i].ParentId.Value);
            }

            // null, we expect get 26 English topics
            getResponse = GetContentssByAccountId(customerInfo, getQuery);
            Assert.AreEqual(sortedTopics.Count, getResponse.value.Count);
            for (int i = 0; i < getResponse.value.Count; i++)
            {
                Assert.AreEqual(sortedTopics.ElementAt(i).Key, getResponse.value[i].TopicId.Value);
                Assert.AreEqual(sortedTopics.ElementAt(i).Value, getResponse.value[i].TopicName.Value);
                Assert.IsNull(getResponse.value[i].ParentId.Value);
            }

            // we don't translate for lcid = 1031, we expect get 26 English topics
            getResponse = GetContentssByAccountId(customerInfo, getQuery, lcidString: "1031");
            Assert.AreEqual(sortedTopics.Count, getResponse.value.Count);
            for (int i = 0; i < getResponse.value.Count; i++)
            {
                Assert.AreEqual(sortedTopics.ElementAt(i).Key, getResponse.value[i].TopicId.Value);
                Assert.AreEqual(sortedTopics.ElementAt(i).Value, getResponse.value[i].TopicName.Value);
                Assert.IsNull(getResponse.value[i].ParentId.Value);
            }
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory(TestCategoryNames.Functional)]
        [Owner(TestOwners.Audience)]
        public void GetTopics_ExpectFail()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, false, true);
            var getQuery = string.Format(TopicAccountUrl, customerInfo.CustomerId, customerInfo.AccountIds[0]);

            var getResponse = GetContentssByAccountId(customerInfo, getQuery, expectSuccess: false);
            Assert.AreEqual("AccountIsNotEligibleForTopicTargeting", getResponse.value[0]["Code"].ToString());
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory(TestCategoryNames.Functional)]
        [Owner(TestOwners.Audience)]
        public void GetPlacements()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, false, true, AccountPilotFeatures.PlacementTargeting);

            var getQuery = string.Format(PlacementAccountUrl, customerInfo.CustomerId, customerInfo.AccountIds[0]);
            var getResponse = GetContentssByAccountId(customerInfo, getQuery);
            Assert.AreEqual(PlacementId2Name.Count, getResponse.value.Count);
            for (int i = 0; i < getResponse.value.Count; i++)
            {
                Assert.AreEqual(PlacementId2Name.ElementAt(i).Key, getResponse.value[i].PlacementId.Value);
                Assert.AreEqual(PlacementId2Name.ElementAt(i).Value, getResponse.value[i].PlacementName.Value);
            }
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory(TestCategoryNames.Functional)]
        [Owner(TestOwners.Audience)]
        public void GetPlacements_ExpectFail()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, false, true);

            var getQuery = string.Format(PlacementAccountUrl, customerInfo.CustomerId, customerInfo.AccountIds[0]);
            var getResponse = GetContentssByAccountId(customerInfo, getQuery, expectSuccess: false);
            Assert.AreEqual("AccountIsNotEligibleForPlacementTargeting", getResponse.value[0]["Code"].ToString());
        }

        public static dynamic GetContentssByAccountId(CustomerInfo customerInfo, string query, bool expectSuccess=true, string lcidString = null)
        {
            var response = ApiHelper.CallApi(customerInfo, c => c.GetAsync(query), e => Assert.AreEqual(expectSuccess, e.IsSuccessStatusCode), lcidString: lcidString);

            Assert.IsNotNull(response);

            return response;
        }
        private DataSet ExecutionDB(string commandStr, int customerId, int accountId)
        {
            var command = new SqlCommand(commandStr);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out DataSet dataSet);

            return dataSet;
        }
    }
}
#endif

