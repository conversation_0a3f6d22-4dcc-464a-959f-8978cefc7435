﻿//--------------------------------------------------------------------------
// <copyright file="ConversionGoal.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// <summary>This file contains entity definition for ConversionGoal </summary>
//--------------------------------------------------------------------------

namespace Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13
{
    using System.Runtime.Serialization;

    /// <summary>
    /// ConversionGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "ConversionGoal")]
    [KnownType(typeof(UrlGoal))]
    [KnownType(typeof(DurationGoal))]
    [KnownType(typeof(PagesViewedPerVisitGoal))]
    [KnownType(typeof(EventGoal))]
    [KnownType(typeof(AppInstallGoal))]
    [KnownType(typeof(OfflineConversionGoal))]
    [KnownType(typeof(InStoreTransactionGoal))]
    public abstract class ConversionGoal
    {
        [DataMember(Name = "Id")]
        public long? Id { get; set; }

        [DataMember(Name = "Name")]
        public string Name { get; set; }

        [DataMember(Name = "Status")]
        public ConversionGoalStatus? Status { get; set; }

        [DataMember(Name = "Type")]
        public ConversionGoalType? Type { get; set; }

        [DataMember(Name = "Scope")]
        public EntityScope? Scope { get; set; }

        [DataMember(Name = "CountType")]
        public ConversionGoalCountType? CountType { get; set; }

        [DataMember(Name = "Revenue")]
        public ConversionGoalRevenue Revenue { get; set; }

        [DataMember(Name = "ConversionWindowInMinutes")]
        public int? ConversionWindowInMinutes { get; set; }

        [DataMember(Name = "TagId")]
        public long? TagId { get; set; }

        [DataMember(Name = "TrackingStatus")]
        public ConversionGoalTrackingStatus? TrackingStatus { get; set; }

        [DataMember(Name = "ExcludeFromBidding")]
        public bool? ExcludeFromBidding { get; set; }

        [DataMember(Name = "ViewThroughConversionWindowInMinutes", EmitDefaultValue = false)]
        public int? ViewThroughConversionWindowInMinutes { get; set; }

        [DataMember(Name = "GoalCategory", EmitDefaultValue = false)]
        public ConversionGoalCategory? GoalCategory { get; set; }

        [DataMember(Name = "AttributionModelType", EmitDefaultValue = false)]
        public AttributionModelType? AttributionModelType { get; set; }

        [DataMember(Name = "IsEnhancedConversionsEnabled", EmitDefaultValue = false)]
        public bool? IsEnhancedConversionsEnabled { get; set; }

        [DataMember(Name = "IsAutoGoal", EmitDefaultValue = false)]
        public bool? IsAutoGoal { get; set; }
    }

    /// <summary>
    /// ConversionGoalRevenue entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "ConversionGoalRevenue")]
    public class ConversionGoalRevenue
    {
        [DataMember(Name = "Type")]
        public ConversionGoalRevenueType? Type { get; set; }

        [DataMember(Name = "Value")]
        public decimal? Value { get; set; }

        [DataMember(Name = "CurrencyCode")]
        public string CurrencyCode { get; set; }
    }

    /// <summary>
    /// UrlGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "UrlGoal")]
    public class UrlGoal : ConversionGoal
    {
        [DataMember(Name = "UrlExpression")]
        public string UrlExpression { get; set; }

        [DataMember(Name = "UrlOperator")]
        public ExpressionOperator? UrlOperator { get; set; }
    }

    /// <summary>
    /// DurationGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "DurationGoal")]
    public class DurationGoal : ConversionGoal
    {
        [DataMember(Name = "MinimumDurationInSeconds")]
        public int? MinimumDurationInSeconds { get; set; }
    }

    /// <summary>
    /// PagesViewedPerVisitGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "PagesViewedPerVisitGoal")]
    public class PagesViewedPerVisitGoal : ConversionGoal
    {
        [DataMember(Name = "MinimumPagesViewed")]
        public int? MinimumPagesViewed { get; set; }
    }

    /// <summary>
    /// EventGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "EventGoal")]
    public class EventGoal : ConversionGoal
    {
        [DataMember(Name = "CategoryExpression")]
        public string CategoryExpression { get; set; }

        [DataMember(Name = "CategoryOperator")]
        public ExpressionOperator? CategoryOperator { get; set; }

        [DataMember(Name = "ActionExpression")]
        public string ActionExpression { get; set; }

        [DataMember(Name = "ActionOperator")]
        public ExpressionOperator? ActionOperator { get; set; }

        [DataMember(Name = "LabelExpression")]
        public string LabelExpression { get; set; }

        [DataMember(Name = "LabelOperator")]
        public ExpressionOperator? LabelOperator { get; set; }

        [DataMember(Name = "Value")]
        public decimal? Value { get; set; }

        [DataMember(Name = "ValueOperator")]
        public ValueOperator? ValueOperator { get; set; }
    }

    /// <summary>
    /// AppInstallGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "AppInstallGoal")]
    public class AppInstallGoal : ConversionGoal
    {
        [DataMember(Name = "AppPlatform")]
        public string AppPlatform { get; set; }

        [DataMember(Name = "AppStoreId")]
        public string AppStoreId { get; set; }
    }

    /// <summary>
    /// OfflineConversionGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "OfflineConversionGoal")]
    public class OfflineConversionGoal : ConversionGoal
    {
        [DataMember(EmitDefaultValue = false)]
        public bool? IsExternallyAttributed { get; set; }
    }

    /// <summary>
    /// InStoreTransactionGoal entity data contract
    /// </summary>
    [DataContract(
        Namespace = Microsoft.AdCenter.Shared.Api.V13.Constants.NamespaceConstants.AdvertiserApi,
        Name = "InStoreTransactionGoal")]
    public class InStoreTransactionGoal : ConversionGoal
    {

    }
}