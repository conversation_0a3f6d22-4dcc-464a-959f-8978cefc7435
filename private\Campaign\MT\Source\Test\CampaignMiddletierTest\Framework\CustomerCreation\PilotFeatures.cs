﻿namespace CampaignMiddleTierTest.Framework
{
    using CampaignMiddleTierTest.Framework.Validators;
    using System.Collections.Generic;

    public class Features
    {
        /// <summary>
        /// Advertiser Campaign Manager
        /// </summary>
        public static int CPA
        {
            get
            {
                return 16;
            }
        }

        public static int AdultAds
        {
            get
            {
                return 21;
            }
        }

        public static int Opportunity
        {
            get
            {
                return 29;
            }
        }

        public static int KeywordsByMatchType
        {
            get
            {
                return 39;
            }
        }

        public static int MarketRedefinition
        {
            get
            {
                return 35;
            }
        }

        public static int InlineEditorialAppeal
        {
            get
            {
                return 57;
            }
        }

        public static int InlineBudgetSuggestions
        {
            get
            {
                return 58;
            }
        }

        public static int LocationExclusion
        {
            get
            {
                return 62;
            }
        }

        public static int SiteLinkAdExtension
        {
            get
            {
                return 56;
            }
        }

        public static int LocationExtension
        {
            get
            {
                return 66;
            }
        }

        public static int PhoneExtension
        {
            get
            {
                return 67;
            }
        }

        public static int CallMetering
        {
            get
            {
                return 78;
            }
        }

        public static int SearchTerms
        {
            get
            {
                return 68;
            }
        }

        public static int ProductExtension
        {
            get
            {
                return 70;
            }
        }

        public static int ImageExtension
        {
            get
            {
                return 123;
            }
        }

        public static int CurrencyPilot
        {
            get
            {
                return 74;
            }
        }

        public static int CurrencyPilotForHK
        {
            get
            {
                return 76;
            }
        }

        public static int CurrencyPilotForBR
        {
            get
            {
                return 81;
            }
        }

        public static int CurrencyPilotForAU
        {
            get
            {
                return 91;
            }
        }

        public static int CurrencyPilotForNZ
        {
            get
            {
                return 92;
            }
        }

        public static int CurrencyPilotForMENA
        {
            get
            {
                return 86;
            }
        }

        public static List<int> OptOutDisallowedFeatureList
        {
            get
            {
                return new List<int>() { MarketRedefinition, KeywordsByMatchType };
            }
        }

        public static int DayTimePostalCodeTarget
        {
            get { return 116; }
        }

        public static int StructuredNegativeKeywords
        {
            get { return 117; }
        }

        public static int EnhancedExactMatch
        {
            get { return 118; }
        }

        public static int ElectronicInsertionOrder
        {
            get { return 119; }
        }

        public static int RadiusTargetingImprovements
        {
            get { return 124; }
        }

        public static int EnhancedSiteLink
        {
            get { return 122; }
        }

        public static int Enable10KLocationTargets
        {
            get { return 138; }
        }

        public static int DeviceTargetingConsolidationOptOut
        {
            get { return 144; }
        }

        public static int EnableDeviceTargetingConsolidation2
        {
            get { return 166; }
        }

        public static int EnableAudienceBidBoosting
        {
            get { return 152; }
        }


        public static int EnhancedShoppingCampaignsFlagId
        {
            get { return 163; }
        }

        public static int EnableNativeAdsFlagId
        {
            get { return 190; }
        }

        public static int EnableNativeAdsImageExtensionFlagId
        {
            get { return 203; }
        }

        public static int AppInstallAds
        {
            get { return 201; }
        }

        public static int UpgradedUrlsFlagId
        {
            get { return 194; }
        }

        public static int UpgradedUrlsForProductParitionFlagId
        {
            get { return 242; }
        }

        public static int ContentAdvertising
        {
            get { return 208; }
        }

        public static int Segmentaion
        {
            get { return 210; }
        }

        public static int AutoBiddingSupportEnhancedCpcForBSC
        {
            get { return 340; }
        }

        public static int SharedBudget
        {
            get { return 263; }
        }

        public static int SitelinkMigration
        {
            get { return 253; }
        }
        public static int BlacklistMigration
        {
            get { return 266; }
        }

        public static int SharedBudgetImport
        {
            get { return 314; }
        }

        public static int PhoneNumberProviderMigration
        {
            get { return 336; }
        }

        public static int OfflineConversion
        {
            get { return 279; }
        }

        public static int OfflineConversionReportV2 => 1415;
        public static int InStoreTransaction
        {
            get { return 353; }
        }

        public static int InStoreVisit
        {
            get { return 902; }
        }

        public static int RemarketingListBasedParameters
        {
            get { return 1017; }
        }

        public static int GoalCurrencyAcrossAllAccounts
        {
            get { return 359; }
        }

        public static int ExcludeAccMulImgExtension
        {
            get
            {
                return 828;
            }
        }

        public static int SharedLibraryForOtherAudienceType
        {
            get
            {
                return 1281;
            }
        }

        public static int KeywordTargeting
        {
            get
            {
                return 1323;
            }
        }

        public static int BaiduFileImport
        {
            get
            {
                return 1324;
            }
        }

        public static int ImpressionBasedRemarketingList
        {
            get
            {
                return 1393;
            }
        }

        public static int IncreaseDescriptionLengthForEXTA => 510;

        public static int ExpandedTextAdsTwoNewFields => 511;

        public static int ActionAdExtension => 244;

        public static int ExpandedTextAd => 275;

        public static int CampaignLanguages => 310;

        public static int LanguageMarketExpansions => 641;

        public static int AdGroupNameCharLimit256 => 391;

        // This feature will never be GA'ed. This is just to afford the Pharma customers
        // an exception on the ExTA Display URL non-editability.
        public static int VanityUrl => 311; // Do NOT Include in All Features

        public static int DSAPilot => 268;

        public static int DisableSTA => 370;

        public static int WhitelistSTA => 371;

        public static int Countdown => 368;

        public static int UETMigration => 389;

        public static int AIM => 388;

        public static int Smart => 571;

        public static int AIMCampaignLevelAudienceTargetingFlagId = 534;

        public static int CoOpCampaign => 425;

        public static int SponsoredProductAdsV2 => 684;

        public static int InHousePromotion => 453;

        public static int UserBasedEditorial => 450;

        public static int LinkedInTargetingForSearchBscDsa => 467;
        public static int GoogleImportInMarketAudienceCriterion => 472;

        public static int AdCustomizer => 480;

        public static int LocalInventoryAds => 351;

        public static int AdClickParallelTracking => 474;

        public static int LocalInventoryAdsV2FlagId => 486;

        public const int ProductPartitionNodeLowBidWarningFlagId = 487;

        public const int KwdDestUrlUpdateDisabledExclusionId = 531;

        public const int RaiseLowBidsToMinServingBidsForBsc = 508;

        public static int UseDataServiceForReportsFlagId => 491;

        public static int Experiment => 500;

        public static int MultiAccountBulk => 209;

        public static int RSA => 525;

        public static int EnhancedResponsiveAd => 526;

        public static int InMarketAudience => 315;

        public static int SharedLibraryFlagId = 506;

        public static int CustomerHierarchyFlagId = 449;

        public static int FinalUrlSuffixPhase1FlagId => 533;

        public static int AccountReparentingFlagId = 662;

        public static int HotelAdsV1FlagId = 344;

        public static int FinalUrlSuffixPhase2FlagId => 566;

        public static int CustomParameterCountIncreasePhase1FlagId => 532;

        public static int CustomParameterCountIncreasePhase2FlagId => 565;

        public static int ConversionGoalSelectionFlagId => 574;

        public static int CustomerMatch => 579;

        public static int CustomerMatchCRM => 580;

        public static int AdExtensionDestUrlUpdateDisabledInclusion => 601;

        public static int AdExtensionDestUrlUpdateDisabledExclusion => 603;

        public static int ExpandedDsaPhase2FlagId => 600;

        public static int IFFunctionForEXTAPilotFlagId = 570;

        public static int NewMarketForBSC => 589;

        public static int DynamicDataFeed => 607;

        public static int ScheduledFeedUploadFlagId = 617;

        public static int MutableDisclaimers => 1269;

        public static int ViewThroughConversionFlagId => 616;

        public static int ProductConversionGoalFlagId => 643;

        public static int CampaignInMarketAudienceCriterionGoogleImport => 632;

        public static int CookieBasedExperiments => 637;

        public static int FinalUrlSuffixPhase3FlagId => 636;

        public static int RsaCombinationExtendedBI => 640;

        public static int DynamicSearchAdsInCHandNLandITandESandSE => 653;

        public static int AdScheduleTimeZoneSetting => 655;

        public static int ManagerAccountSharedWebsiteExclusions => 697;

        public static int CallTrackingInAU => 715;

        public static int AutoBiddingMaxConversionValueEnabled => 716;

        public static int SmartShoppingCampaign => 718;

        public static int AutoBiddingTargetRoasEnabledNew => 721;

        public static int ImportAsServiceEnabled => 734;

        public static int AutoBiddingDropConversionCountAndRevenueCheckPilot => 740;

        public static int FileImportApiEnabled => 755;

        public static int AutoBiddingTargetImpressionShareEnabled => 762;

        //public static int FeedItemInBlobEnabled => 765;

        public static int DynamicFeedCampaign => 766;

        public static int ConversionGoalCategoryFlagId => 743;

        public static int AccountReparentingUET => 770;

        public static int ThirdPartyConversions = 773;

        public static int OfflineConversionRestateRetract = 787;

        public static int EnableResponsiveAdsForSmartShopping => 777;

        public static int SmartPage => 782;

        public static int ManualBiddingDeprecationOptOut => 786;

        public static int DSAMixedModeCampaign => 791;

        public static int AdsGlobalizationPhase1 => 794;

        public static int IsCustomerEnabledForDynamicSearchAdsGlobalizationEUPhase1 => 803;

        public static int IsCustomerEnabledForDynamicSearchAdsGlobalizationEUPhase2 => 916;

        public static int IsAccountEnabledForAccountNegativeKeywordList => 1289;

        public static int DSADanishFinnishNorwegianSupport => 804;

        public static int PortfolioBidStrategy => 823;

        public static int MultiMediaAds => 839;

        public static int VideoAds => 840;

        public static int UnifiedProduct => 810;

        public static int UnifiedProductCCAPI => 891;

        public static int ShoppingAdsDenmarkMarket = 814;

        public static int ShoppingAdsFinlandMarket = 815;

        public static int ShoppingAdsIrelandMarket = 816;

        public static int ShoppingAdsNorwayMarket = 817;

        public static int ShoppingAdsPolandMarket = 818;

        public static int ShoppingAdsPortugalMarket = 819;

        public static int ShoppingAdsTurkeyMarket = 820;

        public static int ShoppingAdsGreeceMarket = 821;

        public static int ShoppingAdsSouthAfricaMarket = 822;

        public static int SharedRemarketingAudienceGoogleImport => 856;

        public static int ManualCPM = 858;

        public static int HotelPayPerStay => 875;

        public static int PersonalizedOffers = 880;

        public static int BSCImportMappingImprovement => 876;

        public static int AdsGlobalizationPhase2 = 881;

        public static int ParallelTrackingExceptionList = 890;

        public static int RSACountdown = 883;

        public static int RSALocation = 884;

        public static int Shopping21MarketsForAdsGBLPhase1 = 893;

        public static int MultiMediaAdsPhase2 => 903;

        public static int AllowCrawlImagesFromLandingPage = 913;

        public static int AdExtensionActionTypesPhase4 = 922;

        public static int UETTagClarityIntegration = 936;

        public static int EnableDynamicDescription = 939;

        public static int BusinessAttributes = 942;
        public static int HotelPayPerStayPhase2 => 944;

        public static int PersonalizedOffersMVP = 949;
        public static int PersonalizedOffersV3 = 979;

        public static int GoogleTagManagerImport = 964;

        public static int VideoAdsEditorCustomThumbnail = 968;

        public static int HotelCampaign = 973;

        public static int PersonalizedOffersCPS = 991;
        public static int MMAForProductAds = 994;

        public static int NeighborhoodLocationTargeting => 1008;

        public static int UrlEqualsInDSATarget => 1025;

        public static int GoogleImportVideoAds => 1028;

        public static int PartialConversion => 1035;
        public static int AdsGlobalizationPhase2Japanese => 1043;
        public static int ProjectEndor => 1041;
        public static int NegativeAgeTargetForAdGroups => 1274;
        public static int IsCustomerEnabledForDynamicSearchAdsGlobalizationJapan => 1044;
        public static int M365IntegrationSupport = 1059;
        public static int ConversionGoalAttributionModel => 1054;
        public static int MultiMediaAdsInDSAAdGroup => 1060;
        public static int SmartCampaignImport => 1066;
        public static int RSAInDisclaimerCampaigns => 1071;
        public static int WhitelistEXTA => 1074;
        public static int AdsGlobalizationPhase6MENA => 1063;
        public static int DisableEXTA => 1075;
        public static int DynamicDataAutosListingFeed => 1081;
        public static int DynamicDataCreditCardsFeed => 1082;
        public static int DynamicDataCruisesFeed => 1083;
        public static int DynamicDataCustomFeed => 1084;
        public static int DynamicDataEventsFeed => 1085;
        public static int DynamicDataHealthInsuranceFeed => 1086;
        public static int DynamicDataHotelsAndVacationRentalsFeed => 1087;
        public static int DynamicDataProfessionalServiceFeed => 1088;
        public static int DynamicDataToursAndActivitiesFeed => 1089;
        public static int DynamicDataMortgageLendersFeed => 1090;
        public static int DynamicDataAutosAggregateFeed => 1091;
        public static int WhitelistEXTAAdCustomizerFeedUpdate => 1117;
        public static int OptimizedTargeting => 1120;
        public static int DynamicDataDebitCardsFeed => 1122;
        public static int OnlineConversionRestateRetract => 1124;
        public static int SponsoredPromotionsForBrands => 1128;
        public static int MMAAdCustomizer => 1132;
        public static int AdsGlobalizationSimplifiedChinese => 1134;
        public static int OfflineConversionDailyReport => 1135;
        public static int PerformanceMaxCampaigns => 1147;
        public static int ShoppableAds => 1150;
        public static int MaxConversionForSSC => 1162;

        public static int CustomEventTool => 1163;

        public static int ConversionTile => 1126;

        public static int LeadFormAdExtension => 1170;

        public static int EnhancedCustomCombinationLists => 1171;

        public static int EnhancedConversions => 1229;

        public static int AssetLibraryV2MVP => 1174;

        public static int CNMarketExpansion => 1183;

        public static int RSAAutoGeneratedAssets => 1187;

        public static int AssetLibraryV3 => 1188;

        public static int EnableGoalLevelChange => 1191;

        public static int EnableTagDeletion => 1192;

        public static int XandrIntegration => 1195;

        public static int DynamicDataJobListingsFeed => 1200;

        public static int LogoAdExtension = 1215;
        
        public static int SearchCampaignPredictiveTargeting => 1216;

        public static int MutableKeywordMatchType => 1227;

        public static int DeprecateManualCpcForAudienceCampaign => 1221;
        public static int FeedLabel => 1224;

        public static int AssetLibraryV4 => 1234;

        public static int IASForBrandAwarenessCampaign = 1241;

        public static int SSCToPerformanceMaxUpgrade => 1246;
        public static int AutomatedCallToAction => 1251;
        public static int DealForMsAds => 1252;
        public static int WebInsights => 1253;
        public static int EnableAutobiddingLimitedStatus => 1255;
        public static int EnableMultiChannelCampaign => 1259;

        public static int YahooExclutionForSWF => 1265;

        public static int AssetLibraryAIGC => 1276;
        public static int PageFeedsInPerformanceMax => 1280;
        public static int ConversionValueRules => 1282;
        public static int AdStrength => 1284;
        public static int PMaxDraftStoreImport => 1287;

        public static int DatamartClickhouseMigration => 1291;
        public static int DatamartClickhouseMigrationPhase2 => 1297;

        public static int AdsGlobalizationPhase9 => 1301;
        public static int AdsGlobalizationPhase9DSA => 1396;
        public static int AdsGlobalizationPhase9VI => 1302;

        public static int AdsGlobalizationPAKorea => 1537;

        public static int LocationIntentChange => 1317;

        public static int BingPlacesMSASignup => 1320;

        public static int BroadOnlyCampaign => 1325;

        public static int PerformanceMaxAutomatedCTA => 1327;

        public static int AssetBasedEditorial => 1350;
        
        public static int SmartShoppingCampaignDeprecation => 1352; 

        public static int MCVForPortfolioBidStrategy => 1354;

        public static int PerformanceMaxCampaignLevelAutoGenAssetsControl => 1355;

        public static int EnableGoalBulk => 1356;

        public static int PerformanceMaxPhaseZeroWhitelist = 1358;

        public static int DataExclusionForAudienceCampaignType = 1359;

        public static int PerformanceMaxBrandExclusion = 1363;

        public static int EnableGoalImportPhase1 = 1365;

        public static int IsAccountEnableForAppCampaign = 1373;
        
        public static int PerformanceMaxCampaignLevelCostPerSaleOptOut => 1399;

        public static int VanityPharmaWebsiteDescription = 1402;

        public static int SyndicationSearchOnlyDeprecation = 1397;

        public static int SyndicationOnlyWhitelist = 1400;

        public static int AutoConversion = 1369;

        public static int AssetReports = 1416;

        public static int BrandSafety => 1417;
        
        public static int PerformanceMaxCampaignAssetGroupSearchTheme => 1424;

        public static int LinkedInCampaign => 1425;

        public static int PerformanceMaxCampaignAudienceInsight => 1495;

        public static int SearchCampaignPredictiveMatching => 1426;

        public static int ShoppingAimandNativePAUnification  => 1371;

        public static int MMAForCN  => 1435;

        public static int LogoAdExtensionCN  => 1436;

        public static int PerformanceMaxCampaignAssetGroupUrlTarget  => 1440;

        public static int DoubleVerifyActivation => 1446;

        public static int ModeledConversionUpliftReport => 1457;

        public static int ImpressionBasedRemarketingForSearchAndPMax => 1454;

        public static int CallToActionOptOut => 1459;

        public static int PmaxNewCustomerAcquisition => 1460;

        public static int PerformanceMaxCampaignInChina => 1461;

        public static int AppCampaignInChina => 1465;

        public static int BrandSafetyWithUnscored => 1468;

        public static int AudienceCampaignExpandedHeadlineCharLimits => 1478;

        public static int UnifiedAppCampaign => 1479;

        public static int CallToActionDefaultOptOut => 1480;     

        public static int ImageFitting => 1487;

        public static int ImageFittingExcludeForMAE => 1488;

        public static int CombinationReports => 1489;

        public static int AdsStudioFeatures => 1490;

        public static int BrandKit => 1505;

        public static int BrandKitForPhase2 => 1575;

        public static int RecommendationAPIV2 => 1581;

        public static int PmaxDsaUrlValidation => 1503;

        public static int EnablePmaxAssetInAssetGrid => 1516;
        
        public static int ROIUnify => 1517;

        public static int AccountPlacementExclusionList => 1525;

        public static int AccountPlacementInclusionList => 1526;

        public static int EnableLinkedInTargetBulkUpload => 1534;

        public static int AudienceShoppingCampaignFeedLabel => 1538;

        public static int EnablePMaxV2AspectRatios => 1543;

        public static int LifetimeBudgetEnabled => 1535;

        public static int LifetimeBudgetEnabledForImport => 1536;

        public static int ExcludeCampaignLevelDates => 1545;

        public static int VerticalVideoSupport => 1549;

        public static int VideoAdsGeneration => 1551;

        public static int VerticalCatetoryReport => 1560;

        public static int CustomerMigratedFromBulkDB => 1553;

        public static int SmartCampaignToPMaxMigration => 1565;

        public static int MTAssetBasedEditorialRSA => 1570;

        public static int MTAssetBasedEditorialAIM => 1571;

        public static int PerformanceMaxSelect = 1573;
        public static int BrandKitBulk => 1594;

        public static int WindowsAppCampaignStartExperience = 1595;
        public static int WindowsAppCampaignLegacyCreationFlowDeprecation = 1596;
        public static int HTML5Asset => 1597;

        public static int TravelQueryInsightsReport => 1610;


        public static int AutobiddingConsolidationFillGapsEnabled => 1618;
        public static int AutobiddingConsolidationEnabled => 1619;
        
        public static int PMaxAssetGroupSegmentation => 1624;

        public static int IBRMultiCampaigns => 1626;

        public static int CNMarketCalloutSSAndPriceExtensions => 1628;

        public static int ConversionDelayMetrics => 1629;

        public static int MobileAppCampaignConversionGoal => 1631;

        /// <summary>
        /// !!! AVOID ADDING TO THIS LIST AS MUCH AS POSSIBLE !!!
        /// These are features that affect DefaultCustomer.
        /// The goal is to remove this array, but many existing tests depend on Standard Text Ad and AppInstallAd pilots so these exist until
        /// the hundreds of tests are updated.
        /// Other features are still in this list because they are not GA in SI (they are GA in CI though).
        /// </summary>
        public static int[] DefaultFeatures => new[]
            {
                Features.WhitelistSTA,
                Features.WhitelistEXTA,
                Features.AppInstallAds,
                Features.ManualBiddingDeprecationOptOut,

                Features.PmaxNewCustomerAcquisition,

                // Not GA in SI
                Features.AutoBiddingSupportEnhancedCpcForBSC,
                Features.AdCustomizer,
                Features.ViewThroughConversionFlagId,
                Features.ConversionGoalSelectionFlagId,
                Features.LinkedInTargetingForSearchBscDsa,
                Features.SharedRemarketingAudienceGoogleImport,
                Features.ProductConversionGoalFlagId,
                Features.ImpressionBasedRemarketingList,
                Features.ImpressionBasedRemarketingForSearchAndPMax
            };
    }
}
