﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <PackageReference Update="AccountReparenting.ClientLibrary" Version="1.0.13" />
    <PackageReference Update="Ads.BI.DataStore" Version="1.3.4-alpha" />
    <PackageReference Update="Ads.BI.PipelineApps.Egress" Version="7.1.1-alpha" />
    <PackageReference Update="Ads.RnR.SmartCampaigns.EstimatorSchemas" Version="1.1.16" />
    <PackageReference Update="AdsApps.Deployer" Version="0.0.6" />
    <PackageReference Update="AdsAppsContainerTools" Version="1.0.23" />
    <PackageReference Update="AdsAppsHelm" Version="3.8.2" />
    <PackageReference Update="Ai.AdsMds.Library" Version="24.4.29" />
    <PackageReference Update="Antlr4.CodeGenerator" Version="4.6.6" />
    <PackageReference Update="Antlr4.Runtime" Version="4.6.6" />
    <PackageReference Update="ARR.Tools.AdsApps" Version="1.1.0" />
    <PackageReference Update="aspnetcoreiismodule" Version="3.1.0" />
    <PackageReference Update="AutoMapper" Version="14.0.0" />
    <PackageReference Update="Azure.Communication.CallAutomation" Version="1.0.0" />
    <PackageReference Update="Azure.Communication.PhoneNumbers" Version="1.1.0-beta.2" />
    <PackageReference Update="Azure.Core" Version="********" />
    <PackageReference Update="Azure.Data.Tables" Version="12.11.0.0" />
    <PackageReference Update="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
    <PackageReference Update="Azure.Identity" Version="1.14.0.0" />
    <PackageReference Update="Azure.Messaging.EventGrid" Version="4.11.0" />
    <PackageReference Update="Azure.Messaging.EventHubs.Processor" Version="5.11.3" />
    <PackageReference Update="Azure.Messaging.EventHubs" Version="5.11.3" />
    <PackageReference Update="Azure.Messaging.ServiceBus" Version="7.2.1" />
    <PackageReference Update="Azure.ResourceManager.DataFactory" Version="1.2.0" />
    <PackageReference Update="Azure.Security.KeyVault.Certificates" Version="4.6.0" />
    <PackageReference Update="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Update="Azure.Storage.Blobs" Version="12.24.0.0" />
    <PackageReference Update="Azure.Storage.Files.DataLake" Version="12.22.0.0" />
    <PackageReference Update="Azure.Storage.Files.Shares" Version="12.22.0.0"/>
    <PackageReference Update="Azure.Storage.Queues" Version="12.22.0.0" />
    <PackageReference Update="AzureProfiler.Ubuntu-20.04" Version="0.0.5" />
    <PackageReference Update="BingAds.Apps.Localization.RESXs" Version="1.0.0-***********-222940" GeneratePathProperty="true" />
    <PackageReference Update="BingAds.CampaignStatistics" Version="1.2.0" />
    <PackageReference Update="BingAds.DSP.Logging" Version="1.0.30" />
    <PackageReference Update="BingAds.Microsoft.com.Utility" Version="2019.2.17" />
    <PackageReference Update="BIReportingResources.Localization.DLLs" Version="1.2024.3131403" />
    <PackageReference Update="Bond.Core.CSharp" Version="13.0.0" />
    <PackageReference Update="Bond.Core.NET" Version="3.6.0-AdsApps" />
    <PackageReference Update="Bond.CSharp" Version="13.0.0" />
    <PackageReference Update="Bond.Runtime.CSharp" Version="13.0.0" />
    <PackageReference Update="BouncyCastle.Cryptography" Version="2.3.1" />
    <PackageReference Update="Castle.Core" Version="5.1.1" />
    <PackageReference Update="Certes" Version="3.0.3" />
    <PackageReference Update="CITools.AdsApps" Version="1.0.14" />
    <PackageReference Update="CITools.CloudTest.AdsApps" Version="1.2.55" GeneratePathProperty="true" />
    <PackageReference Update="ClickHouse.Ado" Version="1.5.5"/>
    <PackageReference Update="ClickHouse.Client" Version="6.8.1"/>
    <PackageReference Update="ClientCenter.Billing.MT.Proxy.Library" Version="2019.9.25" />
    <PackageReference Update="ClientCenter.MT.Proxy.Library" Version="1.0.259" />
    <PackageReference Update="ClientCenter.UserSecurity.Library" Version="1.0.19" />
    <PackageReference Update="ClientCenter.UserSecurityClient.Library" Version="1.0.19" />
    <PackageReference Update="CommandLineParser" Version="2.9.1" />
    <PackageReference Update="CommonServiceLocator.NetCore" Version="1.3.1" />
    <PackageReference Update="Confluent.Kafka" Version="2.1.1" />
    <PackageReference Update="CoreWCF.Http" Version="1.3.2" />
    <PackageReference Update="CoreWCF.Primitives" Version="1.3.2" />
    <PackageReference Update="coverlet.collector" Version="1.3.0" />
    <PackageReference Update="CRoaring.NetStandard" Version="0.2.272" />
    <PackageReference Update="CsvHelper" Version="12.1.2" />
    <PackageReference Update="Dapper" Version="2.1.37" />
    <PackageReference Update="DBInstall.AdsApps" Version="1.0.0" />
    <PackageReference Update="Deedle" Version="2.0.4" />
    <PackageReference Update="Dnet.ClickHouse.Client" Version="1.1.0" />
    <PackageReference Update="DnsClient" Version="1.6.0" />
    <PackageReference Update="DocumentFormat.OpenXml" Version="2.10.1" />
    <PackageReference Update="dotnet-sdk.installer" Version="2.1.401" />
    <PackageReference Update="dotnet-stop-words" Version="1.1.0" />
    <PackageReference Update="DynamicMasterViewPackage" Version="1.0.1" />
    <PackageReference Update="EnterpriseLibrary.TransientFaultHandling.Data.NetCore" Version="6.0.1312" />
    <PackageReference Update="EnterpriseLibrary.TransientFaultHandling.NetCore" Version="6.0.1312"/>
    <PackageReference Update="EntityFramework" Version="6.5.1" />
    <PackageReference Update="EPPlus" Version="4.5.3.1-AdsApps2" />
    <PackageReference Update="FluentFTP" Version="27.1.2" />
    <PackageReference Update="FSharp.Core" Version="4.7.0" />
    <PackageReference Update="FSharpx.AdsApps" Version="2.2.1" />
    <PackageReference Update="GeoCoordinate.NetCore" Version="1.0.0.1" />
    <PackageReference Update="Google.Ads.Common" Version="9.5.3" />
    <PackageReference Update="Google.Ads.GoogleAds" Version="23.0.0" />
    <PackageReference Update="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Update="Google.Apis.Core" Version="1.69.0" />
    <PackageReference Update="Google.Apis.MyBusinessAccountManagement.v1" Version="1.69.0.3730" />
    <PackageReference Update="Google.Apis.MyBusinessBusinessInformation.v1" Version="1.69.0.3730" />
    <PackageReference Update="Google.Apis.Oauth2.v2" Version="1.68.0.1869" />
    <PackageReference Update="Google.Apis.TagManager.v2" Version="1.69.0.3721" />
    <PackageReference Update="Google.Apis" Version="1.69.0" />
    <PackageReference Update="Grpc.AspNetCore" Version="2.42.0" />
    <PackageReference Update="Grpc.Core" Version="2.46.6" />
    <PackageReference Update="Grpc.Net.Client" Version="2.42.0" />
    <PackageReference Update="Hellang.Middleware.ProblemDetails" version="6.5.1" />
    <PackageReference Update="Hyak.Common" Version="1.2.2" />
    <PackageReference Update="Ical.Net" Version="4.2.0" />
    <PackageReference Update="ICSharpCode.SharpZipLib" Version="0.86.0.518" />
    <PackageReference Update="ImportStaticTestFiles" Version="0.1.259" GeneratePathProperty="true" />
    <PackageReference Update="IndexQuality.RetroIndexUrlNormalizer.Library" Version="1.1.0-AdsApps" />
    <PackageReference Update="Java11" Version="1.0.0" />
    <PackageReference Update="Java17" Version="1.0.0" />
    <PackageReference Update="Java8" Version="1.0.2" />
    <PackageReference Update="JetBrains.Annotations" Version="2018.2.1" />
    <PackageReference Update="JsonSubTypes" Version="1.9.0" />
    <PackageReference Update="KubernetesClient" Version="3.0.7" />
    <PackageReference Update="LibCDLV2" Version="1.0.0" />
    <PackageReference Update="LibDiagnostics.AdsApps" Version="4.0.10054-AdsApps" />
    <PackageReference Update="librdkafka.redist" Version="2.1.1" />
    <PackageReference Update="LibXmlConfigFlattener.AdsApps" Version="1.0.0-AdsApps" />
    <PackageReference Update="log4net" Version="2.0.10" />
    <PackageReference Update="lz4net" Version="1.0.15.93" />
    <PackageReference Update="Magick.NET-Q8-AnyCPU" Version="14.0.0" />
    <PackageReference Update="MagicOnion" Version="4.4.0" />
    <PackageReference Update="MathNet.Numerics" Version="4.8.1" />
    <PackageReference Update="Maven" Version="3.9.1" />
    <PackageReference Update="Mavenlib" Version="1.0.37" />
    <PackageReference Update="MediaInfo.DotNetWrapper" Version="1.0.7" />
    <PackageReference Update="MessageCenter.MessageManager.AdsApps" Version="1.0.2" />
    <PackageReference Update="MessagePack" Version="2.5.187" />
    <PackageReference Update="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" Version="2.9.1" />
    <PackageReference Update="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
    <PackageReference Update="Microsoft.ApplicationInsights" Version="2.9.1" />
    <PackageReference Update="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
    <PackageReference Update="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Update="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.11" />
    <PackageReference Update="Microsoft.AspNetCore.Hosting.Abstractions" Version="*******" />
    <PackageReference Update="Microsoft.AspNetCore.Hosting.Server.Abstractions" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.Hosting" Version="2.1.1" />
    <PackageReference Update="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.Http.Extensions" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.Http.Features" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.Http" Version="2.2.2"/>
    <PackageReference Update="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.8" />
    <PackageReference Update="Microsoft.AspNetCore.Mvc.Testing" Version="7.0.5" />
    <PackageReference Update="Microsoft.AspNetCore.Mvc.WebApiCompatShim" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Update="Microsoft.AspNetCore.OData" Version="7.5.17" />
    <PackageReference Update="Microsoft.AspNetCore.Routing" Version="2.2.2" />
    <PackageReference Update="Microsoft.AspNetCore.SignalR.Client" Version="7.0.0" />
    <PackageReference Update="Microsoft.AspNetCore.TestHost" Version="9.0.5" />
    <PackageReference Update="Microsoft.Azure.Amqp" Version="2.4.11" />
    <PackageReference Update="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.1.1" />
    <PackageReference Update="Microsoft.Azure.Cosmos.Table" Version="1.0.8" />
    <PackageReference Update="Microsoft.Azure.EventHubs.Processor" Version="4.3.1" />
    <PackageReference Update="Microsoft.Azure.EventHubs" Version="4.3.1" />
    <PackageReference Update="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.2.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="1.2.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.1.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.1.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker.Sdk" Version="1.17.0" />
    <PackageReference Update="Microsoft.Azure.Functions.Worker" Version="1.21.0" />
    <PackageReference Update="Microsoft.Azure.KeyVault.Core" Version="3.0.1" />
    <PackageReference Update="Microsoft.Azure.KeyVault.WebKey" Version="3.0.3" />
    <PackageReference Update="Microsoft.Azure.KeyVault" Version="3.0.3" />
    <PackageReference Update="Microsoft.Azure.Kusto.Data" Version="13.0.2.0" />
    <PackageReference Update="Microsoft.Azure.Kusto.Ingest" Version="13.0.2.0" />
    <PackageReference Update="Microsoft.Azure.Management.ContainerService" Version="1.2.0" />
    <PackageReference Update="Microsoft.Azure.Management.EventHub" Version="2.7.1" />
    <PackageReference Update="Microsoft.Azure.Management.Media" Version="3.0.4"  />
    <PackageReference Update="Microsoft.Azure.ServiceBus" Version="5.1.2" />
    <PackageReference Update="Microsoft.Azure.Services.AppAuthentication" Version="1.5.0" />
    <PackageReference Update="Microsoft.Azure.StackExchangeRedis" Version="2.0.0" />
    <PackageReference Update="Microsoft.Azure.Storage.Blob" Version="11.2.3" />
    <PackageReference Update="Microsoft.Azure.WebJobs.Extensions.EventHubs" Version="4.3.0" />
    <PackageReference Update="Microsoft.AzureAd.Icm.Types.amd64" Version="2.2.1363.11" />
    <PackageReference Update="Microsoft.AzureAd.Icm.WebService.Client.amd64" Version="2.2.1363.11" />
    <PackageReference Update="Microsoft.Bcl.AsyncInterfaces" Version="9.0.3" />
    <PackageReference Update="Microsoft.Bcl.HashCode" Version="6.0.0.0" />
    <PackageReference Update="Microsoft.BI.Common" Version="4.30.4" />
    <PackageReference Update="Microsoft.BI.Cosmos" Version="3.6.5" />
    <PackageReference Update="Microsoft.Bingads.Dwc.Tools" Version="2.2.1" />
    <PackageReference Update="Microsoft.BingAds.SDK.Internal" Version="13.0.19.8" />
    <PackageReference Update="Microsoft.BingAds.SDK" Version="13.0.16" />
    <PackageReference Update="Microsoft.BingAds.Utils" Version="1.3.371" />
    <PackageReference Update="Microsoft.Bot.Builder.Azure.Blobs" Version="4.22.0" />
    <PackageReference Update="Microsoft.Bot.Builder.Dialogs" Version="4.22.0" />
    <PackageReference Update="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.22.0" />
    <PackageReference Update="Microsoft.Bot.Builder.Testing" Version="4.20.1" />
    <PackageReference Update="Microsoft.Bot.Connector" Version="4.22.0" />
    <PackageReference Update="Microsoft.Build.Utilities.Core" Version="16.10.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.Analyzers" Version="3.11.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.CSharp" Version="4.12.0" />
    <PackageReference Update="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="2.9.8" />
    <PackageReference Update="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.12.0" />
    <PackageReference Update="Microsoft.CodeAnalysis" Version="4.12.0" />
    <PackageReference Update="Microsoft.CodeQuality.Analyzers" Version="2.9.8" />
    <PackageReference Update="Microsoft.Cosmos.ScopeSDK" Version="1.202501241.1" />
    <PackageReference Update="Microsoft.Cosmos.VcClient" Version="2.0.26" />
    <PackageReference Update="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Update="Microsoft.Data.Edm" Version="5.8.4" />
    <PackageReference Update="Microsoft.Data.Services.Client" Version="5.8.4" />
    <PackageReference Update="Microsoft.Data.SqlClient" Version="3.1.5" />
    <PackageReference Update="Microsoft.Domino.AtomEvent.Abstractions" Version="0.1.8"/>
    <PackageReference Update="Microsoft.Domino.AtomEvent.Conversion" Version="0.1.7" />
    <PackageReference Update="Microsoft.Domino.AtomEvent.Dwc" Version="0.1.8" />
    <PackageReference Update="Microsoft.DotNet.PlatformAbstractions" Version="1.1.0" />
    <PackageReference Update="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="8.1.1" />
    <PackageReference Update="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.CommandLine" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration.Xml" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageReference Update="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.DependencyModel" Version="5.0.0" />
    <PackageReference Update="Microsoft.Extensions.FileProviders.Abstractions" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.FileProviders.Physical" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.FileSystemGlobbing" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Hosting" Version="5.0.0" />
    <PackageReference Update="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Http.Polly" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    <PackageReference Update="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.21.0" />
    <PackageReference Update="Microsoft.Extensions.Logging.Console" Version="5.0.0" />
    <PackageReference Update="Microsoft.Extensions.Logging.Debug" Version="5.0.0" />
    <PackageReference Update="Microsoft.Extensions.Logging.EventSource" Version="5.0.0" />
    <PackageReference Update="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Options.DataAnnotations" Version="8.0.0" />
    <PackageReference Update="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Update="Microsoft.Extensions.Primitives" Version="9.0.0" />
    <PackageReference Update="Microsoft.Identity.Client" version="4.72.1.0" />
    <PackageReference Update="Microsoft.Identity.ServiceEssentials.AspNetCore" Version="1.31.1" />
    <PackageReference Update="Microsoft.Identity.ServiceEssentials.SDK" Version="1.31.1" />
    <PackageReference Update="Microsoft.Identity.ServiceEssentials.TokenAcquisitionIdWeb" Version="1.31.1" />
    <PackageReference Update="Microsoft.Identity.Web" Version="3.8.2" />
    <PackageReference Update="Microsoft.Identity.Web.UI" Version="3.8.2" />
    <PackageReference Update="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.2.6" />
    <PackageReference Update="Microsoft.IdentityModel.JsonWebTokens" Version="8.7.0" />
    <PackageReference Update="Microsoft.IdentityModel.Logging" Version="8.7.0" />
    <PackageReference Update="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.7.0" />
    <PackageReference Update="Microsoft.IdentityModel.S2S.Configuration" Version="4.13.1" />
    <PackageReference Update="Microsoft.IdentityModel.Tokens" Version="8.7.0" />
    <PackageReference Update="Microsoft.IO.RecyclableMemoryStream" Version="*******" />
    <PackageReference Update="Microsoft.Membership.Communications.Client" Version="9.0.21202.1" />
    <PackageReference Update="Microsoft.Net.Http.Headers" Version="2.2.0" />
    <PackageReference Update="Microsoft.NET.Sdk.Functions" Version="4.2.0" />
    <PackageReference Update="Microsoft.NET.Test.Sdk" Version="16.9.1" />
    <PackageReference Update="Microsoft.NetCore.Analyzers" Version="2.9.8" />
    <PackageReference Update="Microsoft.NetFramework.Analyzers" Version="2.9.8" />
    <PackageReference Update="Microsoft.OData.Client" Version="7.12.2" />
    <PackageReference Update="Microsoft.OData.Core" Version="7.12.2" />
    <PackageReference Update="Microsoft.OData.Edm" Version="7.12.2" />
    <PackageReference Update="Microsoft.Rest.ClientRuntime.Azure.Authentication" Version="2.4.1" />
    <PackageReference Update="Microsoft.Rest.ClientRuntime.Azure" Version="3.3.19" />
    <PackageReference Update="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
    <PackageReference Update="Microsoft.Search.ObjectStore.AspCompliantClient" Version="7.128.0"  />
    <PackageReference Update="Microsoft.Search.Tools.DecrytionUtility" Version="7.7.0" />
    <PackageReference Update="Microsoft.SourceLink.AzureRepos.Git" Version="1.1.1"/>
    <PackageReference Update="Microsoft.Spatial" Version="7.12.2" />
    <PackageReference Update="Microsoft.SqlServer.TransactSql.ScriptDom" Version="161.9109.0" />
    <PackageReference Update="Microsoft.TestPlatform" Version="16.7.0" />
    <PackageReference Update="Microsoft.VisualBasic" Version="10.3.0" />
    <PackageReference Update="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.9" />
    <PackageReference Update="Microsoft.VisualStudio.Services.Client" Version="16.153.0" />
    <PackageReference Update="Microsoft.VisualStudio.Services.Release.Client" Version="16.153.0" />
    <PackageReference Update="Microsoft.VisualStudio.Threading.Analyzers" Version="17.0.15-alpha" />
    <PackageReference Update="Microsoft.VisualStudio.Threading" Version="15.8.132" />
    <PackageReference Update="Microsoft.Win32.Primitives" Version="4.3.0" />
    <PackageReference Update="Microsoft.WindowsAzure.SDK" Version="2.9.0" />
    <PackageReference Update="Mime" Version="3.5.2" />
    <PackageReference Update="Mono.Posix.NETStandard" Version="1.0.0" />
    <PackageReference Update="Mono.TextTemplating" Version="2.0.5-AdsApps" PrivateAssets="All" />
    <PackageReference Update="Moq" Version="4.11.0" />
    <PackageReference Update="MSAccessDB.AdsApps" Version="14.0.4730.1010" />
    <PackageReference Update="MSTest.TestAdapter" Version="2.2.3" />
    <PackageReference Update="MSTest.TestFramework" Version="2.2.3" />
    <PackageReference Update="mt_ci_rewrite" Version="1.0.0" />
    <PackageReference Update="murmurhash" Version="1.0.3" />
    <PackageReference Update="MySqlConnector.DependencyInjection" Version="2.3.6" />
    <PackageReference Update="MySqlConnector" Version="2.3.7" />
    <PackageReference Update="NCrontab.Signed" Version="3.3.2" />
    <PackageReference Update="NDesk.Options.Core" Version="1.2.8" />
    <PackageReference Update="NetSDK4.6.1_Tools" Version="1.0.1" />
    <PackageReference Update="Newtonsoft.Json.Schema" Version="3.0.10" />
    <PackageReference Update="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Update="Nito.AsyncEx" Version="5.1.2" />
    <PackageReference Update="NLog.Config" Version="4.7.15" />
    <PackageReference Update="NLog.Extensions.Logging" Version="5.2.2" />
    <PackageReference Update="NLog.Schema" Version="5.2.2" />
    <PackageReference Update="NLog" Version="5.1.2" />
    <PackageReference Update="NodaTime" Version="3.0.9" />
    <PackageReference Update="Npgsql.DependencyInjection" Version="8.0.5" />
    <PackageReference Update="Npgsql.Json.NET" Version="8.0.5" />
    <PackageReference Update="Npgsql" Version="8.0.5" />
    <PackageReference Update="NPOI" Version="2.7.1" />
    <PackageReference Update="NSubstitute.Analyzers.CSharp" Version="1.0.17" />
    <PackageReference Update="NSubstitute" Version="5.3.0" />
    <PackageReference Update="NUnit" Version="3.12.0" />
    <PackageReference Update="NUnit3TestAdapter" Version="3.14.0" />
    <PackageReference Update="OptimizedSerialization.AdsApps" Version="1.2.1" />
    <PackageReference Update="Parquet.Net" Version="3.7.4" />
    <PackageReference Update="Polly" Version="7.2.0" />
    <PackageReference Update="prometheus-net.AspNetCore" Version="6.0.0" />
    <PackageReference Update="prometheus-net" Version="6.0.1-private-eventcounter" />
    <PackageReference Update="PrometheusMetricServer" Version="1.3.219" />
    <PackageReference Update="ProteusColor" Version="2.1.2"/>
    <PackageReference Update="ProteusColorImageSharp" Version="2.1.2" />
    <PackageReference Update="protobuf-net.Grpc.AspNetCore" Version="1.0.152"/>
    <PackageReference Update="protobuf-net.Grpc" Version="1.0.152"/>
    <PackageReference Update="protobuf-net" Version="2.4.4" />
    <PackageReference Update="PuppeteerSharp" Version="6.2.0" />
    <PackageReference Update="python.installer" Version="3.7.5" />
    <PackageReference Update="Quartz.AspNetCore" Version="3.11.0" />
    <PackageReference Update="Quartz.Extensions.Hosting" Version="3.11.0" />
    <PackageReference Update="Quartz" Version="3.11.0" />
    <PackageReference Update="RestSharp" Version="*********" />
    <PackageReference Update="Rhino.Library.NetStandard.AdsApps" Version="*******" />
    <PackageReference Update="Shared.Logging.KustoLogProvider" Version="1.3.371" />
    <PackageReference Update="Shared.Logging.KustoLogSink" Version="1.3.371" />
    <PackageReference Update="SharpZipLib" Version="1.4.2" />
    <PackageReference Update="ShellProgressBar" Version="5.2.0" />
    <PackageReference Update="SixLabors.ImageSharp" Version="*******"/>
    <PackageReference Update="SSH.NET" Version="2024.0.0" />
    <PackageReference Update="StackExchange.Redis" Version="2.2.62" />
    <PackageReference Update="StandardSocketsHttpHandler" Version="*******"/>
    <PackageReference Update="StyleCop.Analyzers" Version="1.1.118" />
    <PackageReference Update="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageReference Update="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
    <PackageReference Update="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
    <PackageReference Update="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Update="System.AppContext" Version="4.3.0" />
    <PackageReference Update="System.Buffers" Version="4.5.1" />
    <PackageReference Update="System.Collections.Concurrent" Version="4.3.0" />
    <PackageReference Update="System.Collections.Immutable" Version="8.0.0" />
    <PackageReference Update="System.Collections" Version="4.3.0" />
    <PackageReference Update="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Update="System.ComponentModel.EventBasedAsync" Version="4.3.0" />
    <PackageReference Update="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Update="System.Console" Version="4.3.0" />
    <PackageReference Update="System.Data.DataSetExtensions" Version="4.5" />
    <PackageReference Update="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Update="System.Data.SQLite.Core" Version="1.0.119.0" />
    <PackageReference Update="System.Data.SQLite.EF6" Version="1.0.119.0" />
    <PackageReference Update="System.Data.SQLite" Version="1.0.119.0" />
    <PackageReference Update="System.Diagnostics.Debug" Version="4.3.0" />
    <PackageReference Update="System.Diagnostics.DiagnosticSource" Version="9.0.0" />
    <PackageReference Update="System.Diagnostics.Tools" Version="4.3.0" />
    <PackageReference Update="System.Diagnostics.Tracing" Version="4.3.0" />
    <PackageReference Update="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Update="System.Dynamic.Runtime" Version="4.3.0" />
    <PackageReference Update="System.Formats.Asn1" Version="8.0.1" />
    <PackageReference Update="System.Globalization.Calendars" Version="4.3.0" />
    <PackageReference Update="System.Globalization" Version="4.3.0" />
    <PackageReference Update="System.IdentityModel.Tokens.Jwt" Version="8.7.0" />
    <PackageReference Update="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Update="System.IO.Compression" Version="4.3.0" />
    <PackageReference Update="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Update="System.IO.FileSystem" Version="4.3.0" />
    <PackageReference Update="System.IO.Packaging" Version="4.7.0" />
    <PackageReference Update="System.IO" Version="4.3.0" />
    <PackageReference Update="System.Linq.Async" Version="5.1.0" />
    <PackageReference Update="System.Linq.Expressions" Version="4.3.0" />
    <PackageReference Update="System.Linq.Queryable" Version="4.3.0" />
    <PackageReference Update="System.Linq" Version="4.3.0" />
    <PackageReference Update="System.Memory" Version="4.5.5" />
    <PackageReference Update="System.Net.http.WinHttpHandler" Version="4.5.4" />
    <PackageReference Update="System.Net.Http" Version="4.3.4" />
    <PackageReference Update="System.Net.Primitives" Version="4.3.0" />
    <PackageReference Update="System.Net.Requests" Version="4.3.0" />
    <PackageReference Update="System.Net.Sockets" Version="4.3.0" />
    <PackageReference Update="System.Numerics.Vectors" Version="4.5.0" />
    <PackageReference Update="System.ObjectModel" Version="4.3.0" />
    <PackageReference Update="System.Private.ServiceModel" Version="4.10.2" />
    <PackageReference Update="System.Reactive" Version="4.1.2" />
    <PackageReference Update="System.Reflection.Extensions" Version="4.3.0" />
    <PackageReference Update="System.Reflection.Primitives" Version="4.3.0" />
    <PackageReference Update="System.Reflection" Version="4.3.0" />
    <PackageReference Update="System.Resources.ResourceManager" Version="4.3.0" />
    <PackageReference Update="System.Runtime.5.0.0.0.Fakes" Version="1.0.1" />
    <PackageReference Update="System.Runtime.Caching" Version="7.0.0" />
    <PackageReference Update="System.Runtime.CompilerServices.Unsafe" Version="6.0.0"/>
    <PackageReference Update="System.Runtime.Extensions" Version="4.3.0" />
    <PackageReference Update="System.Runtime.Handles" Version="4.3.0" />
    <PackageReference Update="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
    <PackageReference Update="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Update="System.Runtime.Numerics" Version="4.3.0" />
    <PackageReference Update="System.Runtime" Version="4.3.1" />
    <PackageReference Update="System.Security.AccessControl" Version="6.0.0" />
    <PackageReference Update="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Update="System.Security.Cryptography.Encoding" Version="4.3.0" />
    <PackageReference Update="System.Security.Cryptography.Pkcs" Version="8.0.0" />
    <PackageReference Update="System.Security.Cryptography.Primitives" Version="4.3.0" />
    <PackageReference Update="System.Security.Cryptography.ProtectedData" Version="7.0.1" />
    <PackageReference Update="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Update="System.Security.Cryptography.Xml" Version="8.0.0" />
    <PackageReference Update="System.Security.Permissions" Version="7.0.0" />
    <PackageReference Update="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageReference Update="System.ServiceModel.Duplex" Version="4.10.2" />
    <PackageReference Update="System.ServiceModel.Http" Version="4.10.2" />
    <PackageReference Update="System.ServiceModel.NetTcp" Version="4.10.2" />
    <PackageReference Update="System.ServiceModel.Primitives" Version="4.10.2" />
    <PackageReference Update="System.ServiceModel.Security" Version="4.10.2" />
    <PackageReference Update="System.ServiceModel.Web" Version="1.0.0" />
    <PackageReference Update="System.Spatial" Version="5.8.4" />
    <PackageReference Update="System.Text.Encoding.Extensions" Version="4.3.0" />
    <PackageReference Update="System.Text.Encoding" Version="4.3.0" />
    <PackageReference Update="System.Text.Encodings.Web" Version="8.0.0" />
    <PackageReference Update="System.Text.Json" Version="8.0.5" />
    <PackageReference Update="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Update="System.Threading.Channels" Version="4.5.0" />
    <PackageReference Update="System.Threading.Tasks.Dataflow" Version="4.8.0" />
    <PackageReference Update="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageReference Update="System.Threading.Tasks" Version="4.3.0" />
    <PackageReference Update="System.Threading.Timer" Version="4.3.0" />
    <PackageReference Update="System.Threading" Version="4.3.0" />
    <PackageReference Update="System.ValueTuple" Version="4.5.0" />
    <PackageReference Update="System.Xml.ReaderWriter" Version="4.3.0" />
    <PackageReference Update="System.Xml.XDocument" Version="4.3.0" />
    <PackageReference Update="System.Xml.XPath.XmlDocument" Version="4.3.0" />
    <PackageReference Update="TagPluginResources" Version="1.0.4" />
    <PackageReference Update="TestPayload" Version="13.0.25" GeneratePathProperty="true"/>
    <PackageReference Update="Text.Analyzers" Version="2.6.2" />
    <PackageReference Update="TimeZoneConverter" Version="3.2.0" />
    <PackageReference Update="Unity.Configuration" Version="5.11.2" />
    <PackageReference Update="Unity.Container" Version="5.11.11" />
    <PackageReference Update="Unity.Microsoft.DependencyInjection" Version="5.11.5" />
    <PackageReference Update="Unity.Microsoft.Logging" Version="5.11.1" />
    <PackageReference Update="VideoParser" Version="1.0.7"  />
    <PackageReference Update="WindowsAzure.Storage" Version="9.3.3" />
    <PackageReference Update="WooCommerceResources" Version="1.0.2" />
    <PackageReference Update="XamarinMac.SharedSourceExtrator" Version="1.0.0.1" GeneratePathProperty="true" />
    <PackageReference Update="XMLConfigFlattenerCore" Version="1.0.4" />
    <PackageReference Update="xunit.runner.visualstudio" Version="2.5.0" />
    <PackageReference Update="xunit" Version="2.5.0" />
    <PackageReference Update="YamlDotNet" Version="11.2.1" />
  </ItemGroup>
  <ItemGroup Label="Packages to add to all projects">
    <!--
      To reference a package in all projects, add it to this list.
    -->
    <GlobalPackageReference Include="Microsoft.VisualStudio.SlnGen" Version="12.0.13" Condition=" '$(EnableSlnGen)' != 'false' And '$(MSBuildProjectExtension)' != '.nuproj' " />
    <GlobalPackageReference Include="Microsoft.Build.Telemetry" Version="[$(TelemetryLoggerInstallId)]" />
    <GlobalPackageReference Include="QuickBuild.ProjectCachePlugin" Version="$(QuickBuildCachePluginVersion)" />
  </ItemGroup>
</Project>