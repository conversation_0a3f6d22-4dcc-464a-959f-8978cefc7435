﻿//--------------------------------------------------------------------------
// <copyright file="CommonEnums.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// <summary>Enumeration types</summary>
//--------------------------------------------------------------------------

namespace Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13
{
    using System;
    using System.Runtime.Serialization;
    using Microsoft.AdCenter.Shared.Api.V13;
    using Microsoft.AdCenter.Shared.Api.V13.Constants;
    using Microsoft.AdCenter.Shared.Api.V13.WsdlManager;

    /// <summary>
    /// Status of the campaign.
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "CampaignStatus")]
    public enum CampaignStatus
    {
        /// <summary>
        /// Campaign is active (old name: Submitted)
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// The user has paused the campaign (old name: UserPaused)
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// The campaign has been paused automatically because the budget ran out or because the daily usage amount was overhauled
        /// </summary>
        [EnumMember]
        BudgetPaused,

        /// <summary>
        /// This is the combination of Paused and BudgetPaused. If a given camapign is auto-paused because no budget is available, and then, the user places a manual pause on this campaign, it will take into this state.
        /// </summary>
        [EnumMember]
        BudgetAndManualPaused,

        /// <summary>
        /// The campaign has been deleted
        /// </summary>
        [EnumMember]
        Deleted,

        /// <summary>
        /// The campaign has been suspended due to fraud
        /// </summary>
        [EnumMember]
        Suspended
    }

    /// <summary>
    /// Type of the campaign.
    /// </summary>
    [Flags]
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "CampaignType")]
    public enum CampaignType
    {
        /// <summary>
        /// Search and content (non-customized) campaign.
        /// </summary>
        [EnumMember]
        Search = 1 << 0,

        /// <summary>
        /// Shopping campaign
        /// </summary>
        [EnumMember]
        Shopping = 1 << 1,

        /// <summary>
        /// DynamicSearchAds campaign
        /// </summary>
        [EnumMember]
        DynamicSearchAds = Shopping << 1,

        /// <summary>
        /// Audience campaign
        /// </summary>
        [EnumMember]
        Audience = DynamicSearchAds << 1,

        /// <summary>
        /// Hotel campaign
        /// </summary>
        [EnumMember]
        Hotel = Audience << 2, // Shifting 2 bits because Smart campaign was never added here

        /// <summary>
        /// Performance Max campaign
        /// </summary>
        [EnumMember]
        PerformanceMax = Hotel << 1,

        /// <summary>
        /// App campaign
        /// </summary>
        [EnumMember]
        App = PerformanceMax << 1
    }

    /// <summary>
    /// Budget limit type
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "BudgetLimitType")]
    public enum BudgetLimitType
    {
        /// <summary>
        /// Daily Accelerated budget
        /// </summary>
        [EnumMember]
        DailyBudgetAccelerated,

        /// <summary>
        /// Daily Standard budget.
        /// </summary>
        [EnumMember]
        DailyBudgetStandard,

        /// <summary>
        /// Lifetime standard budget.
        /// </summary>
        [EnumMember]
        LifetimeBudgetStandard

    }


    /// <summary>
    /// The possible statuses of ad group entity
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
                 Name = "AdGroupStatus")]
    public enum AdGroupStatus
    {
        /// <summary>
        /// Active - previosly known as Submitted
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// Expired
        /// </summary>
        [EnumMember]
        Expired,

        /// <summary>
        ///  Deleted
        /// </summary>
        [EnumMember]
        Deleted
    }

    
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
        Name = "GenericEntityStatus")]
    public enum GenericEntityStatus
    {
        [EnumMember]
        Active,
        
        [EnumMember]
        Paused,
        
        [EnumMember]
        Disapproved,
        
        [EnumMember]
        Inactive,
        
        [EnumMember]
        Deleted
    }
    /// <summary>
    /// The possible statuses of asset group entity
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
                 Name = "AssetGroupStatus")]
    public enum AssetGroupStatus
    {
        /// <summary>
        /// Active - previosly known as Submitted
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        ///  Deleted
        /// </summary>
        [EnumMember]
        Deleted,

        /// <summary>
        ///  Expired
        /// </summary>
        [EnumMember]
        Expired
    }

    /// <summary>
    /// The day of the week
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
                 Name = "Day")]
    public enum Day
    {
        /// <summary>
        /// Sunday
        /// </summary>
        [EnumMember]
        Sunday,

        /// <summary>
        /// Monday
        /// </summary>
        [EnumMember]
        Monday,

        /// <summary>
        /// Tuesday
        /// </summary>
        [EnumMember]
        Tuesday,

        /// <summary>
        /// Wednesday
        /// </summary>
        [EnumMember]
        Wednesday,

        /// <summary>
        /// Thursday
        /// </summary>
        [EnumMember]
        Thursday,

        /// <summary>
        /// Friday
        /// </summary>
        [EnumMember]
        Friday,

        /// <summary>
        /// Saturday
        /// </summary>
        [EnumMember]
        Saturday
    }

    /// <summary>
    /// Incremental Bid Percentage
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
                Name = "IncrementalBidPercentage")]
    public enum IncrementalBidPercentage
    {
        [EnumMember]
        ZeroPercent,

        [EnumMember]
        TenPercent,

        [EnumMember]
        TwentyPercent,

        [EnumMember]
        ThirtyPercent,

        [EnumMember]
        FortyPercent,

        [EnumMember]
        FiftyPercent,

        [EnumMember]
        SixtyPercent,

        [EnumMember]
        SeventyPercent,

        [EnumMember]
        EightyPercent,

        [EnumMember]
        NinetyPercent,

        [EnumMember]
        OneHundredPercent,

        [EnumMember]
        NegativeTenPercent,

        [EnumMember]
        NegativeTwentyPercent,

        [EnumMember]
        NegativeThirtyPercent,

        [EnumMember]
        NegativeFortyPercent,

        [EnumMember]
        NegativeFiftyPercent,

        [EnumMember]
        NegativeSixtyPercent,

        [EnumMember]
        NegativeSeventyPercent,

        [EnumMember]
        NegativeEightyPercent,

        [EnumMember]
        NegativeNinetyPercent,

        [EnumMember]
        NegativeOneHundredPercent
    }

    /// <summary>
    /// Hour ranges
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
               Name = "HourRange")]
    public enum HourRange
    {
        /// <summary>
        /// 3-7 AM (was early morning)
        /// </summary>
        [EnumMember]
        ThreeAMToSevenAM,

        /// <summary>
        /// 7 - 11 AM (was morning)
        /// </summary>
        [EnumMember]
        SevenAMToElevenAM,

        /// <summary>
        /// 11 AM - 2 PM (was mid-day)
        /// </summary>
        [EnumMember]
        ElevenAMToTwoPM,

        /// <summary>
        /// 2-6 PM (was afternoon)
        /// </summary>
        [EnumMember]
        TwoPMToSixPM,

        /// <summary>
        /// 6 - 11 PM (was evening / at home)
        /// </summary>
        [EnumMember]
        SixPMToElevenPM,

        /// <summary>
        /// 11 PM - 3 AM (was late night)
        /// </summary>
        [EnumMember]
        ElevenPMToThreeAM
    }

    /// <summary>
    /// Gender
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
              Name = "GenderType")]
    public enum GenderType
    {
        [EnumMember]
        Unknown,

        [EnumMember]
        Male,

        [EnumMember]
        Female
    }

    /// <summary>
    /// Age range
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
              Name = "AgeRange")]
    public enum AgeRange
    {
        [EnumMember]
        Unknown,

        /// <summary>
        /// 18 - 24
        /// </summary>
        [EnumMember]
        EighteenToTwentyFour,

        /// <summary>
        /// 25 - 34
        /// </summary>
        [EnumMember]
        TwentyFiveToThirtyFour,

        /// <summary>
        /// 35 - 49
        /// </summary>
        [EnumMember]
        ThirtyFiveToFourtyNine,

        /// <summary>
        /// 50 - 64
        /// </summary>
        [EnumMember]
        FiftyToSixtyFour,

        /// <summary>
        /// 65 -
        /// </summary>
        [EnumMember]
        SixtyFiveAndAbove,

        /// <summary>
        /// 35 - 54
        /// </summary>
        [EnumMember]
        ThirtyFiveToFiftyFour,

        /// <summary>
        /// 55 -
        /// </summary>
        [EnumMember]
        FiftyFiveAndAbove,
    }

    /// <summary>
    /// Type of Ad
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdType
    {
        /// <summary>
        /// Text Ad
        /// </summary>
        [EnumMember]
        Text,

        /// <summary>
        /// Image Ad
        /// </summary>
        [EnumMember]
        Image,

        /// <summary>
        /// Product Ad
        /// </summary>
        [EnumMember]
        Product,

        /// <summary>
        /// AppInstall Ad
        /// </summary>
        [EnumMember]
        AppInstall,

        /// <summary>
        /// Expanded Text Ad
        /// </summary>
        [EnumMember]
        ExpandedText,

        /// <summary>
        /// Dynamic Search Ad
        /// </summary>
        [EnumMember]
        DynamicSearch,

        [EnumMember]
        ResponsiveAd,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ResponsiveSearchAd)]
        ResponsiveSearch,

        [EnumMember]
        Hotel,
    }

    /// <summary>
    /// Type of status of keyword
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
             Name = "KeywordStatus")]
    public enum KeywordStatus
    {
        /// <summary>
        /// Active (renamed from Submitted)
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// Deleted
        /// </summary>
        [EnumMember]
        Deleted,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive
    }

    /// <summary>
    /// Type of status of Ad
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
             Name = "AdStatus")]
    public enum AdStatus
    {
        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// Deleted
        /// </summary>
        [EnumMember]
        Deleted
    }

    /// <summary>
    /// Editirial Status  Ad
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
            Name = "AdEditorialStatus")]
    public enum AdEditorialStatus
    {
        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    /// <summary>
    /// Editirial Status AssetLink
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
        Name = "AssetLinkEditorialStatus")]
    [HideMeFromWsdl(WsdlConfiguration.Feature.ResponsiveSearchAd)]
    public enum AssetLinkEditorialStatus
    {
        /// <summary>
        /// Unknown
        /// </summary>
        [EnumMember]
        Unknown,

        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    /// <summary>
    /// Editirial Status for Keyword
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
            Name = "KeywordEditorialStatus")]
    public enum KeywordEditorialStatus
    {
        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    /// <summary>
    /// Type of association status between ad and keyword
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
             Name = "KeywordAdAssociationStatus")]
    public enum KeywordAdAssociationStatus
    {
        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "StandardBusinessIcon")]
    public enum StandardBusinessIcon
    {
        [EnumMember]
        MoviesOrVideo,

        [EnumMember]
        PubOrBarOrLiquor,

        [EnumMember]
        Accommodations,

        [EnumMember]
        RestaurantOrDining,

        [EnumMember]
        CafeOrCoffeeShop,

        [EnumMember]
        FlowersOrGarden,

        [EnumMember]
        CarDealerOrServiceOrRental,

        [EnumMember]
        GroceryOrDepartmentStore,

        [EnumMember]
        ShoppingOrBoutique,

        [EnumMember]
        HousewaresOrRealEstateOrHomeRepair,

        [EnumMember]
        PhonesOrServiceProvider,

        [EnumMember]
        BankOrFinanceOrCurrencyExchange,

        [EnumMember]
        BankOrFinanceOrCurrencyExchangeUK,

        [EnumMember]
        BankOrFinanceOrCurrencyExchangeEUR,

        [EnumMember]
        HardwareOrRepair,

        [EnumMember]
        HairdresserOrBarberOrTailor
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "BusinessStatus")]
    public enum BusinessStatus
    {
        [EnumMember]
        Active,

        [EnumMember]
        Inactive,

        [EnumMember]
        Pending
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "PaymentType")]
    public enum PaymentType
    {
        [EnumMember]
        Cash,

        [EnumMember]
        AmericanExpress,

        [EnumMember]
        MasterCard,

        [EnumMember]
        DinersClub,

        [EnumMember]
        DirectDebit,

        [EnumMember]
        Visa,

        [EnumMember]
        TravellersCheck,

        [EnumMember]
        PayPal,

        [EnumMember]
        Invoice,

        [EnumMember]
        CashOnDelivery,

        [EnumMember]
        Other
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "BusinessGeoCodeStatus")]
    public enum BusinessGeoCodeStatus
    {
        [EnumMember]
        Pending,

        [EnumMember]
        Complete,

        [EnumMember]
        Invalid,

        [EnumMember]
        Failed
    }

    /// <summary>
    /// Status of the behavior segment.
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "BehavioralBidStatus")]
    public enum BehavioralBidStatus
    {
        /// <summary>
        /// BehavioralBid is active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// Deleted
        /// </summary>
        [EnumMember]
        Deleted,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive
    }

    /// <summary>
    /// Status of the site.
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "SitePlacementStatus")]
    public enum SitePlacementStatus
    {
        /// <summary>
        /// SitePlacement is active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Paused
        /// </summary>
        [EnumMember]
        Paused,

        /// <summary>
        /// Deleted
        /// </summary>
        [EnumMember]
        Deleted,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive
    }

    /// <summary>
    /// Priority of the cash back.
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "OverridePriority")]
    public enum OverridePriority
    {
        /// <summary>
        /// KeywordEnable
        /// </summary>
        [EnumMember]
        KeywordEnable,

        /// <summary>
        /// SegmentEnable
        /// </summary>
        [EnumMember]
        SegmentEnable,

        /// <summary>
        /// KeywordPriority
        /// </summary>
        [EnumMember]
        KeywordPriority,

        /// <summary>
        /// SegmentPriority
        /// </summary>
        [EnumMember]
        SegmentPriority
    }

    /// <summary>
    /// Type of DownloadEntityFilter
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "DownloadEntityFilter")]
    [Flags]
    public enum DownloadEntityFilter
    {
        /// <summary>
        /// Exclude NegativeKeywords for campaign entity
        /// </summary>
        [EnumMember]
        CampaignNegativeKeywords,

        /// <summary>
        /// Exclude NegativeSites for campaign entity
        /// </summary>
        [EnumMember]
        CampaignNegativeSites,

        /// <summary>
        /// Exclude NegativeKeywords for AdGroup entity
        /// </summary>
        [EnumMember]
        AdGroupNegativeKeywords,

        /// <summary>
        /// Exclude NegativeSites for AdGroup entity
        /// </summary>
        [EnumMember]
        AdGroupNegativeSites
    }

    /// <summary>
    ///     Represents constants for additional entities to include in a Bulk Download request.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdditionalEntity
    {
        [EnumMember]
        CampaignNegativeKeywords = 0,

        [EnumMember]
        AdGroupNegativeKeywords = 1,

        [EnumMember]
        CampaignTargetCriterions = 2,

        [EnumMember]
        AdGroupTargetCriterions = 3,

        [EnumMember]
        CampaignNegativeSites = 4,

        [EnumMember]
        AdGroupNegativeSites = 5,

        [EnumMember]
        AdEditorialRejectionReasons = 6,

        [EnumMember]
        KeywordEditorialRejectionReasons = 7,

        [EnumMember]
        CampaignSiteLinksAdExtensions = 8,

        [EnumMember]
        CampaignProductAdExtensions = 9,

        [EnumMember]
        CampaignLocationAdExtensions = 10,

        [EnumMember]
        CampaignCallAdExtensions = 11,

        [EnumMember]
        AdGroupProductTargets = 12,

        [EnumMember]
        ProductTargetEditorialRejectionReasons = 13,

        [EnumMember]
        AdGroupSiteLinksAdExtensions = 14,

        [EnumMember]
        Campaigns = 15,

        [EnumMember]
        AdGroups = 16,

        [EnumMember]
        Ads = 17,

        [EnumMember]
        Keywords = 18,

        [EnumMember]
        LocationAdExtensions = 19,

        [EnumMember]
        CallAdExtensions = 20,

        [EnumMember]
        SiteLinksAdExtensions = 21,

        [EnumMember]
        ProductAdExtensions = 22,

        [EnumMember]
        ImageAdExtensions = 23,

        [EnumMember]
        CampaignImageAdExtensions = 24,

        [EnumMember]
        AdGroupImageAdExtensions = 25,

        [EnumMember]
        TextAds = 26,

        [EnumMember]
        ProductAds = 27,

        [EnumMember]
        AppInstallAds = 28,

        [EnumMember]
        NegativeKeywordsList = 29,

        [EnumMember]
        NegativeKeyword = 30,

        [EnumMember]
        CampaignNegativeKeywordList = 31,

        [EnumMember]
        AppAdExtensions = 32,

        [EnumMember]
        CampaignAppAdExtensions = 33,

        [EnumMember]
        AdGroupAppAdExtensions = 34,

        [EnumMember]
        PriceAdExtensions = 35,

        [EnumMember]
        CampaignPriceAdExtensions = 36,

        [EnumMember]
        AdGroupPriceAdExtensions = 37,

        [EnumMember]
        ReviewAdExtensions = 38,

        [EnumMember]
        CampaignReviewAdExtensions = 39,

        [EnumMember]
        AdGroupReviewAdExtensions = 40,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicSearchAd)]
        CampaignNegativeDynamicSearchAdTargets = 41,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicSearchAd)]
        AdGroupDynamicSearchAdTargets = 42,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicSearchAd)]
        AdGroupNegativeDynamicSearchAdTargets = 43,

        [EnumMember]
        AdGroupProductPartitions = 44,

        [EnumMember]
        CampaignProductScopes = 45,

        [EnumMember]
        CalloutAdExtensions = 46,

        [EnumMember]
        CampaignCalloutAdExtensions = 47,

        [EnumMember]
        AdGroupCalloutAdExtensions = 48,

        [EnumMember]
        RemarketingLists = 49,

        [EnumMember]
        AdGroupRemarketingListAssociations = 50,

        [EnumMember]
        Sitelink2AdExtensions = 51,

        [EnumMember]
        CampaignSitelink2AdExtensions = 52,

        [EnumMember]
        AdGroupSitelink2AdExtensions = 53,

        [EnumMember]
        StructuredSnippetAdExtensions = 1 + AdGroupSitelink2AdExtensions,

        [EnumMember]
        CampaignStructuredSnippetAdExtensions = 1 + StructuredSnippetAdExtensions,

        [EnumMember]
        AdGroupStructuredSnippetAdExtensions = 1 + CampaignStructuredSnippetAdExtensions,

        [EnumMember]
        ExpandedTextAds = 1 + AdGroupStructuredSnippetAdExtensions,

        [EnumMember]
        Budgets = 1 + ExpandedTextAds,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicSearchAd)]
        DynamicSearchAds = 1 + Budgets,

        [EnumMember]
        Labels = 1 + DynamicSearchAds,

        [EnumMember]
        CampaignLabels = 1 + Labels,

        [EnumMember]
        AdGroupLabels = 1 + CampaignLabels,

        [EnumMember]
        TextAdLabels = 1 + AdGroupLabels,

        [EnumMember]
        KeywordLabels = 1 + TextAdLabels,

        [EnumMember]
        AdGroupNegativeRemarketingListAssociations = 1 + KeywordLabels,

        [EnumMember]
        CustomAudiences = 1 + AdGroupNegativeRemarketingListAssociations,

        [EnumMember]
        AdGroupCustomAudienceAssociations = 1 + CustomAudiences,

        [EnumMember]
        AdGroupNegativeCustomAudienceAssociations = 71,

        [EnumMember]
        InMarketAudiences = 1 + AdGroupNegativeCustomAudienceAssociations,

        [EnumMember]
        AdGroupInMarketAudienceAssociations = 1 + InMarketAudiences,

        [EnumMember]
        AdGroupNegativeInMarketAudienceAssociations = 1 + AdGroupInMarketAudienceAssociations,

        [EnumMember]
        Audiences = 1 + AdGroupNegativeInMarketAudienceAssociations,

        [EnumMember]
        AdGroupAudienceAssociations = 1 + Audiences,

        [EnumMember]
        AdGroupNegativeAudienceAssociations = 1 + AdGroupAudienceAssociations,

        [EnumMember]
        ProductAdLabels = 1 + AdGroupNegativeAudienceAssociations,

        [EnumMember]
        AppInstallAdLabels = 1 + ProductAdLabels,

        [EnumMember]
        ExpandedTextAdLabels = 1 + AppInstallAdLabels,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicSearchAd)]
        DynamicSearchAdLabels = 1 + ExpandedTextAdLabels,

        [EnumMember]
        AccountLocationAdExtensions = 1 + DynamicSearchAdLabels,

        [EnumMember]
        AccountImageAdExtensions = 1 + AccountLocationAdExtensions,

        [EnumMember]
        AccountAppAdExtensions = 1 + AccountImageAdExtensions,

        [EnumMember]
        AccountPriceAdExtensions = 1 + AccountAppAdExtensions,

        [EnumMember]
        AccountReviewAdExtensions = 1 + AccountPriceAdExtensions,

        [EnumMember]
        AccountCalloutAdExtensions = 1 + AccountReviewAdExtensions,

        [EnumMember]
        AccountSitelink2AdExtensions = 1 + AccountCalloutAdExtensions,

        [EnumMember]
        AccountStructuredSnippetAdExtensions = 1 + AccountSitelink2AdExtensions,

        [EnumMember]
        ResponsiveAds = 1 + AccountStructuredSnippetAdExtensions,

        [EnumMember]
        ResponsiveAdLabels = 1 + ResponsiveAds,

        [EnumMember]
        ProductAudiences = 1 + ResponsiveAdLabels,

        [EnumMember]
        AdGroupProductAudienceAssociations = 1 + ProductAudiences,

        [EnumMember]
        AdGroupNegativeProductAudienceAssociations = 1 + AdGroupProductAudienceAssociations,

        [EnumMember]
        SimilarRemarketingLists = 1 + AdGroupNegativeProductAudienceAssociations,

        [EnumMember]
        AdGroupSimilarRemarketingListAssociations = 1 + SimilarRemarketingLists,

        [EnumMember]
        AdGroupNegativeSimilarRemarketingListAssociations = 1 + AdGroupSimilarRemarketingListAssociations,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.Experiment)]
        Experiments = 1 + AdGroupNegativeSimilarRemarketingListAssociations,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ResponsiveSearchAd)]
        ResponsiveSearchAds = 1 + Experiments,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ResponsiveSearchAd)]
        ResponsiveSearchAdLabels = 1 + ResponsiveSearchAds,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        ActionAdExtensions = 1 + ResponsiveSearchAdLabels,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        CampaignActionAdExtensions = 1 + ActionAdExtensions,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        AdGroupActionAdExtensions = 1 + CampaignActionAdExtensions,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        AccountActionAdExtensions = 1 + AdGroupActionAdExtensions,

        [EnumMember]
        CampaignAudienceAssociations = AccountActionAdExtensions + 1,

        [EnumMember]
        CampaignNegativeAudienceAssociations = CampaignAudienceAssociations + 1,

        [EnumMember]
        CampaignCustomAudienceAssociations = CampaignNegativeAudienceAssociations + 1,

        [EnumMember]
        CampaignNegativeCustomAudienceAssociations = CampaignCustomAudienceAssociations + 1,

        [EnumMember]
        CampaignInMarketAudienceAssociations = CampaignNegativeCustomAudienceAssociations + 1,

        [EnumMember]
        CampaignNegativeInMarketAudienceAssociations = CampaignInMarketAudienceAssociations + 1,

        [EnumMember]
        CampaignProductAudienceAssociations = CampaignNegativeInMarketAudienceAssociations + 1,

        [EnumMember]
        CampaignNegativeProductAudienceAssociations = CampaignProductAudienceAssociations + 1,

        [EnumMember]
        CampaignRemarketingListAssociations = CampaignNegativeProductAudienceAssociations + 1,

        [EnumMember]
        CampaignNegativeRemarketingListAssociations = CampaignRemarketingListAssociations + 1,

        [EnumMember]
        CampaignSimilarRemarketingListAssociations = CampaignNegativeRemarketingListAssociations + 1,

        [EnumMember]
        CampaignNegativeSimilarRemarketingListAssociations = CampaignSimilarRemarketingListAssociations + 1,

        [EnumMember]
        Feeds = CampaignNegativeSimilarRemarketingListAssociations + 1,

        [EnumMember]
        FeedItems = Feeds + 1,

        [EnumMember]
        CampaignNegativeStoreCriterions = FeedItems + 1,

        [EnumMember]
        PromotionAdExtensions = CampaignNegativeStoreCriterions + 1,

        [EnumMember]
        AccountPromotionAdExtensions = PromotionAdExtensions + 1,

        [EnumMember]
        CampaignPromotionAdExtensions = AccountPromotionAdExtensions + 1,

        [EnumMember]
        AdGroupPromotionAdExtensions = CampaignPromotionAdExtensions + 1,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicDataExtension)]
        DynamicDataAdExtensions = AdGroupPromotionAdExtensions + 1,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DynamicDataExtension)]
        AccountDynamicDataAdExtensions = DynamicDataAdExtensions + 1,

        [EnumMember]
        CombinedLists = 1 + AccountDynamicDataAdExtensions,

        [EnumMember]
        AdGroupCombinedListAssociations = 1 + CombinedLists,

        [EnumMember]
        AdGroupNegativeCombinedListAssociations = 1 + AdGroupCombinedListAssociations,

        [EnumMember]
        CampaignCombinedListAssociations = 1 + AdGroupNegativeCombinedListAssociations,

        [EnumMember]
        CampaignNegativeCombinedListAssociations = 1 + CampaignCombinedListAssociations,

        [EnumMember]
        Images = 1 + CampaignNegativeCombinedListAssociations,

        [EnumMember]
        CustomerLists = 1 + Images,

        [EnumMember]
        AdGroupCustomerListAssociations = 1 + CustomerLists,

        [EnumMember]
        AdGroupNegativeCustomerListAssociations = 1 + AdGroupCustomerListAssociations,

        [EnumMember]
        CampaignCustomerListAssociations = 1 + AdGroupNegativeCustomerListAssociations,

        [EnumMember]
        CampaignNegativeCustomerListAssociations = 1 + CampaignCustomerListAssociations,

        [EnumMember]
        FilterLinkAdExtensions = CampaignNegativeCustomerListAssociations + 1,

        [EnumMember]
        AccountFilterLinkAdExtensions = FilterLinkAdExtensions + 1,

        [EnumMember]
        CampaignFilterLinkAdExtensions = AccountFilterLinkAdExtensions + 1,

        [EnumMember]
        AdGroupFilterLinkAdExtensions = CampaignFilterLinkAdExtensions + 1,

        [EnumMember]
        FlyerAdExtensions = AdGroupFilterLinkAdExtensions + 1,

        [EnumMember]
        AccountFlyerAdExtensions = FlyerAdExtensions + 1,

        [EnumMember]
        CampaignFlyerAdExtensions = AccountFlyerAdExtensions + 1,

        [EnumMember]
        AdGroupFlyerAdExtensions = CampaignFlyerAdExtensions + 1,

        [EnumMember]
        VideoAdExtensions = AdGroupFlyerAdExtensions + 1,

        [EnumMember]
        AccountVideoAdExtensions = VideoAdExtensions + 1,

        [EnumMember]
        CampaignVideoAdExtensions = AccountVideoAdExtensions + 1,

        [EnumMember]
        AdGroupVideoAdExtensions = CampaignVideoAdExtensions + 1,

        [EnumMember]
        BidStrategies = AdGroupVideoAdExtensions + 1,

        [EnumMember]
        Videos = 1 + BidStrategies,

        [EnumMember]
        DisclaimerAdExtensions = Videos + 1,

        [EnumMember]
        CampaignDisclaimerAdExtensions = DisclaimerAdExtensions + 1,

        [EnumMember]
        AdcustomizerAttribute = 1 + CampaignDisclaimerAdExtensions,

        [EnumMember]
        CampaignAdcustomizerAttribute = 1 + AdcustomizerAttribute,

        [EnumMember]
        AdGroupAdcustomizerAttribute = 1 + CampaignAdcustomizerAttribute,

        [EnumMember]
        KeywordAdcustomizerAttribute = 1 + AdGroupAdcustomizerAttribute,

        [EnumMember]
        CampaignConversionGoal = 1 + KeywordAdcustomizerAttribute,

        [EnumMember]
        AdGroupHotelListingGroups = 1 + CampaignConversionGoal,

        [EnumMember]
        AssetGroups = 1 + AdGroupHotelListingGroups,

        [EnumMember]
        AudienceGroups = 1 + AssetGroups,

        [EnumMember]
        CampaignNegativeWebpages = 1 + AudienceGroups,

        [EnumMember]
        AudienceGroupAssetGroupAssociations = 1 + CampaignNegativeWebpages,

        [EnumMember]
        AssetGroupListingGroups = 1 + AudienceGroupAssetGroupAssociations,

        [EnumMember]
        CampaignAutomatedCallToActionOptOut = 1 + AssetGroupListingGroups,

        [EnumMember]
        CampaignDisclaimerSetting = 1 + CampaignAutomatedCallToActionOptOut,

        [EnumMember]
        AccountLogoAdExtensions = 1 + CampaignDisclaimerSetting,

        [EnumMember]
        CampaignLogoAdExtensions = 1 + AccountLogoAdExtensions,

        [EnumMember]
        AdGroupLogoAdExtensions = 1 + CampaignLogoAdExtensions,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
        LeadFormAdExtensions = 1 + AdGroupLogoAdExtensions,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
        AccountLeadFormAdExtensions = 1 + LeadFormAdExtensions,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
        CampaignLeadFormAdExtensions = 1 + AccountLeadFormAdExtensions,

        [EnumMember]
        AccountNegativeKeywordList = 1 + CampaignLeadFormAdExtensions,

        [EnumMember]
        AccountNegativeKeywordListAssociation = 1 + AccountNegativeKeywordList,

        [EnumMember]
        AccountNegativeKeyword = 1 + AccountNegativeKeywordListAssociation,

        [EnumMember]
        ConversionGoal = 1 + AccountNegativeKeyword,

        [EnumMember]
        EventGoal = 1 + ConversionGoal,

        [EnumMember]
        AppInstallGoal = 1 + EventGoal,

        [EnumMember]
        MultiStageGoal = 1 + AppInstallGoal,

        [EnumMember]
        DurationGoal = 1 + MultiStageGoal,

        [EnumMember]
        OfflineConversionGoal = 1 + DurationGoal,

        [EnumMember]
        UrlGoal = 1 + OfflineConversionGoal,

        [EnumMember]
        InStoreTransactionGoal = 1 + UrlGoal,

        [EnumMember]
        PagesViewedPerVisitGoal = 1 + InStoreTransactionGoal,

        [EnumMember]
        SmartGoal = 1 + PagesViewedPerVisitGoal,

        [EnumMember]
        InStoreVisitGoal = 1 + SmartGoal,

        [EnumMember]
        ProductGoal = 1 + InStoreVisitGoal,

        [EnumMember]
        BrandItem = 1 + ProductGoal,

        [EnumMember]
        CampaignBrandListAssociation = 1 + BrandItem,

        [EnumMember]
        SeasonalityAdjustments = 1 + CampaignBrandListAssociation,

        [EnumMember]
        DataExclusions = 1 + SeasonalityAdjustments,

        //Due to merge conflict collision, moved away from original spot to allocate open enum number
        [EnumMember]
        LogoAdExtensions = 1 + DataExclusions,

        [EnumMember]
        BrandList = 1 + LogoAdExtensions,
        
        [EnumMember]
        AssetGroupSearchThemes = 1 + BrandList,

        [EnumMember]
        ImpressionBasedRemarketingList = 1 + AssetGroupSearchThemes,

        [EnumMember]
        AdGroupImpressionBasedRemarketingListAssociations = 1 + ImpressionBasedRemarketingList,

        [EnumMember]
        AdGroupNegativeImpressionBasedRemarketingListAssociations = 1 + AdGroupImpressionBasedRemarketingListAssociations,

        [EnumMember]
        CampaignImpressionBasedRemarketingListAssociations = 1 + AdGroupNegativeImpressionBasedRemarketingListAssociations,

        [EnumMember]
        CampaignNegativeImpressionBasedRemarketingListAssociations = 1 + CampaignImpressionBasedRemarketingListAssociations,
        
        [EnumMember]
        AssetGroupUrlTargets = 1 + CampaignNegativeImpressionBasedRemarketingListAssociations,

        [EnumMember]
        NewCustomerAcquisitionGoal = 1 + AssetGroupUrlTargets,

        [EnumMember]
        ConversionValueRule = 1 + NewCustomerAcquisitionGoal,

        [EnumMember]
        AccountPlacementExclusionList = 1 + ConversionValueRule,

        [EnumMember]
        AccountPlacementExclusionListItem = 1 + AccountPlacementExclusionList,

        [EnumMember]
        CampaignAccountPlacementExclusionListAssociation = 1 + AccountPlacementExclusionListItem,

        [EnumMember]
        AccountPlacementInclusionList = 1 + CampaignAccountPlacementExclusionListAssociation,

        [EnumMember]
        AccountPlacementInclusionListItem = 1 + AccountPlacementInclusionList,

        [EnumMember]
        CampaignAccountPlacementInclusionListAssociation = 1 + AccountPlacementInclusionListItem,

        [EnumMember]
        Topic = 1 + CampaignAccountPlacementInclusionListAssociation,

        [EnumMember]
        ContentPlacement = 1 + Topic,

        [EnumMember]
        BrandKit = 1 + ContentPlacement,
    }

    /// <summary>
    ///     Represents constants for the type of the download file (Xml/Csv) etc in a bulk download request.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum DownloadFileType
    {
        /// <summary>
        ///     Zipped Csv
        /// </summary>
        [EnumMember]
        Csv,

        /// <summary>
        ///     Zipped Tsv
        /// </summary>
        [EnumMember]
        Tsv
    }

    /// <summary>
    ///     Represents constants for the compression type of the download file (Zip/GZip/None) etc in a bulk download request.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CompressionType
    {
        /// <summary>
        ///     Zip compressed.
        /// </summary>
        [EnumMember]
        Zip,

        /// <summary>
        ///     GZip compressed.
        /// </summary>
        [EnumMember]
        GZip
    }

    /// <summary>
    /// Networks used to show ads
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "Network")]
    public enum Network
    {
        [EnumMember]
        OwnedAndOperatedAndSyndicatedSearch,

        [EnumMember]
        OwnedAndOperatedOnly,

        [EnumMember]
        SyndicatedSearchOnly,

        [EnumMember]
        InHousePromotion
    }

    /// <summary>
    ///     Defines constants for different states of an account's migration.
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "MigrationStatus")]
    public enum MigrationStatus
    {
        /// <summary>
        ///     The migration is not applicable to this account, as the customer is not in pilot for the feature.
        /// </summary>
        [EnumMember]
        NotInPilot,

        /// <summary>
        ///     The migration has not started.
        /// </summary>
        [EnumMember]
        NotStarted,

        /// <summary>
        ///     The migration is in progress.
        /// </summary>
        [EnumMember]
        InProgress,

        /// <summary>
        ///     The migration has completed.
        /// </summary>
        [EnumMember]
        Completed
    }

    /// <summary>
    ///     Defines constants for different states of a campaign's ad extension.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdExtensionStatus
    {
        /// <summary>
        ///     The extension is active.
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        ///     The extension has been deleted.
        /// </summary>
        [EnumMember]
        Deleted
    }

    /// <summary>
    ///     Defines constants for different editorial statuses of a campaign's ad extension.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CampaignAdExtensionEditorialStatus
    {
        /// <summary>
        ///     Approved
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        ///     Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        ///     Under review.
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    /// <summary>
    ///     Defines constants for filtering ad extensions by their types in a "GetAdExtension*" call.
    /// </summary>
    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdExtensionsTypeFilter
    {
        // Do not expose publicly, since we do not allow this to be passed in.
        None = 0,

        [EnumMember]
        LocationAdExtension = 1 << 0,

        [EnumMember]
        CallAdExtension = 1 << 1,

        [EnumMember]
        ImageAdExtension = 1 << 2,

        [EnumMember]
        AppAdExtension = 1 << 3,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.NewsAdExtension)]
        NewsAdExtension = 1 << 4,

        [EnumMember]
        ReviewAdExtension = 1 << 5,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.DataTableAdExtension)]
        DataTableAdExtension = 1 << 6,

        [EnumMember]
        CalloutAdExtension = 1 << 7,

        [EnumMember]
        SitelinkAdExtension = 1 << 8,

        [EnumMember]
        StructuredSnippetAdExtension = 1 << 9,

        [EnumMember]
        PriceAdExtension = 1 << 10,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        ActionAdExtension = 1 << 11,

        [EnumMember]
        PromotionAdExtension = 1 << 12,

        [EnumMember]
        FilterLinkAdExtension = 1 << 13,

        [EnumMember]
        FlyerAdExtension = 1 << 14,

        [EnumMember]
        VideoAdExtension = 1 << 15,

        [EnumMember]
        DisclaimerAdExtension = 1 << 16,

        [EnumMember]
        LogoAdExtension = 1 << 17,

        [EnumMember]
        [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
        LeadFormAdExtension = 1 << 18,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AdExtensionAdditionalField")]
    public enum AdExtensionAdditionalField
    {
        None = 0,

        [HideMeFromWsdl(WsdlConfiguration.Feature.ImageAdExtensionsV2)]
        [EnumMember]
        Images = 1 << 0,

        [HideMeFromWsdl(WsdlConfiguration.Feature.ImageAdExtensionsV2)]
        [EnumMember]
        DisplayText = 1 << 1,

        [HideMeFromWsdl(WsdlConfiguration.Feature.ImageAdExtensionsV2)]
        [EnumMember]
        Layouts = 1 << 2,

        [HideMeFromWsdl(WsdlConfiguration.Feature.ActionAdExtension_V13)]
        [EnumMember]
        ActionTypesPhase3 = 1 << 3,

        [EnumMember]
        ActionTypesPhase4 = 1 << 4,

        [EnumMember]
        NewFilterLinkHeaders = 1 << 5,

        [EnumMember]
        SourceType = 1 << 6,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AdAdditionalField")]
    public enum AdAdditionalField
    {
        None = 0,

        [EnumMember]
        ImpressionTrackingUrls = 1 << 0,

        [EnumMember]
        Videos = 1 << 1,

        [EnumMember]
        LongHeadlines = 1 << 2,

        [EnumMember]
        ImageTargetDimension = 1 << 3,

        [EnumMember]
        AdSubType = 1 << 4,
    }

    [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "CampaignAdditionalField")]
    public enum CampaignAdditionalField : Int64
    {
        None = 0,

        [EnumMember]
        AdScheduleUseSearcherTimeZone = 1L << 0,

        [EnumMember]
        MaxConversionValueBiddingScheme = 1L << 1,

        [EnumMember]
        TargetImpressionShareBiddingScheme = 1L << 2,

        [EnumMember]
        TargetSetting = 1L << 3,

        [EnumMember]
        BidStrategyId = 1L << 4,

        [EnumMember]
        CpvCpmBiddingScheme = 1L << 5,

        [EnumMember]
        DynamicFeedSetting = 1L << 6,

        [EnumMember]
        MultimediaAdsBidAdjustment = 1L << 7,

        [EnumMember]
        VerifiedTrackingSetting = 1L << 8,

        [EnumMember]
        DynamicDescriptionSetting = 1L << 9,

        [EnumMember]
        DisclaimerSetting = 1L << 10,

        [EnumMember]
        CampaignConversionGoal = 1L << 11,

        [EnumMember]
        TargetCpaInMaxConversion = 1L << 12,

        [EnumMember]
        ResponsiveSearchAdsSetting = 1L << 13,

        [EnumMember]
        CostPerSaleBiddingScheme = 1L << 14,

        [EnumMember]
        ShoppingSettingShoppableAdsEnabled = 1L << 15,

        [EnumMember]
        ShoppingSettingFeedLabel = 1L << 16,

        [EnumMember]
        CallToActionSetting = 1L << 17,

        [EnumMember]
        PageFeedInPerformanceMaxSettings = 1L << 18,

        [EnumMember]
        DealIds = 1L << 19,

        [EnumMember]
        AutoGeneratedTextAndImageOptOutInPerformanceMaxSettings = 1L << 20,

        [EnumMember]
        CostPerSaleOptOutInPerformanceMaxSettings = 1L << 21,

        [EnumMember]
        VanityPharmaSetting = 1L << 22,

        [EnumMember]
        ManualCpi = 1L << 23,

        [EnumMember]
        IsDealCampaign = 1L << 24,

        [EnumMember]
        AppSetting = 1L << 25,

        [EnumMember]
        CallToActionOptOut = 1L << 26,

        [EnumMember]
        ThirdPartyMeasurementSetting = 1L << 27,

        [EnumMember]
        ManualCpc = 1L << 28,

        [EnumMember]
        NewCustomerAcquisitionGoalSetting = 1L << 29,

        //This value represents three additional fields in one, StartDate, EndDate, and UseCampaignLevelDates, which are all required for LifetimeBudget. They are
        //consolidated because this enum is a signed int, so we cannot add more than 31 fields without changing the type of the enum and breaking Back Compatibility.
        [EnumMember]
        LifetimeBudgetFields = 1L << 30,

        [EnumMember]
        MaxConversionValueWithMaxCpc = 1L << 31,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "KeywordAdditionalField")]
    public enum KeywordAdditionalField
    {
        None = 0,

        [EnumMember]
        MaxConversionValueWithMaxCpc = 1 << 1,

    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "PortfolioBidStrategyAdditionalField")]
    public enum PortfolioBidStrategyAdditionalField
    {
        None = 0,

        [EnumMember]
        MaxConversionValueWithMaxCpc = 1 << 1,

    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "BrandKitAdditionalField")]
    public enum BrandKitAdditionalField
    {
        None = 0,

        [EnumMember]
        BusinessName = 1 << 0,

        [EnumMember]
        BrandVoice = 1 << 1
    }

    [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AdGroupAdditionalField")]
    public enum AdGroupAdditionalField
    {
        None = 0,

        [EnumMember]
        AdScheduleUseSearcherTimeZone = 1 << 0,

        [EnumMember]
        AdGroupType = 1 << 1,

        [EnumMember]
        CpvBid = 1 << 2,

        [EnumMember]
        CpmBid = 1 << 3,

        [EnumMember]
        MultimediaAdsBidAdjustment = 1 << 4,

        [EnumMember]
        CommissionRate = 1 << 5,

        [EnumMember]
        PercentCpcBid = 1 << 6,

        [EnumMember]
        McpaBid = 1 << 7,

        [EnumMember]
        UseOptimizedTargeting = 1 << 8,

        [EnumMember]
        FrequencyCapSettings = 1 << 9,

        [EnumMember]
        UsePredictiveTargeting = 1 << 10,

        [EnumMember]
        MaxConversionValueWithMaxCpc = 1 << 11,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AssetGroupAdditionalField")]
    public enum AssetGroupAdditionalField
    {
        None = 0,
        
        [EnumMember]
        AssetGroupSearchThemes = 1 << 0,
        
        [EnumMember]
        AssetGroupUrlTargets = 1 << 1,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AudienceGroupAdditionalField")]
    public enum AudienceGroupAdditionalField
    {
        None = 0,

        [EnumMember]
        ProfileDimension = 1,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AssetGroupListingGroupAdditionalField")]
    public enum AssetGroupListingGroupAdditionalField
    {
        None = 0,

        [EnumMember]
        ListingGroupPath = 1 << 0,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "MediaAdditionalField")]
    public enum MediaAdditionalField
    {
        None = 0,

        [EnumMember]
        Text = 1 << 0,
    }

    /// <summary>
    /// Editirial Status  Ad
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi,
            Name = "AssetGroupEditorialStatus")]
    public enum AssetGroupEditorialStatus
    {
        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "CriterionAdditionalField")]
    public enum CriterionAdditionalField
    {
        None = 0,

        [EnumMember]
        CriterionCashback = 1 << 0,

        [EnumMember]
        Operator = 1 << 1,

        [EnumMember]
        Placement = 1 << 2,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdRotationType
    {
        [EnumMember]
        OptimizeForClicks = 0,

        [EnumMember]
        RotateAdsEvenly = 1
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum GeoLocationType
    {
        None = 0,

        [EnumMember]
        Country = 1,

        [EnumMember]
        SubGeography = 2,

        [EnumMember]
        MetroArea = 3,

        [EnumMember]
        City = 4
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum EntityType
    {
        None = 0,

        [EnumMember]
        Campaign = 1,

        [EnumMember]
        AdGroup = 2,

        [EnumMember]
        Ad = 3,

        [EnumMember]
        Keyword = 4,

        [EnumMember]
        AssetGroup = 5,
    }

    /// <summary>
    /// Exclusion Types. Defined as Flags so that we can retrieve multiple Exclusions
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    [Flags]
    public enum ExclusionType
    {
        None = 0,

        [EnumMember]
        Location = 1
    }

    /// <summary>
    /// Exclusion Types. Defined as Flags so that we can retrieve multiple Exclusions
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AppealStatus
    {
        None = 0,

        [EnumMember]
        Appealable = 1,

        [EnumMember]
        AppealPending = 2,

        [EnumMember]
        NotAppealable = 3
    }

    /// <summary>
    /// The image type.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ImageType
    {
        // Do not expose publicly
        None = 0,

        [EnumMember]
        Image = 1,

        [EnumMember]
        Icon = 2,

        [EnumMember]
        Image16x9 = 3,

        [EnumMember]
        Image15x10 = 4,

        [EnumMember]
        Image4x3 = 5,

        [EnumMember]
        Image12x10 = 6
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdGroupCriterionStatus
    {
        [EnumMember]
        Active = 0,

        [EnumMember]
        Paused = 1,

        [EnumMember]
        Deleted = 2
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CampaignCriterionStatus
    {
        [EnumMember]
        Active = 0,

        [EnumMember]
        Paused = 1,

        [EnumMember]
        Deleted = 2
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum HotelDateSelectionType
    {
        [EnumMember]
        Unknown,

        [EnumMember]
        DefaultSelection,

        [EnumMember]
        UserSelection,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdGroupCriterionType : Int64
    {
        None = 0,

        [EnumMember]
        ProductPartition = 1L << 1,

        [EnumMember]
        Webpage = 1L << 2,

        [EnumMember]
        Targets = 1L << 3,

        [EnumMember]
        Age = 1L << 4,

        [EnumMember]
        Gender = 1L << 5,

        [EnumMember]
        DayTime = 1L << 6,

        [EnumMember]
        Device = 1L << 7,

        [EnumMember]
        Location = 1L << 8,

        [EnumMember]
        LocationIntent = 1L << 9,

        [EnumMember]
        Radius = 1L << 10,

        [EnumMember]
        Audience = 1L << 11,

        [EnumMember]
        CustomAudience = 1L << 12,

        [EnumMember]
        InMarketAudience = 1L << 13,

        [EnumMember]
        RemarketingList = 1L << 14,

        [EnumMember]
        CompanyName = 1L << 15,

        [EnumMember]
        JobFunction = 1L << 16,

        [EnumMember]
        Industry = 1L << 17,

        [EnumMember]
        ProductAudience = 1L << 18,

        [EnumMember]
        SimilarRemarketingList = 1L << 19,

        [EnumMember]
        CombinedList = 1L << 20,

        [EnumMember]
        HotelGroup = 1L << 21,

        [EnumMember]
        HotelAdvanceBookingWindow = 1L << 22,

        [EnumMember]
        HotelCheckInDay = 1L << 23,

        [EnumMember]
        HotelLengthOfStay = 1L << 24,

        [EnumMember]
        HotelDateSelectionType = 1L << 25,

        [EnumMember]
        HotelCheckInDate = 1L << 26,

        [EnumMember]
        Genre = 1L << 27,

        [EnumMember]
        CustomerList = 1L << 28,

        [EnumMember]
        ImpressionBasedRemarketingList = 1L << 29,

        [EnumMember]
        Placement = 1L << 30,

        [EnumMember]
        Topic = 1L << 31,
    }

    /// <summary>
    ///     Defines constants for association types.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AssociationType
    {
        /// <summary>
        ///     Campaign association.
        /// </summary>
        [EnumMember]
        Campaign = 1,

        /// <summary>
        ///     Ad Group association.
        /// </summary>
        [EnumMember]
        AdGroup = 2,

        /// <summary>
        ///     Account association.
        /// </summary>
        [EnumMember]
        Account = 3,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdGroupCriterionEditorialStatus
    {
        /// <summary>
        /// Active
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        /// Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        /// Inactive
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ResponseMode
    {
        [EnumMember]
        ErrorsOnly,

        [EnumMember]
        ErrorsAndResults,
    }

    /// <summary>
    ///     Defines constants for different editorial statuses of Ad Extension.
    /// </summary>
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdExtensionEditorialStatus
    {
        /// <summary>
        ///     Approved
        /// </summary>
        [EnumMember]
        Active,

        /// <summary>
        ///     Disapproved
        /// </summary>
        [EnumMember]
        Disapproved,

        /// <summary>
        ///     Under review.
        /// </summary>
        [EnumMember]
        Inactive,

        /// <summary>
        ///     Partially approved.
        /// </summary>
        [EnumMember]
        ActiveLimited
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum DistanceUnit : short
    {
        [EnumMember]
        Miles,
        [EnumMember]
        Kilometers,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum Minute : short
    {
        [EnumMember]
        Zero,
        [EnumMember]
        Fifteen,
        [EnumMember]
        Thirty,
        [EnumMember]
        FortyFive
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum IntentOption : short
    {
        [EnumMember]
        PeopleInOrSearchingForOrViewingPages,
        [EnumMember]
        PeopleIn,
        [EnumMember]
        PeopleSearchingForOrViewingPages
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    [Flags]
    public enum MediaEnabledEntityFilter
    {
        None = 0,

        [EnumMember]
        ImageAdExtension = 1,

        [EnumMember]
        ResponsiveAd = 2,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CampaignCriterionType
    {
        [EnumMember]
        ProductScope = 1 << 0,

        [EnumMember]
        Webpage = 1 << 2,

        [EnumMember]
        Targets = 1 << 3,

        [EnumMember]
        Age = 1 << 4,

        [EnumMember]
        DayTime = 1 << 5,

        [EnumMember]
        Gender = 1 << 6,

        [EnumMember]
        Location = 1 << 7,

        [EnumMember]
        Radius = 1 << 8,

        [EnumMember]
        Device = 1 << 9,

        [EnumMember]
        LocationIntent = 1 << 10,

        [EnumMember]
        Audience = 1 << 11,

        [EnumMember]
        CustomAudience = 1 << 12,

        [EnumMember]
        InMarketAudience = 1 << 13,

        [EnumMember]
        RemarketingList = 1 << 14,

        [HideMeFromWsdl(WsdlConfiguration.Feature.LinkedInForSearchBscDsa)]
        [EnumMember]
        CompanyName = 1 << 15,

        [HideMeFromWsdl(WsdlConfiguration.Feature.LinkedInForSearchBscDsa)]
        [EnumMember]
        JobFunction = 1 << 16,

        [HideMeFromWsdl(WsdlConfiguration.Feature.LinkedInForSearchBscDsa)]
        [EnumMember]
        Industry = 1 << 17,

        [EnumMember]
        ProductAudience = 1 << 18,

        [EnumMember]
        SimilarRemarketingList = 1 << 19,

        [HideMeFromWsdl(WsdlConfiguration.Feature.SponsorProductAdsV2)]
        [EnumMember]
        Store = 1 << 20,

        [EnumMember]
        CombinedList = 1 << 21,

        [EnumMember]
        CustomerList = 1 << 22,

        [EnumMember]
        ImpressionBasedRemarketingList = 1 << 23,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum EntityScope
    {
        [EnumMember]
        Account,

        [EnumMember]
        Customer
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum StringOperator
    {
        /// <summary>
        /// No expression
        /// </summary>
        [EnumMember]
        None = 0,

        /// <summary>
        /// Input string equals to expression string.
        /// </summary>
        [EnumMember]
        Equals = 1,

        /// <summary>
        /// Input string contains expression string.
        /// </summary>
        [EnumMember]
        Contains = 2,

        /// <summary>
        /// Input string starts with expression string.
        /// </summary>
        [EnumMember]
        BeginsWith = 3,

        /// <summary>
        ///  Input string ends with expression string.
        /// </summary>
        [EnumMember]
        EndsWith = 4,

        /// <summary>
        /// Input string does not equal to expression string.
        /// </summary>
        [EnumMember]
        NotEquals = 5,

        /// <summary>
        /// Input string does not contain expression string.
        /// </summary>
        [EnumMember]
        DoesNotContain = 6,

        /// <summary>
        /// Input string does not start with expression string.
        /// </summary>
        [EnumMember]
        DoesNotBeginWith = 7,

        /// <summary>
        ///  Input string does not end with expression string.
        /// </summary>
        [EnumMember]
        DoesNotEndWith = 8,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum NumberOperator
    {
        /// <summary>
        /// No expression
        /// </summary>
        [EnumMember]
        None = 0,

        /// <summary>
        /// Input number equal to expresion
        /// </summary>
        [EnumMember]
        Equals = 1,

        /// <summary>
        ///  Input number greater than expression.
        /// </summary>
        [EnumMember]
        GreaterThan = 2,

        /// <summary>
        ///  Input number less than expression.
        /// </summary>
        [EnumMember]
        LessThan = 3,

        /// <summary>
        ///  Input number greater than and equal to expression.
        /// </summary>
        [EnumMember]
        GreaterThanEqualTo = 4,

        /// <summary>
        ///   Input number less than and equal to expression.
        /// </summary>
        [EnumMember]
        LessThanEqualTo = 5,

        /// <summary>
        ///   Input number not equal to expression.
        /// </summary>
        [EnumMember]
        NotEquals = 6
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AudienceType
    {
        [EnumMember]
        RemarketingList = 1 << 0,

        [EnumMember]
        Custom = 1 << 1,

        [EnumMember]
        InMarket = 1 << 2,

        [EnumMember]
        Product = 1 << 3,

        [EnumMember]
        SimilarRemarketingList = 1 << 4,

        [EnumMember]
        CombinedList = 1 << 5,

        [EnumMember]
        CustomerList = 1 << 6,

        [EnumMember]
        ImpressionBasedRemarketingList = 1 << 7,
    }


    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AudienceAdditionalField
    {
        None = 0,

        [EnumMember]
        NormalForm = 1 << 0,

        [EnumMember]
        NumberRuleItem = 1 << 1,

        [EnumMember]
        ImpressionBasedRemarketingList = 1 << 2,

        [EnumMember]
        CampaignIdsAdGroupIds = 1 << 3,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum NormalForm
    {
        /// <summary>
        /// AND of ORs: Conjunctive normal form.
        /// </summary>
        [EnumMember]
        Conjunctive = 1,

        /// <summary>
        /// OR of ANDs: Disjunctive normal form.
        /// </summary>
        [EnumMember]
        Disjunctive = 2
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ImpressionBasedEntityType
    {
        [EnumMember]
        None = 0,

        [EnumMember]
        Campaign = 1,

        [EnumMember]
        AdGroup = 2,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum LogicalOperator
    {
        [EnumMember]
        And = 1,

        [EnumMember]
        Or = 2,

        [EnumMember]
        Not = 3,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ProductAudienceType
    {
        [EnumMember]
        GeneralVisitors = 1 << 0,

        [EnumMember]
        ProductSearchers = 1 << 1,

        [EnumMember]
        ProductViewers = 1 << 2,

        [EnumMember]
        ShoppingCartAbandoners = 1 << 3,

        [EnumMember]
        PastBuyers = 1 << 4,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ProfileType
    {
        [EnumMember]
        CompanyName,

        [EnumMember]
        JobFunction,

        [EnumMember]
        Industry,

        [EnumMember]
        JobSeniority,

        [EnumMember]
        JobTitle,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum UetTagTrackingStatus
    {
        [EnumMember]
        Unverified,

        [EnumMember]
        Active,

        [EnumMember]
        Inactive
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalTrackingStatus
    {
        [EnumMember]
        TagUnverified,

        [EnumMember]
        NoRecentConversions,

        [EnumMember]
        RecordingConversions,

        [EnumMember]
        TagInactive,

        [EnumMember]
        InactiveDueToTagUnavailable
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalStatus
    {
        [EnumMember]
        Active,

        [EnumMember]
        Paused,

        [EnumMember]
        Deleted
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalType
    {
        // Do not expose publicly, since we do not allow this to be passed in.
        None = 0,

        [EnumMember]
        Url = 1 << 0,

        [EnumMember]
        Duration = 1 << 1,

        [EnumMember]
        PagesViewedPerVisit = 1 << 2,

        [EnumMember]
        Event = 1 << 3,

        [EnumMember]
        AppInstall = 1 << 4,

        [EnumMember]
        OfflineConversion = 1 << 5,

        [EnumMember]
        InStoreTransaction = 1 << 6
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalCountType
    {
        [EnumMember]
        All,

        [EnumMember]
        Unique
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalRevenueType
    {
        [EnumMember]
        FixedValue,

        [EnumMember]
        VariableValue,

        [EnumMember]
        NoValue
    }

    /// <summary>
    /// ConversionGoalAdditionalField entity data contract
    /// </summary>
    [DataContract(
    Namespace = NamespaceConstants.AdvertiserApi,
    Name = "ConversionGoalAdditionalField")]
    [Flags]
    public enum ConversionGoalAdditionalField
    {
        None = 0,

        [EnumMember]
        ViewThroughConversionWindowInMinutes = 1,

        [EnumMember]
        IsExternallyAttributed = ViewThroughConversionWindowInMinutes << 1,

        [EnumMember]
        GoalCategory = IsExternallyAttributed << 1,

        [EnumMember]
        InactiveDueToTagUnavailable = GoalCategory << 1,

        [EnumMember]
        AttributionModelType = InactiveDueToTagUnavailable << 1,

        [EnumMember]
        IsEnhancedConversionsEnabled = AttributionModelType << 1,

        [EnumMember]
        IsAutoGoal = IsEnhancedConversionsEnabled << 1,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ValueOperator
    {
        [EnumMember]
        Equals,

        [EnumMember]
        LessThan,

        [EnumMember]
        GreaterThan
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ExpressionOperator
    {
        [EnumMember]
        Equals,

        [EnumMember]
        BeginsWith,

        [EnumMember]
        RegularExpression,

        [EnumMember]
        Contains
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum WebpageConditionOperand
    {
        [EnumMember]
        Unknown = 0,

        [EnumMember]
        Url = 1,

        [EnumMember]
        Category = 2,

        [EnumMember]
        PageTitle = 3,

        [EnumMember]
        PageContent = 4,

        [EnumMember]
        CustomLabel = 5,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum WebpageConditionOperator
    {
        [EnumMember]
        Unknown = 0,

        [EnumMember]
        Equals = 1,

        [EnumMember]
        Contains = 2,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AccountPropertyName
    {
        [EnumMember]
        None = 0,

        [EnumMember]
        TrackingUrlTemplate = 1,

        [EnumMember]
        MSCLKIDAutoTaggingEnabled = TrackingUrlTemplate << 1,

        [EnumMember]
        AdClickParallelTracking = MSCLKIDAutoTaggingEnabled << 1,

        [EnumMember]
        FinalUrlSuffix = AdClickParallelTracking << 1,

        [EnumMember]
        IncludeViewThroughConversions = FinalUrlSuffix << 1,

        [EnumMember]
        ProfileExpansionEnabled = IncludeViewThroughConversions << 1,

        [EnumMember]
        AllowImageAutoRetrieve = ProfileExpansionEnabled << 1,

        [EnumMember]
        AutoApplyRecommendations = AllowImageAutoRetrieve << 1,

        [EnumMember]
        IncludeAutoBiddingViewThroughConversions = AutoApplyRecommendations << 1,

        [EnumMember]
        AutoBiddingViewThroughConversionsValueAttributionWeight = IncludeAutoBiddingViewThroughConversions << 1,

        [EnumMember]
        LoopBackWindowForViewThroughConversions = AutoBiddingViewThroughConversionsValueAttributionWeight << 1,

        [EnumMember]
        BusinessAttributes = LoopBackWindowForViewThroughConversions << 1,

        [EnumMember]
        EnableMMAUnderDSAAdgroups = BusinessAttributes << 1,

        [EnumMember]
        OptOutFromMCM = EnableMMAUnderDSAAdgroups << 1,

        [EnumMember]
        NetflixTCAccepted = OptOutFromMCM << 1,

        [EnumMember]
        BlockedContentSegments = NetflixTCAccepted << 1,

        [EnumMember]
        AssetAIEnhancementOptout = BlockedContentSegments << 1,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CriterionTypeGroup
    {
        [EnumMember]
        Unknown = 0,

        [EnumMember]
        Gender = 1,

        [EnumMember]
        Age = 2,

        [EnumMember]
        Audience = 3,

        [EnumMember]
        CompanyName = 4,

        [EnumMember]
        JobFunction = 5,

        [EnumMember]
        Industry = 6,

        [EnumMember]
        IncomeRange = 7,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CallToAction
    {
        [EnumMember]
        Unknown,
        [EnumMember]
        ActNow,
        [EnumMember]
        ApplyNow,
        [EnumMember]
        BetNow,
        [EnumMember]
        BidNow,
        [EnumMember]
        BookACar,
        [EnumMember]
        BookHotel,
        [EnumMember]
        BookNow,
        [EnumMember]
        Browse, // Deprecated
        [EnumMember]
        BuyNow,
        [EnumMember]
        ChatNow,
        [EnumMember]
        Compare,
        [EnumMember]
        ContactUs,
        [EnumMember]
        Coupon,
        [EnumMember]
        Donate,
        [EnumMember]
        Download,
        [EnumMember]
        EmailNow,
        [EnumMember]
        EnrollNow,
        [EnumMember]
        Explore, // Deprecated
        [EnumMember]
        FileNow,
        [EnumMember]
        FindJob,
        [EnumMember]
        FreePlay,
        [EnumMember]
        FreeQuote,
        [EnumMember]
        FreeTrial,
        [EnumMember]
        GetDeals,
        [EnumMember]
        GetOffer,
        [EnumMember]
        GetQuote,
        [EnumMember]
        JoinNow,
        [EnumMember]
        LearnMore,
        [EnumMember]
        ListenNow,
        [EnumMember]
        LogIn,
        [EnumMember]
        Message, // Deprecated
        [EnumMember]
        NewCars, // Deprecated
        [EnumMember]
        OrderNow,
        [EnumMember]
        PlayGame,
        [EnumMember]
        PlayNow,
        [EnumMember]
        PostJob,
        [EnumMember]
        Register,
        [EnumMember]
        RentACar,
        [EnumMember]
        RentNow,
        [EnumMember]
        Reserve,
        [EnumMember]
        Sale,
        [EnumMember]
        SaveNow,
        [EnumMember]
        Schedule,
        [EnumMember]
        SeeMenu,
        [EnumMember]
        SeeMore, // Deprecated
        [EnumMember]
        SeeOffer,
        [EnumMember]
        SellNow,
        [EnumMember]
        ShopNow,
        [EnumMember]
        Showtimes,
        [EnumMember]
        SignIn,
        [EnumMember]
        SignUp,
        [EnumMember]
        StartFree, // Deprecated
        [EnumMember]
        StartNow,
        [EnumMember]
        Subscribe,
        [EnumMember]
        TestDrive,
        [EnumMember]
        TryNow,
        [EnumMember]
        UsedCars, // Deprecated
        [EnumMember]
        ViewCars,
        [EnumMember]
        ViewNow, // Deprecated
        [EnumMember]
        ViewPlans,
        [EnumMember]
        VisitSite, // Deprecated
        [EnumMember]
        VoteNow,
        [EnumMember]
        Watch,
        [EnumMember]
        WatchMore,
        [EnumMember]
        WatchNow,
        [EnumMember]
        Directions,
        [EnumMember]
        FindStore,
        [EnumMember]
        SwitchNow,
        [EnumMember]
        VisitStore,
        [EnumMember]
        RenewNow,
        [EnumMember]
        Reorder,
        [EnumMember]
        Default,
        [EnumMember]
        NoButton,
        [EnumMember]
        Install,
        [EnumMember]
        AddToCart,
        [EnumMember]
        BookTravel,
        [EnumMember]
        Buy,
        [EnumMember]
        OpenLink,
        [EnumMember]
        RegisterNow,
        [EnumMember]
        BuildNow,
        [EnumMember]
        Dealers,
        [EnumMember]
        GetDemo,
        [EnumMember]
        GetNow,
        [EnumMember]
        GoToDemo,
        [EnumMember]
        SeeDemo,
        [EnumMember]
        SeeModels,
        [EnumMember]
        SeeOffers,
        [EnumMember]
        ViewDemo,
        [EnumMember]
        CustomText,
        [EnumMember]
        Discover,
        [EnumMember]
        Dismiss,
        [EnumMember]
        Apply,
        [EnumMember]
        ViewQuote,
        [EnumMember]
        Join,
        [EnumMember]
        Attend,
        [EnumMember]
        RequestDemo,
        [EnumMember]
        Automated = 100,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum LanguageName
    {
        [EnumMember] None = 0,
        [EnumMember] Arabic = 7,
        [EnumMember] Danish = 28,
        [EnumMember] Dutch = 29,
        [EnumMember] German = 40,
        [EnumMember] English = 30,
        [EnumMember] Finnish = 35,
        [EnumMember] French = 36,
        [EnumMember] Italian = 55,
        [EnumMember] Norwegian = 82,
        [EnumMember] Portuguese = 88,
        [EnumMember] Spanish = 109,
        [EnumMember] Swedish = 112,
        [EnumMember] TraditionalChinese = 24,
        [EnumMember] SimplifiedChinese = 137,
        [EnumMember] Greek = 41,
        [EnumMember] Polish = 87,
        [EnumMember] Bulgarian = 19,
        [EnumMember] Czech = 27,
        [EnumMember] Estonian = 32,
        [EnumMember] Croatian = 26,
        [EnumMember] Hungarian = 48,
        [EnumMember] Lithuanian = 70,
        [EnumMember] Latvian = 68,
        [EnumMember] Maltese = 75,
        [EnumMember] Romanian = 92,
        [EnumMember] Slovak = 106,
        [EnumMember] Slovenian = 107,
        [EnumMember] Turkish = 123,
        [EnumMember] Serbian = 98,
        [EnumMember] Bosnian = 138,
        [EnumMember] Albanian = 5,
        [EnumMember] Macedonian = 71,
        [EnumMember] Icelandic = 49,
        [EnumMember] Japanese = 56,
        [EnumMember] Hebrew = 46,
        [EnumMember] Russian = 93,
        [EnumMember] Malay = 73,
        [EnumMember] Thai = 118,
        [EnumMember] Indonesian = 50,
        [EnumMember] Tagalog = 113,
        [EnumMember] Vietnamese = 129,
        [EnumMember] Hindi = 47,
        [EnumMember] Korean = 64,

        // Represents All languages. All languages means current languages and languages supported in the future
        [EnumMember] All = 1000
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AdGroupPrivacyStatus")]
    public enum AdGroupPrivacyStatus
    {
        [EnumMember]
        Unknown,

        [EnumMember]
        Active,

        [EnumMember]
        TargetingTooNarrow,

        [EnumMember]
        Pending,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum BMCStoreAdditionalField
    {
        None = 0,

        [EnumMember]
        GlobalStore = 1 << 0,

        [EnumMember]
        StoreUrl = 1 << 1,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum BMCStoreSubType
    {
        [EnumMember]
        CoOp = 1,

        [EnumMember]
        GlobalStore = 2
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum BidOption
    {
        [EnumMember]
        BidValue,

        [EnumMember]
        BidBoost
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ActionAdExtensionActionType
    {
        [EnumMember] Unknown,
        [EnumMember] ActNow,
        [EnumMember] ApplyNow,
        [EnumMember] BetNow,
        [EnumMember] BidNow,
        [EnumMember] BookACar,
        [EnumMember] BookHotel,
        [EnumMember] BookNow,
        [EnumMember] Browse,
        [EnumMember] BuyNow,
        [EnumMember] ChatNow,
        [EnumMember] Compare,
        [EnumMember] ContactUs,
        [EnumMember] Coupon,
        [EnumMember] Donate,
        [EnumMember] Download,
        [EnumMember] EmailNow,
        [EnumMember] EnrollNow,
        [EnumMember] Explore,
        [EnumMember] FileNow,
        [EnumMember] FindJob,
        [EnumMember] FreePlay,
        [EnumMember] FreeQuote,
        [EnumMember] FreeTrial,
        [EnumMember] GetDeals,
        [EnumMember] GetOffer,
        [EnumMember] GetQuote,
        [EnumMember] JoinNow,
        [EnumMember] LearnMore,
        [EnumMember] ListenNow,
        [EnumMember] LogIn,
        [EnumMember] Message,
        [EnumMember] NewCars,
        [EnumMember] OrderNow,
        [EnumMember] PlayGame,
        [EnumMember] PlayNow,
        [EnumMember] PostJob,
        [EnumMember] Register,
        [EnumMember] RentACar,
        [EnumMember] RentNow,
        [EnumMember] Reserve,
        [EnumMember] Sale,
        [EnumMember] SaveNow,
        [EnumMember] Schedule,
        [EnumMember] SeeMenu,
        [EnumMember] SeeMore,
        [EnumMember] SeeOffer,
        [EnumMember] SellNow,
        [EnumMember] ShopNow,
        [EnumMember] Showtimes,
        [EnumMember] SignIn,
        [EnumMember] SignUp,
        [EnumMember] StartFree,
        [EnumMember] StartNow,
        [EnumMember] Subscribe,
        [EnumMember] TestDrive,
        [EnumMember] TryNow,
        [EnumMember] UsedCars,
        [EnumMember] ViewCars,
        [EnumMember] ViewNow,
        [EnumMember] ViewPlans,
        [EnumMember] VisitSite,
        [EnumMember] VoteNow,
        [EnumMember] Watch,
        [EnumMember] WatchMore,
        [EnumMember] WatchNow,
        [EnumMember] Directions,
        [EnumMember] FindStore,
        [EnumMember] SwitchNow,
        [EnumMember] VisitStore,
        [EnumMember] RenewNow,
        [EnumMember] Reorder,
        [EnumMember] BuildNow,
        [EnumMember] Dealers,
        [EnumMember] GetDemo,
        [EnumMember] GetNow,
        [EnumMember] GoToDemo,
        [EnumMember] SeeDemo,
        [EnumMember] SeeModels,
        [EnumMember] SeeOffers,
        [EnumMember] ViewDemo,
    }

    [HideMeFromWsdl(WsdlConfiguration.Feature.ImportApi)]
    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "ImportAdditionalField")]
    public enum ImportAdditionalField
    {
        [EnumMember]
        None = 0,
        [EnumMember]
        NotificationEmail = 1,
        [EnumMember]
        AutoDeviceBidOptimization = 2,
        [EnumMember]
        ActiveAdGroupsOnly = 4,
        [EnumMember]
        SearchAndReplaceForCustomParameters = 8,
        [EnumMember]
        AdScheduleUseSearcherTimezone = 16,
        [EnumMember]
        NewImageAdExtensions = 32,
        [EnumMember]
        UpdateImageAdExtensions = 64,
        [EnumMember]
        SearchAndReplaceForFinalURLSuffix = 128,
        [EnumMember]
        RenameCampaignNameWithSuffix = 256,
        [EnumMember]
        UpdateAdUrls = 512,
        [EnumMember]
        NewLogoAdExtensions = 1024,
        [EnumMember]
        UpdateLogoAdExtensions = 2048,
        [EnumMember]
        UpdateSitelinkUrls = 4096,
        [EnumMember]
        NewLeadFormAdExtensions = 8192,
        [EnumMember]
        UpdateLeadFormAdExtensions = 16384,
        [EnumMember]
        NewAccountNegativeKeywords = 32768,
        [EnumMember]
        UpdateAccountNegativeKeywords = 65536,
        [EnumMember]
        UpdateAdCustomizerAttributes = 131072,
        [EnumMember]
        NewConversionGoals = 262144,
        [EnumMember]
        UpdateConversionGoals = 524288,
        [EnumMember]
        NewBrandSuitability = 1048576,
        [EnumMember]
        UpdateBrandSuitability = 2097152,
        [EnumMember]
        NewCarouselAd = 4194304,
        [EnumMember]
        UpdateAssetAutomationCampaignSetting = 8388608,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum PromotionOccasion
    {
        [EnumMember] Unknown = 0,
        [EnumMember] NewYears = 1,
        [EnumMember] ValentinesDay = 2,
        [EnumMember] Easter = 3,
        [EnumMember] MothersDay = 4,
        [EnumMember] FathersDay = 5,
        [EnumMember] LaborDay = 6,
        [EnumMember] BackToSchool = 7,
        [EnumMember] Halloween = 8,
        [EnumMember] BlackFriday = 9,
        [EnumMember] CyberMonday = 10,
        [EnumMember] Christmas = 11,
        [EnumMember] BoxingDay = 12,
        [EnumMember] None = 13,
        [EnumMember] IndependenceDay = 14,
        [EnumMember] NationalDay = 15,
        [EnumMember] EndOfSeason = 16,
        [EnumMember] WinterSale = 17,
        [EnumMember] SummerSale = 18,
        [EnumMember] FallSale = 19,
        [EnumMember] SpringSale = 20,
        [EnumMember] Ramadan = 21,
        [EnumMember] EidAlFitr = 22,
        [EnumMember] EidAlAdha = 23,
        [EnumMember] SinglesDay = 24,
        [EnumMember] WomensDay = 25,
        [EnumMember] Holi = 26,
        [EnumMember] ParentsDay = 27,
        [EnumMember] StNicholasDay = 28,
        [EnumMember] ChineseNewYear = 29,
        [EnumMember] Carnival = 30,
        [EnumMember] Epiphany = 31,
        [EnumMember] RoshHashanah = 32,
        [EnumMember] Passover = 33,
        [EnumMember] Hanukkah = 34,
        [EnumMember] Diwali = 35,
        [EnumMember] Navratri = 36,
        [EnumMember] Songkran = 37,
        [EnumMember] YearEndGift = 38
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum PromotionDiscountModifier
    {
        [EnumMember] Unknown = 0,

        [EnumMember] UpTo = 1,

        [EnumMember] None = 2,

    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AdExtensionHeaderType
    {
        [EnumMember] Unknown = 0,

        [EnumMember] Amenities = 1,

        [EnumMember] Brands = 2,

        [EnumMember] Classes = 3,

        [EnumMember] Courses = 4,

        [EnumMember] DailyRates = 5,

        [EnumMember] DegreePrograms = 6,

        [EnumMember] Departments = 7,

        [EnumMember] Destinations = 8,

        [EnumMember] FeaturedHotels = 9,

        [EnumMember] Goods = 10,

        [EnumMember] Grades = 11,

        [EnumMember] Highlights = 12,

        [EnumMember] InsuranceCoverage = 13,

        [EnumMember] Items = 14,

        [EnumMember] Languages = 15,

        [EnumMember] Locations = 16,

        [EnumMember] Models = 17,

        [EnumMember] Neighborhoods = 18,

        [EnumMember] Prices = 19,

        [EnumMember] Rates = 20,

        [EnumMember] Ratings = 21,

        [EnumMember] SchoolDistricts = 22,

        [EnumMember] Services = 23,

        [EnumMember] ServiceCatalog = 24,

        [EnumMember] Shows = 25,

        [EnumMember] Sizes = 26,

        [EnumMember] Styles = 27,

        [EnumMember] Tools = 28,

        [EnumMember] Topics = 29,

        [EnumMember] Types = 30,

        [EnumMember] Vacations = 31,

        [EnumMember] Vehicles = 32,

        [EnumMember] What = 33,

        [EnumMember] Who = 34,

        [EnumMember] Why = 35,

        [EnumMember] Deals = 36,

        [EnumMember] BestSellers = 37,

        [EnumMember] AgeGroups = 38,

        [EnumMember] Occasions = 39,

        [EnumMember] Flowers = 40
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
    public enum LeadFormCallToAction
    {
        [EnumMember] Unknown,
        [EnumMember] CustomText,
        [EnumMember] ApplyNow,
        [EnumMember] BookNow,
        [EnumMember] ContactUs,
        [EnumMember] Download,
        [EnumMember] GetOffer,
        [EnumMember] GetQuote,
        [EnumMember] LearnMore,
        [EnumMember] SignUp,
        [EnumMember] Subscribe
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
    public enum ConfirmationAction
    {
        [EnumMember] Unknown = 0,
        [EnumMember] None = 1,
        [EnumMember] VisitWebsite = 2,
        [EnumMember] DownloadFile = 3,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    [HideMeFromWsdl(WsdlConfiguration.Feature.LeadFormAdExtension_V13)]
    public enum LeadDelivery
    {
        [EnumMember] Unknown = 0,
        [EnumMember] CSV = 1,
        [EnumMember] Email = 2,
        [EnumMember] Webhook = 3,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ConversionGoalCategory
    {

        [EnumMember]
        Unknown = 0,

        [EnumMember]
        None = 1,

        [EnumMember]
        Purchase = 2,

        [EnumMember]
        AddToCart = 3,

        [EnumMember]
        BeginCheckout = 4,

        [EnumMember]
        Subscribe = 5,

        [EnumMember]
        SubmitLeadForm = 6,

        [EnumMember]
        BookAppointment = 7,

        [EnumMember]
        Signup = 8,

        [EnumMember]
        RequestQuote = 9,

        [EnumMember]
        GetDirections = 10,

        [EnumMember]
        OutboundClick = 11,

        [EnumMember]
        Contact = 12,

        [EnumMember]
        PageView = 13,

        [EnumMember]
        Download = 14,

        [EnumMember]
        Other = 15
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AttributionModelType
    {

        [EnumMember]
        LastClick = 0,

        [EnumMember]
        LastTouch = 1
    }

    [HideMeFromWsdl(WsdlConfiguration.Feature.HouseholdIncomeTargeting)]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum IncomeRange
    {
        [EnumMember]
        Unknown = 0,

        [EnumMember]
        LowerFifty = 1,

        [EnumMember]
        FiftyToSixty = 2,

        [EnumMember]
        SixtyToSeventy = 3,

        [EnumMember]
        SeventyToEighty = 4,

        [EnumMember]
        EightyToNinety = 5,

        [EnumMember]
        TopTen = 6
    }


    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum ImportEntityType
    {
        [EnumMember] Unknown = 0,

        [EnumMember] Campaign = 1,

        [EnumMember] AdGroup = 2,

        [EnumMember] Ad = 3,

        [EnumMember] Keyword = 4,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AssetGroupListingType
    {
        [EnumMember]
        Subdivision = 1,

        [EnumMember]
        Unit = 2
    }
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum CustomerListActionType
    {
        [EnumMember]
        None = 0,

        [EnumMember]
        Add = 1,

        [EnumMember]
        Remove = 2,

        [EnumMember]
        Replace = 3
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "DeviceType")]
    public enum DeviceType
    {
        [EnumMember]
        None = 0,

        /// <summary>
        /// Laptops and PCs
        /// </summary>
        [EnumMember]
        Computers = 1 << 0,

        /// <summary>
        /// Mobile devices with Hi-fi browsers
        /// </summary>
        [EnumMember]
        Smartphones = 1 << 1,

        /// <summary>
        /// Tablet devices
        /// </summary>
        [EnumMember]
        Tablets = 1 << 2,

        [EnumMember]
        All = Computers | Smartphones | Tablets,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "ConversionValueRuleAdditionalField")]
    public enum ConversionValueRuleAdditionalField
    {
        None = 0,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "SeasonalityAdjustmentAdditionalField")]
    public enum SeasonalityAdjustmentAdditionalField
    {
        None = 0,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "DataExclusionAdditionalField")]
    public enum DataExclusionAdditionalField
    {
        None = 0,
    }

    [Flags]
    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "NewCustomerAcquisitionGoalAdditionalField")]
    public enum NewCustomerAcquisitionGoalAdditionalField
    {
        None = 0,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi)]
    public enum AppCampaignAppStore
    {
        [EnumMember]
        GoogleAppStore = 1,

        [EnumMember]
        AppleAppStore = 2,

        [EnumMember]
        MicrosoftAppStore = 3,
    }

    [DataContract(Namespace = NamespaceConstants.AdvertiserApi, Name = "AdRecommendationAdditionalField")]
    public enum AdRecommendationAdditionalField
    {
        None = 0,

        [EnumMember]
        ImageSuggestionMetadata = 1 << 0,

        [EnumMember]
        MediaRefineResults = 1 << 1,

        [EnumMember]
        PromptBrandWarning = 1 << 2,
    }
}
