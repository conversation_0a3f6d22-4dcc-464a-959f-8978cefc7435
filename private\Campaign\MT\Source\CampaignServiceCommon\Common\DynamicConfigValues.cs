﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common
{
	using AppConfigurationManager;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
	using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Feed;
	using Microsoft.AdCenter.Shared.MT;
	using Microsoft.Advertising.ServiceLocation;
    using Microsoft.BingAds.FrameworkExtensions.Collections;
	using Microsoft.BingAds.Utils;
	using System;
	using System.Collections.Generic;
	using System.Collections.ObjectModel;
	using System.Diagnostics;
	using System.Linq;
	using System.Security;
	using Unity;
	using ConfigurationManager = System.Configuration.ConfigurationManager;

	public interface IConfigValueReader
	{
		bool GetConfigValue(string key, out string value, Dictionary<string, string> overrideConfigValuesFromTest = null);
	}

	public class DynamicConfigValueReaderAdaptor : IConfigValueReader
	{
		public bool GetConfigValue(string key, out string value, Dictionary<string, string> overrideConfigValuesFromTest = null)
		{
			return DynamicConfigValues.GetValue(key, overrideConfigValuesFromTest, out value);
		}
	}

    public static partial class DynamicConfigValues
    {
        public const string MaxCount = "maxCount";
        public const string InitialInterval = "initialInterval";
        public const string Increment = "increment";
        public const string MaxInterval = "maxInterval";

        // Shared empty array for all the properties that need to return one and don't want to alocate a throw away one
        private static readonly IReadOnlyList<int> EmptyIntList = Array.Empty<int>();
        private static readonly IReadOnlyList<long> EmptyLongList = Array.Empty<long>();
        private static readonly IReadOnlyCollection<string> EmptyStringSet = new HashSet<string>();

        private static UnityContainer unityContainer;

        public static void SetUnityContainer(UnityContainer container)
        {
            unityContainer = container;
        }

        public static bool GetValue<T>(string key, Dictionary<string, string> overrideConfigValuesFromTest, out T value)
        {
            string overrideValue;
            if (overrideConfigValuesFromTest != null && overrideConfigValuesFromTest.TryGetValue(key, out overrideValue))
            {
                return DynamicConfig.GetValue(key, overrideValue, out value);
            }
            return DynamicConfig.GetValue(key, out value);
        }

        public static T GetRequiredValue<T>(string key, Dictionary<string, string> overrideConfigValuesFromTest)
        {
            string overrideValue;
            if (overrideConfigValuesFromTest != null && overrideConfigValuesFromTest.TryGetValue(key, out overrideValue))
            {
                return DynamicConfig.GetRequiredValue<T>(key, overrideValue);
            }
            return DynamicConfig.GetRequiredValue<T>(key);
        }

        public static T GetValue<T>(string key, Dictionary<string, string> overrideConfigValuesFromTest, T defaultValue)
        {
            string overrideValue;
            if (overrideConfigValuesFromTest != null && overrideConfigValuesFromTest.TryGetValue(key, out overrideValue))
            {
                return DynamicConfig.GetValue(key, overrideValue, defaultValue);
            }
            return DynamicConfig.GetValue(key, defaultValue);
        }

        public static IReadOnlyDictionary<long, int> GetIntValueByLongKey(string key, Dictionary<string, string> overrideConfigValuesFromTest)
        {
            string overrideValue = null;
            if (overrideConfigValuesFromTest != null && overrideConfigValuesFromTest.ContainsKey(key))
            {
                overrideValue = overrideConfigValuesFromTest[key];
            }

            return GetIntValueByLongKey(key, overrideValue);
        }

        public static int CampaignHealthProbeWebRequestTimeoutInSeconds => DynamicConfig.GetValue<int>("CampaignHealthProbeWebRequestTimeoutInSeconds", 30);
        public static int MaxAdGroupRowCountForUsingFileCache => DynamicConfig.GetValue<int>("MaxAdGroupRowCountForUsingFileCache", 250000);

        public static int TaskEngineDirectCallConversionToServiceCallPercentage => DynamicConfig.GetValue<int>("TaskEngineDirectCallConversionToServiceCallPercentage", 0);

        public static ReadOnlyCollection<long> AccountsSummaryResponseSizeLoggingUserIdWhiteList => DynamicConfig.GetValue<ReadOnlyCollection<long>>("AccountsSummaryResponseSizeLoggingUserIdWhiteList", new ReadOnlyCollection<long>(new List<long>()));

        public static int MaxSearchTermsCountForUsingFileCache => DynamicConfig.GetValue<int>("MaxSearchTermsCountForUsingFileCache", 250000);
        public static bool EnableMemoryCacheForAdGroup => DynamicConfig.GetValue<bool>("EnableMemoryCacheForAdGroup", false);
        public static int AggregatorMemoryCacheLimitMegabytes => DynamicConfig.GetValue<int>("AggregatorMemoryCacheLimitMegabytes", 1024 * 4);

        public static bool EnableConditionalMemoryCacheForSearchTerms => DynamicConfig.GetValue<bool>("EnableConditionalMemoryCacheForSearchTerms", false);

        public static bool SearchTermsComparisonLoadEnabled => DynamicConfig.GetValue<bool>("SearchTermsComparisonLoadEnabled", false);

        // This map contains a mapping of API operation IDs to boolean flags.
        // Developers should use this map when they need to support two versions of responding prompts during feature development/rollout environment wise using the <see cref="DynamicRespondingPromptAttribute"/>.
        // To use this feature, developers should append "<API operation ID>:<bool>" string to the semicolon separated string under the ApiOperationNameToDynamicPromptFlag config.
        // Set the flag to true to use the new responding prompt, and false to use the old responding prompt.
        // Please skip adding if you don't want to support multiple versions of responding prompts for the API.
        public static IReadOnlyDictionary<string, bool> OperationNameToPromptFlag => ConvertToStringBoolMap("ApiOperationNameToDynamicPromptFlag");

        public static bool DisableDateOverride => DynamicConfig.GetValue<bool>("DisableDateOverride", false);

        public static string SymmetricKeyTokensKeyVaultUrl => DynamicConfig.GetRequiredValue<string>("SymmetricKeyTokensKeyVaultUrl");

        public static bool EncodeEmailContentForTemplate => DynamicConfig.GetValue<bool>("EncodeEmailContentForTemplate", true);

        public static bool SupportOfflineConversionRestateRetract => DynamicConfig.GetValue<bool>("SupportOfflineConversionRestateRetract", false);
        public static bool EnableGoalBulk => DynamicConfig.GetValue<bool>("EnableGoalBulk", false);

        public static bool EnableAutoConversion => DynamicConfig.GetValue<bool>("EnableAutoConversion", false);

        public static bool EnableCustomSegmentV2 => DynamicConfig.GetValue<bool>("EnableCustomSegmentV2", false);

        public static bool UseMIForImageBlobAccess => DynamicConfig.GetValue<bool>("UseMIForImageBlobAccess", false);

        public static bool UseMIForEventHubAccess => DynamicConfig.GetValue<bool>("UseMIForEventHubAccess", false);

        public static string WebUIPluginService_sendTimeout => DynamicConfig.GetValue<string>("WebUIPluginService_sendTimeout", string.Empty);
        public static string WebUIPluginService_receiveTimeout => DynamicConfig.GetValue<string>("WebUIPluginService_receiveTimeout", string.Empty);
        public static string WebUIPluginService_openTimeout => DynamicConfig.GetValue<string>("WebUIPluginService_openTimeout", string.Empty);
        public static string WebUIPluginService_closeTimeout => DynamicConfig.GetValue<string>("WebUIPluginService_closeTimeout", string.Empty);
        public static string ImageBlobStorageAccountName => DynamicConfig.GetValue<string>("ImageBlobStorageAccountName", string.Empty);


        public static bool ValidateV1VideoMetadata = DynamicConfig.GetValue<bool>("ValidateV1VideoMetadata", false);

        public static bool MockVideoLibraryTranscodingSubmitJobInCI = DynamicConfig.GetValue<bool>("MockVideoLibraryTranscodingSubmitJobInCI", false);

        public static bool FixAdResourceStatusOnVideoAdValidation => DynamicConfig.GetValue<bool>("FixAdResourceStatusOnVideoAdValidation", false);

        public static bool EnableAssetResharding => DynamicConfig.GetValue<bool>("EnableAssetResharding", false);

        public static string AccountsThatSupportExternalAttribution => DynamicConfig.GetValue<string>("AccountsThatSupportExternalAttribution", string.Empty);

        public static bool EnableRemovingInvalidCharactersFromImportFileNameInImportUserPreferenceUpload => DynamicConfig.GetValue<bool>("EnableRemovingInvalidCharactersFromImportFileNameInImportUserPreferenceUpload", false);

        public static string KeywordIdFilesContainerName => DynamicConfig.GetRequiredValue<string>("KeywordIdFilesContainerName");

        public static string CampaignNameFileContainerName => DynamicConfig.GetRequiredValue<string>("CampaignNameFileContainerName");

        public static bool UnblockMAEFromSharedUIPackage => DynamicConfig.GetRequiredValue<bool>("UnblockMAEFromSharedUIPackage");

        public static string ImportAdGroupNamesContainerName => DynamicConfig.GetRequiredValue<string>("ImportAdGroupNamesContainerName");

        public static bool DoubleVerifyActivationEnabled => DynamicConfig.GetRequiredValue<bool>("DoubleVerifyActivationEnabled");
        public static bool DoubleVerifyAccountUnlinkEnabled => DynamicConfig.GetRequiredValue<bool>("DoubleVerifyAccountUnlinkEnabled");
        public static string DoubleVerifyApiBaseUrl => DynamicConfig.GetRequiredValue<string>("DoubleVerifyEndpoint");
        public static string DoubleVerifyLinkTokensKeyVaultAppId => DynamicConfig.GetRequiredValue<string>("DoubleVerifyLinkTokensKeyVaultAppId");
        public static string DoubleVerifyLinkTokensKeyVaultUrl => DynamicConfig.GetRequiredValue<string>("DoubleVerifyLinkTokensKeyVaultUrl");
        public static bool IsDoubleVerifySyncTaskEnabled => DynamicConfig.GetRequiredValue<bool>("IsDoubleVerifySyncTaskEnabled");
        public static float DoubleVerifySyncTaskMaxCpuUtilization => DynamicConfig.GetRequiredValue<float>("DoubleVerifySyncTaskMaxCpuUtilization");
        public static string DoubleVerifySyncTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("DoubleVerifySyncTaskTypeNameForTaskEngine");
        public static int ParallelExecutionsCountForDoubleVerifySyncTask => DynamicConfig.GetRequiredValue<int>("ParallelExecutionsCountForDoubleVerifySyncTask");
        public static int SleepTimeInMillisecondsForDoubleVerifySyncTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsForDoubleVerifySyncTask");
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForDoubleVerifySyncTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForDoubleVerifySyncTask");
        public static int ParallelExecutionsCountForDoubleVerifyAccountUnlinkTask => DynamicConfig.GetRequiredValue<int>("ParallelExecutionsCountForDoubleVerifyAccountUnlinkTask");
        public static int SleepTimeInMillisecondsForDoubleVerifyAccountUnlinkTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsForDoubleVerifyAccountUnlinkTask");
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForDoubleVerifyAccountUnlinkTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForDoubleVerifyAccountUnlinkTask");
        public static string DoubleVerifyAccountUnlinkTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("DoubleVerifyAccountUnlinkTaskTypeNameForTaskEngine");
        public static string UpdateROITaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("UpdateROITaskTypeNameForTaskEngine");
        public static bool EnableAutobiddingConsolidationFillGaps => DynamicConfig.GetRequiredValue<bool>("EnableAutobiddingConsolidationFillGaps");
        public static bool EnableAutobiddingConsolidation => DynamicConfig.GetRequiredValue<bool>("EnableAutobiddingConsolidation");
        public static bool IncludeTargetRoasInKeywordsAndAdGroupsGrid => DynamicConfig.GetValue<bool>("IncludeTargetRoasInKeywordsAndAdGroupsGrid", false);
        public static bool EnableConsentModeValue => DynamicConfig.GetValue<bool>("EnableConsentModeValue", false);

        public static string KeywordBulkUploadFilesContainerName
        {
            get { return DynamicConfig.GetRequiredValue<string>("KeywordBulkUploadFilesContainerName"); }
        }

        public static string EntityBulkUploadFilesContainerName
        {
            get { return DynamicConfig.GetRequiredValue<string>("EntityBulkUploadFilesContainerName"); }
        }

        public static bool IsTestEnvForDSAMixedModeImportFix
        {
            get { return DynamicConfig.GetRequiredValue<bool>("IsTestEnvForDSAMixedModeImportFix"); }
        }

        public static bool FetchTestAccountPerformanceDataFromAdWords
        {
            get { return DynamicConfig.GetValue<bool>("FetchTestAccountPerformanceDataFromAdWords", false); }
        }

        public static bool UseKeyVaultToStoreSymmetricKeys
        {
            get
            {
                return DynamicConfig.GetValue<bool>("UseKeyVaultToStoreSymmetricKeys", false);
            }
        }

        public static int ShoppingCampaignFetchBatchSizeInImport
        {
            get
            {
                return DynamicConfig.GetValue<int>("ShoppingCampaignFetchBatchSizeInImport", 3000);
            }
        }

        public static bool InitializeAccountAdvertiserCustomerCache
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("InitializeAccountAdvertiserCustomerCache");
            }
        }

        public static bool IsGoogleTagManagerImportTaskItemEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("IsGoogleTagManagerImportTaskItemEnabled");
            }
        }

        public static bool UseDeltaLoadForAdvertiserCustomerCacheAfterInitialLoad
        {
            get
            {
                return DynamicConfig.GetValue<bool>("UseDeltaLoadForAdvertiserCustomerCacheAfterInitialLoad", false);
            }
        }

        public static int GSyncTaskQueueCapacitySize
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncTaskQueueCapacitySize"); }
        }

        public static int GSyncTaskQueueConsumerSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncTaskQueueConsumerSize"); }
        }

        public static int GSyncTaskQueueCapacitySizeForFeedItemTarget => DynamicConfig.GetRequiredValue<int>("GSyncTaskQueueCapacitySizeForFeedItemTarget");

        public static int GSyncTaskQueueConsumerSizeForFeedItemTarget => DynamicConfig.GetRequiredValue<int>("GSyncTaskQueueConsumerSizeForFeedItemTarget");

        public static int GSyncDefaultPageSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultPageSize"); }
        }

        public static IReadOnlyDictionary<long, TimeSpan> SearchStreamRetryTimeoutInSeconds
        {
            get { return GetSearchStreamRetryTimeoutInSecondsByAccountId("SearchStreamRetryTimeoutInSeconds"); }
        }

        public static IReadOnlyList<long> AllAdGroupDemandDetectAccountIdWhitelist => DynamicConfig.GetValue("AllAdGroupDemandDetectAccountIdWhitelist", EmptyLongList);

        public static IReadOnlyDictionary<long, int> AdCustomizerFeedItemBatchSizeByAccountId
        {
            get { return GetIntValueByLongKey("AdCustomizerFeedItemBatchSizeByAccountId"); }
        }

        public static HashSet<long> EnableNativePerformanceMaxCampaignImportAccountBlacklist
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("EnableNativePerformanceMaxCampaignImportAccountBlacklist", EmptyLongList)); }
        }

        public static HashSet<long> EnablePerformanceMaxImageUpdateImportAccountBlockList
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("EnablePerformanceMaxImageUpdateImportAccountBlockList", EmptyLongList)); }
        }

        public static bool EnableImportPMaxAssetGroupMerge => DynamicConfig.GetRequiredValue<bool>("EnableImportPMaxAssetGroupMerge");

        public static bool EnablePMaxUrlDeliveryStatus => DynamicConfig.GetRequiredValue<bool>("EnablePMaxUrlDeliveryStatus");

        public static bool EnableImportPMaxAssetGroupTextAssetMerge => DynamicConfig.GetRequiredValue<bool>("EnableImportPMaxAssetGroupTextAssetMerge");

        public static IReadOnlyDictionary<long, long> PerformanceMaxCustomerAssetGroupLimit
        {
            get { return GetEntityLimitDictionary("PerformanceMaxCustomerAssetGroupLimit"); }
        }

        public static HashSet<long> EnableDsaToPMaxMigrationAccountBlacklist
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("EnableDsaToPMaxMigrationAccountBlacklist", EmptyLongList)); }
        }

        public static HashSet<long> PharmaCustomerIdsFromCustomerExclusionList
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("PharmaCustomerIdsFromCustomerExclusionList", EmptyLongList)); }
        }

        public static bool EnableGoogleAdsSearchStreamCaching => DynamicConfig.GetRequiredValue<bool>("EnableGoogleAdsSearchStreamCaching");

        public static bool EnableKustoExternalTableCreationForMIExperiment => DynamicConfig.GetRequiredValue<bool>("EnableKustoExternalTableCreationForMIExperiment");

        public static bool EnableSendingAssetsToEditorialForAppeal
        {
            get { return DynamicConfig.GetValue<bool>("EnableSendingAssetsToEditorialForAppeal", false); }
        }

        public static string GSyncHeartBeatInterval
        {
            get { return DynamicConfig.GetRequiredValue<string>("GSyncHeartBeatInterval"); }
        }

        public static TimeSpan ImportHeartBeatInterval
        {
            get { return DynamicConfig.GetValue<TimeSpan>("ImportHeartBeatInterval", TimeSpan.FromMinutes(10)); }
        }

        public static TimeSpan ImportCancellationCheckInterval
        {
            get { return DynamicConfig.GetValue<TimeSpan>("ImportCancellationCheckInterval", TimeSpan.FromSeconds(1)); }
        }

        public static int GSyncDefaultPredictValueBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultPredictValueBatchSize"); }
        }

        public static IReadOnlyDictionary<long, long> GSyncDefaultCampaignBatchSizeForKeyword
        {
            get { return GetEntityLimitDictionary("GSyncDefaultCampaignBatchSizeForKeyword"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForCampaignNegativeKeywordListAssociation
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForCampaignNegativeKeywordListAssociation"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForAd
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForAd"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForTemplateAd
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForTemplateAd"); }
        }

        public static IReadOnlyDictionary<long, int> GSyncDefaultCampaignBatchSizeForMultiAssetResponsiveAdByGoogleCustomerId => GetIntValueByLongKey("GSyncDefaultCampaignBatchSizeForMultiAssetResponsiveAdByGoogleCustomerId");

        public static IReadOnlyDictionary<long, int> GSyncDefaultCampaignBatchSizeForResponsiveSearchAdByGoogleCustomerId => GetIntValueByLongKey("GSyncDefaultCampaignBatchSizeForResponsiveSearchAdByGoogleCustomerId");

        public static int GSyncDefaultCampaignBatchSizeForCampaignDSATarget
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForCampaignDSATarget"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForAdGroupTarget
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForAdGroupTarget"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForAdGroup
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForAdGroup"); }
        }

        public static int GSyncDefaultCampaignBatchSizeForAdGroupCustomizer
        {
            get { return DynamicConfig.GetRequiredValue<int>("GSyncDefaultCampaignBatchSizeForAdGroupCustomizer"); }
        }

        public static bool GSyncEnableParseAdvancedLexFunction
        {
            get { return DynamicConfig.GetRequiredValue<bool>("GSyncEnableParseAdvancedLexFunction"); }
        }

        public static bool GSyncEnableHeartBeat
        {
            get { return DynamicConfig.GetRequiredValue<bool>("GSyncEnableHeartBeat"); }
        }

        public static string GSyncFreeStockImageAssetNamePrefix => DynamicConfig.GetValue("GSyncFreeStockImageAssetNamePrefix", "");
        public static string GSyncFreeStockImageAssetId => DynamicConfig.GetValue("GSyncFreeStockImageAssetId", "");

        public static bool EnhancedConversions
        {
            get { return DynamicConfig.GetValue<bool>("EnhancedConversions", false); }
        }

        public static long MaxKeywordAdCombinedPerCampaignBatch =>
            DynamicConfig.GetRequiredValue<long>("MaxKeywordAdCombinedPerCampaignBatch");

        public static long MaxAdGroupPerCampaignBatch =>
            DynamicConfig.GetRequiredValue<long>("MaxAdGroupPerCampaignBatch");

        public static bool ImportEnableSynidcatedSearchCheck
        {
            get { return DynamicConfig.GetRequiredValue<bool>("ImportEnableSynidcatedSearchCheck"); }
        }

        public static int ImportFetchGooglePerformanceDataDateRange => DynamicConfig.GetValue("ImportFetchGooglePerformanceDataDateRange", 7);

        public static int ImportFetchFacebookPerformanceDataDateRange => DynamicConfig.GetValue("ImportFetchFacebookPerformanceDataDateRange", 30);

        public static double ImportGoogleCampaignAverageCpcWithFactor => DynamicConfig.GetValue("ImportGoogleCampaignAverageCpcWithFactor", 1.0);

        public static double ImportGoogleAdGroupAverageCpcWithFactor => DynamicConfig.GetValue("ImportGoogleAdGroupAverageCpcWithFactor", 1.0);

        public static long ImportGoogleAccountClicksThreshold => DynamicConfig.GetValue("ImportGoogleAccountClicksThreshold", 100);

        public static long ImportGoogleCampaignClicksThreshold => DynamicConfig.GetValue("ImportGoogleCampaignClicksThreshold", 100);

        public static long ImportGoogleAdGroupClicksThreshold => DynamicConfig.GetValue("ImportGoogleAdGroupClicksThreshold", 10);

        public static double ImportUnsupportedBiddingStrategyWithAccountMinBid => DynamicConfig.GetValue("ImportUnsupportedBiddingStrategyWithAccountMinBid", 20);

        public static double ImportUnsupportedBiddingStrategyWithCampaignMinBid => DynamicConfig.GetValue("ImportUnsupportedBiddingStrategyWithCampaignMinBid", 20);

        public static double ImportUnsupportedBiddingStrategyWithAdGroupMinBid => DynamicConfig.GetValue("ImportUnsupportedBiddingStrategyWithAdGroupMinBid", 20);

        public static bool ImportApplyNewCpc => DynamicConfig.GetValue<bool>("ImportApplyNewCpc", false);

        public static bool ImportApplyNewCpcForUpdate => DynamicConfig.GetValue<bool>("ImportApplyNewCpcForUpdate", false);

        public static bool ImportIgnoreOthereBidValuesWhendefaultToECPC => DynamicConfig.GetValue<bool>("ImportIgnoreOthereBidValuesWhendefaultToECPC", false);

        public static bool ImportClearGoogleEntityMapForSplitTask => DynamicConfig.GetValue<bool>("ImportClearGoogleEntityMapForSplitTask", false);

        public static bool IsTestEnv => DynamicConfig.GetValue("IsTestEnv", false);

        public static bool IsTestInProdEnv => DynamicConfig.GetValue("IsTestInProdEnv", false);

        public static IReadOnlyList<long> EnableUpdateOnReimportPausedDeletedSiteLinkAdExtensionsWithDummyRowAccountIdWhitelist => DynamicConfig.GetValue("EnableUpdateOnReimportPausedDeletedSiteLinkAdExtensionsWithDummyRowAccountIdWhitelist", EmptyLongList);

        public static IReadOnlyDictionary<long, long> ImportCampaignSplitBatchSizeByAccountId => GetLongValueByLongKey("ImportCampaignSplitBatchSizeByAccountId");

        public static IReadOnlyDictionary<long, long> ImportCampaignSplitMaxDemandPerCampaignBatchByAccountId => GetLongValueByLongKey("ImportCampaignSplitMaxDemandPerCampaignBatchByAccountId");

        public static IReadOnlyDictionary<long, long> ImportCampaignSplitMaxAdGroupPerCampaignBatchByAccountId => GetLongValueByLongKey("ImportCampaignSplitMaxAdGroupPerCampaignBatchByAccountId");

        public static IReadOnlyDictionary<long, int> ImportMaxImageDownloadLimitByAccountId => GetIntValueByLongKey("ImportMaxImageDownloadLimitByAccountId");

        public static IReadOnlyDictionary<long, long> BingToGoogleLocationCacheManualOverride => GetLongValueByLongKey("BingToGoogleLocationCacheManualOverride");

        public static IReadOnlyDictionary<int, TimeSpan> MaxGSyncImportRunTime => GetImportMaxRuntimeByCustomerId("MaxGSyncImportRunTime");

        public static IReadOnlyDictionary<int, TimeSpan> MaxMultiAccountGoogleImportRunTime => GetImportMaxRuntimeByCustomerId("MaxMultiAccountGoogleImportRunTime");

        public static IReadOnlyDictionary<int, int> BulkCampaignBatchLimitForAdExtensionAssociation => GetOverrideIntValueByCustomerId("BulkCampaignBatchLimitForAdExtensionAssociation");

        public static IReadOnlyList<long> ImportDSACampaignForExistingSearchCampainCustomerIdList => DynamicConfig.GetValue("ImportDSACampaignForExistingSearchCampainCustomerIdList", EmptyLongList);

        public static IReadOnlyList<long> SmartImportSchedulerImmediateRunAccountWhitelist => DynamicConfig.GetValue("SmartImportSchedulerImmediateRunAccountWhitelist", EmptyLongList);

        public static IReadOnlyList<long> MultiAccountSmartImportSchedulerImmediateRunCustomerWhitelist => DynamicConfig.GetValue("MultiAccountSmartImportSchedulerImmediateRunCustomerWhitelist", EmptyLongList);

        public static IReadOnlyList<long> DetailedAdExtensionLoggingAccountIdList => DynamicConfig.GetValue("DetailedAdExtensionLoggingAccountIdList", EmptyLongList);

        public static bool MultiAccountGoogleImportAllowEmptyWhenPollingGoogleImportTaskExecution => DynamicConfig.GetValue("MultiAccountGoogleImportAllowEmptyWhenPollingGoogleImportTaskExecution", false);

        public static bool HandleDownlaodFailedVideosStatus => DynamicConfig.GetValue("HandleDownlaodFailedVideosStatus", false);

        public static int PercentAccountsForPmaxFinalURLExtractTrackingParams => DynamicConfig.GetValue("PercentAccountsForPmaxFinalURLExtractTrackingParams", 0);

        public static bool EnablePMaxEnhanceListings => DynamicConfig.GetValue("EnablePMaxEnhanceListings", false);
        
        public static bool EnableDummyAssetIdForGoogleImageAdForTestOnly => DynamicConfig.GetValue("EnableDummyAssetIdForGoogleImageAdForTestOnly", false);

        public static bool EnableImportGoogleImageAdActiveOnly => DynamicConfig.GetValue("EnableImportGoogleImageAdActiveOnly", false);
        
		public static int ImportDelayRunOnceTaskInSeconds => DynamicConfig.GetValue<int>("ImportDelayRunOnceTaskInSeconds", 1);

        public static int DaysToForceSyncScheduleImport => DynamicConfig.GetValue<int>("DaysToForceSyncScheduleImport", 30);

        public static int CtrlCWaitPeriodInSeconds => DynamicConfig.GetValue<int>("CtrlCWaitPeriodInSeconds", 30);

        public static bool EnabledChangeLongTimeImportCallAsAsync => DynamicConfig.GetValue("EnabledChangeLongTimeImportCallAsAsync", false);

        public static bool EnableAutoEnrollInPilotDuringImport => DynamicConfig.GetRequiredValue<bool>("EnableAutoEnrollInPilotDuringImport");

        public static bool EnableSmartGoal => DynamicConfig.GetRequiredValue<bool>("EnableSmartGoal");

        public static bool SmartGoalCanBeCreatedByCustomer => DynamicConfig.GetRequiredValue<bool>("SmartGoalCanBeCreatedByCustomer");

        public static bool SystemAudienceCanBeCreatedByCustomer => DynamicConfig.GetRequiredValue<bool>("SystemAudienceCanBeCreatedByCustomer");

        public static bool IsBlockJobAndCompanyIMAForVideoAdsEnabled => DynamicConfig.GetRequiredValue<bool>("IsBlockJobAndCompanyIMAForVideoAdsEnabled");

        public static bool EnablePlacementTargeting => DynamicConfig.GetRequiredValue<bool>("EnablePlacementTargeting");

        public static bool EnableTopicTargeting => DynamicConfig.GetRequiredValue<bool>("EnableTopicTargeting");

        public static bool EnableContentBulkDownload => DynamicConfig.GetRequiredValue<bool>("EnableContentBulkDownload");

        public static bool MockXandrResult => DynamicConfig.GetValue<bool>("MockXandrResult", false);

        public static bool EnableSubPlacementTargeting => DynamicConfig.GetValue("EnableSubPlacementTargeting", false);

        public static bool SkipUsedByInfo => DynamicConfig.GetRequiredValue<bool>("SkipUsedByInfo");
        public static bool BypassGoalValidationForExperimentCampaign => DynamicConfig.GetRequiredValue<bool>("BypassGoalValidationForExperimentCampaign");

        public static bool EnableGoalNullableProperties => DynamicConfig.GetRequiredValue<bool>("EnableGoalNullableProperties");

        public static bool EnableAutoGoalApiAndValidation => DynamicConfig.GetRequiredValue<bool>("EnableAutoGoalApiAndValidation");

        public static bool EnableLockForAddGoals => DynamicConfig.GetRequiredValue<bool>("EnableLockForAddGoals");

        public static bool ReportingFixPhase2 => DynamicConfig.GetRequiredValue<bool>("ReportingFixPhase2");

        public static int PercentageReportingFixPhase2 => DynamicConfig.GetValue<int>("PercentageReportingFixPhase2", 0);

        public static bool MockLockForAddGoals => DynamicConfig.GetRequiredValue<bool>("MockLockForAddGoals");

        public static bool EnableOOMFixInOfflineConversionReport => DynamicConfig.GetValue<bool>("EnableOOMFixInOfflineConversionReport", false);

        public static int LockForAddGoalsDuration => DynamicConfig.GetValue<int>("LockForAddGoalsDuration", 120);

        public static bool EnableUetDashboard => DynamicConfig.GetRequiredValue<bool>("EnableUetDashboard");

        public static string UetClickHouseUrl => DynamicConfig.GetRequiredValue<string>("UetClickHouseUrl");

        public static bool EnableClickHousePME => DynamicConfig.GetRequiredValue<bool>("EnableClickHousePME");

        public static string UetClickHouseUrlPME => DynamicConfig.GetRequiredValue<string>("UetClickHouseUrlPME");

        public static int ClickhousePortNumber => DynamicConfig.GetRequiredValue<int>("ClickhousePortNumber");
        public static string ClickhouseProtocol => DynamicConfig.GetRequiredValue<string>("ClickhouseProtocol");

        public static bool EnableDashboardSharedLibrary => DynamicConfig.GetRequiredValue<bool>("EnableDashboardSharedLibrary");

        public static bool EnableInStoreVisitConversion => DynamicConfig.GetRequiredValue<bool>("EnableInStoreVisitConversion");

        public static bool EnableOnlineConversion => DynamicConfig.GetRequiredValue<bool>("EnableOnlineConversion");

        public static bool ByPassTagValidationForAutoConv => DynamicConfig.GetRequiredValue<bool>("ByPassTagValidationForAutoConv");

        public static bool EnableOptimizationForGetTag => DynamicConfig.GetRequiredValue<bool>("EnableOptimizationForGetTag");
        public static bool EnableOptimizationForGetTagPhase2 => DynamicConfig.GetRequiredValue<bool>("EnableOptimizationForGetTagPhase2");

        public static bool EnableMistralTextGeneration => DynamicConfig.GetRequiredValue<bool>("EnableMistralTextGeneration");

        public static bool EnableBestPracticeThemeGeneration => DynamicConfig.GetRequiredValue<bool>("EnableBestPracticeThemeGeneration");

        public static bool EnableDKI => DynamicConfig.GetRequiredValue<bool>("EnableDKI");

        public static bool EnablePmaxDiagnoseV2ForCampaign => DynamicConfig.GetRequiredValue<bool>("EnablePmaxDiagnoseV2ForCampaign");

        public static bool EnableDiagnosticConversionStatusV2 => DynamicConfig.GetRequiredValue<bool>("EnableDiagnosticConversionStatusV2");
        public static bool OfflineConversionReportCompressStatus=> DynamicConfig.GetRequiredValue<bool>("OfflineConversionReportCompressStatus");

        public static bool EnableSponsoredProductAdsV2(CampaignManagementRequest request)
        {
            return GetRequiredValue<bool>("EnableSponsoredProductAdsV2", request.OverrideConfigValuesFromTest);
        }

        // Used by the dao which does not have access to the request
        public static bool EnableSponsoredProductAdsV2()
        {
            return DynamicConfig.GetRequiredValue<bool>("EnableSponsoredProductAdsV2");
        }

        public static bool EnableActiveRemarketingSimilarAudience
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableActiveRemarketingSimilarAudience"); }
        }

        public static bool EnablePrioritizeCampaignConversionGoal => DynamicConfig.GetValue("EnablePrioritizeCampaignConversionGoal", false);

        public static bool EnableSkipBiddingSchemeValidationWhenCopyPasteCampaign => DynamicConfig.GetValue("EnableSkipBiddingSchemeValidationWhenCopyPasteCampaign", false);

        public static bool EnableSimilarAudienceGoogleImportDeprecation
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableSimilarAudienceGoogleImportDeprecation"); }
        }

        public static bool EnableKeywordTargeting => DynamicConfig.GetValue("EnableKeywordTargeting", false);
        public static bool EnableUrlRegexValidation => DynamicConfig.GetValue("EnableUrlRegexValidation", false);

        public static bool CustomSegmentEditorialIntegration => DynamicConfig.GetValue("CustomSegmentEditorialIntegration", false);

        public static bool OfflineConversionNeedCheckTwoHoursDelay
        {
            get { return DynamicConfig.GetRequiredValue<bool>("OfflineConversionNeedCheckTwoHoursDelay"); }
        }

        public static bool ReduceBulkCallInOfflineConversion => DynamicConfig.GetValue("ReduceBulkCallInOfflineConversion", false);
        
        public static bool EnableCampaignConversionGoalBiddingSchemeCheck => DynamicConfig.GetValue<bool>("EnableCampaignConversionGoalBiddingSchemeCheck", false);

        public static bool OfflineConversionReportEnabled
        {
            get { return DynamicConfig.GetRequiredValue<bool>("OfflineConversionReportEnabled"); }
        }

        public static int PercentageOfOfflineConversionReportPilot
        {
            get { return DynamicConfig.GetRequiredValue<int>("PercentageOfOfflineConversionReportPilot"); }
        }

        public static string OfflineConversionReportV2Container
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionReportV2Container"); }
        }

        public static bool EnableOptimizedImportCampaignNameMapping
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableOptimizedImportCampaignNameMapping"); }
        }

        public static HashSet<long> OptimizedImportCampaignNameMappingAccountWhitelist
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("OptimizedImportCampaignNameMappingAccountWhitelist", EmptyLongList)); }
        }

        public static HashSet<long> OptimizedImportCampaignNameMappingCustomerExclusionList
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("OptimizedImportCampaignNameMappingCustomerExclusionList", EmptyLongList)); }
        }

        public static HashSet<long> SkipNoGoalAccountList
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("SkipNoGoalAccountList", EmptyLongList)); }
        }

        public static HashSet<long> SkipImageAdExtensionAccountWhitelist
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("SkipImageAdExtensionAccountWhitelist", EmptyLongList)); }
        }

        public static HashSet<long> SkipImageAdExtensionCustomerExclusionList
        {
            get { return new HashSet<long>(DynamicConfig.GetValue("SkipImageAdExtensionCustomerExclusionList", EmptyLongList)); }
        }

        public static int AccountBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("AccountBatchSize"); }
        }

        public static bool EnableClientCenterEventHandler
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableClientCenterEventHandler"); }
        }

        public static bool EnableMIConnectToClientCenterEvent
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableMIConnectToClientCenterEvent"); }
        }

        public static string ClientCenterHostName
        {
            get { return DynamicConfig.GetRequiredValue<string>("ClientCenterHostName"); }
        }

        public static bool EnableBudgetSyncMessageHandler
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableBudgetSyncMessageHandler"); }
        }

        public static bool EnableUsedByCountOfflineTask
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableUsedByCountOfflineTask"); }
        }

        public static string CustomerMatchFileUploadTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("CustomerMatchFileUploadTaskTypeNameForTaskEngine"); }
        }

        public static string CustomerMatchUploadStagingFolderPathForBulkFile
        {
            get { return DynamicConfig.GetRequiredValue<string>("CustomerMatchUploadStagingFolderPathForBulkFile"); }
        }

        public static int MaxRetryCountForCreateCustomerMatchUploadTask
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxRetryCountForCreateCustomerMatchUploadTask"); }
        }

        public static int SleepIntervalInMillSecondsForCreateCustomerMatchUploadTaskRetry
        {
            get { return DynamicConfig.GetRequiredValue<int>("SleepIntervalInMillSecondsForCreateCustomerMatchUploadTaskRetry"); }
        }

        public static int MaxRetryCountForCreateOfflineGeoCodingTask
        {
            get { return DynamicConfig.GetValue<int>("MaxRetryCountForCreateOfflineGeoCodingTask", 3); }
        }

        public static int SleepIntervalInMillSecondsForCreateOfflineGeoCodingTaskRetry
        {
            get { return DynamicConfig.GetValue<int>("SleepIntervalInMillSecondsForCreateOfflineGeoCodingTaskRetry", 1000); }
        }

        public static int SleepIntervalInMillSecondsForACSTestCallDuration
        {
            get { return DynamicConfig.GetValue<int>("SleepIntervalInMillSecondsForACSTestCallDuration", 5000); }
        }

        public static bool EnableCombinedListBulkUpload
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableCombinedListBulkUpload"); }
        }

        public static string EURestrictionCountryList
        {
            get { return DynamicConfig.GetRequiredValue<string>("EURestrictionCountryList"); }
        }

        public static bool UpdateSegPackOptimizationImportedFromGoogle
        {
            get { return DynamicConfig.GetRequiredValue<bool>("UpdateSegPackOptimizationImportedFromGoogle"); }
        }

        public static TimeSpan FileCacheReadTimeout => DynamicConfig.GetValue<TimeSpan>("FileCacheReadTimeout", TimeSpan.FromSeconds(5));

        public static IReadOnlyDictionary<int, int> ImportCampaignLocationCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportCampaignLocationCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static IReadOnlyDictionary<int, int> ImportCampaignRadiusCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportCampaignRadiusCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static IReadOnlyDictionary<int, int> ImportCampaignAudienceCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportCampaignAudienceCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static IReadOnlyDictionary<int, int> ImportAdGroupLocationCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportAdGroupLocationCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static IReadOnlyDictionary<int, int> ImportAdGroupRadiusCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportAdGroupRadiusCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static IReadOnlyDictionary<int, int> ImportAdGroupAudienceCriterionLoaderParentBatchSizeByAccountId
        {
            get
            {
                return
                    GetImportTargetCriterionLoaderParentBatchSizeByAccountId(
                        "ImportAdGroupAudienceCriterionLoaderParentBatchSizeByAccountId");
            }
        }

        public static int ImportDefaultParentCampaignRowNumberBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultParentCampaignRowNumberBatchSize", 200);
            }
        }

        public static int ImportDefaultParentAdGroupRowNumberBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultParentAdGroupRowNumberBatchSize", 200);
            }
        }

        public static int ImportDefaultWriteCsvBlockingCollectionSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultWriteCsvBlockingCollectionSize", 10000);
            }
        }

        public static int ImportDefaultProducerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultProducerCount", 1);
            }
        }

        public static int ImportEntityFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportEntityFetchBatchSize", 1000);
            }
        }

        public static int ImportAdExtensionFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdExtensionFetchBatchSize", 1000);
            }
        }

        public static int ImportAdExtensionAssociationFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdExtensionAssociationFetchBatchSize", 1000);
            }
        }

        public static int ImportKeywordFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportKeywordFetchBatchSize", 3000);
            }
        }

        public static int ImportKeywordProducerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportKeywordProducerCount", 2);
            }
        }

        public static int ImportWriteKeywordCsvBlockingCollectionSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportWriteKeywordCsvBlockingCollectionSize", 6000);
            }
        }

        public static int ImportKeywordParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportKeywordParsingBlockingCollectionConsumerCount", 3);
            }
        }

        public static int ImportAdParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdParsingBlockingCollectionConsumerCount", 3);
            }
        }

        public static int ImportParserBatchSizeForProductPartition
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportParserBatchSizeForProductPartition", 2000);
            }
        }

        public static int ImportProductPartitionParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportProductPartitionParsingBlockingCollectionConsumerCount", 3);
            }
        }

        public static int ImportNegativeKeywordParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeKeywordParsingBlockingCollectionConsumerCount", 3);
            }
        }

        public static int ImportNegativeKeywordParentAdGroupRowNumberBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeKeywordParentAdGroupRowNumberBatchSize", 100);
            }
        }

        public static int ImportNegativeKeywordParentCampaignRowNumberBatchSize
        {
            get { return DynamicConfig.GetValue("ImportNegativeKeywordParentCampaignRowNumberBatchSize", 3); }
        }

        public static int ImportNegativeKeywordCampaignDBFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeKeywordCampaignDBFetchBatchSize", 100000);
            }
        }

        public static int ImportParserBatchSizeForNegativeKeywords
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportParserBatchSizeForNegativeKeywords"); }
        }

        public static int ImportAdExtensionParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdExtensionParsingBlockingCollectionConsumerCount", 1);
            }
        }

        public static int ImportParserBatchSizeForNegativeSites
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ImportParserBatchSizeForNegativeSites");
            }
        }
        public static int ImportNegativeSiteParentAdGroupRowNumberBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeSiteParentAdGroupRowNumberBatchSize", 200);
            }
        }
        public static int ImportNegativeSiteParentCampaignRowNumberBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeSiteParentCampaignRowNumberBatchSize", 200);
            }
        }
        public static int ImportNegativeSiteFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeSiteFetchBatchSize", 4000);
            }
        }

        public static int ImportDsaCriterionFetchBatchSize => DynamicConfig.GetValue("ImportDsaCriterionFetchBatchSize", 200);

        public static int ImportCampaignNegativeWebpageFetchBatchSize => DynamicConfig.GetValue("ImportCampaignNegativeWebpageFetchBatchSize", 200);

        public static int ImportNegativeSiteProducerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportNegativeSiteProducerCount", 2);
            }
        }
        public static int ImportWriteNegativeSiteCsvBlockingCollectionSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportWriteNegativeSiteCsvBlockingCollectionSize", 16000);
            }
        }

        public static int ImportDefaultParsingBlockingCollectionSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultParsingBlockingCollectionSize", 15);
            }
        }

        public static int ImportDefaultParsingBlockingCollectionConsumerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportDefaultParsingBlockingCollectionConsumerCount", 3);
            }
        }

        public static int ImportUpdateKeywordTaskQueueCapacity
        {
            get { return DynamicConfig.GetValue("ImportUpdateKeywordTaskQueueCapacity", 15); }
        }

        public static int ImportUpdateKeywordTaskQueueWorkerCount
        {
            get { return DynamicConfig.GetValue("ImportUpdateKeywordTaskQueueWorkerCount", 3); }
        }

        public static int ImportKeywordParentAdGroupRowNumberBatchSize
        {
            get { return DynamicConfig.GetValue("ImportKeywordParentAdGroupRowNumberBatchSize", 100); }
        }

        public static int ImportAdFetchBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdFetchBatchSize", 3000);
            }
        }

        public static int ImportAdProducerCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportAdProducerCount", 1);
            }
        }

        public static int ImportWriteAdCsvBlockingCollectionSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("ImportWriteAdCsvBlockingCollectionSize", 6000);
            }
        }

        public static int ImportAdParentAdGroupRowNumberBatchSize
        {
            get { return DynamicConfig.GetValue("ImportAdParentAdGroupRowNumberBatchSize", 100); }
        }


        public static int AggregatorCacheReadThrottleWaitTimeInMs
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AggregatorCacheReadThrottleWaitTimeInMs");
            }
        }

        public static bool AggregatorCacheReadThrottleEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("AggregatorCacheReadThrottleEnabled");
            }
        }

        public static int AggregatorCacheWriteThrottleWaitTimeInMs
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AggregatorCacheWriteThrottleWaitTimeInMs");
            }
        }

        public static bool AggregatorCacheWriteThrottleEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("AggregatorCacheWriteThrottleEnabled");
            }
        }

        public static string AggregatorRedisCacheEntryPoint
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("AggregatorRedisCacheEntryPoint");
            }
        }

        public static string AggregatorRedisCacheSecretKey
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("AggregatorRedisCacheSecretKey");
            }
        }

        public static int AggregatorRedisCachePort
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AggregatorRedisCachePort");
            }
        }

        public static int AggregatorRedisCacheConnectTimeoutInMS
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AggregatorRedisCacheConnectTimeoutInMS");
            }
        }

        public static int AggregatorRedisCacheSyncTimeoutInMS
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AggregatorRedisCacheSyncTimeoutInMS");
            }
        }

        public static int AggregatorRedisCacheConnectionCount
        {
            get
            {
                return DynamicConfig.GetValue<int>("AggregatorRedisCacheConnectionCount", 1);
            }
        }

        public static string BingAdsCampaignMTRedisCacheKey
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("BingAdsCampaignMTRedisCacheKey");
            }
        }

        public static string BingAdsCampaignMTRedisCacheEntryPoint
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("BingAdsCampaignMTRedisCacheEntryPoint");
            }
        }

        public static string BingAdsCampaignTextAssetRedisCacheKey
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("BingAdsCampaignTextAssetRedisCacheKey");
            }
        }

        public static int BingAdsCampaignMTRedisCachePort
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BingAdsCampaignMTRedisCachePort");
            }
        }

        public static int BingAdsCampaignMTRedisConnectTimeoutInMS
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BingAdsCampaignMTRedisConnectTimeoutInMS");
            }
        }

        public static int BingAdsCampaignMTRedisTimeoutInMS
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BingAdsCampaignMTRedisTimeoutInMS");
            }
        }

        public static IEnumerable<string> InlineReportHeaders
        {
            get
            {
                var headers = DynamicConfig.GetValue("InlineReportHeaders", new ReadOnlyCollection<string>(new List<string>()));
                var list = new List<string>();
                headers.ForEach(s => list.Add(s.Trim().ToLowerInvariant()));
                return list;
            }
        }

        public static HashSet<string> InlineReportSummaryRowHeaders
        {
            get
            {
                var headers = DynamicConfig.GetValue("InlineReportSummaryRowHeaders", new ReadOnlyCollection<string>(new List<string>()));
                var set = new HashSet<string>();
                headers.ForEach(s => set.Add(s.Trim().ToLowerInvariant()));
                return set;
            }
        }

        public static IEnumerable<string> BaeExportFileNonEntityRowTypes
        {
            get
            {
                var rowTypes = DynamicConfig.GetValue("BaeExportFileNonEntityRowTypes",
                    new ReadOnlyCollection<string>(new List<string>()));
                var list = new List<string>();
                rowTypes.ForEach(t => list.Add(t.Trim().ToLowerInvariant()));
                return list;
            }
        }

        public static int AdsShardedFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdsShardedFanOutMaxSize");
            }
        }

        public static int AssetsShardedFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AssetsShardedFanOutMaxSize");
            }
        }

        public static int AdsNetworkOptimizationThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("AdsNetworkOptimizationThreshold", 100000);
            }
        }

        public static bool EnableCountryCodeInOfferFilters
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableCountryCodeInOfferFilters");
            }
        }

        public static int AdGroupsShardedFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdGroupsShardedFanOutMaxSize");
            }
        }

        public static int AdExtensionsFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdExtensionsFanOutMaxSize");
            }
        }

        public static int KeywordsShardedFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("KeywordsShardedFanOutMaxSize");
            }
        }

        public static int SearchTermGridMaxCount
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("SearchTermGridMaxCount");
            }
        }

        public static int CampaignAllSettingsTargetCriterionParallelFetchMaxThread
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("CampaignAllSettingsTargetCriterionParallelFetchMaxThread");
            }
        }

        public static int TargetCriterionBulkEditUpdateMaxThread
        {
            get { return DynamicConfig.GetRequiredValue<int>("TargetCriterionBulkEditUpdateMaxThread"); }
        }

        public static int TargetCriterionParallelFetchBaseSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("TargetCriterionParallelFetchBaseSize");
            }
        }

        public static int TargetCriterionParallelFetchMaxThread
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("TargetCriterionParallelFetchMaxThread");
            }
        }

        public static int DeviceTargetCriterionParallelFetchBaseSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("DeviceTargetCriterionParallelFetchBaseSize");
            }
        }

        public static int DeviceTargetCriterionParallelFetchMaxThread
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("DeviceTargetCriterionParallelFetchMaxThread");
            }
        }

        public static int ProductGroupParallelFetchBaseSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ProductGroupParallelFetchBaseSize");
            }
        }
        public static int HotelListingGroupParallelFetchBaseSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("HotelListingGroupParallelFetchBaseSize");
            }
        }
        public static int ProductGroupParallelFetchMaxThread
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ProductGroupParallelFetchMaxThread");
            }
        }

        public static int HotelListingGroupParallelFetchMaxThread
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("HotelListingGroupParallelFetchMaxThread");
            }
        }
        

        public static int ProductGroupLoadByIDLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ProductGroupLoadByIDLimit");
            }
        }
        public static int HotelListingGroupLoadByIDLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("HotelListingLoadByIDLimit");
            }
        }

        public static int TargetCriterionLocalFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("TargetCriterionLocalFanOutMaxSize");
            }
        }

        public static bool EnableLocalFanout
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableLocalFanout", false);
            }
        }

        public static int LocalFanoutThreshold
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LocalFanoutThreshold");
            }
        }

        public static int AudienceCampaignAssociationLocalFanoutThreshold
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AudienceCampaignAssociationLocalFanoutThreshold");
            }
        }

        public static int ProductGroupLocalFanoutThreshold
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>(nameof(ProductGroupLocalFanoutThreshold));
            }
        }

        public static bool EnableSearchTermStatusVerboseLogging
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableSearchTermStatusVerboseLogging", false);
            }
        }

        public static int SearchTermsOverviewTakeCountMultiplier
        {
            get
            {
                return DynamicConfig.GetValue<int>("SearchTermsOverviewTakeCountMultiplier", 10);
            }
        }

        public static bool CheckKeywordLimitForSearchTerm
        {
            get
            {
                return DynamicConfig.GetValue<bool>("CheckKeywordLimitForSearchTerm", false);
            }
        }

        public static int SearchTermCommonNegativeKeywordLimit
        {
            get
            {
                return DynamicConfig.GetValue<int>("SearchTermCommonNegativeKeywordLimit", 250000);
            }
        }

        public static int SearchTermAdGroupNegativeKeywordLimit
        {
            get
            {
                return DynamicConfig.GetValue<int>("SearchTermAdGroupNegativeKeywordLimit", 8000000);
            }
        }

        // Percent of extra objects allocated in a data set initially.
        public static int PercentExtraAllocationInFanout
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("PercentExtraAllocationInFanout");
            }
        }

        public static int CampaignParallelSubShardingMaxIncrease
        {
            get
            {
                return DynamicConfig.GetValue<int>("CampaignParallelSubShardingMaxIncrease", 2);
            }
        }

        public static int KeywordsBIParallelSubShardingMaxIncrease
        {
            get
            {
                return DynamicConfig.GetValue<int>("KeywordsBIParallelSubShardingMaxIncrease", 1);
            }
        }

        public static long CampaignParallelSubShardingEntityCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<long>("CampaignParallelSubShardingEntityCountThreshold", 150000);
            }
        }

        public static long KeywordsBIParallelSubShardingEntityCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<long>("KeywordsBIParallelSubShardingEntityCountThreshold", 100000);
            }
        }


        public static int PerformanceTargetProjectedDataAvailableDays
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("PerformanceTargetProjectedDataAvailableDays");
            }
        }

        public static int GeocodeRadiusTargetTimeoutInSecondsForWebUI
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetTimeoutInSecondsForWebUI");
            }
        }

        public static int GeocodeRadiusTargetTimeoutInSecondsForAPI
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetTimeoutInSecondsForAPI");
            }
        }

        public static int GeocodeRadiusTargetTimeoutInSecondsForBulk
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetTimeoutInSecondsForBulk");
            }
        }

        public static int GeocodeRadiusTargetTimeoutInSecondsForOfflineTask
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetTimeoutInSecondsForOfflineTask");
            }
        }

        public static int GeocodeRadiusTargetBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetBatchSize");
            }
        }

        public static int GeocodeRadiusTargetBatchSizeForOfflineTask
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetBatchSizeForOfflineTask");

            }
        }

        public static int GeocodeRadiusTargetMaxRetries
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GeocodeRadiusTargetMaxRetries");
            }
        }

        public static int AsyncReverseGeocodeRadiusTargetThreshold
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AsyncReverseGeocodeRadiusTargetThreshold");
            }
        }

        public static bool AsyncReverseGeocodeQueueOnFailure
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("AsyncReverseGeocodeQueueOnFailure");
            }
        }

        public static string ReverseGeoCodeRadiusTargetTaskTypeNameForTaskEngine
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("ReverseGeoCodeRadiusTargetTaskTypeNameForTaskEngine");
            }
        }
        public static string MicrosoftMerchantCenterAPIUrl
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("MicrosoftMerchantCenterAPIUrl");
            }
        }

        public static string MicrosoftMerchantCenterPartnerId => DynamicConfig.GetRequiredValue<string>("MicrosoftMerchantCenterPartnerId");

        public static int MMCRetryCount => DynamicConfig.GetRequiredValue<int>("MMCRetryCount");

        public static int MMCRetryDelay => DynamicConfig.GetRequiredValue<int>("MMCRetryDelay");

        public static string MicrosoftMerchantCenterBypassAuthToken => DynamicConfig.GetValue(nameof(MicrosoftMerchantCenterBypassAuthToken), string.Empty);

        public static int MicrosoftMerchantCenterBmcProviderNewAPIPilot => DynamicConfig.GetValue(nameof(MicrosoftMerchantCenterBmcProviderNewAPIPilot), 0);

        public static string AccountReparentingTaskTypeNameForTaskEngine
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("AccountReparentingTaskTypeNameForRuleEngine");
            }
        }

        public static int MaxRetryCountForCreateAccountReparentingTask
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxRetryCountForCreateAccountReparentingTask");
            }
        }

        public static int SleepIntervalInMillSecondsForCreateAccountReparentingTaskRetry
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("SleepIntervalInMillSecondsForCreateAccountReparentingTaskRetry");
            }
        }

        public static bool IsInternalUserOrchestrateReparenting
        {
            get
            {
                return DynamicConfig.GetValue<bool>("IsInternalUserOrchestrateReparenting", false);
            }
        }

        public static bool EnableAccountReparentingRetry
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableAccountReparentingRetry", true);
            }
        }

        public static int LargeKeywordSetSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LargeKeywordSetSize");
            }
        }

        public static int MaxAdRowCountForUsingFileCache
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxAdRowCountForUsingFileCache");
            }
        }

        public static int MaxAssetRowCountForUsingFileCache
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxAssetRowCountForUsingFileCache");
            }
        }

        public static int OfferDbPartitionInfoCacheAgeInMinutes
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("OfferDbPartitionInfoCacheAgeInMinutes");
            }
        }

        public static byte OfferDbAccessDataFragmentCount
        {
            get
            {
                return (byte)DynamicConfig.GetRequiredValue<int>("OfferDbAccessDataFragmentCount");
            }
        }

        public static byte BSCMatchedOfferCountThreadLimit
        {
            get
            {
                return (byte)DynamicConfig.GetRequiredValue<int>("BSCMatchedOfferCountThreadLimit");
            }
        }

        // Maximum number of requests we'll send to a single fanout node
        public static int MaxFanoutRequestsPerNode
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxFanoutRequestsPerNode");
            }
        }

        public static int ImportUpdateKeywordBatchSizeToDisk
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportUpdateKeywordBatchSizeToDisk"); }
        }

        public static bool EnableFraudAdsEvaluation
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableFraudAdsEvaluation", false);
            }
        }

        public static bool GetEnableFraudAdsEvaluation(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableFraudAdsEvaluation", request.OverrideConfigValuesFromTest, false);
        }

        public static bool EnableFraudCampaignsEvaluation
        {
            get { return DynamicConfig.GetValue<bool>("EnableFraudCampaignsEvaluation", false); }
        }

        public static int MaxAdsBatchSizeToFraudCheck
        {
            get
            {
                return DynamicConfig.GetValue<int>("MaxAdsBatchSizeToFraudCheck", 1000);
            }
        }

        public static int PercentOfAccountsEnableFraudCampaignsEvaluation
        {
            get { return DynamicConfig.GetValue<int>("PercentOfAccountsEnableFraudCampaignsEvaluation", 0); }
        }

        public static bool EnablePassAdGroupIdWhenCreateAppeal
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnablePassAdGroupIdWhenCreateAppeal", false);
            }
        }


        public static string GoogleImportLocationMappingFilePath => DynamicConfig.GetRequiredValue<string>("GoogleImportLocationMappingFilePath");

        public static string GeoLocationMappingFileVersion => DynamicConfig.GetRequiredValue<string>("GeoLocationMappingFileVersion");

        public static bool EnableNewAdWordsLocationFile => DynamicConfig.GetRequiredValue<bool>("EnableNewAdWordsLocationFile");

        public static int DownloadImportFileFromAzureTimeoutMs => DynamicConfig.GetValue<int>("DownloadImportFileFromAzureTimeoutMs", 0);

        public static int DownloadPharmaCustomerIdsFromAzureTimeoutMs => DynamicConfig.GetValue<int>("DownloadPharmaCustomerIdsFromAzureTimeoutMs", 0);

        public static int DownloadGeoLocationsFromAzureTimeoutMs => DynamicConfig.GetRequiredValue<int>("DownloadGeoLocationsFromAzureTimeoutMs");


        public static bool DownloadDSAMixedModeImportFixFileFromAzure => DynamicConfig.GetRequiredValue<bool>("DownloadDSAMixedModeImportFixFileFromAzure");

        public static int DSAMixedModeImportFixMaxAdGroupCount => DynamicConfig.GetRequiredValue<int>("DSAMixedModeImportFixMaxAdGroupCount");

        public static int MaxAssetCountSupportingFilterAndSorting => DynamicConfig.GetRequiredValue<int>("MaxAssetCountSupportingFilterAndSorting");

        public static int BulkUploadSessionExpirationTimeOutInMinutes
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadSessionExpirationTimeOutInMinutes");
            }
        }

        public static int BSCProductOffersProcessingLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BSCProductOffersProcessingLimit");
            }
        }

        public static int BscSpaOffersProcessingLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BscSpaOffersProcessingLimit");
            }
        }

        public static Microsoft.AdCenter.Shared.MT.ConfigHelper ConfigHelper
        {
            get
            {
                return new Microsoft.AdCenter.Shared.MT.ConfigHelper(DynamicConfig);
            }
        }

        public static Microsoft.AdCenter.Shared.MT.ConfigHelper ConfigHelperWithTestOverride(CampaignManagementRequest request)
        {
            return new Microsoft.AdCenter.Shared.MT.ConfigHelper(DynamicConfig, request?.OverrideConfigValuesFromTest);
        }

        public static bool RemarketingBulkEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("RemarketingBulkEnabled");
            }
        }

        public static bool EnableAudienceBulkDownload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableAudienceBulkDownload");
            }
        }

        public static bool EnableDSABulkUpload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableDSABulkUpload");
            }
        }

        public static int ProductGroupsShardedFanOutMaxSize =>
           DynamicConfig.GetValue<int>("ProductGroupsShardedFanOutMaxSize", 200000);

        public static bool ShouldHotelAdsCallAggregator => DynamicConfig.GetValue("ShouldHotelAdsCallAggregator", false);

        public static bool EnableEditorialErrorFieldMapping => DynamicConfig.GetRequiredValue<bool>("EnableEditorialErrorFieldMapping");

        public static bool EnablePPSDaoMigration => DynamicConfig.GetValue("EnablePPSDaoMigration", false);

        public static bool EnableMeteredCallExtensionScheduling => DynamicConfig.GetValue("EnableMeteredCallExtensionScheduling", false);

        public static bool EnableProductPartitionBidBoost => DynamicConfig.GetValue("EnableProductPartitionBidBoost", false);

        public static bool EnableGMBUploadForAll => DynamicConfig.GetValue("EnableGMBUploadForAll", false);

        public static bool UseBSCCacheForGetProductPartitionAttributes => DynamicConfig.GetValue("UseBSCCacheForGetProductPartitionAttributes", false);

        public static byte BSCProductAttributeStatisticsThreadLimit => DynamicConfig.GetValue<byte>("BSCProductAttributeStatisticsThreadLimit", 1);

        public static int MaxCampaignCountToBulkLoadCampaignCriterions => DynamicConfig.GetRequiredValue<int>("MaxCampaignCountToBulkLoadCampaignCriterions");

        public static int CriterionTargetsConcurrentTasks
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("CriterionTargetsConcurrentTasks");
            }
        }

        public static int DsaTargetsFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("DsaTargetsFanOutMaxSize");
            }
        }

        public static int DsaSearchTermsFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("DsaSearchTermsFanOutMaxSize");
            }
        }

        public static int DsaDsaCategorysFanOutMaxSize => DynamicConfig.GetRequiredValue<int>("DsaDsaCategorysFanOutMaxSize");

        public static bool EnableDSATargetDeliveryStatus => DynamicConfig.GetValue("EnableDSATargetDeliveryStatus", false);

        public static int LogAbnormalDeliveryStatusWarningAutoTargetNumber => DynamicConfig.GetValue("LogAbnormalDeliveryStatusWarningAutoTargetNumber", 0);

        public static bool EnableDSATargetProcessingProgress => DynamicConfig.GetValue("EnableDSATargetProcessingProgress", false);

        public static bool BSCEnableInternationalCampaign
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("BSCEnableInternationalCampaign");
            }
        }

        public static bool EnableBSCBulkUpload
        {
            get
            {
                return DynamicConfig.GetValue("EnableBSCBulkUpload", false);
            }
        }

        public static bool EnableExpertModeCampaignWorkFlow
        {
            get
            {
                return DynamicConfig.GetValue("EnableExpertModeCampaignWorkFlow", false);
            }
        }

        public static bool EnableClarityTagIntegration
        {
            get
            {
                return DynamicConfig.GetValue("EnableClarityTagIntegration", false);
            }
        }

        public static bool EnableRepresentativeSearchPhrases
        {
            get
            {
                return DynamicConfig.GetValue("EnableRepresentativeSearchPhrases", false);
            }
        }

        public static int BulkUploadPercentagePostValidationCheckToAsyncFlow
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadPercentagePostValidationCheckToAsyncFlow");
            }
        }

        public static bool BIFilterLoadAccountBased
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("BIFilterLoadAccountBased");
            }
        }

        public static bool EnableMultiAccountUploadServicePolling
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableMultiAccountUploadServicePolling");
            }
        }

        public static bool EnableCustomerLevelThrotlingForBulkUpload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableCustomerLevelThrotlingForBulkUpload");
            }
        }

        public static bool EnableCustomerLevelThrotlingForOfflineConversionUpload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableCustomerLevelThrotlingForOfflineConversionUpload");
            }
        }

        public static byte TaskQueuingAndInlineUpdateMode
        {
            get
            {
                return (byte)DynamicConfig.GetValue("TaskQueuingAndInlineUpdateMode", 2);
            }
        }

        public static int GoogleSyncRetryPolicyMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncRetryPolicy")[MaxCount]);
            }
        }

        public static int GoogleSyncAPIRetryPolicyMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncAPIRetryPolicy")[MaxCount]);
            }
        }

        public static int GoogleSyncRetryResourceExhaustedPolicyMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncRetryResourceExhaustedPolicy")[MaxCount]);
            }
        }

        public static int GoogleSyncRetryPolicyInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncRetryPolicy")[InitialInterval]);
            }
        }

        public static int GoogleSyncAPIRetryPolicyInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncAPIRetryPolicy")[InitialInterval]);
            }
        }

        public static int GoogleSyncRetryPolicyIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncRetryPolicy")[Increment]);
            }
        }

        public static int GoogleSyncAPIRetryPolicyIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncAPIRetryPolicy")[Increment]);
            }
        }

        public static int GoogleSyncRetryResourceExhaustedPolicyIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("GoogleSyncRetryResourceExhaustedPolicy")[Increment]);
            }
        }

        public static int PinterestSyncAPIRetryMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("PinterestSyncAPIRetryPolicy")[MaxCount]);
            }
        }

        public static int PinterestSyncAPIRetryInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("PinterestSyncAPIRetryPolicy")[InitialInterval]);
            }
        }

        public static int PinterestSyncAPIRetryIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("PinterestSyncAPIRetryPolicy")[Increment]);
            }
        }

        public static int PinterestSyncAPIMaxInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("PinterestSyncAPIRetryPolicy")[MaxInterval]);
            }
        }

        public static int FacebookSyncAPIRetryMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookSyncAPIRetryPolicy")[MaxCount]);
            }
        }

        public static int FacebookSyncAPIRetryInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookSyncAPIRetryPolicy")[InitialInterval]);
            }
        }

        public static int FacebookSyncAPIRetryIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookSyncAPIRetryPolicy")[Increment]);
            }
        }

        public static int FacebookSyncAPIPollMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookAPIPollPolicy")[MaxCount]);
            }
        }

        public static int FacebookSyncAPIPollInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookAPIPollPolicy")[InitialInterval]);
            }
        }

        public static int FacebookSyncAPIPollIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookAPIPollPolicy")[Increment]);
            }
        }

        public static int FacebookSyncAPIPollMaxInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("FacebookAPIPollPolicy")[MaxInterval]);
            }
        }

        public static int AmazonSyncAPIRetryMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("AmazonSyncAPIRetryPolicy")[MaxCount]);
            }
        }

        public static int AmazonSyncAPIRetryInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("AmazonSyncAPIRetryPolicy")[InitialInterval]);
            }
        }

        public static int AmazonSyncAPIRetryIncrement
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("AmazonSyncAPIRetryPolicy")[Increment]);
            }
        }

        public static int BingPlacesRetryMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("BingPlacesRetryPolicy")[MaxCount]);
            }
        }

        public static int BingPlacesRetryInitialInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("BingPlacesRetryPolicy")[InitialInterval]);
            }
        }

        public static int BingPlacesMaxInterval
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("BingPlacesRetryPolicy")[MaxInterval]);
            }
        }

        public static int AZImportSyncCentricRetryPolicyMaxCount
        {
            get
            {
                return Convert.ToInt32(DynamicConfig.GetMapValues("AZImportSyncCentricRetryPolicy")[MaxCount]);
            }
        }

        public static bool RemoveBMMInKeywordTextSearch
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("RemoveBMMInKeywordTextSearch");
            }
        }

        public static int MaxKeywordsToCheckExistence
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxKeywordsToCheckExistence");
            }
        }

        public static bool ProductPartitionBIEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("ProductPartitionBIEnabled");
            }
        }

        public static bool AdGroupAudienceCriterionBIEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("AdGroupAudienceCriterionBIEnabled");
            }
        }

        public static int MaxConcurrentTasksForProductPartitionBulkDownload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxConcurrentTasksForProductPartitionBulkDownload");
            }
        }

        public static int BulkUploadMaxWriteThreadsForProductPartition
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxWriteThreadsForProductPartition");
            }
        }

        public static int BulkUploadMaxWriteThreadsForAssetGroupListingGroup
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxWriteThreadsForAssetGroupListingGroup");
            }
        }

        public static int BulkUploadMaxWriteThreadsForHotelListingGroup
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxWriteThreadsForHotelListingGroup");
            }
        }

        public static int NonHierarchyHotelListingGroupDownloadBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("NonHierarchyHotelListingGroupDownloadBatchSize");
            }
        }

        public static int NonHierarchyAssetGroupListingGroupDownloadBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("NonHierarchyAssetGroupListingGroupDownloadBatchSize");
            }
        }

        public static int HotelListingGroupDownloadQueueSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("HotelListingGroupDownloadQueueSize");
            }
        }

        public static int OfferPropertiesPilotFlags
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("OfferPropertiesPilotFlags");
            }
        }

        public static IReadOnlyDictionary<long, long> MaxAudiencesPerAccountByCustomerId
        {
            get
            {
                return GetEntityLimitDictionary("MaxAudiencesPerAccountByCustomerId");
            }
        }

        public static IReadOnlyDictionary<long, long> MaxAudiencesPerCustomer
        {
            get
            {
                return GetEntityLimitDictionary("MaxAudiencesPerCustomer");
            }
        }

        public static int UetTagLimitPerCustomer
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("UetTagLimitPerCustomer");
            }
        }

        public static int ConversionGoalLimitPerAccount
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ConversionGoalLimitPerAccount");
            }
        }

        public static int ConversionGoalLimitPerCustomer
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ConversionGoalLimitPerCustomer");
            }
        }

        public static IReadOnlyDictionary<long, long> ConversionGoalCustomerLimitOverride
        {
            get
            {
                return GetEntityLimitDictionary("ConversionGoalCustomerLimitOverride");
            }
        }

        public static bool EnableCampaignLevelGoalValidator => DynamicConfig.GetValue("EnableCampaignLevelGoalValidator", false);


        public static int AudienceAssociationsFanOutMaxSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AudienceAssociationsFanOutMaxSize");
            }
        }

        public static ReadOnlyCollection<long> BscCustomerBlockList
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("BscCustomerBlockList", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static ReadOnlyCollection<long> OfflineConversionReportLargeFileCustomerBlockList
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("OfflineConversionReportLargeFileCustomerBlockList", new ReadOnlyCollection<long>(new List<long>()));
            }
        }


        public static ReadOnlyCollection<long> GMBUploadPilotCustomerIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("GMBUploadPilotCustomerIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static ReadOnlyCollection<string> DbTypesToPing
        {
            get
            {
                return DynamicConfig.GetValue("PartitionedDbTypesToPing", new ReadOnlyCollection<string>(new List<string>()));
            }
        }

        public static int MaxDegreeOfParallelismForEvtCalls
        {
            get
            {
                // Maintains existing behavior if Dynamic.config value is missing
                return DynamicConfig.GetValue("MaxDegreeOfParallelismForEvtCalls", -1);
            }
        }

        public static bool EnableAppInstallAds
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableAppInstallAds");
            }
        }

        public static bool EnableAppInstallAdInlineUrlValidation
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableAppInstallAdInlineUrlValidation");
            }
        }

        public static bool EnableAppInstallAdAsyncUrlValidation
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableAppInstallAdAsyncUrlValidation");
            }
        }

        public static int AppInstallAdAsyncUrlValidationMaxThreads
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AppInstallAdAsyncUrlValidationMaxThreads");
            }
        }

        public static int AppInstallAdUrlValidationTimeoutInMilliseconds
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AppInstallAdUrlValidationTimeoutInMilliseconds");
            }
        }

        public static string TagPluginFileUrlContainerName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("TagPluginFileUrlContainerName");
            }
        }

        public static string GeoLocationsFileUrlValidVersions
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("GeoLocationsFileUrlValidVersions");
            }
        }

        public static string GeoLocationsFileUrlValidLanguageLocales
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("GeoLocationsFileUrlValidLanguageLocales");
            }
        }

        public static string GeoLocationsFileUrlContainerName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("GeoLocationsFileUrlContainerName");
            }
        }

        public static string GeoLocationsFileUrlBlobName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("GeoLocationsFileUrlBlobName");
            }
        }

        public static short GeoLocationsFileUrlExpirationTimeInMinutes
        {
            get
            {
                return DynamicConfig.GetRequiredValue<short>("GeoLocationsFileUrlExpirationTimeInMinutes");
            }
        }

        public static IReadOnlyDictionary<string, int> ProfileSupportedLanguages
        {
            get
            {
                return GetProfileSupportedLanguages("ProfileSupportedLanguages");
            }
        }


        public static string DSAMixedModeImportFixFileContainerName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("DSAMixedModeImportFixFileContainerName");
            }
        }

        public static string DSAMixedModeImportFixFileBlobName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("DSAMixedModeImportFixFileBlobName");
            }
        }

        public static int AddTextAssetsBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AddTextAssetsBatchSize");
            }
        }

        public static int TextAssetFetchByIdBatchSize => DynamicConfig.GetValue("TextAssetFetchByIdBatchSize", 500);

        public static bool UseBatchforTextAssetFetchById => DynamicConfig.GetValue("UseBatchforTextAssetFetchById", false);

        public static string ImportCallToActionMapFileContainerName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("ImportCallToActionMapFileContainerName");
            }
        }

        public static string ImportCallToActionMapFileBlobName
        {
            get
            {
                return DynamicConfig.GetRequiredValue<string>("ImportCallToActionMapFileBlobName");
            }
        }

        public static short DSAImportFixFileExpirationTimeInMinutes
        {
            get
            {
                return DynamicConfig.GetRequiredValue<short>("DSAImportFixFileExpirationTimeInMinutes");
            }
        }


        public static int ExtendedFanoutKeywordsThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("ExtendedFanoutKeywordsThreshold", 4000000);
            }
        }

        public static int ExtendedFanoutAdGroupNegativeKeywordsForSearchTermThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("ExtendedFanoutAdGroupNegativeKeywordsForSearchTermThreshold", 4000000);
            }
        }

        public static int ExtendedFanoutEstimatedSearchTermsCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("ExtendedFanoutEstimatedSearchTermsCountThreshold", 4000000);
            }
        }

        public static int LocalFanoutSearchTermsCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("LocalFanoutSearchTermsCountThreshold", 100000);
            }
        }

        public static bool EnableKeywordPerformanceDataCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableKeywordPerformanceDataCountThreshold", false);
            }
        }

        public static int KeywordPerformanceDataCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("KeywordPerformanceDataCountThreshold", 1000000);
            }
        }

        public static int RemoteFanoutSearchTermsCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("RemoteFanoutSearchTermsCountThreshold", 250000);
            }
        }

        public static int LocalFanoutDsaSearchTermsThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("LocalFanoutDsaSearchTermsThreshold", 200000);
            }
        }

        public static int RemoteFanoutDsaSearchTermsThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("RemoteFanoutDsaSearchTermsThreshold", 1000000);
            }
        }

        public static int DsaSearchTermsAdLandingPageUrlThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("DsaSearchTermsAdLandingPageUrlThreshold", 100000);
            }
        }

        public static int DsaSearchTermsAdLandingPageUrlBatchSize
        {
            get
            {
                return DynamicConfig.GetValue<int>("DsaSearchTermsAdLandingPageUrlBatchSize", 0);
            }
        }

        // This is a per shard threshold
        public static int DsaSearchTermsBIParallelSubShardingEntityCountThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("DsaSearchTermsBIParallelSubShardingEntityCountThreshold", 50000);
            }
        }

        public static int DsaSearchTermsBIParallelSubShardingMaxIncrease
        {
            get
            {
                return DynamicConfig.GetValue<int>("DsaSearchTermsBIParallelSubShardingMaxIncrease", 2);
            }
        }

        public static int ExtendedFanoutAdsThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("ExtendedFanoutAdsThreshold", 2000000);
            }
        }

        public static int ExtendedFanoutAdGroupsThreshold
        {
            get
            {
                return DynamicConfig.GetValue<int>("ExtendedFanoutAdGroupsThreshold", 2000000);
            }
        }

        public static int MaxCustomerBatchSizeForDvsCalls
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxCustomerBatchSizeForDvsCalls");
            }
        }

        public static int MaxCustomerBatchSizeForAdGroupDvsCalls
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxCustomerBatchSizeForAdGroupDvsCalls");
            }
        }

        public static int MaxEntityBatchSizeForDvsCalls
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxEntityBatchSizeForDvsCalls");
            }
        }

        public static int NumberOfThreadsForGetBasicAdgroupInfo
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("NumberOfThreadsForGetBasicAdgroupInfo");
            }
        }

        public static int NumberOfThreadsForSearchAccounts
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("NumberOfThreadsForSearchAccounts");
            }
        }

        public static int MinimumBatchSizeToUseMultithreadingForGetBasicAdgroupInfo
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MinimumBatchSizeToUseMultithreadingForGetBasicAdgroupInfo");
            }
        }

        public static int MaxThreadsForDvsDaoCalls
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxThreadsForDvsDaoCalls");
            }
        }

        private static readonly bool _alwaysResolveConfig = false;

        static DynamicConfigValues()
        {
            var processName = Process.GetCurrentProcess().ProcessName;

            // Unit tests change Unity registration for config classes, so need to always re-resolve config in unit tests
            if (processName.StartsWith("testhost", StringComparison.InvariantCultureIgnoreCase) ||
                processName.StartsWith("vstest.console", StringComparison.InvariantCultureIgnoreCase))
            {
                _alwaysResolveConfig = true;
            }
        }

        private static IApplicationConfigHolder _applicationConfigHolder;

        public static AppConfig DynamicConfig
        {
            get
            {
                if (_applicationConfigHolder == null || _alwaysResolveConfig)
                {
                    try
                    {
                        if (unityContainer == null)
                        {
                            if (ServiceLocator.Current == null)
                            {
                                Environment.FailFast($"ServiceLocator.Current IS NULL");
                            }
                            _applicationConfigHolder = ServiceLocator.Current.Resolve<IApplicationConfigHolder>();
                        }
                        else
                        {
                            _applicationConfigHolder = unityContainer.Resolve<IApplicationConfigHolder>();
                        }
                    }
                    catch (Exception e)
                    {
                        string tmp = ConfigurationManager.AppSettings["TerminateOnDynamicConfigLoadFailure"];

                        if (bool.TryParse(tmp, out bool shouldCrash) && shouldCrash)
                        {
                            // Someone tried to access dynamic configuration before it was initialized. This can lead to
                            // subsequent failures that are very hard to diagnose the root cause of. To avoid this, ensure
                            // it is very obvious that someone did something wrong by crashing the process.
                            Environment.FailFast(
                                $"Could not resolve IApplicationConfigHolder. This is a fatal error. Process terminating. Exception was: {e}.");
                        }

                        throw;
                    }

                    if (_applicationConfigHolder == null)
                    {
                        throw new InvalidOperationException("Could not get an instance of IApplicationConfigHolder");
                    }
                }

                return _applicationConfigHolder.GetDynamicApplicationConfig();
            }
        }

        public static List<Language> BingAdsSupportedLanguageListSortedByMarketImpact
        {
            get
            {
                var bingAdsSupportedLanguageListSortedByMarketImpact = new List<Language>();
                var languageListSortedbyMarket =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageListSortedByMarketImpact").Split(',');
                foreach (var bingAdsSupportedLanguage in languageListSortedbyMarket.OrEmpty())
                {
                    Language language;
                    if (Enum.TryParse<Language>(bingAdsSupportedLanguage, out language))
                    {
                        bingAdsSupportedLanguageListSortedByMarketImpact.Add(language);
                    }
                }
                return bingAdsSupportedLanguageListSortedByMarketImpact;
            }
        }

        public static IReadOnlyDictionary<string, string> AdWordsLanguageCodeToBingAdsSupportedLanguageCodeMap
        {
            get
            {
                var adWordsLanguageCodeToBingAdsSupportedLanguageCodeMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                var languageCodeMap =
                    DynamicConfig.GetRequiredValue<string>("AdWordsLanguageCodeToBingAdsSupportedLanguageCodeMap").Split(';');
                foreach (var languageCodeMapItem in languageCodeMap.OrEmpty())
                {
                    var adWordsLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[1];
                    adWordsLanguageCodeToBingAdsSupportedLanguageCodeMap.Add(adWordsLanguageCode, bingAdsSupportedLanguageCode);
                }
                return adWordsLanguageCodeToBingAdsSupportedLanguageCodeMap;
            }
        }

        public static IReadOnlyDictionary<string, string> BingAdsSupportedLanguageCodeToLanguageNameMap(
            IEnumerable<uint> adsAccountGlobalizationPilotIds)
        {
            var bingAdsSupportedLanguageCodeToLanguageNameMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            var languageCodeMap =
                DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMap").Split(';');
            foreach (var languageCodeMapItem in languageCodeMap.OrEmpty())
            {
                var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
            }

            if (adsAccountGlobalizationPilotIds != null && adsAccountGlobalizationPilotIds.Contains(LanguagePilot.AdsGlobalizationPhaseTextAdsJapan))
            {
                var languageCodeMapExtended =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMapExtendedTextAdsJapan").Split(';');
                foreach (var languageCodeMapItem in languageCodeMapExtended.OrEmpty())
                {
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                    bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
                }
            }

            if (adsAccountGlobalizationPilotIds != null && adsAccountGlobalizationPilotIds.Contains(LanguagePilot.AdsGlobalizationPhase6MENA))
            {
                var languageCodeMapExtended =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMapAdsGBLPhase6MENA").Split(';');
                foreach (var languageCodeMapItem in languageCodeMapExtended.OrEmpty())
                {
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                    bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
                }
            }

            if (adsAccountGlobalizationPilotIds != null && adsAccountGlobalizationPilotIds.Contains(LanguagePilot.AdsGlobalizationPhaseSimplifiedChinese))
            {
                var languageCodeMapExtended =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMapAdsGBLPhaseSimplifiedChinese").Split(';');
                foreach (var languageCodeMapItem in languageCodeMapExtended.OrEmpty())
                {
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                    bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
                }
            }

            if (adsAccountGlobalizationPilotIds != null && adsAccountGlobalizationPilotIds.Contains(LanguagePilot.AdsGlobalizationPhase9))
            {
                var languageCodeMapExtended =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMapAdsGBLPhase9").Split(';');
                foreach (var languageCodeMapItem in languageCodeMapExtended.OrEmpty())
                {
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                    bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
                }
            }

            if (adsAccountGlobalizationPilotIds != null && adsAccountGlobalizationPilotIds.Contains(LanguagePilot.AdsGlobalizationPhase9VI))
            {
                var languageCodeMapExtended =
                    DynamicConfig.GetRequiredValue<string>("BingAdsSupportedLanguageCodeToLanguageNameMapAdsGBLPhase9VI").Split(';');
                foreach (var languageCodeMapItem in languageCodeMapExtended.OrEmpty())
                {
                    var bingAdsSupportedLanguageCode = languageCodeMapItem.Split(',')[0];
                    var bingAdsSupportedLanguageName = languageCodeMapItem.Split(',')[1];
                    bingAdsSupportedLanguageCodeToLanguageNameMap.Add(bingAdsSupportedLanguageCode, bingAdsSupportedLanguageName);
                }
            }

            return bingAdsSupportedLanguageCodeToLanguageNameMap;
        }

        public static IReadOnlyDictionary<string, string> AdWordsCallToActionTextToBingAdsCallToActionEnumMap
        {
            get
            {
                var adWordsCallToActionTextToBingAdsCallToActionEnumMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                var callToActionMap = DynamicConfig.GetRequiredValue<string>("AdWordsCallToActionTextToBingAdsCallToActionEnumMap").Split(';');
                foreach (var pair in callToActionMap.OrEmpty())
                {
                    var items = pair.Split(',');
                    var adWordsCallToActionText = items[0];
                    var bingAdsCallToActionEnumString = items[1];
                    adWordsCallToActionTextToBingAdsCallToActionEnumMap.Add(adWordsCallToActionText, bingAdsCallToActionEnumString);
                }
                return adWordsCallToActionTextToBingAdsCallToActionEnumMap;
            }
        }

        public static IReadOnlyDictionary<int, Language> AdWordsCallToActionTextToBingAdsCallToActionLanguageEnumMap
        {
            get
            {
                var adWordsCallToActionTextToBingAdsCallToActionLanguageEnumMap = new Dictionary<int, Language>();
                var callToActionMap = DynamicConfig.GetRequiredValue<string>("AdWordsCallToActionTextToBingAdsCallToActionLanguageEnumMap").Split(';');
                for (int i = 0; i < callToActionMap.Length; i++)
                {
                    if (Enum.TryParse(callToActionMap[i], out Language language))
                    {
                        adWordsCallToActionTextToBingAdsCallToActionLanguageEnumMap.Add(i, language);
                    }
                }
                return adWordsCallToActionTextToBingAdsCallToActionLanguageEnumMap;
            }
        }

        public static bool DropExtraCustomParameterInApiFlow
        {
            get
            {
                return DynamicConfig.GetValue<bool>("DropExtraCustomParameterInApiFlow", false);
            }
        }

        public static ReadOnlyCollection<string> DeltaLoadDbTypes
        {
            get
            {
                return DynamicConfig.GetValue("ShardedDeltaLoadDbTypes", new ReadOnlyCollection<string>(new List<string>()));
            }
        }

        public static string GoogleImportApiTaskType
        {
            get { return DynamicConfig.GetRequiredValue<string>("GoogleImportApiTaskType"); }
        }
        public static string FileImportApiTaskType
        {
            get { return DynamicConfig.GetRequiredValue<string>("FileImportApiTaskType"); }
        }

        public static bool EnableMDSCallByAAD
        {
            get { return DynamicConfig.GetRequiredValue<bool>("EnableMDSCallByAAD"); }
        }

        public static bool UseAzureServiceTokenProviderInOData => DynamicConfig.GetValue("UseAzureServiceTokenProviderInOData", false);

        public static bool UseAzureServiceTokenProviderInAggregationService => DynamicConfig.GetValue("UseAzureServiceTokenProviderInAggregationService", false);
        public static bool AllowUpdateSalesCountryforShoppingInImportprocess => DynamicConfig.GetValue("AllowUpdateSalesCountryforShoppingInImportprocess", false);
        public static bool UseAggregationServiceEndpointForAutoImage => DynamicConfig.GetValue("UseAggregationServiceEndpointForAutoImage", false);

        public static List<int> ClonedDBPartitions
        {
            get
            {
                List<int> clonedDBPartitions = null;
                clonedDBPartitions = DynamicConfig.GetListValues("ClonedDBPartitions").Select(Int32.Parse).ToList();
                return clonedDBPartitions;
            }
        }

        public static List<string> BlacklistedShadowCalls
        {
            get { return DynamicConfig.GetListValues("BlacklistedShadowCalls"); }
        }

        public static List<string> WhitelistedShadowCalls
        {
            get { return DynamicConfig.GetListValues("WhitelistedShadowCalls"); }
        }

        public static string ShadowMTEndpoint
        {
            get { return DynamicConfig.GetRequiredValue<string>("ShadowMTEndpoint"); }
        }

        public static string ShadowApi9Endpoint
        {
            get { return DynamicConfig.GetRequiredValue<string>("ShadowApi9Endpoint"); }
        }


        public static int MarketIntelligenceDataFetchWorkerThreadNumber
        {
            get { return DynamicConfig.GetRequiredValue<int>("MarketIntelligenceDataFetchWorkerThreadNumber"); }
        }

        public static int MarketIntelligenceBatchDonwlaodWorkerThreadNumber
        {
            get { return DynamicConfig.GetRequiredValue<int>("MarketIntelligenceBatchDonwlaodWorkerThreadNumber"); }
        }


        public static int MarketIntelligenceFrameworkPilotPercentage
        {
            get { return DynamicConfig.GetRequiredValue<int>("MarketIntelligenceFrameworkPilotPercentage"); }
        }

        public static int MarketIntelligenceMemoryCacheMemoryLimitInMB
        {
            get { return DynamicConfig.GetRequiredValue<int>("MarketIntelligenceMemoryCacheMemoryLimitInMB"); }
        }

        public static int MarketIntelligenceMemoryCacheExpiryInMin
        {
            get { return DynamicConfig.GetRequiredValue<int>("MarketIntelligenceMemoryCacheExpiryInMin"); }
        }

        public static int MIBlobAccessExpiryTimeInSeconds
        {
            get { return DynamicConfig.GetRequiredValue<int>("MIBlobAccessExpiryTimeInSeconds"); }
        }

        public static int MIBlobBuckets
        {
            get { return DynamicConfig.GetRequiredValue<int>("MIBlobBuckets"); }
        }

        public static long MIFileSplitSizeLimitInMB => DynamicConfig.GetValue<long>("MIFileSplitSizeLimitInMB", 10 * 1024);

        public static IReadOnlyDictionary<string, short> IntervalInHoursForTriggerRunOnceMITasks => ConvertToStringShortMap("IntervalInHoursForTriggerRunOnceMITasks");

        public static IReadOnlyDictionary<string, string> OverrideMinSyncIntervalForEntityType => DynamicConfig.GetMapValues("OverrideMinSyncIntervalForEntityType");

        public static string MITaskCreationTestNamePrefix => DynamicConfig.GetValue<string>("MITaskCreationTestNamePrefix", "MITaskCreationTest");

        public static int ImportErrorBuckets => DynamicConfig.GetValue<int>("ImportErrorBuckets", 2);

        public static int ImportBulkResBuckets => DynamicConfig.GetValue<int>("ImportBulkResBuckets", 8);

        public static bool IsTestRunEnv => DynamicConfig.GetRequiredValue<bool>("IsTestRunEnv");

        public static int MaxImageDownloadDegreeOfParallelism = DynamicConfig.GetValue<int>("MaxImageDownloadDegreeOfParallelism", 10);

        public static bool AddAdditionalHeadersToImageDownload => DynamicConfig.GetRequiredValue<bool>("AddAdditionalHeadersToImageDownload");
        public static double RoiShrinkThreshold => DynamicConfig.GetValue<double>("RoiShrinkThreshold", 0.15);
        public static bool AllowDSAInAssetGridInAllAccounts => DynamicConfig.GetRequiredValue<bool>("AllowDSAInAssetGridInAllAccounts");
        public static bool AllowMMAInAssetGridInAllAccounts => DynamicConfig.GetRequiredValue<bool>("AllowMMAInAssetGridInAllAccounts");
        public static bool AutoConvertCampaignToAudienceStatic => DynamicConfig.GetValue<bool>("AutoConvertCampaignToAudienceStatic", false);

        public static int CosmosOutputScheduleTriggerTaskTypeIdForTaskEngine => DynamicConfig.GetRequiredValue<int>("CosmosOutputScheduleTriggerTaskTypeIdForTaskEngine");
        public static int PrivacyCheckMaxParallelRequests => DynamicConfig.GetRequiredValue<int>("PrivacyCheckMaxParallelRequests");
        public static bool PrivacyCheckUseAdInsightBCPForTaskEngine => DynamicConfig.GetRequiredValue<bool>("PrivacyCheckUseAdInsightBCPForTaskEngine");
        public static int PrivacyCheckRequestInterval => DynamicConfig.GetRequiredValue<int>("PrivacyCheckRequestInterval");
        public static int PrivacyCheckMaxParallelUpdatesToDB => DynamicConfig.GetRequiredValue<int>("PrivacyCheckMaxParallelUpdatesToDB");
        public static int PrivacyCheckAccountPilotPercentage => DynamicConfig.GetRequiredValue<int>("PrivacyCheckAccountPilotPercentage");
        public static HashSet<int> PrivacyCheckWhitelistAccounts => new HashSet<int>(DynamicConfig.GetValue("PrivacyCheckWhitelistAccounts", EmptyIntList));
        public static bool EnableDailyPrivacyCheckStatusUpdate => DynamicConfig.GetRequiredValue<bool>("EnableDailyPrivacyCheckStatusUpdate");
        public static int DailyPrivacyCheckTaskTypeIdForTaskEngine => DynamicConfig.GetRequiredValue<int>("DailyPrivacyCheckTaskTypeIdForTaskEngine");
        public static string DailyPrivacyCheckCosmosOutputBasePathForTaskEngine => DynamicConfig.GetRequiredValue<string>("DailyPrivacyCheckCosmosOutputBasePathForTaskEngine");
        public static string DailyPrivacyCheckCosmosScheduleCheckCronScheduleForTaskEngine => DynamicConfig.GetRequiredValue<string>("DailyPrivacyCheckCosmosScheduleCheckCronScheduleForTaskEngine");

        public static int VideoRecommendationTaskTypeIdForTaskEngine => DynamicConfig.GetRequiredValue<int>("VideoRecommendationTaskTypeIdForTaskEngine");
        public static string VideoRecommendationCosmosScheduleCheckCronScheduleForTaskEngine => DynamicConfig.GetRequiredValue<string>("VideoRecommendationCosmosScheduleCheckCronScheduleForTaskEngine");
        public static string AdNeedVideoRecommendationFilePath => DynamicConfig.GetRequiredValue<string>("AdNeedVideoRecommendationFilePath");

        public static string AdNeedVideoRecommendationP1FilePath => DynamicConfig.GetRequiredValue<string>("AdNeedVideoRecommendationP1FilePath");


        public static int VideoRecommendationHeartbeatInterval => DynamicConfig.GetValue<int>("VideoRecommendationHeartbeatInterval", 10000);
        public static string VideoRecommendationTemplateIds => DynamicConfig.GetRequiredValue<string>("VideoRecommendationTemplateIds");


        public static string VideoRecommendationAssetDefinitionContainerName => DynamicConfig.GetRequiredValue<string>("VideoRecommendationAssetDefinitionContainerName");

        public static bool EnableQueueVideoRecommendationSubTask => DynamicConfig.GetRequiredValue<bool>("EnableQueueVideoRecommendationSubTask");

        public static bool EnableQueueVideoRecommendationP1SubTask => DynamicConfig.GetRequiredValue<bool>("EnableQueueVideoRecommendationP1SubTask");
        public static int DailyPrivacyCheckFilePartitionCountForTaskEngine => DynamicConfig.GetRequiredValue<int>("DailyPrivacyCheckFilePartitionCountForTaskEngine");
        public static bool EnableQueueDailyPrivacyCheckForTaskEngine => DynamicConfig.GetRequiredValue<bool>("EnableQueueDailyPrivacyCheckForTaskEngine");

        public static string NewMarketIntelligenceReportDownloadingTimeout
        {
            get { return DynamicConfig.GetRequiredValue<string>("NewMarketIntelligenceReportDownloadingTimeout"); }
        }

        public static int InlineDownloadDataSelectRowsPerChunk
        {
            get { return DynamicConfig.GetRequiredValue<int>("InlineDownloadDataSelectRowsPerChunk"); }
        }


        public static int ImportParserBatchSizeForCampaignsAndAdGroups
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportParserBatchSizeForCampaignsAndAdGroups"); }
        }
        public static int ImportParserBatchSizeForKeywords
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportParserBatchSizeForKeywords"); }
        }
        public static int ImportParserBatchSizeForAds
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportParserBatchSizeForAds"); }
        }
        public static int ImportParserDefaultBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportParserDefaultBatchSize"); }
        }

        public static int AdGroupSitelinkAssociationFetchThreshold
        {
            get { return DynamicConfig.GetValue<int>("AdGroupSitelinkAssociationFetchThreshold", 10000); }
        }

        public static int LastImportTimeFrameInDays
        {
            get { return DynamicConfig.GetRequiredValue<int>("LastImportTimeFrameInDays"); }
        }

        public static int MaxForIdBasedDbFetch
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxForIdBasedDbFetch"); }
        }

        public static string BulkUploadTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("BulkUploadTaskTypeNameForTaskEngine"); }
        }

        public static string BulkDownloadTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("BulkDownloadTaskTypeNameForTaskEngine"); }
        }

        public static int SleepIntervalInMillSecondsForCreateBulkDownloadTaskRetry
        {
            get { return DynamicConfig.GetRequiredValue<int>("SleepIntervalInMillSecondsForCreateBulkDownloadTaskRetry"); }
        }

        public static int MaxRetryCountForCreateBulkDownloadTask
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxRetryCountForCreateBulkDownloadTask"); }
        }

        public static string UpdateGeoLocationStatusTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("UpdateGeoLocationStatusTaskTypeNameForTaskEngine"); }
        }

        public static string CosmosOutputScheduleTriggerTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("CosmosOutputScheduleTriggerTaskTypeNameForTaskEngine"); }
        }

        public static string DailyPrivacyCheckTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("DailyPrivacyCheckTaskTypeNameForTaskEngine"); }
        }

        public static string BulkEditTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("BulkEditTaskTypeNameForTaskEngine"); }
        }

        public static string BulkEditCustomerThrottleTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("BulkEditCustomerThrottleTaskTypeNameForTaskEngine"); }
        }

        public static string UpdateCampaignStoreIdTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("UpdateCampaignStoreIdTaskTypeNameForTaskEngine"); }
        }
        public static string PpsReconciliationReportUploadTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("PpsReconciliationReportUploadTaskTypeNameForTaskEngine"); }
        }
        public static string PpsReconciliationReportManagerTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("PpsReconciliationReportManagerTaskTypeNameForTaskEngine"); }
        }

        public static string OfflineConversionPreviewTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionPreviewTaskTypeNameForTaskEngine"); }
        }

        public static string OfflineConversionCommitTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionCommitTaskTypeNameForTaskEngine"); }
        }

        public static int OfflineConversionTaskExecutionMaxCount
        {
            get { return DynamicConfig.GetValue<int>("OfflineConversionTaskExecutionMaxCount", 200); }
        }
        public static int CosmosDownloadRetryCount
        {
            get { return DynamicConfig.GetValue<int>("CosmosDownloadRetryCount", 1); }
        }

        public static int WriteObjectStoreRetryCount
        {
            get { return DynamicConfig.GetValue<int>("WriteObjectStoreRetryCount", 1); }
        }

        public static bool EnablePmaxDiagnoseV2Optimization
        {
            get { return DynamicConfig.GetValue<bool>("EnablePmaxDiagnoseV2Optimization", false); }
        }

        public static int CosmosDownloadRetryIntervalInMilliSecondsForAutoConvTask
        {
            get { return DynamicConfig.GetValue<int>("CosmosDownloadRetryIntervalInMilliSecondsForAutoConvTask", 10000); }
        }

        public static int AutoConvTaskRetryIntervalInSeconds
        {
            get { return DynamicConfig.GetValue<int>("AutoConvTaskRetryIntervalInSeconds", 1800); }
        }

        public static string OfflineConversionDailyReportTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionDailyReportTaskTypeNameForTaskEngine"); }
        }

        public static string PMaxBiddingSchemeSwitchBackTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("PMaxBiddingSchemeSwitchBackTaskTypeNameForTaskEngine"); }
        }

        public static string LogoAdExtensionAutoGenerationTaskTypeNameForTaskEngine
        {
            get { return DynamicConfig.GetRequiredValue<string>("LogoAdExtensionAutoGenerationTaskTypeNameForTaskEngine"); }
        }

        public static string PMaxBiddingSchemeSwitchBackCosmosFilePath
        {
            get { return DynamicConfig.GetRequiredValue<string>("PMaxBiddingSchemeSwitchBackCosmosFilePath"); }
        }

        public static string OfflineConversionGServerEndpoint
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionGServerEndpoint"); }
        }

        public static string OfflineConversionLinkedInGServerEndpoint
        {
            get { return DynamicConfig.GetRequiredValue<string>("OfflineConversionLinkedInGServerEndpoint"); }
        }

        public static int OfflineConversionMaxDelayHours
        {
            get { return DynamicConfig.GetValue<int>("OfflineConversionMaxDelayHours", 8); }
        }

        public static int OfflineConversionMinDelayHours
        {
            get { return DynamicConfig.GetValue<int>("OfflineConversionMinDelayHours", 2); }
        }

        public static string OnlineConversionGServerEndpoint
        {
            get { return DynamicConfig.GetRequiredValue<string>("OnlineConversionGServerEndpoint"); }
        }

        public static int SleepIntervalInMillSecondsForGetBulkEditSessionHistoryRetry
        {
            get { return DynamicConfig.GetRequiredValue<int>("SleepIntervalInMillSecondsForGetBulkEditSessionHistoryRetry"); }
        }

        public static int MaxRetryCountForGetBulkEditSessionHistory
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxRetryCountForGetBulkEditSessionHistory"); }
        }

        public static bool EnableTestHookStatusForBulkUpload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableTestHookStatusForBulkUpload");
            }
        }

        public static bool enableMSANLiftExperimentPilot
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("enableMSANLiftExperimentPilot");
            }
        }

        public static bool HideLowConfidenceMSANLiftExperimentResults
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("HideLowConfidenceMSANLiftExperimentResults");
            }
        }

        public static bool IncludeLinkedinImpressionsInLiftExperimentEligibility 
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("IncludeLinkedinImpressionsInLiftExperimentEligibility");
            }
        }

        public static int TargetCriterionBulkUploadMaxThread
        {
            get { return DynamicConfig.GetRequiredValue<int>("TargetCriterionBulkUploadMaxThread"); }
        }

        public static int SleepSecondsForLargeRadiusTargetCreation
        {
            get { return DynamicConfig.GetRequiredValue<int>("SleepSecondsForLargeRadiusTargetCreation"); }
        }

        public static int LimitSizeOfLargeRadiusTargetCreation
        {
            get { return DynamicConfig.GetRequiredValue<int>("LimitSizeOfLargeRadiusTargetCreation"); }
        }

        public static int TargetCriterionFetchRelationCountBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("TargetCriterionFetchRelationCountBatchSize"); }
        }

        public static int TargetCriterionSpiltUpdateBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("TargetCriterionSpiltUpdateBatchSize"); }
        }

        public static int PerformanceTargetSplitUpdateBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("PerformanceTargetSplitUpdateBatchSize"); }
        }

        public static int PerformanceTargetSplitUnPauseBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("PerformanceTargetSplitUnPauseBatchSize"); }
        }

        public static int PerformanceTargetBulkEditUpdateMaxThread
        {
            get { return DynamicConfig.GetRequiredValue<int>("PerformanceTargetBulkEditUpdateMaxThread"); }
        }

        public static int OptimisticEntitySplitBatchSizeForTargetCriterions
        {
            get { return DynamicConfig.GetRequiredValue<int>("OptimisticEntitySplitBatchSizeForTargetCriterions"); }
        }

        public static int PessimisticEntitySplitBatchSizeForTargetCriterions
        {
            get { return DynamicConfig.GetRequiredValue<int>("PessimisticEntitySplitBatchSizeForTargetCriterions"); }
        }

        public static int TargetCriterionUpdateBatchMaxSizePerEntity
        {
            get { return DynamicConfig.GetRequiredValue<int>("TargetCriterionUpdateBatchMaxSizePerEntity"); }
        }

        public static int MaxAdGroupsPerApplyProductPartitionsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxAdGroupsPerApplyProductPartitionsCall"); }
        }

        public static int MaxAssetGroupsPerApplyListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxAssetGroupsPerApplyListingGroupsCall"); }
        }

        public static int BulkMaxAdGroupsPerApplyProductPartitionsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAdGroupsPerApplyProductPartitionsCall"); }
        }

        public static int BulkMaxAdGroupsWithDeletesPerApplyProductPartitionsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAdGroupsWithDeletesPerApplyProductPartitionsCall"); }
        }

        public static int BulkMaxAssetGroupsPerApplyListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAssetGroupsPerApplyListingGroupsCall"); }
        }

        public static int BulkMaxAssetGroupsWithDeletesPerApplyListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAssetGroupsWithDeletesPerApplyListingGroupsCall"); }
        }

        public static int MaxProductPartitionsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxProductPartitionsPerApplyCall"); }
        }

        public static int MaxAssetGroupListingGroupsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxAssetGroupListingGroupsPerApplyCall"); }
        }

        public static int BulkMaxProductPartitionsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxProductPartitionsPerApplyCall"); }
        }

        public static int BulkMaxAssetGroupListingGroupsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAssetGroupListingGroupsPerApplyCall"); }
        }

        public static int MaxAdGroupsPerApplyHotelListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxAdGroupsPerApplyHotelListingGroupsCall"); }
        }

        public static int BulkMaxAdGroupsPerApplyHotelListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAdGroupsPerApplyHotelListingGroupsCall"); }
        }

        public static int BulkMaxAdGroupsWithDeletesPerApplyHotelListingGroupsCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxAdGroupsWithDeletesPerApplyHotelListingGroupsCall"); }
        }

        public static int MaxHotelListingGroupsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("MaxHotelListingGroupsPerApplyCall"); }
        }

        public static int BulkMaxHotelListingGroupsPerApplyCall
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkMaxHotelListingGroupsPerApplyCall"); }
        }

        public static bool EnableAutoBidding
        {
            get
            {
                return DynamicConfig.GetValue("EnableAutoBidding", false);
            }
        }

        public static bool UseMIForBillingBudgetServiceBus
        {
            get
            {
                return DynamicConfig.GetValue("UseMIForBillingBudgetServiceBus", false);
            }

        }

        public static bool EnableDiagnosticsPilot
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableDiagnosticsPilot");
            }
        }

        public static bool DynamicSearchAdsEnabled => DynamicConfig.GetValue("DynamicSearchAdsEnabled", false);

        public static int BulkUploadMaxThreadsForAdExtension
        {
            get { return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxThreadsForAdExtension"); }
        }

        public static bool MetricClientEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("MetricClientEnabled");
            }
        }

        public static bool RecordErrorMetrics
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("RecordErrorMetrics");
            }
        }

        public static bool RecordDbMetrics
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("RecordDbMetrics");
            }
        }

        public static HashSet<int> CustomerIdListBlockedForBulkOperations
        {
            get
            {
                return new HashSet<int>(DynamicConfig.GetValue<ReadOnlyCollection<int>>("CustomerIdListBlockedForBulkOperations", new ReadOnlyCollection<int>(new List<int>())));
            }
        }

        public static bool ShouldHeartBeat
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("ShouldHeartBeat");
            }
        }

        public static bool EnableTaskFlowInExportImportedData
        {
            get
            {
                return DynamicConfig.GetValue<bool>("EnableTaskFlowInExportImportedData", false);
            }
        }

        public static List<string> TravelDaoReadOperationConfig => DynamicConfig.GetListValues("TravelDaoReadOperationConfig");

        public static int ImportAddAdWordsIdMappingDefaultBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingDefaultBatchSize");
            }
        }

        public static int ImportAddAdWordsIdMappingBatchSizeForCampaignAndAdGroup
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingBatchSizeForCampaignAndAdGroup");
            }
        }

        public static int ImportAddAdWordsIdMappingBatchSizeForKeyword
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingBatchSizeForKeyword");
            }
        }

        public static int ImportAddAdWordsIdMappingBatchSizeForAd
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingBatchSizeForAd");
            }
        }

        public static int ImportAddAdWordsIdMappingTaskQueueCapacity
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingTaskQueueCapacity"); }
        }

        public static int ImportAddAdWordsIdMappingConsumerCount
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportAddAdWordsIdMappingConsumerCount"); }
        }

        public static int ImportBuildBulkUploadCsvForDeletionTaskQueueCapacity
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportBuildBulkUploadCsvForDeletionTaskQueueCapacity"); }
        }

        public static int ImportBuildBulkUploadCsvForDeletionConsumerCount
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportBuildBulkUploadCsvForDeletionConsumerCount"); }
        }

        public static int ImportBuildBulkUploadCsvForAdGroupDeletionTaskQueueCapacity
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportBuildBulkUploadCsvForAdGroupDeletionTaskQueueCapacity"); }
        }

        public static int ImportBuildBulkUploadCsvForAdGroupDeletionConsumerCount
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportBuildBulkUploadCsvForAdGroupDeletionConsumerCount"); }
        }

        public static int ImportEntityFetchByParentBatchSizeForAdGroupDeletion
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportEntityFetchByParentBatchSizeForAdGroupDeletion"); }
        }

        public static int ImportAdWordsAdIdFetchByAdGroupIdTaskQueueCapacityForAdDeletion
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportAdWordsAdIdFetchByAdGroupIdTaskQueueCapacityForAdDeletion"); }
        }

        public static int ImportAdWordsAdIdFetchByAdGroupIdConsumerCountForAdDeletion
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportAdWordsAdIdFetchByAdGroupIdConsumerCountForAdDeletion"); }
        }

        public static int ImportAdWordsAdIdFetchByAdGroupIdBatchSizeForAdDeletion
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportAdWordsAdIdFetchByAdGroupIdBatchSizeForAdDeletion"); }
        }

        public static int ImportGetDeletedAdsAndWriteToImportDbTaskQueueCapacity
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportGetDeletedAdsAndWriteToImportDbTaskQueueCapacity"); }
        }

        public static int ImportGetDeletedAdsAndWriteToImportDbConsumerCount
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportGetDeletedAdsAndWriteToImportDbConsumerCount"); }
        }

        public static int ImportGetDeletedAdsAndWriteToImportDbBatchSize
        {
            get { return DynamicConfig.GetRequiredValue<int>("ImportGetDeletedAdsAndWriteToImportDbBatchSize"); }
        }

        public static bool ImportEnableMetricClientMonitoring
        {
            get { return DynamicConfig.GetRequiredValue<bool>("ImportEnableMetricClientMonitoring"); }
        }

        public static ReadOnlyCollection<int> ImportMetricClientErrorCodeExclusionList
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<int>>("ImportMetricClientErrorCodeExclusionList", new ReadOnlyCollection<int>(new List<int>()));
            }
        }

        public static ReadOnlyCollection<int> ImportErrorCodeBlackList
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<int>>("ImportErrorCodeBlackList", new ReadOnlyCollection<int>(new List<int>()));
            }
        }

        public static ReadOnlyCollection<long> DeltaImportCustomerIdBlackList
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("DeltaImportCustomerIdBlackList", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static bool LimitMaxPartitionsToCheckForConsistency
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("LimitMaxPartitionsToCheckForConsistency ");
            }
        }

        public static bool AdPreferenceEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("AdPreferenceEnabled");
            }
        }

        public static int RetryCountForUpdateTaskByTaskEngine
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("RetryCountForUpdateTaskByTaskEngine");
            }
        }

        public static int RetryIntervalForUpdateTaskByTaskEngineInMilliSeconds
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("RetryIntervalForUpdateTaskByTaskEngineInMilliSeconds");
            }
        }

        public static int MaxErrorMessageLengthInTaskEngineCheckPoint
        {
            get
            {
                return DynamicConfig.GetValue("MaxErrorMessageLengthInTaskEngineCheckPoint", 0);
            }
        }

        public static bool LogSqlConnectionStats
        {
            get
            {
                return DynamicConfig.GetValue<bool>("LogSqlConnectionStats", false);
            }
        }

        public static TimeSpan ProcDaoPerfLoggerThreshold
        {
            get
            {
                return DynamicConfig.GetValue<TimeSpan>("ProcDaoPerfLoggerThreshold", TimeSpan.Zero);
            }
        }

        public static int MaxSearchAccountsInBatch
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxSearchAccountsInBatch");
            }
        }

        public static int BulkEditMaxLabelAssociationsInBatch
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkEditMaxLabelAssociationsInBatch");

            }
        }

        public static int LabelAssociationMaxWriteThreads
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelAssociationMaxWriteThreads");

            }
        }

        public static int LabelAssociationBulkEditMinWaitSeconds
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelAssociationBulkEditMinWaitSeconds");

            }
        }

        public static int LabelAssociationBulkEditMaxWaitSeconds
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelAssociationBulkEditMaxWaitSeconds");

            }
        }

        public static int MaxLabelAssociationsPerEntity
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxLabelAssociationsPerEntity");

            }
        }

        public static bool EnableAsyncTaskLabelsFlow
        {
            get
            {
                return DynamicConfig.GetValue("EnableAsyncTaskLabelsFlow", false);
            }
        }

        public static bool EnableAsyncTaskLabelsMccFlow
        {
            get
            {
                return DynamicConfig.GetValue("EnableAsyncTaskLabelsMccFlow", false);
            }
        }

        public static int CampaignLabelLimitFactor
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("CampaignLabelLimitFactor");
            }
        }

        public static int AdGroupLabelLimitFactor
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdGroupLabelLimitFactor");
            }
        }

        public static int AdLabelLimitFactor
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdLabelLimitFactor");
            }
        }

        public static int KeywordLabelLimitFactor
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("KeywordLabelLimitFactor");
            }
        }

        public static int CampaignLabelHardLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("CampaignLabelHardLimit");
            }
        }

        public static int AdGroupLabelHardLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdGroupLabelHardLimit");
            }
        }

        public static int AdLabelHardLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AdLabelHardLimit");
            }
        }

        public static int KeywordLabelHardLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("KeywordLabelHardLimit");
            }
        }

        public static int LabelReportRowAggregateMaxThreads
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelReportRowAggregateMaxThreads");
            }
        }

        public static int LabelReportRowAggregateMinSizeUsingBatch
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelReportRowAggregateMinSizeUsingBatch");
            }
        }

        public static int LabelReportBiDataAggregateMaxThreads
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelReportBiDataAggregateMaxThreads");
            }
        }

        public static int LabelReportBiDataAggregateMinSizeUsingBatch
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelReportBiDataAggregateMinSizeUsingBatch");
            }
        }

        public static int LabelBulkDownloadCampaignIdBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("LabelBulkDownloadCampaignIdBatchLimit");
            }
        }

        public static int BulkUploadLabelAddBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadLabelAddBatchLimit");
            }
        }

        public static int BulkUploadLabelDeleteBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadLabelDeleteBatchLimit");
            }
        }

        public static int BulkUploadLabelAssociationAddBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadLabelAssociationAddBatchLimit");
            }
        }

        public static int BulkUploadLabelAssociationDeleteBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadLabelAssociationDeleteBatchLimit");
            }
        }

        public static int BulkUploadLabelUpdateBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadLabelUpdateBatchLimit");
            }
        }

        public static int BulkUploadFeedBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadFeedBatchLimit");
            }
        }

        public static int BulkUploadFeedItemBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadFeedItemBatchLimit");
            }
        }

        public static bool EnableFeedBulkUpload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableFeedBulkUpload");
            }
        }

        public static bool EnableFeedBulkDownload
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableFeedBulkDownload");
            }
        }

        public static int FeedItemBulkDownloadBatchSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("FeedItemBulkDownloadBatchSize");
            }
        }

        public static long GetFeedItemHashesBatchSize =>
            DynamicConfig.GetRequiredValue<long>(nameof(GetFeedItemHashesBatchSize));

        public static int BulkUploadMaxThreadsForLabel
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxThreadsForLabel");
            }
        }

        public static int BulkUploadMaxThreadsForLabelAssociation
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadMaxThreadsForLabelAssociation");
            }
        }
        public static int BulkUploadFeedAssociationBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadFeedAssociationBatchLimit");
            }
        }

        public static int AccountsSummaryMaxAccountsLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AccountsSummaryMaxAccountsLimit");
            }
        }


        public static int AccountsSummaryRequestChunkSize
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("AccountsSummaryRequestChunkSize");
            }
        }

        public static int GsyncCacheExpirationTime
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GsyncCacheExpirationTime");
            }
        }

        public static bool GsyncPermissionCacheEnabled
        {
            get
            {
                return DynamicConfig.GetRequiredValue<bool>("EnableCacheGsyncUserPermissions");
            }
        }

        public static int GSyncImageDownloadLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("GSyncImageDownloadLimit");
            }
        }

        public static int AccountLimitForGettingCampaignCount => DynamicConfig.GetRequiredValue<int>("AccountLimitForGettingCampaignCount");

        public static int AccountDashboardMaxAccountsLimit => DynamicConfig.GetValue<int>("AccountDashboardMaxAccountsLimit", 4000);

        public static int AccountDashboardMaxAccountsDBLimitPerPartition => DynamicConfig.GetValue<int>("AccountDashboardMaxAccountsDBLimitPerPartition", 1000);

        public static int AccountDashboardPagesize => DynamicConfig.GetValue<int>("AccountDashboardPagesize", 1000);

        public static bool AIUpScaleImageEnable = DynamicConfig.GetValue<bool>("AIUpScaleImageEnable", false);

        public static bool AccountsSummaryUsePreloadEnum => DynamicConfig.GetValue<bool>("AccountsSummaryUsePreloadEnum", false);
        public static TimeSpan AccountsSummaryPreLoadMaxAgeTimespan => DynamicConfig.GetValue<TimeSpan>("AccountsSummaryPreLoadMaxAgeTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan AccountsSummaryPreLoadCacheObjectTtlTimespan => DynamicConfig.GetValue<TimeSpan>("AccountsSummaryPreLoadCacheObjectTtlTimespan", TimeSpan.FromSeconds(10));
        public static TimeSpan AccountsSummaryPreLoadMaxAdditionalWaitTimespan => DynamicConfig.GetValue<TimeSpan>("AccountsSummaryPreLoadMaxAdditionalWaitTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan AccountsSummaryPreLoadAdditionalWaitPollIntervalTimespan => DynamicConfig.GetValue<TimeSpan>("AccountsSummaryPreLoadAdditionalWaitPollIntervalTimespan", TimeSpan.FromMilliseconds(100));

        public static TimeSpan AccountListPreloadMaxAgeTimespan => DynamicConfig.GetValue<TimeSpan>("AccountListPreloadMaxAgeTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan AccountListPreloadCacheObjectTtlTimespan => DynamicConfig.GetValue<TimeSpan>("AccountListPreloadCacheObjectTtlTimespan", TimeSpan.FromSeconds(10));
        public static TimeSpan AccountListPreloadMaxAdditionalWaitTimespanFromPreload => DynamicConfig.GetValue<TimeSpan>("AccountListPreloadMaxAdditionalWaitTimespanFromPreload", TimeSpan.FromSeconds(2));
        public static TimeSpan AccountListPreloadMaxAdditionalWaitTimespanFromFetch => DynamicConfig.GetValue<TimeSpan>("AccountListPreloadMaxAdditionalWaitTimespanFromFetch", TimeSpan.FromSeconds(1));
        public static TimeSpan AccountListPreloadAdditionalWaitPollIntervalTimespan => DynamicConfig.GetValue<TimeSpan>("AccountListPreloadAdditionalWaitPollIntervalTimespan", TimeSpan.FromMilliseconds(100));
        public static bool AccountListPreloadEnabled => DynamicConfig.GetValue<bool>("AccountListPreloadEnabled", true);
        public static bool UseGetAccountsPropertiesV2 => DynamicConfig.GetValue<bool>("UseGetAccountsPropertiesV2", true);

        public static bool CleanupCampaignUpdate => DynamicConfig.GetValue<bool>("CleanupCampaignUpdate", true);

        public static TimeSpan MultiAccountOverviewPreloadMaxAge => DynamicConfig.GetValue<TimeSpan>("MultiAccountOverviewPreloadMaxAge", TimeSpan.FromSeconds(2));
        public static TimeSpan MultiAccountOverviewPreloadWaitPollInterval => DynamicConfig.GetValue<TimeSpan>("MultiAccountOverviewPreloadWaitPollInterval", TimeSpan.FromMilliseconds(100));
        public static TimeSpan MultiAccountOverviewPreloadWaitPollTimeout => DynamicConfig.GetValue<TimeSpan>("MultiAccountOverviewPreloadWaitPollTimeout", TimeSpan.FromSeconds(2));

        public static TimeSpan CampaignOverviewPreloadMaxAge => DynamicConfig.GetValue<TimeSpan>("CampaignOverviewPreloadMaxAge", TimeSpan.FromSeconds(2));
        public static TimeSpan CampaignOverviewPreloadWaitPollInterval => DynamicConfig.GetValue<TimeSpan>("CampaignOverviewPreloadWaitPollInterval", TimeSpan.FromMilliseconds(100));
        public static TimeSpan CampaignOverviewPreloadWaitPollTimeout => DynamicConfig.GetValue<TimeSpan>("CampaignOverviewPreloadWaitPollTimeout", TimeSpan.FromSeconds(2));

        public static bool MultiAccountRecommendationPreloadEnabled => DynamicConfig.GetValue<bool>("MultiAccountRecommendationPreloadEnabled", true);
        public static TimeSpan MultiAccountRecommendationPreloadMaxAge => DynamicConfig.GetValue<TimeSpan>("MultiAccountRecommendationPreloadMaxAge", TimeSpan.FromSeconds(2));
        public static TimeSpan MultiAccountRecommendationPreloadWaitPollInterval => DynamicConfig.GetValue<TimeSpan>("MultiAccountRecommendationPreloadWaitPollInterval", TimeSpan.FromMilliseconds(100));
        public static TimeSpan MultiAccountRecommendationPreloadWaitPollTimeoutFromPreload => DynamicConfig.GetValue<TimeSpan>("MultiAccountRecommendationPreloadWaitPollTimeoutFromPreload", TimeSpan.FromSeconds(2));
        public static TimeSpan MultiAccountRecommendationPreloadWaitPollTimeoutFromFetch => DynamicConfig.GetValue<TimeSpan>("MultiAccountRecommendationPreloadWaitPollTimeoutFromFetch", TimeSpan.FromSeconds(1));

        public static bool ODataReturnCacheObjectUsed => DynamicConfig.GetValue<bool>("ODataReturnCacheObjectUsed", false);
        public static List<string> LogAdditionalODataRoutingDebugTracesForMethods => DynamicConfig.GetListValues("LogAdditionalODataRoutingDebugTracesForMethods");

        public static bool CampaignPreLoadEnabled => DynamicConfig.GetValue<bool>("CampaignPreLoadEnabled", false);
        public static bool CampaignPreLoadUsePageTrackingId => DynamicConfig.GetValue<bool>("CampaignPreLoadUsePageTrackingId", false);
        public static TimeSpan CampaignPreLoadMaxAgeTimespan => DynamicConfig.GetValue<TimeSpan>("CampaignPreLoadMaxAgeTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan CampaignPreLoadMaxAdditionalWaitTimespan => DynamicConfig.GetValue<TimeSpan>("CampaignPreLoadMaxAdditionalWaitTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan CampaignPreLoadCacheObjectTtlTimespan => DynamicConfig.GetValue<TimeSpan>("CampaignPreLoadCacheObjectTtlTimespan", TimeSpan.FromSeconds(10));
        public static TimeSpan CampaignPreLoadAdditionalWaitPollIntervalTimespan => DynamicConfig.GetValue<TimeSpan>("CampaignPreLoadAdditionalWaitPollIntervalTimespan", TimeSpan.FromMilliseconds(100));

        public static bool KeywordPreLoadEnabled => DynamicConfig.GetValue<bool>("KeywordPreLoadEnabled", false);
        public static TimeSpan KeywordPreLoadMaxAgeTimespan => DynamicConfig.GetValue<TimeSpan>("KeywordPreLoadMaxAgeTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan KeywordPreLoadMaxAdditionalWaitTimespan => DynamicConfig.GetValue<TimeSpan>("KeywordPreLoadMaxAdditionalWaitTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan KeywordPreLoadCacheObjectTtlTimespan => DynamicConfig.GetValue<TimeSpan>("KeywordPreLoadCacheObjectTtlTimespan", TimeSpan.FromSeconds(10));
        public static TimeSpan KeywordPreloadAdditionalWaitPollIntervalTimespan => DynamicConfig.GetValue<TimeSpan>("KeywordPreloadAdditionalWaitPollIntervalTimespan", TimeSpan.FromMilliseconds(100));

        public static bool SearchTermPreLoadEnabled => DynamicConfig.GetValue<bool>("SearchTermPreLoadEnabled", false);
        public static TimeSpan SearchTermPreLoadMaxAgeTimespan => DynamicConfig.GetValue<TimeSpan>("SearchTermPreLoadMaxAgeTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan SearchTermPreLoadMaxAdditionalWaitTimespan => DynamicConfig.GetValue<TimeSpan>("SearchTermPreLoadMaxAdditionalWaitTimespan", TimeSpan.FromSeconds(2));
        public static TimeSpan SearchTermPreLoadCacheObjectTtlTimespan => DynamicConfig.GetValue<TimeSpan>("SearchTermPreLoadCacheObjectTtlTimespan", TimeSpan.FromSeconds(10));
        public static TimeSpan SearchTermPreLoadAdditionalWaitPollIntervalTimespan => DynamicConfig.GetValue<TimeSpan>("SearchTermPreLoadAdditionalWaitPollIntervalTimespan", TimeSpan.FromMilliseconds(100));

        public static TimeSpan InlineChartTimeout
        {
            get { return DynamicConfig.GetRequiredValue<TimeSpan>("InlineChartTimeout"); }
        }

        public static HashSet<string> CampaignApiCustomerIdBlackList
        {
            get
            {
                var nodes = DynamicConfig.GetValue("CampaignApiCustomerIdBlackList", new ReadOnlyCollection<string>(new List<string>()));
                var set = new HashSet<string>();
                nodes.ForEach(s => set.Add(s));
                return set;
            }
        }

        public static HashSet<string> ACSTestPhoneNumbers
        {
            get
            {
                var nodes = DynamicConfig.GetValue("ACSTestPhoneNumbers", new ReadOnlyCollection<string>(new List<string>()));
                var set = new HashSet<string>();
                nodes.ForEach(s => set.Add(s));
                return set;
            }
        }

        public static HashSet<string> NodeBlackList
        {
            get
            {
                var nodes = DynamicConfig.GetValue("NodeBlackList", new ReadOnlyCollection<string>(new List<string>()));
                var set = new HashSet<string>();
                nodes.ForEach(s => set.Add(s.Trim().ToLowerInvariant()));
                return set;
            }
        }

        public static int MaxAllowedBadNodeCount
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("MaxAllowedBadNodeCount");
            }
        }

        public static bool AllowPassingTokensToStartAdWordsSession
        {
            get { return DynamicConfig.GetRequiredValue<bool>("AllowPassingTokensToStartAdWordsSession"); }
        }

        public static bool AllowPassingTokensToStartAmazonSession
        {
            get { return DynamicConfig.GetRequiredValue<bool>("AllowPassingTokensToStartAmazonSession"); }
        }

        public static bool AllowPassingTokensToStartPinterestSession
        {
            get { return DynamicConfig.GetRequiredValue<bool>("AllowPassingTokensToStartPinterestSession"); }
        }

        public static bool AllowPassingTokensToStartFacebookSession
        {
            get { return DynamicConfig.GetRequiredValue<bool>("AllowPassingTokensToStartFacebookSession"); }
        }

        public static bool AllowEmptyEmailInFacebookSession
        {
            get { return DynamicConfig.GetRequiredValue<bool>("AllowEmptyEmailInFacebookSession"); }
        }

        public static bool EnableAccountsSummaryVerboseLogging
        {
            get { return DynamicConfig.GetValue<bool>("EnableAccountsSummaryVerboseLogging", false); }
        }

        public static bool EnableSOVVerboseLogging
        {
            get { return DynamicConfig.GetValue<bool>("EnableSOVVerboseLogging", false); }
        }


        public static int ExtaPathRegexVersion => DynamicConfig.GetValue<int>("ExtaPathRegexVersion", 0);

        public static string GoogleImportCampaignIdsContainerName
        {
            get { return DynamicConfig.GetRequiredValue<string>("GoogleImportCampaignIdsContainerName"); }
        }

        public static int MaxGetImportJobsCount => DynamicConfig.GetValue<int>("MaxGetImportJobsCount", 500);

        public static string FileImportUploadUrlFileExtension
        {
            get { return DynamicConfig.GetRequiredValue<string>("FileImportUploadUrlFileExtension"); }
        }

        public static string OutOfServiceFilePath
        {
            get { return (CrossPlatformHelpers.RunningInContainer) ? DynamicConfig.GetValue("OutOfServiceFilePathLinux", @"/app/OutOfService") : DynamicConfig.GetValue("OutOfServiceFilePath", @"d:\data\CampaignMiddleTier\OutOfService.txt"); }
        }

        public static string LinuxCPUFilePath
        {
            get { return DynamicConfig.GetValue("LinuxCPUFilePath", @"/app/cpuUtilization"); }
        }

        public static string ScheduledImportDataContainerName => DynamicConfig.GetValue("ScheduledImportDataContainerName", "gischeduledata");

        public static string LegacyDSAAdIdsContainerName => DynamicConfig.GetValue("LegacyDSAAdIdsContainerName", "dsaadids");

        public static string RunNowImportDataContainerName => DynamicConfig.GetValue("RunNowImportDataContainerName", "girunnowdata");

        public static string SyncCentricContainerName => DynamicConfig.GetValue("SyncCentricContainerName", "azsynccentricdata");

        public static bool UseSnapshotAsDefaultMethod => DynamicConfig.GetValue("UseSnapshotAsDefaultMethod", false);

        public static int AmazonRequestPageSize => DynamicConfig.GetValue("AmazonRequestPageSize", 5000);

        public static int SyncCentricCustomerId => DynamicConfig.GetValue("SyncCentricCustomerId", 0);

        public static int SyncCentricUserId => DynamicConfig.GetValue("SyncCentricUserId", 0);

        public static string AmazonLookupTableName => DynamicConfig.GetValue("AmazonLookupTableName", "AmazonIdLookup");

        public static string AZImportSyncCentricAccounts => DynamicConfig.GetValue("AZImportSyncCentricAccounts", string.Empty);


        public static int SplitContainerForMultiAccountImportAccount => DynamicConfig.GetValue("SplitContainerForMultiAccountImportAccount", 0);

        public static string ConnectionDrainPath => DynamicConfig.GetValue("ConnectionDrainPath", @"D:\data\ConnectionDrain\ConnectionDrain.txt");

        public static string MachineInfoCsvPath => DynamicConfig.GetValue("MachineInfoCsvPath", @"d:\data\machineinfo.csv");

        public static IEnumerable<string> AggregatorServiceMachineFunctionNames => DynamicConfig.GetValue("AggregatorServiceMachineFunctionNames", new ReadOnlyCollection<string>(new List<string>()));

        public static IEnumerable<string> AggregatorServiceVIPForMachineFunctions => DynamicConfig.GetValue("CampaignMTVIPForMachineFunctions", new ReadOnlyCollection<string>(new List<string>()));

        public static IEnumerable<string> DistributedQueryServiceMachineFunctionNames => DynamicConfig.GetValue("DistributedQueryServiceMachineFunctionNames", new ReadOnlyCollection<string>(new List<string>()));
        public static bool DistributedQueryServiceUseEndPointWatcher => DynamicConfig.GetValue("DistributedQueryServiceUseEndPointWatcher", false);
        public static bool DistributedQueryServiceUseServiceHealth => DynamicConfig.GetValue("DistributedQueryServiceUseServiceHealth", false);

        public static bool EnableServerRedirect => DynamicConfig.GetValue<bool>("EnableServerRedirect", true);

        public static string CampaignMtProbeUrlTemplate => DynamicConfig.GetValue("CampaignMtProbeUrlTemplate", @"http://{0}:10094/CampaignMT/v6/Diagnostics/HealthDiagCampaignService.svc/Probe");

        public static string DistributedQueryProbeUrlTemplate => DynamicConfig.GetValue("DistributedQueryProbeUrlTemplate", @"http://{0}:5000/api/Evt/Ready");

        public static int LoadBalancerProbeTimeoutMs => DynamicConfig.GetValue<int>("LoadBalancerProbeTimeoutMs", 500);

        public static string AdWordsTokensKeyVaultUrl => DynamicConfig.GetValue("AdWordsTokensKeyVaultUrl", string.Empty);

        public static bool CacheDecryptedValues => DynamicConfig.GetValue("CacheDecryptedValues", false);

        public static bool LoadShardGroupPartitionFromService => DynamicConfig.GetValue("LoadShardGroupPartitionFromService", false);

        public static bool LoadTimeStampsFromMetadataService => DynamicConfig.GetValue("LoadTimeStampsFromMetadataService", false);

        public static string GsyncClientId => DynamicConfig.GetValue("GsyncClientId", string.Empty);

        public static string GsyncClientSecret => DynamicConfig.GetValue("GsyncClientSecret", string.Empty);

        public static string GoogleMyBusinessAPIScope => DynamicConfig.GetValue("GoogleMyBusinessAPIScope", string.Empty);

        public static string GoogleMyBusinessClientId => DynamicConfig.GetValue("GoogleMyBusinessClientId", string.Empty);

        public static string GoogleMyBusinessClientSecret => DynamicConfig.GetValue("GoogleMyBusinessClientSecret", string.Empty);

        public static string GTMClientId => DynamicConfig.GetValue("GTMClientId", string.Empty);

        public static string GTMClientSecret => DynamicConfig.GetValue("GTMClientSecret", string.Empty);

        public static string GTMClientId2 => DynamicConfig.GetValue("GTMClientId2", string.Empty);

        public static string GTMClientSecret2 => DynamicConfig.GetValue("GTMClientSecret2", string.Empty);

        public static bool GTMBatchImport => DynamicConfig.GetValue("GTMBatchImport", true);

        public static int GTMBatchSize => DynamicConfig.GetValue("GTMBatchSize", 10);

        public static int GTMImportTaskIntervalInSeconds => DynamicConfig.GetValue("GTMImportTaskIntervalInSeconds", 120);

        public static int GTMBatchImportIntervalInMs => DynamicConfig.GetValue("GTMBatchImportIntervalInMs", 5000);

        public static bool GTMIsSupportGoogleAdsConversionTrackingTag => DynamicConfig.GetValue("GTMIsSupportGoogleAdsConversionTrackingTag", false);

        public static string GTMStagingFolder => DynamicConfig.GetValue("GTMStagingFolder", string.Empty);

        public static string GTMImportCacheFolder => DynamicConfig.GetValue("GTMImportCacheFolder", string.Empty);

        public static bool GTMImportMultiTasks => DynamicConfig.GetValue("GTMImportMultiTasks", true);

        public static string BingPlacesClientId => DynamicConfig.GetValue("BingPlacesClientId", string.Empty);

        public static string BingPlacesClientSecret => DynamicConfig.GetValue("BingPlacesClientSecret", string.Empty);

        public static string BingPlacesCodeVerifier => DynamicConfig.GetValue("BingPlacesCodeVerifier", string.Empty);

        public static string BingPlacesGetBusinessesEndpoint => DynamicConfig.GetValue("BingPlacesGetBusinessesEndpoint", string.Empty);

        public static string BingPlacesApiRootUrl => DynamicConfig.GetValue("BingPlacesApiRootUrl", string.Empty);

        public static string BingPlacesTokenUrl => DynamicConfig.GetValue("BingPlacesTokenUrl", string.Empty);

        public static bool BingPlacesMSASignup => DynamicConfig.GetValue("BingPlacesMSASignup", false);

        public static string BingPlacesMSASignUpClientId => DynamicConfig.GetValue("BingPlacesMSASignUpClientId", string.Empty);

        public static string BingPlacesMSAKeyVaultName => DynamicConfig.GetValue("BingPlacesMSAKeyVaultName", string.Empty);

        public static string BingPlacesMSAKeyVaultRunAs => DynamicConfig.GetValue("BingPlacesMSAKeyVaultRunAs", string.Empty);

        public static string BAEGsyncClientId => DynamicConfig.GetValue("BAEGsyncClientId", string.Empty);

        public static string BAEGsyncClientSecret => DynamicConfig.GetValue("BAEGsyncClientSecret", string.Empty);

        public static string BAEGsyncClientId2 => DynamicConfig.GetValue("BAEGsyncClientId2", string.Empty);

        public static string GoogleOAuthScope => DynamicConfig.GetValue("GoogleOAuthScope", string.Empty);

        public static string BAEGsyncClientSecret2 => DynamicConfig.GetValue("BAEGsyncClientSecret2", string.Empty);

        public static string BAEGsyncDevToken => DynamicConfig.GetValue("BAEGsyncDevToken", string.Empty);

        public static bool RedeemCouponPostTaskCreation => DynamicConfig.GetValue("RedeemCouponPostTaskCreation", false);

        public static string AmazonImportClientId => DynamicConfig.GetValue("AmazonImportClientId", string.Empty);

        public static string AmazonImportClientSecret => DynamicConfig.GetValue("AmazonImportClientSecret", string.Empty);

        public static string AmazonImportApiVersion => DynamicConfig.GetValue<string>("AmazonImportApiVersion", "v2");

        public static int AmazonSnapshotMaxDelayInSeconds => DynamicConfig.GetValue("AmazonSnapshotMaxDelayInSeconds", 30);

        public static int AmazonSnapshotTimeoutInSeconds => DynamicConfig.GetValue("AmazonSnapshotTimeoutInSeconds", 900);

        public static string SyncCentricApiVersion => DynamicConfig.GetValue<string>("SyncCentricApiVersion", "ver1");

        public static int SyncCentricPollMaxDelayInSeconds => DynamicConfig.GetValue("SyncCentricPollMaxDelayInSeconds", 30);

        public static int SyncCentricPollTimeoutInSeconds => DynamicConfig.GetValue("SyncCentricPollTimeoutInSeconds", 900);

        public static IReadOnlyDictionary<string, string> SyncCentricApiKeys => DynamicConfig.GetMapValues("SyncCentricApiKeys");

        public static string AzSyncStagingFolder => DynamicConfig.GetValue<string>("AzSyncStagingFolder", string.Empty);

        public static string PinterestSyncStagingFolder => DynamicConfig.GetValue<string>("PinterestSyncStagingFolder", string.Empty);

        public static string FacebookImportAppId => DynamicConfig.GetValue("FacebookImportAppId", string.Empty);

        public static string FacebookImportAppSecret => DynamicConfig.GetValue("FacebookImportAppSecret", string.Empty);

        public static string FbSyncStagingFolder => DynamicConfig.GetValue("FbSyncStagingFolder", string.Empty);

        public static TimeSpan DefaultRequestPipelineTimeout => DynamicConfig.GetValue("DefaultRequestPipelineTimeout", TimeSpan.FromSeconds(60));

        public static string PinterestImportClientId => DynamicConfig.GetValue("PinterestImportClientId", string.Empty);

        public static string PinterestImportClientSecret => DynamicConfig.GetValue("PinterestImportClientSecret", string.Empty);

        public static int PinterestIDTableRetryCount => DynamicConfig.GetValue("PinterestIDTableRetryCount", 3);

        public static int PinterestIDTableInitialInterval => DynamicConfig.GetValue("PinterestIDTableInitialInterval", 1);

        public static int PinterestIDTableIncrementalInverval => DynamicConfig.GetValue("PinterestIDTableIncrementalInverval", 2);
        public static string PinterestIDTableName => DynamicConfig.GetValue("PinterestIDTableName", "PinterestIDTable");

        public static IReadOnlyDictionary<long, int> PinterestImageDownloadLimit => GetIntValueByLongKey("PinterestImageDownloadLimit");

        public static bool EnablePinterestForeverRefreshToken => DynamicConfig.GetValue("EnablePinterestForeverRefreshToken", false);

        public static int AppInstallAdAppNameMaxLength
        {
            get
            {
                return DynamicConfig.GetValue("AppInstallAdAppNameMaxLength", 0);
            }
        }

        public static int DbPingSampleSizePercentage => DynamicConfig.GetValue<int>("DbPingSampleSizePercentage", 5);



        public static int DaysToDisplayHistoryImportsInUI => DynamicConfig.GetValue<int>("DaysToDisplayHistoryImportsInUI", 90);

        public static bool MultiAccountGoogleImport => DynamicConfig.GetValue<bool>("MultiAccountGoogleImport", false);

        public static bool FailBulkIfExecutingTaskIsInFinalState
        {
            get
            {
                return DynamicConfig.GetValue<bool>("FailBulkIfExecutingTaskIsInFinalState", false);
            }
        }
        public static bool FailOfflineConversionIfExecutingTaskIsInFinalState
        {
            get
            {
                return DynamicConfig.GetValue<bool>("FailOfflineConversionIfExecutingTaskIsInFinalState", false);
            }
        }

        public static int AzureUploadRetryCount => DynamicConfig.GetValue<int>("AzureUploadRetryCount", 3);
        public static int AzureUploadRetryInitialInterval => DynamicConfig.GetValue<int>("AzureUploadRetryInitialInterval", 2);
        public static int AzureUploadRetryIncrementalInterval => DynamicConfig.GetValue<int>("AzureUploadRetryIncrementalInterval", 5);

        public static int AudienceRetryPolicyMaxCount => DynamicConfig.GetValue<int>("AudienceRetryPolicyMaxCount", 3);

        public static int AudienceRetryPolicyInitialInterval => DynamicConfig.GetValue<int>("AudienceRetryPolicyInitialInterval", 1);

        public static int AudienceRetryPolicyIncrement => DynamicConfig.GetValue<int>("AudienceRetryPolicyIncrement", 1);

        public static int GTMRetryPolicyMaxCount => DynamicConfig.GetValue<int>("GTMRetryPolicyMaxCount", 3);

        public static int GTMRetryPolicyInitialInterval => DynamicConfig.GetValue<int>("GTMRetryPolicyInitialInterval", 10);

        public static int GTMRetryPolicyIncrement => DynamicConfig.GetValue<int>("GTMRetryPolicyIncrement", 10);

        public static int UETRetryPolicyMaxCount => DynamicConfig.GetValue<int>("UETRetryPolicyMaxCount", 3);

        public static int UETRetryPolicyInitialInterval => DynamicConfig.GetValue<int>("UETRetryPolicyInitialInterval", 1);
        public static int UETClarityDeleteForSI => DynamicConfig.GetValue<int>("UETClarityDeleteForSI", 1);

        public static int UETRetryPolicyIncrement => DynamicConfig.GetValue<int>("UETRetryPolicyIncrement", 1);

        public static bool EnableMultiAgency => DynamicConfig.GetValue<bool>("EnableMultiAgency", true);

        public static bool EnableUPUetLink => DynamicConfig.GetValue<bool>("EnableUPUetLink", false);

        public static bool ProvisionClarityEnabled => DynamicConfig.GetValue<bool>("ProvisionClarityEnabled", false);

        public static bool UETClaritySNI => DynamicConfig.GetValue<bool>("UETClaritySNI", false);

        public static string ImportAzureStorageConnectionString => DynamicConfig.GetRequiredValue<string>("ImportAzureStorageConnectionString");

        public static string ImportAdsAppsStorageConnectionString => DynamicConfig.GetRequiredValue<string>("ImportAdsAppsStorageConnectionString");

        public static string ImportStoragePMEConnectionString => DynamicConfig.GetRequiredValue<string>("ImportStoragePMEConnectionString");

        public static string ImportPMEAzureStorageAccountName => DynamicConfig.GetRequiredValue<string>("ImportPMEAzureStorageAccountName");

        public static string SmartPageAzureStorageConnectionString => DynamicConfig.GetRequiredValue<string>("SmartPageAzureStorageConnectionString");

        public static bool SmartPageUseManagedIdentity => DynamicConfig.GetRequiredValue<bool>("SmartPageUseManagedIdentity");

        public static string SmartPageBlobStorageAccountName => DynamicConfig.GetRequiredValue<string>("SmartPageBlobStorageAccountName");

        public static string SmartPageTableStorageAccountName => DynamicConfig.GetRequiredValue<string>("SmartPageTableStorageAccountName");

        public static string SmartPageCertKeyName => DynamicConfig.GetRequiredValue<string>("SmartPageCertKeyName");

        public static string SmartPageM365Text2ImageAnalyzerEndpoint => DynamicConfig.GetRequiredValue<string>("SmartPageM365Text2ImageAnalyzerEndpoint");

        public static string MessageCenterMTAddress => DynamicConfig.GetRequiredValue<string>("MessageCenterMTAddress");

        public static string MessageCenterClientId => DynamicConfig.GetRequiredValue<string>("MessageCenterClientId");

        public static string MessageCenterClientSecret => DynamicConfig.GetRequiredValue<string>("MessageCenterClientSecret");

        public static string MessageCenterClientCertName => DynamicConfig.GetRequiredValue<string>("MessageCenterClientCertName");

        public static string MessageCenterTenantId => DynamicConfig.GetRequiredValue<string>("MessageCenterTenantId");

        public static string MessageCenterAuthorityUri => DynamicConfig.GetRequiredValue<string>("MessageCenterAuthorityUri");

        public static string MessageCenterScope => DynamicConfig.GetRequiredValue<string>("MessageCenterScope");

        public static string M365AdminServerAppId => DynamicConfig.GetRequiredValue<string>("M365AdminServerAppId");

        public static string M365AdminServerAppCertKeyName => DynamicConfig.GetRequiredValue<string>("M365AdminServerAppCertKeyName");

        public static string M365AdminAuthorityUri => DynamicConfig.GetRequiredValue<string>("M365AdminAuthorityUri");

        public static string M365AdminApiEndpoint => DynamicConfig.GetRequiredValue<string>("M365AdminApiEndpoint");

        public static string M365AdminApiScope => DynamicConfig.GetRequiredValue<string>("M365AdminApiScope");

        public static string SmartPageKeyVaultName => DynamicConfig.GetRequiredValue<string>("SmartPageKeyVaultName");

        public static string SmartPageKeyVaultRunAs => DynamicConfig.GetRequiredValue<string>("SmartPageKeyVaultRunAs");
        public static string SmartPageAcmeClientPemKey => DynamicConfig.GetRequiredValue<string>("SmartPageAcmeClientPemKey");

        public static string SmartPageCustomDomainDNSVaule => DynamicConfig.GetRequiredValue<string>("SmartPageCustomDomainDNSVaule");

        public static bool EnableSmartPageAssociatedCampaignStatus => DynamicConfig.GetValue<bool>("EnableSmartPageAssociatedCampaignStatus", false);

        public static bool EnableSmartPageLeadsTracking => DynamicConfig.GetValue<bool>("EnableSmartPageLeadsTracking", false);

        public static bool EnableSmartPageCustomDomain => DynamicConfig.GetValue<bool>("EnableSmartPageCustomDomain", false);

        public static bool QueueCustomDomainOfflineTasks => DynamicConfig.GetValue<bool>("QueueCustomDomainOfflineTasks", false);

        public static bool QueueSubdomainOfflineTasks => DynamicConfig.GetValue<bool>("QueueSubdomainOfflineTasks", false);

        public static bool EnableSmartPageM365Cert => DynamicConfig.GetValue<bool>("EnableSmartPageM365Cert", false);

        public static bool EnvironmentTrafficPilotingEnabled => DynamicConfig.GetValue<bool>("EnvironmentTrafficPilotingEnabled", false);



        public static int ODataCorePortForSameLDC => DynamicConfig.GetValue<int>("ODataCorePortForSameLDC", 8080);

        public static List<string> CountriesToDisablePhoneNumberFormatting
        {
            get
            {
                return DynamicConfig.GetListValues("CountriesToDisablePhoneNumberFormatting");
            }
        }


        public static HashSet<long> FetchMMCStoreIdFromAzureTableAccountIdWhiteList => new HashSet<long>(DynamicConfig.GetValue("FetchMMCStoreIdFromAzureTableAccountIdWhiteList", EmptyLongList));


        public static string ImportFailureDetailTableName => DynamicConfig.GetRequiredValue<string>("ImportFailureDetailTableName");

        public static string MultiAccountGoogleImportTaskCheckPointAzureTableName =>
            DynamicConfig.GetRequiredValue<string>("MultiAccountGoogleImportTaskCheckPointAzureTableName");

        public static string MultiAccountGoogleImportUserPreferenceAzureTableName => DynamicConfig.GetRequiredValue<string>("MultiAccountGoogleImportUserPreferenceAzureTableName");

        public static string MultiAccountGoogleImportScheduleInfoAzureTableName => DynamicConfig.GetRequiredValue<string>("MultiAccountGoogleImportScheduleInfoAzureTableName");

        public static string ExternalSessionTableName =>
            DynamicConfig.GetRequiredValue<string>("ExternalSessionTableName");

        public static string LastImportTableName => DynamicConfig.GetRequiredValue<string>("LastImportTableName");

        public static string LastFullySyncedScheduleImportTableName => DynamicConfig.GetRequiredValue<string>("LastFullySyncedScheduleImportTableName");

        public static string MMCStoreCountryTableName => DynamicConfig.GetRequiredValue<string>("MMCStoreCountryTableName");

        public static bool EnablePMaxRecommendationImportNotification => DynamicConfig.GetValue<bool>("EnablePMaxRecommendationImportNotification", false);

        public static bool EnableImportErrorSolution => DynamicConfig.GetValue<bool>("EnableImportErrorSolution", false);

        public static string GetLegacyAdWordsDSATableName => DynamicConfig.GetRequiredValue<string>("GetLegacyAdWordsDSATableName");

        public static bool ExcludeDSAFromAdDeletion => DynamicConfig.GetRequiredValue<bool>("ExcludeDSAFromAdDeletion");

        public static string LoginCustomerIdTableName => DynamicConfig.GetValue<string>("LoginCustomerIdTableName", "LoginCustomerId");

        public static string ImportTaskFeedbackTableName => DynamicConfig.GetRequiredValue<string>("ImportTaskFeedbackTableName");

        public static string ImportAppMappingTableName => DynamicConfig.GetRequiredValue<string>("ImportAppMappingTableName");

        public static string ImportLocationCacheTableName => DynamicConfig.GetValue<string>("ImportLocationCacheTableName", "LocationAdExtensionCache");

        public static int GetTimeZoneForLocationAdExtensionPercentage => DynamicConfig.GetValue<int>("GetTimeZoneForLocationAdExtensionPercentage", 0);

        public static bool EnableSkipTrackingSettingImportForTPManagedAccounts(CampaignManagementRequest request)
        {
            return GetValue("EnableSkipTrackingSettingImportForTPManagedAccounts", request?.OverrideConfigValuesFromTest, false);
        }

        public static int PercentAccountsForEnableSkipTrackingSettingImport => DynamicConfig.GetValue("PercentAccountsForEnableSkipTrackingSettingImport", 0);

        public static ReadOnlyCollection<string> SkipTrackingSettingImportDevTokenList => DynamicConfig.GetValue<ReadOnlyCollection<string>>("SkipTrackingSettingImportDevTokenList", new ReadOnlyCollection<string>(new List<string>()));

        public static string AccountDevTokenMappingForImportTableName => DynamicConfig.GetRequiredValue<string>("AccountDevTokenMappingForImportAzureTableName");

        public static int AccountDevTokenMappingTimeWindowInDays => DynamicConfig.GetRequiredValue<int>("AccountDevTokenMappingTimeWindowInDays");

        public static bool MultiAccountGoogleImportUserPreferenceAzureTableEnabled => DynamicConfig.GetRequiredValue<bool>("MultiAccountGoogleImportUserPreferenceAzureTableEnabled");

        public static int LastImportQueryBatchSize => DynamicConfig.GetValue<int>("LastImportQueryBatchSize", 250);

        public static int ImportAzureTableRetryCount => DynamicConfig.GetValue<int>("ImportAzureTableRetryCount", 3);

        public static int ImportAzureTableInitialInterval => DynamicConfig.GetValue<int>("ImportAzureTableInitialInterval", 1);

        public static int ImportAzureTableIncrementalInterval => DynamicConfig.GetValue<int>("ImportAzureTableIncrementalInterval", 1);

        public static string CategoriesExcludedFromEVT => DynamicConfig.GetRequiredValue<string>("CategoriesExcludedFromEVT");

        public static int EVTRetryTimes => DynamicConfig.GetValue<int>("EVTRetryTimes", 0);

        public static bool ShouldReturnTrueForDisableSTAPilot => DynamicConfig.GetValue("ShouldReturnTrueForDisableSTAPilot", false);

        public static bool ShouldReturnTrueForDisableEXTAPilot => DynamicConfig.GetValue("ShouldReturnTrueForDisableEXTAPilot", false);

        public static bool ShouldIgnoreSTAPilots => DynamicConfig.GetValue("ShouldIgnoreSTAPilots", false);

        public static bool ShouldIgnoreEXTAPilots => DynamicConfig.GetValue("ShouldIgnoreEXTAPilots", true);

        public static bool ShouldIgnoreAdPreferenceUpdate => DynamicConfig.GetValue("ShouldIgnoreAdPreferenceUpdate", true);

        public static bool EnableNewEVTDatabasePing => DynamicConfig.GetValue("EnableNewEVTDatabasePing", true);
        public static string EVTConfigRetryTests => DynamicConfig.GetValue("EVTConfigRetryTests", string.Empty);

        public static string TestsExcludedFromEVT => DynamicConfig.GetRequiredValue<string>("TestsExcludedFromEVT");

        public static bool FilterAdGroupBeforeLoadingTargetsFromCampaignDB
            => DynamicConfig.GetRequiredValue<bool>("FilterAdGroupBeforeLoadingTargetsFromCampaignDB");

        public static TimeSpan AdIntelligenceClientTimeout
            => DynamicConfig.GetValue<TimeSpan>("AdIntelligenceClientTimeout", TimeSpan.FromSeconds(3));

        public static int MultiAccountRecommendationAccountsCapping
            => DynamicConfig.GetValue<int>("MultiAccountRecommendationAccountsCapping", 2000);

        public static TimeSpan MultiAccountRecommendationAccountsTimeout
            => DynamicConfig.GetValue<TimeSpan>("MultiAccountRecommendationAccountsTimeout", TimeSpan.FromMinutes(2));

        public static TimeSpan ShoppingCampaignDeliveryStatusTimeout
            => DynamicConfig.GetValue<TimeSpan>("ShoppingCampaignDeliveryStatusTimeout", TimeSpan.FromSeconds(3));

        public static TimeSpan EstimatedSearchTermsCountTimeout
            => DynamicConfig.GetValue<TimeSpan>("EstimatedSearchTermsCountTimeout", TimeSpan.FromMilliseconds(500));

        public static TimeSpan BICacheDefaultTimeToLive => DynamicConfig.GetValue<TimeSpan>("BICacheDefaultTimeToLive", TimeSpan.FromHours(1));

        public static TimeSpan SearchTermsBICacheDefaultTimeToLive => DynamicConfig.GetValue<TimeSpan>("SearchTermsBICacheDefaultTimeToLive", TimeSpan.FromMinutes(15));

        public static TimeSpan ReportPreviewBICacheDefaultTimeToLive => DynamicConfig.GetValue<TimeSpan>("ReportPreviewBICacheDefaultTimeToLive", TimeSpan.FromHours(1));

        public static TimeSpan BICacheFastBITimeToLive => DynamicConfig.GetValue<TimeSpan>("BICacheFastBITimeToLive", TimeSpan.FromMinutes(5));

        public static TimeSpan PagingCacheDefaultTimeToLive => DynamicConfig.GetValue<TimeSpan>("PagingCacheDefaultTimeToLive", TimeSpan.FromHours(1));

        public static TimeSpan BICacheFetchTimeout => DynamicConfig.GetValue<TimeSpan>("BICacheFetchTimeout", TimeSpan.FromSeconds(1));

        public static TimeSpan BICacheFetchTimeoutForScripts => DynamicConfig.GetValue<TimeSpan>("BICacheFetchTimeoutForScripts", TimeSpan.FromSeconds(2));

        public static TimeSpan BICacheFetchTimeoutForReportPreview => DynamicConfig.GetValue<TimeSpan>("BICacheFetchTimeoutForReportPreview", TimeSpan.FromSeconds(5));

        public static int BatchSizeToCheckImportStatus => DynamicConfig.GetRequiredValue<int>("BatchSizeToCheckImportStatus");


        public static Dictionary<string, string> TokenExpiryDaysByImportScheduledType
            => DynamicConfig.GetMapValues("TokenExpiryDaysByImportScheduledType");

        public static bool EnableBatchDownloadProductPartition => DynamicConfig.GetValue("EnableBatchDownloadProductPartition", false);

        public static int BulkDownloadLastSyncTimeValidPeriodInDays => DynamicConfig.GetRequiredValue<int>("BulkDownloadLastSyncTimeValidPeriodInDays");

        public static string ParsingErrorFileContainerName
            => DynamicConfig.GetRequiredValue<string>("ParsingErrorFileContainerName");

        public static string ParsingErrorFileWithPartitionContainerName
            => DynamicConfig.GetRequiredValue<string>("ParsingErrorFileWithPartitionContainerName");

        public static string ImportBulkUploadResWithPartitionContainerName
            => DynamicConfig.GetRequiredValue<string>("ImportBulkUploadResWithPartitionContainerName");

        public static string BackupGoogleEntityIdContainerName
            => DynamicConfig.GetRequiredValue<string>("BackupGoogleEntityIdContainerName");

        public static bool EnableBackupGoogleEntityIdInBlob => DynamicConfig.GetRequiredValue<bool>("EnableBackupGoogleEntityIdInBlob");

        public static int ParsingErrorWriteBatchSize
            => DynamicConfig.GetRequiredValue<int>("ParsingErrorWriteBatchSize");

        public static int ClickToCallBISyncBatchSize => DynamicConfig.GetValue("ClickToCallBISyncBatchSize", 1000);

        public static int ClickToCallBISyncDBTimeoutInSec => DynamicConfig.GetValue("ClickToCallBISyncDBTimeoutInSec", 60);

        public static int SkypeReprovisionBatchSize => DynamicConfig.GetValue("SkypeReprovisionBatchSize", 10);

        public static int SkypeReprovisionBatchLimitSize => DynamicConfig.GetValue("SkypeReprovisionBatchLimitSize", 100);

        public static bool IsPilotCheckRequiredFOrUpdateAppExtensionMetadata => DynamicConfig.GetValue("IsPilotCheckRequiredFOrUpdateAppExtensionMetadata", false);


        public static bool UseAppsConsolidatedInAzure => DynamicConfig.GetValue("UseAppsConsolidatedInAzure", false);

        public static bool DisablePPSRequeue => DynamicConfig.GetValue("DisablePPSRequeue", false);

        public static TimeSpan BulkDownloadCacheTimeToLive => DynamicConfig.GetValue<TimeSpan>("BulkDownloadCacheTimeToLive", TimeSpan.FromHours(1));

        public static bool IsAccountEnabledForFullToDeltaDownloadChange(long accountId)
        {
            int percentage = DynamicConfig.GetValue("FullToDeltaDownloadPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static bool IsAccountInAdExtensionOrderSummaryinBatch(long accountId)
        {
            int percentage = DynamicConfig.GetValue("AdExtensionOrderSummaryinBatch", 0);
            return (accountId % 100) < percentage;
        }
        public static bool IsAccountInAdExtensionOrderSummaryDeltainBatch(long accountId)
        {
            int percentage = DynamicConfig.GetValue("AdExtensionOrderSummaryDeltainBatch", 0);
            return (accountId % 100) < percentage;
        }

        public static int AdExtensionOrderReadRowsBatch(Dictionary<string, string> overrideConfig)
        {
            GetValue("AdExtensionOrderReadRowsBatch", overrideConfig, out int batchsize);
            return batchsize;
        }

        public static int AdExtensionOrderReadRowsBatchForDelta(Dictionary<string, string> overrideConfig)
        {
            GetValue("AdExtensionOrderReadRowsBatchForDelta", overrideConfig, out int batchsize);
            return batchsize;
        }

        public static int MaxTryOnMdsCallFailure => DynamicConfig.GetValue("MaxTryOnMdsCallFailure", 1);

        public static int VideoSourceUrlCheckPercentage => DynamicConfig.GetValue("VideoSourceUrlCheckPercentage", 0);

        public static ReadOnlyCollection<long> BlockPPSPauseUnpauseEnqueueCustomerIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("BlockPPSPauseUnpauseEnqueueCustomerIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }
        public static ReadOnlyCollection<long> BlockPPSDeleteEnqueueCustomerIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("BlockPPSDeleteEnqueueCustomerIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static int PPSSkypePingTestRetryCount => DynamicConfig.GetValue<int>("PPSSkypePingTestRetryCount", 0);

        public static bool EnableAggregatorLoadAdGroupCallTrackingOptimization => DynamicConfig.GetValue("EnableAggregatorLoadAdGroupCallTrackingOptimization", false);

        public static bool DeprecateCloudImportV1 => DynamicConfig.GetValue("DeprecateCloudImportV1", false);

        public static TimeSpan MaxMITaskRunTime => DynamicConfig.GetValue("MaxMITaskRunTime", new TimeSpan(6, 0, 0));

        public static TimeSpan MaxCloudImportRunTime => DynamicConfig.GetValue("MaxCloudImportRunTime", new TimeSpan(1, 0, 0));

        public static TimeSpan MaxSmartImportSchedulerTaskRunTime => DynamicConfig.GetValue("MaxSmartImportSchedulerTaskRunTime", new TimeSpan(0, 30, 0));

        public static TimeSpan MaxAZImportSyncCentricTaskRunTime => DynamicConfig.GetValue("MaxAZImportSyncCentricTaskRunTime", new TimeSpan(2, 30, 0));

        public static int PercentUsersForUserPreferencePartitionByParentCustomerId => DynamicConfig.GetValue("PercentUsersForUserPreferencePartitionByParentCustomerId", 0);

        public static IReadOnlyList<long> UserIdListForUserPreferencePartitionByParentCustomerId => DynamicConfig.GetValue("UserIdListForUserPreferencePartitionByParentCustomerId", EmptyLongList);

        public static int GetEstimatedEntityCountsMaxValueForLoad => DynamicConfig.GetValue("GetEstimatedEntityCountsMaxValueForLoad", 10000);

        public static ReadOnlyCollection<long> AIMEditorialAccountIdWhiteList => DynamicConfig.GetValue<ReadOnlyCollection<long>>(
            "AIMEditorialAccountIdWhiteList", new ReadOnlyCollection<long>(new List<long>()));

        public static ReadOnlyCollection<long> ImageAdExtensionEditorialAccountIdWhiteList => DynamicConfig.GetValue<ReadOnlyCollection<long>>(
            "ImageAdExtensionEditorialAccountIdWhiteList", new ReadOnlyCollection<long>(new List<long>()));

        public static bool ImageAdExtensionEditorialV2Enabled => DynamicConfig.GetValue("ImageAdExtensionEditorialV2Enabled", false);

        public static bool ReturnCropSettingWhenGetImage => DynamicConfig.GetValue("ReturnCropSettingWhenGetImage", false);

        public static bool TransparentImageAnnotationEnabled => DynamicConfig.GetValue("TransparentImageAnnotationEnabled", false);

        public static bool SaveLargeImageMetadataToBlobEnabled => DynamicConfig.GetValue("SaveLargeImageMetadataToBlobEnabled", false);

        public static bool FetchFeedItemPAStatusEnabled => DynamicConfig.GetValue("FetchFeedItemPAStatusEnabled", false);

        public static bool IncreaseResponsiveAdsHeadlineLength => DynamicConfig.GetValue("IncreaseResponsiveAdsHeadlineLength", false);

        public static bool NativeSubtotalMerged => DynamicConfig.GetValue("NativeSubtotalMerged", false);

        public static string ProfileEnvironmentIdentifier => DynamicConfig.GetValue("ProfileEnvironmentIdentifier", string.Empty);

        public static bool CampaignObjectiveSettingEnabled => DynamicConfig.GetValue("CampaignObjectiveSettingEnabled", false);

        public static bool PerformanceMaxCostPerSaleOptOutEnabled => DynamicConfig.GetValue("PerformanceMaxCostPerSaleOptOutEnabled", false);
        public static bool EnableSearchThemeBulkUpload => DynamicConfig.GetRequiredValue<bool>("EnableSearchThemeBulkUpload");
        public static bool EnableLinkedInTargetBulkUpload => DynamicConfig.GetValue("EnableLinkedInTargetBulkUpload", false);
        public static bool EnablePMaxCopyPaste => DynamicConfig.GetValue("EnablePMaxCopyPaste", false);
        public static bool EnableAudienceSegmentationForAssetGridView => DynamicConfig.GetValue("EnableAudienceSegmentationForAssetGridView", false);
        public static bool EnableAssetGroupsSegmentation => DynamicConfig.GetValue("EnableAssetGroupsSegmentation", false);

        public static bool BoostGoalRevenueUpdateCheck => DynamicConfig.GetValue("BoostGoalRevenueUpdateCheck", false);

        public static bool EnableCampaignVerifyTrackingSetting => DynamicConfig.GetValue("EnableCampaignVerifyTrackingSetting", false);

        public static bool BoostEditorialSkipEnabled => DynamicConfig.GetValue("BoostEditorialSkipEnabled", false);

        public static bool OptimizedTargetingEnabled => DynamicConfig.GetValue("OptimizedTargetingEnabled", false);
        public static bool FrequencyCapSettingsEnabled => DynamicConfig.GetValue("FrequencyCapSettingsEnabled", false);

        public static bool EnableAdsGlobalizationOPCDBPilot => DynamicConfig.GetValue("EnableAdsGlobalizationOPCDBPilot", false);

        public static int HotelAdsMaxVerticalCampaignsPerAccount => DynamicConfig.GetValue("HotelAdsMaxVerticalCampaignsPerAccount", 1);

        public static bool EnableHierarchicalRequestIdForAllMtRequests => DynamicConfig.GetValue("EnableHierarchicalRequestIdForAllMtRequests", false);

        public static int AddExperimentEntityMapBatchSize => DynamicConfig.GetValue("AddExperimentEntityMapBatchSize", 10000);

        public static int AssetGroupSearchThemeBulkProcessBatchSize => DynamicConfig.GetValue("AssetGroupSearchThemeBulkProcessBatchSize", 1000);

        public static bool EnableVideoMetadata => DynamicConfig.GetValue("UseNewVideoSprocs", false);

        public static bool AllowVideoMetadataUpdates => DynamicConfig.GetValue("AllowVideoMetadataUpdates", false);

        public static bool FetchValidVideoDataOnly => DynamicConfig.GetValue("FetchValidVideoDataOnly", false);

        public static bool AmazonSyncAPIRetry => DynamicConfig.GetValue("AmazonSyncAPIRetry", false);

        public static bool RSASettingEnabled => DynamicConfig.GetValue("RSASettingEnabled", false);

        public static int EnableDisplayCampaignImageAdImportPercentage => DynamicConfig.GetValue("EnableDisplayCampaignImageAdImportPercentage", 0);

        public static bool EnableDownlaodInitForExifTool => DynamicConfig.GetValue("EnableDownlaodInitForExifTool", false);

        public static bool ShouldSendNotificationForGoogleImportVideoAds => DynamicConfig.GetValue("ShouldSendNotificationForGoogleImportVideoAds", false);

        public static bool ImportVideoAsMultiFormatAdForCPMAndNewCampaignDemand => DynamicConfig.GetValue("ImportVideoAsMultiFormatAdForCPMAndNewCampaignDemand", false);

        public static bool EnableSquareImageAsOriginalImageWhenNoLandscapeImageForDiscoveyrCampaign => DynamicConfig.GetValue("EnableSquareImageAsOriginalImageWhenNoLandscapeImageForDiscoveyrCampaign", false);

        public static int RSAAutoAssetAccountPilotPercentage => DynamicConfig.GetValue<int>("RSAAutoAssetAccountPilotPercentage", 0);

        public static int GoogleVideoCampaignImportEnabledForAccountPercentage => DynamicConfig.GetValue<int>("GoogleVideoCampaignImportEnabledForAccountPercentage", 0);

        public static int EnableFacebookMultiFormatAdImportPercentage => DynamicConfig.GetValue<int>("EnableFacebookMultiFormatAdImportPercentage", 0);

        public static bool UseBitMaskToJudgeDealCampaign => DynamicConfig.GetValue("UseBitMaskToJudgeDealCampaign", false);

        public static bool UseIsDealCampaignBitMaskToValidate => DynamicConfig.GetValue("UseIsDealCampaignBitMaskToValidate", false);

        public static bool AppSettingEnabled => DynamicConfig.GetValue("AppSettingEnabled", false);

        public static bool MultiChannelCampaignEnabled => DynamicConfig.GetValue("MultiChannelCampaignEnabled", false);

        public static bool BroadMatchOnlyCampaignEnabled => DynamicConfig.GetValue("BroadMatchOnlyCampaignEnabled", false);

        public static string ObjectStoreClient => DynamicConfig.GetValue("ObjectStoreClient", "MemoryMock");

        public static IReadOnlyList<int> PauseCampaignWhenBudgetIsInferredCustomerList => DynamicConfig.GetValue("PauseCampaignWhenBudgetIsInferredCustomerList", EmptyIntList);
        public static bool BIDeletedRowsAggEnabled => DynamicConfig.GetValue("BIDeletedRowsAggEnabled", false);

        public static bool EnabledExternalChannelSyncEnabled => DynamicConfig.GetValue("EnabledExternalChannelSyncEnabled", false);

        public static bool LifetimeBudgetEnabled => DynamicConfig.GetValue("LifetimeBudgetEnabled", false);

        public static bool LifetimeBudgetEnabledForImport => DynamicConfig.GetValue("LifetimeBudgetEnabledForImport", false);

        public static bool SendLifetimeBudgetDetailsToFraud => DynamicConfig.GetValue("SendLifetimeBudgetDetailsToFraud", false);

        public static bool LifetimeBudgetAPIEnabled => DynamicConfig.GetValue("LifetimeBudgetAPIEnabled", false);

        public static bool EnableAssetGroupImportSkipFreeStockImage => DynamicConfig.GetValue("EnableAssetGroupImportSkipFreeStockImage", false);
        
        public static bool EnableBrandBulkProcess => DynamicConfig.GetValue<bool>("EnableBrandBulkProcess", false);
        
        public static int BrandBulkProcessBatchSize => DynamicConfig.GetValue<int>("BrandBulkProcessBatchSize", 1000);
        
        public static int BrandCacheLockDurationMillseconds => DynamicConfig.GetValue<int>("BrandCacheLockDurationMillseconds", 15000);
        
        public static int Location2CacheLockDurationMillseconds => DynamicConfig.GetValue<int>("Location2CacheLockDurationMillseconds", 15000);

        public static bool CampaignBrandKitEnabled => DynamicConfig.GetValue("CampaignBrandKitEnabled", false);

        public static bool PmaxSovMetricsPilotControl => DynamicConfig.GetValue("PmaxSovMetricsPilotControl", false);

        public static bool FixCampaignChildDependencyEnabled => DynamicConfig.GetValue("FixCampaignChildDependencyEnabled", false);

        public static TimeSpan ExperimentTaskHeartBeatInterval
        {
            get { return DynamicConfig.GetValue<TimeSpan>("ExperimentTaskHeartBeatInterval", TimeSpan.FromMinutes(10)); }
        }

        public static int ContextualOptionCacheIntervalInMinute => DynamicConfig.GetValue("ContextualOptionCacheIntervalInMinute", 60);

        private static IReadOnlyDictionary<int, int> GetImportTargetCriterionLoaderParentBatchSizeByAccountId(string dynamicConfigName)
        {
            var result = new Dictionary<int, int>();
            string batchSizesByAccountIdConfigStr = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var batchSizesByAccountId = batchSizesByAccountIdConfigStr.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var batchSizeByAccountId in batchSizesByAccountId)
            {
                var elements = batchSizeByAccountId.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {accountId0}:{batchSize0};{accountId1}:{batchSize1}");
                }

                int accountId;
                int batchSize;
                if (!int.TryParse(elements[0], out accountId) || !int.TryParse(elements[1], out batchSize))
                {
                    throw new ArgumentException("Incorrect dynamic config format, account id and batch size must be int");
                }

                result[accountId] = batchSize;
            }

            return result;
        }

        private static IReadOnlyDictionary<long, int> GetIntValueByLongKey(string dynamicConfigKey, string testOverrideValue = null)
        {
            var result = new Dictionary<long, int>();
            var dynamicConfigValue = string.IsNullOrEmpty(testOverrideValue) ? DynamicConfig.GetValue(dynamicConfigKey, string.Empty)
                                                                             : testOverrideValue;
            var intValueByLongKey = dynamicConfigValue.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var kv in intValueByLongKey)
            {
                var keyAndValue = kv.Split(':');
                if (keyAndValue.Length != 2)
                {
                    throw new ArgumentException(
                        "Incorrect dynamic config format, please follow pattern: {LongKey0}:{IntValue0};{LongKey1}:{IntValue1}");
                }

                if (!long.TryParse(keyAndValue[0], out var longKey) || !int.TryParse(keyAndValue[1], out var intValue))
                {
                    throw new ArgumentException("Incorrect dynamic config format, Key must be Long and Value must be Int");
                }

                result[longKey] = intValue;
            }

            return result;
        }

        private static IReadOnlyDictionary<long, long> GetLongValueByLongKey(string dynamicConfigKey)
        {
            var result = new Dictionary<long, long>();
            var dynamicConfigValue = DynamicConfig.GetValue(dynamicConfigKey, string.Empty);
            var longValueByLongKey = dynamicConfigValue.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var kv in longValueByLongKey)
            {
                var keyAndValue = kv.Split(':');
                if (keyAndValue.Length != 2)
                {
                    throw new ArgumentException(
                        "Incorrect dynamic config format, please follow pattern: {LongKey0}:{LongValue0};{LongKey1}:{LongValue1}");
                }

                if (!long.TryParse(keyAndValue[0], out var longKey) || !long.TryParse(keyAndValue[1], out var longValue))
                {
                    throw new ArgumentException("Incorrect dynamic config format, Key and Value must be Long");
                }

                result[longKey] = longValue;
            }

            return result;
        }

        private static IReadOnlyDictionary<long, long> GetEntityLimitDictionary(string dynamicConfigName)
        {
            var result = new Dictionary<long, long>();
            var entityLimitStr = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var entityLimit = entityLimitStr.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entityLimitId in entityLimit)
            {
                var elements = entityLimitId.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {EntityId0}:{Limit0};{EntityId1}:{Limit1}");
                }

                if (!long.TryParse(elements[0], out var entityId) || !long.TryParse(elements[1], out var limit))
                {
                    throw new ArgumentException("Incorrect dynamic config format, entity id must be long, and limit must be long");
                }

                result[entityId] = limit;
            }
            return result;
        }

        private static IReadOnlyDictionary<int, TimeSpan> GetImportMaxRuntimeByCustomerId(string dynamicConfigName)
        {
            var result = new Dictionary<int, TimeSpan>();
            var maxImportRuntimeStr = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var maxImportRuntimePerCustomer = maxImportRuntimeStr.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in maxImportRuntimePerCustomer)
            {
                var elements = entity.Split('|');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {CusotmerId0}|{MaxImportTime};{CustomerId1}|{MaxImportTime}");
                }

                int customerId;
                TimeSpan maxRuntime;
                if (!int.TryParse(elements[0], out customerId) || !TimeSpan.TryParse(elements[1], out maxRuntime))
                {
                    throw new ArgumentException("Incorrect dynamic config format, customer id must be int, and maxRuntime must be Timespan");
                }

                result[customerId] = maxRuntime;
            }
            return result;
        }

        private static IReadOnlyDictionary<long, TimeSpan> GetSearchStreamRetryTimeoutInSecondsByAccountId(string dynamicConfigName)
        {
            var result = new Dictionary<long, TimeSpan>();
            var searchStreamRetryTimeoutStr = DynamicConfig.GetValue<string>(dynamicConfigName, "-1|900");
            var searchStreamRetryTimeoutPerAccount = searchStreamRetryTimeoutStr.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var entity in searchStreamRetryTimeoutPerAccount)
            {
                var elements = entity.Split('|');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {AccountId0}|{SearchStreamRetryTimeout0};{AccountId1}|{SearchStreamRetryTimeout1}");
                }

                long accountId;
                int searchStreamRetryTimeoutInSeconds;
                if (!long.TryParse(elements[0], out accountId) || !int.TryParse(elements[1], out searchStreamRetryTimeoutInSeconds))
                {
                    throw new ArgumentException("Incorrect dynamic config format, account id must be long, and searchStreamRetryTimeoutInSeconds must be int");
                }

                result.TryAddEntry(accountId, TimeSpan.FromSeconds(searchStreamRetryTimeoutInSeconds));
            }

            if (!result.ContainsKey(-1))
            {
                result.Add(-1, TimeSpan.FromSeconds(900));
            }

            return result;
        }

        private static IReadOnlyDictionary<int, int> GetOverrideIntValueByCustomerId(string dynamicConfigName)
        {
            var result = new Dictionary<int, int>();
            var overrideString = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var overrideStringPerCustomer = overrideString.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in overrideStringPerCustomer)
            {
                var elements = entity.Split('|');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {CusotmerId0}|{Override};{CustomerId1}|{Override}");
                }

                if (!int.TryParse(elements[0], out int customerId) || !int.TryParse(elements[1], out int overrideVal))
                {
                    throw new ArgumentException("Incorrect dynamic config format, customer id must be int, and overrideVal must be int");
                }

                result[customerId] = overrideVal;
            }
            return result;
        }

        private static IReadOnlyDictionary<string, int> GetProfileSupportedLanguages(string dynamicConfigName)
        {
            var result = new Dictionary<string, int>(StringComparer.InvariantCultureIgnoreCase);
            var profileSupportedLanguagesStr = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var profileSupportedPerLanguageStr = profileSupportedLanguagesStr.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in profileSupportedPerLanguageStr)
            {
                var elements = entity.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {LanuageLocale}:{Lcid};{LanuageLocale}:{Lcid}");
                }

                int lcid;
                if (!int.TryParse(elements[1], out lcid))
                {
                    throw new ArgumentException("Incorrect dynamic config format, lcid must be int");
                }

                result[elements[0]] = lcid;
            }

            return result;
        }

        private static IReadOnlyDictionary<string, HashSet<string>> GetStringHashSetFromStringKey(string dynamicConfigName)
        {
            var result = new Dictionary<string, HashSet<string>>();
            var savedConfigValue = DynamicConfig.GetValue<string>(dynamicConfigName, "");

            var hashSetPerString = savedConfigValue.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var entity in hashSetPerString)
            {
                var elements = entity.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException("Incorrect dynamic config format, please follow pattern: {Key1}|{value1},{value2};{Key2}|{value3},{value4}");
                }

                string key = elements[0].Trim();
                HashSet<string> values = new HashSet<string>();

                var configValues = elements[1].Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var value in configValues)
                {
                    values.Add(value.Trim());
                }
                result.TryAddEntry(key, values);
            }
            return result;
        }


        public static long GetEntityLimitsFromConfigDictionary(IReadOnlyDictionary<long, long> entityLimitsByCustomerId, long customerId, long defaultCustomerId = -1)
        {
            if (entityLimitsByCustomerId == null)
            {
                throw new ArgumentException("There is no limit dictionary loaded.");
            }
            if (entityLimitsByCustomerId.TryGetValue(customerId, out var demandLimit))
            {
                return demandLimit;
            }
            else if (entityLimitsByCustomerId.TryGetValue(defaultCustomerId, out demandLimit))
            {
                return (int)demandLimit;
            }
            else
            {
                throw new ArgumentException("Missing demand limit setting in config");
            }
        }

        public static int MaxThreadForNegativeKeywordBulkLoad => DynamicConfig.GetValue<int>("MaxThreadForNegativeKeywordBulkLoad", 1);

        public static int MaxThreadForOrderTargetBulkLoad => DynamicConfig.GetValue<int>("MaxThreadForOrderTargetBulkLoad", 1);

        public static int EntityBatchSizeToFetchAdGroupCriterions => DynamicConfig.GetValue<int>("EntityBatchSizeToFetchAdGroupCriterions", 1000);

        public static int BulkNKWShardingFullModCount => DynamicConfig.GetValue<int>("BulkNKWShardingFullModCount", 0);

        public static int BulkNKWShardingDeltaModCount => DynamicConfig.GetValue<int>("BulkNKWShardingDeltaModCount", 0);

        public static int BulkNKWShardingPagingThreshold => DynamicConfig.GetValue<int>("BulkNKWShardingPagingThreshold", 0);

        public static int BulkTargetShardingFullModCount => DynamicConfig.GetValue<int>("BulkTargetShardingFullModCount", 16);

        public static int BulkTargetShardingDeltaModCount => DynamicConfig.GetValue<int>("BulkTargetShardingDeltaModCount", 16);

        public static string AggregatorEnvironmentIdentifier => DynamicConfig.GetValue<string>("AggregatorEnvironmentIdentifier", String.Empty);

        public static string AggregatorEnvironmentRegionIdentifier => DynamicConfig.GetValue<string>("AggregatorEnvironmentRegionIdentifier", String.Empty);

        public static string AggregatorLocalFileCachePath => DynamicConfig.GetValue<string>("AggregatorLocalFileCachePath", null);

        public static string DistributedQueryLocalFileCachePath => DynamicConfig.GetValue<string>("DistributedQueryLocalFileCachePath", null);

        public static bool UsePageTrackingId => DynamicConfig.GetValue<bool>("UsePageTrackingId", false);

        public static bool KeywordsGridUsePageTrackingId => DynamicConfig.GetValue<bool>("KeywordsGridUsePageTrackingId", false);

        public static bool ImageMigrationEnabled => DynamicConfig.GetValue<bool>("ImageMigrationEnabled", false);
        public static bool ImageAspectMigrationEnabled => DynamicConfig.GetValue<bool>("ImageAspectMigrationEnabled", false);

        public static int? TestCustomerForImageMigration => DynamicConfig.GetValue<int?>("TestCustomerForImageMigration", null);
        public static IEnumerable<string> TestCustomersForImageAspectMigration => DynamicConfig.GetListValues("TestCustomersForImageAspectMigration");
        public static int? ImageAspectMigrationFeatureId => DynamicConfig.GetValue<int?>("ImageAspectMigrationFeatureId", null);
        public static bool? ImageAspectMigrationFeatureGA => DynamicConfig.GetValue<bool?>("ImageAspectMigrationFeatureGA", null);
        public static int ImageMigrationCheckoutMinutes => DynamicConfig.GetValue<int>("ImageMigrationCheckoutMinutes", 60);

        public static int ImageMigrationWaitPerBatchMillisec => DynamicConfig.GetValue<int>("ImageMigrationWaitPerBatchMillisec", 50);

        public static int ImageMigrationWaitNoWorkMultiplier => DynamicConfig.GetValue<int>("ImageMigrationWaitNoWorkMultiplier", 15);

        public static int ImageMigrationBatchSize => DynamicConfig.GetValue<int>("ImageMigrationBatchSize", 100);

        public static bool EnableImageReKey => DynamicConfig.GetValue<bool>("EnableImageReKey", false);

        public static string ThumbnailServerUrlFormat => DynamicConfig.GetValue<string>("ThumbnailServerUrlFormat", "http://www.bing.com/th?id=OADD.{0}&pid=21.2");

        public static string ObjectStoreUrlFormat => DynamicConfig.GetValue<string>("ObjectStoreUrlFormat", "http://localhost:10875/dam.mock");

        public static string ObjectStoreUrlFormatNew => DynamicConfig.GetValue<string>("ObjectStoreUrlFormatNew", "http://localhost:10875/dam.mock");

        public static string AppMetdataObjectStoreUrlFormat => DynamicConfig.GetValue<string>("AppMetdataObjectStoreUrlFormat", "http://localhost:10875/dam.mock"); 

        public static string ObjectStoreCertificateName => DynamicConfig.GetValue<string>("ObjectStoreCertificateName", "MsanObjectStoreCert-Si");

        public static string ObjectStoreDataCentersNew => DynamicConfig.GetValue<string>("ObjectStoreDataCentersNew", "EastUS;CentralUS;WestUS;Europe;EastAsia");

        public static string ObjectStoreDataCentersAppIcon => DynamicConfig.GetValue<string>("ObjectStoreDataCentersAppIcon", "EastUS;CentralUS;WestUS;Europe;EastAsia;KoreaSouth");

        public static string ObjectStoreUrlLocations => DynamicConfig.GetValue<string>("ObjectStoreUrlLocations", "CO");

        public static string ObjectStoreReplicaUrlFormat => DynamicConfig.GetValue<string>("ObjectStoreReplicaUrlFormat", String.Empty);

        public static string ObjectStoreReplicaUrlLocations => DynamicConfig.GetValue<string>("ObjectStoreReplicaUrlLocations", String.Empty);

        public static int ObjectStoreRetryCount => DynamicConfig.GetValue<int>("ObjectStoreRetryCount", 20);

        public static int LiftExperimentMonetizationTimeDelta => DynamicConfig.GetValue<int>("LiftExperimentMonetizationTimeDelta", 13);

        public static int LiftExperimentUETTimeDelta => DynamicConfig.GetValue<int>("LiftExperimentUETTimeDelta", 7);

        public static int LiftExperimentRunTimeDelta => DynamicConfig.GetValue<int>("LiftExperimentRunTimeDelta", 2);

        public static bool EnableLiftExperimentOfflineTasks => DynamicConfig.GetValue<bool>("EnableLiftExperimentOfflineTasks", false);

        public static int ObjectStoreRetryDelayMilliseconds => DynamicConfig.GetValue<int>("ObjectStoreRetryDelayMilliseconds", 100);

        public static string ObjectStorePartnerName => DynamicConfig.GetValue<string>("ObjectStorePartnerName", String.Empty);

        public static string ObjectStoreContainerName => DynamicConfig.GetValue<string>("ObjectStoreContainerName", String.Empty);

        public static string SmartRecommendationsOsEnvironments => DynamicConfig.GetValue<string>("SmartRecommendationsOsEnvironments", String.Empty);


        public static int AddAssetBatchSize => DynamicConfig.GetValue<int>("AddAssetBatchSize", 1);

        public static string GoalRecommendationsOsEnvironments => DynamicConfig.GetValue<string>("GoalRecommendationsOsEnvironments", String.Empty);

        public static string GoalRecommendationsOsNamespace => DynamicConfig.GetValue<string>("GoalRecommendationsOsNamespace", String.Empty);

        public static string GoalRecommendationsOsTable => DynamicConfig.GetValue<string>("GoalRecommendationsOsTable", String.Empty);

        public static string DiagnosticTileOsTable => DynamicConfig.GetValue<string>("DiagnosticTileOsTable", String.Empty);

        public static string AccountGoalDiagnosticOsTable => DynamicConfig.GetValue<string>("AccountGoalDiagnosticOsTable", String.Empty);

        public static string GoalRecommendationsOsCertificateName => DynamicConfig.GetValue<string>("GoalRecommendationOsCertificateName", String.Empty);

        public static string NrtObjectStoreEnvironments => DynamicConfig.GetValue<string>("NrtObjectStoreEnvironments", String.Empty);

        public static string NrtObjectStoreNamespace => DynamicConfig.GetValue<string>("NrtObjectStoreNamespace", String.Empty);

        public static string NrtObjectStoreCertificateName => DynamicConfig.GetValue<string>("NrtObjectStoreCertificateName", String.Empty);

        public static int NrtObjectStoreWriteBatchSize => DynamicConfig.GetValue<int>("NrtObjectStoreWriteBatchSize", 1000);

        public static int NrtObjectStoreVisitAttempts => DynamicConfig.GetValue<int>("NrtObjectStoreVisitAttempts", 1);

        public static bool DomainThirdPartyObjectStoreEnabled => DynamicConfig.GetValue<bool>("DomainThirdPartyObjectStoreEnabled", false);

        public static bool DiagnosticTileObjectStoreEnabled => DynamicConfig.GetValue<bool>("DiagnosticTileObjectStoreEnabled", false);

        public static string DomainThirdPartyOsTable => DynamicConfig.GetValue<string>("DomainThirdPartyOsTable", String.Empty);

        public static int AutoConversionTaskHeartBeatInMs => DynamicConfig.GetValue<int>("AutoConversionTaskHeartBeatInMs", 10000);

        public static int AutoConversionObjectStoreBatchSize => DynamicConfig.GetValue<int>("AutoConversionObjectStoreBatcSize", 500);

        public static int AutoConversionObjectStoreWriteInternalMS => DynamicConfig.GetValue<int>("AutoConversionObjectStoreWriteInternalMS", 500);

        public static int AddAssetBatchLimit => DynamicConfig.GetValue<int>("AddAssetBatchLimit", 10);

        public static int UpdateAssetBatchLimit => DynamicConfig.GetValue<int>("UpdateAssetBatchLimit", 100);

        public static bool EnableBulkImageManagement => DynamicConfig.GetValue<bool>("EnableBulkImageManagement", false);

        public static int ImageDownloadTimeout => DynamicConfig.GetValue<int>("ImageDownloadTimeout", 15);

        public static bool InlineFetchROIInfo => DynamicConfig.GetValue<bool>("InlineFetchROIInfo", false);

        public static int GetAdImpressionTrackingUrlStatusMaxRetryCount => DynamicConfig.GetValue<int>("GetAdImpressionTrackingUrlStatusMaxRetryCount", 1);

        public static int GetAdImpressionTrackingUrlStatusTimeoutSpanMilseconds => DynamicConfig.GetValue<int>("GetAdImpressionTrackingUrlStatusTimeoutSpanMilseconds", 500);

        public static bool EnableAddingAdditionalRequestHeaders => DynamicConfig.GetValue<bool>("EnableAddingAdditionalRequestHeaders", false);

        public static bool ValidateAdImpressionTrackingUrlAccessible => DynamicConfig.GetValue<bool>("ValidateAdImpressionTrackingUrlAccessible", true);

        public static int FireAdImpressionTrackingUrlTimeoutInMilseconds => DynamicConfig.GetValue<int>("FireAdImpressionTrackingUrlTimeoutInMilseconds", 1000);

        public static int AdImpressionTrackingDataParallelLoadMaximumNumber => DynamicConfig.GetValue<int>("AdImpressionTrackingDataParallelLoadMaximumNumber", 5);

        public static bool EnabledAdImpressionTrackingRetryCountThresholdToQueueNewTask => DynamicConfig.GetValue<bool>("EnabledAdImpressionTrackingRetryCountThresholdToQueueNewTask", true);
        public static bool EnabledTaskEngineAdImpressionTrackingTaskFailOver => DynamicConfig.GetValue<bool>("EnabledTaskEngineAdImpressionTrackingTaskFailOver", true);
        public static int MaximumImpressionsToFireInSingleExecution => DynamicConfig.GetValue<int>("MaximumImpressionsToFireInSingleExecution", 200);
        public static List<int> EnabledMaximumImpressionFiringInSingleExecutionAccounts
        {
            get
            { 
                List<string> config = DynamicConfig.GetListValues("EnabledMaximumImpressionFiringInSingleExecutionAccounts");
                List<int> result = new List<int>();
                if (config == null) return result;
                if (config.Count == 1 && string.Equals("GA", config.FirstOrDefault(), StringComparison.OrdinalIgnoreCase))
                {
                    return null;
                }
                foreach (string aidStr in config)
                {
                    if (int.TryParse(aidStr, out int aid))
                    {
                        result.Add(aid);
                    }
                }
                return result;
            }
        }
        public static int AdImpressionTrackingRetryCountThresholdToQueueNewTask => DynamicConfig.GetValue<int>("AdImpressionTrackingRetryCountThresholdToQueueNewTask", 30);
        public static int AdImpressionTrackingNewTaskNextRunningTimeMultiplerInSeconds => DynamicConfig.GetValue<int>("AdImpressionTrackingNewTaskNextRunningTimeMultiplerInSeconds", 5);
        public static int AdImpressionTrackingMaximumRequeueCount => DynamicConfig.GetValue<int>("AdImpressionTrackingMaximumRequeueCount", 20);

        public static int ExecuteAdImpressionTrackingUrlTaskTimeoutInSeconds => DynamicConfig.GetValue<int>("ExecuteAdImpressionTrackingUrlTaskTimeoutInSeconds", 4);

        public static int MaximumFiringTimePerAdTrackingDataInSeconds => DynamicConfig.GetValue<int>("MaximumFiringTimePerAdTrackingDataInSeconds", 60);

        public static int FireAdImpressionTrackingUrlMaxAttemptCount => DynamicConfig.GetValue<int>("FireAdImpressionTrackingUrlMaxAttemptCount", 1);
        public static string AdImpressionTrackingTaskTypeNameForTaskEngine => DynamicConfig.GetValue<string>("AdImpressionTrackingTaskTypeNameForTaskEngine", "AdImpressionTracking");
        public static int AdImpressionTrackingTaskRetryIntervalInSeconds => DynamicConfig.GetValue<int>("AdImpressionTrackingTaskRetryIntervalInSeconds", 30);
        public static int AdImpressionTrackingFiringCoolDownInMilliSeconds => DynamicConfig.GetValue<int>("AdImpressionTrackingFiringCoolDownInMilliSeconds", 1500);
        public static int ParallelExecutionsCountForAdImpressionTrackingTask => DynamicConfig.GetValue<int>("ParallelExecutionsCountForAdImpressionTrackingTask", 1);
        public static int SleepTimeInMillisecondsForAdImpressionTrackingTask => DynamicConfig.GetValue<int>("SleepTimeInMillisecondsForAdImpressionTrackingTask", 5000);
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForAdImpressionTrackingTask => DynamicConfig.GetValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForAdImpressionTrackingTask", 5000);
        public static IReadOnlyList<long> FireAdImpressionTrackingBlockAccountIdList => DynamicConfig.GetValue("FireAdImpressionTrackingBlockAccountIdList", EmptyLongList);

        public static IReadOnlyList<long> CampaignSubtypeMigrationNonMSANCampaignIdList => DynamicConfig.GetValue("CampaignSubtypeMigrationNonMSANCampaignIdList", EmptyLongList);
        public static bool EnableAdOfflineEditorialUpdateV43 => DynamicConfig.GetValue("EnableAdOfflineEditorialUpdateV43", false);
        public static bool EnableUpdateAssetsMetadataV2 => DynamicConfig.GetValue("EnableUpdateAssetsMetadataV2", false);

        public static IReadOnlyList<long> FireAdImpressionTrackingBlockAdIdList => DynamicConfig.GetValue("FireAdImpressionTrackingBlockAdIdList", EmptyLongList);

        public static IReadOnlyList<long> AdImpressionTrackingUrlORDParamPilotAccountList => DynamicConfig.GetValue("AdImpressionTrackingUrlORDParamPilotAccountList", EmptyLongList);

        public static bool EnableAdImpressionTrackingUrlFireAndForget => DynamicConfig.GetValue("EnableAdImpressionTrackingUrlFireAndForget", false);

        public static int AdImpressionTrackingUrlFiringTimesThresholdToUseParalellism => DynamicConfig.GetValue("AdImpressionTrackingUrlFiringTimesThresholdToUseParalellism", 10);

        public static int AdImpressionTrackingUrlFireParallelism => DynamicConfig.GetValue("AdImpressionTrackingUrlFireParallelism", 1);

        public static IReadOnlyList<long> AdImpressionTrackingUrlParallelismPilotAccountList => DynamicConfig.GetValue("AdImpressionTrackingUrlParallelismPilotAccountList", EmptyLongList);

        public static int DefaultParallelismDegreeForGettingIdMappingByParentId
            => DynamicConfig.GetValue("DefaultParallelismDegreeForGettingIdMappingByParentId", 3);

        public static int ImportExternalIdForDeletionFlushSize
            => DynamicConfig.GetValue("ImportExternalIdForDeletionFlushSize", 1000);

        public static int DefaultParentBatchsizeForLoadingIdMapping
            => DynamicConfig.GetValue("DefaultParentBatchsizeForLoadingIdMapping", 1000);

        public static bool UseDimensionTabReportTestType
        {
            get { return DynamicConfig.GetRequiredValue<bool>("UseDimensionTabReportTestType"); }
        }

        public static bool UseInlineDownloadReportTestType
        {
            get { return DynamicConfig.GetValue<bool>("UseInlineDownloadReportTestType", false); }
        }

        public static bool IsTestEnvForReportDataService
        {
            get { return DynamicConfig.GetRequiredValue<bool>("IsTestEnvForReportDataService"); }
        }

        public static string ImportKeywordIdDownloadTimeout => DynamicConfig.GetRequiredValue<string>("ImportKeywordIdDownloadTimeout");
        public static string ImportAdGroupNameDownloadTimeout => DynamicConfig.GetRequiredValue<string>("ImportAdGroupNameDownloadTimeout");

        public static int BulkUploadFormatVersionForTravel => DynamicConfig.GetValue<int>("BulkUploadFormatVersionForTravel", 5);

        public static int BillingCallbackBatchLimit => DynamicConfig.GetValue<int>("BillingCallbackBatchLimit", 1000);

        public static int ParallelOpenConPartitionForCampaign => DynamicConfig.GetValue<int>("ParallelOpenConPartitionForCampaign", 0);

        public static int ParallelOpenConBiPartitionForCampaign => DynamicConfig.GetValue<int>("ParallelOpenConBiPartitionForCampaign", 0);


        public static int TravelDownloadURLExpirationTimeInSeconds => DynamicConfig.GetValue<int>("TravelDownloadURLExpirationTimeInSeconds", 604800);

        public static HashSet<int> EnabledAutomatedExtensionId =>
                                DynamicConfig.GetValue("EnabledAutomatedExtensionId", "").Split(',').Where(s => !string.IsNullOrWhiteSpace(s)).ToHashSet(id => int.Parse(id));

        public static bool EnableQPSLimitControl => DynamicConfig.GetValue("EnableQPSLimitControl", false);

        public static double AvailabilityRequiredOfGMBGetMediaItemsAPI => DynamicConfig.GetValue("AvailabilityRequiredOfGMBGetMediaItemsAPI", 0.5);

        public static string LocalTempFileLocation => DynamicConfig.GetRequiredValue<string>("LocalTempFileLocation");

        public static bool EnablePPSGetSkypeCertFromLocal => DynamicConfig.GetValue("EnablePPSGetSkypeCertFromLocal", false);

        public static int CallAdExtensionEventBatchSize => DynamicConfig.GetValue<int>("CallAdExtensionEventBatchSize", 100);

        public static int CallAdExtensionEventRetryDelayInHours => DynamicConfig.GetValue<int>("CallAdExtensionEventRetryDelayInHours", 1);

        public static long SchedulerIntervalInSeconds => DynamicConfig.GetValue<long>("SchedulerIntervalInSeconds", 10);

        public static long BISyncSchedulerIntervalInSeconds => DynamicConfig.GetValue<long>("BISyncSchedulerIntervalInSeconds", 10);

        public static int BulkDownloadBteFromDirectBIPercentage
        {
            get
            {
                return DynamicConfig.GetValue("BulkDownloadBteFromDirectBIPercentage", 0);
            }
        }

        public static int BulkEditBteFromDirectBIPercentage
        {
            get
            {
                return DynamicConfig.GetValue("BulkEditBteFromDirectBIPercentage", 0);
            }
        }

        public static long? AutomatedExtensionBIMaxRowNum
        {
            get
            {
                var value = DynamicConfig.GetValue<long>("AutomatedExtensionBIMaxRowNum", 0);
                return (value <= 0) ? (long?)null : value;
            }
        }

        public static bool DisableOutlookMsnNegativeSite
        {
            get
            {
                return DynamicConfig.GetValue("DisableOutlookMsnNegativeSite", false);
            }
        }

        public static bool EnableBulkDownloadUTCDateCache
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkDownloadUTCDateCache", false);
            }
        }

        public static bool EnableBulkDownloadSeasonalityAdjustment
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkDownloadSeasonalityAdjustment", false);
            }
        }

        public static bool EnableBulkDownloadDataExclusion
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkDownloadDataExclusion", false);
            }
        }

        public static bool EnableBulkUploadSeasonalityAdjustment
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkUploadSeasonalityAdjustment", false);
            }
        }

        public static bool EnableBulkUploadDataExclusion
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkUploadDataExclusion", false);
            }
        }

        public static bool EnableBulkDownloadBrand
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkDownloadBrand", false);
            }
        }

        public static bool EnableBulkDownloadNCA
        {
            get
            {
                return DynamicConfig.GetValue("EnableBulkDownloadNCA", false);
            }
        }

        public static bool AllowMobileAppBundleIdExclusion => DynamicConfig.GetValue<bool>("AllowMobileAppBundleIdExclusion", false);
        public static bool AllowMobileAppBundleIdExclusionCampaignAdGroup(long accountId)
        {
            var accountIds = new HashSet<long>(DynamicConfig.GetValue("AllowMobileAppBundleIdExclusionCampaignAdGroupAccounts", EmptyLongList));
            return DynamicConfig.GetValue<bool>("AllowMobileAppBundleIdExclusionCampaignAdGroup", false) | accountIds.Contains(accountId);
        }
            
        public static bool EnableBulkDownloadAssetGroupSearchThemes => DynamicConfig.GetValue<bool>("EnableBulkDownloadAssetGroupSearchThemes", false);

        public static bool EnableBulkDownloadAssetGroupUrlTargets => DynamicConfig.GetValue<bool>("EnableBulkDownloadAssetGroupUrlTargets", false);

        public static bool EnableAssetGroupUrlTarget => DynamicConfig.GetValue<bool>("EnableAssetGroupUrlTarget", false);
        
        public static bool EnableAssetGroupUrlTargetValidator => DynamicConfig.GetValue<bool>("EnableAssetGroupUrlTargetValidator", false);
        
        public static bool EnableAssetGroupUrlTargetValidatorForConditionConflict => DynamicConfig.GetValue<bool>("EnableAssetGroupUrlTargetValidatorForConditionConflict", false);

        public static int SetEntityExclusionsBatchLimit => DynamicConfig.GetValue<int>("SetEntityExclusionsBatchLimit", 50000);

        public static bool UseOperationErrorForSetEntityExclusionsBatchLimitExceeded => DynamicConfig.GetValue<bool>("UseOperationErrorForSetEntityExclusionsBatchLimitExceeded", false);

        public static bool EnableSetIPExclusionsBatchFlow => DynamicConfig.GetValue<bool>("EnableSetIPExclusionsBatchFlow", false);

        public static int SetEntityExclusionsBatchSize => DynamicConfig.GetValue<int>("SetEntityExclusionsBatchSize", 1000);

        public static int SetEntityExclusionsParallelBatches => DynamicConfig.GetValue<int>("SetEntityExclusionsParallelBatches", 1);

        public static string DsaWebsiteLanguagesConfigValue => DynamicConfig.GetValue<string>("DsaWebsiteLanguagesConfigValue", "English;French;German;Spanish;Italian;Dutch;Swedish");

        public static string DynamicSearchAdsGlobalizationEUPhase1 => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationEUPhase1", "Polish;Portuguese;Czech;Romanian;Hungarian;Greek;Slovak;Bulgarian;Croatian;Lithuanian;Slovenian;Estonian;Latvian;Maltese");

        public static string DynamicSearchAdsGlobalizationEUPhase2 => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationEUPhase2", "Turkish;Serbian;Bosnian;Albanian;Macedonian;Icelandic");

        public static string DynamicSearchAdsGlobalizationJapan => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationJapan", "Japanese");
        public static string DynamicSearchAdsGlobalizationMENA => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationMENA", "Arabic;Hebrew;Russian;TraditionalChinese");
        public static string DynamicSearchAdsGlobalizationSimplifiedChinese => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationSimplifiedChinese", "SimplifiedChinese");

        public static string DynamicSearchAdsGlobalizationPhase9 => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationPhase9", "Hindi;Thai;Tagalog;Malay;Indonesian");

        public static string DynamicSearchAdsGlobalizationPhase9VI => DynamicConfig.GetValue<string>("DynamicSearchAdsGlobalizationPhase9VI", "Vietnamese");

        public static string DsaWebsiteLanguagesConfigValueForDanishFinnishNorwegian => DynamicConfig.GetValue<string>("DsaWebsiteLanguagesConfigValueForDanishFinnishNorwegian", "Danish;Finnish;Norwegian");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueV2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueV2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationPhase1V2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationPhase1V2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationPhase2V2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationPhase2V2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationJapaneseV2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationJapaneseV2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationSimplifiedChineseV2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationSimplifiedChineseV2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationPhase6V2 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationPhase6V2");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationPhase9 => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationPhase9");
        public static IReadOnlyDictionary<string, HashSet<string>> SSLanguagesConfigValueGlobalizationPhase9VI => GetStringHashSetFromStringKey("SSLanguagesConfigValueGlobalizationPhase9VI");

        public static string PriceExtensionLanguagesConfigValue => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValue", $"{{\"Danish\", \"Dutch\", \"English\", \"Finnish\", \"French\", \"German\", \"Italian\", \"Norwegian\", \"Portuguese\", \"Spanish\", \"Swedish\", \"TraditionalChinese\"}}");

        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhase1 => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhase1", $"{{\"Polish\", \"Greek\", \"Czech\", \"Romanian\", \"Hungarian\", \"Slovak\", \"Bulgarian\", \"Croatian\", \"Lithuanian\", \"Slovenian\", \"Estonian\", \"Latvian\", \"Maltese\"}}");

        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhase2 => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhase2", $"{{\"Turkish\", \"Serbian\", \"Bosnian\", \"Albanian\", \"Macedonian\", \"Icelandic\"}}");
        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhaseJapanese => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhaseJapanese", $"{{\"Japanese\"}}");
        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhase6 => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhase6", $"{{\"Russian\", \"Arabic\", \"Hebrew\"}}");
        public static string PriceExtensionLanguagesConfigValueForGlobalizationSimplifiedChinese => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationSimplifiedChinese", $"{{\"SimplifiedChinese\"}}");

        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhase9 => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhase9", $"{{\"Hindi\", \"Malay\", \"Thai\", \"Indonesian\", \"Tagalog\"}}");
        public static string PriceExtensionLanguagesConfigValueForGlobalizationPhase9VI => DynamicConfig.GetValue<string>("PriceExtensionLanguagesConfigValueForGlobalizationPhase9VI", $"{{\"Vietnamese\"}}");

        public static string PriceExtensionCurrencyCodesConfigValue => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValue", $"{{\"ARS\", \"AUD\", \"BRL\", \"CAD\", \"CHF\", \"CLP\", \"CNY\", \"COP\", \"DKK\", \"EUR\", \"GBP\", \"HKD\", \"INR\", \"MXN\", \"NZD\", \"PEN\", \"PHP\", \"PLN\", \"SEK\", \"SGD\", \"USD\", \"TWD\", \"VEF\"}}");

        public static string PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase2 => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase2", $"{{\"RSD\", \"BAM\", \"ALL\", \"MKD\", \"ISK\"}}");
        public static string PriceExtensionCurrencyCodesConfigValueForGlobalizationPhaseJapanese => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValueForGlobalizationPhaseJapanese", $"{{\"JPY\"}}");
        public static string PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase6 => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase6", $"{{\"AED\", \"ILS\", \"NGN\", \"SAR\"}}");

        public static string PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase9 => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase9", $"{{\"IDR\", \"MYR\", \"THB\"}}");

        public static string PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase9VI => DynamicConfig.GetValue<string>("PriceExtensionCurrencyCodesConfigValueForGlobalizationPhase9VI", $"{{\"VND\"}}");

        public static string PriceExtensionTypesConfigValue => DynamicConfig.GetValue<string>("PriceExtensionTypesConfigValue", $"{{\"Brands\", \"Events\", \"Locations\", \"Neighborhoods\", \"ProductCategories\", \"ProductTiers\", \"ServiceCategories\", \"Services\", \"ServiceTiers\"}}");

        public static string DefaultRemarketingBidAdjustmentConfigValue => DynamicConfig.GetValue<string>("DefaultRemarketingBidAdjustmentConfigValue", "15");

        public static string FirstPartyBundlesConfigValue => DynamicConfig.GetValue<string>("FirstPartyBundlesConfigValue", $"{{\"Productivity\", \"Homepages\", \"Games\"}}");

        public static bool UseCampaignSummaryForPPS => DynamicConfig.GetValue<bool>("UseCampaignSummaryForPPS", true);

        public static bool IncludeScheduleDataFromAXAssociation => DynamicConfig.GetValue("IncludeScheduleDataFromAXAssociation", false);

        public static bool IsKeywordMatchTypeSymbolEnabled => DynamicConfig.GetValue<bool>("IsKeywordMatchTypeSymbolEnabled", false);

        public static List<string> AdExtensionAccountsDisabledForXSSValidation()
        {
            return DynamicConfig.GetListValues("AdExtensionAccountsDisabledForXSSValidation");
        }

        public static int ChangeHistoryRowLimit
        {
            get
            {
                return DynamicConfig.GetValue("ChangeHistoryRowLimit", 0);
            }
        }

        public static int SkipEditorialForUrlSchemeUpdateAds => DynamicConfig.GetValue("SkipEditorialForUrlSchemeUpdateAds", 0);

        public static int SkipEditorialForUrlSchemeUpdateAdExt => DynamicConfig.GetValue("SkipEditorialForUrlSchemeUpdateAdExt", 0);

        public static bool SkipEditorialForAIMResponsiveAds => DynamicConfig.GetValue("SkipEditorialForAIMResponsiveAds", false);

        public static bool SkipEditorialForLinkedInAds => DynamicConfig.GetValue("SkipEditorialForLinkedInAds", false);

        public static int MaxDegreeOfParallelismForLibrarySharding => DynamicConfig.GetValue("MaxDegreeOfParallelismForLibrarySharding", 1);

        public static int MaxConcurrentNumberForOrderNegativeKeywordsBulkLoading => DynamicConfig.GetValue("MaxConcurrentNumberForOrderNegativeKeywordsBulkLoading", 1);
        public static bool EnableHotelAdsConversions => DynamicConfig.GetValue("EnableHotelAdsConversions", false);

        public static int BulkUploadExperimentBatchSize => DynamicConfig.GetValue("BulkUploadExperimentBatchSize", 10);

        public static int BulkUploadExperimentMaxThreads => DynamicConfig.GetValue("BulkUploadExperimentMaxThreads", 1);

        public static bool EnableValidateImageGeoReplication => DynamicConfig.GetValue("EnableValidateImageGeoReplication", false);

        public static int ValidateImageGeoReplicationWaitSeconds => DynamicConfig.GetValue("ValidateImageGeoReplicationWaitSeconds", 5);

        public static string ExperimentCampaignApplyTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("ExperimentCampaignApplyTaskTypeNameForTaskEngine");

        public static string ExperimentCampaignCloneTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("ExperimentCampaignCloneTaskTypeNameForTaskEngine");

        public static bool ExperimentCampaignCloneTaskEnabled => DynamicConfig.GetValue<bool>("ExperimentCampaignCloneTaskEnabled", false);

        public static bool EnableExperimentLoadTaskForEntities => DynamicConfig.GetValue("EnableExperimentLoadTaskForEntities", false);

        public static string AzureCognitiveTranslator => DynamicConfig.GetRequiredValue<string>("AzureCognitiveTranslator");

        public static string AzureCognitiveTranslatorSubscriptionKey => DynamicConfig.GetRequiredValue<string>("AzureCognitiveTranslatorSubscriptionKey");


        public static int ApplyProductPartitionNodeLimitInBatch => DynamicConfig.GetValue<int>("ApplyProductPartitionNodeLimitInBatch", 2000);

        public static int MaxFeedAssociationCountInOneBatch => DynamicConfig.GetValue<int>("MaxFeedAssociationCountInOneBatch", 1000);

        public static bool UseNewGridDataFilter => DynamicConfig.GetValue<bool>("UseNewGridDataFilter", false);


        public static bool ReturnAdAssetLevelRejectionReasons => DynamicConfig.GetValue<bool>("ReturnAdAssetLevelRejectionReasons", false);

        public static short MaximumNumberOfDVSReportsReturned => DynamicConfig.GetValue<short>("MaximumNumberOfDVSReportsReturned", 200);

        public static bool SkipDVS => DynamicConfig.GetValue<bool>("SkipDVS", false);

        public static TimeSpan DefaultCancelTokenTimeout
        {
            get
            {
                return DynamicConfig.GetValue<TimeSpan>("DefaultCancelTokenTimeout", TimeSpan.FromSeconds(60));
            }
        }

        public static string BulkDownloadContainerName => DynamicConfig.GetRequiredValue<string>("BulkDownloadContainerName");

        public static long SynchronousBulkDownloadRowLimit => DynamicConfig.GetRequiredValue<int>("SynchronousBulkDownloadRowLimit");

        public static int SynchronousBulkDownloadTimeLimitInSeconds => DynamicConfig.GetRequiredValue<int>("SynchronousBulkDownloadTimeLimitInSeconds");

        public static int FeedItemAggSvcLocalFanoutThreshold => DynamicConfig.GetValue<int>("FeedItemAggSvcLocalFanoutThreshold", 250000);

        public static bool EnableFeedItemAggSvcLoadByMultipleThreadsInLocalFanout => DynamicConfig.GetValue("EnableFeedItemAggSvcLoadByMultipleThreadsInLocalFanout", false);

        public static TimeSpan CheckoutTimeOut => DynamicConfig.GetValue<TimeSpan>("CheckoutTimeOut", TimeSpan.FromHours(5));

        public static bool UseTestTaskTypeForFeedUploadService => DynamicConfig.GetValue("UseTestTaskTypeForFeedUploadService", false);

        public static bool FeedUploadProtocolValidation => DynamicConfig.GetValue("FeedUploadProtocolValidation", false);

        public static bool UseTestTaskTypeForBAM => DynamicConfig.GetValue("UseTestTaskTypeForBAM", false);

        public static bool AddCroppingTypeForSmartAndManul => DynamicConfig.GetValue("AddCroppingTypeForSmartAndManul", false);

        public static bool ShouldIgnoreShoppingShowcaseAds => DynamicConfig.GetValue<bool>("ShouldIgnoreShoppingShowcaseAds", false);

        public static int TokenRefreshIntervalInHours => DynamicConfig.GetRequiredValue<int>("TokenRefreshIntervalInHours");
        public static int SkypeBatchLimit => DynamicConfig.GetValue<int>("SkypeBatchLimit", 1000);

        public static int SkypeCallIntervalInSeconds => DynamicConfig.GetValue<int>("SkypeCallIntervalInSeconds", 10);

        public static int CallAdExtensionEventMaxRetryCount => DynamicConfig.GetValue<int>("CallAdExtensionEventMaxRetryCount", 8);

        public static bool RefreshPendingExecutionTaskCache => DynamicConfig.GetValue<bool>("RefreshPendingExecutionTaskCache", false);

        public static TimeSpan CompletedDataTimeToLive => TimeSpan.FromMinutes(DynamicConfig.GetRequiredValue<int>("CompletedDataTimeToLiveInMin"));

        public static TimeSpan IncompletedDataTimeToLive => TimeSpan.FromMinutes(DynamicConfig.GetRequiredValue<double>("IncompletedDataTimeToLiveInMin"));

        public static TimeSpan ExtendedIncompletedDataTimeToLive => TimeSpan.FromMinutes(DynamicConfig.GetRequiredValue<int>("ExtendedIncompletedDataTimeToLiveInMin"));

        public static TimeSpan StatusDataTimeToLive => TimeSpan.FromSeconds(DynamicConfig.GetRequiredValue<int>("StatusDataTimeToLiveInSecond"));

        public static bool EnableIncompleteDataLastDataLoadTimeCache => DynamicConfig.GetValue<bool>("EnableIncompleteDataLastDataLoadTimeCache", false);

        public static int PercentForIncompleteDataLastDataLoadTimeCache => DynamicConfig.GetValue<int>("PercentForIncompleteDataLastDataLoadTimeCache", 0);

        public static List<string> ReportTypeForIncrementalDataCheckChange = DynamicConfig.GetListValues("ReportTypeForIncrementalDataCheckChange");

        public static bool EnableMcaDgoEvent => DynamicConfig.GetValue<bool>("EnableMcaDgoEvent", false);

        public static bool EnableOverlappingTargetHandle => DynamicConfig.GetValue<bool>("EnableOverlappingTargetHandle", false);

        public static bool EnableAllowFBInsDomain => DynamicConfig.GetValue<bool>("EnableAllowFBInsDomain", false);

        public static bool McaFbAdminSystemUserAccessToken => DynamicConfig.GetValue<bool>("McaFbAdminSystemUserAccessToken", false);

        public static bool McaFbBusinessId => DynamicConfig.GetValue<bool>("McaFbBusinessId", false);

        public static int QueueOfflineTaskForKeywordBulkOPCSyncIntervalInMins => DynamicConfig.GetValue<int>("QueueOfflineTaskForKeywordBulkOPCSyncIntervalInMins", 0);

        public static int QueueOfflineTaskForKeywordBulkOPCSyncDelayInMins => DynamicConfig.GetValue<int>("QueueOfflineTaskForKeywordBulkOPCSyncDelayInMins", 60);

        public static bool EnableSynchronousCacheWriteForFastForwardPaging => DynamicConfig.GetValue<bool>("EnableSynchronousCacheWriteForFastForwardPaging", false);

        public static bool GetAssetsIncludingDeleted => DynamicConfig.GetValue<bool>("GetAssetsIncludingDeleted", false);

        public static bool EnableCacheForUpgradedUrl => DynamicConfig.GetValue<bool>("EnableCacheForUpgradedUrl", false);


        public static string AzureHotLrsBlobConnectionString =>
            DynamicConfig.GetRequiredValue<string>("AzureHotLrsBlobConnectionString");

        public static TimeSpan AzureHotLrsBlobConnectionTimeOut =>
            DynamicConfig.GetValue<TimeSpan>("AzureHotLrsBlobConnectionTimeOut", TimeSpan.FromSeconds(30));

        public static TimeSpan AggergatorServiceRemoteNodeConsumerPollDelay =>
            DynamicConfig.GetValue<TimeSpan>("AggergatorServiceRemoteNodeConsumerPollDelay", TimeSpan.FromMilliseconds(50));

        public static TimeSpan AggergatorServiceRemoteNodeBlobRetryDelay =>
            DynamicConfig.GetValue<TimeSpan>("AggergatorServiceRemoteNodeBlobRetryDelay", TimeSpan.FromMilliseconds(200));

        public static TimeSpan AggergatorServiceRemoteNodeProducerMaxBatchWaitTime =>
            DynamicConfig.GetValue<TimeSpan>("AggergatorServiceRemoteNodeProducerMaxBatchWaitTime", TimeSpan.FromMilliseconds(500));

        public static int AggergatorServiceRemoteNodeProducerBatchSize =>
            DynamicConfig.GetValue<int>("AggergatorServiceRemoteNodeProducerBatchSize", 200000);

        public static bool EnableApplyPatchToAudienceCampaignForLogoAndCallToAction => DynamicConfig.GetValue("EnableApplyPatchToAudienceCampaignForLogoAndCallToAction", false);

        public static bool ConversionGoalSelectionEnabled => DynamicConfig.GetValue(nameof(ConversionGoalSelectionEnabled), false);

        public static bool UseDateInBulkDownloadFileName => DynamicConfig.GetValue<bool>("UseDateInBulkDownloadFileName", false);

        public static IReadOnlyList<long> SkypeReprovisionWhiteUserIdList => DynamicConfig.GetValue("SkypeReprovisionWhiteUserIdList", EmptyLongList);

        public static bool DisableParallelReprovisionDbCall => DynamicConfig.GetValue("DisableParallelReprovisionDbCall", false);

        public static bool EnableAudienceIdFilter => DynamicConfig.GetValue("EnableAudienceIdFilter", false);

        public static int MaxCriterionRowCountForSerializeInBulkFileWriting => DynamicConfig.GetValue("MaxCriterionRowCountForSerializeInBulkFileWriting", 100);

        public static int MaxReturnedRowCountPerShard => DynamicConfig.GetValue("MaxReturnedRowCountPerShard", 100000);

        public static int MaxReturnedRowCountPerLibraryShard => DynamicConfig.GetValue("MaxReturnedRowCountPerLibraryShard", 50000);

        public static int MaxEntityCountInOneBulkLoadBatchPerShard => DynamicConfig.GetValue("MaxEntityCountInOneBulkLoadBatchPerShard", 1000);

        public static int MaxConcurrentNumberForOrderTargetBulkLoading => DynamicConfig.GetValue("MaxConcurrentNumberForOrderTargetBulkLoading", 3);

        public static int MaxConcurrentNumberForOrderTargetSerailization => DynamicConfig.GetValue("MaxConcurrentNumberForOrderTargetSerailization", 3);

        public static string ScriptCheckPointContainerName => DynamicConfig.GetRequiredValue<string>("ScriptCheckPointContainerName");

        public static bool SaveScriptBlobAuthorizationMetadata => DynamicConfig.GetValue<bool>("SaveScriptBlobAuthorizationMetadata", false);

        public static bool VerifyScriptBlobAuthorizationMetadata => DynamicConfig.GetValue<bool>("VerifyScriptBlobAuthorizationMetadata", false);

        public static int TaskExecutionRetryTimes => DynamicConfig.GetValue("TaskExecutionRetryTimes", 3);

        public static TimeSpan TaskExecutionRetryDelay => DynamicConfig.GetValue<TimeSpan>("TaskExecutionRetryDelay", TimeSpan.FromSeconds(5));

        public static bool DisableEnforcingKeywordDefaultText => DynamicConfig.GetValue("DisableEnforcingKeywordDefaultText", true);

        public static bool UseCustomEncodingForFeedItemAttributeNames => DynamicConfig.GetValue("UseCustomEncodingForFeedItemAttributeNames", false);

        public static bool UseModValueInsteadOfTrackingIdInlineDownloadFileName => DynamicConfig.GetValue("UseModValueInsteadOfTrackingIdInlineDownloadFileName", false);

        public static int SmartListingCategoryCount => DynamicConfig.GetValue("SmartListingCategoryCount", 1);

        public static int SmartListingProductOrServiceCountLimit => DynamicConfig.GetValue("SmartListingProductOrServiceCountLimit", 10);

        public static int SmartCampaignProductOrServiceCountLimit => DynamicConfig.GetValue("SmartCampaignProductOrServiceCountLimit", 50);

        public static int SmartCampaignProductOrServiceOfflineCountLimit => DynamicConfig.GetValue("SmartCampaignProductOrServiceOfflineCountLimit", 100);

        public static float DefaultMinimumBudgetKpi => DynamicConfig.GetValue("DefaultMinimumBudgetKpi", 2);

        public static float DefaultSuggestedBudgetKpi => DynamicConfig.GetValue("DefaultSuggestedBudgetKpi", 20);

        public static float DefaultMaximumBudgetKpi => DynamicConfig.GetValue("DefaultMaximumBudgetKpi", 200);

        public static float DefaultGlobalMaximumBudgetKpi => DynamicConfig.GetValue("DefaultGlobalMaximumBudgetKpi", 2000);

        public static float SmartRecommendationMaxLimit => DynamicConfig.GetValue("SmartRecommendationMaxLimit", 15);

        public static bool EnableSearchPhraseStatusVerboseLogging => DynamicConfig.GetValue("EnableSearchPhraseStatusVerboseLogging", false);

        public static bool LogExtendedCustomParameterAndFinalUrlSuffixUsage => DynamicConfig.GetValue("LogExtendedCustomParameterAndFinalUrlSuffixUsage", false);

        public static bool EnableMMAUnderDSAAdgroupsFlag => DynamicConfig.GetValue("EnableMMAUnderDSAAdgroupsFlag", false);


        public static bool EnableGetProfileDataFileUrl => DynamicConfig.GetValue("EnableGetProfileDataFileUrl", false);

        public static bool EnableSearchCompanies => DynamicConfig.GetValue("EnableSearchCompanies", false);

        public static int TopNResultOfSearchCompanies => DynamicConfig.GetValue("TopNResultOfSearchCompanies", 1000);

        public static string AdStrengthBlobContainerName => DynamicConfig.GetRequiredValue<string>("AdStrengthBlobContainerName");

        public static string AdStrengthBlobTreeFileName => DynamicConfig.GetRequiredValue<string>("AdStrengthBlobTreeFileName");

        public static string AdStrengthv2FileName => DynamicConfig.GetRequiredValue<string>("AdStrengthv2FileName");

        public static bool EnableAdStrengthPendingStatus => DynamicConfig.GetValue("EnableAdStrengthPendingStatus", false);

        public static bool UseFileFetchUserErrorCodes => DynamicConfig.GetValue<bool>("UseFileFetchUserErrorCodes", false);

        public static bool IncreaseCustomParameterInImportFlow => DynamicConfig.GetValue("IncreaseCustomParameterInImportFlow", false);

        public static int GetFeedItemIdMappingBatchSize => DynamicConfig.GetValue("GetFeedItemIdMappingBatchSize", 10000);

        public static bool EnablePilotCheckForInvalidLanguages => DynamicConfig.GetValue("EnablePilotCheckForInvalidLanguages", false);

        public static bool PassInTrackingInfoInTargetProcs => DynamicConfig.GetValue("PassInTrackingInfoInTargetProcs", false);

        public static bool ThrottleReportAPICalls => DynamicConfig.GetValue("ThrottleReportAPICalls", false);

        public static int ProductGroupSplitUpdateBatchSize => DynamicConfig.GetRequiredValue<int>("ProductGroupSplitUpdateBatchSize");
        public static int HotelListingGroupSplitUpdateBatchSize => DynamicConfig.GetRequiredValue<int>("HotelListingGroupSplitUpdateBatchSize"); // ???

        public static int ProductGroupBulkEditUpdateMaxThread => DynamicConfig.GetRequiredValue<int>("ProductGroupBulkEditUpdateMaxThread");

        public static int ProductGroupOnlineBiDataLoadingOfferLimit => DynamicConfig.GetRequiredValue<int>("ProductGroupOnlineBiDataLoadingOfferLimit");

        public static int HotelListingGroupOnlineBiDataLoadingHotelLimit => DynamicConfig.GetRequiredValue<int>("HotelListingGroupOnlineBiDataLoadingHotelLimit");

        public static int ProductGroupOfflineBiDataLoadingOfferLimit => DynamicConfig.GetRequiredValue<int>("ProductGroupOfflineBiDataLoadingOfferLimit");
        public static bool HotelListingTreeTrackingClickhouseEnabled => DynamicConfig.GetRequiredValue<bool>("HotelListingTreeTrackingClickhouseEnabled");

        public static bool OrderProductTreeTrackingClickhouseEnabled => DynamicConfig.GetRequiredValue<bool>("OrderProductTreeTrackingClickhouseEnabled");
        public static int OffersProcessBatchCount => DynamicConfig.GetRequiredValue<int>("OffersProcessBatchCount");

        public static bool SkipBiLoadWhenProductOffersOverLimit => DynamicConfig.GetValue("SkipBiLoadWhenProductOffersOverLimit", false);

        public static int ConversionAdviceEventLimit => DynamicConfig.GetRequiredValue<int>("ConversionAdviceEventLimit");

        public static bool EnableTaskStateChangeNotifications => DynamicConfig.GetValue("EnableTaskStateChangeNotifications", false);

        public static bool SendTaskStateChangesSynchronously => DynamicConfig.GetValue("SendTaskStateChangesSynchronously", false);

        public static string TaskStateChangeTopicName => DynamicConfig.GetValue("TaskStateChangeTopicName", "TaskStateChange");

        public static TimeSpan TaskStateChangeMessageLifespan => DynamicConfig.GetValue("TaskStateChangeMessageLifespan", TimeSpan.FromMinutes(1));

        public static bool EnableParallelProcessPPSEvents => DynamicConfig.GetValue("EnableParallelProcessPPSEvents", false);


        public static int PercentAccountsForNewFlowQueuePPSEvents => DynamicConfig.GetValue("PercentAccountsForNewFlowQueuePPSEvents", 0);

        public static int CampaignExcludedSitesBatchSize => DynamicConfig.GetValue("CampaignExcludedSitesBatchSize", 1);

        public static int CampaignExcludedSitesMaxThreads => DynamicConfig.GetValue("CampaignExcludedSitesMaxThreads", 10);
        public static bool EnableProductFilterSprocV2 => DynamicConfig.GetValue("EnableProductFilterSprocV2", false);

        public static bool EnablePPSVerifyCampaignIdsNewFlow => DynamicConfig.GetValue("EnablePPSVerifyCampaignIdsNewFlow", false);

        public static bool EnableAdCustomizerFeedItemStatusFilter => DynamicConfig.GetValue("EnableAdCustomizerFeedItemStatusFilter", false);

        public static bool EnableDisclaimerAds => DynamicConfig.GetValue("EnableDisclaimerAds", false);
        public static bool EnableDisclaimerBulkApiSupport => DynamicConfig.GetValue("EnableDisclaimerBulkApiSupport", false);

        public static bool EnableAdminPilotFlagLogging => DynamicConfig.GetValue("EnableAdminPilotFlagLogging", false);

        public static bool IsIfAndCountdownForDSAEnabled => DynamicConfig.GetValue("IsIfAndCountdownForDSAEnabled", false);

        public static bool IsIFFunctionForRSAEnabled => DynamicConfig.GetValue("IsIFFunctionForRSAEnabled", false);
        public static bool IsLocationForEXTAEnabled => DynamicConfig.GetValue("IsLocationForEXTAEnabled", false);
        public static bool IsCountDownInRSAFormatForEXTAEnabled => DynamicConfig.GetValue("IsCountDownInRSAFormatForEXTAEnabled", false);
        public static bool IsAdCustomizerForRSAEnabled => DynamicConfig.GetValue("IsAdCustomizerForRSAEnabled", false);
        public static int RSAAdCustomizerFeedUploadProcessingBatchSize => DynamicConfig.GetValue("RSAAdCustomizerFeedUploadgBatchSize", 200);
        public static int RSAAdCustomizerAttributeNameLengthLimit => DynamicConfig.GetValue("RSAAdCustomizerAttributeNameLengthLimit", 40);
        public static int RSAAdCustomizerAttributeValueLengthLimit => DynamicConfig.GetValue("RSAAdCustomizerAttributeValueLengthLimit", 90);
        public static bool IsRSACustomizerOfflineDeleteTaskEnabled => DynamicConfig.GetValue("IsRSACustomizerOfflineDeleteTaskEnabled", false);
        public static ReadOnlyCollection<long> IsEditorialForRSAAdCustomizerAttributeEnabledForCustomerIds => DynamicConfig.GetValue("IsEditorialForRSAAdCustomizerAttributeEnabledForCustomerIds", new ReadOnlyCollection<long>(new List<long>() { ********* }));

        public static ReadOnlyCollection<long> IfAndCountdownDSATestCustomerIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("IfAndCountdownDSATestCustomerIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static bool IsEditorialForRSAAdCustomizerAttributeEnabled(long accountId)
        {
            return IsEditorialForRSAAdCustomizerAttributeEnabledForCustomerIds.Contains(accountId)
                || (accountId % 100) <= DynamicConfig.GetValue("PercentAccountsForEditorialForRSAAdCustomizerAttributeEnabled", 0);
        }

        public static bool IsInPilotForDownloadAdgroupSummaryFetchOptimization(long accountId)
        {
            int percentage = DynamicConfig.GetValue("PercentAccountsForDownloadAdgroupSummaryFetchOptimization", 0);
            return (accountId % 100) < percentage;
        }

        public static bool UseLocalSecondaryForCampaignInlineChart => DynamicConfig.GetValue("UseLocalSecondaryForCampaignInlineChart", false);

        public static bool EnableProductPropertyDomainDataCacheOptimization => DynamicConfig.GetValue("EnableProductPropertyDomainDataCacheOptimization", false);

        public static bool NoBSCPropertydataInitialize => DynamicConfig.GetValue("NoBSCPropertydataInitialize", false);
        public static bool EnableProductGroupSummaryToLocalSecondary => DynamicConfig.GetValue("EnableProductGroupSummaryToLocalSecondary", false);

        public static bool EnableProductAttributeBIDataToLocalSecondary => DynamicConfig.GetValue("EnableProductAttributeBIDataToLocalSecondary", false);

        public static bool DisabledAutoBiddingProductGroupNodeBidUpdate => DynamicConfig.GetValue("DisabledAutoBiddingProductGroupNodeBidUpdate", false);

        public static bool EnablePartialProductGroupNodeBidUpdate => DynamicConfig.GetValue("EnablePartialProductGroupNodeBidUpdate", false);

        public static string ReportResourcesBaseName => DynamicConfig.GetValue("ReportResourcesBaseName", "Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.Resources.strings");

        public static string ReportResourcesFileAssemblyPath => DynamicConfig.GetValue("ReportResourcesFileAssemblyPath", "Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.Resources");

        public static string MessageManagerServiceUrl => DynamicConfig.GetValue("MessageManagerServiceUrl",
            "https://ClientCenterMT.redmond.corp.microsoft.com:6089/messagecenter/messagemanager/mt");

        public static ReadOnlyCollection<string> ExportVideoUrlDevTokenList => DynamicConfig.GetValue<ReadOnlyCollection<string>>("ExportVideoUrlDevTokenList", new ReadOnlyCollection<string>(new List<string>()));

        public static ReadOnlyCollection<string> SetShouldServeOnMSANDevTokenList => DynamicConfig.GetValue<ReadOnlyCollection<string>>("SetShouldServeOnMSANDevTokenList", new ReadOnlyCollection<string>(new List<string>()));

        public static ReadOnlyCollection<string> GetCampaignDealIdsDevTokenList => DynamicConfig.GetValue<ReadOnlyCollection<string>>("GetCampaignDealIdsDevTokenList", new ReadOnlyCollection<string>(new List<string>()));

        public static int BatchSizeOfPullingDealCriterions => DynamicConfig.GetValue("BatchSizeOfPullingDealCriterions", 500);

        public static int ParallelismOfPullingDealCriterions => DynamicConfig.GetValue("ParallelismOfPullingDealCriterions", 1);

        public static string MultiChannelExpertSyncDevToken => DynamicConfig.GetValue("MultiChannelExpertSyncDevToken", string.Empty);

        public static bool FetchLanguageAndTargetDataForGetAdgroupsByCampaignId => DynamicConfig.GetValue("FetchLanguageAndTargetDataForGetAdgroupsByCampaignId", false);

        public static bool EnableFeedAttributeNewLineCheck => DynamicConfig.GetValue("EnableFeedAttributeNewLineCheck", false);

        public static bool EnableFeedAttributeEnumCheck => DynamicConfig.GetValue("EnableFeedAttributeEnumCheck", false);

        public static bool EnableStaticGridFilterMetadataResult => DynamicConfig.GetValue("EnableStaticGridFilterMetadataResult", false);

        public static bool EnableLocalSecondaryForCHSplitCall => DynamicConfig.GetValue("EnableLocalSecondaryForCHSplitCall", false);

        public static List<string> PartialConversionWhiteListGrids => DynamicConfig.GetListValues("PartialConversionWhiteListGrids");

        public static bool FetchTargetDataForGetAdgroupsByIds => DynamicConfig.GetValue("FetchTargetDataForGetAdgroupsByIds", false);

        public static bool EnableCriterionLimitAtCustomerLevel => DynamicConfig.GetValue("EnableCriterionLimitAtCustomerLevel", false);

        public static bool EnableUPFetchMTReport => DynamicConfig.GetValue("EnableUPFetchMTReport", false);

        public static bool OfferDBAccessTrimStrings => DynamicConfig.GetValue("OfferDBAccessTrimStrings", false);

        public static bool IsInPilotForDisableOrderChangeTrackingFetchForAdextensions(long accountId)
        {
            int percentage = DynamicConfig.GetValue("PercentAccountsForDisableOrderChangeTrackingFetchForAdextensions", 0);
            return (accountId % 100) < percentage;
        }

        public static bool RefreshFraudTimer => DynamicConfig.GetValue("RefreshFraudTimer", false);
        public static TimeSpan FraudChannelFactoryCloseTimespan => DynamicConfig.GetValue<TimeSpan>("FraudChannelFactoryCloseTimespan", TimeSpan.FromMinutes(5));

        public static bool UseNewOrderLoadProcForAutomatedExtension => DynamicConfig.GetValue("UseNewOrderLoadProcForAutomatedExtension", false);

        public static string ShutterStockImageEndpoint => DynamicConfig.GetValue("ShutterStockImageEndpoint", "https://api-sandbox.shutterstock.com/v2/images");
        public static string ShutterStockCompImageEndpoint => DynamicConfig.GetValue("ShutterStockCompImageEndpoint", "https://api.shutterstock.com/v2/images");
        public static string ShutterStockAppName => DynamicConfig.GetValue("ShutterStockAppName", "MSDEMO");
        public static string ShutterStockCompAppName => DynamicConfig.GetValue("ShutterStockCompAppName", "MSDEMO");

        public static string ShutterStockAuthToken => DynamicConfig.GetValue("ShutterStockAuthToken", "");
        public static string ShutterStockCompAuthToken => DynamicConfig.GetValue("ShutterStockCompAuthToken", "");
        public static string ShutterStockSubscriptionId => DynamicConfig.GetValue("ShutterStockSubscriptionId", "s49348952");
        public static string ShutterStockCompSubscriptionId => DynamicConfig.GetValue("ShutterStockCompSubscriptionId", "s49348952");
        public static string ShutterStockCompMetaDataField2 => DynamicConfig.GetValue("ShutterStockCompMetaDataField2", "");
        public static int ShutterStockRetryCount => DynamicConfig.GetValue("ShutterStockRetryCount", 3);
        public static int ShutterStockMaxEntityCount => DynamicConfig.GetValue("ShutterStockMaxEntityCount", 1);
        public static bool ShutterStockKeywordSafeSearch => DynamicConfig.GetValue("ShutterStockKeywordSafeSearch", true);
        public static string ShutterStockNotList => DynamicConfig.GetValue("ShutterStockNotList", "logo");
        public static string ShutterStockNotList2 => DynamicConfig.GetValue("ShutterStockNotList2", "logo");
        public static string ODataServiceUrl => DynamicConfig.GetValue("ODataServiceUrl", "http://localhost:10877/ODataApi/Advertiser/V2/");
        public static int StockImageEligiblityBatchSize => DynamicConfig.GetValue("StockImageEligiblityBatchSize", 40);
        public static int ImageDownloadRetryCount => DynamicConfig.GetValue("ImageDownloadRetryCount", 3);
        public static string AMSMockOutputContainerName => DynamicConfig.GetValue("AMSMockOutputContainerName", "asset-10ffcd02-02ee-43b2-ae93-572a65ecc077");
        public static int VideoUploadSasUrlExpiryTimeInSeconds => DynamicConfig.GetValue("VideoUploadSasUrlExpiryTimeInSeconds", 10800);

        public static bool InfiniteVideoUploadSasUrlExpiry => DynamicConfig.GetValue("InfiniteVideoUploadSasUrlExpiry", false);
        public static string VideoStorageConnectionString(int blobStoreKey)
        {
            return DynamicConfig.GetValue($"VideoStorageConnectionString_{blobStoreKey}", String.Empty);
        }

        public static string VideoUploadContainerName => DynamicConfig.GetValue("VideoUploadContainerName", "videouploads");
        public static string VideoStorageContainerName => DynamicConfig.GetValue("VideoStorageContainerName", "videostorage");
        public static string StaticFileStorageContainerName => DynamicConfig.GetValue("StaticFileStorageContainerName", "$web");
        public static string StaticFileBaseUrl => DynamicConfig.GetValue("StaticFileBaseUrl", "");
        public static string VideoNewAadClientId => DynamicConfig.GetValue("VideoNewAadClientId", "0f755238-e5d3-42c6-9e53-9e6d71de730f");
        public static string VideoNewNewAadClientId => DynamicConfig.GetValue("VideoNewNewAadClientId", String.Empty);
        public static string VideoNewAadSecret => DynamicConfig.GetValue("VideoNewAadSecret", String.Empty);
        public static string VideoNewAadTenantId => DynamicConfig.GetValue("VideoNewAadTenantId", "72f988bf-86f1-41af-91ab-2d7cd011db47");
        public static string VideoSubscriptionId => DynamicConfig.GetValue("VideoSubscriptionId", "ca1b45b0-6764-457d-96e9-01874dd5badb");
        public static string VideoArmAadAudience => DynamicConfig.GetValue("VideoArmAadAudience", "https://management.core.windows.net");
        public static string VideoArmEndpoint => DynamicConfig.GetValue("VideoArmEndpoint", "https://management.azure.com");
        public static int VideoAmsRetryMinSeconds => DynamicConfig.GetValue("VideoAmsRetryMinSeconds", 60);
        public static string VideoHostAccounts => DynamicConfig.GetValue("VideoHostAccounts", "1,adsappsvideosi,West US 2,AdsAppsVideoService,adsappsvideosi-usw22.streaming.media.azure.net,1,adsappsvideostoragesi,0,http://localhost:1901/dam.mock/videotranscoding,http://localhost:1901/dam.mock/encodingtable;2,adsappsvideosi,West US 2,AdsAppsVideoService,adsappsvideosi-usw22.streaming.media.azure.net,2,adsappsvideostoragesi2,0,http://localhost:1901/dam.mock/videotranscoding,http://localhost:1901/dam.mock/encodingtable;3,adsappsvideosi23,West US 2,AdsAppsVideoService,adsappsvideosi23-usw22.streaming.media.azure.net,3,adsappsvideostoragesi23,10,http://localhost:1901/dam.mock/videotranscoding,http://localhost:1901/dam.mock/encodingtable;");
        public static int VideoDownloadRetrySeconds => DynamicConfig.GetValue("VideoDownloadRetrySeconds", 5);
        public static int VideoDownloadRetryCount => DynamicConfig.GetValue("VideoDownloadRetryCount", 5);
        public static int VideoAmsRetryIntervalBaseNumber => DynamicConfig.GetValue("VideoAmsRetryIntervalBaseNumber", 166);

        public static string MSANLiftADFAppTenantId
        {
            get
            {
                return DynamicConfig.GetValue("MSANLiftADFAppTenantId", "975f013f-7f24-47e8-a7d3-abc4752bf346");
            }
        }

        public static string UCMTenantId
        {
            get
            {
                return DynamicConfig.GetValue("UCMTenantId", "72f988bf-86f1-41af-91ab-2d7cd011db47");
            }
        }

        public static string UCMStorageAccount
        {
            get
            {
                return DynamicConfig.GetValue("UCMStorageAccount", "https://ucmdatalakeprodgen2.blob.core.windows.net");
            }
        }

        public static string UCMContainer
        {
            get
            {
                return DynamicConfig.GetValue("UCMContainer", "gen1");
            }
        }

        public static string UCMCustomerHierarchyPath
        {
            get
            {
                return DynamicConfig.GetValue("UCMCustomerHierarchyPath", "Shared/Global/UCMBA_Level0/DimCustomerGroup/DimCustomerGroup.parquet");
            }
        }

        public static string DemandMetricsADFTenantId
        {
            get
            {
                return DynamicConfig.GetValue("DemandMetricsADFTenantId", "72f988bf-86f1-41af-91ab-2d7cd011db47");
            }
        }

        public static string DemandMetricsADFSubscriptionId
        {
            get
            {
                return DynamicConfig.GetValue("DemandMetricsADFSubscriptionId", "ca1b45b0-6764-457d-96e9-01874dd5badb");
            }
        }

        public static string DemandMetricsADFResourceGroupName
        {
            get
            {
                return DynamicConfig.GetValue("DemandMetricsADFResourceGroupName", "DemandMetrics-RG");
            }
        }

        public static string DemandMetricsADFName
        {
            get
            {
                return DynamicConfig.GetValue("DemandMetricsADFName", "DemandMetrics-DF");
            }
        }

        public static string MSANLiftADFAppClientId
        {
            get
            {
                return DynamicConfig.GetValue("MSANLiftADFAppClientId", "5a5d3260-7538-4a6d-bc2f-cdf17665c8e3");
            }
        }

        public static int MaximumAllowedOverlappedExperiments
        {
            get
            {
                return DynamicConfig.GetValue<int>("MaximumAllowedOverlappedExperiments", 3);
            }
        }

        public static string DefaultDeltaBackOff => DynamicConfig.GetValue("DefaultDeltaBackOff", "1");
        public static string DefaultMaxRetryCount => DynamicConfig.GetValue("DefaultMaxRetryCount", "3");
        public static string DefaultMaxExecutionTimeInSecondsForUploading => DynamicConfig.GetValue("DefaultMaxExecutionTimeInSecondsForUploading", "3600");
        public static string DefaultSingleBlobUploadThresholdInBytes => DynamicConfig.GetValue("DefaultSingleBlobUploadThresholdInBytes", "67108864");
        public static string BlobAccessExpiryTimeInSeconds => DynamicConfig.GetValue("BlobAccessExpiryTimeInSeconds", "120");
        public static string MaxDegreeOfParallelismForUploading => DynamicConfig.GetValue("MaxDegreeOfParallelismForUploading", "1");

        public static string SmartPageContainerName => DynamicConfig.GetRequiredValue<string>("SmartPageContainerName");

        public static string SmartPageCustomDomainCertKeyVaultName => DynamicConfig.GetRequiredValue<string>("SmartPageCustomDomainCertKeyVaultName");
        public static string SmartPageCustomDomainNginxConfigContainerName => DynamicConfig.GetRequiredValue<string>("SmartPageCustomDomainNginxConfigContainerName");
        public static string SmartPageCustomDomainMappingFileName => DynamicConfig.GetRequiredValue<string>("SmartPageCustomDomainMappingFileName");

        public static string SmartPageDomain => DynamicConfig.GetRequiredValue<string>("SmartPageDomain");

        public static bool StockImageReKeyIgnoreNoImageData => DynamicConfig.GetValue("StockImageReKeyIgnoreNoImageData", true);

        public static bool IsInPilotForKeywordLoadBIDataThroughSubSharding(long accountId)
        {
            int percentage = DynamicConfig.GetValue("PercentAccountsForKeywordLoadBIDataThroughSubSharding", 0);
            return (accountId % 100) < percentage;
        }

        public static string CampaignUserSecretKeyVaultName => DynamicConfig.GetValue("CampaignUserSecretKeyVaultName", "CampaignUserSecretKVProd");

        public static string CampaignUserSecretKeyVaultClientId => DynamicConfig.GetValue("CampaignUserSecretKeyVaultClientId", "NA");

        public static bool AutoBiddingTargetRoasEnableConversionAndRevenueCheck => DynamicConfig.GetValue("AutoBiddingTargetRoasEnableConversionAndRevenueCheck", true);

        public static bool EnableChangeBiddingSchemeForSmartUnifiedProduct => DynamicConfig.GetValue("EnableChangeBiddingSchemeForSmartUnifiedProduct", true);

        public static IReadOnlyList<long> EnableUrlListToUrlInPageFeedImportAccountList => DynamicConfig.GetValue("EnableUrlListToUrlInPageFeedImportAccountList", EmptyLongList);

        public static IReadOnlyList<long> EnableUrlListToUrlInPageFeedImportCustomerList => DynamicConfig.GetValue("EnableUrlListToUrlInPageFeedImportCustomerList", EmptyLongList);

        public static bool EnableImportAssetGroupLookupDedupe => DynamicConfig.GetValue("EnableImportAssetGroupLookupDedupe", true);

        public static HashSet<long> CustomerIdWithDSAAds => new HashSet<long>(DynamicConfig.GetValue("CustomerIdWithDSAAds", EmptyLongList));

        public static HashSet<long> AccountIdListToThrottleSiteExclusionCalls => new HashSet<long>(DynamicConfig.GetValue("AccountIdListToThrottleSiteExclusionCalls", EmptyLongList));

        public static int FeedItemAggregatorServiceDDEGridThreshold => DynamicConfig.GetValue<int>("FeedItemAggregatorServiceDDEGridThreshold", 500000);

        public static int FeedItemAggregatorServiceDDEInlineDownloadThreshold => DynamicConfig.GetValue<int>("FeedItemAggregatorServiceDDEInlineDownloadThreshold", 2000000);

        public static int BulkEditMaxRetries => DynamicConfig.GetValue("BulkEditMaxRetries", 3);

        public static int BulkEditRetryIntervalInSeconds => DynamicConfig.GetValue("BulkEditRetryIntervalInSeconds", 5);

        public static bool EnableUserPreferenceImportFeatures => DynamicConfig.GetValue("EnableUserPreferenceImportFeatures", false);

        public static bool EnableBiddingSchemeCurrencyConversion => DynamicConfig.GetValue("EnableBiddingSchemeCurrencyConversion", false);

        public static bool EnableNegativeKeywordLimitAtAccountLevel => DynamicConfig.GetValue("EnableNegativeKeywordLimitAtAccountLevel", false);

        public static bool TravelGetCountFromLocalSecondary => DynamicConfig.GetValue("TravelGetCountFromLocalSecondary", false);

        public static int MaxFeedUploadFileHeaderCount => DynamicConfig.GetValue("MaxFeedUploadFileHeaderCount", 1000);

        public static int TestCustomerIdMaxFeed => DynamicConfig.GetValue("TestCustomerIdMaxFeed", *********);

        public static int TestCustomerMaxFeedUploadDataRowCnt => DynamicConfig.GetValue("TestCustomerMaxFeedUploadDataRowCnt", ********);

        public static int ImageAdExtensionCroppingBackfillBatchSize = DynamicConfig.GetValue("ImageAdExtensionCroppingBackfillBatchSize", 1000);

        public static int MaxFeedUploadDataRowCnt => DynamicConfig.GetValue("MaxFeedUploadDataRowCnt", 5000000);

        public static int EditResultsToAzurePilotPercentage => DynamicConfig.GetValue<int>("EditResultsToAzurePilotPercentage", 0);


        public static bool NegativeKeywordLoadNewImplementation => DynamicConfig.GetValue("NegativeKeywordLoadNewImplementation", false);

        public static int PercentCustomersForNegativeKeywordCatalogInsert => DynamicConfig.GetValue("PercentCustomersForNegativeKeywordCatalogInsert", 0);

        public static int PercentCustomersForNegativeKeywordInsertOnlyCatalog => DynamicConfig.GetValue("PercentCustomersForNegativeKeywordInsertOnlyCatalog", 0);

        public static int PercentCustomersForNegativeKeywordCatalogFetch => DynamicConfig.GetValue("PercentCustomersForNegativeKeywordCatalogFetch", 0);

        #region  Fraud related config settings
        public static string FraudEvaluateAdGroupUpdateEventHubName => DynamicConfig.GetValue("FraudEvaluateAdGroupUpdateEventHubName", string.Empty);

        public static string FraudEvaluateAssetGroupEventHubName => DynamicConfig.GetValue("FraudEvaluateAssetGroupEventHubName", string.Empty);

        public static string FraudEvaluateCampaignEventHubName => DynamicConfig.GetValue("FraudEvaluateCampaignEventHubName", string.Empty);

        public static string FraudEvaluateAdsEventHubName => DynamicConfig.GetValue("FraudEvaluateAdsEventHubName", string.Empty);

        public static string FraudEvaluateAccountsEventHubName => DynamicConfig.GetValue("FraudEvaluateAccountsEventHubName", string.Empty);

        public static int MaxAdGroupUpdateByCampaignIdEventHubBatchSizeToFraudCheck => DynamicConfig.GetValue("MaxAdGroupUpdateByCampaignIdEventHubBatchSizeToFraudCheck", 0);

        public static int MaxAssetGroupUpdateByCampaignIdEventHubBatchSizeToFraudCheck => DynamicConfig.GetValue("MaxAssetGroupUpdateByCampaignIdEventHubBatchSizeToFraudCheck", 0);

        public static string FraudEventHubConnectionString => DynamicConfig.GetValue("FraudEventHubConnectionString", string.Empty);

        public static string FraudMIClientId => DynamicConfig.GetValue("FraudMIClientId", "bf7d8c98-83a4-40a9-8afe-6c2734a78e6f");

        public static string FraudEventHubURI => DynamicConfig.GetValue("FraudEventHubURI", @"sb://fraudevaluatedatatest.servicebus.windows.net");

        public static bool FraudLogEventCount => DynamicConfig.GetValue("FraudLogEventCount", false);

        public static bool FraudLogEventBody => DynamicConfig.GetValue("FraudLogEventBody", false);

        public static string EditorialTestEventHubConnectionString => DynamicConfig.GetValue("EditorialTestEventHubConnectionString", string.Empty);
        #endregion

        #region Editorial Event Hubs
        public static string EditorialAppealEventHubName => DynamicConfig.GetValue("EditorialAppealEventHubName", string.Empty);

        public static string EditorialEventHubConnectionString => DynamicConfig.GetValue("EditorialEventHubConnectionString", string.Empty);

        public static IReadOnlyList<int> EditorialAssetEditorialAIMImageAssetAssociationTypes => DynamicConfig.GetValue("EditorialAssetEditorialAIMImageAssetAssociationTypes", EmptyIntList);

        public static bool AssetEditorialRollupFailMissingAds => DynamicConfig.GetValue("AssetEditorialRollupFailMissingAds", true);
        #endregion

        public static int PercentAccountForTargetedCountryBasedEditorialStatusAdExtensionInline => DynamicConfig.GetValue("PercentAccountForTargetedCountryBasedEditorialStatusAdExtensionInline", 0);

        public static int PercentAccountForTargetedCountryBasedEditorialStatusOfflineBulk => DynamicConfig.GetValue("PercentAccountForTargetedCountryBasedEditorialStatusOfflineBulk", 0);

        public static int PercentAccountForESSFraudServiceBus => DynamicConfig.GetValue("PercentAccountForESSFraudServiceBus", 0);

        public static int PercentAccountForESSAccountPenaltyServiceBus => DynamicConfig.GetValue("PercentAccountForESSAccountPenaltyServiceBus", 0);

        public static int PercentAccountForESSUpdateFraudStatusServiceBus => DynamicConfig.GetValue("PercentAccountForESSUpdateFraudStatusServiceBus", 0);

        public static int PercentAccountForAdExAccountInline => DynamicConfig.GetValue("PercentAccountForAdExAccountInline", 0);

        public static int PercentAccountForOrderAndStatusUpdateV53 => DynamicConfig.GetValue("PercentAccountForOrderAndStatusUpdateV53", 0);

        public static string UETClaritySecretKeyVault => DynamicConfig.GetValue("UETClaritySecretKeyVault", string.Empty);
        public static string UETClarityAppName => DynamicConfig.GetValue("UETClarityAppName", "UET-ClarityAPI-SI");

        public static string UETClarityAppId => DynamicConfig.GetValue("UETClarityAppId", string.Empty);
        public static string ClarityTenantId => DynamicConfig.GetValue("ClarityTenantId", string.Empty);
        public static string CampaignMTClientCertName => DynamicConfig.GetValue("CampaignMTClientCertName", "bingads-campaignmt-clientcert-pme");
        public static string UETClarityAuthorityUrl => DynamicConfig.GetValue("UETClarityAuthorityUrl", string.Empty);

        public static string UETClarityScopeUrl => DynamicConfig.GetValue("UETClarityScopeUrl", string.Empty);
        public static string ClarityApiVersionPreviousV2 => DynamicConfig.GetValue("ClarityApiVersionPreviousV2", "v2.0");
        public static string ClarityApiVersionPreviousV1 => DynamicConfig.GetValue("ClarityApiVersionPreviousV1", "v1.0");

        public static string UETClarityUrl => DynamicConfig.GetValue("UETClarityUrl", string.Empty);

        public static string UETClarityUrlForTest => DynamicConfig.GetValue("UETClarityUrlForTest", string.Empty);

        public static bool EnableManualBiddingDeprecation(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableManualBiddingDeprecation", request.OverrideConfigValuesFromTest, true);
        }

        public static bool EnableAutoBidUpdateDeprecation => DynamicConfig.GetValue("EnableAutoBidUpdateDeprecation", false);

        public static int GoogleAdsMaxReceiveMessageSize => DynamicConfig.GetValue<int>("GoogleAdsMaxReceiveMessageSize", 1073741824);

        public static int ProductBiddingCategoryRefreshRateInMinutes => DynamicConfig.GetValue<int>("ProductBiddingCategoryRefreshRateInMinutes", 60);

        public static bool EnableGetLastLoadCompleteTime
        {
            get { return DynamicConfig.GetValue<bool>("EnableGetLastLoadCompleteTime", false); }
        }

        public static bool EnableAudienceSOV
        {
            get { return DynamicConfig.GetValue<bool>("EnableAudienceSOV", false); }
        }

        public static bool EnableESCDataToUCM
        {
            get { return DynamicConfig.GetValue<bool>("EnableESCDataToUCM", false); }
        }

        public static bool TemporarilyDisableGridFilterForNewXandrAPI
        {
            get { return DynamicConfig.GetValue<bool>("TemporarilyDisableGridFilterForNewXandrAPI", false); }
        }

        public static bool EnableRequestLoggingForBIDataForAccounts
        {
            get { return DynamicConfig.GetValue<bool>("EnableRequestLoggingForBIDataForAccounts", false); }
        }

        public static bool EnabledResponseValidationForBIDataForAccounts
        {
            get { return DynamicConfig.GetValue<bool>("EnabledResponseValidationForBIDataForAccounts", false); }
        }

        public static bool AllowThrowExceptionOnPartialResultGetBIDataForAccounts
        {
            get { return DynamicConfig.GetValue<bool>("AllowThrowExceptionOnPartialResultGetBIDataForAccounts", false); }
        }

        public static bool GetEnableMSANMetricsForUCM(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableMSANMetricsForUCM", request.OverrideConfigValuesFromTest, false);
        }

        public static bool GetEnableVideoAdsMetricsForUCM(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableVideoAdsMetricsForUCM", request.OverrideConfigValuesFromTest, false);
        }

        public static bool GetEnablePMaxMetricsForUCM(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnablePMaxMetricsForUCM", request.OverrideConfigValuesFromTest, false);
        }

        public static bool EnableVideoAdsToUCM
        {
            get { return DynamicConfig.GetValue<bool>("EnableVideoAdsToUCM", false); }
        }

        public static bool EnableCountryOfSaleToUCM
        {
            get { return DynamicConfig.GetValue<bool>("EnableCountryOfSaleToUCM", false); }
        }

        public static bool EnablePOPForUCM
        {
            get { return DynamicConfig.GetValue<bool>("EnablePOPForUCM", false); }
        }

        public static bool EnableUniversalConfigTest
        {
            get { return DynamicConfig.GetValue<bool>("EnableUniversalConfigTest", false); }
        }

        public static int MinAccountCountForCampaignWithDataCacheForUCM
        {
            get { return DynamicConfig.GetValue<int>("MinAccountCountForCampaignWithDataCacheForUCM", 0); }
        }

        public static bool EnableCampaignsWithDataCacheForUCMQualityScore
        {
            get { return DynamicConfig.GetValue<bool>("EnableCampaignsWithDataCacheForUCMQualityScore", false); }
        }

        public static bool EnableDBLoadtForCampaignWithDataCacheForUCM
        {
            get { return DynamicConfig.GetValue<bool>("EnableDBLoadtForCampaignWithDataCacheForUCM", false); }
        }

        public static int MaxThresholdForCacheForUCMOptimization
        {
            // set default value to be a lot larger then currently support number of accounts so it doesnt get triggered
            get { return DynamicConfig.GetValue<int>("MaxThresholdForCacheForUCMOptimization", 200000); }
        }

        public static int UCMProcTaskTimeoutInSecondsOverride
        {
            get { return DynamicConfig.GetValue<int>("UCMProcTaskTimeoutInSecondsOverride", 30); }
        }

        public static bool LogAudienceSOVRelatedBIData
        {
            get { return DynamicConfig.GetValue<bool>("LogAudienceSOVRelatedBIData", false); }
        }

        public static bool LogAudienceSegmentationBiData => DynamicConfig.GetValue<bool>("LogAudienceSegmentationBiData", false);

        public static int CustomColumnExpressionOperatorLimit => DynamicConfig.GetValue<int>("CustomColumnExpressionOperatorLimit", 20);

        public static double TopNFeedItemsCacheTTLInMinutes => DynamicConfig.GetValue("TopNFeedItemsCacheTTLInMinutes", 15.0);

        public static int MinimumBusinessCategoryNumber => DynamicConfig.GetValue<int>("MinimumBusinessCategoryNumber", 1);

        public static string FileUploadBaseUri => DynamicConfig.GetValue<string>("FileUploadBaseUri", string.Empty);

        public static int FileUploadClientTimeoutInSeconds => DynamicConfig.GetValue<int>("FileUploadClientTimeoutInSeconds", 100);

        public static string PhoneProvisioningBaseUri => DynamicConfig.GetValue<string>("PhoneProvisioningBaseUri", string.Empty);

        public static string CampaignLegacyBaseUri => DynamicConfig.GetValue<string>("CampaignLegacyBaseUri", string.Empty);

        public static bool EnableUseNewSpForCampaignExcludedSite => DynamicConfig.GetValue("EnableUseNewSpForCampaignExcludedSite", false);

        public static int CustomerSharedEntityDeleteListCleanupOfflineTaskDelayInSec => DynamicConfig.GetValue("CustomerSharedEntityDeleteListCleanupOfflineTaskDelayInSec", 60);

        public static int MaxFeedItemReadBatchSize => DynamicConfig.GetValue("MaxFeedItemReadBatchSize", 100);

        public static int MaxFeedItemsPerBlob => DynamicConfig.GetValue("MaxFeedItemsPerBlob", 10000);

        public static string FeedItemInBlobContainerName => DynamicConfig.GetValue("FeedItemInBlobContainerName", string.Empty);


        public static string ReconciliationReportAzureTableName => DynamicConfig.GetValue("ReconciliationReportAzureTableName", string.Empty);

        public static string ProcessedReconciliationReportAzureTableName => DynamicConfig.GetValue("ProcessedReconciliationReportAzureTableName", string.Empty);

        public static string InputReconReportBlobContainerName => DynamicConfig.GetValue("InputReconReportBlobContainerName", string.Empty);

        public static string PreviewedReconReportBlobContainerName => DynamicConfig.GetValue("PreviewedReconReportBlobContainerName", string.Empty);

        public static string CommittedReconReportBlobContainerPrefix => DynamicConfig.GetValue("CommittedReconReportBlobContainerPrefix", string.Empty);

        public static string StagingProcessedReconReportBlobContainerName => DynamicConfig.GetValue("StagingProcessedReconReportBlobContainerName", string.Empty);

        public static string InputProcessedReconReportBlobContainerPrefix => DynamicConfig.GetValue("InputProcessedReconReportBlobContainerPrefix", string.Empty);

        public static string CommittedProcessedReconReportBlobContainerPrefix => DynamicConfig.GetValue("CommittedProcessedReconReportBlobContainerPrefix", string.Empty);

        public static string InputImportPreferenceBulkUpdateFileContainerName => DynamicConfig.GetValue("InputImportPreferenceBulkUpdateFileContainerName", string.Empty);

        public static string PreviewedImportPreferenceBulkUpdateFileContainerName => DynamicConfig.GetValue("PreviewedImportPreferenceBulkUpdateFileContainerName", string.Empty);

        public static string CommittedImportPreferenceBulkUpdateFileContainerName => DynamicConfig.GetValue("CommittedImportPreferenceBulkUpdateFileContainerName", string.Empty);

        public static string TextAssetCacheNameSpace => DynamicConfig.GetValue("TextAssetCacheNameSpace", "TextAsset");

        public static int accountPercentForUTM_sourceUpdate(CampaignManagementRequest request)
        {
            return GetValue<int>("accountPercentForUTM_sourceUpdate", request.OverrideConfigValuesFromTest, 0);
        }

        public static bool enable_DSAAutoTarget_UTMSourceUpdate(CampaignManagementRequest request)
        {
            return GetValue<bool>("enable_DSAAutoTarget_UTMSourceUpdate", request.OverrideConfigValuesFromTest, false);
        }

        public static int accountPercentForAdExtensionUTM_sourceUpdate(CampaignManagementRequest request)
        {
            return GetValue<int>("accountPercentForAdExtensionUTM_sourceUpdate", request.OverrideConfigValuesFromTest, 0);
        }

        public static int EditorialUserId => DynamicConfig.GetValue("EditorialUserId", 1704269);

        public static bool GotoRedisCacheOnlyForEditorialUserId => DynamicConfig.GetValue("GotoRedisCacheOnlyForEditorialUserId", false);

        public static bool EndorGA(CampaignManagementRequest request)
        {
            return GetValue<bool>("EndorGA", request.OverrideConfigValuesFromTest, false);
        }

        public static IReadOnlyList<long> ValidUsersForIsBrandSafety(CampaignManagementRequest request)
        {
            return GetValue<IReadOnlyList<long>>("ValidUsersForIsBrandSafety", request.OverrideConfigValuesFromTest, EmptyLongList);
        }

        public static int TextAssetCacheTimoutinMin => DynamicConfig.GetValue("TextAssetCacheTimoutinMin", 60);

        public static bool FeedItemBlobPurgeWorkerThreadEnabled => DynamicConfig.GetValue("FeedItemBlobPurgeWorkerThreadEnabled", false);

        public static bool SqlToAzureFeedMigrationWorkerThreadEnabled => DynamicConfig.GetValue("SqlToAzureFeedMigrationWorkerThreadEnabled", false);

        public static int SqlToAzureFeedMigrationWorkerThreadSleepTime => DynamicConfig.GetValue("SqlToAzureFeedMigrationWorkerThreadSleepTime", 300);

        public static bool SkipFeedItemAttributeValueDeserialization => DynamicConfig.GetValue("SkipFeedItemAttributeValueDeserialization", false);

        public static bool FeedItemBlobCompactJobWorkerThreadEnabled => DynamicConfig.GetValue("FeedItemBlobCompactJobWorkerThreadEnabled", false);

        public static int FeedItemBlobCompactJobProcessTimeoutInMins => DynamicConfig.GetValue("FeedItemBlobCompactJobProcessTimeoutInMins", 30);

        public static int FeedMigrationProcessTimeoutInMins => DynamicConfig.GetValue("FeedMigrationProcessTimeoutInMins", 60);

        public static int GetMaxFeedItemsPerBlobForFeedType(FeedType feedType)
        {
            var configName = $"MaxFeedItemsPerBlob_{feedType}";
            var value = DynamicConfig.GetValue(configName, -1);
            return value == -1 ? MaxFeedItemsPerBlob : value;
        }

        public static IReadOnlyDictionary<string, IEnumerable<string>> EntityColumnsPerReportType => ParseStringToListMap("EntityColumnsPerReportType");
        //Used to parse a string with such formatting: "SearchQueryPerformanceReport:AdGroupName,AdGroupId,AdId,Keyword,KeywordId;PublisherUsagePerformanceReport:AdGroupName,AdGroupId"
        private static IReadOnlyDictionary<string, IEnumerable<string>> ParseStringToListMap(string dynamicConfigName)
        {
            var result = new Dictionary<string, IEnumerable<string>>(StringComparer.InvariantCultureIgnoreCase);
            var configString = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var entities = configString.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in entities)
            {
                var pairs = entity.Split(':');
                if (pairs.Length != 2)
                {
                    throw new ArgumentException($"Incorrect dynamic config format in {dynamicConfigName}, error section [{string.Join(":", pairs)}].");
                }

                var values = pairs[1].Split(',');

                result[pairs[0]] = values;
            }

            return result;
        }

        private static IReadOnlyDictionary<string, short> ConvertToStringShortMap(string dynamicConfigName)
        {
            var result = new Dictionary<string, short>(StringComparer.InvariantCultureIgnoreCase);
            var configString = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var stringIntPairs = configString.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in stringIntPairs)
            {
                var elements = entity.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException(
                        $"Incorrect dynamic config format in {dynamicConfigName}, error section [{string.Join(":", elements)}]. please follow pattern: string:int;string:int.");
                }

                short numerical;
                if (!short.TryParse(elements[1], out numerical))
                {
                    throw new ArgumentException($"Incorrect dynamic config format in {dynamicConfigName}, error section [{string.Join(":", elements)}]. The second part must be int");
                }

                result[elements[0]] = numerical;
            }

            return result;
        }

        private static IReadOnlyDictionary<string, bool> ConvertToStringBoolMap(string dynamicConfigName)
        {
            var result = new Dictionary<string, bool>(StringComparer.InvariantCultureIgnoreCase);
            var configString = DynamicConfig.GetValue<string>(dynamicConfigName, string.Empty);
            var stringBoolPairs = configString.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var entity in stringBoolPairs)
            {
                var elements = entity.Split(':');
                if (elements.Length != 2)
                {
                    throw new ArgumentException(
                        $"Incorrect dynamic config format in {dynamicConfigName}, error section [{string.Join(":", elements)}]. please follow pattern: string:bool;string:bool.");
                }

                bool boolValue;
                if (!bool.TryParse(elements[1], out boolValue))
                {
                    throw new ArgumentException($"Incorrect dynamic config format in {dynamicConfigName}, error section [{string.Join(":", elements)}]. The second part must be bool");
                }

                result[elements[0]] = boolValue;
            }

            return result;
        }

        public static IReadOnlyDictionary<string, short> ExchangeRateAvailableCurrencyCodeToIdMap => ConvertToStringShortMap("CurrencyWithExchangeRateCodeToIdMap");
		public static int BatchSearchCustomerHierarchySize => DynamicConfig.GetValue("BatchSearchCustomerHierarchySize", 1000);
		public static bool EnableRemarketingGoogleImport => DynamicConfig.GetValue("EnableRemarketingGoogleImport", false);
		public static bool EnableSharedRemarketingAudienceGoogleImport => DynamicConfig.GetValue("EnableSharedRemarketingAudienceGoogleImport", false);
		public static bool AddLogForRuleItemIsEmpty => DynamicConfig.GetValue("AddLogForRuleItemIsEmpty", false);
		public static bool EnableSimilarAudienceGoogleImport => DynamicConfig.GetValue("EnableSimilarAudienceGoogleImport", false);
		public static bool EnableSharedSimilarAudienceGoogleImport => DynamicConfig.GetValue("EnableSharedSimilarAudienceGoogleImport", false);
		public static bool EnableOperateAudiencesAction => DynamicConfig.GetValue("EnableOperateAudiencesAction", false);
		public static bool DisableConversionCountDataFetchForDeliveryStatus => DynamicConfig.GetValue("DisableConversionCountDataFetchForDeliveryStatus", true);
		
		public static string ReportDataCacheEnvironmentPrefix => DynamicConfig.GetValue("ReportDataCacheEnvironmentPrefix", "");
		public static bool EnableOneCentBidForAudienceShopping => DynamicConfig.GetValue("EnableOneCentBidForAudienceShopping", false);

        public static bool EnableNewEOInterfacesForSA => DynamicConfig.GetValue("EnableNewEOInterfacesForSA", false);

        public static bool SyndicationOnlyDeprecationEnabled => DynamicConfig.GetValue("SyndicationOnlyDeprecationEnabled", false);

        public static string DefaultingUpdateBiddingStrategiesAfterSplitOptOutCustomerIds => DynamicConfig.GetValue("DefaultingUpdateBiddingStrategiesAfterSplitOptOutCustomerIds", string.Empty);

        public static string EditorialServiceUrl => DynamicConfig.GetValue("EditorialServiceUrl", string.Empty);

        public static int BulkUploadImagesDeleteBatchLimit
        {
            get
            {
                return DynamicConfig.GetRequiredValue<int>("BulkUploadImagesDeleteBatchLimit");
            }
        }

        public static string AggregationServiceEndpoint =>
            DynamicConfig.GetRequiredValue<string>("AggregationServiceEndpoint");

        public static string AggregationServiceClientId =>
            DynamicConfig.GetRequiredValue<string>("AggregationServiceClientId");

        public static string AggregationServiceClientSecret =>
            DynamicConfig.GetRequiredValue<string>("AggregationServiceClientSecret");

        public static string AggregationServiceAuthority =>
            DynamicConfig.GetRequiredValue<string>("AggregationServiceAuthority");

        public static string AutoImageServiceEndpoint =>
            DynamicConfig.GetRequiredValue<string>("AutoImageServiceEndpoint");

        public static string AdsRankingClientId =>
            DynamicConfig.GetRequiredValue<string>("AdsRankingClientId");

        public static string AdsRankingClientSecret =>
            DynamicConfig.GetRequiredValue<string>("AdsRankingClientSecret");

        public static int AggregationServiceAIGCImageCount =>
            DynamicConfig.GetValue<int>("AggregationServiceAIGCImageCount", 4);

        public static int AggregationServiceAIGCImageResolutionX =>
            DynamicConfig.GetValue<int>("AggregationServiceAIGCImageResolutionX", 1024);

        public static int AggregationServiceAIGCImageResolutionY =>
            DynamicConfig.GetValue<int>("AggregationServiceAIGCImageResolutionY", 1024);

        public static string DoubleVerifyAuthToken =>
            DynamicConfig.GetRequiredValue<string>("DoubleVerifyAuthToken");

        public static bool NRTNewNokaPipeline
        {
            get
            {
                return DynamicConfig.GetValue<bool>("NRTNewNokaPipeline", false);
            }
        }

        public static HashSet<long> SmartShoppingCampaignImportedAsBasicShoppingCampaignIdList
        {
            get
            {
                HashSet<long> CampaignIdList = new HashSet<long>();
                var campaignIdRawList = DynamicConfig.GetValue<string>("SmartShoppingCampaignImportedAsBasicShoppingCampaignIdList", "").Split(',');
                foreach (string campaignIdRaw in campaignIdRawList)
                {
                    if (long.TryParse(campaignIdRaw, out long campaignId))
                    {
                        CampaignIdList.Add(campaignId);
                    }
                }

                return CampaignIdList;
            }
        }

        public static bool EnableDayTimeBidBoostOnly(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableDayTimeBidBoostOnly", request.OverrideConfigValuesFromTest, false);
        }

        public static int PortfolioBidStrategyBulkProcessBatchSize => DynamicConfig.GetRequiredValue<int>("PortfolioBidStrategyBulkProcessBatchSize");
        public static HashSet<long> ProductGroupRemoteFanoutPilotAccountIds => new HashSet<long>(DynamicConfig.GetValue(nameof(ProductGroupRemoteFanoutPilotAccountIds), EmptyLongList));

        public static string FacebookImportApiVersion => DynamicConfig.GetValue<string>("FacebookImportApiVersion", "v15.0");

        public static string FacebookMIApiVersion => DynamicConfig.GetValue<string>("FacebookMIApiVersion", "v15.0");

        public static int FacebookMIMaxBackfillWindowInMonths => DynamicConfig.GetValue<int>("FacebookMIMaxBackfillWindowInMonths", 24);

        public static bool EnableMaxConversionValueForSearchCampaigns => DynamicConfig.GetValue<bool>("EnableMaxConversionValueForSearchCampaigns", false);

        public static bool EnableMaxConversionValueForStandardShoppingCampaigns => DynamicConfig.GetValue<bool>("EnableMaxConversionValueForStandardShoppingCampaigns", false);

        public static bool EnableAccountLabels => DynamicConfig.GetValue<bool>("EnableAccountLabels", false);

        public static bool EnableManagedIdentity => DynamicConfig.GetValue<bool>("EnableManagedIdentity", false);

        public static string AzureResourceCredentialConfigConnectionString => DynamicConfig.GetValue<string>("AzureResourceCredentialConfigConnectionString", "UseLocalAuth=true");

        public static int AccountLabelsDimensionMaxAccountLimit => DynamicConfig.GetValue<int>("AccountLabelsDimensionMaxAccountLimit", 10000);

        public static bool ByPassAutoApplyOptInStatusCheck => DynamicConfig.GetValue<bool>("ByPassAutoApplyOptInStatusCheck", false);

        public static ReadOnlyCollection<long> AppSyncBlockCustomerIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("AppSyncBlockCustomerIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static bool ByPassSTAAdgroupCheck => DynamicConfig.GetValue<bool>("ByPassSTAAdgroupCheck", false);

        public static ReadOnlyCollection<long> ByPassSTAAdgroupCheckAccountIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("ByPassSTAAdgroupCheckAccountIds", new ReadOnlyCollection<long>(new List<long>()));
            }
        }

        public static bool UseAPIV13SchemaForReportCalls => DynamicConfig.GetValue<bool>("UseAPIV13SchemaForReportCalls", false);

        public static bool EnableImageAutoCropping => DynamicConfig.GetValue<bool>("EnableImageAutoCropping", false);

        public static bool EnableAutoFillOnImageBulkCropSettings => DynamicConfig.GetValue<bool>("EnableAutoFillOnImageBulkCropSettings", false);

        public static bool EnableSkipLoadAppExtensionAssocation => DynamicConfig.GetValue<bool>("EnableSkipLoadAppExtensionAssocation", false);

        public static bool EnableFeedColumnAliasing => DynamicConfig.GetValue<bool>("EnableFeedColumnAliasing", false);

        public static bool IsAccountEnabledForFeedUploadPerfOptV2PilotPercent(long accountId)
        {
            int percentage = DynamicConfig.GetValue("FeedUploadPerfOptV2PilotPercent", 0);
            return FeedUploadPerfOptV2PilotAccountWhitelist.Contains(accountId) || (accountId % 100) < percentage;
        }

        public static bool SupportGridDataDateRangePreset => DynamicConfig.GetValue("SupportGridDataDateRangePreset", false);

        public static bool LoadAdAndKeywordEditorialRejectionDataForFiltering => DynamicConfig.GetValue<bool>("LoadAdAndKeywordEditorialRejectionDataForFiltering", false);


        public static bool EnableHouseholdIncomeTargeting => DynamicConfig.GetValue<bool>("EnableHouseholdIncomeTargeting", false);

        public static int PercentAccountsForAssetBasedPageFeedImport => DynamicConfig.GetValue("PercentAccountsForAssetBasedPageFeedImport", 0);
        public static HashSet<long> AssetBasedPageFeedImportCustomerIdWhiteList => new HashSet<long>(DynamicConfig.GetValue("AssetBasedPageFeedImportCustomerIdWhiteList", EmptyLongList));
        public static HashSet<long> AssetBasedPageFeedImportCustomerIdBlackList => new HashSet<long>(DynamicConfig.GetValue("AssetBasedPageFeedImportCustomerIdBlackList", EmptyLongList));
        public static HashSet<long> AccountIdBlockListForPMaxAssetAutomationSettingImport => new HashSet<long>(DynamicConfig.GetValue("AccountIdBlockListForPMaxAssetAutomationSettingImport", EmptyLongList));
        public static bool EnablePilotCheckForAdsGlobalizationPhase1 => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase1", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase2 => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase2", false);
        public static bool EnablePilotCheckForAdsGlobalizationTextAdsJapan => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationTextAdsJapan", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase3America => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase3America", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase3APAC => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase3APAC", false);

        public static bool EnablePilotCheckForAdsGlobalizationPhase5Africa => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase5Africa", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase6MENA => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase6MENA", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhaseSimplifiedChinese => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhaseSimplifiedChinese", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase7 => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase7", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase8 => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase8", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase9 => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase9", false);
        public static bool EnablePilotCheckForAdsGlobalizationPhase9VI => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPhase9VI", false);

        public static bool EnablePilotCheckForAdsGlobalizationPAKorea => DynamicConfig.GetValue("EnablePilotCheckForAdsGlobalizationPAKorea", false);
        
        public static bool EnablePilotCheckForCNMarketExpansion => DynamicConfig.GetValue("EnablePilotCheckForCNMarketExpansion", false);

        public static bool EnableEditorialGlobalizationCalculations => DynamicConfig.GetValue("EnableEditorialGlobalizationCalculations", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase3America => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase3America", false);

        public static bool EnableExpandedAssetEditorial => DynamicConfig.GetValue<bool>("EnableExpandedAssetEditorial", false);
        public static bool EnableAssetBasedEditorialForAIM => DynamicConfig.GetValue("EnableAssetBasedEditorialForAIM", false);

        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase3APAC => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase3APAC", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase2Japanese => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase2Japanese", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase5Africa => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase5Africa", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase6Mena => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase6Mena", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase7 => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase7", false);
        public static bool EditorialOfflineSyncGAAdsGlobalizationPhase8 => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPhase8", false);

        public static bool EditorialOfflineSyncGAAdsGlobalizationPAKorea => DynamicConfig.GetValue("EditorialOfflineSyncGAAdsGlobalizationPAKorea", false);
        public static bool EditorialOfflineSyncGACNMarketExpansion => DynamicConfig.GetValue("EditorialOfflineSyncGACNMarketExpansion", false);
        public static bool EnableAdExtensionInlineEditorialForCNMarketExpansion => DynamicConfig.GetValue("EnableAdExtensionInlineEditorialForCNMarketExpansion", false);
        public static bool UseNewFormatForIFFunction => DynamicConfig.GetValue("UseNewFormatForIFFunction", false);

        public static bool DisableNewCurrencyLoadAdsGlobalization => DynamicConfig.GetValue("DisableNewCurrencyLoadAdsGlobalization", true);
        public static bool EnablePublisherCountiresForAdsGBL => DynamicConfig.GetValue("EnablePublisherCountiresForAdsGBL", false);

        public static int EnableNewCountriesForCallAndLocationAdExtensions => DynamicConfig.GetValue("EnableNewCountriesForCallAndLocationAdExtensions", 0);

        public static bool EnableNewCallTrackingFlow => DynamicConfig.GetValue("EnableNewCallTrackingFlow", true);
        public static int AdExtensionBatchLimit => DynamicConfig.GetValue("AdExtensionBatchLimit", 1000);

        public static string PAOfflineKafkaBootstrapServers => DynamicConfig.GetValue("PAOfflineKafkaBootstrapServers", String.Empty);
        public static string PAOfflineKafkaSaslUsername => DynamicConfig.GetValue("PAOfflineKafkaSaslUsername", String.Empty);
        public static string PAOfflineKafkaSaslPassword => DynamicConfig.GetValue("PAOfflineKafkaSaslPassword", String.Empty);
        public static string PAOfflineKafkaTopic => DynamicConfig.GetValue("PAOfflineKafkaTopic", String.Empty);
        public static int PAOfflineKafkaRetryCount => DynamicConfig.GetValue("PAOfflineKafkaRetryCount", 5);
        public static int PAOfflineKafkaRetryDelay => DynamicConfig.GetValue("PAOfflineKafkaRetryDelay", 200);

        public static bool ImportTaskRecommendation => DynamicConfig.GetValue("ImportTaskRecommendation", false);
        public static bool IsSmartImportAutoFrequencyScheduleUpdateProducerEnabled => DynamicConfig.GetValue("IsSmartImportAutoFrequencyScheduleUpdateProducerEnabled", false);

        public static int ImportAutoFrequencyMaxIntervalInDays => DynamicConfig.GetValue("ImportAutoFrequencyMaxIntervalInDays", 7);

        public static int DectectChangeForAutoFrequencyRecommendationInDays => DynamicConfig.GetValue("DectectChangeForAutoFrequencyRecommendationInDays", 3);
        public static bool IsScheduleImportEnableAutoFrequencyRecommendation => DynamicConfig.GetValue("IsScheduleImportEnableAutoFrequencyRecommendation", false);
        public static bool EnableAggregatorServiceAdInsightRecommendation => DynamicConfig.GetValue("EnableAggregatorServiceAdInsightRecommendation", false);

        public static int UseCCMTSmallTokenToCallAdInsightODataThreshold => DynamicConfig.GetValue("UseCCMTSmallTokenToCallAdInsightODataThreshold", 5000);

        public static string AdInsightODataEndpointUrl => DynamicConfig.GetValue("AdInsightODataEndpointUrl", string.Empty);
        public static string PerformanceInsightsODataEndpointUrl => DynamicConfig.GetValue("PerformanceInsightsODataEndpointUrl", string.Empty);

        public static int KeywordBidPrecision => DynamicConfig.GetValue("KeywordBidPrecision", 3);
        public static bool LegacyRedirectEnabled => DynamicConfig.GetValue("LegacyRedirectEnabled", true);

        public static bool FetchUetTagsWithAudienceCount => DynamicConfig.GetValue("FetchUetTagsWithAudienceCount", true);

        public static bool ShouldAssociateToSingleInactiveTag => DynamicConfig.GetValue("ShouldAssociateToSingleInactiveTag", false);

        public static bool UseActiveTagWithAudience => DynamicConfig.GetValue("UseActiveTagWithAudience", false);

        public static bool AudienceAssociationBidRecommendation => DynamicConfig.GetValue("AudienceAssociationBidRecommendation", false);

        public static bool EnableFacebookImportVideoAds => DynamicConfig.GetValue("EnableFacebookImportVideoAds", false);

        public static IReadOnlyList<long> FacebookImportVideoAdsEnabledAccountIdList => DynamicConfig.GetValue("FacebookImportVideoAdsEnabledAccountIdList", EmptyLongList);
        public static bool EnableAudienceVideoAds => DynamicConfig.GetValue("EnableAudienceVideoAds", false);
        public static bool EnableHTML5Ads => DynamicConfig.GetValue("EnableHTML5Ads", false);

        public static bool EnableAudienceUnverifiedVideoAds => DynamicConfig.GetValue("EnableAudienceUnverifiedVideoAds", false);

        public static bool EnableCPVPricingModel => DynamicConfig.GetValue("EnableCPVPricingModel", false);

        public static bool EnableVideoBids => DynamicConfig.GetValue("EnableVideoBids", false);

        public static bool EnableVideoCache => DynamicConfig.GetValue("EnableVideoCache", false);

        public static bool EnableCpvBidInImport => DynamicConfig.GetValue("EnableCpvBidInImport", false);

        public static int PercentAccountEnabledForCPMInheritFromParentOverride => DynamicConfig.GetValue("PercentAccountEnabledForCPMInheritFromParentOverride", 0);

        public static bool UseReplaceCommaInQuote2 => DynamicConfig.GetValue("UseReplaceCommaInQuote2", false);

        public static int MultiAccountOverviewMaxAccounts => DynamicConfig.GetValue("MultiAccountOverviewMaxAccounts", 10000);

        public static bool SimulateSvcRedirectProblem => DynamicConfig.GetValue<bool>("SimulateSvcRedirectProblem", false);

        public static int DisclaimersBatchSize => DynamicConfig.GetValue<int>("DisclaimersBatchSize", 100);

        public static bool EnableLabelMccCache => DynamicConfig.GetValue("EnableLabelMccCache", true);

        public static bool IsExcludeAgeTargetEnabledForOrderUnderSearchCampaign => DynamicConfig.GetValue("IsExcludeAgeTargetEnabledForOrderUnderSearchCampaign", false);

        public static bool EnableAutoBiddingLimitedStatusForCampaigns => DynamicConfig.GetValue("EnableAutoBiddingLimitedStatusForCampaigns", false);
        public static bool UseNewRulesToAnnounceDealTargetingCapability => DynamicConfig.GetValue("UseNewRulesToAnnounceDealTargetingCapability", false);

        public static bool EnableAutoBiddingLimitedStatusForPortfolios => DynamicConfig.GetValue("EnableAutoBiddingLimitedStatusForPortfolios", false);

        public static SecureString ClientCenterUserName => DynamicConfigValues.GetValue("PPSClientCenterUserName", null, string.Empty).ToSecureString();

        public static SecureString ClientCenterPassword => DynamicConfigValues.GetValue("PPSClientCenterPassword", null, string.Empty).ToSecureString();

        public static bool EnableBSCImportMappingImprovement(CampaignManagementRequest request)
        {
            return GetValue<bool>("EnableBSCImportMappingImprovement", request.OverrideConfigValuesFromTest, false);
        }

        public static int CampaignIdBatchSizeForAudienceAssociationCheck => DynamicConfig.GetValue<int>("CampaignIdBatchSizeForAudienceAssociationCheck", 1000);

        public static bool IsUpdateCampaignStoreIdTaskEnabled => DynamicConfig.GetValue("IsUpdateCampaignStoreIdTaskEnabled", false);

        public static bool IsEnabledForVerifiedImpressionTrackingAtCampaignLevel => DynamicConfig.GetValue<bool>("IsEnabledForVerifiedImpressionTrackingAtCampaignLevel", false);

        public static bool EnableCampaignThirdPartyMeasurement => DynamicConfig.GetValue<bool>("EnableCampaignThirdPartyMeasurement", false);

        public static bool EnableESCBulkUpload => DynamicConfig.GetValue<bool>("EnableESCBulkUpload", false);

        public static bool EnableCreateActiveESCCampaign => DynamicConfig.GetValue<bool>("EnableCreateActiveESCCampaign", false);

        public static bool MultiAccountOverviewPreLoadUsePageTrackingId => DynamicConfig.GetValue("MultiAccountOverviewPreLoadUsePageTrackingId", true);

        public static int MmcOfferParallelSubShardingEntityCountThreshold => DynamicConfig.GetValue("MmcOfferParallelSubShardingEntityCountThreshold", 62500);

        public static int MmcOfferParallelSubShardingMaxIncreaseCount => DynamicConfig.GetValue("MmcOfferParallelSubShardingMaxIncreaseCount", 1);

        public static int HotelParallelSubShardingEntityCountThreshold => DynamicConfig.GetValue("HotelParallelSubShardingEntityCountThreshold", 62500);

        public static int HotelParallelSubShardingMaxIncreaseCount => DynamicConfig.GetValue("HotelParallelSubShardingMaxIncreaseCount", 1);

        public static bool FailBulkEditsReferencingAveragePosition => DynamicConfig.GetValue("FailBulkEditsReferencingAveragePosition", false);

        public static bool ShouldReturnZeroAveragePosition => DynamicConfig.GetValue("ShouldReturnZeroAveragePosition", false);

        public static HashSet<long> TextAssetInMultiMediaAdsAccounts => new HashSet<long>(DynamicConfig.GetValue("TextAssetInMultiMediaAdsAccounts", EmptyLongList));

        public static string McaProvisioningServiceEndpoint => DynamicConfig.GetRequiredValue<string>("McaProvisioningServiceEndpoint");

        public static string McaProvisioningSecret => DynamicConfig.GetRequiredValue<string>("McaProvisioningSecret");

        public static bool UseFeedAddV41 => DynamicConfig.GetValue("UseFeedAddV41", false);

        public static int CcmtTokenExpireTimeInMinutes => DynamicConfig.GetValue("CcmtTokenExpireTimeInMinutes", 0);

        public static string PrimaryAuthenticatedAccountUsernameKey => DynamicConfig.GetValue("PrimaryAuthenticatedAccountUsernameKey", String.Empty);

        public static string PrimaryAuthenticatedAccountPasswordKey => DynamicConfig.GetValue("PrimaryAuthenticatedAccountPasswordKey", String.Empty);

        public static string SecondaryAuthenticatedAccountUsernameKey => DynamicConfig.GetValue("SecondaryAuthenticatedAccountUsernameKey", String.Empty);

        public static string SecondaryAuthenticatedAccountPasswordKey => DynamicConfig.GetValue("SecondaryAuthenticatedAccountPasswordKey", String.Empty);

        public static bool FlipAuthenticatedAccount => DynamicConfig.GetValue("FlipAuthenticatedAccount", false);

        public static bool AllowTotalMemoryLog => DynamicConfig.GetValue("AllowTotalMemoryLog", false);

        public static bool AllowGen2FullCollectionBetweenRequests => DynamicConfig.GetValue("AllowGen2FullCollectionBetweenRequests", false);

        public static bool AllowGen2FullCollectionAlways => DynamicConfig.GetValue("AllowGen2FullCollectionAlways", false);

        public static bool LogGCMemoryInfo => DynamicConfig.GetValue("LogGCMemoryInfo", false);

        public static bool FixSortingPerfAndMemory => DynamicConfig.GetValue("FixSortingPerfAndMemory", false);

        public static bool EnableStringPredicateOptimization => DynamicConfig.GetValue("EnableStringPredicateOptimization", false);

        public static bool EnableConnPoolLogging => DynamicConfig.GetValue("EnableConnPoolLogging", false);

        public static bool ShouldIgnoreFinalUrlSuffixForImportedBrandAwarenessVideoAds => DynamicConfig.GetValue("ShouldIgnoreFinalUrlSuffixForImportedBrandAwarenessVideoAds", false);

        public static bool FixResponsiveAdAssetRowReaderDuplicateAssets => DynamicConfig.GetValue("FixResponsiveAdAssetRowReaderDuplicateAssets", false);

        public static bool FixAudienceAssociationParallelFilter => DynamicConfig.GetValue("FixAudienceAssociationParallelFilter", false);

        public static bool ParallelizeAudienceSegmentation => DynamicConfig.GetValue("ParallelizeAudienceSegmentation", false);

        public static bool UseTemporaryBufferForAudienceTotals => DynamicConfig.GetValue("UseTemporaryBufferForAudienceTotals", false);

        public static string[] RestApiDisabledMethods => DynamicConfig.GetRawValue("RestApiDisabledMethods")?.Split(',') ?? Array.Empty<string>();

        public static bool SkipAdInsightCallIfIsBidLandscapeAvailableNotRequested => DynamicConfig.GetValue("SkipAdInsightCallIfIsBidLandscapeAvailableNotRequested", false);

        public static bool SkipCampaignSettingsCallForAdGroupIfNotRequested => DynamicConfig.GetValue("SkipCampaignSettingsCallForAdGroupIfNotRequested", false);

        public static bool NewGSyncFileFlagOverride(CampaignManagementRequest request)
        {
            return GetValue<bool>("NewGSyncFileFlagOverride", request.OverrideConfigValuesFromTest, false);
        }

        public static int MaxProductOfferRowCountForUsingFileCache => DynamicConfig.GetValue("MaxProductOfferRowCountForUsingFileCache", 250000);

        public static int CoolDownPeriodAfterFirstDismissalInMins => DynamicConfig.GetValue("CoolDownPeriodAfterFirstDismissalInMins", 0);
        public static int CoolDownPeriodAfterSecondDismissalInMins => DynamicConfig.GetValue("CoolDownPeriodAfterSecondDismissalInMins", 0);
        public static int CoolDownPeriodAfterThirdDismissalInMins => DynamicConfig.GetValue("CoolDownPeriodAfterThirdDismissalInMins", 0);
        public static int CoolDownPeriodAfterAdoptionInMins => DynamicConfig.GetValue("CoolDownPeriodAfterAdoptionInMins", 0);
        public static int ImportRecoTableClientExpireTimeInMinutes => DynamicConfig.GetValue("ImportRecoTableClientExpireTimeInMinutes", 60);

        public static bool BingPlacesRetry => DynamicConfig.GetValue("BingPlacesRetry", false);

        public static int FetchCampaignsByAccountIdBatchSize => DynamicConfig.GetValue("FetchCampaignsByAccountIdBatchSize", 500);

        public static int PinterestAPIDefaultPageSize => DynamicConfig.GetValue("PinterestAPIDefaultPageSize", 25);

        public static IReadOnlyDictionary<long, int> GetPinsPageSizeOverrideDictionary => GetIntValueByLongKey("GetPinsPageSizeOverrideDictionary");

        public static bool EnablePinterestAPIResponseCaching => DynamicConfig.GetValue("EnablePinterestAPIResponseCaching", false);

        public static int PinterestSyncCacheExpirationTime => DynamicConfig.GetValue("PinterestSyncCacheExpirationTime", 10);

        public static int FacebookSyncAPITimeoutInSeconds => DynamicConfig.GetValue("FacebookSyncAPITimeoutInSeconds", 60);
        public static int PinterestSyncAPITimeoutInSeconds => DynamicConfig.GetValue("PinterestSyncAPITimeoutInSeconds", 60);
        public static int FacebookAPIDefaultPageSize => DynamicConfig.GetValue("FacebookAPIDefaultPageSize", 25);
        public static int FacebookAPIPageSizeForAdInsights => DynamicConfig.GetValue("FacebookAPIPageSizeForAdInsights", 25);
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForAdsetByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForAdsetByFbAdAccountId");
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForAdCreativeByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForAdCreativeByFbAdAccountId");
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForCustomAudienceByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForCustomAudienceByFbAdAccountId");
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForAdByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForAdByFbAdAccountId");
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForVideoByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForVideoByFbAdAccountId");
        public static IReadOnlyDictionary<long, int> FacebookAPIPageSizeForImagesByFbAdAccountId => GetIntValueByLongKey("FacebookAPIPageSizeForImagesByFbAdAccountId");

        public static IReadOnlyDictionary<string, short> FacebookAPIPageSizeForGetIdsByFbAdAccountId => ConvertToStringShortMap("FacebookAPIPageSizeForGetIdsByFbAdAccountId");
        public static bool EnableFacebookAPIResponseCaching => DynamicConfig.GetValue("EnableFacebookAPIResponseCaching", false);
        public static int FacebookSyncCacheExpirationTime => DynamicConfig.GetValue("FacebookSyncCacheExpirationTime", 10);

        public static bool GetAbsoluteUrlInFbImport => DynamicConfig.GetValue("GetAbsoluteUrlInFbImport", false);
        public static IReadOnlyList<long> FbAccountIdsForGetAdIdByCampaignIdInFbImport => DynamicConfig.GetValue("FbAccountIdsForGetAdIdByCampaignIdInFbImport", EmptyLongList);

        public static bool UseMediumSubtotalsForAdExtensionGrid => DynamicConfig.GetValue<bool>("UseMediumSubtotalsForAdExtensionGrid", false);

        public static bool GenerateImageIdBasedOnHashInPiImport => DynamicConfig.GetValue("GenerateImageIdBasedOnHashInPiImport", false);

        public static bool PauseAIMAdGroupIfAllAudienceCriterionNotImported => DynamicConfig.GetValue("PauseAIMAdGroupIfAllAudienceCriterionNotImported", false);

        public static bool DownloadFbInterestsInFbImport => DynamicConfig.GetValue("DownloadFbInterestsInFbImport", false);

        public static int PercentAccountsForPerformanceDataRetrieveInFbImport => DynamicConfig.GetValue("PercentAccountsForPerformanceDataRetrieveInFbImport", 0);

        public static int PercentAccountsForFacebookDeltaImport => DynamicConfig.GetValue("PercentAccountsForFacebookDeltaImport", 0);

        public static IReadOnlyList<long> FacebookDeltaImportAccountIdWhiteList => DynamicConfig.GetValue("FacebookDeltaImportAccountIdWhiteList", EmptyLongList);

        public static IReadOnlyList<long> FacebookDeltaImportCustomerIdBlackList => DynamicConfig.GetValue("FacebookDeltaImportCustomerIdBlackList", EmptyLongList);

        public static int PercentAccountsForAllowImportCampaignRenameBack => DynamicConfig.GetValue("PercentAccountsForAllowImportCampaignRenameBack", 0);
        public static int PercentCustomersForImportCampaignStatusNotification => DynamicConfig.GetValue("PercentCustomersForImportCampaignStatusNotification", 0);
        public static HashSet<long> ImportCampaignStatusNotificationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("ImportCampaignStatusNotificationCustomerWhitelist", EmptyLongList));

        public static bool EnableImportCampaignStatusNotification(long customerId)
        {
            return ImportCampaignStatusNotificationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < PercentCustomersForImportCampaignStatusNotification;
        }

        public static int PercentCustomersForImportCampaignBudgetNotification => DynamicConfig.GetValue("PercentCustomersForImportCampaignBudgetNotification", 0);
        public static HashSet<long> ImportCampaignBudgetNotificationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("ImportCampaignBudgetNotificationCustomerWhitelist", EmptyLongList));

        public static bool EnableImportCampaignBudgetNotification(long customerId)
        {
            return ImportCampaignBudgetNotificationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < PercentCustomersForImportCampaignBudgetNotification;
        }

        public static int PercentCustomersForImportBiddingStrategiesNotification => DynamicConfig.GetValue("PercentCustomersForImportBiddingStrategiesNotification", 0);
        public static HashSet<long> ImportBiddingStrategiesNotificationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("ImportBiddingStrategiesNotificationCustomerWhitelist", EmptyLongList));

        public static bool EnableImportBiddingStrategiesNotification(long customerId)
        {
            return ImportBiddingStrategiesNotificationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < PercentCustomersForImportBiddingStrategiesNotification;
        }

        public static bool EnableImportCampaignPropertyChangeNotification(long customerId)
        {
            return EnableImportCampaignStatusNotification(customerId) ||
                   EnableImportCampaignBudgetNotification(customerId) ||
                   EnableImportBiddingStrategiesNotification(customerId);
        }

        public static int PercentCustomersForReturnTaskListForCampaignStatusNotification => DynamicConfig.GetValue("PercentCustomersForReturnTaskListForCampaignStatusNotification", 0);
        public static HashSet<long> ReturnTaskListForCampaignStatusNotificationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("ReturnTaskListForCampaignStatusNotificationCustomerWhitelist", EmptyLongList));

        public static bool EnableReturnTaskListForCampaignStatusNotification(long customerId)
        {
            return ReturnTaskListForCampaignStatusNotificationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < PercentCustomersForReturnTaskListForCampaignStatusNotification;
        }

        public static int EnableMetaUpsellInSuccessfulImportNotificationPercentage => DynamicConfig.GetValue("EnableMetaUpsellInSuccessfulImportNotificationPercentage", 0);
        public static HashSet<long> EnableMetaUpsellInSuccessfulImportNotificationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("EnableMetaUpsellInSuccessfulImportNotificationCustomerWhitelist", EmptyLongList));

        public static bool EnableMetaUpsellInSuccessfulImportNotification(long customerId)
        {
            return EnableMetaUpsellInSuccessfulImportNotificationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < EnableMetaUpsellInSuccessfulImportNotificationPercentage;
        }

        public static HashSet<long> AllowImportCampaignRenameBackCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("AllowImportCampaignRenameBackCustomerWhitelist", EmptyLongList));

        public static HashSet<long> BMMToBMOptinCIDs => new HashSet<long>(DynamicConfig.GetValue("BMMToBMOptinCIDs", EmptyLongList));

		public static HashSet<long> BMMToBMOptoutCIDs => new HashSet<long>(DynamicConfig.GetValue("BMMToBMOptoutCIDs", EmptyLongList));

		public static HashSet<long> GoogleImportCampaignRenameCustomerBlackList => new HashSet<long>(DynamicConfig.GetValue("GoogleImportCampaignRenameCustomerBlackList", EmptyLongList));

		public static HashSet<long> AllowImportRSADuplicateCheckSkip => new HashSet<long>(DynamicConfig.GetValue("AllowImportRSADuplicateCheckSkip", EmptyLongList));

		public static HashSet<long> FlushCampaignAdGroupAfterAdLinesCids => new HashSet<long>(DynamicConfig.GetValue("FlushCampaignAdGroupAfterAdLinesCids", EmptyLongList));

		public static HashSet<long> EnableOptimizedAssetBasedAdExtensionMergingAccountIdWhiteList => new HashSet<long>(DynamicConfig.GetValue("EnableOptimizedAssetBasedAdExtensionMergingAccountIdWhiteList", EmptyLongList));

		public static bool SkipVideoViewsObjectiveCampaign => DynamicConfig.GetValue("SkipVideoViewsObjectiveCampaign", false);

		public static bool EnableVideoAdsImport => DynamicConfig.GetValue("EnableVideoAdsImport", true);

		public static bool IsAccountEnabledForVideoAdsEditorCustomThumbnail => DynamicConfig.GetValue("ThumbnailEnabled", false);

		public static bool IsGetGoogleAdsPermissionRecommendationEnabled => DynamicConfig.GetValue("IsGetGoogleAdsPermissionRecommendationEnabled", false);

		public static bool IsAccessTokenExpiringPermissionRecommendationEnabled => DynamicConfig.GetValue("IsAccessTokenExpiringPermissionRecommendationEnabled", false);

		public static bool IsAdGroupBidValueAdjustRecommendationEnabled => DynamicConfig.GetValue("IsAdGroupBidValueAdjustRecommendationEnabled", false);

		public static string ImportPredictionServiceUrl => DynamicConfig.GetValue("ImportPredictionServiceUrl", string.Empty);

		public static bool IsBingAllowedForSiteExclusion => DynamicConfig.GetValue("IsBingAllowedForSiteExclusion", false);

		public static bool EnablePhoneNumberCountryCode => DynamicConfig.GetValue("EnablePhoneNumberCountryCode", false);

		public static bool EnableCampaignLanguage => DynamicConfig.GetValue("EnableCampaignLanguage", false);

		public static bool EnableGoogleCampaignLanguage => DynamicConfig.GetValue("EnableGoogleCampaignLanguage", false);

		public static bool RelaxKeywordRegexValidationForGlobalization => DynamicConfig.GetValue("RelaxKeywordRegexValidationForGlobalization", false);

		public static int SynccentricAmazonLookupTaskRetryTimes => DynamicConfig.GetValue("SynccentricAmazonLookupTaskRetryTimes", 5);
		public static bool EnableAmazonAPIResponseCaching => DynamicConfig.GetValue("EnableAmazonAPIResponseCaching", false);
		public static int AmazonSyncCacheExpirationTime => DynamicConfig.GetValue("AmazonSyncCacheExpirationTime", 10);

		public static int SynccentricSearchTaskRetryIntervalSeconds => DynamicConfig.GetValue("SynccentricSearchTaskRetryIntervalSeconds", 100);

		public static bool AZSyncCentricAmazonQueueSearchEnabled => DynamicConfig.GetValue("AZSyncCentricAmazonQueueSearchEnabled", false);

		public static string AdGroupNameWithAudienceCriterionFailed => DynamicConfig.GetValue("AdGroupNameWithAudienceCriterionFailed", "");

		public static string AzureTableConfigValuesConnectionString => DynamicConfig.GetRequiredValue<string>("AzureTableConfigValuesConnectionString");

		public static string AzureTableConfigValuesPartitionKey => DynamicConfig.GetRequiredValue<string>("AzureTableConfigValuesPartitionKey");

		public static int AzureTableConfigValuesCacheSyncPeriodSeconds => DynamicConfig.GetValue("AzureTableConfigValuesCacheSyncPeriodSeconds", 30);

		public static bool CpmCpvBidLandscapeEnabled => DynamicConfig.GetValue("CpmCpvBidLandscapeEnabled", false);

		public static bool BulkEditAssociationsLazyEval => DynamicConfig.GetValue("BulkEditAssociationsLazyEval", false);

		public static bool IsEnabledAudienceCampaignMMA => DynamicConfig.GetValue("IsEnabledAudienceCampaignMMA", false);

		public static bool LoadHotSpotDataInAggregatorSerice => DynamicConfig.GetValue("LoadHotSpotDataInAggregatorSerice", false);

		public static bool IsEnabledAudienceCampaignAutoBidding => DynamicConfig.GetValue("IsEnabledAudienceCampaignAutoBidding", false);

		public static bool IsEnabledAudienceCampaignAutoBiddingV2 => DynamicConfig.GetValue("IsEnabledAudienceCampaignAutoBiddingV2", false);

		public static bool IsEnabledAudienceCampaigneMaxConversionsForGoogleImport => DynamicConfig.GetValue("IsEnabledAudienceCampaigneMaxConversionsForGoogleImport", false);

		public static bool IsEnabledDiscoveryDemandAnalysis => DynamicConfig.GetValue("IsEnabledDiscoveryDemandAnalysis", false);

        public static HashSet<long> EnableDemandAnalysisForHTML5AdByBingAccountId => new HashSet<long>(DynamicConfig.GetValue("EnableDemandAnalysisForHTML5AdByBingAccountId", EmptyLongList));

		public static bool IsEnabledIASBulkEditForMSANMMA => DynamicConfig.GetValue("IsEnabledIASBulkEditForMSANMMA", false);

		public static bool IsEnabledConvertingUnsupportedBiddingStrategyToEcpcForDiscoveryCampaign => DynamicConfig.GetValue("IsEnabledConvertingUnsupportedBiddingStrategyToEcpcForDiscoveryCampaign", false);

		public static bool IsEnabledAudienceCampaignMMAForGoogleImport => DynamicConfig.GetValue("IsEnabledAudienceCampaignMMAForGoogleImport", false);

		public static bool ImportOptionForBudgetAndBidStrategyNameUpdate => DynamicConfig.GetValue("ImportOptionForBudgetAndBidStrategyNameUpdate", true);

        public static bool IsEnabledCheckImageQuotaForBulkUpload => DynamicConfig.GetValue("IsEnabledCheckImageQuotaForBulkUpload", false);

		public static bool IsEnabledFileBasedImportForMMA => DynamicConfig.GetValue("IsEnabledFileBasedImportForMMA", false);

		public static bool EnableGenreTargeting => DynamicConfig.GetValue("EnableGenreTargeting", false);

		public static bool EnableDeviceTargetingForOLV => DynamicConfig.GetValue("EnableDeviceTargetingForOLV", false);

        public static bool EnableDeviceTargetingForPremiumStreamingOTT => DynamicConfig.GetValue("EnableDeviceTargetingForPremiumStreamingOTT", false);

		public static bool EnableNegativeAudienceLocationForVideo => DynamicConfig.GetValue("EnableNegativeAudienceLocationForVideo", false);

        public static bool EnableAdsStudioFeatures => DynamicConfig.GetValue("EnableAdsStudioFeatures", false);

        public static bool EnableAdsStudioRecentSorting => DynamicConfig.GetValue("EnableAdsStudioRecentSorting", false);

        public static int EnableAdsStudioFeaturesPilotPercentage => DynamicConfig.GetValue<int>("EnableAdsStudioFeaturesPilotPercentage", 0);

        public static int EnableAdStudioIndexingBackfillPilotPercentage => DynamicConfig.GetValue<int>("EnableAdStudioIndexingBackfillPilotPercentage", 0);

        public static bool EnableBrandKit => DynamicConfig.GetValue("EnableBrandKit", false);

        public static int EnableBrandKitPilotPercentage => DynamicConfig.GetValue<int>("EnableBrandKitPilotPercentage", 0);

        public static bool EnableBrandKitForPhase2 => DynamicConfig.GetValue("EnableBrandKitForPhase2", false);

        public static int EnableBrandKitForPhase2PilotPercentage => DynamicConfig.GetValue<int>("EnableBrandKitForPhase2PilotPercentage", 0);

        public static bool EnableBrandKitBulk => DynamicConfig.GetValue("EnableBrandKitBulk", false);

        public static int EnableBrandKitBulkPilotPercentage => DynamicConfig.GetValue<int>("EnableBrandKitBulkPilotPercentage", 0);

        public static bool EnableBrandKitBusinessName => DynamicConfig.GetValue("EnableBrandKitBusinessName", false);

        public static bool EnableBrandKitBrandVoice => DynamicConfig.GetValue("EnableBrandKitBrandVoice", false);

        public static bool EnableRecommendationAPIV2 => DynamicConfig.GetValue("EnableRecommendationAPIV2", false);

        public static int EnableRecommendationAPIV2PilotPercentage => DynamicConfig.GetValue<int>("EnableRecommendationAPIV2PilotPercentage", 0);

        public static bool EnableVideoAdsGeneration => DynamicConfig.GetValue("EnableVideoAdsGeneration", false);

        public static int  EnableVideoAdsGenerationPilotPercentage => DynamicConfig.GetValue<int>("EnableVideoAdsGenerationPilotPercentage", 0);

        public static bool EnableNegativeAudienceLocationForDisplay => DynamicConfig.GetValue("EnableNegativeAudienceLocationForDisplay", false);

		public static bool EnableTextAdAssetAutoGenerationForBrandAwarenessVideoAds => DynamicConfig.GetValue("EnableTextAdAssetAutoGenerationForBrandAwarenessVideoAds", false);

		public static string GoogleTagManagerImportTypeName => DynamicConfig.GetValue("GoogleTagManagerImportTypeName", "GoogleTagManagerImport");

		public static bool UseNewEnvIndentifierInDealAutomatedIngestion => DynamicConfig.GetValue("UseNewEnvIndentifierInDealAutomatedIngestion", false);

        public static string CosmosFileRootPathForImageFittingBackupOriginalImageData => DynamicConfig.GetValue("CosmosFileRootPathForImageFittingBackupOriginalImageData", "");
        
        public static string CampaignSecretsKeyVaultName => DynamicConfig.GetValue("CampaignSecretsKeyVaultName", "CampaignSecretsKVSI");

        public static string CampaignSecretsKeyVaultNameTESTCORP => DynamicConfig.GetValue("CampaignSecretsKeyVaultName-TEST-CORP", "CampaignSecretsKVCI");

		public static string EnvIndentifierInDealAutomatedIngestion => DynamicConfig.GetValue("EnvIndentifierInDealAutomatedIngestion", "Unknown");

		public static bool AddTrackingInfoInMetadataDealAutomatedIngestion => DynamicConfig.GetValue("AddTrackingInfoInMetadataDealAutomatedIngestion", false);

		public static bool IsEnabledDealsBlobCleanUp => DynamicConfig.GetValue("IsEnabledDealsBlobCleanUp", false);

		public static bool IsEnabledForNewDealConstraintMapper => DynamicConfig.GetValue("IsEnabledForNewDealConstraintMapper", false);

		public static bool IsDealCampaignCriterionsDeprecated => DynamicConfig.GetValue("IsDealCampaignCriterionsDeprecated", false);

		public static bool IsEnabledResponsiveAdPatch => DynamicConfig.GetValue<bool>("IsEnabledResponsiveAdPatch", false);

		public static int SeasonalityAdjustmentUpdateBatchSize => DynamicConfig.GetValue(nameof(SeasonalityAdjustmentUpdateBatchSize), 1000);

		public static bool ValidateCPSBidIsNull => DynamicConfig.GetValue<bool>("ValidateCPSBidIsNull", false);

		public static int AssetBasedBatchingPilotPercentage => DynamicConfig.GetValue("AssetBasedBatchingPilotPercentage", 0);
		
		public static int SupportAdgroupCriterionForDiscoveryCampaignGoogleImport => DynamicConfig.GetValue("SupportAdgroupCriterionForDiscoveryCampaignGoogleImport", 0);

		public static bool SupportGoogleVideoCampaignImport => DynamicConfig.GetValue("SupportGoogleVideoCampaignImport", false);

		public static HashSet<long> AssetBasedBatchingAccountWhitelist => new HashSet<long>(DynamicConfig.GetValue("AssetBasedBatchingAccountWhitelist", EmptyLongList));

		public static HashSet<long> FeedUploadPerfOptV2PilotAccountWhitelist => new HashSet<long>(DynamicConfig.GetValue("FeedUploadPerfOptV2PilotAccountWhitelist", EmptyLongList));

		public static bool IsInPilotForAssetBasedBatching(long accountId)
		{
			return AssetBasedBatchingAccountWhitelist.Contains(accountId) || (accountId % 100) < AssetBasedBatchingPilotPercentage;
		}

		public static bool OfflineConversionGserverDelayEnabled => DynamicConfig.GetValue<bool>("OfflineConversionGserverDelayEnabled", false);

		public static int BatchGServerDelayMillisecond => DynamicConfig.GetValue<int>("BatchGServerDelayMillisecond", 0);
		public static int RetryGServerDelayMillisecond => DynamicConfig.GetValue<int>("RetryGServerDelayMillisecond", 0);
		public static int MaxNumThreadToGServer => DynamicConfig.GetValue<int>("MaxNumThreadToGServer", 10);
		public static int RetryToSendToGServerLimit => DynamicConfig.GetValue<int>("RetryToSendToGServerLimit", 3);
		public static int GServerLogCountEachTime => DynamicConfig.GetValue<int>("GServerLogCountEachTime", 48);

		public static string DistributedQueryServiceUrlTemplate => DynamicConfig.GetValue<string>("DistributedQueryServiceUrlTemplate", "https://{0}:5001");

		public static TimeSpan GrpcDeadline => DynamicConfig.GetValue<TimeSpan>("GrpcDeadline", TimeSpan.FromHours(1));

		public static int GrpcStreamingBatchSize => DynamicConfig.GetValue<int>("GrpcStreamingBatchSize", 10000);

		public static int RepartitionBufferBatchSize => DynamicConfig.GetValue<int>("RepartitionBufferBatchSize", 2);
		public static bool DirectBIUseLocalSecondaryForCalls => DynamicConfig.GetValue<bool>("DirectBIUseLocalSecondaryForCalls", false);
		public static bool UseLocalSecondaryForCombinedKeywordInlineChart => DynamicConfig.GetValue<bool>("UseLocalSecondaryForCombinedKeywordInlineChart", false);
		public static bool UseLocalSecondaryForCombinedAdGroupInlineChart => DynamicConfig.GetValue<bool>("UseLocalSecondaryForCombinedAdGroupInlineChart", false);
		public static bool UseLocalSecondaryForCombinedAdInlineChart => DynamicConfig.GetValue<bool>("UseLocalSecondaryForCombinedAdInlineChart", false);
		public static bool UseLocalSecondaryForCombinedInlineChartUnfiltered => DynamicConfig.GetValue<bool>("UseLocalSecondaryForCombinedInlineChartUnfiltered ", false);
        public static bool ForceValidation => DynamicConfig.GetValue<bool>("ForceValidation", false);
        public static bool ForceValidateResponsiveAd => DynamicConfig.GetValue<bool>("ForceValidateResponsiveAd", false);

		public static bool IsUrlTrimmingEnabledForWebsiteExclusionLists => DynamicConfig.GetValue<bool>("IsUrlTrimmingEnabledForWebsiteExclusionLists", false);

		public static int SupportUserAgentForImageDownload => DynamicConfig.GetValue<int>("SupportUserAgentForImageDownload", 0);

		public static int AIMOrderActiveCountLimitCheckV2Percentage => DynamicConfig.GetValue<int>("AIMOrderActiveCountLimitCheckV2Percentage", 0);
		public static int AIMOrderActiveCountLimit => DynamicConfig.GetValue<int>("AIMOrderActiveCountLimit", 10000);
		
		public static bool UseSetHeadersIndexV2 => DynamicConfig.GetValue<bool>("UseSetHeadersIndexV2", false);

		public static int EntityRowCountThresholdForMemoryStream => DynamicConfig.GetValue<int>("EntityRowCountThresholdForMemoryStream", 1000000);

		public static string NewImageLicenseWorkflowAccounts = DynamicConfig.GetValue("NewImageLicenseWorkflowAccounts", "");
		public static string ImgLicBlobStorageConn => DynamicConfig.GetValue<string>("ImgLicBlobStorageConn", "http://localhost:10875/dam.mock/");
		public static string ImgLicEventHubNamespaceConn => DynamicConfig.GetValue<string>("ImgLicEventHubNamespaceConn", "http://localhost:10875/dam.mock/");
        public static string ImgLicEventHubNamespaceHostName => DynamicConfig.GetValue<string>("ImgLicEventHubNamespaceHostName", "");
        public static int ObjectStoreActiveUrlCacheMinutes => DynamicConfig.GetValue<int>("ObjectStoreActiveUrlCacheMinutes", 60);
		public static int ObjectStoreFailoverUrlCacheMinutes => DynamicConfig.GetValue<int>("ObjectStoreFailoverUrlCacheMinutes", 5);
		public static bool UseNewObjectStoreWriter => DynamicConfig.GetValue<bool>("UseNewObjectStoreWriter", true);
		public static bool UseNewObjectStoreEndpoints => DynamicConfig.GetValue("UseNewObjectStoreEndpoints", false);


		public static bool ReportPreviewValidateFilterColumn => DynamicConfig.GetValue<bool>("ReportPreviewValidateFilterColumn", true);

		public static bool FixImportTaskWithEmptyCampaignInEntityBlob => DynamicConfig.GetValue("FixImportTaskWithEmptyCampaignInEntityBlob", false);

		public static bool EnableCollectAppliedRecommendation => DynamicConfig.GetValue<bool>("EnableCollectAppliedRecommendation", false);

		public static bool AlwaysCreateNewCampaignsForSmartCampaigns => DynamicConfig.GetValue<bool>("AlwaysCreateNewCampaignsForSmartCampaigns", false);

		public static int AdcustomizerAttributeLookupKeywordIdBatchSize => DynamicConfig.GetValue<int>("AdcustomizerAttributeLookupKeywordIdBatchSize", 500);

		public static bool EnableCheckDeprecateEXTAPilotFlagInImport => DynamicConfig.GetValue<bool>("EnableCheckDeprecateEXTAPilotFlagInImport", false);

		public static bool EnableUpdateCampaignsAPIBatching => DynamicConfig.GetValue<bool>("EnableUpdateCampaignsAPIBatching", false);

		public static IReadOnlyList<long> DeferFacebookAbsoluteUrlResolvingAccountId => DynamicConfig.GetValue("DeferFacebookAbsoluteUrlResolvingAccountId", EmptyLongList);
		public static IReadOnlyDictionary<long, int> FacebookAbsoluteUrlResolvingMaxErrorForDomain => GetIntValueByLongKey("FacebookAbsoluteUrlResolvingMaxErrorForDomain");
		public static IReadOnlyDictionary<long, int> FacebookAbsoluteUrlResolvingMaxTotalTimeSpendInSecond => GetIntValueByLongKey("FacebookAbsoluteUrlResolvingMaxTotalTimeSpendInSecond");
		public static IReadOnlyDictionary<long, int> FacebookAbsoluteUrlResolvingTimeoutInMs => GetIntValueByLongKey("FacebookAbsoluteUrlResolvingTimeoutInMs");
		public static IReadOnlyDictionary<long, int> FacebookAbsoluteUrlResolvingMaxRetriesPerUrl => GetIntValueByLongKey("FacebookAbsoluteUrlResolvingMaxRetriesPerUrl");
		public static IReadOnlyDictionary<long, int> FacebookImageDownloadLimit => GetIntValueByLongKey("FacebookImageDownloadLimit");

		public static bool EnableCloudV3ErrorFileDownload => DynamicConfig.GetValue<bool>("EnableCloudV3ErrorFileDownload", false);

		public static bool PageFeedAssetResourceServiceUseNewFlow => DynamicConfig.GetValue<bool>("PageFeedAssetResourceServiceUseNewFlow", false);

		public static int MaxLATRecordsForSavedReportsPerKeyInAzureTable => DynamicConfig.GetValue<int>("MaxLATRecordsForSavedReportsPerKeyInAzureTable", 100);

		public static string PharmacyCustomerIdsTableName => DynamicConfig.GetValue<string>("PharmacyCustomerIdsTableName", string.Empty);

		public static string ImportNotificationTableName => DynamicConfig.GetValue("ImportNotificationTableName", string.Empty);

		public static HashSet<long> MCMOptOutEnabledAccounts => new HashSet<long>(DynamicConfig.GetValue("MCMOptOutEnabledAccounts", EmptyLongList));

        public static bool EnableNewCategoryDBFlag => DynamicConfig.GetValue<bool>("EnableNewCategoryDBFlag", false);
        public static bool EnableSiteExclusionUrlComputeHashOnMT => DynamicConfig.GetValue<bool>("EnableSiteExclusionUrlComputeHashOnMT", false);
		
		public static int PercentAccountsForSplitSiteExclusionWorkFlow => DynamicConfig.GetValue("PercentAccountsForSplitSiteExclusionWorkFlow", 0);

        public static int PercentAccountsToThrottleSiteExclusionCalls => DynamicConfig.GetValue("PercentAccountsToThrottleSiteExclusionCalls", 0);

        public static bool EnableMCAMergeScMigrationMetrics => DynamicConfig.GetValue<bool>("EnableMCAMergeScMigrationMetrics", false);
		public static bool EnableESCGlobalization => DynamicConfig.GetValue<bool>("EnableESCGlobalization", false);

		public static int MCAPositiveKeywordLimit => DynamicConfig.GetValue<int>("McaPositiveKeywordLimit", 20);
		public static int MCANegativeKeywordLimit => DynamicConfig.GetValue<int>("McaNegativeKeywordLimit", 500);

		public static int EnableEscUetTagPercentage => DynamicConfig.GetValue<int>("EnableEscUetTagPercentage", 0);

        public static int MaxDelayinMicroSeconds => DynamicConfig.GetValue<int>("MaxDelayinMicroSeconds", 5000);

		public static string EnableEscUetTagWhiteList => DynamicConfig.GetValue<string>("EnableEscUetTagWhiteList", string.Empty);

		public static bool EnableDeleteMcaClarity => DynamicConfig.GetValue<bool>("EnableDeleteMcaClarity", false);

		public static bool EnableAudienceAdsInESC => DynamicConfig.GetValue<bool>("EnableAudienceAdsInESC", false);
		public static bool ByPassESCAudienceAdsPilotCheck => DynamicConfig.GetValue<bool>("ByPassESCAudienceAdsPilotCheck", false);

		public static string PPSBlobStorageConnection => DynamicConfig.GetValue<string>("PPSBlobStorageConnection", string.Empty);
		public static string PPSBlobStorageContainer => DynamicConfig.GetValue<string>("PPSBlobStorageContainer", string.Empty);
		public static string ACSLogsEventHubNamespaceConnection => DynamicConfig.GetValue<string>("ACSLogsEventHubNamespaceConnection", string.Empty);
		public static string ACSLogsEventHubName => DynamicConfig.GetValue<string>("ACSLogsEventHubName", string.Empty);
		public static bool ACSFilterUsageType => DynamicConfig.GetValue<bool>("ACSFilterUsageType", false);
		public static HashSet<string> ACSSuppportedUsageType
		{
			get
			{
				var supportedUsageType = DynamicConfig.GetValue("ACSSuppportedUsageType", new ReadOnlyCollection<string>(new List<string>()));
				var set = new HashSet<string>();
				supportedUsageType.ForEach(s => set.Add(s.Trim().ToLowerInvariant()));
				return set;
			}
		}

		public static string MPSRedirectionServerUrl => DynamicConfig.GetValue("MPSRedirectionServerUrl", string.Empty);
		public static int ACSResourceCount => DynamicConfig.GetValue<int>("ACSResourceCount", 0);
		public static string ACSResourceName => DynamicConfig.GetValue<string>("ACSResourceName", string.Empty);
        public static bool ACSEnableManagedIdentity => DynamicConfig.GetValue<bool>("ACSEnableManagedIdentity", false);

        public static int ACSLogLockTimeOutInMinutes => DynamicConfig.GetValue<int>("ACSLogLockTimeOutInMinutes", 5);
		public static int ACSLogProcessedTimeOutInHours => DynamicConfig.GetValue<int>("ACSLogProcessedTimeOutInHours", 24);

		public static bool IsImportReturnAdPreferenceSprocEnabled => DynamicConfig.GetValue<bool>("IsImportReturnAdPreferenceSprocEnabled", false);

		public static bool SetDefaultCategoryCountryCode => DynamicConfig.GetValue<bool>("SetDefaultCategoryCountryCode", false);
		public static int BatchSizeForPreviouslyImportedCampaignTypeRetrieval => DynamicConfig.GetValue<int>("BatchSizeForPreviouslyImportedCampaignTypeRetrieval", 1000);

		public static bool EnablePPSACSFlow => DynamicConfig.GetValue("EnablePPSACSFlow", false);
		public static bool EnableGetMeteredCallAdExtensionMigrationStatus => DynamicConfig.GetValue("EnableGetMeteredCallAdExtensionMigrationStatus", false);
		public static bool ProvsionUsingACSClient => DynamicConfig.GetValue("ProvsionUsingACSClient", false);
		public static int ReleaseNumberHoldInPoolHours => DynamicConfig.GetValue("ReleaseNumberHoldInPoolHours", 1);
		public static int PauseNumberHoldInPoolDays => DynamicConfig.GetValue("PauseNumberHoldInPoolDays", 1);

		public static int DefaultNativeBidAdjustmentForMCM => DynamicConfig.GetValue<int>("DefaultNativeBidAdjustmentForMCM", 0);

        public static ReadOnlyCollection<long> TextAssetBlobEnabledAccountIds
		{
			get
			{
				return DynamicConfig.GetValue<ReadOnlyCollection<long>>("TextAssetBlobEnabledAccountIds", new ReadOnlyCollection<long>(new List<long>()));
			}
		}

		public static bool IsAccountEnabledForTextAssetBlob(long accountId)
		{
			return IsEnabledForTextAssetBlob || TextAssetBlobEnabledAccountIds.Contains(accountId);
		}

		public static bool TextAssetBlobOperationTypeRunInCI => DynamicConfig.GetValue("TextAssetBlobOperationTypeRunInCI", false);

		public static bool ParallelLoadingTaskExecutor_MultipleSettingsEnabled => DynamicConfig.GetValue("ParallelLoadingTaskExecutor_MultipleSettingsEnabled", false);

		public static int ReadMaxAttemptsForMetaData => DynamicConfig.GetValue("ReadMaxAttemptsForMetaData", 1);

		public static bool IsEnabledForTextAssetBlob => DynamicConfig.GetValue("IsEnabledForTextAssetBlob", false);
		public static bool EnableDailySyncACSToAzureTable => DynamicConfig.GetValue("EnableDailySyncACSToAzureTable", false);
		public static bool OfflineConversionDailyReportTestLocal => DynamicConfig.GetValue<bool>("OfflineConversionDailyReportTestLocal", false);

		public static bool IsEnabledForEditorialCategoryload => DynamicConfig.GetValue("IsEnabledForEditorialCategoryload", false);

		public static bool DisableLanguageUpdateEvent => DynamicConfig.GetValue<bool>("DisableLanguageUpdateEvent", false);

		public static string LocationIdMappingContainerName => DynamicConfig.GetValue<string>("LocationIdMappingContainerName", "do-not-delete-location-mapping-data");

		public static HashSet<long> PPSDeprovsionAccountIdWhiteList => new HashSet<long>(DynamicConfig.GetValue("PPSDeprovsionAccountIdWhiteList", EmptyLongList));

		public static int MaxCombinationsToFetchFromBI => DynamicConfig.GetValue<int>("MaxCombinationsToFetchFromBI", 100);

		public static int AudienceGroupBulkProcessBatchSize => DynamicConfig.GetRequiredValue<int>("AudienceGroupBulkProcessBatchSize");

		public static int AssetGroupBulkProcessBatchSize => DynamicConfig.GetRequiredValue<int>("AssetGroupBulkProcessBatchSize");

		public static bool EnableAutoGeneratedImageAsset = DynamicConfig.GetValue<bool>("EnableAutoGeneratedImageAsset", false);

		public static bool EnableAutoGeneratedAssetGroupAssetsBlockingWithType = DynamicConfig.GetValue<bool>("EnableAutoGeneratedAssetGroupAssetsBlockingWithType", false);

		public static bool ValidateValidStoreForFlyer => DynamicConfig.GetRequiredValue<bool>("ValidateValidStoreForFlyer");

		public static bool IsEnabledForPredictiveTargetingNonPilotScenarioDataFiltration => DynamicConfig.GetValue("IsEnabledForPredictiveTargetingNonPilotScenarioDataFiltration", false);

		public static bool UseDeltaUpdatesForVerticalItemBidMultiplier(long accountId)
		{
			int percentage = DynamicConfig.GetValue("UseDeltaUpdatesForVerticalItemBidMultiplier", 0);
			return (accountId % 100) < percentage;
		}

		public static int ChangeHistoryReportNewWorkflowPilotPercentage => DynamicConfig.GetValue("ChangeHistoryReportNewWorkflowPilotPercentage", 0);

		public static int CampaignPerfLog => DynamicConfig.GetValue("CampaignPerfLog", 0);

		public static bool BloblDebugPerfLog => DynamicConfig.GetValue("BloblDebugPerfLog", false);

		public static int CHUIReadFromAdvBIDBPilotPercent => DynamicConfig.GetValue("CHUIReadFromAdvBIDBPilotPercent", 0);
		public static bool ShouldUpsertUndoChangeToAdvBIAdGroupShards => DynamicConfig.GetValue("ShouldUpsertUndoChangeToAdvBIAdGroupShards", false);

		public static string AppMetadataSyncAzureTableName => DynamicConfig.GetValue<string>("AppMetadataSyncAzureTableName", "");
		public static string AppMetadataSyncSearchAzureTableName => DynamicConfig.GetValue<string>("AppMetadataSyncSearchAzureTableName", "");

		public static bool BulkEditValidateAccountStatus => DynamicConfig.GetValue<bool>("BulkEditValidateAccountStatus", false);

        public static bool ForceFinalVideoCopy => DynamicConfig.GetValue<bool>("ForceFinalVideoCopy", false);

		public static ReadOnlyCollection<long> AllowedProductAdsWindowsStores
		{
			get
			{
				return DynamicConfig.GetValue<ReadOnlyCollection<long>>("AllowedProductAdsWindowsStores", new ReadOnlyCollection<long>(new List<long>()));
			}
		}

		public static HashSet<long> EnableAppCampaignImportCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("EnableAppCampaignImportCustomerWhitelist", EmptyLongList));

		public static HashSet<long> ImportBypassGetAdsByNameResultIfAdWordsAdIdMappingExistsAccountIds => new HashSet<long>(DynamicConfig.GetValue("ImportBypassGetAdsByNameResultIfAdWordsAdIdMappingExistsAccountIds", EmptyLongList));
		public static HashSet<long> ImportBypassGetAdsByNameResultIfAdWordsAdIdMappingExistsCustomerIds => new HashSet<long>(DynamicConfig.GetValue("ImportBypassGetAdsByNameResultIfAdWordsAdIdMappingExistsCustomerIds", EmptyLongList));

		public static bool ExternalExposeVideoBackupFileUrl => DynamicConfig.GetValue<bool>("ExternalExposeVideoBackupFileUrl", false);

		public static bool EnableBaiduFileImport => DynamicConfig.GetValue<bool>("EnableBaiduFileImport", false);

		public static int PercentEnablePageFeedImportUpdateFix => DynamicConfig.GetValue<int>("PercentEnablePageFeedImportUpdateFix", 0);

        public static HashSet<long> AdGroupAdScheduleUseSearcherTimeZoneAlwaysFalseCustomerIdList => new HashSet<long>(DynamicConfig.GetValue("AdGroupAdScheduleUseSearcherTimeZoneAlwaysFalseCustomerIdList", EmptyLongList));

        public static bool MergeHotelDimensionBiDataUseNewFlow => DynamicConfig.GetValue<bool>("MergeHotelDimensionBiDataUseNewFlow", false);
		public static bool DedupDifferentTaiwan => DynamicConfig.GetValue("DedupDifferentTaiwan", false);
		public static ReadOnlyCollection<string> PMaxMMCIntegrationDomainWhitelist => DynamicConfig.GetValue("PMaxMMCIntegrationDomainWhitelist", new ReadOnlyCollection<string>(new List<string>()));
		public static bool IsExceptionProcessedPiloting => DynamicConfig.GetValue<bool>("IsExceptionProcessedPiloting", false);

		public static string DealContainerName => DynamicConfig.GetValue<string>("DealContainerName", "dealcontainer");
		public static string AdUnitHierarchyContainer => DynamicConfig.GetValue<string>("AdUnitHierarchyContainer", "adunithierarchy");

		public static string AllowQueueAccountStatusChangeCustomerId => DynamicConfig.GetValue<string>("AllowQueueAccountStatusChangeCustomerId", "");
		public static bool EnableAdExtensionGlobalizationPhase8 => DynamicConfig.GetValue<bool>("EnableAdExtensionGlobalizationPhase8", false);

		public static bool ReturnDummyDataForGenreReport => DynamicConfig.GetValue<bool>(nameof(ReturnDummyDataForGenreReport), false);
		public static string MmpBaseUrlSI => DynamicConfig.GetValue<string>(nameof(MmpBaseUrlSI), "");
		public static string MmpBaseUrlProd => DynamicConfig.GetValue<string>(nameof(MmpBaseUrlProd), "");

		public static string MmpEnvironment => DynamicConfig.GetValue<string>(nameof(MmpEnvironment), "");

		public static bool EnableDisplayAdsVerifiedTracking => DynamicConfig.GetValue<bool>(nameof(EnableDisplayAdsVerifiedTracking), false);


        public static bool EnableDeviceForXandrDisplayAds => DynamicConfig.GetValue<bool>(nameof(EnableDeviceForXandrDisplayAds), false);

        public static bool EnableDeviceForMSANDisplayAds => DynamicConfig.GetValue<bool>(nameof(EnableDeviceForMSANDisplayAds), false);


        public static bool MimeTypeAsVideoType => DynamicConfig.GetValue<bool>(nameof(MimeTypeAsVideoType), false);

		public static bool ShouldFetchTextAssetByAccountId => DynamicConfig.GetValue<bool>("ShouldFetchTextAssetByAccountId", true);

		public static int ShouldFetchTextAssetByAccountIdAdCountThreshold => DynamicConfig.GetValue<int>("ShouldFetchTextAssetByAccountIdAdCountThreshold", 0);

		public static HashSet<long> FetchTextAssetByAccountIdWhiteList => new HashSet<long>(DynamicConfig.GetValue("FetchTextAssetByAccountIdWhiteList", EmptyLongList));

		public static int TimeoutForVideoDownloadByAccountId => DynamicConfig.GetValue<int>("TimeoutForVideoDownloadByAccountId", 10000);
		public static bool ReturnDummyXandrDeals => DynamicConfig.GetValue<bool>("ReturnDummyXandrDeals", false);

		public static bool HotelIdDateRangeLimitCheck => DynamicConfig.GetValue<bool>(nameof(HotelIdDateRangeLimitCheck), false);

        public static bool AzureMediaServiceAppSecretRemoval => DynamicConfig.GetValue<bool>("AzureMediaServiceAppSecretRemoval", false);

        public static HashSet<long> AzureMediaServiceAppSecretRemovalAccounts => new HashSet<long>(DynamicConfig.GetValue("AzureMediaServiceAppSecretRemovalAccounts", EmptyLongList));

        public static HashSet<string> DealBlobNames
		{
			get
			{
				var dealBlobNameReadOnlyCollection = DynamicConfig.GetValue("DealBlobNames", new ReadOnlyCollection<string>(new List<string>()));
				var set = new HashSet<string>();
				dealBlobNameReadOnlyCollection.ForEach(s => set.Add(s.Trim().ToLowerInvariant()));
				return set;
			}
		}

		public static bool EnableCTAInAssetLinkGrid => DynamicConfig.GetValue<bool>("EnableCTAInAssetLinkGrid", false);

		#region  Datamart Clickhouse Migration
		public static string ClickhouseQueryBuilderRedisCache => DynamicConfig.GetValue("ClickhouseQueryBuilderRedisCache", string.Empty);
		public static string ClickhouseAzureStorageAccountConnectionString => DynamicConfig.GetValue("ClickhouseAzureStorageAccountConnectionString", string.Empty);
		#endregion

		public static bool EnableSUMBIDataForMultipleDB => DynamicConfig.GetValue<bool>("EnableSUMBIDataForMultipleDB", false);

		public static bool EnableMaxCampaignLevelBIDataRowCountLimit => DynamicConfig.GetValue<bool>("EnableMaxCampaignLevelBIDataRowCountLimit", false);

		public static int MaxCampaignLevelBIDataRowCount => DynamicConfig.GetValue<int>("MaxCampaignLevelBIDataRowCount", 380000);

		public static bool CostPerSaleDeliveryStatusEnabled => DynamicConfig.GetValue<bool>("CostPerSaleDeliveryStatusEnabled", false);

		public static bool EnableDynamicAdFeedAnalysis => DynamicConfig.GetValue<bool>("EnableDynamicAdFeedAnalysis", false);

        public static bool CropForMMAV2 => DynamicConfig.GetValue<bool>("CropForMMAV2", false);

        public static bool EnableConversionActionImport => DynamicConfig.GetValue<bool>("EnableConversionActionImport", false);

        public static bool EnableCustomerMatchListMSID => DynamicConfig.GetValue<bool>("EnableCustomerMatchListMSID", false);

        public static ReadOnlyCollection<long> GoalImportEnabledAccountIds
        {
            get
            {
                return DynamicConfig.GetValue<ReadOnlyCollection<long>>("EnableConversionActionImportTestAidList", new ReadOnlyCollection<long>(new List<long>()));
            }
        }   

		public static bool IsTestAccountgForGoalImport(long accountId)
		{
			return GoalImportEnabledAccountIds.Contains(accountId);
		}

		public static bool EnableMaxConversionValueForSearchCampaignsForImport => DynamicConfig.GetValue<bool>("EnableMaxConversionValueForSearchCampaignsForImport", false);

        public static bool EnableMigrateEscConversionEntities => DynamicConfig.GetValue<bool>("EnableMigrateEscConversionEntities", false);

        public static bool EnableTagMappingMultipleOverwrite => DynamicConfig.GetValue<bool>("EnableTagMappingMultipleOverwrite", false);

        public static bool EnableMaxConversionValueForSearchCampaignsForUpdateForImport => DynamicConfig.GetValue<bool>("EnableMaxConversionValueForSearchCampaignsForUpdateForImport", false);

		public static bool EnableSparseBiPiloting => DynamicConfig.GetValue<bool>("EnableSparseBiPiloting", false);

        public static bool EnableBiDataGroupSelectionLogic => DynamicConfig.GetValue<bool>("EnableBiDataGroupSelectionLogic", false);

        public static int EnableExcludeCampaignIdsThreshold => DynamicConfig.GetValue<int>("EnableExcludeCampaignIdsThreshold", 100);
		public static bool EnableExcludeCampaignIdForAdGroupBIData => DynamicConfig.GetValue<bool>("EnableExcludeCampaignIdForAdGroupBIData", false);

        public static bool EnableRemoveInlineChartOverallCalculation => DynamicConfig.GetValue<bool>("EnableRemoveInlineChartOverallCalculation", true);

		public static bool EnableNativeAdsFCap => DynamicConfig.GetValue("EnableNativeAdsFCap", false);

		public static bool IsAccountEnabledForVideoSourceCheck(long accountId)
		{
			int percentage = VideoSourceUrlCheckPercentage;
			return (accountId % 100) < percentage;
		}

		public static bool VideoSourceUrlFtpCallMock => DynamicConfig.GetValue<bool>("VideoSourceUrlFtpCallMock", false);

		public static bool EnableConversionValueRule => DynamicConfig.GetValue<bool>("EnableConversionValueRule", false);

        public static bool EnableUCMAccountDirectBIPilotCheck => DynamicConfig.GetValue<bool>("EnableUCMAccountDirectBIPilotCheck", false);

        public static int ImportSchedulerRunHours => DynamicConfig.GetValue<int>("ImportSchedulerRunHours", 24);

		public static bool EnableSkipInMarketAudienceCall => DynamicConfig.GetValue<bool>("EnableSkipInMarketAudienceCall", false);

		public static bool EnableAudienceAndGoalBatchCall => DynamicConfig.GetValue<bool>("EnableAudienceAndGoalBatchCall", false);
		public static int EnableAudienceAndGoalBatchSize => DynamicConfig.GetValue<int>("EnableAudienceAndGoalBatchSize", 500);

		public static int MaxCustomerScopesPerRequestForInternalUsers => DynamicConfig.GetValue<int>("MaxCustomerScopesPerRequestForInternalUsers", int.MaxValue);
		public static IReadOnlyDictionary<long, int> MaxCustomerScopesPerRequestForInternalUsersOverride => GetIntValueByLongKey("MaxCustomerScopesPerRequestForInternalUsersOverride");
		public static IReadOnlyCollection<string> CNResellerCodes => DynamicConfig.GetValue<IReadOnlyCollection<string>>("CNResellerCodes", EmptyStringSet);

		public static bool EnableSponsoredProductAdsWithoutSalesCountry => DynamicConfig.GetValue<bool>("EnableSponsoredProductAdsWithoutSalesCountry", false);

		public static bool IsNetflixTCAcceptedPropertyEnabled => DynamicConfig.GetValue<bool>("IsNetflixTCAcceptedPropertyEnabled", false);
        public static bool EnableMSANServingForAllDisplay => DynamicConfig.GetValue<bool>("EnableMSANServingForAllDisplay", false);

        public static HashSet<long> EnableSponsoredProductAdsWithoutSalesCountryCustomerIdAllowList => new HashSet<long>(DynamicConfig.GetValue("EnableSponsoredProductAdsWithoutSalesCountryCustomerIdAllowList", EmptyLongList));

		public static int ImageAnnotationBatchSize => DynamicConfig.GetValue<int>("ImageAnnotationBatchSize", 100);

        public static bool ImageAnnotatioIncludeROIInfo => DynamicConfig.GetValue<bool>("ImageAnnotatioIncludeROIInfo", false);


        public static string ImageAnnotationCosmosFileParentPath => DynamicConfig.GetValue<string>("ImageAnnotationCosmosFileParentPath", "");
		public static string ImageAnnotationAssetCosmosFileParentPath => DynamicConfig.GetValue<string>("ImageAnnotationAssetCosmosFileParentPath", "");

		public static int ImageAnnotationAccountTaskRescheduleDelaySeconds => DynamicConfig.GetValue<int>("ImageAnnotationAccountTaskRescheduleDelaySeconds", 100);
		public static int ImageAnnotationAssetTaskRescheduleDelaySeconds => DynamicConfig.GetValue<int>("ImageAnnotationAssetTaskRescheduleDelaySeconds", 100);

		public static int ImageAnnotationDataProcessBatchSize => DynamicConfig.GetValue<int>("ImageAnnotationDataProcessBatchSize", 100);

        public static bool ImageAnnotationEnableAssetBlobStorage => DynamicConfig.GetValue<bool>("ImageAnnotationEnableAssetBlobStorage", false);
        
        public static string ImageAnnotationBlobStorageAccount => DynamicConfig.GetValue<string>("ImageAnnotationBlobStorageAccount", "");

        public static string ImageAnnotationBlobStorageContainer => DynamicConfig.GetValue<string>("ImageAnnotationBlobStorageContainer", "");

        public static string ImageAnnotationBlobStoragePath => DynamicConfig.GetValue<string>("ImageAnnotationBlobStoragePath", "");

        public static bool EnableCostPerSaleBidStrategyLimitedStatus => DynamicConfig.GetValue<bool>("EnableCostPerSaleBidStrategyLimitedStatus", false);

		public static bool DisableHotelAdsV1API => DynamicConfig.GetValue<bool>("DisableHotelAdsV1API", true);

		public static string MMPBulkUploadDevToken => DynamicConfig.GetValue("MMPBulkUploadDevToken", string.Empty);

		public static string VideoTranscodingFullyQualifiedNamespace => DynamicConfig.GetValue("VideoTranscodingFullyQualifiedNamespace", string.Empty);
		public static string VideoTranscodingServiceQueueName => DynamicConfig.GetValue("VideoTranscodingServiceQueueName", string.Empty);
		public static string VideoTranscodingServiceClientId => DynamicConfig.GetValue("VideoTranscodingServiceClientId", string.Empty);

        public static bool RefreshVideoQueueMessages => DynamicConfig.GetValue("RefreshVideoQueueMessages", false);
        public static bool VideoUnexpectedErrorRetry => DynamicConfig.GetValue("VideoUnexpectedErrorRetry", false);
        public static bool ModeledConversionUpliftingReport => DynamicConfig.GetValue("ModeledConversionUpliftingReport", false);
        public static double ModeledConversionUpliftingThreshold => DynamicConfig.GetValue("ModeledConversionUpliftingThreshold", 2.0);

        public static bool EnablePmaxV2AspectRatios => DynamicConfig.GetValue("EnablePmaxV2AspectRatios", false);


        public static bool EnableAzureQueueExportVideo => DynamicConfig.GetValue("EnableAzureQueueExportVideo", false);

        public static bool IsVideoLibraryTranscodingEnabled(long accountId)
        {
            int percentage = DynamicConfig.GetValue("VideoLibraryTranscodingPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static bool IsHybirdTranscodingNonSavingEnabled(long accountId)
        {
            int percentage = DynamicConfig.GetValue("HybirdTranscodingNonSavingPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static bool IsHybirdTranscodingThumbnailSavingEnabled(long accountId)
        {
            int percentage = DynamicConfig.GetValue("HybirdTranscodingThumbnailUrlSavingPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static bool IsHybirdTranscodingStreamingUrlSavingEnabled(long accountId)
        {
            int percentage = DynamicConfig.GetValue("HybirdTranscodingStreamingUrlSavingPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static bool IsHybirdTranscodingDownloadUrlSavingPilotEnabled(long accountId)
        {
            int percentage = DynamicConfig.GetValue("HybirdTranscodingDownloadUrlSavingPilotPercentage", 0);
            return (accountId % 100) < percentage;
        }

        public static string AdsAppsVideoClientId => DynamicConfig.GetValue("AdsAppsVideoClientId", string.Empty);

        public static bool VideoStorageAccountAccountKeyRemoval(long accountId) 
        {
            var enabledAccounts = new HashSet<long>(DynamicConfig.GetValue("VideoStorageAccountAccountKeyRemovalAccounts", EmptyLongList));
            return DynamicConfig.GetValue<bool>("VideoStorageAccountAccountKeyRemoval", false) || enabledAccounts.Contains(accountId);
        } 

        public static bool EnableMMPXandarMsAdsSyncLogSource => DynamicConfig.GetValue<bool>("EnableMMPXandarMsAdsSyncLogSource", false);

		public static bool UseClickBasedConvForRepeatRate => DynamicConfig.GetValue<bool>("UseClickBasedConvForRepeatRate", false);

        public static TimeSpan SSOBlockingMigrationMaxRunTime => DynamicConfig.GetValue("SSOBlockingMigrationMaxRunTime", TimeSpan.FromSeconds(30));

        public static TimeSpan LogoAdExtensionAutoGenerationProcessorMaxRunTime => DynamicConfig.GetValue("LogoAdExtensionAutoGenerationProcessorMaxRunTime", TimeSpan.FromSeconds(30));

        public static int PercentCustomersForLogoAdExtensionAutoGeneration => DynamicConfig.GetValue("PercentCustomersForLogoAdExtensionAutoGeneration", 0);

        public static IReadOnlyList<long> LogoAdExtensionAutoGenerationCustomerList => DynamicConfig.GetValue("LogoAdExtensionAutoGenerationCustomerList", EmptyLongList);
        
        public static bool IsMigrationForAMSVideoEnabled => DynamicConfig.GetValue<bool>("MigrationForAMSVideo", false);

        public static bool FeedLabelAsCountryCodeEnabled => DynamicConfig.GetValue("FeedLabelAsCountryCodeEnabled", false);

        public static bool IsAccountNotificationServiceBusMSIAuthEnabled => DynamicConfig.GetValue<bool>("IsAccountNotificationServiceBusMSIAuthEnabled", false);

        public static string CCLinkingServiceBusHostName => DynamicConfig.GetValue("CCLinkingServiceBusHostName", string.Empty);

        public static string ChannelAndLocationMappingFileConfig => DynamicConfig.GetValue("ChannelAndLocationMappingFileConfig", "Xandr:bingads-xandr-location-mapping.csv,LinkedIn:linkedin-location-mapping.csv");

        public static bool FetchDisclaimerAdExtForDeliveryStatus => DynamicConfig.GetValue("FetchDisclaimerAdExtForDeliveryStatus", false);

        public static bool EnableCallToActionAssetInAssetGrid => DynamicConfig.GetValue("EnableCallToActionAssetInAssetGrid", false);

        public static bool EnableCountdownExpiredDeliveryStatusInAdGrid => DynamicConfig.GetValue("EnableCountdownExpiredDeliveryStatusInAdGrid", false);

        public static bool EnableAdImagePlacementMismatchDeliveryStatus => DynamicConfig.GetValue("EnableAdImagePlacementMismatchDeliveryStatus", false);

        public static bool EnableHasSensitiveContentAdAuditPoint => DynamicConfig.GetValue("EnableHasSensitiveContentAdAuditPoint", false);

        public static bool EnableSnapShotReportAvailability => DynamicConfig.GetValue("EnableSnapShotReportAvailability", false);
        public static int AudienceCampaignExpandedHeadlineCharLimitsCustomerPercentage => DynamicConfig.GetValue<int>("AudienceCampaignExpandedHeadlineCharLimitsCustomerPercentage", 0);
        
        public static bool IsUsingAPIForSalesCountryEnabled => DynamicConfig.GetValue("IsUsingAPIForSalesCountryEnabled", false);

        // Plugin configs
        public static string PluginClientId => DynamicConfig.GetValue("PluginClientId", string.Empty);
        public static string PluginAudience => DynamicConfig.GetValue("PluginAudience", string.Empty);
        public static string PluginTenantId => DynamicConfig.GetValue("PluginTenantId", string.Empty);
        public static string PluginAdInsightMTBaseUrl => DynamicConfig.GetValue("PluginAdInsightMTBaseUrl", string.Empty);
        public static string PluginCampaignODataBaseUrl => DynamicConfig.GetValue("PluginCampaignODataBaseUrl", string.Empty);
        public static string PluginCCMTBaseUrl => DynamicConfig.GetValue("PluginCCMTBaseUrl", string.Empty);

        public static IReadOnlyList<long> UsingAPIForSalesCountryAllowCIDList => DynamicConfig.GetValue<IReadOnlyList<long>>("UsingAPIForSalesCountryAllowCIDList", EmptyLongList);
        public static bool EnableLinkedInDeliveryStatus => DynamicConfig.GetValue("EnableLinkedInDeliveryStatus", false);

        public static bool EnableEULocationTargetingForLinkedInCampaign => DynamicConfig.GetValue("EnableEULocationTargetingForLinkedInCampaign", false);

        public static bool AllowUSOnlyLocationForLinkedInCampaign => DynamicConfig.GetValue("AllowUSOnlyLocationForLinkedInCampaign", false);

        public static bool EnableLinkedInAudienceEstimationValidation => DynamicConfig.GetValue("EnableLinkedInAudienceEstimationValidation", false);
        public static bool UseAddAndUpdateCampaignSprocV68 => DynamicConfig.GetValue("UseAddAndUpdateCampaignSprocV68", false);
        public static bool EnableLinkedInCampaignCustomerListAssoction => DynamicConfig.GetValue("EnableLinkedInCampaignCustomerListAssocaition", false);

        public static bool UseMIForImportTaskScheduler => DynamicConfig.GetValue("UseMIForImportTaskScheduler", false);
        public static string ImportTaskSchedulerEventHubEndpoint => DynamicConfig.GetValue("ImportTaskSchedulerEventHubEndpoint", string.Empty);
        public static string ImportTaskSchedulerEventHubName => DynamicConfig.GetValue("ImportTaskSchedulerEventHubName", string.Empty);
        public static bool EnableCheckingTargetCpaAndBudgetForImport=> DynamicConfig.GetValue("EnableCheckingTargetCpaAndBudgetForImport", false);
        public static bool EditorialAppealUseMI => DynamicConfig.GetValue("EditorialAppealUseMI", false);
        public static string EditorialAppealEventHubEndPoint => DynamicConfig.GetValue("EditorialAppealEventHubEndPoint", string.Empty);

        public static string ITunesUrlTemplate => DynamicConfig.GetValue("ITunesUrlTemplate", string.Empty);

        public static string ITunesAppUrlTemplate => DynamicConfig.GetValue("ITunesAppUrlTemplate", string.Empty);

        public static string[] ObjectStoreTableLocations => DynamicConfig.GetValue("ObjectStoreTableLocations", "").Split(';');

        public static bool AppIconObjectStoreHttpsAccess => DynamicConfig.GetValue("AppIconObjectStoreHttpsAccess", false);

        public static bool AllowMergingDuplicateImageLink => DynamicConfig.GetValue("AllowMergingDuplicateImageLink", false);
        public static string AddressExtractionServiceUri => DynamicConfig.GetValue<string>("AddressExtractionServiceUri", string.Empty);
        public static string AddressExtractionServiceSecret => DynamicConfig.GetValue<string>("AddressExtractionServiceSecret", string.Empty);
        public static string XandrInvestEndpointUrl => DynamicConfig.GetValue<string>("XandrInvestEndpointUrl", string.Empty);
        public static int XandrCreativeQueryRetryCount => DynamicConfig.GetValue<int>("XandrCreativeQueryRetryCount", 3);

        public static bool EnableAdGroupNameLookupSkip => DynamicConfig.GetValue<bool>("EnableAdGroupNameLookupSkip", false);

        public static bool EnableAssetGroupVideoAssetEditorial => DynamicConfig.GetValue("EnableAssetGroupVideoAssetEditorial", false);

        public static bool EnableAccountPlacementExclusionLists => DynamicConfig.GetValue("EnableAccountPlacementExclusionLists", false);
        public static bool EnableAccountPlacementInclusionLists => DynamicConfig.GetValue("EnableAccountPlacementInclusionLists", false);

        public static bool EnableConversionDelayMetrics => DynamicConfig.GetValue("EnableConversionDelayMetrics", false);

        public static bool EnableBulkDownloadAccountPlacementExclusionList => DynamicConfig.GetValue("EnableBulkDownloadAccountPlacementExclusionList", false);

        public static bool UsePublicUpdateAssetV9 => DynamicConfig.GetValue("UsePublicUpdateAssetV9", false);
        public static string AssetBackfillTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("AssetBackfillTaskTypeNameForTaskEngine");
        public static int ParallelExecutionsCountForAssetBackfillTask => DynamicConfig.GetRequiredValue<int>("ParallelExecutionsCountForAssetBackfillTask");
        public static int SleepTimeInMillisecondsForAssetBackfillTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsForAssetBackfillTask");
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForAssetBackfillTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForAssetBackfillTask");
        public static int AssetBackfillTaskHeartBeatInMs => DynamicConfig.GetValue<int>("AssetBackfillTaskHeartBeatInMs", 10000);

        public static bool VerticalVideoSupport => DynamicConfig.GetValue("VerticalVideoSupport", false);
        public static int  VerticalVideoSupportPilotPercentage => DynamicConfig.GetValue<int>("VerticalVideoSupportPilotPercentage", 0);
        
        public static bool BulkDBMigrationStarted => DynamicConfig.GetValue("BulkDBMigrationStarted", false);
        public static bool BulkDBMigrationCompleted => DynamicConfig.GetValue("BulkDBMigrationCompleted", false);

        public static string VideoRecommendationTaskTypeNameForTaskEngine => DynamicConfig.GetValue<string>("VideoRecommendationTaskTypeNameForTaskEngine", "VideoRecommendationTest");

        public static int ParallelExecutionsCountForVideoRecommendationTask => DynamicConfig.GetValue<int>("ParallelExecutionsCountForVideoRecommendationTask", 1);
        public static int SleepTimeInMillisecondsForVideoRecommendationTask => DynamicConfig.GetValue<int>("SleepTimeInMillisecondsForVideoRecommendationTask", 5000);
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForVideoRecommendationTask => DynamicConfig.GetValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForVideoRecommendationTask", 5000);
        public static bool EnableClickhouseQueriesByPassPilot => DynamicConfig.GetValue("EnableClickhouseQueriesByPassPilot", false);

        public static int VideoRecommendationBatchSize => DynamicConfig.GetValue<int>("VideoRecommendationBatchSize", 10);

        public static int VideoRecommendationAdsFetchBatchSize => DynamicConfig.GetValue<int>("VideoRecommendationAdsFetchBatchSize", 10);
        
        public static int MaxP1AdCountToGenerate => DynamicConfig.GetValue<int>("MaxP1AdCountToGenerate", 50);
        public static string VideoRecommendationStreamFileRootPath => DynamicConfig.GetValue<string>("VideoRecommendationStreamFileRootPath", string.Empty);

        public static int VideoRecommendationRetryIntervalInSeconds => DynamicConfig.GetValue<int>("VideoRecommendationRetryIntervalInSeconds", 3000);

        public static bool UseMockToGenerateVideo => DynamicConfig.GetValue<bool>("UseMockToGenerateVideo", true);
    
        public static int VideoRecommendationMileSecondToSleep => DynamicConfig.GetValue<int>("VideoRecommendationMileSecondToSleep", 18000);
        
        public static string VideoRecommendationAccountIdsWhiteList => DynamicConfig.GetValue<string>("VideoRecommendationAccountIdsWhiteList", string.Empty);

        public static bool UseNewFlowForVideoRecommendation => DynamicConfig.GetValue<bool>("UseNewFlowForVideoRecommendation", false);

        public static string VideoGenClipCreationTaskTypeNameForTaskEngine => DynamicConfig.GetRequiredValue<string>("VideoGenClipCreationTaskTypeNameForTaskEngine");
        public static int ParallelExecutionsCountForVideoGenClipCreationTask => DynamicConfig.GetRequiredValue<int>("ParallelExecutionsCountForVideoGenClipCreationTask");
        public static int SleepTimeInMillisecondsForVideoGenClipCreationTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsForVideoGenClipCreationTask");
        public static int SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForVideoGenClipCreationTask => DynamicConfig.GetRequiredValue<int>("SleepTimeInMillisecondsWhenMaxConcurrentRequestsReachedForVideoGenClipCreationTask");
        public static int VideoGenClipCreationTaskHeartBeatInMs => DynamicConfig.GetValue<int>("VideoGenClipCreationTaskHeartBeatInMs", 10000);
        public static bool VideoGenClipCreationTaskUseMock => DynamicConfig.GetValue<bool>("VideoGenClipCreationTaskUseMock", true);

        public static IReadOnlyList<int> CCPilotPropagationFeatures => DynamicConfig.GetValue("CCPilotPropagationFeatures", EmptyIntList);
        public static bool IgnoreAppMetadataFailedToSync => DynamicConfig.GetValue<bool>("IgnoreAppMetadataFailedToSync", false);
        public static string[] AppIdsToIgnoreIfFailedToSync => DynamicConfig.GetValue("AppIdsToIgnoreIfFailedToSync", "").Split(';');
        public static string MsanPlannerPrimaryClickHouseConnectionSecretName => DynamicConfig.GetValue("MsanPlannerPrimaryClickHouseConnectionSecretName", string.Empty);
        public static string MsanPlannerBCPClickHouseConnectionSecretName => DynamicConfig.GetValue("MsanPlannerBcpClickHouseConnectionSecretName", string.Empty);
        public static int MsanPlannerClickhouseMaxThreads => DynamicConfig.GetValue<int>("MsanPlannerClickhouseMaxThreads", 4);
        public static long MsanPlannerClickhouseMaxMemUsage => DynamicConfig.GetValue<long>("MsanPlannerClickhouseMaxMemUsage", ********000);

        // config for consent mode campaign data task
        public static string ConsentModeCustomerOsTable => DynamicConfig.GetValue<string>("ConsentModeCustomerOsTable", String.Empty);
        public static int ConsentModeBatchSize => DynamicConfig.GetValue<int>("ConsentModeBatchSize", 1000);
        public static string ConsentModeListTaskCosmosFilePath => DynamicConfig.GetValue<string>("ConsentModeListTaskCosmosFilePath", String.Empty);
        public static string ConsentModeListTaskCosmosFileName => DynamicConfig.GetValue<string>("ConsentModeListTaskCosmosFileName", String.Empty);
        public static bool EnableUpdateConsentModeDecision => DynamicConfig.GetValue<bool>("EnableUpdateConsentModeDecision", false);
        public static string ConsentModeDecisionOsTable => DynamicConfig.GetValue<string>("ConsentModeCustomerDecisionOsTable", String.Empty); //Conv_ConsentModeCustomerDecision
        //config for query fix info under account goal api
        public static bool EnableQueryFixInfoForAccountGoalApi => DynamicConfig.GetValue<bool>("EnableQueryFixInfoForAccountGoalApi", false);
        public static int ObjectStoreQueryBatchSizeForAccountGoalApi => DynamicConfig.GetValue<int>("ObjectStoreQueryBatchSizeForAccountGoalApi", 600);
        public static int ObjectStoreInternalInMsForAccountGoalApi => DynamicConfig.GetValue<int>("ObjectStoreInternalInMsForAccountGoalApi", 500);

        public static bool IsSavingExistingEditorialStatusEnabled => DynamicConfig.GetValue<bool>("IsSavingExistingEditorialStatusEnabled", false);
        public static IReadOnlyDictionary<string, short> TaskEngineRestAPIPilotPercentageMap => ConvertToStringShortMap("TaskEngineRestAPIPilotPercentageMap");

        public static bool EnableAIGCForAADUser => DynamicConfig.GetValue("EnableAIGCForAADUser", false);

        public static bool EnableAppCampaignTrackingTemplateValidator => DynamicConfig.GetValue<bool>("EnableAppCampaignTrackingTemplateValidator", false);

        public static bool EnableMsanPlannerCache => DynamicConfig.GetValue<bool>("EnableMsanPlannerCache", false);

        // config for GTM domain upload task
        public static string DomainGTMListOsTable => DynamicConfig.GetValue<string>("DomainGTMListOsTable", String.Empty);
        public static string DomainGTMListTaskCosmosFilePath => DynamicConfig.GetValue<string>("DomainGTMListTaskCosmosFilePath", String.Empty);
        public static string DomainGTMListTaskCosmosFileName => DynamicConfig.GetValue<string>("DomainGTMListTaskCosmosFileName", String.Empty);
        public static bool EnableDomainGTMListTask => DynamicConfig.GetValue<bool>("EnableDomainGTMListTask", false);

        public static string Html5StorageAccountName => DynamicConfig.GetValue<string>("Html5StorageAccountName", String.Empty);

        public static string Html5StorageContainerName => DynamicConfig.GetValue<string>("Html5StorageContainerName", String.Empty);

        public static string Html5CDNBaseUrl = DynamicConfig.GetValue<string>("Html5CDNBaseUrl", String.Empty);

        public static string ClipchampExportQueueUrl => DynamicConfig.GetValue<string>("ClipchampExportQueueUrl", String.Empty);

        public static string CampaignMTServiceEndPoint => DynamicConfig.GetValue<string>("CampaignMTServiceEndPoint", String.Empty);

        public static bool SetCTALanguageAsNone => DynamicConfig.GetValue<bool>("SetCTALanguageAsNone", false);

        public static int OfflineConversionValidationV2Percentage => DynamicConfig.GetValue<int>("OfflineConversionValidationV2Percentage", 0);
        public static bool EnableLinkedInOfflineConversionGoal => DynamicConfig.GetValue<bool>("EnableLinkedInOfflineConversionGoal", false);
        public static IReadOnlyList<long> LinekdInOfflineConversionUploadAccountList => DynamicConfig.GetValue("LinekdInOfflineConversionUploadAccountList", EmptyLongList);

        public static bool EnableResponsiveAdVideoRecommendationTask => DynamicConfig.GetValue<bool>("EnableResponsiveAdVideoRecommendationTask", false);
        public static string ResponsiveAdVideoRecommendationTaskTypeNameForTaskEngine => DynamicConfig.GetValue<string>("ResponsiveAdVideoRecommendationTaskTypeNameForTaskEngine", string.Empty);
    
        public static bool IBlobStorageAsyncAggregatorServiceCore => DynamicConfig.GetValue<bool>("IBlobStorageAsyncAggregatorServiceCore", false);
        public static bool IBlobStorageAsyncCampaignServiceData => DynamicConfig.GetValue<bool>("IBlobStorageAsyncCampaignServiceData", false);
        public static bool IBlobStorageAsyncReconciliationReportAzure => DynamicConfig.GetValue<bool>("IBlobStorageAsyncReconciliationReportAzure", false);



        public static HashSet<long> ShoppingCampaignDeliveryStatusMigrationCustomerWhitelist => new HashSet<long>(DynamicConfig.GetValue("ShoppingCampaignDeliveryStatusMigrationCustomerWhitelist", EmptyLongList));
        public static int PercentCustomersForCampaignDeliveryStatusMigration => DynamicConfig.GetValue("PercentCustomersForCampaignDeliveryStatusMigration", 0);

        public static bool BulkDownloadValidateAuthorizedUser => DynamicConfig.GetValue<bool>("BulkDownloadValidateAuthorizedUser", false);

        public static bool EnableShoppingCampaignDeliveryStatusMigration(long customerId)
        {
            return ShoppingCampaignDeliveryStatusMigrationCustomerWhitelist.Contains(customerId)
                || (customerId % 100) < PercentCustomersForCampaignDeliveryStatusMigration;
        }

        public static bool EnableAppCampaignCopyPaste => DynamicConfig.GetValue<bool>("EnableAppCampaignCopyPaste", false);
    }
}
