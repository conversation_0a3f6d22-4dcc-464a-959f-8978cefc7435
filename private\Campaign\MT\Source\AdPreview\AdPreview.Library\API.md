﻿# API Specification

## Endpoints

- SI: https://adrecommendation.api.sandbox.ads.microsoft.com
- Prod: https://adrecommendation.api.ads.microsoft.com

## Fetch Recommendations

POST /api/asset 

**Headers:**
x-ms-requestid: GUID

**Payload:**
```json
{
    "Website": "https://noodleboatthai.com",
    "Prompt": "Authentic thai food"
}
```

**Sample Response (Success - complete):**
```json
{
    "lookupId": "REqkNsUVRKP1GtUFv8v1RFqDuG6K6J1e6o/WFnSOuhs=",
    "businessDetails": {
        "businessName": "NOODLE BOAT THAI CUISINE",
        "address": "700 NW Gilman Blvd, Issaquah, WA 98027",
        "phoneNumber": "(*************",
        "internationalPhoneNumber": "******-391-8096"
    },
    "recommendations": [
        {
            "campaignType": "Pmax",
            "imageAssets": [
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/01c6e5cf-4f70-4807-8281-421b6e9b0344.jpeg",
                    "source": "LandingPage"
                },
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/190b707b-e22c-4343-b3e9-19f40fdb7a24.jpeg",
                    "source": "LandingPage"
                }
            ],
            "shortHeadlines": [
                {
                    "text": "Taste of Thailand",
                    "modelSource": "LLM"
                },
                {
                    "text": "Flavorful Thai",
                    "modelSource": "LLM"
                }
            ],
            "longHeadlines": [
                {
                    "text": "Savor authentic Thai cuisine at Noodle Boat in Issaquah!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Savor authentic Thai cuisine at our family-run spot in Issaquah!",
                    "modelSource": "LLM"
                }
            ],
            "descriptions": [
                {
                    "text": "Savor the taste of Authentic Thai cuisine at Noodle Boat!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Discover the authentic taste of Thailand at Noodle Boat",
                    "modelSource": "LLM"
                }
            ],
            "hasErrors": false
        }
    ]
}
```

**Sample Response (Success - business details missing):**
```json
{
    "lookupId": "REqkNsUVRKP1GtUFv8v1RFqDuG6K6J1e6o/WFnSOuhs=",
    "businessDetails": {
        "businessName": null,
        "address": null,
        "phoneNumber": null,
        "internationalPhoneNumber": null
    },
    "recommendations": [
        {
            "campaignType": "Pmax",
            "imageAssets": [
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/01c6e5cf-4f70-4807-8281-421b6e9b0344.jpeg",
                    "source": "LandingPage"
                },
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/190b707b-e22c-4343-b3e9-19f40fdb7a24.jpeg",
                    "source": "LandingPage"
                }
            ],
            "shortHeadlines": [
                {
                    "text": "Taste of Thailand",
                    "modelSource": "LLM"
                },
                {
                    "text": "Flavorful Thai",
                    "modelSource": "LLM"
                }
            ],
            "longHeadlines": [
                {
                    "text": "Savor authentic Thai cuisine at Noodle Boat in Issaquah!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Savor authentic Thai cuisine at our family-run spot in Issaquah!",
                    "modelSource": "LLM"
                }
            ],
            "descriptions": [
                {
                    "text": "Savor the taste of Authentic Thai cuisine at Noodle Boat!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Discover the authentic taste of Thailand at Noodle Boat",
                    "modelSource": "LLM"
                }
            ],
            "hasErrors": false
        }
    ]
}
```

**Sample Response (Error):**
```json
{
    "title": "InvalidPayload",
    "status": 400,
    "detail": "Value cannot be null. (Parameter 'Look up id cannot be null.')"
}
```

## Lookup Recommendations

GET /api/asset 

**Headers:**
x-ms-requestid: GUID

**Query Parameters:**

Param | Type | IsRequired | Source |
----------- | ----------- | ------------ | ------------ | 
lookupId | string | Yes | Returned in Fetch Recommendations call / email click link |
signature | string | No | Embedded in email click link |
email | string | No | Embedded in email click link |
useMock | bool | No | For mock response in UI CI test scenarios |

**Sample Response (Success - From LandingPage):**
```json
{
    "businessDetails": {
        "businessName": "NOODLE BOAT THAI CUISINE",
        "address": "700 NW Gilman Blvd, Issaquah, WA 98027",
        "phoneNumber": "(*************",
        "internationalPhoneNumber": "******-391-8096"
    },
    "recommendations": [
        {
            "campaignType": "Pmax",
            "imageAssets": [
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/01c6e5cf-4f70-4807-8281-421b6e9b0344.jpeg",
                    "source": "LandingPage"
                },
                {
                    "url": "https://aggsvcstoragedev.blob.core.windows.net/images/4b3e2118-757d-4b73-9bac-5dcf6bf520b7/190b707b-e22c-4343-b3e9-19f40fdb7a24.jpeg",
                    "source": "LandingPage"
                }
            ],
            "shortHeadlines": [
                {
                    "text": "Taste of Thailand",
                    "modelSource": "LLM"
                },
                {
                    "text": "Flavorful Thai",
                    "modelSource": "LLM"
                }
            ],
            "longHeadlines": [
                {
                    "text": "Savor authentic Thai cuisine at Noodle Boat in Issaquah!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Savor authentic Thai cuisine at our family-run spot in Issaquah!",
                    "modelSource": "LLM"
                }
            ],
            "descriptions": [
                {
                    "text": "Savor the taste of Authentic Thai cuisine at Noodle Boat!",
                    "modelSource": "LLM"
                },
                {
                    "text": "Discover the authentic taste of Thailand at Noodle Boat",
                    "modelSource": "LLM"
                }
            ],
            "hasErrors": false
        }
    ],
    "website": "https://noodleboatthai.com",
    "prompt": "Authentic thai food"
}
```


**Sample Response (Cache miss):**
```json
{
    "businessDetails": null,
    "recommendations": null,
    "website": null,
    "prompt": null
}
```

**Sample Response (From Email):**
```json
{
    "userDetails": {
        "name": "Test User",
        "email": "<EMAIL>",
        "phone": "1234567895"
    },
    "businessDetails": {
        "businessName": "NOODLE BOAT THAI CUISINE",
        "address": "700 NW Gilman Blvd, Issaquah, WA 98027",
        "phoneNumber": "(*************",
        "internationalPhoneNumber": "******-391-8096"
    },
    "recommendations": [
        {
            "campaignType": "Pmax",
            "imageAssets": [
                {
                    "url": "https://aggsvcstorage.blob.core.windows.net/stock-images/1--7p0nKf-QBEP9zvps_j31Gbfgk6xeU_.jpg",
                    "source": "Other"
                }
            ],
            "shortHeadlines": [
                {
                    "text": "Family Flavor",
                    "modelSource": "LLM"
                }
            ],
            "longHeadlines": [
                {
                    "text": "Discover our family-run Thai delights",
                    "modelSource": "LLM"
                }
            ],
            "descriptions": [
                {
                    "text": "Try our flavorsome dishes from a family-run Thai spot",
                    "modelSource": "LLM"
                }
            ],
            "hasErrors": false,
            "errors": []
        }
    ],
    "website": "https://noodleboatthai.com",
    "prompt": ""
}
```

## Onboard

POST /api/asset/onboard

**Headers:**
x-ms-requestid: GUID

**Query Parameters:**

Param | Type | IsRequired | Source |
----------- | ----------- | ------------ | ------------ | 
lookupId | string | Yes | Returned in Fetch Recommendations call |
resumeUrl | string | Yes | signup resume url generated by client |
emailVersion | int | No | 1: email w/o consent, 2: email with consent |
useMock | bool | No | For mock recommendations in UI CI test scenarios |

**Payload:**
```json
{
    "Name": "Test User",
    "Email": "<EMAIL>",
    "Phone": "12354678951",
    "Country": "US",
    "Locale": "en-US"
}
```

**Sample Response (Success):**
```json
{
    "signature": "Fcs5w4au5t2alChoArDjw442QZAFCxx02%2fY1L8elqns%3d"
}
```

**Sample Response (Error):**
```json
{
    "type": "https://tools.ietf.org/html/rfc7235#section-3.1",
    "title": "Unauthorized",
    "status": 401,
    "traceId": "00-8456dc2ae08e02791059ff532ea0bfbb-27131ac5318c41cd-00"
}
```

## Error Handling

HTTP Status | Error | Detail | Example |
----------- | ----------- | ------------ | ------------ | 
400 | InvalidPayload | Website should be a valid URL | <EMAIL> |
400 | InvalidPayload | Prompt length exceeds the maximum size | very long prompt string |
400 | InvalidPayload | LookupId is null or empty | empty string |
400 | InvalidPayload | Email address is null or empty | empty string |
400 | AdPreviewWebsiteCrawlError | Website is unavailable or invalid | user entered an invalid/unavailable website (e.g. https://noodleboatthai.co) |
400 | AdPreviewInappropriateContentError | Inappropriate content | user entered inapproprate content in prompt (blocked by RAI) |
400 | AdPreviewWebsiteInsufficientContentError | Website has insufficient content | user entered a site that has insufficient content or content not approved by editorial policies |
401 | Unauthorized |  | Signature is null or invalid |
429 | Too Many Requests | Rate limit reached | Received more than allowed request from the same ip address |
500 | InternalError | InternalError | Unable to fetch recommendations due to token error / storage failure |
503 | Service Unavailable | API has been turned off | High call volume detected so DRI has turned off the api |