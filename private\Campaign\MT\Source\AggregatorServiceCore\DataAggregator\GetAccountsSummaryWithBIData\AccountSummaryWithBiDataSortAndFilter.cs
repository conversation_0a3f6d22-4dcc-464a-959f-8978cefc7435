﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.DataAggregator;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities;
    using Microsoft.Advertising.Client.Import;
    using Microsoft.BingAds.Utils.SortUtils;

    public class AccountsSummaryWithBIDataSortAndFilter
    {
        private ILogShared logger;
        private GridDataSelection gridDataSelection;
        private AccountsSummaryWithBiDataCacheItem accountSummaryCache;
        private CultureInfo culture;
        private bool usePeriodComparisonBiData;

        public AccountsSummaryWithBIDataSortAndFilter(
            ILogShared logger,
            GridDataSelection gridDataSelection,
            AccountsSummaryWithBiDataCacheItem accountSummaryCache,
            bool usePeriodComparisonBiData)
        {
            if (logger == null)
            {
                throw new ArgumentNullException(nameof(logger));
            }

            if (accountSummaryCache == null)
            {
                throw new ArgumentNullException(nameof(accountSummaryCache));
            }

            this.gridDataSelection = gridDataSelection;
            this.logger = logger;
            this.accountSummaryCache = accountSummaryCache;
            this.usePeriodComparisonBiData = usePeriodComparisonBiData;

            this.culture = CultureInfo.InvariantCulture;
            int? cultureInfoLCID = this.gridDataSelection?.LocalizationLookup?.CultureInfoLCID;

            if (cultureInfoLCID.HasValue && cultureInfoLCID != 0)
            {
                this.culture = new CultureInfo(cultureInfoLCID.Value);
            }
        }

        public bool HasFilter()
        {
            return AggregatorHelper.HasGridFilter(this.gridDataSelection);
        }

        public IList<int> Execute(IAccountCallContext context)
        {
            ParallelQuery<int> rawKeys = this.accountSummaryCache.Accounts.Keys.AsParallel();

            IEnumerable<int> filteredKeys = this.Filter(rawKeys);

            IList<int> filteredAndSorted = this.Sort(filteredKeys.AsParallel());

            return filteredAndSorted;
        }

        private IEnumerable<int> Filter(IEnumerable<int> input)
        {
            IEnumerable<int> results = input;

            using (logger.AcquireNamedPerfLogger("AccountSummaryWithBIDataSortAndFilter", "Filter"))
            {
                if (this.gridDataSelection != null && this.gridDataSelection.GridFilter != null && this.gridDataSelection.GridFilter.Any())
                {
                    foreach (GridFilterExpression expression in this.gridDataSelection.GridFilter)
                    {
                        results = results.Where(this.CreateWherePredicate(expression));
                    }
                }
            }

            return results;
        }

        public IList<int> Sort(IEnumerable<int> input)
        {
            using (this.logger.AcquireNamedPerfLogger("AccountSummaryWithBIDataSortAndFilter", "DeletedAccountsLastSort"))
            {
                IEnumerable<int> resultKeys;

                // Always sort by account deleted status first, as UI needs the block of deleted accounts on the bottom of the page.
                IOrderedPagedEnumerable<int> firstSort = AggregatorEntityOperations.SortNonStringHelper(
                    input,
                    (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.IsAccountDeleted),
                    new PageInfo(),
                    isAsc: true);

                if (this.gridDataSelection != null && this.gridDataSelection.GridSort != null)
                {
                    GridSort gridSort = this.gridDataSelection.GridSort;
                    bool isAsc = gridSort.SortDirection == SortDirection.ASCENDING;
                    IOrderedPagedEnumerable<int> secondSort;

                    PeriodComparisonOptions options = PeriodComparisonOptions.None;
                    if (this.usePeriodComparisonBiData)
                    {
                        options = gridSort.PeriodComparsionSortOptions;

                        switch (options)
                        {
                            case PeriodComparisonOptions.Change:
                            case PeriodComparisonOptions.ChangePercentage:
                            case PeriodComparisonOptions.Period:
                            case PeriodComparisonOptions.None:
                                break;

                            default:
                                throw new InvalidOperationException(string.Format("Sort - AccountsSummary - Unknown PeriodComparisonOptions value: {0}", options));
                        }
                    }

                    if (gridSort.SortByGridColumn == GridColumn.Status)
                    {
                        this.logger.LogInfo("Accounts Summary Sort - unexpected use of GridColumn.Status instead of GridColumn.AccountStatus");
                    }

                    switch (gridSort.SortByGridColumn)
                    {
                        case GridColumn.Status:
                        case GridColumn.AccountStatus:
                            secondSort = this.ThenBySortNonString<AccountStatus>(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Status));
                            break;

                        case GridColumn.AccountName:
                            secondSort = this.ThenBySortString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Name));
                            break;

                        case GridColumn.AccountId:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Id));
                            break;

                        case GridColumn.Currency:
                            secondSort = this.ThenBySortString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.CurrencyCode));
                            break;

                        case GridColumn.CompanyName:
                            secondSort = this.ThenBySortString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.CustomerName));
                            break;

                        case GridColumn.BillToCustomerName:
                            secondSort = this.ThenBySortString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.BillToCustomerName));
                            break;

                        case GridColumn.AccountInactiveReasonsCount:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.AccountInactiveReasonsCount));
                            break;

                        case GridColumn.AccountMode:
                            secondSort = this.ThenBySortNonString<AccountMode>(firstSort, isAsc, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.AccountMode2 ?? AccountMode.Legacy));
                            break;

                        case GridColumn.QualityScore:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetAccountQualityScoreMemberOrDefault(accountId, (qualityScoreData) => qualityScoreData.OverallQualityScore));
                            break;
                        // IO
                        case GridColumn.IOPurchaseOrder:
                            secondSort = this.ThenBySortString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.PurchaseOrder));
                            break;
                        case GridColumn.IOBudget:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.Budget.GetValueOrDefault()));
                            break;
                        case GridColumn.RemainingIOBudget:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.RemainingBudget.GetValueOrDefault()));
                            break;
                        case GridColumn.TotalRemainingIOBudget:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.TotalRemainingBudget.GetValueOrDefault()));
                            break;
                        case GridColumn.StartDate:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDate.GetValueOrDefault()));
                            break;
                        case GridColumn.EndDate:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EndDate.GetValueOrDefault()));
                            break;

                        // budget
                        case GridColumn.DailyBudget:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.DailyBudget.GetValueOrDefault()));
                            break;
                        case GridColumn.MonthlyBudget:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.MonthlyBudget.GetValueOrDefault()));
                            break;
                        case GridColumn.LifetimeBudgetAmount:
                            secondSort = this.ThenBySortNonString(firstSort, isAsc, (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.LifetimeBudget.GetValueOrDefault()));
                            break;

                        // BI Columns
                        case GridColumn.CTR:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ClickThruRate.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.Clicks:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.Clicks.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.Impressions:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.Impressions.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.TotalEffectiveCost: // "Spend"
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.Spent.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.Conversions:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.Conversions.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AverageCPC:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AverageCPC.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AverageCPM:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AverageCPM.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AverageCPV:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AverageCPV.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AveragePosition:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AveragePosition.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.CPA:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.CPA.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.RevenueOnAdSpend:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.RevenueOnAdSpend.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.ConversionRate:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ConversionRate.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AdvertiserReportedRevenue:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AdvertiserReportedRevenue.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.TopImpressionRate:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.TopImpressionRate.GetValueOrDefault(), options, isAsc);
                            break;

                        case GridColumn.AbsoluteTopImpressionRate:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AbsoluteTopImpressionRate.GetValueOrDefault(), options, isAsc);
                            break;

                        // IS Column - The selector must return the nullable value for the correct SoV participation filter logic to work.
                        case GridColumn.AuctionWonPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionWonPercent, options, isAsc);
                            break;

                        case GridColumn.AuctionLostToBudgetPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionLostToBudgetPercent, options, isAsc);
                            break;

                        case GridColumn.AuctionLostToRankPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionLostToRankPercent, options, isAsc);
                            break;

                        case GridColumn.AuctionLostToLandingPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionLostToLandingPercent, options, isAsc);
                            break;

                        case GridColumn.AuctionLostToAdQualityPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionLostToAdQualityPercent, options, isAsc);
                            break;

                        case GridColumn.AuctionLostToBidPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AuctionLostToBidPercent, options, isAsc);
                            break;

                        case GridColumn.AbsoluteTopImpressionSharePercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AbsoluteTopImpressionSharePercent, options, isAsc);
                            break;

                        case GridColumn.ClickSharePercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.ClickSharePercent, options, isAsc);
                            break;

                        case GridColumn.ExactMatchImpressionSharePercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.ExactMatchImpressionSharePercent, options, isAsc);
                            break;

                        case GridColumn.TopImpressionSharePercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.TopImpressionSharePercent, options, isAsc);
                            break;

                        case GridColumn.TopISLostToBudgetPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.TopISLostToBudgetPercent, options, isAsc);
                            break;

                        case GridColumn.TopISLostToRankPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.TopISLostToRankPercent, options, isAsc);
                            break;

                        case GridColumn.AbsoluteTopISLostToBudgetPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AbsoluteTopISLostToBudgetPercent, options, isAsc);
                            break;

                        case GridColumn.AbsoluteTopISLostToRankPercent:
                            secondSort = this.SortThenBySovColumn(firstSort, (biData) => biData.AbsoluteTopISLostToRankPercent, options, isAsc);
                            break;

                        // Audience SOV 
                        case GridColumn.AudienceAuctionWonPercent:
                            secondSort = this.SortThenByAudienceSovColumn(firstSort, (biData) => biData.AudienceAuctionWonPercent, options, isAsc);
                            break;

                        case GridColumn.AudienceTopISLostToBudgetPercent:
                            secondSort = this.SortThenByAudienceSovColumn(firstSort, (biData) => biData.AudienceTopISLostToBudgetPercent, options, isAsc);
                            break;

                        case GridColumn.AudienceTopISLostToRankPercent:
                            secondSort = this.SortThenByAudienceSovColumn(firstSort, (biData) => biData.AudienceTopISLostToRankPercent, options, isAsc);
                            break;

                        case GridColumn.CustomColumn:
                            secondSort = AggregatorEntityOperations.SortThenByCustomColumn(
                                firstSort,
                                (int accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, gridSort.CustomColumnId),
                                (int accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, gridSort.CustomColumnId),
                                options,
                                isAsc);
                            break;

                        case GridColumn.AllConversions:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversions.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversions:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversions.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsRevenue:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversionsRevenue.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsCPA:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversionsCPA.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsReturnOnAdSpend:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversionsReturnOnAdSpend.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsRate:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversionsRate.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllConversionRate:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversionRate.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllConversionAdvertiserReportedRevenue:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllConversionRevenueOnAdSpend:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversionRevenueOnAdSpend.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllConversionCPA:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversionCPA.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ConversionsCredit:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ConversionsCredit.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.PartialConversionCPA:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.PartialConversionCPA.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.PartialConversionRate:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.PartialConversionRate.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllConversionsCredit:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllConversionsCredit.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllPartialConversionCPA:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllPartialConversionCPA.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.AllPartialConversionRate:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.AllPartialConversionRate.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsCredit:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughConversionsCredit.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughPartialConversionsCPA:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughPartialConversionsCPA.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;
                        case GridColumn.ViewThroughPartialConversionsRate:
                            secondSort = this.SortThenByBIDataColumn(
                                firstSort,
                                (biData) => biData.ViewThroughPartialConversionsRate.GetValueOrDefault(),
                                options,
                                isAsc);
                            break;

                        case GridColumn.ImportName:
                            secondSort = this.ThenBySortString(
                                firstSort,
                                isAsc,
                                (int accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.ImportName));
                            break;
                        case GridColumn.ImportEntityIdsSpecified:
                            secondSort = this.ThenBySortNonString(
                                firstSort,
                                isAsc,
                                (int accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EntityIdsSpecified));
                            break;
                        case GridColumn.ImportStartTime:
                            secondSort = this.ThenBySortNonString(
                                firstSort,
                                isAsc,
                                (int accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDateTime));
                            break;

                        // Video Metrics
                        case GridColumn.VideoViews:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.VideoViews.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.ViewThroughRate:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ViewThroughRate.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.TotalWatchTimeInMS:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.TotalWatchTimeInMS.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.AverageWatchTimePerImpression:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AverageWatchTimePerImpression.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.AverageWatchTimePerVideoView:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AverageWatchTimePerVideoView.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.Reach:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.Reach.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.VideoViewsAt25Percent:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.VideoViewsAt25Percent.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.VideoViewsAt50Percent:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.VideoViewsAt50Percent.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.VideoViewsAt75Percent:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.VideoViewsAt75Percent.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.CompletedVideoViews:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.CompletedVideoViews.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.VideoCompletionRate:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.VideoCompletionRate.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.AdvertiserReportedRevenueAdjustment:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.AdvertiserReportedRevenueAdjustment.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.AllConversionAdvertiserReportedRevenueAdjustment:
                            secondSort = this.SortThenByBIDataColumn( firstSort, (biData) => biData.AllConversionAdvertiserReportedRevenueAdjustment.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.ViewThroughConversionsRevenueAdjustment:
                            secondSort = this.SortThenByBIDataColumn( firstSort, (biData) => biData.ViewThroughConversionsRevenueAdjustment.GetValueOrDefault(), options, isAsc);
                            break;

                        //Conversion Delay Metrics
                        case GridColumn.ConversionDelayZeroDay:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ConversionDelayZeroDay.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.ConversionDelayNinety:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ConversionDelayNinety.GetValueOrDefault(), options, isAsc);
                            break;
                        case GridColumn.ConversionDelayNinetyNine:
                            secondSort = this.SortThenByBIDataColumn(firstSort, (biData) => biData.ConversionDelayNinetyNine.GetValueOrDefault(), options, isAsc);
                            break;

                        default:
                            string message = string.Format("Unexpected sort column: {0}", gridSort.SortByGridColumn);
                            this.logger.LogUserError(message);
                            throw new InvalidOperationException(message);
                    }

                    // Tie Breaker
                    if (isAsc)
                    {
                        resultKeys = secondSort.ThenBy(accountId => accountId);
                    }
                    else
                    {
                        resultKeys = secondSort.ThenByDescending(accountId => accountId);
                    }
                }
                else
                {
                    resultKeys = firstSort.ThenBy(accountId => accountId);
                }

                return resultKeys.ToList();
            }
        }

        public IOrderedPagedEnumerable<int> SortThenByBIDataColumn(
            IOrderedPagedEnumerable<int> keys,
            Func<BiData, long> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByLongBIDataColumn(
                keys,
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<int> SortThenByBIDataColumn(
            IOrderedPagedEnumerable<int> keys,
            Func<BiData, double> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByDoubleBIDataColumn(
                keys,
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<int> SortThenBySovColumn(
            IOrderedPagedEnumerable<int> keys,
            Func<IBiData, double?> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenBySoVBIDataColumn(
                keys,
                (int accountId) => this.GetSovMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetSovMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<int> SortThenByAudienceSovColumn(
            IOrderedPagedEnumerable<int> keys,
            Func<IBiData, double?> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByAudienceSoVBIDataColumn(
                keys,
                (int accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector),
                options,
                isAsc);
        }

        private Func<int, bool> CreateWherePredicate(GridFilterExpression expression)
        {
            Func<int, bool> result;

            PeriodComparisonOptions options = PeriodComparisonOptions.None;
            if (this.usePeriodComparisonBiData)
            {
                options = expression.PeriodComparisonFilterOptions;

                switch (options)
                {
                    case PeriodComparisonOptions.Change:
                    case PeriodComparisonOptions.ChangePercentage:
                    case PeriodComparisonOptions.Period:
                    case PeriodComparisonOptions.None:
                        break;

                    default:
                        throw new InvalidOperationException(string.Format("CreateWherePredicate - AccountsSummary - Unknown PeriodComparisonOptions value: {0}", options));
                }
            }

            if (expression.ColumnToFilter == GridColumn.Status)
            {
                this.logger.LogInfo("Accounts Summary Filter - unexpected use of GridColumn.Status instead of GridColumn.AccountStatus");
            }

            switch (expression.ColumnToFilter)
            {
                case GridColumn.AccountId:
                    result = PredicateHelper.PredicateByNumericTypes<int, int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Id));
                    break;
                case GridColumn.AccountName:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Name),
                        this.culture);
                    break;
                case GridColumn.AccountNumber:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountNumber),
                        this.culture);
                    break;

                case GridColumn.Status:
                case GridColumn.AccountStatus:
                    result = PredicateHelper.PredicateByEnum<int, AccountStatus>(expression, (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Status));
                    break;

                case GridColumn.Currency:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.CurrencyCode),
                        this.culture);
                    break;

                case GridColumn.TimeZone:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.TimeZone.ToString()),
                        this.culture);
                    break;

                case GridColumn.BillToCustomerName:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.BillToCustomerName),
                        this.culture);
                    break;

                case GridColumn.CompanyName:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.CustomerName),
                        this.culture);
                    break;

                case GridColumn.AccountMode:
                    result = PredicateHelper.PredicateByNumericTypes<int, byte>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (int accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountMode2?.GetByte() ?? (byte)0));
                    break;

                case GridColumn.AccountInactiveReasons:
                    result = PredicateHelper.PredicateByEnum<int, AccountInactiveReasonCode>(
                        this.GetAccountInactiveReasonCode,
                        expression);
                    break;

                // Insertion Order
                case GridColumn.IOPurchaseOrder:
                    result = PredicateHelper.PredicateByString<int>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.PurchaseOrder),
                        this.culture);
                    break;
                
                case GridColumn.IOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.Budget.GetValueOrDefault()));
                    break;
                case GridColumn.RemainingIOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.RemainingBudget.GetValueOrDefault()));
                    break;
                case GridColumn.TotalRemainingIOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.TotalRemainingBudget.GetValueOrDefault()));
                    break;
                case GridColumn.StartDate:
                    result = PredicateHelper.PredicateByNumericTypes<int, DateTime>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDate.GetValueOrDefault()));
                    break;
                case GridColumn.EndDate:
                    result = PredicateHelper.PredicateByNumericTypes<int, DateTime>(
                        expression,
                        (int accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EndDate.GetValueOrDefault()));
                    break;
                // budget
                case GridColumn.DailyBudget:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.DailyBudget.GetValueOrDefault()));
                    break;
                case GridColumn.MonthlyBudget:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.MonthlyBudget.GetValueOrDefault()));
                    break;
                case GridColumn.LifetimeBudgetAmount:
                    result = PredicateHelper.PredicateByNumericTypes<int, double>(
                        expression,
                        (int accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.LifetimeBudget.GetValueOrDefault()));
                    break;

                // BI Columns
                case GridColumn.CTR:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ClickThruRate.GetValueOrDefault());
                    break;

                case GridColumn.Clicks:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Clicks.GetValueOrDefault());
                    break;

                case GridColumn.Impressions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Impressions.GetValueOrDefault());
                    break;

                case GridColumn.TotalEffectiveCost: // "Spend"
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Spent.GetValueOrDefault());
                    break;

                case GridColumn.Conversions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Conversions.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPC:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPC.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPM:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPM.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPV:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPV.GetValueOrDefault());
                    break;

                case GridColumn.AveragePosition:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AveragePosition.GetValueOrDefault());
                    break;

                case GridColumn.CPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.CPA.GetValueOrDefault());
                    break;

                case GridColumn.RevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.RevenueOnAdSpend.GetValueOrDefault());
                    break;

                case GridColumn.ConversionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionRate.GetValueOrDefault());
                    break;

                case GridColumn.AdvertiserReportedRevenue:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AdvertiserReportedRevenue.GetValueOrDefault());
                    break;

                case GridColumn.TopImpressionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.TopImpressionRate.GetValueOrDefault());
                    break;

                case GridColumn.AbsoluteTopImpressionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AbsoluteTopImpressionRate.GetValueOrDefault());
                    break;
                // Video Metrics
                case GridColumn.VideoViews:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViews.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughRate.GetValueOrDefault());
                    break;
                case GridColumn.TotalWatchTimeInMS:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.TotalWatchTimeInMS.GetValueOrDefault());
                    break;
                case GridColumn.AverageWatchTimePerImpression:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageWatchTimePerImpression.GetValueOrDefault());
                    break;
                case GridColumn.AverageWatchTimePerVideoView:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageWatchTimePerVideoView.GetValueOrDefault());
                    break;
                case GridColumn.Reach:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Reach.GetValueOrDefault());
                    break;
                case GridColumn.VideoViewsAt25Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt25Percent.GetValueOrDefault());
                    break;
                case GridColumn.VideoViewsAt50Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt50Percent.GetValueOrDefault());
                    break;
                case GridColumn.VideoViewsAt75Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt75Percent.GetValueOrDefault());
                    break;
                case GridColumn.CompletedVideoViews:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.CompletedVideoViews.GetValueOrDefault());
                    break;
                case GridColumn.VideoCompletionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoCompletionRate.GetValueOrDefault());
                    break;

                // IS Columns - The selector must return the nullable value for the correct SoV participation filter logic to work.
                case GridColumn.AuctionWonPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionWonPercent);
                    break;

                case GridColumn.AuctionLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToBudgetPercent);
                    break;

                case GridColumn.AuctionLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToRankPercent);
                    break;

                case GridColumn.AuctionLostToLandingPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToLandingPercent);
                    break;

                case GridColumn.AuctionLostToAdQualityPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToAdQualityPercent);
                    break;

                case GridColumn.ExactMatchImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.ExactMatchImpressionSharePercent);
                    break;

                case GridColumn.AbsoluteTopImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopImpressionSharePercent);
                    break;

                case GridColumn.ClickSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.ClickSharePercent);
                    break;

                case GridColumn.AuctionLostToBidPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToBidPercent);
                    break;

                case GridColumn.TopImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopImpressionSharePercent);
                    break;

                case GridColumn.TopISLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopISLostToBudgetPercent);
                    break;

                case GridColumn.TopISLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopISLostToRankPercent);
                    break;

                case GridColumn.AbsoluteTopISLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopISLostToRankPercent);
                    break;

                case GridColumn.AbsoluteTopISLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopISLostToBudgetPercent);
                    break;

                case GridColumn.QualityScore:
                    result = PredicateHelper.PredicateByNumericTypes<int, short>(
                           expression,
                           (int accountId) => this.GetAccountQualityScoreMemberOrDefault(accountId, (qualityScore) => qualityScore.OverallQualityScore.GetValueOrDefault()));
                    break;

                // Audience IS Columns
                case GridColumn.AudienceAuctionWonPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceAuctionWonPercent);
                    break;

                case GridColumn.AudienceTopISLostToBudgetPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceTopISLostToBudgetPercent);
                    break;

                case GridColumn.AudienceTopISLostToRankPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceTopISLostToRankPercent);
                    break;

                case GridColumn.CustomColumn:
                    result = this.PredicateByCustomColumn(expression);
                    break;
                case GridColumn.AllConversions:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversions.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversions:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversions.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRevenue:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsRevenue.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsCPA.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsReturnOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsReturnOnAdSpend.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsRate.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionRevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionRevenueOnAdSpend.GetValueOrDefault());
                    break;

                case GridColumn.AllConversionRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionRate.GetValueOrDefault());
                    break;

                case GridColumn.AllConversionAdvertiserReportedRevenue: // "Revenue"
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.ConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.PartialConversionCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.PartialConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.PartialConversionRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.PartialConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.AllPartialConversionCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllPartialConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.AllPartialConversionRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllPartialConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughPartialConversionsCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughPartialConversionsCPA.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughPartialConversionsRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughPartialConversionsRate.GetValueOrDefault());
                    break;

                case GridColumn.ImportName:
                    result = PredicateHelper.PredicateByString<int>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (int accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (lastImportInfo) => lastImportInfo.ImportName),
                        this.culture);
                    break;
                case GridColumn.ImportEntityIdsSpecified:
                    result = PredicateHelper.PredicateByNullableBool<int>(
                        expression,
                        (int accountId) => this.GetLastImportInfoMemberOrDefault<bool?>(accountId, (lastImportInfo) => lastImportInfo.EntityIdsSpecified));
                    break;
                case GridColumn.ImportStartTime:
                    result = PredicateHelper.PredicateByNumericTypes<int, DateTime>(
                        expression,
                        (int accountId) => this.GetLastImportInfoMemberOrDefault<DateTime>(accountId, (lastImportInfo) => lastImportInfo.StartDateTime));
                    break;
                case GridColumn.Labels:
                    var filterOnLabels = expression.FilterOnValues.Select(PredicateHelper.ChangeType<Label>).ToList();
                    if (filterOnLabels.Any(l => l.Scope == EntityScope.Customer))
                    {
                        logger.LogInfo("Filtering by Customer level label: {0}", expression.FilterOperation);
                    }

                    result = PredicateHelper.PredicateByObjectCollection<int, Label>(
                        expression.FilterOperation,
                        filterOnLabels,
                        this.GetAssociatedLabels,
                        new Label.LabelEqualityComparer());
                    break;
                case GridColumn.AdvertiserReportedRevenueAdjustment:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AdvertiserReportedRevenueAdjustment.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRevenueAdjustment:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsRevenueAdjustment.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionAdvertiserReportedRevenueAdjustment:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.AllConversionAdvertiserReportedRevenueAdjustment.GetValueOrDefault());
                    break;
                case GridColumn.ConversionDelayZeroDay:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.ConversionDelayZeroDay.GetValueOrDefault());
                    break;
                case GridColumn.ConversionDelayNinety:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.ConversionDelayNinety.GetValueOrDefault());
                    break;
                case GridColumn.ConversionDelayNinetyNine:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.ConversionDelayNinetyNine.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerConversions:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerConversions.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerRevenue:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerRevenue.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerConversionRate:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerCPA:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerCPA.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerRevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerRevenueOnAdSpend.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerCount:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.NewCustomerCount.GetValueOrDefault());
                    break;
                case GridColumn.UnknownCustomerConversions:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.UnknownCustomerConversions.GetValueOrDefault());
                    break;
                case GridColumn.UnknownCustomerRevenue:
                    result = this.PredicateByBIDataColumn(expression,
                         (biData) => biData.UnknownCustomerRevenue.GetValueOrDefault());
                    break;

                default:
                    string message = string.Format("Unexpected filter column: {0}", expression.ColumnToFilter);
                    this.logger.LogUserError(message);
                    throw new InvalidOperationException(message);
            }

            return result;
        }

        private IReadOnlyCollection<Label> GetAssociatedLabels(int accountId)
        {
            if (this.accountSummaryCache.AssociatedLabelIdsByAccountIds != null &&
                this.accountSummaryCache.AssociatedLabelIdsByAccountIds.TryGetValue(accountId, out List<long> associatedLabelIds) && 
                associatedLabelIds != null)
            {
                return associatedLabelIds.Select(x =>
                    new Label
                    {
                        Id = x,
                        Scope = EntityScope.Customer
                    }).ToList();
            }

            return null;
        }

        private Func<int, bool> PredicateByCustomColumn(GridFilterExpression gfe)
        {
            return PredicateHelper.PredicateByBIDataColumn<int>(
                gfe,
                (int accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, gfe.CustomColumnId),
                (int accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, gfe.CustomColumnId)
            );
        }

        private Func<int, bool> PredicateByBIDataColumn(
            GridFilterExpression gfe,
            Func<BiData, long> biDataItemSelector)
        {
            return PredicateHelper.PredicateByBIDataColumn<int>(
                gfe,
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector));
        }

        private Func<int, bool> PredicateByBIDataColumn(
            GridFilterExpression gfe,
            Func<BiData, double> biDataItemSelector)
        {
            return PredicateHelper.PredicateByBIDataColumn<int>(
                gfe,
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetBIMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector));
        }

        private Func<int, bool> PredicateBySoVColumn(
            GridFilterExpression gfe,
            Func<IBiData, double?> biDataItemSelector)
        {
            return PredicateHelper.PredicateBySoVColumn<int>(
                gfe,
                (int accountId) => this.GetSovMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetSovMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector));
        }

        private Func<int, bool> PredicateByAudienceSoVColumn(
            GridFilterExpression gfe,
            Func<IBiData, double?> biDataItemSelector)
        {
            return PredicateHelper.PredicateByAudienceSoVColumn<int>(
                gfe,
                (int accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.accountSummaryCache.AccountBiByAccountId, biDataItemSelector),
                (int accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.accountSummaryCache.PeriodAccountBiByAccountId, biDataItemSelector));
        }

        private CustomColumnValue GetCustomColumnValue(string id, BiData biData)
        {
            return biData.CustomColumns.Find(column => column.Id == id).Value;
        }

        private MemberType GetAccountSummaryRowMemberOrDefault<MemberType>(int accountId, Func<AccountInfo2, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.accountSummaryCache.Accounts, memberFetcher);

            return result;
        }

        private T GetLastImportInfoMemberOrDefault<T>(
            int accountId,
            Func<LastImportInfo, T> memberFetcher)
        {
            T result = GetMemberFromDictionaryItemOrDefault(accountId, this.accountSummaryCache.LastImportInfoById, memberFetcher);
            return result;
        }

        private IEnumerable<AccountInactiveReasonCode> GetAccountInactiveReasonCode(int accountId)
        {
            IList<AccountInactiveReasonCode> inactiveReasonCodes = new List<AccountInactiveReasonCode>();
            IEnumerable<AccountInactiveReason> inactiveReasons = GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountInactiveReasons);

            foreach (var inactiveReason in inactiveReasons)
            {
                inactiveReasonCodes.Add(inactiveReason.Reason);
            }

            return inactiveReasonCodes;
        }

        private MemberType GetAccountQualityScoreMemberOrDefault<MemberType>(int accountId, Func<QualityScoreData, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.accountSummaryCache.AccountQualityScoreData, memberFetcher);

            return result;
        }

        private MemberType GetIOMemberOrDefault<MemberType>(int accountId, Func<InsertionOrderDetails, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.accountSummaryCache.InsertionOrderDetailsByAccountId, memberFetcher);

            return result;
        }

        private MemberType GetBudgetMemberOrDefault<MemberType>(int accountId, Func<AggregatedBudget, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.accountSummaryCache.AggregatedBudgetByAccountId, memberFetcher);

            return result;
        }

        private CustomColumnValue GetCustomColumnMemberOrDefault(int accountId, Dictionary<int, BiData> biDataDictionary, string customColumnId)
        {
            CustomColumnValue result = GetMemberFromDictionaryItemOrDefault(accountId, biDataDictionary, biData => GetCustomColumnValue(customColumnId, biData));

            if (result == null)
            {
                result = CustomColumnValue.Zero;
            }

            return result;
        }

        private MemberType GetBIMemberOrDefault<MemberType>(int accountId, Dictionary<int, BiData> biDataDictionary, Func<BiData, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, biDataDictionary, memberFetcher);
            return result;
        }

        private MemberType GetSovMemberOrDefault<MemberType>(int accountId, Dictionary<int, BiData> biDataDictionary, Func<IBiData, MemberType> memberFetcher)
        {
            MemberType result = default(MemberType);

            if (biDataDictionary != null && biDataDictionary.TryGetValue(accountId, out BiData biData))
            {
                result = BiData.GetSoVMemberOrDefault<MemberType>(biData, memberFetcher);
            }

            return result;
        }

        private MemberType GetAudienceSovMemberOrDefault<MemberType>(int accountId, Dictionary<int, BiData> biDataDictionary, Func<IBiData, MemberType> memberFetcher)
        {
            MemberType result = default(MemberType);

            if (biDataDictionary != null && biDataDictionary.TryGetValue(accountId, out BiData biData))
            {
                result = BiData.GetAudienceSoVMemberOrDefault<MemberType>(biData, memberFetcher);
            }

            return result;
        }

        private MemberType GetMemberFromDictionaryItemOrDefault<MemberType, ItemType>(int key, IDictionary<int, ItemType> dictionary, Func<ItemType, MemberType> memberFetcher)
        {
            MemberType result = default(MemberType);

            ItemType item;
            if (dictionary != null && dictionary.TryGetValue(key, out item))
            {
                result = memberFetcher(item);
            }

            return result;
        }

        private IOrderedPagedEnumerable<int> ThenBySortString(IOrderedPagedEnumerable<int> input, bool isAsc, Func<int, string> keySelector)
        {
            StringComparer comparer = StringComparer.Create(this.culture, true);
            return AggregatorEntityOperations.SortThenByStringHelper(input, keySelector, comparer, isAsc);
        }

        private IOrderedPagedEnumerable<int> ThenBySortNonString<ItemType>(IOrderedPagedEnumerable<int> input, bool isAsc, Func<int, ItemType> keySelector)
        {
            return AggregatorEntityOperations.SortThenByNonStringHelper(input, keySelector, isAsc);
        }
    }
}
