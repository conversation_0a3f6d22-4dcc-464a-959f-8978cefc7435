﻿#if V2
namespace Microsoft.Advertising.Advertiser.Api.V2
{
    using System;
    using System.Configuration;
    using System.Data;
    using System.Net;
    using System.Net.Http;
    using CampaignMiddleTierTest.Framework;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json;
    using CampaignMiddleTierTest.Framework.Utilities;
    using CSharp.RuntimeBinder;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.Data.SqlClient;
    using Newtonsoft.Json.Linq;
    using System.Linq;
    using System.Collections.Generic;
    using System.Collections;
    using VcClient;
    using Microsoft.BingAds.Utils;

    public class TestUETV2Operations
    {
        public static string UETV2BaseUrl = ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})";
        public static string UETV2BaseUrlForCustomerContext = ApiVersion.BaseUrl + "/Customers({0})";

        public static dynamic CreateTag(string name, string description, bool? isReadOnly = null)
        {
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.odatatype = "#Model.Tag";
            tag.Name = name;
            tag.Description = description;
            tag.IsReadOnly = isReadOnly ?? false;
            return tag;
        }

        public static dynamic CreateTagClarityTagEnabled(string name, string description)
        {
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.odatatype = "#Model.Tag";
            tag.Name = name;
            tag.Description = description;
            tag.IsClarityTagEnabled = 1;
            return tag;
        }

        public static dynamic CreateSmartGoal(long tagId, long accountId, bool isAccountLevel = true)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.SmartGoal";
            goal.Name = "Smart Goal " + accountId;
            goal.Type = "SmartGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 0;
            goal.IsAccountLevel = isAccountLevel;
            goal.ConversionCountType = "All";

            goal.ViewThroughLookbackWindowinMinutes = null;

            // revenue should be deleted after
            goal.GoalValueType = "NoValue";
            goal.GoalValue = null;
            goal.CurrencyCode = null;
            goal.GoalCategory = "Other";



            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            goal.IsMainConversionGoal = true;
            return goal;
        }

        public static dynamic ConvertSmartGoalToDynamic(Goal existinggoal)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.SmartGoal";
            goal.Id = existinggoal.Id;
            goal.Name = existinggoal.Name;
            goal.Type = existinggoal.Type.ToString();
            goal.Status = existinggoal.Status.ToString();
            goal.LookbackWindowInMinutes = (existinggoal.LookbackWindowDays ?? 0) * 24 * 60 + (existinggoal.LookbackWindowHours ?? 0) * 60 + (existinggoal.LookbackWindowMinutes ?? 0);
            goal.IsAccountLevel = existinggoal.IsAccountLevel;
            goal.ConversionCountType = existinggoal.ConversionCountType.ToString(); ;

            if (!existinggoal.ViewThroughLookbackWindowinDays.HasValue && !existinggoal.ViewThroughLookbackWindowinHours.HasValue && !existinggoal.ViewThroughLookbackWindowinMinutes.HasValue)
            {
                goal.ViewThroughLookbackWindowinMinutes = null;
            }
            else
            {
                goal.ViewThroughLookbackWindowinMinutes = (existinggoal.ViewThroughLookbackWindowinDays ?? 0) * 24 * 60 + (existinggoal.ViewThroughLookbackWindowinHours ?? 0) * 60 + (existinggoal.ViewThroughLookbackWindowinMinutes ?? 0);
            }

            goal.GoalValueType = existinggoal.Revenue.Type.Value.ToString();
            goal.GoalValue = existinggoal.Revenue.Value;
            goal.CurrencyCode = existinggoal.Revenue.CurrencyCode;
            goal.GoalCategory = existinggoal.GoalCategory.ToString();

            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = existinggoal.TagId;
            goal.Tag = tag;
            var IsMainConversionGoal = !existinggoal.ExcludeFromBidding;
            goal.IsMainConversionGoal = IsMainConversionGoal ?? true;
            return goal;
        }

        public static dynamic UpdateSmartGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Update Goal Request");
            }
            expectedHttpStatusCode = HttpStatusCode.BadRequest;
            var patchUrl = string.Format(UETV2BaseUrl + "/Goals({2})", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0], goal.Id);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var request = new HttpRequestMessage(new HttpMethod("PATCH"), patchUrl)
            {
                Content = new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")
            };
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.SendAsync(request),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic CreateEventGoal(long tagId, bool isAccountLevel = true, bool? isMainConversionGoalValue = null, 
            int? ViewThroughConversionWindowInMinutes = null, bool? isReadOnlyGoal = null, string GoalName = null, 
            bool? isAutoGoal = null, string goalSourceId = null, string goalCategory = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.EventGoal";
            goal.Name = (GoalName == null) ? ("TestGoal-" + StringUtil.GenerateUniqueId()) : GoalName;
            goal.Type = "EventGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 88;
            goal.IsAccountLevel = isAccountLevel;
            goal.ConversionCountType = "All";
            goal.GoalValue = 8;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = string.IsNullOrEmpty(goalCategory) ? "Other" : goalCategory;
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;

            //eventGoal specific properties
            if (isAutoGoal == null || !isAutoGoal.Value)
            {
                goal.Category = "Category";
                goal.CategoryOperator = "BeginsWith";
                goal.Action = "action";
                goal.ActionOperator = "EqualsTo";
                goal.Label = "label";
                goal.LabelOperator = "RegularExpression";
                goal.Value = 5.8;
                goal.ValueOperator = "GreaterThan";
            }

            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            goal.IsMainConversionGoal = isMainConversionGoalValue;
            goal.IsReadOnly = isReadOnlyGoal ?? false;

            if (isAutoGoal != null)
            {
                goal.IsAutoGoal = isAutoGoal;
            }

            if (goalSourceId != null) goal.GoalSourceId = goalSourceId;
            return goal;
        }

        public static dynamic CreateEventGoalForGoalCategoryForGoalTypeValidation(long tagId, bool isAccountLevel = true, bool? isMainConversionGoalValue = null, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.DurationGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "DurationGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            goal.GoalCategory = "Purchase"; //add a Goal Category which does not belong to Duration Goal.
            //durationGoal specific properties
            goal.Duration = 518;
            goal.Operator = "LessThan";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateEventGoalForGoalCategory(long tagId, bool isAccountLevel = true, bool? isMainConversionGoalValue = null, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.EventGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "EventGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 88;
            goal.IsAccountLevel = isAccountLevel;
            goal.ConversionCountType = "All";
            goal.GoalValue = 8;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;

            //eventGoal specific properties
            goal.Category = "Category";
            goal.CategoryOperator = "BeginsWith";
            goal.Action = "action";
            goal.ActionOperator = "EqualsTo";
            goal.Label = "label";
            goal.LabelOperator = "RegularExpression";
            goal.Value = 5.8;
            goal.ValueOperator = "GreaterThan";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            goal.IsMainConversionGoal = isMainConversionGoalValue;
            goal.GoalCategory = "Purchase"; //add goalcategory column
            return goal;
        }

        public static dynamic CreateEventGoalForGoalCategoryDisabledViewThroughConversion(long tagId, bool isAccountLevel = true, bool? isMainConversionGoalValue = null, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.EventGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "EventGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 88;
            goal.IsAccountLevel = isAccountLevel;
            goal.ConversionCountType = "All";
            goal.GoalValue = 8;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.ViewThroughLookbackWindowinMinutes = null;

            //eventGoal specific properties
            goal.Category = "Category";
            goal.CategoryOperator = "BeginsWith";
            goal.Action = "action";
            goal.ActionOperator = "EqualsTo";
            goal.Label = "label";
            goal.LabelOperator = "RegularExpression";
            goal.Value = 5.8;
            goal.ValueOperator = "GreaterThan";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            goal.IsMainConversionGoal = isMainConversionGoalValue;
            goal.GoalCategory = "Purchase"; //add goalcategory column
            return goal;
        }

        public static dynamic CreatePartialEventGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.EventGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "EventGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 88;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 8;
            goal.GoalValueType = "FixedValue";
            //eventGoal specific properties
            goal.Category = "category";
            goal.CategoryOperator = "BeginsWith";
            goal.GoalCategory = "Other";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateAppInstallGoal(int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.ApplicationInstallGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Status = "Active";
            goal.Type = "ApplicationInstallGoal";
            goal.ConversionCountType = "All";
            goal.GoalValue = 344;
            goal.GoalValueType = "FixedValue";
            goal.IsAccountLevel = false;
            goal.LookbackWindowInMinutes = 4565;
            goal.GoalCategory = "Download";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes;
            //appInstallGoal specific properties
            goal.ApplicationPlatform = "Android";
            goal.ApplicationStoreId = "test.package";
            return goal;
        }

        public static dynamic CreateDestinationGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null, AttributionModelType? attributionModelType = null, string category = "Other")
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.DestinationGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "DestinationGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            goal.GoalCategory = category;
            //destinationGoal specific properties
            goal.UrlString = "testUrl";
            goal.Operator = "EqualsTo";
            if (attributionModelType.HasValue)
            {
                goal.AttributionModelType = attributionModelType.ToString();
            }
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateDurationGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.DurationGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "DurationGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.GoalCategory = "Other";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            //durationGoal specific properties
            goal.Duration = 518;
            goal.Operator = "LessThan";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateProductConversionGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.ProductConversionGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "ProductConversionGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = "Purchase";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreatePageViewsPerVisitGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.PageViewsPerVisitGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "PageViewsPerVisitGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.GoalCategory = "Other";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes ?? 1440;
            //durationGoal specific properties
            goal.PageViews = 10;
            goal.Operator = "GreaterThan";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;
            return goal;
        }

        public static dynamic CreateOfflineGoal(string goalName = null, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.OfflineConversionGoal";
            goal.Name = goalName ?? "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "OfflineConversionGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = "Other";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes;

            return goal;
        }

        public static dynamic CreateInStoreTransactionGoal(int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.InStoreTransactionGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "InStoreTransactionGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1501;
            goal.IsAccountLevel = false;
            goal.ConversionCountType = "All";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = "Purchase";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes;

            return goal;
        }

        public static dynamic CreateInStoreVisitGoal(int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.InStoreVisitGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "InStoreVisitGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 1440;
            goal.IsAccountLevel = false;
            goal.ConversionCountType = "Unique";
            goal.GoalValue = 44;
            goal.GoalValueType = "FixedValue";
            goal.CurrencyCode = "USD";
            goal.GoalCategory = "InStoreVisit";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes;

            return goal;
        }

        public static dynamic CreateMultiStageGoal(long tagId, int? ViewThroughConversionWindowInMinutes = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.MultiStageGoal";
            goal.Name = "TestGoal-" + StringUtil.GenerateUniqueId();
            goal.Type = "MultiStageGoal";
            goal.Status = "Active";
            goal.LookbackWindowInMinutes = 88;
            goal.IsAccountLevel = true;
            goal.ConversionCountType = "All";
            goal.GoalValue = 8;
            goal.GoalValueType = "FixedValue";
            goal.GoalCategory = "Download";
            goal.ViewThroughLookbackWindowinMinutes = ViewThroughConversionWindowInMinutes;

            //Multi-stage goal specific properties
            dynamic stage1 = CreateStage(1);
            dynamic stage2 = CreateStage(2);
            dynamic stage3 = CreateStage(3);
            dynamic stage4 = CreateStage(4);
            dynamic stage5 = CreateStage(5);
            dynamic stage6 = CreateStage(6);

            goal.Stages = new[] {stage1, stage2, stage3, stage4, stage5, stage6};

            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = tagId;
            goal.Tag = tag;

            return goal;
        }

        public static dynamic UpdateGoalWithNullProperties(dynamic originalGoal, string odatatype = null, string status = null, string action = null, string actionOperator = null,
           string category = null, string categoryOperator = null, string label = null, string labelOperator = null, int? value = null, string valueOperator = null, string urlString = null,
           int? duration = null, string expressionOperator = null, int? pageViews = null, string type = null, int? lookbackWindowInMinutes = null, bool? isAccountLevel = null,
           string GoalValueType = null, string currencyCode = null, string platform = null, string appStoreId = null, decimal? goalValue = null)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.Id = originalGoal.Id;
            goal.odatatype = odatatype ?? originalGoal.odatatype;
            goal.Status = status;
            goal.IsAccountLevel = isAccountLevel;
            goal.Type = type ?? originalGoal.Type;
            goal.LookbackWindowInMinutes = lookbackWindowInMinutes;
            goal.GoalValueType = GoalValueType;
            goal.CurrencyCode = currencyCode;
            goal.GoalValue = goalValue;
            // specific properties
            switch (goal.odatatype)
            {
                case "#Model.PageViewsPerVisitGoal":
                    goal.PageViews = pageViews;
                    goal.Operator = valueOperator;
                    break;
                case "#Model.DurationGoal":
                    goal.Operator = valueOperator;
                    goal.Duration = duration;
                    break;
                case "#Model.DestinationGoal":
                    goal.UrlString = urlString;
                    goal.Operator = expressionOperator;
                    break;
                case "#Model.EventGoal":
                    goal.Category = category;
                    goal.CategoryOperator = categoryOperator;
                    goal.Action = action;
                    goal.ActionOperator = actionOperator;
                    goal.Label = label;
                    goal.LabelOperator = labelOperator;
                    goal.Value = value;
                    goal.ValueOperator = valueOperator;
                    break;
                case "#Model.ApplicationInstallGoal":
                    goal.ApplicationPlatform = platform;
                    goal.ApplicationStoreId = appStoreId;
                    break;
                default:
                    break;
            }
            return goal;
        }

        public static dynamic CreateGoals(
            CustomerInfo cInfo,
            dynamic goals,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null,
            int? accountId = null,
            int? customerId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "CreateGoals request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/goals/Default.AddGoals()", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goals);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            jsonStr = "{\"Goals\":" + jsonStr + "}";

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic GetGoalRecommendation(CustomerInfo cInfo, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, int? accountId = null,
           int? customerId = null)
        {
            var getRecommendationUrl = string.Format(UETV2BaseUrl + "/Goals/Default.GetGoalRecommendation", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getRecommendationUrl), e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Get goal recommendation"));
        }

        public static dynamic UpdateGoals(
            CustomerInfo cInfo,
            dynamic goals,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null,
            int? accountId = null,
            int? customerId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "UpdateGoals request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/goals/Default.UpdateGoals()", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goals);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            jsonStr = "{\"Goals\":" + jsonStr + "}";

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic CreateStage(int idx)
        {
            dynamic stage = new System.Dynamic.ExpandoObject();

            if (idx%2 == 1)
            {
                stage.odatatype = "#Model.EventGoalStage";
                stage.Type = "EventGoal";
                stage.Category = "category" + idx;
                stage.CategoryOperator = "BeginsWith";
                stage.Action = "action" + idx;
                stage.ActionOperator = "EqualsTo";
                stage.Label = "label" + idx;
                stage.LabelOperator = "RegularExpression";
                stage.Value = 5.8;
                stage.ValueOperator = "GreaterThan";
            }
            else
            {
                stage.odatatype = "#Model.DestinationGoalStage";
                stage.Type = "DestinationGoal";
                stage.UrlString = "testUrl" + idx;
                stage.Operator = "EqualsTo";
            }

            return stage;
        }

        public static dynamic PostTag(
            CustomerInfo cInfo, 
            dynamic tag, 
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, 
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, 
            int? accountId = null,
            int? customerId = null,
            string urlSuffix = null,
            bool isCustomerContext = false)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Tag request");
            }

            var postUrl = isCustomerContext ? string.Format(UETV2BaseUrlForCustomerContext + "/Tags", customerId ?? cInfo.CustomerId)
                : string.Format(UETV2BaseUrl + "/Tags", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            if (!string.IsNullOrEmpty(urlSuffix))
            {
                postUrl += urlSuffix;
            }
            string jsonStr = JsonConvert.SerializeObject(tag);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
                Assert.IsNotNull(result.Id);
                tag.Id = result.Id.Value;
                tag.TrackingCode = result.TrackingCode.Value;
                tag.TrackingStatus = result.TrackingStatus.Value;
                tag.Status = result.Status.Value;
                tag.GoalsCount = result.GoalsCount.Value;
                tag.AudiencesCount = result.AudiencesCount.Value;
            }
            return result;
        }

        public static dynamic UpdateTagsWebInsightsSetting(
            CustomerInfo cInfo, 
            dynamic tags, 
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, 
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, 
            int? accountId = null,
            int? customerId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "UpdateTagsWebInsightsSetting request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/Tags/Default.UpdateTagsWebInsightsSetting()", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(tags);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            jsonStr = "{\"Tags\":" + jsonStr + "}";

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic UpdateTagsWebInsightsSettingCustomerLevel(
            CustomerInfo cInfo,
            dynamic tags,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null,
            int? customerId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "UpdateTagsWebInsightsSetting request");
            }

            var postUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags/Default.UpdateTagsWebInsightsSetting()", customerId ?? cInfo.CustomerId);
            string jsonStr = JsonConvert.SerializeObject(tags);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            jsonStr = "{\"Tags\":" + jsonStr + "}";

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic GetTilesData(
            CustomerInfo cInfo, 
            string tagId, 
            string date,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, 
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, 
            int? accountId = null,
            int? customerId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "GetTilesData request");
            }

            var postUrl = string.Format(UETV2BaseUrl
                + "/Tags({2})/Default.GetWebInsights()?" +
                "$select=TotalSessionCounts,QuickBacks,Devices,Countries,PopularPages,AverageActiveTime",
                customerId ?? cInfo.CustomerId,
                accountId ?? cInfo.AccountIds[0],
                tagId);
            string jsonStr = @"{
                ""StartDate"": ""2023-07-06"",
                ""EndDate"": ""2023-07-06"",
                ""CountryFilter"": ""China,India"",
                ""DeviceFilter"": ""Mobile,Tablet"",
                ""PageUrlFilters"": [
                    {
                        ""Operator"": ""StartsWith"",
                        ""Value"": ""bing""
                    },
                    {
                        ""Operator"": ""Equals"",
                        ""Value"": ""google""
                    }
                ],
                ""TimeZoneId"": 50
            }";
            jsonStr = jsonStr.Replace("2023-07-06", date);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic GetTilesDataCustomerLevel(
            CustomerInfo cInfo,
            string tagId,
            string date,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "GetTilesData request");
            }

            var postUrl = string.Format(UETV2BaseUrlForCustomerContext
                + "/Tags({1})/Default.GetWebInsights()?" +
                "$select=TotalSessionCounts,QuickBacks,Devices,Countries,PopularPages,AverageActiveTime",
                cInfo.CustomerId,
                tagId);
            string jsonStr = @"{
                ""StartDate"": ""2023-07-06"",
                ""EndDate"": ""2023-07-06"",
                ""CountryFilter"": ""China,India"",
                ""DeviceFilter"": ""Mobile,Tablet"",
                ""PageUrlFilters"": [
                    {
                        ""Operator"": ""StartsWith"",
                        ""Value"": ""bing""
                    },
                    {
                        ""Operator"": ""Equals"",
                        ""Value"": ""google""
                    }
                ],
                ""TimeZoneId"": 50
            }";
            jsonStr = jsonStr.Replace("2023-07-06", date);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic DeleteClarityProject(CustomerInfo cInfo, dynamic tag, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Tag request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/Tags/Default.DeleteClarityProjectForSI", cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(tag);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
                Assert.IsNotNull(result.Id);
                tag.Id = result.Id.Value;
            }
            return result;
        }

        public static dynamic GetDomainTag(CustomerInfo cInfo, string url, dynamic domainTagReq, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null)
        {

            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Get Domain ");
            }

            var postUrl = string.Format(
                UETV2BaseUrl + url,
                cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            string jsonStr = JsonConvert.SerializeObject(domainTagReq);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic WriteDomainTag(CustomerInfo cInfo, string url, dynamic domainTagReq, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null)
        {

            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, url);
            }

            var postUrl = string.Format(
                UETV2BaseUrl + url,
                cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            string jsonStr = JsonConvert.SerializeObject(domainTagReq);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic GetTagDashboard(CustomerInfo cInfo, string url, dynamic TagStatisticReq, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null)
        {

            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Get Tag Dashboard");
            }

            var postUrl = string.Format(
                UETV2BaseUrl + url,
                cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            string jsonStr = JsonConvert.SerializeObject(TagStatisticReq);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic UetEventTestPage(CustomerInfo cInfo, dynamic deleteTagsReq, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "UetEventTestPage");
            }

            var postUrl = string.Format(
                UETV2BaseUrl + "/Tags/Default.UetEventTestPage",
                cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            string jsonStr = JsonConvert.SerializeObject(deleteTagsReq);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic DeleteTags(CustomerInfo cInfo, dynamic deleteTagsReq, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, int? accountId = null, bool isCustomerContext = false)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "DeleteTags");
            }

            var postUrl = isCustomerContext ?  string.Format(UETV2BaseUrlForCustomerContext + "/Tags/Default.DeleteTags", cInfo.CustomerId)
                : string.Format(UETV2BaseUrl + "/Tags/Default.DeleteTags", cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            string jsonStr = JsonConvert.SerializeObject(deleteTagsReq);

            var result = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));

            if (expectedHttpStatusCode == HttpStatusCode.OK)
            {
                Assert.IsNotNull(result);
            }
            return result;
        }

        public static dynamic PostGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/Goals", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic PostODataSmartGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(r.StatusCode, r.StatusCode, "Post Goal request");
            }
            var postUrl = string.Format(UETV2BaseUrl + "/Goals", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic PostSmartGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal request");
            }
            expectedHttpStatusCode = HttpStatusCode.BadRequest;

            var postUrl = string.Format(UETV2BaseUrl + "/Goals", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic SetClarityEventInfo(CustomerInfo cInfo, long goalId, dynamic clarityEventInfo, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/Goals({2})/Default.SetClarityEventInfo", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0], goalId);
            string jsonStr = JsonConvert.SerializeObject(clarityEventInfo);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic GetClarityEventInfo(CustomerInfo cInfo, long goalId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal request");
            }

            var postUrl = string.Format(UETV2BaseUrl + "/Goals({2})/Default.GetClarityEventInfo", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0], goalId);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.GetAsync(postUrl),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic PostGoalForGoalCategoryForGoalTypeValidation(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Post Goal with incorrect category for goal type request");
            }

            expectedHttpStatusCode = HttpStatusCode.BadRequest;

            var postUrl = string.Format(UETV2BaseUrl + "/Goals", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0]);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.PostAsync(postUrl, new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }


        public static dynamic GetGoalByGoalId(CustomerInfo cInfo, long goalId)
        {
            var getTagsUrl = string.Format(UETV2BaseUrl + "/Goals({2})?$expand=Tag($select=Id)", cInfo.CustomerId, cInfo.AccountIds[0], goalId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goal by Goal Id request"));
        }

        public static dynamic GetGoalsWithBiData(CustomerInfo cInfo, string filter = null, string orderBy = null, int start = -7, int end = 2)
        {
            string query = string.Format("/Goals?startdate={0}&enddate={1}", DateTime.Now.AddDays(start).ToString("o").Replace("+", "%2B"), DateTime.Now.AddDays(end).ToString("o").Replace("+", "%2B"));

            if (orderBy != null)
            {
                query = string.Format(query + "&$orderby={0}", orderBy);
            }

            if (filter != null)
            {
                query = string.Format(query + "&$filter={0}", filter);
            }

            var url = string.Format(UETV2BaseUrl + query, cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(url), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request"));
        }

        public static dynamic GetConversionGoalsByAccountId(CustomerInfo cInfo)
        {
            var getTagsUrl = string.Format(UETV2BaseUrl + "/Goals/Default.GetConversionGoals", cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Conversion Goal by Account Id request"));
        }

        public static dynamic GetGoals(CustomerInfo cInfo)
        {
            var getTagsUrl = string.Format(UETV2BaseUrl + "/Goals?$expand=Tag($select=Id)&StartDate=08/01/2015&EndDate=08/15/2015&$count=true", cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request"));
        }

        public static dynamic GetGoalsFilterByTagId(CustomerInfo cInfo, long tagId)
        {
            var getTagsUrl = string.Format(UETV2BaseUrl + "/Goals?$expand=Tag($select=Id)&StartDate=08/01/2015&EndDate=08/15/2015&$top=50&$filter=Tag%2FId+eq+"+tagId+"&$count=true", cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request"));
        }

        public static dynamic GetGoalsFilterByType(CustomerInfo cInfo, GoalEntityType type)
        {
            var getGoalsUrl = string.Format(UETV2BaseUrl + "/Goals?$expand=Tag($select=Id)&StartDate=08/01/2015&EndDate=08/15/2015&$top=50&$filter=Type+eq+Enum.GoalEntityType'" + type + "'&$count=true", cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getGoalsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request"));
        }

        public static dynamic GetAppInstallGoalsFilterByAppStore(
            CustomerInfo cInfo, 
            string applicationStoreId, 
            string applicationPlatform)
        {
            var typeValue = GoalEntityType.ApplicationInstallGoal.ToString();
            var statusValue = GoalStatus.Active.ToString();

            var getGoalsUrl = string.Format(
                UETV2BaseUrl + "/Goals?$expand=Tag($select=Id)&StartDate=08/01/2015&EndDate=08/15/2015&$top=50" +
                "&$filter=Type eq Enum.GoalEntityType'" + typeValue + "'" +
                " and Model.ApplicationInstallGoal/ApplicationStoreId eq '" + applicationStoreId + "'" +
                " and Model.ApplicationInstallGoal/ApplicationPlatform eq '" + applicationPlatform + "'" +
                " and Status eq Enum.GoalStatus'" + statusValue + "'" +
                "&$count=true",
                cInfo.CustomerId,
                cInfo.AccountIds[0]
            );

            return ApiHelper.CallApi(
                cInfo,
                c => c.GetAsync(getGoalsUrl),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request")
            );
        }

        public static dynamic GetGoalsFilterByAutoGoal(CustomerInfo cInfo)
        {
            var getGoalsUrl = string.Format(UETV2BaseUrl + "/Goals?&StartDate=08/01/2015&EndDate=08/15/2015&$top=50&$filter=IsAutoGoal+eq+true&$count=true", cInfo.CustomerId, cInfo.AccountIds[0]);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getGoalsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Goals request"));
        }

        public static dynamic GetTagByTagId(CustomerInfo cInfo, long tagId, long? customerId = null, string urlSuffix = null, bool isCustomerContext = false)
        {
            var getTagsUrl = isCustomerContext ? string.Format(UETV2BaseUrlForCustomerContext + "/Tags({1})", customerId ?? cInfo.CustomerId, tagId)
                : string.Format(UETV2BaseUrl + "/Tags({2})", customerId ?? cInfo.CustomerId, cInfo.AccountIds[0], tagId);
            if (!string.IsNullOrEmpty(urlSuffix))
            {
                getTagsUrl += urlSuffix;
            }
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Tag by Tag Id"));
        }

        public static dynamic WebInsights(CustomerInfo cInfo, long tagId, HttpStatusCode httpStatusCode)
        {
            var webInsightsUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags({1})/Default.GetWebInsights()", cInfo.CustomerId, tagId);
            return ApiHelper.CallApi(cInfo, c => c.PostAsync(webInsightsUrl, new StringContent("", System.Text.Encoding.UTF8, "application/json")), 
                e => Assert.AreEqual(httpStatusCode, e.StatusCode, "Get Web Insights"));
        }

        public static dynamic UpdateTagsWebInsightsSetting(CustomerInfo cInfo, HttpStatusCode httpStatusCode)
        {
            var updateTagsWebInsightsSettingUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags/Default.UpdateTagsWebInsightsSetting()", cInfo.CustomerId);
            return ApiHelper.CallApi(cInfo, c => c.PostAsync(updateTagsWebInsightsSettingUrl, new StringContent("", System.Text.Encoding.UTF8, "application/json")), 
                e => Assert.AreEqual(httpStatusCode, e.StatusCode, "Update Tags Web Insights Setting"));
        }

        public static dynamic GetTagWordPressPluginFileUrl(CustomerInfo cInfo, long tagId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getTagWordPressPluginFileUrl = string.Format(UETV2BaseUrl + "/Tags({2})/Default.GetTagPuglinBlobUrl?", cInfo.CustomerId, cInfo.AccountIds[0], tagId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagWordPressPluginFileUrl), e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Get Tag Word Press Plugin File Url by Tag Id"));
        }

        public static dynamic GetTagWordPressPluginFileUrlForCustomerContext(CustomerInfo cInfo, long tagId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getTagWordPressPluginFileUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags({1})/Default.GetTagPuglinBlobUrl?", cInfo.CustomerId, tagId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagWordPressPluginFileUrl), e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Get Tag Word Press Plugin File Url by Tag Id"));
        }

        public static dynamic GetTagWooCommercePluginFileUrl(CustomerInfo cInfo, long tagId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getTagWooCommercePluginFileUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags({2})/Default.GetWooCommerceBlobUrl?", cInfo.CustomerId, cInfo.AccountIds[0], tagId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagWooCommercePluginFileUrl), e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Get Tag WooCommerce Plugin File Url by Tag Id"));
        }

        public static dynamic GetTagWooCommercePluginFileUrlForCustomerContext(CustomerInfo cInfo, long tagId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getTagWooCommercePluginFileUrl = string.Format(UETV2BaseUrlForCustomerContext + "/Tags({1})/Default.GetWooCommerceBlobUrl?", cInfo.CustomerId, tagId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagWooCommercePluginFileUrl), e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Get Tag WooCommerce Plugin File Url by Tag Id"));
        }

        public static dynamic GetTags(CustomerInfo cInfo, int? accountId = null, string urlSuffix = null, bool isCustomerContext = false)
        {
            var getTagsUrl = isCustomerContext ? string.Format(UETV2BaseUrlForCustomerContext + "/Tags?$count=true", cInfo.CustomerId)
                : string.Format(UETV2BaseUrl + "/Tags?$count=true", cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            if (!string.IsNullOrEmpty(urlSuffix))
            {
                getTagsUrl += urlSuffix;
            }
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Tags request"));
        }

        public static dynamic GetTagsFilterByName(CustomerInfo cInfo, string tagName)
        {
            var getTagsUrl = string.Format(UETV2BaseUrl + "/Tags?$top=20&$count=true&$filter=Name+eq+'{2}'", cInfo.CustomerId, cInfo.AccountIds[0], tagName);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getTagsUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Tags request"));
        }

        public static dynamic UpdateTag(CustomerInfo cInfo, dynamic tag, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
            Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, string urlSuffix = null, bool isCustomerContext = false)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Update Tag request");
            }

            var patchUrl = isCustomerContext ? string.Format(UETV2BaseUrlForCustomerContext + "/Tags({1})", cInfo.CustomerId, tag.Id)
                : string.Format(UETV2BaseUrl + "/Tags({2})", cInfo.CustomerId, cInfo.AccountIds[0], tag.Id);
            if (!string.IsNullOrEmpty(urlSuffix))
            {
                patchUrl += urlSuffix;
            }
            string jsonStr = JsonConvert.SerializeObject(tag);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var request = new HttpRequestMessage(new HttpMethod("PATCH"), patchUrl)
            {
                Content = new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")
            };
            var result = ApiHelper.CallApi(
                cInfo,
                c => c.SendAsync(request),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static dynamic UpdateGoal(CustomerInfo cInfo, dynamic goal, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, Action<HttpResponseMessage, HttpStatusCode> responceValidation = null, long accountId = -1, CustomerInfo user = null)
        {
            if (responceValidation == null)
            {
                responceValidation = (r, e) => Assert.AreEqual(e, r.StatusCode, "Update Goal Request");
            }

            var patchUrl = string.Format(UETV2BaseUrl + "/Goals({2})", cInfo.CustomerId, accountId > 0 ? accountId : cInfo.AccountIds[0], goal.Id);
            string jsonStr = JsonConvert.SerializeObject(goal);
            jsonStr = jsonStr.Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var request = new HttpRequestMessage(new HttpMethod("PATCH"), patchUrl)
            {
                Content = new StringContent(jsonStr, System.Text.Encoding.UTF8, "application/json")
            };
            var result = ApiHelper.CallApi(
                user ?? cInfo,
                c => c.SendAsync(request),
                response => responceValidation(response, expectedHttpStatusCode));
            return result;
        }

        public static void AssertWebInsightsNonNull(dynamic resultForGetTiles)
        {
            Assert.IsNotNull(resultForGetTiles);
            Assert.IsNotNull(resultForGetTiles["AverageActiveTimeResult"].ToString());
            Assert.IsNotNull(resultForGetTiles["TotalSessionCountsResult"].ToString());
            Assert.IsNotNull(resultForGetTiles["QuickBackResult"].ToString());
            Assert.IsNotNull(resultForGetTiles["PopularPagesResult"][0]["PopularPageURL"].ToString());
            Assert.IsNotNull(resultForGetTiles["PopularPagesResult"][0]["VisitorCount"].ToString());
            Assert.IsNotNull(resultForGetTiles["PopularPagesResult"][1]["PopularPageURL"].ToString());
            Assert.IsNotNull(resultForGetTiles["PopularPagesResult"][1]["VisitorCount"].ToString());
            Assert.IsNotNull(resultForGetTiles["DevicesResult"][0]["DeviceTypeName"].ToString());
            Assert.IsNotNull(resultForGetTiles["DevicesResult"][0]["VisitorCount"].ToString());
            Assert.IsNotNull(resultForGetTiles["DevicesResult"][1]["DeviceTypeName"].ToString());
            Assert.IsNotNull(resultForGetTiles["DevicesResult"][1]["VisitorCount"].ToString());
            Assert.IsNotNull(resultForGetTiles["CountriesResult"][0]["CountryName"].ToString());
            Assert.IsNotNull(resultForGetTiles["CountriesResult"][0]["VisitorCount"].ToString());
            Assert.IsNotNull(resultForGetTiles["CountriesResult"][1]["CountryName"].ToString());
            Assert.IsNotNull(resultForGetTiles["CountriesResult"][1]["VisitorCount"].ToString());
        }

        public static void AssertTagEqual(dynamic expected, dynamic actual)
        {
            if (expected == null && actual == null)
            {
                return;
            }

            if (expected == null || actual == null)
            {
                Assert.Fail("Tag: null v.s. non null");
            }

            AssertStringValueEqual(expected.Name, actual.Name, "Tag Name");
            AssertStringValueEqual(expected.Description, actual.Description, "Tag Description");
            AssertStringValueEqual(expected.Id, actual.Id, "Tag Id");
            AssertStringValueEqual(expected.TrackingCode, actual.TrackingCode, "Tag TrackingCode");
            AssertStringValueEqual(expected.TrackingStatus, actual.TrackingStatus, "Tag TrackingStatus");
            AssertStringValueEqual(expected.GoalsCount, actual.GoalsCount, "Tag GoalsCount");
            AssertStringValueEqual(expected.AudiencesCount, actual.AudiencesCount, "Tag AudiencesCount");
        }

        public static void AssertGoalEqual(dynamic expected, dynamic actual, bool compareCurrencyCode = false, bool isVerifyAttributionModel = false, bool isVerifyEventTool = false, bool isVerifiedAutoGoal = false)
        {
            if (expected == null && actual == null)
            {
                return;
            }

            if (expected == null || actual == null)
            {
                Assert.Fail("Goal: null v.s. non null");
            }
            AssertBaseGoalEqual(expected, actual, compareCurrencyCode, isVerifyAttributionModel, isVerifyAutoGoal: isVerifiedAutoGoal);
        }

        public static bool EnableMigrationTestForSI()
        {
            return !ConfigurationManager.AppSettings["AdCenterEnvironment"].ToUpper().Contains("SI-") ||
                   ConfigurationManager.AppSettings["EnableUETMigrationTestsForSI"].ToUpper().Equals("TRUE");
        }

        private static void AssertBaseGoalEqual(dynamic expected, dynamic actual, bool compareCurrencyCode = false, bool isVerifyAttributionModel = false, bool isVerifyEventTool = false, bool isVerifyAutoGoal = false)
        {
            AssertStringValueEqual(expected.Id, actual.Id, "Goal Id");
            AssertStringValueEqual(expected.Name, actual.Name, "Goal Name");
            AssertStringValueEqual(expected.Type, actual.Type, "Goal Type");
            AssertStringValueEqual(expected.Status, actual.Status, "Goal Status");
            AssertStringValueEqual(expected.LookbackWindowInMinutes, actual.LookbackWindowInMinutes, "Goal LookbackWindowInMinutes");
            AssertStringValueEqual(expected.IsAccountLevel, actual.IsAccountLevel, "Goal IsAccountLevel");
            AssertStringValueEqual(expected.ConversionCountType, actual.ConversionCountType, "Goal ConversionCountType");
            AssertStringValueEqual(expected.GoalValue, actual.GoalValue, "Goal GoalValue");
            AssertStringValueEqual(expected.GoalValueType, actual.GoalValueType, "Goal GoalValueType");
            AssertStringValueEqual(expected.ViewThroughLookbackWindowinMinutes, actual.ViewThroughLookbackWindowinMinutes, "Goal ViewThroughLookbackWindowinMinutes");
            AssertStringValueEqual(expected.GoalCategory, actual.GoalCategory, "Goal GoalCategory");
            AssertCollectionsEqual(GetOptionalProperty(expected, "ServableExternalChannels"), actual.ServableExternalChannels, "Goal.ServableExternalChannels");

            if (isVerifyAutoGoal)
            {
                AssertStringValueEqual(expected.GoalSourceId, actual.GoalSourceId, "Goal Source Id");
                AssertStringValueEqual(expected.IsAutoGoal, actual.IsAutoGoal, "Goal IsAutoGoal");
            }

            if (isVerifyAttributionModel)
            {
                AssertStringValueEqual(expected.AttributionModelType, actual.AttributionModelType, "Goal AttributionModelType doesn't match");
            }
            
            bool? isMainConversionGoal = null;
            try
            {
                isMainConversionGoal = expected.IsMainConversionGoal ?? (expected.Type.ToString().Equals("InStoreVisitGoal") ? false : true);

            }
            catch (RuntimeBinderException)
            {
                isMainConversionGoal = null;
            }
            AssertStringValueEqual(isMainConversionGoal ?? (expected.Type.ToString().Equals("InStoreVisitGoal")? false : true), actual.IsMainConversionGoal,
                "Goal IsMainConversionGoal");

            if (!expected.Type.ToString().Equals("ApplicationInstallGoal") &&
                !expected.Type.ToString().Equals("OfflineConversionGoal") &&
                !expected.Type.ToString().Equals("InStoreTransactionGoal") &&
                !expected.Type.ToString().Equals("InStoreVisitGoal"))
            {
                AssertStringValueEqual(expected.Tag.Id, actual.Tag.Id, "Tag Id");
            }

            if (compareCurrencyCode)
            {
                AssertStringValueEqual(expected.CurrencyCode, actual.CurrencyCode, "Goal CurrencyCode");
            }

            if (expected.Type.ToString() == "EventGoal")
            {
                bool verifiedAutoGoal = isVerifyAutoGoal && actual.ToString().Contains("IsAutoGoal") && (actual.IsAutoGoal.ToString() == "True");
                AssertEventGoalEqual(expected, actual, verifiedAutoGoal);
            }

            if (expected.Type.ToString() == "MultiStageGoal")
            {
                AssertMultiStageGoalEqual(expected, actual);
            }

            if (isVerifyEventTool)
            {
                AssertStringValueEqual(expected.ClarityEventDefinitionId, actual.ClarityEventDefinitionId, "Goal ClarityEventDefinitionId doesn't match");
                AssertStringValueEqual(expected.ClarityEventDefinitionObject, actual.ClarityEventDefinitionObject, "Goal ClarityEventDefinitionObject doesn't match");
            }
        }

        public static void AssertGoalCategoryEqual(dynamic expected, dynamic actual, bool compareCurrencyCode = false)
        {
                AssertStringValueEqual(expected.GoalCategory, actual.GoalCategory, "Goal GoalCategory");
        }

        private static void AssertMultiStageGoalEqual(dynamic expected, dynamic actual)
        {
            Assert.AreEqual(expected.Stages.Length, actual.Stages.Count);
            for (var i = 0; i < actual.Stages.Count; i++)
            {
                dynamic expectedStage = expected.Stages[i];
                dynamic actualStage = actual.Stages[i];
                
                AssertStringValueEqual(expectedStage.Type, actualStage.Type, "Stage Type");

                if (expectedStage.Type == "EventGoal")
                {
                    AssertStringValueEqual(expectedStage.Category, actualStage.Category, "EventGoalStage Category");
                    AssertStringValueEqual(expectedStage.CategoryOperator, actualStage.CategoryOperator, "EventGoalStage CategoryOperator");
                    AssertStringValueEqual(expectedStage.Action, actualStage.Action, "EventGoalStage Action");
                    AssertStringValueEqual(expectedStage.ActionOperator, actualStage.ActionOperator, "EventGoalStage ActionOperator");
                    AssertStringValueEqual(expectedStage.Label, actualStage.Label, "EventGoalStage Label");
                    AssertStringValueEqual(expectedStage.LabelOperator, actualStage.LabelOperator, "EventGoalStage LabelOperator");
                    AssertStringValueEqual(expectedStage.Value, actualStage.Value, "EventGoalStage Value");
                    AssertStringValueEqual(expectedStage.ValueOperator, actualStage.ValueOperator, "EventGoalStage ValueOperator");
                }
                else
                {
                    AssertStringValueEqual(expectedStage.UrlString, actualStage.UrlString, "DestinationGoalStage Url");
                    AssertStringValueEqual(expectedStage.Operator, actualStage.Operator, "DestinationGoalStage Operator");
                }
            }
        }

        private static void AssertEventGoalEqual(dynamic expected, dynamic actual, bool isVerifyAutoGoal)
        {
            AssertStringValueEqual(expected.Action, actual.Action, "EventGoal Action");
            AssertStringValueEqual(expected.ActionOperator, actual.ActionOperator, "EventGoal ActionOperator");
            if (!isVerifyAutoGoal)
            {
                AssertStringValueEqual(expected.Category, actual.Category, "EventGoal Category");
                AssertStringValueEqual(expected.CategoryOperator, actual.CategoryOperator, "EventGoal CategoryOperator");
                AssertStringValueEqual(expected.Label, actual.Label, "EventGoal Label");
                AssertStringValueEqual(expected.LabelOperator, actual.LabelOperator, "EventGoal LabelOperator");
                AssertStringValueEqual(expected.Value, actual.Value, "EventGoal Value");
                AssertStringValueEqual(expected.ValueOperator, actual.ValueOperator, "EventGoal ValueOperator");
            }
        }

        public static void AssertStringValueEqual(dynamic expected, dynamic actual, string property)
        {
            var dummyExpected = expected ?? string.Empty;
            var dummyActual = actual ?? string.Empty;

            Assert.AreEqual(dummyExpected.ToString(), dummyActual.ToString(), property + " mismatch. Expected: {0}, actual: {1}", expected, actual);
        }

        public static void AssertCollectionsEqual(dynamic expected, dynamic actual, string property)
        {
            expected ??= new string[0];
            actual ??= new string[0];
            if (expected is not IEnumerable expectedJArray)
            {
                Assert.Fail($"Verify {property} Failed: {expected} is not IEnumerable");
                return;
            }

            if (actual is not IEnumerable actualJArray)
            {
                Assert.Fail($"Verify {property} Failed: {actual} is not IEnumerable");
                return;
            }

            var expectedArray = expectedJArray.Cast<object>().Select(x => x.ToString()).ToList();
            var actualArray = actualJArray.Cast<object>().Select(x => x.ToString()).ToList();

            CollectionAssert.AreEqual(expectedArray, actualArray, $"Verify {property} Failed: Expected={string.Join(",", expectedArray)}, Actual={string.Join(",", actualArray)}");
        }

        public static dynamic GetExternalIntegrationsForCustomerContext(CustomerInfo cInfo, long tagId, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getThirdPartyIntegration = 
                string.Format(UETV2BaseUrl + "/Tags({2})/Default.GetExternalIntegrations?", 
                cInfo.CustomerId, cInfo.AccountIds[0], tagId);
            return ApiHelper.CallApi(cInfo, 
                c => c.GetAsync(getThirdPartyIntegration), 
                e => Assert.AreEqual(expectedHttpStatusCode, 
                e.StatusCode, "Get Third-Party Integration by Tag Id"));
        }

        public static dynamic AddExternalIntegrationsForCustomerContext(CustomerInfo cInfo, long tagId,
            string gtmAccountId, string gtmContainerId, string gtmTagId, 
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var getThirdPartyIntegration = 
                string.Format(UETV2BaseUrl + "/Tags({2})/AddExternalIntegrations?gtmAccountId={3}&gtmContainerId={4}&gtmTagId={5}", 
                cInfo.CustomerId, cInfo.AccountIds[0], tagId, gtmAccountId, gtmContainerId, gtmTagId);
            return ApiHelper.CallApi(cInfo, 
                c => c.GetAsync(getThirdPartyIntegration), 
                e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode, "Add Third-Party Integration by Tag Id"));
        }

        public static dynamic CreateThirdPartyResponse(long tagId)
        {
            dynamic goal = new System.Dynamic.ExpandoObject();
            goal.odatatype = "#Model.ThirdPartyIntegration";
            goal.UetTagId = tagId;
            goal.NumberOfEntities = 1;

            dynamic GtmInfoItems = new System.Dynamic.ExpandoObject();
            goal.GtmIntegrations = GtmInfoItems;

            return goal;
        }



        public static void AssertThirdPartyIntegrationEqual(dynamic expected, dynamic actual)
        {
            if (expected == null && actual == null)
            {
                return;
            }

            if (expected == null || actual == null)
            {
                Assert.Fail("Goal: null v.s. non null");
            }

            AssertStringValueEqual(expected.UetTagId, actual.UetTagId, "UET TagID");
            AssertStringValueEqual(expected.NumberOfEntities, actual.NumberOfEntities, "Number of Entities in returning result");
        }

        public static void RemoveAdminUser(int customerId)

         {
            var commandStr = string.Format(
                @" UPDATE [User]
                      SET LifeCycleStatusId = 103,
                          ModifiedDTim = getutcdate()
                   WHERE UserId in (SELECT DISTINCT UserId FROM UserUserRoleCustomerScope WHERE CustomerId = {0} and UserRoleId in (41, 68));",
                customerId);

            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out data);
        }
        
        public static dynamic GetModeledConversionGrid(CustomerInfo cInfo, DateTime endDate, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, 
            int? accountId = null, int? customerId = null, string filteringStr = null, string orderByStr = null, int top = -1, int skip = -1)
        {
            var reqUrl = string.Format(UETV2BaseUrl + $"/Goals/Default.UpliftReportGrid?%24count=true",
                customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);

            if(!string.IsNullOrEmpty(filteringStr))
            {
                reqUrl = string.Format(reqUrl + "&$filter={0}", filteringStr);
            }
            if(!string.IsNullOrEmpty(orderByStr))
            {
                reqUrl =  string.Format(reqUrl + "&$orderby={0}", orderByStr);
            }
            if(top > 0)
            {
                reqUrl = string.Format(reqUrl + "&$top={0}", top);
            }
            if(skip > 0)
            {
                reqUrl = string.Format(reqUrl + "&$skip={0}", skip);
            }

            string jsonBodyStr = "{\"gridDataParameters\":{\"DateRange\":{\"EndDate\":\"" +endDate.ToString("yyyy-MM-dd") +"\"}}}";
            return ApiHelper.CallApi(cInfo, 
                c => c.PostAsync(reqUrl , new StringContent(jsonBodyStr, System.Text.Encoding.UTF8, "application/json")),
                e => Assert.AreEqual(expectedHttpStatusCode, 
                e.StatusCode, "Get modeled conversion grid data"));
        }

        public static dynamic GetModeledConversionStatus(CustomerInfo cInfo, DateTime endDate, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, int? accountId = null, int? customerId = null)
        {
            string endDateInQuery = Uri.EscapeDataString(endDate.ToString("MM/dd/yyyy"));
            var reqUrl = string.Format(UETV2BaseUrl + $"/Goals/Default.UpliftReportStatus?enddate={endDateInQuery}", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonBodyStr = "{\"gridDataParameters\":{\"DateRange\":{\"EndDate\":\"" +endDate.ToString("yyyy-MM-dd") +"\"}}}";
            return ApiHelper.CallApi(cInfo, 
                c => c.PostAsync(reqUrl , new StringContent(jsonBodyStr, System.Text.Encoding.UTF8, "application/json")),
                e => Assert.AreEqual(expectedHttpStatusCode, 
                e.StatusCode, "Get modeled conversion report status"));
        }

        public static dynamic UpdateConsentModeDecision(CustomerInfo cInfo,
            string ConsentModeDecision, string emailListStr,
            HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK)
        {
            var reqUrl = string.Format(UETV2BaseUrlForCustomerContext + $"/Accounts/Default.UpdateConsentModeDecision", cInfo.CustomerId);
            string jsonBodyStr = $@"{{ 
                ""ConsentModeDecision"": ""{ConsentModeDecision}"", 
                ""EmailAddresses"": ""{emailListStr}"" 
            }}";

            return ApiHelper.CallApi(cInfo,
                c => c.PostAsync(reqUrl, new StringContent(jsonBodyStr, System.Text.Encoding.UTF8, "application/json")),
                e => Assert.AreEqual(expectedHttpStatusCode,
                e.StatusCode, "Get modeled conversion report status"));
        }
        
        public static dynamic GetUETConversionStatus(CustomerInfo cInfo)
        {
            var reqUrl = string.Format(UETV2BaseUrlForCustomerContext + $"/Accounts/Default.UETConsentModeStatus", cInfo.CustomerId);
            return ApiHelper.CallApi(cInfo,
                c => c.GetAsync(reqUrl),
                e => Assert.AreEqual(HttpStatusCode.OK,
                e.StatusCode, "Get UET Consent Mode Status"));
        }

        public static dynamic GetModeledConversionDownloadLink(CustomerInfo cInfo, DateTime endDate, HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK, int? accountId = null, int? customerId = null)
        {
            string endDateInQuery = Uri.EscapeDataString(endDate.ToString("MM/dd/yyyy"));
            var reqUrl = string.Format(UETV2BaseUrl + $"/Goals/Default.GetExportUrlForUpliftReport?enddate={endDateInQuery}", customerId ?? cInfo.CustomerId, accountId ?? cInfo.AccountIds[0]);
            string jsonBodyStr = "{\"gridDataParameters\":{\"DateRange\":{\"EndDate\":\"" +endDate.ToString("yyyy-MM-dd") +"\"}}}";
            return ApiHelper.CallApi(cInfo, 
                c => c.PostAsync(reqUrl , new StringContent(jsonBodyStr, System.Text.Encoding.UTF8, "application/json")),
                e => Assert.AreEqual(expectedHttpStatusCode, 
                e.StatusCode, "Get modeled conversion report download link"));
        }

        public static dynamic GetCampaignByIdWithCampaignGoal(CustomerInfo cInfo, long campaignId)
        {
            var getUrl = string.Format(UETV2BaseUrl + "/Campaigns({2})?$expand=CampaignConversionGoal/Goals", cInfo.CustomerId, cInfo.AccountIds[0], campaignId);
            return ApiHelper.CallApi(cInfo, c => c.GetAsync(getUrl), e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode, "Get Campaign by Campaign Id request"));
        }

        private static object GetOptionalProperty(dynamic obj, string property, dynamic defaultValue = null)
        {
            switch (obj)
            {
                case IDictionary<string, object> dict:
                    if (dict.TryGetValue(property, out var value))
                    {
                        return value;
                    }

                    break;

                case JObject jObject:
                    if (jObject.TryGetValue(property, out var jToken))
                    {
                        return jToken;
                    }

                    break;

                default:
                    Assert.Fail("Unsuppoorted object type, please add implementation");
                    break;
            }

            return defaultValue;
        }

        public static void AuthForCosmos()
        {
            var certName = (CrossPlatformHelpers.RunningInLocal || CrossPlatformHelpers.RunningInCorp) ? "bingads-campaignmt-clientcert-corp" : "AKSCosmosCertificate";
            var certKV = (CrossPlatformHelpers.RunningInLocal || CrossPlatformHelpers.RunningInCorp) ? ConfigurationManager.AppSettings["CampaignSecretsKeyVaultName-TEST-CORP"] : ConfigurationManager.AppSettings["CampaignSecretsKeyVaultName"];
            var VCName = "https://cosmos08.osdinfra.net/cosmos/bingAds.BI.OI/";
            var cert = new KeyVaultHelperWrapper().GetCertificateAsync(certKV, certName).Result;
            VC.Setup(VCName, cert);
        }

        public static void UploadToCosmos(string localPath, string cosmosPath)
        {
            try
            {
                if (VC.StreamExists(cosmosPath))
                {
                    Console.WriteLine($"delete original cosmos file: {cosmosPath}");
                    VC.Delete(cosmosPath);
                }
                VC.Upload(sourceFile: localPath, destinationStream: cosmosPath, compression: false, expirationTime: new TimeSpan(24 * 7, 0, 0));
                Console.WriteLine($"upload to cosmos: {cosmosPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"upload to cosmos fail: {ex.Message}, target path: {cosmosPath}");
            }
        }

        public static void DeleteCosmosFile(string cosmosPath)
        {
            try
            {
                if (VC.StreamExists(cosmosPath))
                {
                    Console.WriteLine($"delete original cosmos file: {cosmosPath}");
                    VC.Delete(cosmosPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"delete cosmos file fail: {ex.Message}, target path: {cosmosPath}");
            }
        }

        public static void AssertInvestUniversalPixelEqual(InvestUniversalPixel expected, dynamic actual)
        {
            Assert.AreEqual(expected.Uuid, (string)actual.Uuid);
            Assert.AreEqual(expected.Id, (int)actual.Id);
            Assert.AreEqual(expected.Name, (string)actual.Name);
        }

        public static string GenerateEventActionForAutoGoal(GoalCategory? goalCategory)
        {
            string actionHeader = "AutoEvent_";
            switch (goalCategory) {
                case GoalCategory.Purchase:
                    return actionHeader + "purchase";
                case GoalCategory.AddToCart:
                    return actionHeader + "add_to_cart";
                case GoalCategory.BeginCheckout:
                    return actionHeader + "begin_checkout";
                case GoalCategory.Subcribe:
                    return actionHeader + "subscribe";
                case GoalCategory.SubmitLeadForm:
                    return actionHeader + "submit_form";
                case GoalCategory.BookAppointment:
                    return actionHeader + "book_appointment";
                case GoalCategory.Signup:
                    return actionHeader + "sign_up";
                case GoalCategory.RequestQuote:
                    return actionHeader + "request_quote";
                case GoalCategory.GetDirections:
                    return actionHeader + "look_at_directions";
                case GoalCategory.OutboundClick:
                    return actionHeader + "outbound_click";
                case GoalCategory.Contact:
                    return actionHeader + "contact_us";
                default:
                    return string.Empty; //unsupported category for auto goal
            }
        }
    }
}

#endif