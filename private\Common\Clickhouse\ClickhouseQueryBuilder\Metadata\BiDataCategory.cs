﻿namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public enum BiDataCategory
    {
        AccountAssets,
        AccountAssetsByAccountId,
        AccountQualityScore,
        AccountUsage,
        AdAdExtensionClickTypeUsage,
        AdLandingPageUrlUsage,
        AdUsage,
        AssetAdGroupSnapshotUsage,
        AssetCombinationUsage,
        AssetGroupUsage,
        AssetSnapShotUsage,
        AssetUsage,
        AutomatedExtensionUsage,
        AutoTargetUsage,
        BidSuggestion,
        BSCQueryUsage,
        CallDetailsUsage,
        CampaignExperimentUsage,
        CampaignOfferMatchCounts,
        CampaignPrediction,
        CampaignQualityScore,
        CampaignTargetUsage,
        CampaignUsage,
        CookieCampaignExperimentUsage,
        CustomerStore,
        ElementAdUsage,
        ElementAdUsageByOrder,
        ElementOrderItemUsage,
        FeedItemAdExtensionUsage,
        FeedItemUsage,
        FlattenedOrderProductTreeSettingAssociation,
        GenderAgeUsage,
        GoalUsage,
        HotelBookingUsage,
        HotelUsage,
        HotelUsageByHotel,
        HotelVerticalImpressionShareUsage,
        HotelVerticalUsage,
        ImpressionShareAccountUsage,
        ImpressionShareCampaignUsage,
        ImpressionShareHotelUsage,
        ImpressionShareHotelUsageByHotel,
        ImpressionShareOrderItemUsage,
        ImpressionShareOrderUsage,
        ImpressionShareProductOfferUsage,
        InProgressAccountUsage,
        InProgressAdAdExtensionClickTypeUsage,
        InProgressAdLandingPageUrlUsage,
        InProgressAdUsage,
        InProgressAssetGroupUsage,
        InprogressAssetUsage,
        InProgressAutomatedExtensionUsage,
        InProgressAutoTargetUsage,
        InProgressCallDetailsUsage,
        InProgressCampaignUsage,
        InProgressElementAdUsageByOrder,
        InProgressElementOrderItemUsage,
        InprogressFeedItemAdExtensionUsage,
        InprogressFeedItemUsage,
        InProgressGenderAgeUsage,
        InProgressGoalUsage,
        InProgressLocationHourUsage,
        InProgressMSXAccountUsage,
        InProgressOrderItemAdExtensionClickTypeUsage,
        InProgressOrderItemDDAUsage,
        InProgressOrderItemUsage,
        InProgressOrderTargetUsage,
        InProgressOrderUsage,
        InProgressProductOfferCampaignUsage,
        InProgressProductOfferUsage,
        InprogressProfessionalDemographicsUsage,
        InProgressPublisherPlacementUsage,
        InProgressQueryUsage,
        InProgressRadiusTargetedLocationHourUsage,
        InProgressSubAccountUsage_NoIS,
        InProgressSubCampaignUsage_NoIS,
        InProgressSubOrderItemUsage_NoIS,
        LeadFormResponse,
        LocationHourUsage,
        MatchedOffers,
        MSANImpressionShareAccountUsage,
        MSANImpressionShareCampaignUsage,
        MSANImpressionShareOrderUsage,
        MSANRelativeCTRCampaignUsage,
        MSANRelativeCTROrderUsage,
        MSXAccountUsage,
        NegativeKeywordConflict,
        OrderItemAdExtensionClickTypeUsage,
        OrderItemDDAUsage,
        OrderItemOfferMatchCounts,
        OrderItemQualityScore,
        OrderItemUsage,
        OrderOfferMatchCounts,
        OrderProductTree,
        OrderProductTreeSettingAssociation,
        OrderProductTreeSettingProperty,
        OrderQualityScore,
        OrderTargetUsage,
        OrderUsage,
        ProductOffer_Partition,
        ProductOfferCampaignUsage,
        ProductOfferConversionCampaignUsage,
        ProductOfferConversionUsage,
        ProductOfferUsage,
        ProfessionalDemographicsUsage,
        PublisherPlacementUsage,
        QueryUsage,
        RadiusTargetedLocationHourUsage,
        SearchPhraseUsage,
        SPAOffer,
        SubAccountUsage,
        SubAccountUsage_NoIS,
        SubCampaignUsage,
        SubCampaignUsage_NoIS,
        SubImpressionShareAccountUsage,
        SubImpressionShareCampaignUsage,
        SubOrderItemUsage_NoIS,
        SubOrderUsage,
        SubQueryUsage,
        ToBeDeletedAd,
        ToBeDeletedAdData,
        ToBeDeletedCampaign,
        ToBeDeletedOrder,
        ToBeDeletedOrderItem,
        ToBeDeletedOrderItemData,
        vLocationHourFactUsage,
        vLocationHourUsage,
        vOfferMatchCount,
        vRadiusTargetedLocationHourFactUsage,
        vRadiusTargetedLocationHourUsage,
        vSubAccountUsage,
        vSubCampaignUsage,
        vSubOrderUsage,
        vSubQueryUsage,
        vHotelVerticalUsage,
        vHotelVerticalImpressionShareUsage,
        CampaignReachUsage,
        OrderReachUsage,
        AdReachUsage,
        ProductOfferImpressionShareUsage,
        ContentPerformanceUsage,
    }
}
