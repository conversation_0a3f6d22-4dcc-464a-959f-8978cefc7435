{"prc_AccountImpressionShare_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdScenarioType", "AdvertisingChannelTypeId", "AdvertisingSubChannelTypeId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "ClickTypeId", "CountryCode", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeviceOSId", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "GroupId", "HourNum", "HourOfDay", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId", "PricingModelId", "ProductId", "ProductName", "QuarterStartDate", "RelationshipId", "TargetTypeId", "WeekStartDate", "YearNum"], "Measures": ["AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "BSCAuctionParticipation", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TopImpressionCnt", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_AccountSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdPosition", "AdScenarioType", "AdUnitId", "AdvertisingChannelTypeId", "AdvertisingSubChannelTypeId", "AudienceName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignFeatureBitMask", "ClickTypeId", "CountryCode", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeliveryFormatId", "DemandTypeId", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DomainTypeId", "GoalId", "GoalTypeId", "GregorianDate", "GroupId", "HourNum", "HourOfDay", "IsMSANSWFRecord", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId", "PagePositionId2", "PricingModelId", "ProductId", "ProductName", "QuarterStartDate", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum", "AgencyId", "CurrencyId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "ImpressionWithPositionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalAmountUSD", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionLag", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "BSCAuctionParticipation", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment"]}, "prc_AccountSummaryLite_ui": {"Dimensions": ["AccountId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "GregorianDate", "HourNum", "HourOfDay", "MediumId", "MonthStartDate", "NetworkId", "PagePositionId2", "QuarterStartDate", "WeekStartDate", "YearNum"], "Measures": []}, "prc_AdSummary_ui": {"Dimensions": ["AccountId", "AdId", "AdScenarioType", "AdUnitId", "BiddedMatchTypeId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "QuarterStartDate", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum", "FeedItemId", "AudienceId", "PagePositionId2"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt", "AdExtensionTypeId", "<PERSON>ed<PERSON><PERSON><PERSON>"]}, "prc_AgeGenderSummary_Dim": {"Dimensions": ["AccountId", "AdScenarioType", "AdvertisingChannelTypeId", "AgeBucketDesc", "AgeBucketId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceTypeId", "DistributionChannelId", "GenderId", "GenderName", "KeywordOrderName", "MediumId", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "RelationshipId", "SOSId"], "Measures": []}, "prc_AssetGroupSummary_ui": {"Dimensions": ["AccountId", "AssetGroupId", "CampaignId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MediumId", "MonthStartDate", "NetworkId", "QuarterStartDate", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "PagePositionId", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_CallDetailsSummary_Dim": {"Dimensions": ["AccountId", "AdId", "AreaCode", "CallEndReason", "CallEndStatus", "CallEndTime", "CallId", "CallStartTime", "CampaignId", "CampaignName", "CampaignStatusName", "CityName", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceTypeId", "EndTime", "HourNum", "IsOfflineCall", "KeywordOrderName", "LoadTime", "OrderId", "StartTime", "SubGeographyName", "Account<PERSON><PERSON>", "AccountStatusName", "AdGroupStatusName", "CallTypeName", "GregorianDate", "CallReasonName", "Region", "City"], "Measures": []}, "prc_CampaignImpressionShareSummary_ui": {"Dimensions": ["AccountId", "AdScenarioType", "AdvertisingChannelTypeId", "AudienceName", "BiddedMatchTypeId", "BudgetId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceTypeId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "PagePositionId", "PagePositionId2", "PricingModelId", "ProductId", "QualityBandId", "QuarterStartDate", "RelationshipId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": []}, "prc_CampaignSummary_ui": {"Dimensions": ["AccountId", "AdScenarioType", "AdUnitId", "BiddedMatchTypeId", "BidStrategyId", "BudgetId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "PagePositionId", "PricingModelId", "ProductId", "QualityBandId", "QuarterStartDate", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum", "PagePositionId2"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionValueCnt", "FullConversionValueCnt", "ConversionLag", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AdvertiserClicks", "RelativeAuctionWonCnt", "SameSectionCount", "SameSectionCTR", "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment"]}, "prc_ConversionGoalsSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "CampaignId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "GoalCategory", "GoalId", "GoalName", "IsMainConversionGoal", "OrderId", "OrderItemId", "UserDim1Id", "UserDim2Id", "UserDim3Id", "UserDim4Id", "UserDim5Id"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AllConversionCnt", "AllConversionCredit", "AssistCnt", "ConversionCnt", "ConversionCredit", "ExtendedCost", "FullAdvertiserReportedRevenueAdjustment", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "Stage1Cnt", "Stage2Cnt", "Stage3Cnt", "Stage4Cnt", "Stage5Cnt", "TotalConversionCnt", "TotalConversionCredit", "TotalCost", "UniqueConversionCnt", "UniqueConversionCredit", "ConversionLag"]}, "prc_DSAAutoTargetCategorySummary_ui": {"Dimensions": ["AccountId", "AdId", "AdLandingPageUrlId", "BiddedMatchTypeId", "CampaignId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "FirstLevelCategory", "GoalId", "GoalTypeId", "GregorianDate", "HasCategory", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PeriodEndDate", "PeriodStartDate", "PricingModelId", "QuarterStartDate", "SecondLevelCategory", "TargetTypeId", "TopLevelCategory", "WeekStartDate", "YearNum"], "Measures": []}, "prc_DSAAutoTargetSummary_ui": {"Dimensions": ["AccountId", "AdId", "BiddedMatchTypeId", "CampaignId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PeriodEndDate", "PeriodStartDate", "PricingModelId", "QuarterStartDate", "TargetTypeId", "WebsiteCoverage", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_GeographicalLocationSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "CampaignId", "CampaignName", "CityName", "CountryName", "DerivedCountryId", "DerivedLocationId", "KeywordOrderName", "LocationId", "OrderId", "SubGeographyName"], "Measures": []}, "prc_GeoLocationSummary_Dim": {"Dimensions": ["AccountId", "AdScenarioType", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CityLatitude", "CityLongitude", "CityName", "ClickTypeId", "CountryLatitude", "CountryLongitude", "CountryName", "County", "CountyLatitude", "CountyLongitude", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GeoLocationId", "KeywordOrderName", "LocationId", "MatchTypeId", "MediumId", "MetroAreaLatitude", "MetroAreaLongitude", "MetroAreaName", "MostSpecificLocation", "Neighborhood", "NeighborhoodLatitude", "NeighborhoodLongitude", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "QueryGeoLocationId", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyLatitude", "SubGeographyLongitude", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeId", "ZipCode", "ZipCodeLatitude", "ZipCodeLongitude"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BusinessLocationId", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CPC", "CPM", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationId2", "HourNum", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_GetSearchQuerySummaryLite": {"Dimensions": ["AccountId", "AdId", "AdLandingPageTitle", "AdLandingPageUrlId", "AdScenarioType", "AdTitle", "BiddedMatchTypeId", "CampaignId", "CategoryList", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "Description", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "FeedURL", "FinalURL", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "KeywordOrderId", "KeywordOrderItemId", "LandingPageTitle", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PeriodEndDate", "PeriodStartDate", "PricingModelId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchTerm", "SOSId", "SubMatchTypeId", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_HotelGroupSummary_Online_ui": {"Dimensions": ["AccountId", "AdId", "AdvancedBookingWindow", "AdvertiserHotelId", "Brand", "CampaignHotelId", "CampaignId", "Category", "CheckInDate", "CityLocationId", "CityName", "CountryLocationId", "CountryName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "GregorianDate", "HotelListingId", "HotelName", "HourNum", "HourOfDay", "LengthOfStay", "MonthStartDate", "OrderId", "PublisherCountryId", "QuarterStartDate", "SiteType", "SlotId", "SourceHotelId", "StarRating", "StateLocationId", "StateName", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": ["AssistCnt", "BasePrice", "BookedAdvancedBookingWindow", "ClickCnt", "ConversionCnt", "ImpressionCnt", "TotalAmount", "TotalAmountUSD", "TotalBookedNights", "TotalPosition", "TotalPrice", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "TotalPartnerClick"]}, "prc_HotelSummary_ui": {"Dimensions": ["AccountId", "AdId", "AdvancedBookingWindow", "CampaignHotelId", "CampaignId", "CheckInDate", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "GregorianDate", "HotelListingId", "HourNum", "HourOfDay", "LengthOfStay", "MonthStartDate", "OrderId", "PublisherCountryId", "QuarterStartDate", "SiteType", "SlotId", "SourceHotelId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": ["AssistCnt", "BasePrice", "BookedAdvancedBookingWindow", "ClickCnt", "ConversionCnt", "ImpressionCnt", "TotalAmount", "TotalAmountUSD", "TotalBookedNights", "TotalPosition", "TotalPrice", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "TotalPartnerClick"]}, "prc_HotelVerticalSummary_ui": {"Dimensions": ["DeviceTypeId", "VerticalItemGroupId", "VerticalItemId", "VerticalCampaignId"], "Measures": ["AdvancedBookingWindow", "AssistCount", "BasePrice", "BookedAdvancedBookingWindow", "CheckInDate", "ClickCnt", "ConversionCount", "ConversionCredit", "DateType", "HourNum", "ImpressionCnt", "LengthOfStay", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "PublisherCountryId", "SiteType", "SlotId", "TotalAmount", "TotalAmountUSD", "TotalBookedNights", "TotalPartnerClick", "TotalPosition", "TotalPrice", "TotalSlotPosition"]}, "prc_KeywordImpressionShareSummary_ui": {"Dimensions": ["AccountId", "AdScenarioType", "AudienceName", "BiddedMatchTypeId", "CampaignId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId2", "PricingModelId", "QuarterStartDate", "RelationshipId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": []}, "prc_KeywordSummary_ui": {"Dimensions": ["AccountId", "AdId", "AdScenarioType", "BiddedMatchTypeId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PricingModelId", "QuarterStartDate", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment"]}, "prc_LandingPageURLSummary_Dim": {"Dimensions": ["AccountId", "AdId", "AdScenarioType", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomParameters", "<PERSON><PERSON><PERSON>", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "FinalURL", "FinalUrlSuffix", "GoalId", "GoalTypeId", "KeywordOrderName", "MatchTypeId", "MediumId", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "TrackingTemplate"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits"]}, "prc_OrderImpressionShareSummary_ui": {"Dimensions": ["AccountId", "AdScenarioType", "AdvertisingChannelTypeId", "AudienceName", "BiddedMatchTypeId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelId", "QuarterStartDate", "RelationshipId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum"], "Measures": []}, "prc_OrderSummary_ui": {"Dimensions": ["AccountId", "AdScenarioType", "AdUnitId", "BiddedMatchTypeId", "CampaignId", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "QuarterStartDate", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "YearNum", "PagePositionId2"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AdvertiserClicks", "RelativeAuctionWonCnt", "SameSectionCount", "SameSectionCTR", "AllConversionAdvertiserReportedRevenueAdjustment", "ViewThroughConversionsRevenueAdjustment"]}, "prc_PerformanceTargetSummary_ui": {"Dimensions": ["DayOfWeek", "GregorianDate", "HourNum", "HourOfDay", "MonthStartDate", "PTId", "QuarterStartDate", "WeekStartDate", "YearNum", "<PERSON><PERSON><PERSON>"], "Measures": ["AdScenarioType", "AdUnitId", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BiddedMatchTypeId", "BidStrategyId", "ClickCnt", "ClickTypeId", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DeviceOSID2", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "MediumId", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "PagePositionId", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "PricingModelId", "ProductId", "Purchases", "QualityBandId", "RelationshipId", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "SOSId", "Subscriptions", "TargetValueId", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionValueCnt", "FullConversionValueCnt", "ConversionLag"]}, "prc_ProductOfferSummary_Dim": {"Dimensions": ["AccountId", "AdId", "BiddedMatchTypeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "<PERSON><PERSON><PERSON>", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeId", "MediumId", "MerchantId", "MerchantProductId", "NetworkId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "Price", "PricingModelId", "ProductBoughtTitle", "ProductOfferId", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetTypeId", "Title"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "prc_ProductTabSummaryForAccountCampaign_ui": {"Dimensions": ["DayOfWeek", "GregorianDate", "HourNum", "HourOfDay", "MonthStartDate", "QuarterStartDate", "WeekStartDate", "YearNum", "GlobalOfferId", "CampaignId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "ChannelTypeId", "ClickCnt", "ClickTypeId", "CollectionId", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "Purchased<PERSON><PERSON>", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalShoppingAbsTopPosition"]}, "prc_PublisherPlacementSummary_Dim": {"Dimensions": ["AccountId", "AppBundle", "AppName", "AppStoreUrl", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "KeywordOrderName", "MatchTypeId", "MediumId", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "PropertyUrl", "PublisherId", "PublisherURL", "RelationshipId"], "Measures": []}, "prc_SearchQuerySummary_Dim": {"Dimensions": ["AccountId", "AdId", "AdLandingPageUrlId", "AdScenarioType", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalId", "GoalTypeId", "Is<PERSON>ther", "IsPageUrl", "Keyword", "KeywordOrderName", "MatchTypeId", "MediumId", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "Query", "QueryHash", "RelationshipId", "SearchTerm", "SOSId", "SubMatchTypeId"], "Measures": []}, "prc_SearchQuerySummary_ui": {"Dimensions": ["AccountId", "AdId", "AdLandingPageTitle", "AdLandingPageUrlId", "AdScenarioType", "AdTitle", "BiddedMatchTypeId", "CampaignId", "CategoryList", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "Description", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "FeedURL", "FinalURL", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "KeywordOrderId", "KeywordOrderItemId", "LandingPageTitle", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PeriodEndDate", "PeriodStartDate", "PricingModelId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchTerm", "SOSId", "SubMatchTypeId", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_TimeSummary_Dim": {"Dimensions": ["QuarterStartDate", "GregorianDate", "MonthStartDate", "WeekStartDate", "DayOfWeek", "HourOfDay", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt"]}, "prc_UserLocationSummary_Dim": {"Dimensions": ["AccountId", "AdScenarioType", "BiddedMatchTypeId", "BusinessLocationId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CityLatitude", "CityLongitude", "CityName", "ClickTypeId", "CountryCode", "CountryId", "CountryLatitude", "CountryLongitude", "CountryName", "County", "CountyLatitude", "CountyLongitude", "CountyName", "<PERSON><PERSON><PERSON>", "DerivedCountryId", "DerivedLocationId", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "GeoLocationTypeId", "GoalId", "GoalTypeId", "KeywordOrderName", "LCID", "LocationId", "MatchTypeId", "MediumId", "MetroAreaLatitude", "MetroAreaLongitude", "MetroAreaName", "MostSpecificLocation", "Neighborhood", "NeighborhoodLatitude", "NeighborhoodLongitude", "NetworkId", "OrderId", "PagePositionId", "PostalCode", "PostalCodeLatitude", "PostalCodeLongitude", "PricingModelId", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyLatitude", "SubGeographyLongitude", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeId", "ZipCode", "ZipCodeLatitude", "ZipCodeLongitude"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CPC", "CPM", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationId2", "HourNum", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_AccountImpressionSharePerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdScenarioType", "AdvertisingChannelTypeId", "AdvertisingSubChannelTypeId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "GregorianDate", "HourNum", "HourOfDay", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "StatusName", "TargetTypeId", "TargetTypeName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "BSCAuctionParticipation", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TopImpressionCnt", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_AccountPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdPosition", "AdScenarioType", "AdUnitId", "AdvertisingChannelTypeId", "AdvertisingSubChannelTypeId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignFeatureBitMask", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeliveryFormatId", "DemandTypeId", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DomainTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "IsMSANSWFRecord", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetTypeName", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "ImpressionWithPositionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalAmountUSD", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionLag"]}, "rpt_AdActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdBusinessName", "AdDescription", "AdDescription2", "AdGroupStatusName", "AdId", "AdLabe<PERSON>", "AdName", "AdScenarioType", "AdStatus", "AdStatusName", "AdStrengthJSON", "AdTitle", "AdTitle1", "AdTitle2", "AdTitle3", "AdTypeId", "AdTypeName", "AdUnitId", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalAppUrl", "FinalMobileUrl", "FinalURL", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "LongHeadline", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "Path1", "Path2", "PricingModelId", "PricingModelName", "ProductName", "RelationshipId", "ShortHeadline", "SOSId", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt", "Reach1d", "Reach2d", "Reach3d", "Reach4d", "Reach5d", "Reach6d", "Reach7d", "Reach8d", "Reach9d", "Reach10d", "Reach11d", "Reach12d", "Reach13d", "Reach14d", "Reach15d", "Reach16d", "Reach17d", "Reach18d", "Reach19d", "Reach20d", "Reach21d", "Reach22d", "Reach23d", "Reach24d", "Reach25d", "Reach26d", "Reach27d", "Reach28d", "Reach29d", "Reach30d"]}, "rpt_AdExtensionItemActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdExtensionId", "AdExtensionItemId", "AdExtensionPropertyValue", "AdExtensionTypeId", "AdExtensionTypeName", "AdExtensionVersion", "AdGroupStatusName", "AdId", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AppExtensionOS", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "DataForReport", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LCID", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "ProductId", "ProductName", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdExtImpressionCnt", "AdImpressionCnt", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SecondaryClickCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition"]}, "rpt_AdParameterSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdDescription2", "AdGroupStatusName", "AdId", "AdLabe<PERSON>", "AdName", "AdScenarioType", "AdStatusName", "AdTitle", "AdTitle1", "AdTitle2", "AdTitle3", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "ElementId", "FinalAppUrl", "FinalMobileUrl", "FinalURL", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "Param1", "Param2", "Param3", "Path1", "Path2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "SOSId", "TargetTypeId", "TargetTypeName", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits"]}, "rpt_AgeGenderSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "AdvertisingChannelTypeId", "AgeBucketDesc", "AgeBucketId", "BaseCampaignId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GenderId", "GenderName", "GleamTypeName", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "SOSId", "StatusName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_AgeGenderSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "AdvertisingChannelTypeId", "AgeBucketDesc", "AgeBucketId", "BaseCampaignId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DistributionChannelId", "DistributionChannelName", "GenderId", "GenderName", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "QuarterStartDate", "RelationshipId", "SOSId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_AppSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddingSchemeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CurrencyCode", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DeviceTypeName", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId2", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DistributionChannelId", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "PagePositionId", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TargetValueId", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_AssetCombinationSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AdId", "AdTypeId", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "CampaignId", "CampaignName", "CampaignType", "CTA", "<PERSON><PERSON><PERSON>", "DayOfWeek", "Description1", "Description1AssetId", "Description2", "Description2AssetId", "DescriptionAssetId", "GregorianDate", "Headline1", "Headline1AssetId", "Headline2", "Headline2AssetId", "Headline3", "Headline3AssetId", "HeadlineAssetId", "Image", "ImageId", "KeywordOrderName", "Logo", "LogoImageId", "LongHeadline", "LongHeadlineAssetId", "MonthStartDate", "OrderId", "VideoId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "ClickCnt", "ConversionCnt", "ConversionCredit", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "HourNum", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_AssetGroupSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DistributionChannelId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MediumId", "MonthStartDate", "NetworkId", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "PagePositionId", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_AssetSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AdAssetAssociationTypeId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AssetId", "AssetSource", "AssetType", "CampaignId", "CampaignName", "CampaignType", "<PERSON><PERSON><PERSON>", "DayOfWeek", "GregorianDate", "KeywordOrderName", "LoadTime", "MonthStartDate", "OrderId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["ClickCnt", "CompletedViewCnt", "FirstLaunches", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "TotalAmount", "TotalWatchTime", "ViewCnt", "ConversionCredit", "ConversionCnt", "AdvertiserReportedRevenue"]}, "rpt_AudienceSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AssociationId", "AssociationLevel", "AssociationStatus", "AudienceId", "AudienceName", "AudienceTypeId", "BaseCampaignId", "BidAdjustment", "CampaignId", "CampaignName", "CampaignStatusName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "TargetSetting", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_BSCSearchQuerySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AutoTargetId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "KeywordOrderId", "KeywordOrderName", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "QuarterStartDate", "Query", "SearchQuery", "<PERSON><PERSON><PERSON><PERSON>", "SOSId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "EligibleImpressionCnt", "BudgetFilteredImpressionCnt", "RankFilteredImpressionCnt", "BenchmarkBid_Numerator", "BenchmarkBid_Denominator", "BenchmarkCTR_Numerator", "BenchmarkCTR_Denominator"]}, "rpt_BSCSearchQuerySummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "AutoTargetId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "KeywordOrderId", "KeywordOrderName", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "QuarterStartDate", "Query", "SearchQuery", "<PERSON><PERSON><PERSON><PERSON>", "SOSId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "EligibleImpressionCnt", "BudgetFilteredImpressionCnt", "RankFilteredImpressionCnt", "BenchmarkBid_Numerator", "BenchmarkBid_Denominator", "BenchmarkCTR_Numerator", "BenchmarkCTR_Denominator"]}, "rpt_CampaignImpressionSharePerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BudgetAmount", "BudgetAssociationStatus", "BudgetId", "BudgetName", "BudgetStatusName", "BudgetType", "CampaignId", "CampaignLabels", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "DailyBudgetAmt", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelName", "FinalUrlSuffix", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "IncrementalBudgetAmt", "LabelColor", "LabelName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityBandId", "QualityScore", "QualityScoreName", "RelationshipId", "StatusName", "TargetTypeId", "TargetTypeName", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TopImpressionCnt", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TargetValueId", "DistributionChannelId"]}, "rpt_CampaignPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdScenarioType", "AdUnitId", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BidStrategyId", "BudgetAmount", "BudgetAssociationStatus", "BudgetId", "BudgetName", "BudgetStatusName", "BudgetType", "CampaignId", "CampaignLabels", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "DailyBudgetAmt", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelName", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "IncrementalBudgetAmt", "LabelColor", "LabelName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityBandId", "QualityScore", "QualityScoreName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionValueCnt", "FullConversionValueCnt", "ConversionLag"]}, "rpt_ConversionGoalsSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "CampaignId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "GoalCategory", "GoalId", "GoalName", "IsMainConversionGoal", "OrderId", "OrderItemId", "UserDim1Id", "UserDim2Id", "UserDim3Id", "UserDim4Id", "UserDim5Id"], "Measures": []}, "rpt_ConversionModelCompareSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "DayOfWeek", "DeliveredMatchTypeDesc", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "IsMainConversionGoal", "Keyword", "KeywordOrderName", "MatchTypeId", "MonthStartDate", "QuarterStartDate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["CampaignTypeId", "ClickCnt", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DDAFullAdvertiserReportedRevenue", "DDAFullConversionCredit", "DDAFullViewAdvertiserReportedRevenue", "DDAFullViewThroughConversion", "ImpressionCnt", "LCAFullAdvertiserReportedRevenue", "LCAFullConversionCredit", "LCAFullViewAdvertiserReportedRevenue", "LCAFullViewThroughConversion", "TotalAmount"]}, "rpt_DestinationURLActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdScenarioType", "AdStatusName", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "ElementId", "FinalAppUrl", "FinalMobileUrl", "FinalURL", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageLocaleId", "LanguageName", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "PricingModelId", "ProductId", "ProductName", "RelationshipId", "SOSId", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits"]}, "rpt_DSAAutoTargetCategorySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdStatusName", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "FirstLevelCategory", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "PagePositionId2", "PricingModelId", "QuarterStartDate", "SecondLevelCategory", "TargetTypeId", "TopLevelCategory", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_DSAAutoTargetSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AutoTarget", "AutoTargetId", "AutoTargetStatusName", "BiddedMatchTypeId", "BiddingSchemeId", "CampaignId", "CampaignName", "CampaignStatusName", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "PagePositionId2", "PricingModelId", "QuarterStartDate", "TargetTypeId", "TrackingTemplate", "WebsiteCoverage", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_ElementAdUsagePerformance": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdDescription2", "AdExtensionId", "AdExtensionItemId", "AdExtensionPropertyValue", "AdExtensionTypeId", "AdExtensionTypeName", "AdExtensionVersion", "AdGroupStatusName", "AdId", "AdStatusName", "AdTitle", "AdTitle1", "AdTitle2", "AdTitle3", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "ElementId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelName", "ProductId", "ProductName", "PublisherURL", "RelationshipId", "SOSId", "TargetTypeName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdExtImpressionCnt", "AdImpressionCnt", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SecondaryClickCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "DataForReport", "TargetValueId"]}, "rpt_ElementKeywordUsagePerformance": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdExtensionId", "AdExtensionTypeId", "AdExtensionTypeName", "AdExtensionVersion", "AdGroupStatusName", "AdId", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "ElementId", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderId", "KeywordOrderName", "KeywordStatusName", "LCID", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "ProductId", "ProductName", "RelationshipId", "SOSId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdExtImpressionCnt", "AdImpressionCnt", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SecondaryClickCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "AdExtensionItemId", "DataForReport"]}, "rpt_FeedItemSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AdId", "CampaignId", "CampaignName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "FeedId", "FeedItemId", "GregorianDate", "HourNum", "HourOfDay", "MonthStartDate", "OrderId", "QuarterStartDate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdExtensionTypeId", "AdScenarioType", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "AudienceId", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "<PERSON>ed<PERSON><PERSON><PERSON>", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_GoalsFunnelsSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "CampaignId", "CampaignName", "CampaignStatusName", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "GleamTypeName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "KeywordStatusName", "MonthStartDate", "OrderId", "OrderItemId", "ProductId", "ProductName", "StatusName", "UserDim1Id", "UserDim2Id", "UserDim3Id", "UserDim4Id", "UserDim5Id", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AllConversionCnt", "AllConversionCredit", "AssistCnt", "ConversionCnt", "ConversionCredit", "ExtendedCost", "FullAdvertiserReportedRevenueAdjustment", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "Stage1Cnt", "Stage2Cnt", "Stage3Cnt", "Stage4Cnt", "Stage5Cnt", "TotalConversionCnt", "TotalConversionCredit", "TotalCost", "UniqueConversionCnt", "UniqueConversionCredit", "ConversionLag"]}, "rpt_GoalsFunnelsSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "GleamTypeName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "KeywordStatusName", "MonthStartDate", "OrderId", "OrderItemId", "ProductId", "ProductName", "StatusName", "UserDim1Id", "UserDim2Id", "UserDim3Id", "UserDim4Id", "UserDim5Id", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AllConversionCnt", "AllConversionCredit", "AssistCnt", "ConversionCnt", "ConversionCredit", "ExtendedCost", "FullAdvertiserReportedRevenueAdjustment", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "Stage1Cnt", "Stage2Cnt", "Stage3Cnt", "Stage4Cnt", "Stage5Cnt", "TotalConversionCnt", "TotalConversionCredit", "TotalCost", "UniqueConversionCnt", "UniqueConversionCredit", "ConversionLag"]}, "rpt_HotelGroupSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AdvancedBookingWindow", "BiddingSchemeId", "CampaignHotelId", "CampaignId", "CampaignName", "CampaignStatusName", "CheckInDate", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "FinalUrlSuffix", "GregorianDate", "HotelGroup", "HotelListingId", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LengthOfStay", "MaxCPC", "MonthStartDate", "NodeId", "OrderId", "PartitionType", "PublisherCountryId", "SiteType", "SlotId", "SourceHotelId", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AssistCnt", "BasePrice", "BookedAdvancedBookingWindow", "ClickCnt", "ConversionCnt", "ImpressionCnt", "TotalAmount", "TotalAmountUSD", "TotalBookedNights", "TotalPosition", "TotalPrice", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "TotalPartnerClick"]}, "rpt_HotelGroupSummary_is": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdvancedBookingWindow", "BiddingSchemeId", "CampaignHotelId", "CampaignId", "CampaignName", "CampaignStatusName", "CheckInDate", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "DeviceTypeName", "FinalUrlSuffix", "GregorianDate", "HotelGroup", "HotelListingId", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LengthOfStay", "MaxCPC", "MonthStartDate", "NodeId", "OrderId", "PartitionType", "PublisherCountryId", "SiteType", "SlotId", "SourceHotelId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_HotelSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AdvancedBookingWindow", "AdvertiserHotelId", "BiddingSchemeId", "Brand", "CampaignHotelId", "CampaignId", "CampaignName", "CampaignStatusName", "Category", "CheckInDate", "CheckInDateDayOfWeek", "CityName", "CountryName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "FinalUrlSuffix", "GregorianDate", "HotelListingId", "HotelName", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LengthOfStay", "MonthStartDate", "OrderId", "PublisherCountryId", "SiteType", "SlotId", "SourceHotelId", "StarRating", "SubGeographyName", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "UserCountryCode", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AssistCnt", "BasePrice", "BookedAdvancedBookingWindow", "ClickCnt", "ConversionCnt", "ImpressionCnt", "TotalAmount", "TotalAmountUSD", "TotalBookedNights", "TotalPosition", "TotalPrice", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "TotalPartnerClick"]}, "rpt_HotelSummary_is": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdvancedBookingWindow", "AdvertiserHotelId", "BiddingSchemeId", "Brand", "CampaignHotelId", "CampaignId", "CampaignName", "CampaignStatusName", "Category", "CheckInDate", "CheckInDateDayOfWeek", "CityName", "CountryName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "DeviceTypeName", "FinalUrlSuffix", "GregorianDate", "HotelListingId", "HotelName", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LengthOfStay", "MonthStartDate", "OrderId", "PublisherCountryId", "SiteType", "SlotId", "SourceHotelId", "StarRating", "SubGeographyName", "TrackingTemplate", "UserCountryCode", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_HotelVerticalBookingDetails": {"Dimensions": ["AdvertiserHotelId", "BookingDate", "BookingReferenceNumber", "CheckInDate", "<PERSON><PERSON><PERSON>", "DayOfWeek", "GregorianDate", "MonthStartDate", "QuarterStartDate", "VerticalCampaignId", "VerticalCampaignName", "VerticalItemGroupId", "VerticalItemGroupName", "VerticalItemId", "VerticalItemName", "WeekStartDate", "YearNum"], "Measures": ["AdvertiserVerticalItemId", "BasePrice", "BookedCheckInDate", "HourNum", "TotalBookedNights", "TotalPrice"]}, "rpt_HotelVerticalDetails": {"Dimensions": ["AccountId", "AdvancedBookingWindow", "AdvertiserHotelId", "AssociationCount", "AverageCPC", "AverageCPCUSD", "AverageSlotPosition", "AverageTotalPosition", "AvgBookedABW", "AvgBookedNights", "BidSource", "BookedABW", "CheckInDate", "CheckinDay", "<PERSON>licks", "ClickShare", "ConversionRate", "Conversions", "CPA", "CTR", "CustomerId", "<PERSON><PERSON><PERSON>", "DateType", "DayOfWeek", "DeviceTypeId", "EffectiveBidAmount", "EffectiveBidTypeId", "EligibleImpression", "GregorianDate", "GrossRevenue", "GrossRevenuePerClick", "GrossRevenuePerConv", "GrossROAS", "HotelCountryCode", "HourNum", "HourOfDay", "Impressions", "ImpressionShare", "LengthOfStay", "MissedImpressions", "MissedImpressionsInSufficientBid", "MissedImpressionsNoBid", "MissedImpressionsNoTax", "MissedImpressionsOther", "MissedImpressionsSpendingCapReached", "MonthStartDate", "NetRevenue", "NetRevenueConv", "NetRevenuePerClick", "NetROAS", "NetworkId", "ParticipationRate", "PublisherCountryCode", "PublisherCountryId", "RelationshipId", "SiteType", "SlotId", "SlotType", "SourceVerticalItemId", "TotalBookedNights", "TotalCost", "TotalCostUSD", "TotalPosition", "VerticalCampaignBidAmount", "VerticalCampaignBidType", "VerticalCampaignBudgetAmount", "VerticalCampaignBudgetPauseType", "VerticalCampaignId", "VerticalCampaignMaximumBid", "VerticalCampaignName", "VerticalCampaignStatus", "VerticalItemBidAmount", "VerticalItemBidType", "VerticalItemGroupBidAmount", "VerticalItemGroupBidSource", "VerticalItemGroupBidType", "VerticalItemGroupEffectiveBidAmount", "VerticalItemGroupEffectiveBidTypeId", "VerticalItemGroupId", "VerticalItemGroupName", "VerticalItemGroupStatus", "VerticalItemId", "VerticalItemName", "VerticalItemStatus", "WeekStartDate", "YearNum"], "Measures": ["AssistCount", "BasePrice", "BookedAdvancedBookingWindow", "ClickCnt", "ConversionCount", "ConversionCredit", "ImpressionCnt", "PartnerClick", "PartnerEligibleImpression", "PartnerImpression", "PartnerMissedImpression", "PartnerMissedImpressionInsufficientBid", "PartnerMissedImpressionNoBid", "PartnerMissedImpressionNoTax", "PartnerMissedImpressionOther", "PartnerMissedImpressionSpendingCapReached", "TotalAmount", "TotalAmountUSD", "TotalPartnerClick", "TotalPrice", "TotalSlotPosition"]}, "rpt_HotelVerticalDetails_campaign": {"Dimensions": ["AssociationCount", "AverageCPC", "AverageCPCUSD", "AverageSlotPosition", "AverageTotalPosition", "AvgBookedABW", "AvgBookedNights", "BookedABW", "<PERSON>licks", "ClickShare", "ConversionRate", "Conversions", "CPA", "CTR", "EligibleImpression", "GrossRevenue", "GrossRevenuePerClick", "GrossRevenuePerConv", "GrossROAS", "Impressions", "ImpressionShare", "MissedImpressions", "MissedImpressionsInSufficientBid", "MissedImpressionsNoBid", "MissedImpressionsNoTax", "MissedImpressionsOther", "MissedImpressionsSpendingCapReached", "NetRevenue", "NetRevenueConv", "NetRevenuePerClick", "NetROAS", "ParticipationRate", "TotalBookedNights", "TotalCost", "TotalCostUSD", "TotalPosition", "VerticalCampaignBidAmount", "VerticalCampaignBidType", "VerticalCampaignBudgetAmount", "VerticalCampaignBudgetPauseType", "VerticalCampaignId", "VerticalCampaignMaximumBid", "VerticalCampaignName", "VerticalCampaignStatus"], "Measures": []}, "rpt_HotelVerticalDetails_VerticalGroup": {"Dimensions": ["AssociationCount", "AverageCPC", "AverageCPCUSD", "AverageSlotPosition", "AverageTotalPosition", "AvgBookedABW", "AvgBookedNights", "BookedABW", "<PERSON>licks", "ClickShare", "ConversionRate", "Conversions", "CPA", "CTR", "EligibleImpression", "GrossRevenue", "GrossRevenuePerClick", "GrossRevenuePerConv", "GrossROAS", "Impressions", "ImpressionShare", "MissedImpressions", "MissedImpressionsInSufficientBid", "MissedImpressionsNoBid", "MissedImpressionsNoTax", "MissedImpressionsOther", "MissedImpressionsSpendingCapReached", "NetRevenue", "NetRevenueConv", "NetRevenuePerClick", "NetROAS", "ParticipationRate", "TotalBookedNights", "TotalCost", "TotalCostUSD", "TotalPosition", "VerticalCampaignBidAmount", "VerticalCampaignBidType", "VerticalCampaignBudgetAmount", "VerticalCampaignBudgetPauseType", "VerticalCampaignId", "VerticalCampaignMaximumBid", "VerticalCampaignName", "VerticalCampaignStatus", "VerticalItemGroupBidAmount", "VerticalItemGroupBidSource", "VerticalItemGroupBidType", "VerticalItemGroupEffectiveBidAmount", "VerticalItemGroupEffectiveBidTypeId", "VerticalItemGroupId", "VerticalItemGroupName", "VerticalItemGroupStatus"], "Measures": []}, "rpt_HotelVerticalDetails_VerticalItem": {"Dimensions": ["AdvertiserHotelId", "AverageCPC", "AverageCPCUSD", "AverageSlotPosition", "AverageTotalPosition", "AvgBookedABW", "AvgBookedNights", "BidSource", "BookedABW", "<PERSON>licks", "ClickShare", "ConversionRate", "Conversions", "CPA", "CTR", "EffectiveBidAmount", "EffectiveBidTypeId", "EligibleImpression", "GrossRevenue", "GrossRevenuePerClick", "GrossRevenuePerConv", "GrossROAS", "HotelCountryCode", "Impressions", "ImpressionShare", "MissedImpressions", "MissedImpressionsInSufficientBid", "MissedImpressionsNoBid", "MissedImpressionsNoTax", "MissedImpressionsOther", "MissedImpressionsSpendingCapReached", "NetRevenue", "NetRevenueConv", "NetRevenuePerClick", "NetROAS", "ParticipationRate", "TotalBookedNights", "TotalCost", "TotalCostUSD", "TotalPosition", "VerticalCampaignBidAmount", "VerticalCampaignBidType", "VerticalCampaignBudgetAmount", "VerticalCampaignBudgetPauseType", "VerticalCampaignId", "VerticalCampaignMaximumBid", "VerticalCampaignName", "VerticalCampaignStatus", "VerticalItemBidAmount", "VerticalItemBidType", "VerticalItemGroupBidAmount", "VerticalItemGroupBidSource", "VerticalItemGroupBidType", "VerticalItemGroupEffectiveBidAmount", "VerticalItemGroupEffectiveBidTypeId", "VerticalItemGroupId", "VerticalItemGroupName", "VerticalItemGroupStatus", "VerticalItemId", "VerticalItemName", "VerticalItemStatus"], "Measures": []}, "rpt_KeywordActivitySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdScenarioType", "AdTypeId", "AdTypeName", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "ElementId", "FinalAppUrl", "FinalMobileUrl", "FinalURL", "FinalUrlSuffix", "GleamTypeName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "Keyword", "KeywordLabels", "KeywordOrderName", "KeywordStatusName", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "Mainline1Bid", "MainlineBid", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityImpact", "QualityImpactName", "QualityScore", "QualityScoreName", "RelationshipId", "SidebarBid", "SOSId", "StatusName", "TargetTypeId", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits"]}, "rpt_KeywordActPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "ElementId", "GleamTypeName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "Keyword", "KeywordLabels", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "ORVRating", "ORVRatingName", "PagePositionId", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityImpact", "QualityImpactName", "QualityScore", "QualityScoreName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt"]}, "rpt_KeywordImpressionsharePerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CategoryId", "CategoryName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GleamTypeName", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "Keyword", "KeywordLabels", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "ORVRating", "ORVRatingName", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityImpact", "QualityImpactName", "QualityScore", "QualityScoreName", "RelationshipId", "StatusName", "TargetTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_KeywordPerf_UCM": {"Dimensions": ["AccountId", "AdId", "AdScenarioType", "BiddedMatchTypeId", "CampaignId", "ClickTypeId", "CountryCode", "CurrencyId", "CustomerId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeId", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "RelationshipId", "SOSId", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_LocationActivitySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CityName", "ClickTypeId", "CountryCode", "CountryCodeCode", "CountryId", "CountryName", "County", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GeoLocationId", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourBucket", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "LocationId", "MatchTypeId", "MediumId", "MediumName", "MetroAreaName", "MonthStartDate", "MostSpecificLocation", "Neighborhood", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductName", "QueryGeoLocationId", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeDesc", "TargetedLocationTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "ZipCode"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BusinessLocationId", "CityLatitude", "CityLongitude", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CountryLatitude", "CountryLongitude", "CountyLatitude", "CountyLongitude", "CountyName", "CPC", "CPM", "DerivedCountryId", "DerivedLocationId", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationTypeId", "ImpressionCnt", "LCID", "MetroAreaLatitude", "MetroAreaLongitude", "NeighborhoodLatitude", "NeighborhoodLongitude", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PostalCode", "PostalCodeLatitude", "PostalCodeLongitude", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "SubGeographyLatitude", "SubGeographyLongitude", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_LocationActivitySummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CityName", "ClickTypeId", "CountryCode", "CountryCodeCode", "CountryId", "CountryName", "County", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GeoLocationId", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourBucket", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "LocationId", "MatchTypeId", "MediumId", "MediumName", "MetroAreaName", "MonthStartDate", "MostSpecificLocation", "Neighborhood", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductName", "QueryGeoLocationId", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeDesc", "TargetedLocationTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "ZipCode"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BusinessLocationId", "CityLatitude", "CityLongitude", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CountryLatitude", "CountryLongitude", "CountyLatitude", "CountyLongitude", "CountyName", "CPC", "CPM", "DerivedCountryId", "DerivedLocationId", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationTypeId", "ImpressionCnt", "LCID", "MetroAreaLatitude", "MetroAreaLongitude", "NeighborhoodLatitude", "NeighborhoodLongitude", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PostalCode", "PostalCodeLatitude", "PostalCodeLongitude", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "SubGeographyLatitude", "SubGeographyLongitude", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_MajorCitySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CityLatitude", "CityLongitude", "CityName", "ClickTypeId", "CountryCode", "CountryCodeCode", "CountryId", "CountryLatitude", "CountryLongitude", "CountryName", "County", "CountyLatitude", "CountyLongitude", "CountyName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DerivedCountryId", "DerivedLocationId", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GeoLocationTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourBucket", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "LocationId", "MatchTypeId", "MediumId", "MediumName", "MetroAreaLatitude", "MetroAreaLongitude", "MetroAreaName", "MonthStartDate", "MostSpecificLocation", "Neighborhood", "NeighborhoodLatitude", "NeighborhoodLongitude", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PostalCode", "PostalCodeLatitude", "PostalCodeLongitude", "PricingModelId", "PricingModelName", "ProductName", "QueryIntentCityName", "QueryIntentCountryName", "QueryIntentCounty", "QueryIntentLocationId", "QueryIntentMetroAreaName", "QueryIntentNeighborhood", "QueryIntentSubGeographyName", "QueryIntentZipCode", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyLatitude", "SubGeographyLongitude", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "ZipCode", "CampaignType"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CPC", "CPM", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationId2", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_MajorCitySummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CityLatitude", "CityLongitude", "CityName", "ClickTypeId", "CountryCode", "CountryCodeCode", "CountryId", "CountryLatitude", "CountryLongitude", "CountryName", "County", "CountyLatitude", "CountyLongitude", "CountyName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DerivedCountryId", "DerivedLocationId", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GeoLocationTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourBucket", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "LocationId", "MatchTypeId", "MediumId", "MediumName", "MetroAreaLatitude", "MetroAreaLongitude", "MetroAreaName", "MonthStartDate", "MostSpecificLocation", "Neighborhood", "NeighborhoodLatitude", "NeighborhoodLongitude", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PostalCode", "PostalCodeLatitude", "PostalCodeLongitude", "PricingModelId", "PricingModelName", "ProductName", "QueryIntentCityName", "QueryIntentCountryName", "QueryIntentCounty", "QueryIntentLocationId", "QueryIntentMetroAreaName", "QueryIntentNeighborhood", "QueryIntentSubGeographyName", "QueryIntentZipCode", "QueryLocationId", "<PERSON><PERSON>", "RelationshipId", "SubGeographyLatitude", "SubGeographyLongitude", "SubGeographyName", "TargetedLocation", "TargetedLocationTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "ZipCode"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CPC", "CPM", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "GeoLocationId2", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_MSXAccountUsageSummary": {"Dimensions": ["DayOfWeek", "DeliveredCountry", "DeliveredMarket", "<PERSON><PERSON>", "GregorianDate", "HourNum", "HourOfDay", "MonthStartDate", "PageType", "PlacementId", "Provider", "Publisher", "QuarterStartDate", "Region", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdClickCnt", "ConversionCnt", "ServedImpressionCnt", "ServedRevenueAmount", "ViewedImpressionCnt"]}, "rpt_OrderImpressionSharePerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupLabels", "AdGroupStatusName", "AdGroupTypeId", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityScore", "QualityScoreName", "RelationshipId", "StatusName", "TargetTypeId", "TargetTypeName", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TopImpressionCnt", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_OrderPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupLabels", "AdGroupStatusName", "AdGroupTypeId", "AdScenarioType", "AdUnitId", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "KeywordOrderName", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityScore", "QualityScoreName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt"]}, "rpt_ProductDimensionSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "ChannelType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "Gtin", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "Mpn", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetCountries", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "AbsoluteTopImpressionCnt", "AuctionParticipantClickCnt", "BenchmarkBid_Denominator", "BenchmarkBid_Numerator", "BenchmarkCTR_Denominator", "BenchmarkCTR_Numerator", "BudgetFilteredImpressionCnt", "<PERSON>licks", "EligibleImpressionCnt", "RankFilteredImpressionCnt"]}, "rpt_ProductDimensionSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ChannelType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "Gtin", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "Mpn", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetCountries", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "AbsoluteTopImpressionCnt", "AuctionParticipantClickCnt", "BenchmarkBid_Denominator", "BenchmarkBid_Numerator", "BenchmarkCTR_Denominator", "BenchmarkCTR_Numerator", "BudgetFilteredImpressionCnt", "<PERSON>licks", "EligibleImpressionCnt", "RankFilteredImpressionCnt"]}, "rpt_ProductOfferActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CategoryId", "CategoryName", "ChannelTypeId", "ClickTypeId", "CollectionId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageCode", "LanguageName", "LocalStoreCode", "MatchTypeId", "MediumId", "MediumName", "MerchantProductId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductName", "ProductOfferId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetTypeId", "TargetTypeName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "rpt_ProductPartitionHistoricalSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "rpt_ProductPartitionHistoricalSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "rpt_ProductPartitionMatchCnt": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "CampaignId", "CampaignName", "CurrencyCode", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "MonthStartDate", "NodeId", "OrderId", "OrderItemId", "PartitionType", "ProductGroup", "ProductGroup_V1", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["MatchedOfferCampCount", "MatchedOfferOrderCount", "MatchedOfferOrderitemCount"]}, "rpt_ProductPartitionMatchCntV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AssetGroupId", "AssetGroupName", "CampaignId", "CampaignName", "CampaignType", "CurrencyCode", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "MonthStartDate", "NodeId", "OrderId", "OrderItemId", "PartitionType", "ProductGroup", "ProductGroup_V1", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["MatchedOfferCampCount", "MatchedOfferOrderCount", "MatchedOfferOrderitemCount"]}, "rpt_ProductPartitionSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "ChannelType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "AbsoluteTopImpressionCnt", "AuctionParticipantClickCnt", "BenchmarkBid_Denominator", "BenchmarkBid_Numerator", "BenchmarkCTR_Denominator", "BenchmarkCTR_Numerator", "BudgetFilteredImpressionCnt", "<PERSON>licks", "EligibleImpressionCnt", "RankFilteredImpressionCnt"]}, "rpt_ProductPartitionSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdDescription", "AdGroupStatusName", "AdId", "AdName", "AdStatus", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "Brand", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ChannelType", "ChannelTypeId", "ClickTypeId", "CollectionId", "Condition", "CountryOfSaleName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomLabel0", "CustomLabel1", "CustomLabel2", "CustomLabel3", "CustomLabel4", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceTypeId", "DeviceTypeName", "DisplayURL", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderId", "KeywordOrderName", "LanguageName", "LocalStoreCode", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MerchantId", "MerchantProductId", "MonthStartDate", "NetworkId", "NodeId", "OfferCategoryL1", "OfferCategoryL2", "OfferCategoryL3", "OfferCategoryL4", "OfferCategoryL5", "OrderId", "OrderItemId", "PagePositionId", "PagePositionId2", "Param1", "Param2", "Param3", "PartitionType", "Price", "PricingModelId", "PricingModelName", "ProductBoughtTitle", "ProductGroup", "ProductGroup_V1", "ProductName", "ProductOfferId", "ProductOfferLanguageName", "ProductTypeL1", "ProductTypeL2", "ProductTypeL3", "ProductTypeL4", "ProductTypeL5", "ProviderId", "Purchased<PERSON><PERSON>", "RelationshipId", "<PERSON><PERSON><PERSON><PERSON>", "TargetTypeId", "Title", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeImpressionCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "QuantityBought", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition", "AbsoluteTopImpressionCnt", "AuctionParticipantClickCnt", "BenchmarkBid_Denominator", "BenchmarkBid_Numerator", "BenchmarkCTR_Denominator", "BenchmarkCTR_Numerator", "BudgetFilteredImpressionCnt", "<PERSON>licks", "EligibleImpressionCnt", "RankFilteredImpressionCnt"]}, "rpt_ProfileSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "CampaignId", "CampaignName", "CampaignStatusName", "CompanyId", "CompanyName", "CompanySizeId", "CompanySizeName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DistributionChannelId", "DistributionChannelName", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "IndustryId", "IndustryName", "JobFunctionId", "JobFunctionName", "KeywordOrderName", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderName", "PagePositionId", "QuarterStartDate", "SOSId", "TargetCompanyId", "TargetCompanySizeId", "TargetIndustryId", "TargetJobFunctionId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_PublisherPlacementSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdTypeId", "AdTypeName", "AppBundle", "AppName", "AppStoreUrl", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "PropertyUrl", "PublisherId", "PublisherURL", "RelationshipId", "TargetTypeName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_PublisherPlacementSummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AppBundle", "AppName", "AppStoreUrl", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Keyword", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "PropertyUrl", "PublisherId", "PublisherURL", "RelationshipId", "TargetTypeName", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "rpt_SearchInsightBySearchCategoryPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdScenarioType", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "KeywordOrderName", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchCategory", "SearchQuery", "SearchSubCategory", "SearchVolume", "SOSId", "SubMatchTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_SearchInsightBySearchTermPerf": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdScenarioType", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "KeywordOrderName", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchCategory", "SearchQuery", "SearchSubCategory", "SearchVolume", "SOSId", "SubMatchTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_SearchQuerySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdScenarioType", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AutoTarget", "AutoTargetId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "Description", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "DSAAdTitle", "DSACategoryList", "DSADestinationURL", "DSALandingPageTitle", "ElementId", "FeedURL", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "Keyword", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "PagePositionId2", "PricingModelId", "ProductTarget", "ProductTargetId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchQuery", "SOSId", "SubMatchTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_SearchQuerySummaryV2": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdScenarioType", "AdStatusName", "AdTitle", "AdTypeId", "AdTypeName", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "AutoTarget", "AutoTargetId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "Description", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "DSAAdTitle", "DSACategoryList", "DSADestinationURL", "DSALandingPageTitle", "ElementId", "FeedURL", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "Keyword", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "OrderName", "PagePositionId", "PagePositionId2", "PricingModelId", "ProductTarget", "ProductTargetId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchQuery", "SOSId", "SubMatchTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_SiteActivitySummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdScenarioType", "AdStatusName", "AdTypeId", "AdTypeName", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "ElementId", "FinalAppUrl", "FinalMobileUrl", "FinalURL", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "LCID", "LifeCycleStatusId", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "SiteId", "SiteName", "SOSId", "TargetTypeId", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": []}, "prc_CampaignQualityScore_ui": {"Dimensions": ["CampaignId"], "Measures": ["ORVRating", "PClickRating", "QBRRating", "QualityScore"]}, "prc_KeywordQualityScore_ui": {"Dimensions": ["OrderItemId", "BiddedMatchTypeId"], "Measures": ["ORVRating", "PClickRating", "QBRRating", "QualityImpact", "QualityScore"]}, "prc_FeedItemAdExtensionSummary_ui": {"Dimensions": ["FeedItemId", "CampaignId", "OrderId", "AdId", "AdScenarioType"], "Measures": ["AdExtensionTypeId", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "AudienceId", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "<PERSON>ed<PERSON><PERSON><PERSON>", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_OrderQualityScore_ui": {"Dimensions": ["AccountId", "CampaignId", "OrderId"], "Measures": ["ORVRating", "PClickRating", "QBRRating", "QualityScore"]}, "prc_AdExtensionsByOrderV2_ui": {"Dimensions": ["AdExtensionId", "CampaignId", "OrderId", "MediumId", "ClickTypeId", "GoalId", "GoalTypeId", "NetworkId", "PagePositionId", "TargetValueId", "DeviceTypeId", "AdExtensionTypeId", "SOSId", "DeviceOSId", "PagePositionId2", "GregorianDate", "HourNum", "HourOfDay", "QuarterStartDate", "WeekStartDate", "WeekStartDateMonday", "YearNum", "MonthStartDate", "<PERSON><PERSON><PERSON>", "DayOfWeek", "MatchTypeId", "BiddedMatchTypeId", "TargetTypeId"], "Measures": ["AdExtensionItemId", "AdExtensionVersion", "AdExtImpressionCnt", "AdImpressionCnt", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DataForReport", "DeviceOSID2", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "RelationshipId", "SecondaryClickCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition"]}, "prc_AdExtensionsOrderAssociation_ui": {"Dimensions": ["AdExtensionGroupId", "CampaignId", "OrderId", "AdExtensionId", "MediumId", "ClickTypeId", "GoalId", "GoalTypeId", "NetworkId", "PagePositionId", "TargetValueId", "DeviceTypeId", "AdExtensionTypeId", "SOSId", "GregorianDate", "HourNum", "HourOfDay", "QuarterStartDate", "WeekStartDate", "WeekStartDateMonday", "YearNum", "MonthStartDate", "<PERSON><PERSON><PERSON>", "DayOfWeek", "PagePositionId2", "MatchTypeId", "BiddedMatchTypeId", "TargetTypeId"], "Measures": ["AdExtensionItemId", "AdExtensionVersion", "AdExtImpressionCnt", "AdImpressionCnt", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DataForReport", "DeviceOSID2", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "RelationshipId", "SecondaryClickCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition"]}, "prc_AgeGenderSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "AdvertisingChannelTypeId", "AgeBucketDesc", "AgeBucketId", "BaseCampaignId", "CampaignId", "CampaignName", "CampaignStatusName", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DistributionChannelId", "DistributionChannelName", "GenderId", "GenderName", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "LanguageCode", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "PricingModelId", "QuarterStartDate", "RelationshipId", "SOSId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_AssetAdGroupSnapshotSummary_ui": {"Dimensions": [], "Measures": []}, "prc_AssetCombinationSummary_ui": {"Dimensions": ["OrderId", "AdId", "Headline1AssetId", "Headline2AssetId", "Headline3AssetId", "Description1AssetId", "Description2AssetId", "HeadlineAssetId", "LongHeadlineAssetId", "DescriptionAssetId", "ImageId", "LogoImageId"], "Measures": ["Impressions", "<PERSON>licks", "TotalCost", "Conversions"]}, "prc_AssetSnapShotSummary_ui": {"Dimensions": [], "Measures": []}, "prc_AssetSummary_ui": {"Dimensions": ["OrderId", "AdId", "AssetId", "AdAssetAssociationTypeId", "TargetTypeId", "TargetValueId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_AudienceSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AssociationId", "AssociationLevel", "AssociationStatus", "AudienceId", "AudienceName", "AudienceTypeId", "BaseCampaignId", "BidAdjustment", "CampaignId", "CampaignName", "CampaignStatusName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "TargetSetting", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "QuarterStartDate"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_CampaignExperimentSummary_ui": {"Dimensions": ["AccountId", "CampaignId", "CustomerId"], "Measures": ["AdvertiserReportedRevenue", "ClickCnt", "ClickSumOfSquare", "ClickTimesConversion", "ClickTimesConversionCredit", "ClickTimesImpression", "ClickTimesRevenue", "ConversionCnt", "ConversionCredit", "ConversionCreditSumOfSquare", "ConversionCreditTimesRevenue", "ConversionSumOfSquare", "ConversionTimesRevenue", "ConversionValueCnt", "ConversionValueSumOfSquare", "ConversionValueTimesRevenue", "FullClickTimesConversion", "FullClickTimesConversionCredit", "FullConversionCnt", "FullConversionCredit", "FullConversionCreditSumOfSquare", "FullConversionCreditTimesRevenue", "FullConversionSumOfSquare", "FullConversionTimesRevenue", "FullConversionValueCnt", "FullConversionValueSumOfSquare", "FullConversionValueTimesRevenue", "FullViewConversionCnt", "FullViewConversionCredit", "FullViewConversionCreditSumOfSquare", "HourNum", "ImpressionCnt", "ImpressionSumOfSquare", "PositionSumOfSquare", "RevenueSumOfSquare", "SearchAbsTopPositionSumOfSquare", "SearchTopPositionSumOfSquare", "SearchUniqueImpressionCnt", "SearchUniqueImpressionSumOfSquare", "ShoppingAbsTopPositionSumOfSquare", "ShoppingUniqueImpressionCnt", "ShoppingUniqueImpressionSumOfSquare", "TotalConversionCnt", "TotalConversionCredit", "TotalConversionCreditSumOfSquare", "TotalConversionSumOfSquare", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "EndDate<PERSON>ey", "AdvertiserReportedRevenue", "ConversionValueCnt", "FullConversionValueCnt"]}, "prc_CheckAccountFactDataExists": {"Dimensions": [], "Measures": []}, "prc_EstimateRows_Dim": {"Dimensions": [], "Measures": []}, "prc_GetAccountQualityScore": {"Dimensions": [], "Measures": []}, "prc_GetAdLandingPageUrlDimensions_ui": {"Dimensions": [], "Measures": []}, "prc_GetAssetTextByAccountIdAssetId_v2": {"Dimensions": [], "Measures": []}, "prc_GetAssetTextByIds_ui": {"Dimensions": [], "Measures": []}, "prc_GetAudienceRecommendation": {"Dimensions": [], "Measures": []}, "prc_GetAutoTargetWebsiteCoverage": {"Dimensions": [], "Measures": []}, "prc_GetBidSuggestion": {"Dimensions": [], "Measures": []}, "prc_GetCampaignConvData": {"Dimensions": [], "Measures": []}, "prc_GetChangeHistorySummary_ui": {"Dimensions": [], "Measures": []}, "prc_GetChangeHistorySummaryByUser": {"Dimensions": [], "Measures": []}, "prc_GetChangeHistorySummaryCount_ui": {"Dimensions": [], "Measures": []}, "prc_GetEntityValuesByEntityIds": {"Dimensions": [], "Measures": []}, "prc_GetLastLoadCompleteTime": {"Dimensions": [], "Measures": []}, "prc_GetMeteredCallDetails": {"Dimensions": ["AccountId", "AdId", "AreaCode", "CallEndReason", "CallEndStatus", "CallEndTime", "CallId", "CallStartTime", "CampaignId", "CampaignName", "CampaignStatusName", "CityName", "ClickTypeId", "<PERSON><PERSON><PERSON>", "DeviceOSId", "DeviceTypeId", "EndTime", "HourNum", "IsOfflineCall", "KeywordOrderName", "LoadTime", "OrderId", "StartTime", "SubGeographyName", "Account<PERSON><PERSON>", "AccountStatusName", "AdGroupStatusName", "CallTypeName", "GregorianDate", "CallReasonName", "Region", "City"], "Measures": ["CallDuration", "PagePositionId", "PhoneCost", "RelationshipId"]}, "prc_GetNegativeKeywordConflictDetails": {"Dimensions": [], "Measures": []}, "prc_GetNegativeKeywordConflictStatus": {"Dimensions": [], "Measures": []}, "prc_GetPGCriteriaSelectionSummary_Perf_ui": {"Dimensions": ["AccountId", "CampaignId", "OrderId", "ProductOfferId", "ChannelTypeId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BiddedMatchTypeId", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeId", "ClickTypeImpressionCnt", "CollectionId", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "DistributionChannelId", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "InstallCnt", "LocalStoreCode", "MediumId", "PagePositionId", "PricingModelId", "ProductBoughtTitle", "Purchased<PERSON><PERSON>", "QuantityBought", "RelationshipId", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "prc_GetProcedureDetails": {"Dimensions": [], "Measures": []}, "prc_GetProductOfferCountsAccountCampaign_ui": {"Dimensions": [], "Measures": []}, "prc_GetProductTargetsCountsAccountCampaign_ui": {"Dimensions": [], "Measures": []}, "prc_GetSearchPhraseSummary": {"Dimensions": ["SearchPhrase", "OrderId", "CampaignId", "AccountId"], "Measures": ["ClickCnt", "HourNum", "ImpressionCnt", "TotalAmount"]}, "prc_GetSearchQuerySummary": {"Dimensions": [], "Measures": ["AdLandingPageUrlId", "AdScenarioType", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BiddedMatchTypeId", "ClickCnt", "ClickTypeId", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "DeviceOSID2", "DistributionChannelId", "ElementId", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "IsPageUrl", "MediumId", "PagePositionId", "PricingModelId", "Query", "QueryHash", "RelationshipId", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "SOSId", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "prc_GetTaskStatus_ui": {"Dimensions": [], "Measures": []}, "prc_GetUsersOfChanges_V3": {"Dimensions": [], "Measures": []}, "prc_LeadFormExtensionDetails_ui": {"Dimensions": [], "Measures": []}, "prc_ProductGroupSummary_V1_ui": {"Dimensions": ["AccountId", "CampaignId", "OrderId", "ProductOfferId", "ChannelTypeId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BiddedMatchTypeId", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeId", "ClickTypeImpressionCnt", "CollectionId", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "DistributionChannelId", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "InstallCnt", "LocalStoreCode", "MediumId", "PagePositionId", "PricingModelId", "ProductBoughtTitle", "Purchased<PERSON><PERSON>", "QuantityBought", "RelationshipId", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "prc_ProductGroupSummaryForAccountCampaignOrder_ui": {"Dimensions": ["AccountId", "CampaignId", "OrderId", "ProductOfferId", "ChannelTypeId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "BiddedMatchTypeId", "ClickCnt", "ClickTypeClickCnt", "ClickTypeConversionCnt", "ClickTypeConversionCredit", "ClickTypeId", "ClickTypeImpressionCnt", "CollectionId", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "CooperativeClickCnt", "CooperativeConversionCnt", "CooperativeConversionCredit", "CooperativeImpressionCnt", "DistributionChannelId", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "HourNum", "ImpressionCnt", "InstallCnt", "LocalStoreCode", "MediumId", "PagePositionId", "PricingModelId", "ProductBoughtTitle", "Purchased<PERSON><PERSON>", "QuantityBought", "RelationshipId", "SalesCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "TotalSearchAbsTopPosition"]}, "prc_ProfileSummary_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "CampaignId", "CampaignName", "CampaignStatusName", "CompanyId", "CompanyName", "CompanySizeId", "CompanySizeName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "DistributionChannelId", "DistributionChannelName", "GoalCategory", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "IndustryId", "IndustryName", "JobFunctionId", "JobFunctionName", "KeywordOrderName", "LanguageName", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderName", "PagePositionId", "QuarterStartDate", "SOSId", "TargetCompanyId", "TargetCompanySizeId", "TargetIndustryId", "TargetJobFunctionId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_PublicLatestCompleteDTimLoad": {"Dimensions": [], "Measures": []}, "prc_TargetSummaryV2_ui": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AssociationId", "AssociationLevel", "AssociationStatus", "AudienceId", "AudienceName", "AudienceTypeId", "BaseCampaignId", "BidAdjustment", "CampaignId", "CampaignName", "CampaignStatusName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "KeywordOrderName", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "PagePositionId", "TargetSetting", "TargetTypeId", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "QuarterStartDate", "PagePositionId2"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalWatchTime", "ViewCnt"]}, "prc_UpliftSummary_ui": {"Dimensions": [], "Measures": []}, "prc_UpsertUndoneChange": {"Dimensions": [], "Measures": []}, "rpt_AccountActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdPosition", "AdScenarioType", "AdUnitId", "AdvertisingChannelTypeId", "AdvertisingSubChannelTypeId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "CampaignFeatureBitMask", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DeliveryFormatId", "DemandTypeId", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DomainTypeId", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "IsMSANSWFRecord", "LabelColor", "LabelName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "PagePositionId", "PagePositionId2", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "RelationshipId", "SOSId", "TargetTypeId", "TargetTypeName", "TargetValueId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "ImpressionWithPositionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalAmountUSD", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionLag", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "BSCAuctionParticipation", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt"]}, "rpt_CampaignActivity": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdScenarioType", "AdUnitId", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BidStrategyId", "BudgetAmount", "BudgetAssociationStatus", "BudgetId", "BudgetName", "BudgetStatusName", "BudgetType", "CampaignId", "CampaignLabels", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "DailyBudgetAmt", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelName", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "IncrementalBudgetAmt", "LabelColor", "LabelName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityBandId", "QualityScore", "QualityScoreName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum", "DeliveredMatchTypeId"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "Downloads", "ExtendedCost", "FirstLaunches", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "InvalidClickCnt", "InvalidConversionCnt", "InvalidConversionCredit", "InvalidGeneralClickCnt", "InvalidImpressionCnt", "NewCustomerConversionCredit", "NewCustomerRevenue", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "Purchases", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "Subscriptions", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "ConversionValueCnt", "FullConversionValueCnt", "ConversionLag", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AdvertiserClicks", "RelativeAuctionWonCnt", "SameSectionCount", "SameSectionCTR", "Reach1d", "Reach2d", "Reach3d", "Reach4d", "Reach5d", "Reach6d", "Reach7d", "Reach8d", "Reach9d", "Reach10d", "Reach11d", "Reach12d", "Reach13d", "Reach14d", "Reach15d", "Reach16d", "Reach17d", "Reach18d", "Reach19d", "Reach20d", "Reach21d", "Reach22d", "Reach23d", "Reach24d", "Reach25d", "Reach26d", "Reach27d", "Reach28d", "Reach29d", "Reach30d"]}, "rpt_GetAccountInfo": {"Dimensions": [], "Measures": []}, "rpt_GetCampaignInfo": {"Dimensions": [], "Measures": []}, "rpt_GetChangeHistorySummary": {"Dimensions": [], "Measures": []}, "rpt_GetEntityValuesByEntityIds": {"Dimensions": [], "Measures": []}, "rpt_KeywordAuctionSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdScenarioType", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BiddingSchemeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CategoryId", "CategoryName", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeliveredMatchTypeId", "DestinationURL", "DeviceOSId", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "GleamTypeName", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "Keyword", "KeywordLabels", "KeywordOrderName", "KeywordStatusName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MaxCPC", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "ORVRating", "ORVRatingName", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityImpact", "QualityImpactName", "QualityScore", "QualityScoreName", "RelationshipId", "StatusName", "TargetTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum", "AdId", "ClickTypeId", "DeviceOSID2", "ElementId", "GoalCategory", "GoalId", "PagePositionId", "SOSId", "TargetValueId"], "Measures": ["AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TopImpressionCnt", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "MaxCPClick", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalVisits"]}, "rpt_NegativeKeywordConflictsList": {"Dimensions": [], "Measures": []}, "rpt_NegativeKeywordConflictsListV2": {"Dimensions": [], "Measures": []}, "rpt_OrderSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupLabels", "AdGroupStatusName", "AdGroupTypeId", "AdScenarioType", "AdUnitId", "BaseCampaignId", "BiddedMatchTypeDesc", "BiddedMatchTypeId", "BusinessLocationId", "BusinessName", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "CategoryId", "CategoryName", "ClickTypeId", "CurrencyCode", "CurrencyId", "CustomerId", "CustomerName", "CustomParameters", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeliveredMatchTypeDesc", "DeviceOSId", "DeviceOSID2", "DeviceOSName", "DeviceTypeId", "DeviceTypeName", "DistributionChannelId", "DistributionChannelName", "FinalUrlSuffix", "GoalCategory", "GoalId", "GoalName", "GoalTypeId", "GregorianDate", "HistoricalORVRating", "HistoricalORVRatingName", "HistoricalPClickRating", "HistoricalPClickRatingName", "HistoricalQBRRating", "HistoricalQBRRatingName", "HistoricalQualityScore", "HistoricalQualityScoreName", "HourNum", "HourOfDay", "KeywordOrderName", "LabelColor", "LabelName", "LanguageCode", "LanguageName", "MatchTypeDesc", "MatchTypeId", "MediumId", "MediumName", "MonthStartDate", "NetworkId", "OrderId", "ORVRating", "ORVRatingName", "PagePositionId", "PagePositionId2", "PClickRating", "PClickRatingName", "PricingModelId", "PricingModelName", "ProductId", "ProductName", "QBRRating", "QBRRatingName", "QualityScore", "QualityScoreName", "RelationshipId", "SOSId", "StatusName", "TargetTypeId", "TargetTypeName", "TargetValueId", "TrackingTemplate", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "CompletedViewCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewAdvertiserReportedRevenue", "FullViewAdvertiserReportedRevenueAdjustment", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "InstallCnt", "OfflinePhoneCallCnt", "OfflinePhoneCost", "OnlinePhoneCallCnt", "OnlinePhoneCost", "Percent25ViewCnt", "Percent50ViewCnt", "Percent75ViewCnt", "PhoneImpressionCnt", "SalesCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalBounces", "TotalConversionCnt", "TotalConversionCredit", "TotalDuration", "TotalPages", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition", "TotalVisits", "TotalWatchTime", "ViewCnt", "AbsoluteTopImpressionCnt", "AuctionLostToBudgetAbsTopCnt", "AuctionLostToBudgetCnt", "AuctionLostToBudgetTopCnt", "AuctionLostToLandPageRelevanceCnt", "AuctionLostToLowAdQualityCnt", "AuctionLostToMinBidCnt", "AuctionLostToOtherCnt", "AuctionLostToRankAbsTopCnt", "AuctionLostToRankAggCnt", "AuctionLostToRankCnt", "AuctionLostToRankTopCnt", "AuctionParticipantClickCnt", "AuctionParticipantCnt", "AuctionWonCnt", "<PERSON>licks", "EMAuctionParticipation", "EMAuctionWon", "TopImpressionCnt", "AdvertiserClicks", "RelativeAuctionWonCnt", "SameSectionCount", "SameSectionCTR", "Reach1d", "Reach2d", "Reach3d", "Reach4d", "Reach5d", "Reach6d", "Reach7d", "Reach8d", "Reach9d", "Reach10d", "Reach11d", "Reach12d", "Reach13d", "Reach14d", "Reach15d", "Reach16d", "Reach17d", "Reach18d", "Reach19d", "Reach20d", "Reach21d", "Reach22d", "Reach23d", "Reach24d", "Reach25d", "Reach26d", "Reach27d", "Reach28d", "Reach29d", "Reach30d"]}, "rpt_SearchInsightSummary": {"Dimensions": ["AccountId", "Account<PERSON><PERSON>", "AccountNumber", "AccountStatusName", "AdGroupStatusName", "AdId", "AdLandingPageUrlId", "AdScenarioType", "AssetGroupId", "AssetGroupName", "AssetGroupStatusName", "BiddedMatchTypeId", "CampaignId", "CampaignName", "CampaignStatusName", "CampaignType", "ClickTypeId", "CustomerId", "CustomerName", "<PERSON><PERSON><PERSON>", "DayOfWeek", "DeviceOSId", "DeviceOSID2", "DeviceTypeId", "DistributionChannelId", "ElementId", "GoalId", "GoalTypeId", "GregorianDate", "HourNum", "HourOfDay", "Is<PERSON>ther", "IsPageUrl", "KeywordOrderName", "MatchTypeId", "MediumId", "MonthStartDate", "NetworkId", "OrderId", "OrderItemId", "PagePositionId", "PricingModelId", "QuarterStartDate", "Query", "QueryHash", "RelationshipId", "SearchCategory", "SearchQuery", "SearchSubCategory", "SearchVolume", "SOSId", "SubMatchTypeId", "WeekStartDate", "WeekStartDateMonday", "YearNum"], "Measures": ["AdvertiserReportedRevenue", "AdvertiserReportedRevenueAdjustment", "AssistCnt", "ClickCnt", "ConversionCnt", "ConversionCredit", "ConversionEnabledClickCnt", "ConversionEnabledTotalAmount", "ExtendedCost", "FullAdvertiserReportedRevenue", "FullAdvertiserReportedRevenueAdjustment", "FullConversionCnt", "FullConversionCredit", "FullViewConversionCnt", "FullViewConversionCredit", "ImpressionCnt", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt", "TotalAmount", "TotalConversionCnt", "TotalConversionCredit", "TotalPosition", "TotalSearchAbsTopPosition", "TotalSearchTopPosition", "TotalShoppingAbsTopPosition"]}, "rpt_TANegativeKeywordConflictsList": {"Dimensions": [], "Measures": []}, "rpt_UserRoleChangeHistory": {"Dimensions": [], "Measures": []}, "rpt_SearchVerticalCategoryInsightsReport": {"Dimensions": ["ICEL1Id", "ICEL2Id", "ICEL3Id", "CountryCode", "AccountId", "StartDate", "EndDate"], "Measures": []}, "rpt_SearchVerticalCategoryClickShareReport": {"Dimensions": ["ICEL1Id", "ICEL2Id", "ICEL3Id", "CountryCode", "StartDate", "EndDate"], "Measures": []}}