<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClickHouse.Client" Version="7.1.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="BiMetadataGenerator.cs" />
    <Compile Include="BiMetadataConfiguration.cs" />
    <Compile Include="IQueryProvider.cs" />
    <Compile Include="QueryUtility2.cs" />
    <Compile Include="BiMetadataValidator.cs" />
    <Compile Include="Constants2.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Common\Clickhouse\ClickhouseQueryBuilder\ClickhouseQueryBuilder.csproj" />
  </ItemGroup>

</Project>
