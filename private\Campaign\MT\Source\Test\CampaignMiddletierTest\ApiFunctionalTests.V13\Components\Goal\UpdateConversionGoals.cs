﻿using CampaignMiddleTierTest.Framework;
using CampaignMiddleTierTest.Framework.Utilities;
using CampaignTest.ApiFunctionalTests;
using CampaignTest.ApiFunctionalTests.Collections;
using Microsoft.BingAds.CampaignManagement;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ApiFactories = CampaignTest.ApiFunctionalTests.Factories;
using ConversionGoalCollection = CampaignTest.ApiFunctionalTests.Collections.ConversionGoalCollection;
using FieldNames = CampaignTest.Framework.FieldNames;
using ResponseValidator = CampaignTest.ApiFunctionalTests.Validators.ResponseValidator;
using UETTagCollection = CampaignTest.ApiFunctionalTests.Collections.UETTagCollection;


namespace Microsoft.Advertising.Advertiser.APIV13.Goal
{
    [TestClass]
    public class UpdateConversionGoals : CampaignTestBase
    {
        private static CustomerInfo cInfo = GoalsCustomer;
        private static CustomerInfo ViewThroughConversionsCustomerInfo = ViewThroughConversionPilotCustomer;
        private static CustomerInfo MainGoalConversionsCustomerInfo = MainConversionGoalPilotCustomer;
        private static CustomerInfo GoalCategoryPilotCustomerInfo = GoalCategoryPilotCustomer;
        private static CustomerInfo GoalBulkCustomerInfo;
        private static long tagId;
        private static long tagId_ViewThroughConversionCustomer;
        private static long tagId_MainGoalConversionCustomer;
        private static long tagId_GoalCategoryCustomer;
        private static long tagId_GoalBulkCustomer;

        [ClassInitialize]
        public static void Initialize(TestContext context)
        {
            tagId = GetUetTagId(cInfo);
            tagId_ViewThroughConversionCustomer = GetUetTagId(ViewThroughConversionsCustomerInfo);
            tagId_MainGoalConversionCustomer = GetUetTagId(MainGoalConversionsCustomerInfo);
            tagId_GoalCategoryCustomer = GetUetTagId(GoalCategoryPilotCustomerInfo);
        }

        [TestMethod, Priority(2)]
        [SkipInit]
        public void UpdateConversionGoals_GoalBulk()
        {
            var GoalBulkCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnableGoalBulk });
            long tagId_GoalBulkCustomer = GetUetTagId(GoalBulkCustomerInfo);

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Event }, tagId_GoalBulkCustomer);
            goalsCollection.Add_Success(GoalBulkCustomerInfo);

            goalsCollection.Goals[0].Name = "Changed" + StringUtil.GenerateUniqueId();
            goalsCollection.Update_Success(GoalBulkCustomerInfo); // Get_Success is in Update_Success.
        }

        [TestMethod, Priority(2)]
        [SkipInit]
        public void UpdateConversionGoals_EnhancedConversion_Failure_FirstTime()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url }, tagId);
            goalsCollection.Add_Success(customer);

            goalsCollection.Goals[0].IsEnhancedConversionsEnabled = true;
            goalsCollection.Update_Fail(customer);
        }

        [TestMethod, Priority(2)]
        [SkipInit]
        public void UpdateConversionGoals_EnhancedConversion_Success_InPilotAndSecondTime()
        {
            CustomerInfo enhancedConversionsCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions });
            long tagId = GetUetTagId(enhancedConversionsCustomer);

            dynamic goal = ConversionGoalCollection.CreateDestinationGoal(tagId);
            goal.IsEnhancedConversionsEnabled = true;
            dynamic response = ConversionGoalCollection.PostGoal(cInfo: enhancedConversionsCustomer, goal: goal);

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url }, tagId);
            goalsCollection.Goals[0].IsEnhancedConversionsEnabled = true;
            goalsCollection.Add_Success(enhancedConversionsCustomer);
            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), type: ConversionGoalType.Url, enhancedConversionsCustomer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsEnhancedConversionsEnabled);
            Assert.IsNotNull(resultGoals);
            var resultGoal = resultGoals[0] as UrlGoal;
            Assert.IsTrue(resultGoal.IsEnhancedConversionsEnabled);

            goalsCollection.Goals[0].IsEnhancedConversionsEnabled = false;
            goalsCollection.Update_Success(enhancedConversionsCustomer);

            resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), type: ConversionGoalType.Url, enhancedConversionsCustomer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsEnhancedConversionsEnabled);
            Assert.IsNotNull(resultGoals);
            resultGoal = resultGoals[0] as UrlGoal;
            Assert.IsFalse(resultGoal.IsEnhancedConversionsEnabled);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_AllProperty_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url,
                ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.Event, ConversionGoalType.AppInstall, ConversionGoalType.OfflineConversion}, tagId);

            goalsCollection.Add_Success(cInfo);

            SetMsClickIdTaggingAndValidate(cInfo, "false");

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url,
                ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.Event, ConversionGoalType.AppInstall, ConversionGoalType.OfflineConversion}, tagId);
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            updateGoalsCollection.Update_Success(cInfo);

            GetMsClickIdTaggingAndValidate(cInfo, "true");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_SomeProperty_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url,
                ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.Event, ConversionGoalType.AppInstall, ConversionGoalType.OfflineConversion}, tagId);
            goalsCollection.Goals[0].Scope = EntityScope.Account;
            goalsCollection.Goals[3].Scope = EntityScope.Account;
            goalsCollection.Add_Success(cInfo);

            var goals = new List<ConversionGoal>();
            var urlGoal = new UrlGoal();
            urlGoal.Id = goalsCollection.Goals[0].Id;
            urlGoal.Name = "Name:" + DateTime.Now.Ticks;
            urlGoal.GoalCategory = ConversionGoalCategory.Other;
            urlGoal.UrlExpression = "www.bing.com";
            urlGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.FixedValue,
                Value = 2,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid).CurrencyCode
            };
            goals.Add(urlGoal);
            var durationGoal = new DurationGoal();
            durationGoal.Id = goalsCollection.Goals[1].Id;
            durationGoal.CountType = ConversionGoalCountType.Unique;
            durationGoal.GoalCategory = ConversionGoalCategory.Other;
            goals.Add(durationGoal);
            var pageGoal = new PagesViewedPerVisitGoal();
            pageGoal.Id = goalsCollection.Goals[2].Id;
            pageGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.FixedValue,
                Value = 2
            };
            goals.Add(pageGoal);
            var evenGoal = new EventGoal();
            evenGoal.Id = goalsCollection.Goals[3].Id;
            evenGoal.GoalCategory = ConversionGoalCategory.Other;
            evenGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue,
                Value = 2,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid).CurrencyCode
            };
            evenGoal.ActionExpression = "action";
            evenGoal.Value = 2;
            goals.Add(evenGoal);
            var appGoal = new AppInstallGoal();
            appGoal.Id = goalsCollection.Goals[4].Id;
            appGoal.AppStoreId = "abcde";
            appGoal.GoalCategory = ConversionGoalCategory.Download;
            goals.Add(appGoal);
            var offlineGoal = new OfflineConversionGoal();
            offlineGoal.Id = goalsCollection.Goals[5].Id;
            offlineGoal.Name = "offline" + DateTime.Now.Ticks;
            offlineGoal.GoalCategory = ConversionGoalCategory.Other;
            goals.Add(offlineGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPISuccess(response);

            List<object> getConversionGoalCollection = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), cInfo);
            goalsCollection.Goals[0].Name = urlGoal.Name;
            goalsCollection.Goals[0].Revenue = urlGoal.Revenue;
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = urlGoal.UrlExpression;
            goalsCollection.Goals[1].CountType = durationGoal.CountType;
            goalsCollection.Goals[2].Revenue = new ConversionGoalRevenue()
            {
                Type = pageGoal.Revenue.Type,
                Value = pageGoal.Revenue.Value
            };
            var eventGoal = (EventGoal)goalsCollection.Goals[3];
            eventGoal.Revenue = evenGoal.Revenue;
            eventGoal.ActionExpression = evenGoal.ActionExpression;
            eventGoal.Value = evenGoal.Value;
            eventGoal.GoalCategory = ConversionGoalCategory.Other;

            // These null properties should backfilled when updated.
            eventGoal.CategoryExpression = null;
            eventGoal.CategoryOperator = null;
            eventGoal.LabelExpression = null;
            eventGoal.LabelOperator = null;
            eventGoal.ActionOperator = null;
            eventGoal.ValueOperator = null;

            ((AppInstallGoal)goalsCollection.Goals[4]).AppStoreId = appGoal.AppStoreId;
            ((OfflineConversionGoal)goalsCollection.Goals[5]).Name = offlineGoal.Name;

            goalsCollection.AreEqualInSequence(getConversionGoalCollection, isUpdate: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_ChangeGoalLevelFromAccountLevel_Fail()
        {
            DatabaseHelper.EnablePilotFeatures(cInfo.CustomerId, true, Features.EnableGoalLevelChange);
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url }, tagId);
            //the origin scope is account level
            goalsCollection.Goals[0].Scope = EntityScope.Account;
            goalsCollection.Add_Success(cInfo);

            var goals = new List<ConversionGoal>();
            var urlGoal = new UrlGoal();
            //try to change the scope to customer level
            urlGoal.Scope = EntityScope.Customer; 
            urlGoal.Id = goalsCollection.Goals[0].Id;
            urlGoal.Name = "Name:" + DateTime.Now.Ticks;
            urlGoal.UrlExpression = "www.bing.com";
            goals.Add(urlGoal);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);
            object response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPISuccess(response);

            List<object> getConversionGoalCollection = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), cInfo);
            goalsCollection.Goals[0].Name = urlGoal.Name;
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = urlGoal.UrlExpression;
            //the scope should still be account level
            goalsCollection.Goals[0].Scope = EntityScope.Account;
            
            goalsCollection.AreEqualInSequence(getConversionGoalCollection, isUpdate: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [Ignore]
        public void UpdateConversionGoals_ChangeGoalLevelFromCustomerLevel_Fail()
        {
            DatabaseHelper.EnablePilotFeatures(cInfo.CustomerId, true, Features.EnableGoalLevelChange);
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url }, tagId);
            //the origin scope is customer level
            goalsCollection.Goals[0].Scope = EntityScope.Customer;
            goalsCollection.Add_Success(cInfo);

            var goals = new List<ConversionGoal>();
            var urlGoal = new UrlGoal();
            //try to change the scope to account level
            urlGoal.Scope = EntityScope.Account;
            urlGoal.Id = goalsCollection.Goals[0].Id;
            urlGoal.Name = "Name:" + DateTime.Now.Ticks;
            urlGoal.UrlExpression = "www.bing.com";
            goals.Add(urlGoal);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);
            object response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPISuccess(response);

            List<object> getConversionGoalCollection = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), cInfo);
            goalsCollection.Goals[0].Name = urlGoal.Name;
            ((UrlGoal)goalsCollection.Goals[0]).UrlExpression = urlGoal.UrlExpression;
            //the scope should still be customer level
            goalsCollection.Goals[0].Scope = EntityScope.Customer;

            goalsCollection.AreEqualInSequence(getConversionGoalCollection, isUpdate: true);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_Property_ViewThroughConversionWindowinMinutes_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;

            goalsCollection.Add_Success(ViewThroughConversionsCustomerInfo);

            var goals = new List<ConversionGoal>();
            var durationGoal = new DurationGoal();
            durationGoal.Id = goalsCollection.Goals[0].Id;
            durationGoal.ViewThroughConversionWindowInMinutes = 1000;
            durationGoal.GoalCategory = ConversionGoalCategory.Other;
            goals.Add(durationGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(ViewThroughConversionsCustomerInfo);
            ResponseValidator.ValidateAPISuccess(response);

            List<object> getGoals = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), ViewThroughConversionsCustomerInfo, ConversionGoalAdditionalField.ViewThroughConversionWindowInMinutes);
            var expectedValue = durationGoal.ViewThroughConversionWindowInMinutes;
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.AreEqual(expectedValue, realValue, "VTC doesn't match");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_CarryCategory_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId_GoalCategoryCustomer);
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            goalsCollection.Add_Success(GoalCategoryPilotCustomerInfo);

            var goals = new List<ConversionGoal>();
            var offlineGoal = new OfflineConversionGoal();
            offlineGoal.Id = goalsCollection.Goals[0].Id;
            offlineGoal.Name = "update_the_name-" + StringUtil.GenerateUniqueId();
            offlineGoal.GoalCategory = ConversionGoalCategory.Purchase;
            goals.Add(offlineGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(GoalCategoryPilotCustomerInfo);
            ResponseValidator.ValidateAPISuccess(response);
        }

        [Ignore]
        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_NullCategory_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId_GoalCategoryCustomer);
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            goalsCollection.Add_Success(GoalCategoryPilotCustomerInfo);

            var goals = new List<ConversionGoal>();
            var offlineGoal = new OfflineConversionGoal();
            offlineGoal.Id = goalsCollection.Goals[0].Id;
            offlineGoal.Name = "update_the_name"; 
            goals.Add(offlineGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(GoalCategoryPilotCustomerInfo);
            ResponseValidator.ValidateAPISuccess(response);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_NoneCategory_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId_GoalCategoryCustomer);
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            goalsCollection.Add_Success(GoalCategoryPilotCustomerInfo);

            var goals = new List<ConversionGoal>();
            var offlineGoal = new OfflineConversionGoal();
            offlineGoal.Id = goalsCollection.Goals[0].Id;
            offlineGoal.Name = "update_the_name";
            offlineGoal.GoalCategory = ConversionGoalCategory.None;
            goals.Add(offlineGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(GoalCategoryPilotCustomerInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidCategoryForGoalType);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_Property_ConversionGoalSelection_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_MainGoalConversionCustomer);
            goalsCollection.Goals[0].ExcludeFromBidding = true;
            goalsCollection.Add_Success(MainGoalConversionsCustomerInfo);
            var goals = new List<ConversionGoal>();
            var durationGoal = new DurationGoal();
            durationGoal.Id = goalsCollection.Goals[0].Id;
            durationGoal.GoalCategory = ConversionGoalCategory.Other;
            durationGoal.ExcludeFromBidding = false;
            goals.Add(durationGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);
            object response = updateGoalsCollection.Update(MainGoalConversionsCustomerInfo);
            ResponseValidator.ValidateAPISuccess(response);
            List<object> getGoals = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), MainGoalConversionsCustomerInfo);
            var vtcProperty = getGoals[0].GetType().GetProperty("ExcludeFromBidding");
            var realValue = (bool?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.AreEqual(durationGoal.ExcludeFromBidding, realValue, "ExcludeFromBidding doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_Property_ViewThroughConversionWindowinMinutes_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Goals[1].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Add_Success(ViewThroughConversionsCustomerInfo);

            var goals = new List<ConversionGoal>();
            var durationGoal = new DurationGoal();
            durationGoal.Id = goalsCollection.Goals[0].Id;
            durationGoal.ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.Exceed).ViewThroughConversionWindowInMinutes;
            goals.Add(durationGoal);
            durationGoal = new DurationGoal();
            durationGoal.Id = goalsCollection.Goals[1].Id;
            durationGoal.GoalCategory = ConversionGoalCategory.Other;
            durationGoal.ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.Negative).ViewThroughConversionWindowInMinutes;
            goals.Add(durationGoal);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(goals);

            object response = updateGoalsCollection.Update(ViewThroughConversionsCustomerInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidViewThroughConversionWindowInMinutes);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidViewThroughConversionWindowInMinutes);

            // None uet based update fail
            goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.AppInstall, tagId_ViewThroughConversionCustomer);
            goalsCollection.Add_Success(ViewThroughConversionsCustomerInfo);
            goals = new List<ConversionGoal>();
            var AppInstallGoal = new AppInstallGoal();
            AppInstallGoal.Id = goalsCollection.Goals[0].Id;
            AppInstallGoal.GoalCategory = ConversionGoalCategory.Download;
            AppInstallGoal.ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goals.Add(AppInstallGoal);
            updateGoalsCollection = new ConversionGoalCollection(goals);

            response = updateGoalsCollection.Update(ViewThroughConversionsCustomerInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ViewThroughConversionNotApplicableToGoalType);
        }       

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_EmptyGoalArray_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(0, ConversionGoalType.Url, tagId);
            var response = goalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalsNotPassed);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_GoalIsNull_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            updateGoalsCollection.Goals[0] = null;
            updateGoalsCollection.Goals[1].Id = goalsCollection.Goals[0].Id;
            updateGoalsCollection.Goals[1].Scope = goalsCollection.Goals[0].Scope;
            if (updateGoalsCollection.Goals[1].Scope == EntityScope.Customer && updateGoalsCollection.Goals[1].Revenue != null
                && updateGoalsCollection.Goals[1].Revenue.Type != ConversionGoalRevenueType.NoValue)
            {
                updateGoalsCollection.Goals[1].Revenue.CurrencyCode = "USD";
            }
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalIsNull);
            List<object> partialErrors = ObjectParser.GetFieldValuesOrNull(response, FieldNames.CommonField_PartialErrors);
            Assert.AreEqual(1, partialErrors.Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_DuplicateGoalNames_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            goalsCollection.Goals[1].Name = goalsCollection.Goals[0].Name;
            var response = goalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.DuplicateConversionGoalName);
            List<object> partialErrors = ObjectParser.GetFieldValuesOrNull(response, FieldNames.CommonField_PartialErrors);
            Assert.AreEqual(1, partialErrors.Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_ExistingGoalNames_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            goalsCollection.Goals[1].Name = goalsCollection.Goals[0].Name;
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { goalsCollection.Goals[1] });
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalNameAlreadyExists);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_ExceedsLimit_Fail()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(101, ConversionGoalType.Url, tagId);
            var response = goalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalArrayExceedsLimit);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_IdIsNull_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            var response = goalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalIdIsNull);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidTagId_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            updateGoalsCollection.Goals[0].TagId = 9999999;
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidUetTagId);
        }

// Doesn't make sense for REST API since we use the Type field to identify the object type to create
#if !REST_API
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_ConversionGoalTypeNotMatched_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            updateGoalsCollection.Goals[0].Type = ConversionGoalType.Event;
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalTypeNotMatched);
        }
#endif

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidRevenue_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.AppInstall, ConversionGoalType.Event }, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.AppInstall, ConversionGoalType.Event }, tagId);

            ConversionGoalRevenue revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue
            };
            updateGoalsCollection.Goals[0].Revenue = revenue;
            updateGoalsCollection.Goals[1].Revenue = revenue;
            updateGoalsCollection.Goals[2].Revenue = revenue;
            updateGoalsCollection.Goals[3].Revenue.Value = -2;
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidConversionGoalRevenueType);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidConversionGoalRevenueValue);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidGoalValue_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.AppInstall, ConversionGoalType.Event, ConversionGoalType.OfflineConversion }, tagId);
            goalsCollection.Goals[0].Revenue = null;
            goalsCollection.Goals[1].Revenue = null;
            goalsCollection.Goals[2].Revenue = null;
            goalsCollection.Goals[3].Revenue = null;
            goalsCollection.Goals[4].Revenue = null;
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Duration, ConversionGoalType.PagesViewedPerVisit,
                ConversionGoalType.AppInstall, ConversionGoalType.Event, ConversionGoalType.OfflineConversion }, tagId);

            ConversionGoalRevenue revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.FixedValue,
                Value = null,
                CurrencyCode = null
            };
            updateGoalsCollection.Goals[0].Revenue = revenue;
            updateGoalsCollection.Goals[1].Revenue = revenue;
            updateGoalsCollection.Goals[2].Revenue = revenue;
            updateGoalsCollection.Goals[3].Revenue = revenue;
            updateGoalsCollection.Goals[4].Revenue = revenue;
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalRevenueValue);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalRevenueValue);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidConversionGoalRevenueValue);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidConversionGoalRevenueValue);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidConversionGoalRevenueValue);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidUrlGoalUrlExpression_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(4, ConversionGoalType.Url, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(4, ConversionGoalType.Url, tagId);
            ((UrlGoal)updateGoalsCollection.Goals[0]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.Unspecified).UrlExpression;
            ((UrlGoal)updateGoalsCollection.Goals[1]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.WhiteSpace).UrlExpression;
            ((UrlGoal)updateGoalsCollection.Goals[2]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.NonprintableAscii).UrlExpression;
            ((UrlGoal)updateGoalsCollection.Goals[3]).UrlExpression = ApiTestSetting.Factories.ApiURLGoalFactory.Produce(ApiFactories.URLGoalFactory.UrlExpression.OverTheMaxLengthBoundary).UrlExpression;

            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);

            List<object> partialErrors = ObjectParser.GetFieldValuesOrNull(response, FieldNames.CommonField_PartialErrors);
            Assert.AreEqual(2, partialErrors.Count);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidUrlGoalUrlExpression);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidUrlGoalUrlExpression);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_AppInstallGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(7, ConversionGoalType.AppInstall, null);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(7, ConversionGoalType.AppInstall, tagId);
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            ((AppInstallGoal)updateGoalsCollection.Goals[0]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.Unspecified).AppStoreId;
            ((AppInstallGoal)updateGoalsCollection.Goals[1]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.WhiteSpace).AppStoreId;
            ((AppInstallGoal)updateGoalsCollection.Goals[2]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.NonprintableAscii).AppStoreId;
            ((AppInstallGoal)updateGoalsCollection.Goals[3]).AppStoreId = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppStoreId.OverTheMaxLengthBoundary).AppStoreId;
            ((AppInstallGoal)updateGoalsCollection.Goals[4]).AppPlatform = ApiTestSetting.Factories.ApiAppInstallGoalFactory.Produce(ApiFactories.AppInstallGoalFactory.AppPlatform.FakeStore).AppPlatform;
            updateGoalsCollection.Goals[5].CountType = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.CountType.Unique).CountType;
            updateGoalsCollection.Goals[6].Scope = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.Scope.Account).Scope;

            var response = updateGoalsCollection.Update(cInfo);
            List<object> partialErrors = ObjectParser.GetFieldValuesOrNull(response, FieldNames.CommonField_PartialErrors);
            Assert.AreEqual(5, partialErrors.Count);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidAppInstallGoalStoreId);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidAppInstallGoalStoreId);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidAppInstallGoalAppPlatform);
            ResponseValidator.ValidateAPIPatialError(response, 5, ErrorCodes.InvalidAppInstallGoalCountType);
            ResponseValidator.ValidateAPIPatialError(response, 6, ErrorCodes.InvalidAppInstallGoalScope);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidMinimumPagesViewedForGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.PagesViewedPerVisit, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.PagesViewedPerVisit, tagId, ApiFactories.PageViewsPerVisitGoalFactory.MinimumPagesViewed.Invalid);
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidMinimumPagesViewedForGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidDurationGoalDurationTime_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            ((DurationGoal)updateGoalsCollection.Goals[0]).MinimumDurationInSeconds = ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Negative).MinimumDurationInSeconds;
            ((DurationGoal)updateGoalsCollection.Goals[1]).MinimumDurationInSeconds = ApiTestSetting.Factories.ApiDurationGoalFactory.Produce(ApiFactories.DurationGoalFactory.MinimumDurationInSeconds.Exceed).MinimumDurationInSeconds;

            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);

            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidDurationGoalDurationTime);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidDurationGoalDurationTime);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidLookBackWindow_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Duration, tagId);
            updateGoalsCollection.Goals[0].ConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Negative).ConversionWindowInMinutes;
            updateGoalsCollection.Goals[1].ConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(ApiFactories.ConversionGoalFactory.ConversionWindowInMinutes.Exceed).ConversionWindowInMinutes;

            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidConversionGoalConversionWindow);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalConversionWindow);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InvalidEventGoal_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(5, ConversionGoalType.Event, tagId);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(5, ConversionGoalType.Event, tagId);
            ((EventGoal)updateGoalsCollection.Goals[0]).Value = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.Value.Unspecified).Value;
            ((EventGoal)updateGoalsCollection.Goals[0]).ActionExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ActionExpression.WhiteSpace).ActionExpression;
            ((EventGoal)updateGoalsCollection.Goals[0]).CategoryExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.CategoryExpression.Unspecified).CategoryExpression;
            ((EventGoal)updateGoalsCollection.Goals[0]).LabelExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.LabelExpression.WhiteSpace).LabelExpression;
            ((EventGoal)updateGoalsCollection.Goals[1]).Value = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.Value.InvalidRange).Value;
            ((EventGoal)updateGoalsCollection.Goals[2]).ActionExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.ActionExpression.NonprintableAscii).ActionExpression;
            ((EventGoal)updateGoalsCollection.Goals[3]).CategoryExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.CategoryExpression.OverTheMaxLengthBoundary).CategoryExpression;
            ((EventGoal)updateGoalsCollection.Goals[4]).LabelExpression = ApiTestSetting.Factories.ApiEventGoalFactory.Produce(ApiFactories.EventGoalFactory.LabelExpression.NonprintableAscii).LabelExpression;

            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            var response = updateGoalsCollection.Update(cInfo);
            // This error should not appear when we support backfill the null properties.
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.EventGoalExpressionWithOperatorNotPassed);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidEventGoalValue);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidEventGoalActionExpression);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidEventGoalCategoryExpression);
            ResponseValidator.ValidateAPIPatialError(response, 4, ErrorCodes.InvalidEventGoalLabelExpression);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_OfflineConversionGoal_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null);
            goalsCollection.Add_Success(cInfo);

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null);
            CopyGoalIdAndScope(updateGoalsCollection, goalsCollection);
            updateGoalsCollection.Goals[0].TagId = 9999;
            updateGoalsCollection.Goals[0].Name = "offline" + DateTime.Now.Ticks;
            updateGoalsCollection.Goals[0].CountType = ConversionGoalCountType.Unique;

            updateGoalsCollection.Update_Success(cInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InStoreTransactionGoal_Success()
        {
            InStoreTransactionGoal inStoreGoal = null;
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            List<object> inStoreGoals = goalsCollection.Get_Success(null, ConversionGoalType.InStoreTransaction, cInfo);
            if (inStoreGoals.Count == 0)
            {
                goalsCollection.Add_Success(cInfo);
                List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
                Assert.IsNotNull(resultGoals);
                Assert.AreEqual(1, resultGoals.Count);

                inStoreGoal = resultGoals[0] as InStoreTransactionGoal;
                Assert.IsNotNull(inStoreGoal);
                Assert.IsNull(inStoreGoal.TagId);

                goalsCollection.AreEqualInSequence(resultGoals);
            }
            else if (inStoreGoals.Count == 1)
            {
                inStoreGoal = inStoreGoals[0] as InStoreTransactionGoal;
                Assert.IsNotNull(inStoreGoal);
                Assert.IsNull(inStoreGoal.TagId);
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", inStoreGoals.Count, cInfo.CustomerId);
            }

            inStoreGoal.Name = "in-store transaction goal " + DateTime.Now.Ticks;
            inStoreGoal.TagId = 99999;
            inStoreGoal.Revenue.CurrencyCode = null;

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new[] { inStoreGoal });
            updateGoalsCollection.Update_Success(cInfo);

            List<object> goals = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), cInfo);
            Assert.IsNotNull(goals);
            Assert.AreEqual(1, goals.Count);
            Assert.IsNotNull(goals[0] as InStoreTransactionGoal);
            Assert.IsNull((goals[0] as InStoreTransactionGoal).TagId);
            updateGoalsCollection.AreEqualInSequence(goals, true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_InStoreTransactionGoal_BatchError()
        {
            InStoreTransactionGoal inStoreGoal = null;
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.InStoreTransaction, null);
            List<object> inStoreGoals = goalsCollection.Get_Success(null, ConversionGoalType.InStoreTransaction, cInfo);
            if (inStoreGoals.Count == 0)
            {
                goalsCollection.Add_Success(cInfo);
                List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
                Assert.IsNotNull(resultGoals);
                Assert.AreEqual(1, resultGoals.Count);

                inStoreGoal = resultGoals[0] as InStoreTransactionGoal;
                Assert.IsNotNull(inStoreGoal);
                Assert.IsNull(inStoreGoal.TagId);

                goalsCollection.AreEqualInSequence(resultGoals);
            }
            else if (inStoreGoals.Count == 1)
            {
                inStoreGoal = inStoreGoals[0] as InStoreTransactionGoal;
                Assert.IsNotNull(inStoreGoal);
                Assert.IsNull(inStoreGoal.TagId);
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", inStoreGoals.Count, cInfo.CustomerId);
            }

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            updateGoalsCollection.Goals[0].Id = inStoreGoal.Id;
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalTypeCannotBeChanged);
            List<object> partialErrors = ObjectParser.GetFieldValuesOrNull(response, FieldNames.CommonField_PartialErrors);
            Assert.AreEqual(1, partialErrors.Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateOfflineConversionGoals_CurrencyAcrossAllAccounts_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);
            goalsCollection.Add_Success(cInfo);

            var updateOfflineGoal = new OfflineConversionGoal();
            updateOfflineGoal.Id = goalsCollection.Goals[0].Id;
            updateOfflineGoal.GoalCategory = ConversionGoalCategory.Purchase;
            updateOfflineGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue,
                Value = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.Value.Unspecified).Value,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified).CurrencyCode
            };
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { updateOfflineGoal });
            updateGoalsCollection.Update_Success(cInfo);

            List<object> resultGoals = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), ConversionGoalType.OfflineConversion, cInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(1, resultGoals.Count);
            Assert.IsNotNull(resultGoals[0] as OfflineConversionGoal);
            updateGoalsCollection.AreEqualInSequence(resultGoals, true);

            updateGoalsCollection.Goals[0].Revenue.CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid).CurrencyCode;
            updateGoalsCollection.Update_Success(cInfo);

            resultGoals = updateGoalsCollection.Get_Success(updateGoalsCollection.GetGoalIds(), ConversionGoalType.OfflineConversion, cInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(1, resultGoals.Count);
            Assert.IsNotNull(resultGoals[0] as OfflineConversionGoal);
            updateGoalsCollection.AreEqualInSequence(resultGoals, true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateOfflineConversionGoals_CurrencyAcrossAllAccounts_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.NoValue);
            goalsCollection.Add_Success(cInfo);

            var updateOfflineGoal = new OfflineConversionGoal();
            updateOfflineGoal.Id = goalsCollection.Goals[0].Id;
            updateOfflineGoal.GoalCategory = ConversionGoalCategory.Purchase;
            updateOfflineGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.FixedValue,
                Value = 1,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified).CurrencyCode
            };
            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { updateOfflineGoal });
            var response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNull);

            updateGoalsCollection.Goals[0].Revenue.CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Invalid).CurrencyCode;
            response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidGoalCurrencyCode);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_CurrencyAcrossAllAccounts_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.FixedValue,
                ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange, ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Valid);
            goalsCollection.Add_Success(cInfo);

            var urlGoal = new UrlGoal();
            urlGoal.Id = goalsCollection.Goals[0].Id;
            urlGoal.GoalCategory = ConversionGoalCategory.Other;
            urlGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue,
                Value = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.Value.Unspecified).Value,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified).CurrencyCode
            };

            var eventGoal = new EventGoal();
            eventGoal.Id = goalsCollection.Goals[1].Id;
            eventGoal.GoalCategory = ConversionGoalCategory.Other;
            eventGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue,
                Value = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.Value.Unspecified).Value,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified).CurrencyCode
            };

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { urlGoal, eventGoal });
            object response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPISuccess(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_CurrencyAcrossAllAccounts_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Url, ConversionGoalType.Event }, tagId,
                ApiFactories.ConversionGoalFactory.Scope.Customer, ApiFactories.ConversionGoalRevenueFactory.Type.NoValue);
            goalsCollection.Add_Success(cInfo);

            var urlGoal = new UrlGoal();
            urlGoal.Id = goalsCollection.Goals[0].Id;
            urlGoal.GoalCategory = ConversionGoalCategory.Other;
            urlGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.FixedValue,
                Value = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange).Value,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Unspecified).CurrencyCode
            };

            var eventGoal = new EventGoal();
            eventGoal.Id = goalsCollection.Goals[1].Id;
            eventGoal.GoalCategory = ConversionGoalCategory.Other;
            eventGoal.Revenue = new ConversionGoalRevenue()
            {
                Type = ConversionGoalRevenueType.VariableValue,
                Value = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.Value.ValidRange).Value,
                CurrencyCode = ApiTestSetting.Factories.ApiConversionGoalRevenureFactory.Produce(
                    ApiFactories.ConversionGoalRevenueFactory.CurrencyCode.Invalid).CurrencyCode
            };

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { urlGoal, eventGoal });
            object response = updateGoalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCurrencyCodeIsNull);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidGoalCurrencyCode);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateOfflineConversionGoals_IsExternallyAttributed_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.OfflineConversion, null);
            (goalsCollection.Goals[0] as OfflineConversionGoal).IsExternallyAttributed = true;

            goalsCollection.Add_Success(ThirdPartyConversionPilotCustomer);

            var updateOfflineGoal1 = new OfflineConversionGoal();
            updateOfflineGoal1.Id = goalsCollection.Goals[0].Id;
            updateOfflineGoal1.GoalCategory = ConversionGoalCategory.Purchase;
            updateOfflineGoal1.IsExternallyAttributed = false;

            var updateOfflineGoal2 = new OfflineConversionGoal();
            updateOfflineGoal2.Id = goalsCollection.Goals[1].Id;
            updateOfflineGoal2.GoalCategory = ConversionGoalCategory.Purchase;
            updateOfflineGoal2.IsExternallyAttributed = true;

            ConversionGoalCollection updateGoalsCollection = new ConversionGoalCollection(new ConversionGoal[] { updateOfflineGoal1, updateOfflineGoal2 });
            var response = updateGoalsCollection.Update(ThirdPartyConversionPilotCustomer);

            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.IsExternallyAttributedCannotBeChanged);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.IsExternallyAttributedCannotBeChanged);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_Boost_RevenueChange_Success()
        {
            var cInfo = AudienceCampaignBoostCustomer;
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null);

            var response = goalsCollection.Add(cInfo);
            goalsCollection.Goals[0].Revenue.CurrencyCode = "USD";
            goalsCollection.Goals[0].Revenue.Type = ConversionGoalRevenueType.FixedValue;
            goalsCollection.Goals[0].Revenue.Value = 100;
            goalsCollection.Goals[0].Id = (response as AddConversionGoalsResponse).ConversionGoalIds[0];

            var overrideConfigValuesFromTest = new Dictionary<string, string>
            {
                { "BoostGoalRevenueUpdateUserIdWhitelist", $"{cInfo.UserId},2121" }
            };
            response = goalsCollection.Update(cInfo, overrideConfigValuesFromTest: overrideConfigValuesFromTest);
            ResponseValidator.ValidateAPISuccess(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void UpdateConversionGoals_Boost_RevenueChange_Fail()
        {
            var cInfo = AudienceCampaignBoostCustomer;
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, null);

            var response = goalsCollection.Add(cInfo);
            goalsCollection.Goals[0].Revenue.Type = ConversionGoalRevenueType.FixedValue;
            goalsCollection.Goals[0].Revenue.CurrencyCode = "USD";
            goalsCollection.Goals[0].Revenue.Value = 100;
            goalsCollection.Goals[0].Id = (response as AddConversionGoalsResponse).ConversionGoalIds[0];

            response = goalsCollection.Update(cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.CannotPerformCurrentOperation);
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_AutoGoal_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagId);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;
            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;

            goalsCollection.Goals[0].ExcludeFromBidding = false;
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = 1000;
            goalsCollection.Goals[0].ConversionWindowInMinutes = 1000;

            goalsCollection.Add_Success(customer, ReturnAdditionalFields:ConversionGoalAdditionalField.IsAutoGoal);

            goalsCollection.Goals[0].ExcludeFromBidding = true;
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = 900;
            goalsCollection.Goals[0].ConversionWindowInMinutes = 900;
        
            goalsCollection.Update_Success(customer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsAutoGoal);
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_AutoGoal_UpdateTag_Failure()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });

            UETTagCollection tagsCollection = new UETTagCollection(2);
            tagsCollection.Add_Success(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagsCollection.Tags[0].Id);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;
            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;
            goalsCollection.Add_Success(customer, ReturnAdditionalFields:ConversionGoalAdditionalField.IsAutoGoal);

            goalsCollection.Goals[0].TagId = tagsCollection.Tags[1].Id;
            var response = goalsCollection.Update(customer);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.TagNotAllowToChangeForAutoGoal);
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void UpdateConversionGoals_AutoGoal_UpdateCategory_Failure()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });

            UETTagCollection tagsCollection = new UETTagCollection(2);
            tagsCollection.Add_Success(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagsCollection.Tags[0].Id);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;
            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;
            goalsCollection.Add_Success(customer, ReturnAdditionalFields:ConversionGoalAdditionalField.IsAutoGoal);

            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.AddToCart;
            var response = goalsCollection.Update(customer);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.GoalCategoryCannotBeChangedForAutoGoal);
        }

        private void CopyGoalIdAndScope(ConversionGoalCollection targetCollection, ConversionGoalCollection sourceCollection)
        {
            for (int i = 0; i < targetCollection.Goals.Count; i++)
            {
                if (targetCollection.Goals[i].Scope == EntityScope.Account && sourceCollection.Goals[i].Scope == EntityScope.Customer
                    && (targetCollection.Goals[i].Type == ConversionGoalType.Url || targetCollection.Goals[i].Type == ConversionGoalType.Event || targetCollection.Goals[i].Type == ConversionGoalType.OfflineConversion)
                    && targetCollection.Goals[i].Revenue != null && targetCollection.Goals[i].Revenue.Type != ConversionGoalRevenueType.NoValue)
                {
                    targetCollection.Goals[i].Revenue.CurrencyCode = "USD";
                }

                targetCollection.Goals[i].Id = sourceCollection.Goals[i].Id;
                targetCollection.Goals[i].Scope = sourceCollection.Goals[i].Scope;
            }
        }

        private static long GetUetTagId(CustomerInfo cInfo = null)
        {
            var uetTagCollection = new UETTagCollection(1);
            var uetTags = uetTagCollection.Get_Success(null, cInfo);
            if (uetTags.Count > 0)
            {
                return ((UetTag)uetTags[0]).Id.Value;
            }
            uetTagCollection.Add_Success(cInfo);
            return uetTagCollection.Tags[0].Id.Value;
        }

        private void GetMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string expectedMsClickIdTagging)
        {
            AccountPropertyCollection collection = new AccountPropertyCollection(expectedMsClickIdTagging, null, null);
            List<object> accountProperties = collection.Get_Sucess(new[] { AccountPropertyName.MSCLKIDAutoTaggingEnabled }, customerInfo);
            collection.Validate(accountProperties);
        }

        private void SetMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string msClickIdTagging)
        {
            AccountPropertyCollection collection = new AccountPropertyCollection(msClickIdTagging, null, null);
            collection.Set_Success(customerInfo);
            List<object> accountProperties = collection.Get_Sucess(new[] { AccountPropertyName.MSCLKIDAutoTaggingEnabled }, customerInfo);
            collection.Validate(accountProperties);
        }
    }
}