﻿using Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13;
using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
using Microsoft.AdCenter.Shared.Api.V13.Logging;
using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
using EventTracking = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
using ValueOperator = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ValueOperator;
using ExpressionOperator = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ExpressionOperator;
using EventTrackingUtil = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common.EventTracking;
using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common.EventTracking;
using DurationGoal = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DurationGoal;
using EventGoal = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EventGoal;
using InStoreTransactionGoal = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.InStoreTransactionGoal;
using OfflineConversionGoal = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.OfflineConversionGoal;
using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages;

namespace Microsoft.AdCenter.Advertiser.CampaignManagement.Api.Operations.V13.Translation
{
    static class ConvensionGoalTranslator
    {
        internal static ConversionGoal[] ToApiConversionGoals(
            ILogContext logContext,
            EventTracking.Goal[] goals,
            ConversionGoalAdditionalField? ReturnAdditionalFields)
        {
            ConversionGoal[] ConversionGoals = new ConversionGoal[goals.Length];

            for (var i = 0; i < goals.Length; i++)
            {
                var goal = goals[i];
                if (goal != null)
                {
                    if (goal.Type == GoalEntityType.DestinationGoal)
                    {
                        var urlGoal = new UrlGoal();
                        var mtUrlGoal = goal as DestinationGoal;
                        if (mtUrlGoal != null)
                        {
                            urlGoal.UrlExpression = mtUrlGoal.UrlString;
                            urlGoal.UrlOperator = ToApiExpressionOperator(mtUrlGoal.Operator.GetValueOrDefault());
                            if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsEnhancedConversionsEnabled))
                            {
                                urlGoal.IsEnhancedConversionsEnabled = mtUrlGoal.IsEnhancedConversionsEnabled;
                            }
                        }
                        ConversionGoals[i] = urlGoal;

                    }
                    else if (goal.Type == GoalEntityType.DurationGoal)
                    {
                        var durationGoal = new DataContracts.V13.DurationGoal();
                        var mtDurationGoal = goal as EventTracking.DurationGoal;
                        if (mtDurationGoal != null)
                        {
                            durationGoal.MinimumDurationInSeconds = mtDurationGoal.Hours * 3600 + mtDurationGoal.Minutes * 60 + mtDurationGoal.Seconds;
                            if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsEnhancedConversionsEnabled))
                            {
                                durationGoal.IsEnhancedConversionsEnabled = mtDurationGoal.IsEnhancedConversionsEnabled;
                            }
                        }
                        ConversionGoals[i] = durationGoal;

                    }
                    else if (goal.Type == GoalEntityType.PageViewsPerVisitGoal)
                    {
                        var pageViewsPerVisitGoal = new PagesViewedPerVisitGoal();
                        var mtPageViewGoal = goal as PageViewsPerVisitGoal;
                        if (mtPageViewGoal != null)
                        {
                            pageViewsPerVisitGoal.MinimumPagesViewed = mtPageViewGoal.PageViews;
                            if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsEnhancedConversionsEnabled))
                            {
                                pageViewsPerVisitGoal.IsEnhancedConversionsEnabled = mtPageViewGoal.IsEnhancedConversionsEnabled;
                            }
                        }
                        ConversionGoals[i] = pageViewsPerVisitGoal;
                    }
                    else if (goal.Type == GoalEntityType.EventGoal)
                    {
                        var eventGoal = new DataContracts.V13.EventGoal();
                        var mtEventGoal = goal as EventTracking.EventGoal;
                        if (mtEventGoal != null)
                        {
                            eventGoal.CategoryExpression = mtEventGoal.Category;
                            eventGoal.CategoryOperator = ToApiExpressionOperator(mtEventGoal.CategoryOperator.GetValueOrDefault());
                            eventGoal.ActionExpression = mtEventGoal.Action;
                            eventGoal.ActionOperator = ToApiExpressionOperator(mtEventGoal.ActionOperator.GetValueOrDefault());
                            eventGoal.LabelExpression = mtEventGoal.Label;
                            eventGoal.LabelOperator = ToApiExpressionOperator(mtEventGoal.LabelOperator.GetValueOrDefault());
                            eventGoal.Value = mtEventGoal.Value;
                            eventGoal.ValueOperator = ToApiValueOperatior(mtEventGoal.ValueOperator.GetValueOrDefault());
                            if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsEnhancedConversionsEnabled))
                            {
                                eventGoal.IsEnhancedConversionsEnabled = mtEventGoal.IsEnhancedConversionsEnabled;
                            }
                        }
                        ConversionGoals[i] = eventGoal;
                    }
                    else if (goal.Type == GoalEntityType.ApplicationInstallGoal)
                    {
                        var applicationInstallGoal = new AppInstallGoal();
                        applicationInstallGoal.AppPlatform = ((ApplicationInstallGoal)goal).ApplicationPlatform;
                        applicationInstallGoal.AppStoreId = ((ApplicationInstallGoal)goal).ApplicationStoreId;
                        ConversionGoals[i] = applicationInstallGoal;
                    }
                    else if (goal.Type == GoalEntityType.OfflineConversionGoal)
                    {
                        var offlineConversionGoal = new DataContracts.V13.OfflineConversionGoal();
                        ConversionGoals[i] = offlineConversionGoal;
                        if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsExternallyAttributed))
                        {
                            if (goal.AttributionModelType.HasValue)
                            {
                                offlineConversionGoal.IsExternallyAttributed = goal.AttributionModelType.Value == EventTracking.AttributionModelType.External;
                            }
                        }
                        if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsEnhancedConversionsEnabled))
                        {
                            offlineConversionGoal.IsEnhancedConversionsEnabled = goal.IsEnhancedConversionsEnabled;
                        }
                    }
                    else if (goal.Type == GoalEntityType.InStoreTransactionGoal)
                    {
                        var inStoreTransactionGoal = new DataContracts.V13.InStoreTransactionGoal();
                        ConversionGoals[i] = inStoreTransactionGoal;
                    }

                    ConversionGoals[i].Id = goal.Id;
                    ConversionGoals[i].Name = goal.Name;
                    ConversionGoals[i].Type = ToApiConvensionGoalType(goal.Type.GetValueOrDefault());
                    ConversionGoals[i].Status = ToApiConvensionGoalStatus(goal.Status.GetValueOrDefault());
                    ConversionGoals[i].ExcludeFromBidding = goal.ExcludeFromBidding;
                    ConversionGoals[i].Revenue = new ConversionGoalRevenue()
                    {
                        Value = goal.Revenue.Value,
                        CurrencyCode = goal.Revenue.CurrencyCode,
                        Type = ToApiConvensionGoalValueType(goal.Revenue.Type.GetValueOrDefault()),
                    };
                    ConversionGoals[i].CountType = goal.ConversionCountType == ConversionCountType.All
                                                        ? ConversionGoalCountType.All
                                                        : ConversionGoalCountType.Unique;
                    ConversionGoals[i].ConversionWindowInMinutes = EventTrackingUtil.EventTrackingHelper.ComputeWindowMinutesAllowNull(goal.LookbackWindowDays, goal.LookbackWindowHours, goal.LookbackWindowMinutes);
                    if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.ViewThroughConversionWindowInMinutes))
                    {
                        ConversionGoals[i].ViewThroughConversionWindowInMinutes = EventTrackingUtil.EventTrackingHelper.ComputeWindowMinutesAllowNull(goal.ViewThroughLookbackWindowinDays, goal.ViewThroughLookbackWindowinHours, goal.ViewThroughLookbackWindowinMinutes);
                    }
                    
                    if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.GoalCategory))
                    {
                        ConversionGoals[i].GoalCategory = ToApiGoalCategory(goal.GoalCategory);
                    }

                    if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.AttributionModelType))
                    {
                        ConversionGoals[i].AttributionModelType = ToApiAttributionModelType(goal);
                    }

                    ConversionGoals[i].TagId = goal.TagId;
                    ConversionGoals[i].Scope = goal.IsAccountLevel ? EntityScope.Account : EntityScope.Customer;
                    ConversionGoals[i].TrackingStatus = ToApiConvensionGoalTrackingStatus(goal.GoalTrackingStatus.GetValueOrDefault(), ReturnAdditionalFields);
                    if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
                    {
                        if (ReturnAdditionalFields.HasValue && ReturnAdditionalFields.Value.HasFlag(ConversionGoalAdditionalField.IsAutoGoal))
                        {
                            ConversionGoals[i].IsAutoGoal = goal.IsAutoGoal;
                        }
                    }
                }
                else
                {
                    ConversionGoals[i] = null;
                }
            }

            return ConversionGoals;
        }

        internal static EventTracking.Goal[] ToMTGoals(ConversionGoal[] conversionGoals)
        {
            EventTracking.Goal[] Goals = new EventTracking.Goal[conversionGoals.Length];

            for (var i = 0; i < conversionGoals.Length; i++)
            {
                var conversionGoal = conversionGoals[i];
                if (conversionGoal != null)
                {
                    if (conversionGoal is UrlGoal)
                    {
                        var urlGoal = new DestinationGoal();
                        var apiGoal = conversionGoal as UrlGoal;
                        if (apiGoal != null)
                        {
                            urlGoal.UrlString = apiGoal.UrlExpression;
                            urlGoal.Operator = ToMtExpressionOperator(apiGoal.UrlOperator);
                            urlGoal.IsEnhancedConversionsEnabled = apiGoal.IsEnhancedConversionsEnabled;
                        }
                        Goals[i] = urlGoal;

                    }
                    else if (conversionGoal is DataContracts.V13.DurationGoal)
                    {
                        var durationGoal = new EventTracking.DurationGoal();
                        var apiGoal = conversionGoal as DataContracts.V13.DurationGoal;
                        durationGoal.Operator = EventTracking.ValueOperator.GreaterThan;
                        if (apiGoal != null)
                        {
                            if (apiGoal.MinimumDurationInSeconds == null)
                            {
                                durationGoal.Hours = null;
                                durationGoal.Minutes = null;
                                durationGoal.Seconds = null;
                            }
                            else if (apiGoal.MinimumDurationInSeconds.Value >= 0)
                            {
                                TimeSpan timeSpan = TimeSpan.FromSeconds(apiGoal.MinimumDurationInSeconds.Value);
                                durationGoal.Hours = timeSpan.Hours;
                                durationGoal.Minutes = timeSpan.Minutes;
                                durationGoal.Seconds = timeSpan.Seconds;
                            }
                            durationGoal.IsEnhancedConversionsEnabled = apiGoal.IsEnhancedConversionsEnabled;
                        }

                        Goals[i] = durationGoal;

                    }
                    else if (conversionGoal is PagesViewedPerVisitGoal)
                    {
                        var pageViewsPerVisitGoal = new PageViewsPerVisitGoal();
                        var apiPageGoal = conversionGoal as PagesViewedPerVisitGoal;
                        if (apiPageGoal != null)
                        {
                            pageViewsPerVisitGoal.PageViews = apiPageGoal.MinimumPagesViewed;
                            pageViewsPerVisitGoal.Operator = EventTracking.ValueOperator.GreaterThan;
                            pageViewsPerVisitGoal.IsEnhancedConversionsEnabled = apiPageGoal.IsEnhancedConversionsEnabled;
                        }
                        Goals[i] = pageViewsPerVisitGoal;
                    }
                    else if (conversionGoal is DataContracts.V13.EventGoal)
                    {
                        var eventGoal = new EventTracking.EventGoal();
                        var apiGoal = conversionGoal as DataContracts.V13.EventGoal;
                        if (apiGoal != null)
                        {
                            eventGoal.Category = apiGoal.CategoryExpression;
                            eventGoal.CategoryOperator = ToMtExpressionOperator(apiGoal.CategoryOperator);
                            eventGoal.Action = apiGoal.ActionExpression;
                            eventGoal.ActionOperator = ToMtExpressionOperator(apiGoal.ActionOperator);
                            eventGoal.Label = apiGoal.LabelExpression;
                            eventGoal.LabelOperator = ToMtExpressionOperator(apiGoal.LabelOperator);
                            eventGoal.Value = apiGoal.Value;
                            eventGoal.ValueOperator = ToMtValueOperator(apiGoal.ValueOperator);
                            eventGoal.IsEnhancedConversionsEnabled = apiGoal.IsEnhancedConversionsEnabled;
                        }
                        Goals[i] = eventGoal;
                    }
                    else if (conversionGoal is AppInstallGoal)
                    {
                        var applicationInstallGoal = new ApplicationInstallGoal();
                        var apiGoal = conversionGoal as AppInstallGoal;
                        applicationInstallGoal.ApplicationPlatform = apiGoal.AppPlatform;
                        applicationInstallGoal.ApplicationStoreId = apiGoal.AppStoreId;
                        Goals[i] = applicationInstallGoal;
                    }
                    else if (conversionGoal is DataContracts.V13.OfflineConversionGoal)
                    {
                        conversionGoal.Scope = conversionGoal.Scope ?? EntityScope.Account;

                        var offlineConversionGoal = new EventTracking.OfflineConversionGoal();
                        var apiGoal = conversionGoal as DataContracts.V13.OfflineConversionGoal;
                        if (apiGoal.IsExternallyAttributed.HasValue)
                        {
                            offlineConversionGoal.AttributionModelType = apiGoal.IsExternallyAttributed.Value ? EventTracking.AttributionModelType.External : EventTracking.AttributionModelType.Default;
                        }
                        Goals[i] = offlineConversionGoal;
                    }
                    else if (conversionGoal is DataContracts.V13.InStoreTransactionGoal)
                    {
                        var inStoreTransactionGoal = new EventTracking.InStoreTransactionGoal();
                        Goals[i] = inStoreTransactionGoal;
                    }

                    Goals[i].Id = conversionGoal.Id;
                    Goals[i].Name = conversionGoal.Name;
                    Goals[i].Type = ToMtGoalType(conversionGoal, conversionGoal.Type);
                    Goals[i].ConversionCountType = ToMtConversionCountType(conversionGoal.CountType);
                    Goals[i].TagId = conversionGoal.TagId;
                    Goals[i].IsAccountLevel = conversionGoal.Scope == EntityScope.Account;
                    Goals[i].Status = ToMtGoalStatus(conversionGoal.Status);
                    Goals[i].Revenue = new GoalRevenue();
                    Goals[i].ExcludeFromBidding = conversionGoal.ExcludeFromBidding;
                    if (conversionGoal.Revenue != null)
                    {
                        Goals[i].Revenue.Value = conversionGoal.Revenue.Value;
                        Goals[i].Revenue.CurrencyCode = conversionGoal.Revenue.CurrencyCode;
                        Goals[i].Revenue.Type = ToMtGoalValueType(conversionGoal.Revenue.Type);
                    }
                    else
                    {
                        Goals[i].Revenue.Value = null;
                        Goals[i].Revenue.Type = null;
                    }
                    if (conversionGoal.ConversionWindowInMinutes == null)
                    {
                        Goals[i].LookbackWindowDays = null;
                        Goals[i].LookbackWindowHours = null;
                        Goals[i].LookbackWindowMinutes = null;
                    }
                    else if (conversionGoal.ConversionWindowInMinutes >= 0)
                    {
                        TimeSpan timeSpan = TimeSpan.FromMinutes(conversionGoal.ConversionWindowInMinutes.Value);
                        Goals[i].LookbackWindowDays = timeSpan.Days;
                        Goals[i].LookbackWindowHours = timeSpan.Hours;
                        Goals[i].LookbackWindowMinutes = timeSpan.Minutes;
                    }
                    if (conversionGoal.ViewThroughConversionWindowInMinutes.HasValue)
                    {
                        TimeSpan timeSpan = TimeSpan.FromMinutes(conversionGoal.ViewThroughConversionWindowInMinutes.Value);
                        Goals[i].ViewThroughLookbackWindowinDays = timeSpan.Days;
                        Goals[i].ViewThroughLookbackWindowinHours = timeSpan.Hours;
                        Goals[i].ViewThroughLookbackWindowinMinutes = timeSpan.Minutes;
                    }
                    if (conversionGoal.GoalCategory.HasValue)
                    {
                        Goals[i].GoalCategory = ToMtGoalCategory(conversionGoal.GoalCategory);
                    }
                    else
                    {
                        //Initialize the goalcategory value when customer is in the piloting list and does not pass the Goalcategory value.
                        //Set the default value as PM spec said.
                        //Otherwise it will return an error message called InvalidGoalCategory cause we have not passed the GoalCategory
                        Goals[i].GoalCategory = GetDefaultGoalCategoryByGoalType(Goals[i].Type);

                    }

                    // offline conversion has its own logic of handling the AttributionModelType
                    if (!(conversionGoal is DataContracts.V13.OfflineConversionGoal))
                    {
                        Goals[i].AttributionModelType = ToMtAttributionModelType(conversionGoal.AttributionModelType);
                    }

                    if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
                    {
                        Goals[i].IsAutoGoal = conversionGoal.IsAutoGoal;
                        if (conversionGoal.IsAutoGoal ?? false &&
                            conversionGoal is DataContracts.V13.EventGoal && //we only support event goal for auto conversion
                            conversionGoal.GoalCategory != null) // auto goal must have goal category
                        {
                            var apiGoal = conversionGoal as DataContracts.V13.EventGoal;
                            var eventAutoGoal = Goals[i] as EventTracking.EventGoal;

                            //ignore customer's input for auto goal
                            eventAutoGoal.Action = null;
                            eventAutoGoal.ActionOperator = EventTracking.ExpressionOperator.NoExpression;

                            if (string.IsNullOrEmpty(apiGoal.ActionExpression) && apiGoal.ActionOperator == null)
                            {
                                eventAutoGoal.Action = EventTrackingHelper.GenerateEventActionForAutoGoal(ToMtGoalCategory(conversionGoal.GoalCategory));
                                eventAutoGoal.ActionOperator = EventTracking.ExpressionOperator.EqualsTo;
                                //will validate each event goal's parameter in EO layer (same validation logic for both OData and SOAP)
                            }
                            Goals[i] = eventAutoGoal;

                            //we do not expose GoalSourceId field to public API, use UET2 as default
                            Goals[i].GoalSourceId = GoalSource.UET2;
                        }
                    }
                }
                else
                {
                    Goals[i] = null;
                }
            }

            return Goals;
        }

        internal static EventTracking.CampaignConversionGoal[] ToMTCampaignConversionGoals(DataContracts.V13.CampaignConversionGoal[] campaignConversionGoals)
        {
            if (campaignConversionGoals == null)
            {// MT layer will handle empty CampaignConversionGoal
                return Array.Empty<EventTracking.CampaignConversionGoal>();
            }

            EventTracking.CampaignConversionGoal[] mtCampaignConversionGoals = new EventTracking.CampaignConversionGoal[campaignConversionGoals.Length];

            int i = 0;
            foreach (var campaignConversionGoal in campaignConversionGoals)
            {
                mtCampaignConversionGoals[i] = new EventTracking.CampaignConversionGoal { CampaignId = campaignConversionGoal.CampaignId, GoalId = campaignConversionGoal.GoalId };
                i++;
            }

            return mtCampaignConversionGoals;
        }

        private static GoalCategory? GetDefaultGoalCategoryByGoalType(GoalEntityType? goalType)
        {
            if (goalType == GoalEntityType.DurationGoal || goalType == GoalEntityType.PageViewsPerVisitGoal)
            {
                return GoalCategory.Other;
            }
            else if (goalType == GoalEntityType.ApplicationInstallGoal)
            {
                return GoalCategory.Download;
            }
            else if (goalType == GoalEntityType.InStoreTransactionGoal || goalType == GoalEntityType.ProductConversionGoal)
            {
                return GoalCategory.Purchase;
            }
            else
            {
                return null;
            }
            
        }

        private static ConversionCountType? ToMtConversionCountType(ConversionGoalCountType? countType)
        {
            switch (countType)
            {
                case ConversionGoalCountType.All:
                    return ConversionCountType.All;
                case ConversionGoalCountType.Unique:
                    return ConversionCountType.Unique;
                default:
                    return null;
            }
        }

        private static GoalValueType? ToMtGoalValueType(ConversionGoalRevenueType? revenueType)
        {
            switch (revenueType)
            {
                case ConversionGoalRevenueType.FixedValue:
                    return GoalValueType.FixedValue;
                case ConversionGoalRevenueType.VariableValue:
                    return GoalValueType.VariantValue;
                case ConversionGoalRevenueType.NoValue:
                    return GoalValueType.NoValue;
                default:
                    return null;
            }
        }

        private static ConversionGoalRevenueType ToApiConvensionGoalValueType(GoalValueType valueType)
        {
            switch (valueType)
            {
                case GoalValueType.FixedValue:
                    return ConversionGoalRevenueType.FixedValue;
                case GoalValueType.VariantValue:
                    return ConversionGoalRevenueType.VariableValue;
                default:
                    return ConversionGoalRevenueType.NoValue;
            }
        }

        private static ConversionGoalStatus ToApiConvensionGoalStatus(GoalStatus status)
        {
            switch (status)
            {
                case GoalStatus.Active:
                    return ConversionGoalStatus.Active;
                default:
                    return ConversionGoalStatus.Paused;
            }
        }

        private static GoalStatus? ToMtGoalStatus(ConversionGoalStatus? status)
        {
            switch (status)
            {
                case ConversionGoalStatus.Active:
                    return GoalStatus.Active;
                case ConversionGoalStatus.Paused:
                    return GoalStatus.Paused;
                default:
                    return null;
            }
        }

        private static ConversionGoalTrackingStatus? ToApiConvensionGoalTrackingStatus(GoalTrackingStatus? status, ConversionGoalAdditionalField? returnAdditionalField)
        {
            switch (status)
            {
                case GoalTrackingStatus.NoRecentConversion:
                    return ConversionGoalTrackingStatus.NoRecentConversions;
                case GoalTrackingStatus.RecordingConversions:
                    return ConversionGoalTrackingStatus.RecordingConversions;
                case GoalTrackingStatus.TagInactive:
                    return ConversionGoalTrackingStatus.TagInactive;
                case GoalTrackingStatus.Unverified:
                    return ConversionGoalTrackingStatus.TagUnverified;
                case GoalTrackingStatus.InactiveDueToTagUnavailable:
                    if(returnAdditionalField.HasValue && returnAdditionalField.Value.HasFlag(ConversionGoalAdditionalField.InactiveDueToTagUnavailable))
                    {
                        return ConversionGoalTrackingStatus.InactiveDueToTagUnavailable;
                    }
                    return ConversionGoalTrackingStatus.TagInactive;
                default:
                    return null;
            }
        }

        private static ConversionGoalType ToApiConvensionGoalType(GoalEntityType type)
        {
            switch (type)
            {
                case GoalEntityType.DestinationGoal:
                    return ConversionGoalType.Url;
                case GoalEntityType.DurationGoal:
                    return ConversionGoalType.Duration;
                case GoalEntityType.PageViewsPerVisitGoal:
                    return ConversionGoalType.PagesViewedPerVisit;
                case GoalEntityType.EventGoal:
                    return ConversionGoalType.Event;
                case GoalEntityType.ApplicationInstallGoal:
                    return ConversionGoalType.AppInstall;
                case GoalEntityType.OfflineConversionGoal:
                    return ConversionGoalType.OfflineConversion;
                case GoalEntityType.InStoreTransactionGoal:
                    return ConversionGoalType.InStoreTransaction;
                default:
                    return ConversionGoalType.None;
            }
        }


        private static GoalEntityType? ToMtGoalType(ConversionGoal goal, ConversionGoalType? type)
        {
            switch (type)
            {
                case ConversionGoalType.Url:
                    return GoalEntityType.DestinationGoal;
                case ConversionGoalType.Duration:
                    return GoalEntityType.DurationGoal;
                case ConversionGoalType.PagesViewedPerVisit:
                    return GoalEntityType.PageViewsPerVisitGoal;
                case ConversionGoalType.Event:
                    return GoalEntityType.EventGoal;
                case ConversionGoalType.AppInstall:
                    return GoalEntityType.ApplicationInstallGoal;
                case ConversionGoalType.OfflineConversion:
                    return GoalEntityType.OfflineConversionGoal;
                case ConversionGoalType.InStoreTransaction:
                    return GoalEntityType.InStoreTransactionGoal;
                default:
                    return GetGoalType(goal);
            }
        }

        private static GoalCategory? ToMtGoalCategory(ConversionGoalCategory? type)
        {
            if(type == null)
            {
                return null;
            }
            switch (type)
            {
                case ConversionGoalCategory.None:
                    return GoalCategory.None;
                case ConversionGoalCategory.Purchase:
                    return GoalCategory.Purchase;
                case ConversionGoalCategory.AddToCart:
                    return GoalCategory.AddToCart;
                case ConversionGoalCategory.BeginCheckout:
                    return GoalCategory.BeginCheckout;
                case ConversionGoalCategory.Subscribe:
                    return GoalCategory.Subcribe;
                case ConversionGoalCategory.SubmitLeadForm:
                    return GoalCategory.SubmitLeadForm;
                case ConversionGoalCategory.BookAppointment:
                    return GoalCategory.BookAppointment;
                case ConversionGoalCategory.Signup:
                    return GoalCategory.Signup;
                case ConversionGoalCategory.RequestQuote:
                    return GoalCategory.RequestQuote;
                case ConversionGoalCategory.GetDirections:
                    return GoalCategory.GetDirections;
                case ConversionGoalCategory.OutboundClick:
                    return GoalCategory.OutboundClick;
                case ConversionGoalCategory.Contact:
                    return GoalCategory.Contact;
                case ConversionGoalCategory.PageView:
                    return GoalCategory.PageView;
                case ConversionGoalCategory.Download:
                    return GoalCategory.Download;
                case ConversionGoalCategory.Other:
                    return GoalCategory.Other;
                default:
                    return GoalCategory.None;
            }
        }

        private static ConversionGoalCategory? ToApiGoalCategory(GoalCategory? type)
        {
            if (type == null)
            {
                return null;
            }
            switch (type)
            {
                case GoalCategory.None:
                    return ConversionGoalCategory.None;
                case GoalCategory.Purchase:
                    return ConversionGoalCategory.Purchase;
                case GoalCategory.AddToCart:
                    return ConversionGoalCategory.AddToCart;
                case GoalCategory.BeginCheckout:
                    return ConversionGoalCategory.BeginCheckout;
                case GoalCategory.Subcribe:
                    return ConversionGoalCategory.Subscribe;
                case GoalCategory.SubmitLeadForm:
                    return ConversionGoalCategory.SubmitLeadForm;
                case GoalCategory.BookAppointment:
                    return ConversionGoalCategory.BookAppointment;
                case GoalCategory.Signup:
                    return ConversionGoalCategory.Signup;
                case GoalCategory.RequestQuote:
                    return ConversionGoalCategory.RequestQuote;
                case GoalCategory.GetDirections:
                    return ConversionGoalCategory.GetDirections;
                case GoalCategory.OutboundClick:
                    return ConversionGoalCategory.OutboundClick;
                case GoalCategory.Contact:
                    return ConversionGoalCategory.Contact;
                case GoalCategory.PageView:
                    return ConversionGoalCategory.PageView;
                case GoalCategory.Download:
                    return ConversionGoalCategory.Download;
                case GoalCategory.Other:
                    return ConversionGoalCategory.Other;
                default:
                    return ConversionGoalCategory.Unknown;
            }
        }

        private static EventTracking.AttributionModelType? ToMtAttributionModelType(DataContracts.V13.AttributionModelType? attributionModelType)
        {
            if (!attributionModelType.HasValue)
            {
                return null;
            }

            switch (attributionModelType)
            {
                case DataContracts.V13.AttributionModelType.LastClick:
                    return EventTracking.AttributionModelType.Default;
                case DataContracts.V13.AttributionModelType.LastTouch:
                    return EventTracking.AttributionModelType.LastTouch;
                default:
                    return null;
            }
        }

        private static DataContracts.V13.AttributionModelType? ToApiAttributionModelType(EventTracking.Goal goal)
        {
            var attributionModelType = goal.AttributionModelType;
            if (EventTrackingHelper.IsUetBasedGoal(goal) && attributionModelType.HasValue)
            {
                switch (attributionModelType)
                {
                    case EventTracking.AttributionModelType.Default:
                        return DataContracts.V13.AttributionModelType.LastClick;
                    case EventTracking.AttributionModelType.LastTouch:
                        return DataContracts.V13.AttributionModelType.LastTouch;
                    default:
                        return null;
                }
            }

            return null;
        }

        private static GoalEntityType? GetGoalType(ConversionGoal goal)
        {
            switch (goal)
            {
                case UrlGoal _:
                    return GoalEntityType.DestinationGoal;
                case DurationGoal _:
                    return GoalEntityType.DurationGoal;
                case PagesViewedPerVisitGoal _:
                    return GoalEntityType.PageViewsPerVisitGoal;
                case EventGoal _:
                    return GoalEntityType.EventGoal;
                case OfflineConversionGoal _:
                    return GoalEntityType.OfflineConversionGoal;
                case InStoreTransactionGoal _:
                    return GoalEntityType.InStoreTransactionGoal;
                case AppInstallGoal _:
                    return GoalEntityType.ApplicationInstallGoal;
                default:
                    return null;
            }
        }

        private static ValueOperator? ToApiValueOperatior(EventTracking.ValueOperator valueOperator)
        {
            switch (valueOperator)
            {
                case EventTracking.ValueOperator.EqualTo:
                    return ValueOperator.Equals;
                case EventTracking.ValueOperator.LessThan:
                    return ValueOperator.LessThan;
                case EventTracking.ValueOperator.GreaterThan:
                    return ValueOperator.GreaterThan;
                default:
                    return null;
            }
        }

        private static EventTracking.ValueOperator ToMtValueOperator(ValueOperator? valueOperator)
        {
            switch (valueOperator)
            {
                case ValueOperator.GreaterThan:
                    return EventTracking.ValueOperator.GreaterThan;
                case ValueOperator.Equals:
                    return EventTracking.ValueOperator.EqualTo;
                case ValueOperator.LessThan:
                    return EventTracking.ValueOperator.LessThan;
                default:
                    return EventTracking.ValueOperator.NoValue;
            }
        }

        private static ExpressionOperator? ToApiExpressionOperator(EventTracking.ExpressionOperator expressionOperator)
        {
            switch (expressionOperator)
            {
                case EventTracking.ExpressionOperator.BeginsWith:
                    return ExpressionOperator.BeginsWith;
                case EventTracking.ExpressionOperator.Contains:
                    return ExpressionOperator.Contains;
                case EventTracking.ExpressionOperator.EqualsTo:
                    return ExpressionOperator.Equals;
                case EventTracking.ExpressionOperator.RegularExpression:
                    return ExpressionOperator.RegularExpression;
                default:
                    return null;
            }
        }

        private static EventTracking.ExpressionOperator ToMtExpressionOperator(ExpressionOperator? expressionOperator)
        {
            switch (expressionOperator)
            {
                case ExpressionOperator.Contains:
                    return EventTracking.ExpressionOperator.Contains;
                case ExpressionOperator.BeginsWith:
                    return EventTracking.ExpressionOperator.BeginsWith;
                case ExpressionOperator.Equals:
                    return EventTracking.ExpressionOperator.EqualsTo;
                case ExpressionOperator.RegularExpression:
                    return EventTracking.ExpressionOperator.RegularExpression;
                default:
                    return EventTracking.ExpressionOperator.NoExpression;
            }
        }

        public static List<GoalEntityType> CreateGoalTypeFilter(ConversionGoalType conversionGoalTypes)
        {
            var list = new List<GoalEntityType>();
            if (conversionGoalTypes.HasFlag(ConversionGoalType.Url))
            {
                list.Add(GoalEntityType.DestinationGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.Duration))
            {
                list.Add(GoalEntityType.DurationGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.PagesViewedPerVisit))
            {
                list.Add(GoalEntityType.PageViewsPerVisitGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.Event))
            {
                list.Add(GoalEntityType.EventGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.AppInstall))
            {
                list.Add(GoalEntityType.ApplicationInstallGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.OfflineConversion))
            {
                list.Add(GoalEntityType.OfflineConversionGoal);
            }
            if (conversionGoalTypes.HasFlag(ConversionGoalType.InStoreTransaction))
            {
                list.Add(GoalEntityType.InStoreTransactionGoal);
            }
            return list;
        }
    }
}
