﻿namespace Microsoft.BingAds.Advertiser.CampaignManagement.DownloadService
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Bulk.CsvOutput.Filter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ExtensionMethods;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.Download;
    using Microsoft.AdCenter.Shared.MT.Filtering;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Bulk.Output;

    public static class DownloadManagerHelper
    {
        private static readonly Dictionary<FilterEntityTypeV2, List<AdditionalEntityV2>> filterEntityToAdditionalEntityMap = new Dictionary<FilterEntityTypeV2, List<AdditionalEntityV2>>
            {
                {FilterEntityTypeV2.CampaignNegativeKeywords, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignNegativeKeywords } },
                {FilterEntityTypeV2.AdGroupNegativeKeywords, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupNegativeKeywords } },
                {FilterEntityTypeV2.CampaignTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignCriterionTargets } },
                {FilterEntityTypeV2.AdGroupTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupCriterionTargets} },
                {FilterEntityTypeV2.CampaignNegativeSites, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignNegativeSites } },
                {FilterEntityTypeV2.AdGroupNegativeSites, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupNegativeSites } },
                {FilterEntityTypeV2.AdEditorialRejectionReasons, new List<AdditionalEntityV2> { AdditionalEntityV2.AdEditorialRejectionReasons } },
                {FilterEntityTypeV2.KeywordEditorialRejectionReasons, new List<AdditionalEntityV2> { AdditionalEntityV2.KeywordEditorialRejectionReasons } },
                {FilterEntityTypeV2.CampaignSiteLinksAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignSiteLinksAdExtensions, AdditionalEntityV2.SiteLinksAdExtensions } },
                {FilterEntityTypeV2.CampaignLocationAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignLocationAdExtensions, AdditionalEntityV2.LocationAdExtensions } },
                {FilterEntityTypeV2.CampaignCallAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignCallAdExtensions, AdditionalEntityV2.CallAdExtensions} },
                {FilterEntityTypeV2.AdGroupProductTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupProductTargets } },
                {FilterEntityTypeV2.ProductTargetEditorialRejectionReasons, new List<AdditionalEntityV2> { AdditionalEntityV2.ProductTargetEditorialRejectionReasons } },
                {FilterEntityTypeV2.AdGroupSiteLinksAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupSiteLinksAdExtensions, AdditionalEntityV2.SiteLinksAdExtensions } },
                {FilterEntityTypeV2.Campaigns, new List<AdditionalEntityV2> { AdditionalEntityV2.Campaigns, AdditionalEntityV2.CoOpCampaigns } },
                {FilterEntityTypeV2.AdGroups, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroups } },
                {FilterEntityTypeV2.Ads, new List<AdditionalEntityV2> { AdditionalEntityV2.Ads } },
                {FilterEntityTypeV2.Keywords, new List<AdditionalEntityV2> { AdditionalEntityV2.Keywords } },
                {FilterEntityTypeV2.LocationAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.LocationAdExtensions } },
                {FilterEntityTypeV2.CallAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CallAdExtensions } },
                {FilterEntityTypeV2.SiteLinksAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.SiteLinksAdExtensions } },
                {FilterEntityTypeV2.ImageAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.ImageAdExtensions } },
                {FilterEntityTypeV2.CampaignImageAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignImageAdExtensions } },
                {FilterEntityTypeV2.AdGroupImageAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupImageAdExtensions } },

                {FilterEntityTypeV2.NegativeKeywordsList, new List<AdditionalEntityV2> { AdditionalEntityV2.NegativeKeywordsList } },
                {FilterEntityTypeV2.NegativeKeyword, new List<AdditionalEntityV2> { AdditionalEntityV2.NegativeKeyword } },
                {FilterEntityTypeV2.CampaignNegativeKeywordList, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignNegativeKeywordList } },
                {FilterEntityTypeV2.AppAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.AppAdExtensions } },
                {FilterEntityTypeV2.CampaignAppAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignAppAdExtensions, AdditionalEntityV2.AppAdExtensions} },
                {FilterEntityTypeV2.AdGroupAppAdExtensions, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupAppAdExtensions, AdditionalEntityV2.AppAdExtensions} },

                {FilterEntityTypeV2.CampaignNegativeDynamicSearchAdTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignNegativeDynamicSearchAdTargets } },
                {FilterEntityTypeV2.AdGroupDynamicSearchAdTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupDynamicSearchAdTargets } },
                {FilterEntityTypeV2.AdGroupNegativeDynamicSearchAdTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupNegativeDynamicSearchAdTargets } },
                {FilterEntityTypeV2.AdGroupProductPartitions, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupProductPartitions } },
                {FilterEntityTypeV2.CampaignProductScopes, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignProductScopes } },
                {FilterEntityTypeV2.RemarketingLists, new List<AdditionalEntityV2> { AdditionalEntityV2.RemessagingAudiences, AdditionalEntityV2.AdGroupRemessagingAudienceCriterions, AdditionalEntityV2.AdGroupNegativeRemessagingAudienceCriterions, AdditionalEntityV2.CampaignRemessagingAudienceCriterions, AdditionalEntityV2.CampaignNegativeRemessagingAudienceCriterions } },
                {FilterEntityTypeV2.CustomAudiences, new List<AdditionalEntityV2> { AdditionalEntityV2.CustomAudiences, AdditionalEntityV2.AdGroupCustomAudienceCriterions, AdditionalEntityV2.AdGroupNegativeCustomAudienceCriterions, AdditionalEntityV2.CampaignCustomAudienceCriterions, AdditionalEntityV2.CampaignNegativeCustomAudienceCriterions } },
                {FilterEntityTypeV2.InMarketAudiences, new List<AdditionalEntityV2> { AdditionalEntityV2.InMarketAudiences, AdditionalEntityV2.AdGroupInMarketAudienceCriterions, AdditionalEntityV2.AdGroupNegativeInMarketAudienceCriterions, AdditionalEntityV2.CampaignInMarketAudienceCriterions, AdditionalEntityV2.CampaignNegativeInMarketAudienceCriterions } },
                {FilterEntityTypeV2.ProductAudiences, new List<AdditionalEntityV2> { AdditionalEntityV2.ProductAudiences, AdditionalEntityV2.AdGroupProductAudienceCriterions, AdditionalEntityV2.AdGroupNegativeProductAudienceCriterions, AdditionalEntityV2.CampaignProductAudienceCriterions, AdditionalEntityV2.CampaignNegativeProductAudienceCriterions } },
                {FilterEntityTypeV2.SimilarRemarketingLists, new List<AdditionalEntityV2> { AdditionalEntityV2.RemarketingSimilarAudiences, AdditionalEntityV2.AdGroupRemarketingSimilarAudienceCriterions, AdditionalEntityV2.AdGroupNegativeRemarketingSimilarAudienceCriterions, AdditionalEntityV2.CampaignRemarketingSimilarAudienceCriterions, AdditionalEntityV2.CampaignNegativeRemarketingSimilarAudienceCriterions } },

                {FilterEntityTypeV2.Budgets, new List<AdditionalEntityV2> { AdditionalEntityV2.Budgets } },
                {FilterEntityTypeV2.BidStrategies, new List<AdditionalEntityV2> { AdditionalEntityV2.BidStrategies } },
                {FilterEntityTypeV2.AudienceGroups, new List<AdditionalEntityV2> { AdditionalEntityV2.AudienceGroups } },
                {FilterEntityTypeV2.AudienceGroupAssetGroupAssociations, new List<AdditionalEntityV2> { AdditionalEntityV2.AudienceGroupAssetGroupAssociations } },
                {FilterEntityTypeV2.AssetGroups, new List<AdditionalEntityV2> { AdditionalEntityV2.AssetGroups } },
                {FilterEntityTypeV2.AssetGroupListingGroups, new List<AdditionalEntityV2> { AdditionalEntityV2.AssetGroupListingGroups } },
                {FilterEntityTypeV2.CampaignNegativeWebpages, new List<AdditionalEntityV2> { AdditionalEntityV2.CampaignNegativeWebpages } },
                {FilterEntityTypeV2.AssetGroupSearchThemes, new List<AdditionalEntityV2> { AdditionalEntityV2.AssetGroupSearchThemes } },
                {FilterEntityTypeV2.AssetGroupUrlTargets, new List<AdditionalEntityV2> { AdditionalEntityV2.AssetGroupUrlTargets } },
                
                {FilterEntityTypeV2.AccountAppAdExtensions, new List<AdditionalEntityV2>() {AdditionalEntityV2.AccountAppAdExtensions, AdditionalEntityV2.AppAdExtensions } },
                {FilterEntityTypeV2.AccountImageAdExtensions, new List<AdditionalEntityV2>() { AdditionalEntityV2.AccountImageAdExtensions, AdditionalEntityV2.ImageAdExtensions} },
                {FilterEntityTypeV2.AccountLocationAdExtensions, new List<AdditionalEntityV2>() { AdditionalEntityV2.AccountLocationAdExtensions, AdditionalEntityV2.LocationAdExtensions} },

                {FilterEntityTypeV2.LabelAd, new List<AdditionalEntityV2>() { AdditionalEntityV2.Labels, AdditionalEntityV2.DynamicSearchAdLabels, AdditionalEntityV2.ProductAdLabels, AdditionalEntityV2.TextAdLabels, AdditionalEntityV2.ExpandedTextAdLabels, AdditionalEntityV2.AppInstallAdLabels } },
                {FilterEntityTypeV2.LabelCampaign, new List<AdditionalEntityV2>() { AdditionalEntityV2.Labels, AdditionalEntityV2.CampaignLabels } },
                {FilterEntityTypeV2.LabelKeyword, new List<AdditionalEntityV2>() { AdditionalEntityV2.Labels, AdditionalEntityV2.KeywordLabels } },
                {FilterEntityTypeV2.LabelAdGroup, new List<AdditionalEntityV2>() { AdditionalEntityV2.Labels, AdditionalEntityV2.AdGroupLabels } },

                {FilterEntityTypeV2.CampaignNegativeStoreCriterions, new List<AdditionalEntityV2>() { AdditionalEntityV2.CampaignNegativeStoreCriterions } },
                {FilterEntityTypeV2.Image, new List<AdditionalEntityV2>() { AdditionalEntityV2.Images } },
                {FilterEntityTypeV2.Videos, new List<AdditionalEntityV2>() { AdditionalEntityV2.Videos} },

                {FilterEntityTypeV2.AdGroupHotelListingGroups, new List<AdditionalEntityV2> { AdditionalEntityV2.AdGroupHotelListingGroups } },

                {FilterEntityTypeV2.AccountNegativeKeyword, new List<AdditionalEntityV2> { AdditionalEntityV2.AccountNegativeKeyword } },
                {FilterEntityTypeV2.AccountNegativeKeywordList, new List<AdditionalEntityV2> { AdditionalEntityV2.AccountNegativeKeywordList } },
                {FilterEntityTypeV2.AccountNegativeKeywordListAssociation, new List<AdditionalEntityV2> { AdditionalEntityV2.AccountNegativeKeywordListAssociation } },

                {FilterEntityTypeV2.ConversionGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.ConversionGoal } },
                {FilterEntityTypeV2.EventGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.EventGoal } },
                {FilterEntityTypeV2.AppInstallGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.AppInstallGoal } },
                {FilterEntityTypeV2.MultiStageGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.MultiStageGoal } },
                {FilterEntityTypeV2.DurationGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.DurationGoal } },
                {FilterEntityTypeV2.OfflineConversionGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.OfflineConversionGoal } },
                {FilterEntityTypeV2.UrlGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.UrlGoal } },
                {FilterEntityTypeV2.InStoreTransactionGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.InStoreTransactionGoal } },
                {FilterEntityTypeV2.PagesViewedPerVisitGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.PagesViewedPerVisitGoal } },
                {FilterEntityTypeV2.SmartGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.SmartGoal } },
                {FilterEntityTypeV2.InStoreVisitGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.InStoreVisitGoal } },
                {FilterEntityTypeV2.ProductGoal, new List<AdditionalEntityV2> { AdditionalEntityV2.ProductGoal } },
                {FilterEntityTypeV2.ConversionValueRule, new List<AdditionalEntityV2> { AdditionalEntityV2.ConversionValueRule } },

                {FilterEntityTypeV2.AccountPlacementExclusionList, new List<AdditionalEntityV2>{ AdditionalEntityV2.AccountPlacementExclusionList } },
                {FilterEntityTypeV2.AccountPlacementExclusionListItem, new List<AdditionalEntityV2>{ AdditionalEntityV2.AccountPlacementExclusionListItem }  },
                {FilterEntityTypeV2.CampaignAccountPlacementExclusionListAssociation, new List<AdditionalEntityV2>{ AdditionalEntityV2.CampaignAccountPlacementExclusionListAssociation } },
                {FilterEntityTypeV2.AccountPlacementInclusionList, new List<AdditionalEntityV2>{ AdditionalEntityV2.AccountPlacementInclusionList } },
                {FilterEntityTypeV2.AccountPlacementInclusionListItem, new List<AdditionalEntityV2>{ AdditionalEntityV2.AccountPlacementInclusionListItem } },
                {FilterEntityTypeV2.CampaignAccountPlacementInclusionListAssociation, new List<AdditionalEntityV2>{ AdditionalEntityV2.CampaignAccountPlacementInclusionListAssociation } },
            };

        static DownloadManagerHelper ()
        {
            AdExtensionHandler.Instance.EnabledAdExtensionDescriptors.ForEach(descriptor =>
            {
                var additionalEntityType = AddFilterEntityToAdditionalEntityMapping($"{descriptor.ShortTypeName}s", null);

                if (descriptor.CampaignLevelTypeName != null)
                {
                    AddFilterEntityToAdditionalEntityMapping($"{descriptor.CampaignLevelTypeName}s", additionalEntityType);
                }

                if (descriptor.AdGroupLevelTypeName != null)
                {
                    AddFilterEntityToAdditionalEntityMapping($"{descriptor.AdGroupLevelTypeName}s", additionalEntityType);
                }

                if (descriptor.AccountLevelTypeName != null)
                {
                    AddFilterEntityToAdditionalEntityMapping($"{descriptor.AccountLevelTypeName}s", additionalEntityType);
                }
            });
        }

        public static List<AdditionalEntityV2> GetAdditionalEntities(DownloadTaskParameters downloadTaskParameters)
        {
            var entitiesRequested = downloadTaskParameters.DownloadEntityTypesV2.Concat<FilterEntityTypeV2>(GetEntityTypes(downloadTaskParameters.DownloadFilter)).ToHashSet(a => a);

            HashSet<AdditionalEntityV2> additionalEntities = new HashSet<AdditionalEntityV2>();
            foreach (var entityRequested in entitiesRequested)
            {
                foreach (var item in filterEntityToAdditionalEntityMap[entityRequested])
                {
                    additionalEntities.Add(item);
                }
            }

            if (downloadTaskParameters.DownloadEntityTypesV2.Contains(FilterEntityTypeV2.Campaigns))
            {
                foreach (var item in filterEntityToAdditionalEntityMap[FilterEntityTypeV2.Budgets])
                {
                    additionalEntities.Add(item);
                }

                foreach (var item in filterEntityToAdditionalEntityMap[FilterEntityTypeV2.BidStrategies])
                {
                    additionalEntities.Add(item);
                }
            }

            //TODO : murali remove after GA
            if (!GetDownloadSemanticVersion(downloadTaskParameters.FormatVersion).Equals("5.0")
                && !GetDownloadSemanticVersion(downloadTaskParameters.FormatVersion).Equals("6.0"))
            {
                if (additionalEntities.Contains(AdditionalEntityV2.CampaignCriterionTargets))
                {
                    additionalEntities.Add(AdditionalEntityV2.CampaignTargets);
                    additionalEntities.Remove(AdditionalEntityV2.CampaignCriterionTargets);
                }

                if (additionalEntities.Contains(AdditionalEntityV2.AdGroupCriterionTargets))
                {
                    additionalEntities.Add(AdditionalEntityV2.AdGroupTargets);
                    additionalEntities.Remove(AdditionalEntityV2.AdGroupCriterionTargets);
                }
            }

            return additionalEntities.ToList();
        }

        public static void EnsureV2EntityTypesPopulated(DownloadTaskParameters taskParams)
        {
            if (taskParams.DownloadEntityTypesV2.IsNullOrEmpty())
            {
                taskParams.DownloadEntityTypesV2 = taskParams.DownloadEntityTypes.ToV2();
            }

            if (taskParams.DownloadFilter != null && taskParams.DownloadFilter.EntityFilter != null && taskParams.DownloadFilter.EntityFilter.EntityTypeV2 == FilterEntityTypeV2.Unknown)
            {
                var downloadFilter = taskParams.DownloadFilter;
                var entityFilter = downloadFilter.EntityFilter;
                entityFilter.EntityTypeV2 = entityFilter.EntityType.ToV2Single();
                if (downloadFilter.ChildEntityFilters != null)
                {
                    foreach (var filter in downloadFilter.ChildEntityFilters)
                    {
                        if (filter != null && filter.EntityTypeV2 == FilterEntityTypeV2.Unknown)
                        {
                            filter.EntityTypeV2 = filter.EntityType.ToV2Single();
                        }
                    }
                }

                if (downloadFilter.ParentEntityFilters != null)
                {
                    foreach (var filter in downloadFilter.ParentEntityFilters)
                    {
                        if (filter != null && filter.EntityTypeV2 == FilterEntityTypeV2.Unknown)
                        {
                            filter.EntityTypeV2 = filter.EntityType.ToV2Single();
                        }
                    }
                }
            }
        }

        private static AdditionalEntityV2 AddFilterEntityToAdditionalEntityMapping(string entityName, AdditionalEntityV2? listEntity)
        {
            var mapList = new List<AdditionalEntityV2>(2);
            if (listEntity.HasValue)
            {
                mapList.Add(listEntity.Value);
            }

            var filterEntityType = (FilterEntityTypeV2)Enum.Parse(typeof(FilterEntityTypeV2), entityName);
            var additionalEntityType = (AdditionalEntityV2)Enum.Parse(typeof(AdditionalEntityV2), entityName);

            mapList.Add(additionalEntityType);

            if (!filterEntityToAdditionalEntityMap.ContainsKey(filterEntityType))
            {
                filterEntityToAdditionalEntityMap.Add(filterEntityType, mapList);
            }

            return additionalEntityType;
        }

        private static string GetDownloadSemanticVersion(string formatVersion)
        {
            if (string.IsNullOrWhiteSpace(formatVersion))
            {
                return StringTable.BamDownloadDefaultSemanticVersion;
            }
            return formatVersion;
        }

        private static HashSet<FilterEntityTypeV2> GetEntityTypes(DownloadFilter downloadFilter)
        {
            HashSet<FilterEntityTypeV2> filterEntityType = new HashSet<FilterEntityTypeV2> { FilterEntityTypeV2.Campaigns} ;

            if (downloadFilter == null)
                return filterEntityType;

            filterEntityType = new HashSet<FilterEntityTypeV2> { downloadFilter.EntityFilter.EntityTypeV2 };

            if (downloadFilter.ChildEntityFilters != null)
            {
                foreach (var filter in downloadFilter.ChildEntityFilters)
                {
                    filterEntityType.Add(filter.EntityTypeV2);
                }
            }

            if (downloadFilter.ParentEntityFilters != null)
            {
                foreach (var filter in downloadFilter.ParentEntityFilters)
                {
                    filterEntityType.Add(filter.EntityTypeV2);
                }
            }

            return filterEntityType;
        }

    }
}
