﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.EventTracking
{
    using Common;
    using Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.AdCenter.Shared.Async;
    using Shared.MT;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using EventTracking = Entities.EventTracking;

    public partial class EventTrackingEO
    {
        public BatchResult<long> UpdateGoals(CustomerCallContext context, EventTracking.Goal[] goals, bool backFillByExistingGoals = false, bool? skipCurrencyCodeValidation = false, bool? skipGoalCategoryValidation = false)
        {
            var batchResult = new BatchResult<long>();

            ValidateCustomerId(context.Request, batchResult);
            ValidateAccountId(context.Request, batchResult);

            if (batchResult.HasOperationErrors)
            {
                return batchResult;
            }

            if (DynamicConfigValues.EnableMultiAgency &&
                !ValidateAgencyLinkForGoal(context.Request, goals, batchResult, context.Logger))
            {
                return batchResult;
            }

            Dictionary<long, TagTrackingStatus> tagStatus = null;
            List<Tag> allSharedTags = null;

            GetPilotingFeatures(context, out var pilotOfCustomerDict);

            GetAccountPilotingFeatures(context, out var pilotOfAccountDict);

            var getTagResult = GetTagsByCustomerId(context, false, true, fetchTagUsedBy: false);
            if (getTagResult.Failed)
            {
                batchResult.AddErrors(getTagResult.Errors);
                return batchResult;
            }
            allSharedTags = getTagResult.Entities.Where(e => e.Id.HasValue && HasSharingScope(e)).ToList();
            tagStatus = allSharedTags.ToDictionary(e => e.Id.Value, e => e.TrackingStatus);

            var getGoalsResult = new BatchResult<EventTracking.Goal>();
            var goalIdList = goals.OrEmpty().Select(x => (long)x.Id).ToList();
            if (goalIdList.Count > 0)
            {
                var returnServingExternalChannelInfos = goalValidator.IsAccountEnabledForLinkedInCampaign(context);
                getGoalsResult = GetGoalsByIds(context, goalIdList, null, null, tagStatus, returnServingExternalChannelInfos: returnServingExternalChannelInfos);
                batchResult.MergeErrors(getGoalsResult);
            }
            ForceBackfillPropertiesForUpdateGoals(goals, getGoalsResult.Entities, batchResult);

            if (backFillByExistingGoals)
            {
                BackfillGoalsForUpdateGoals(context, goals, getGoalsResult.Entities, batchResult, pilotOfCustomerDict);
            }

            GoalValidator.ValidateExternalChannelsServabilityForUpdate(goals, getGoalsResult.Entities, batchResult);
            GoalValidator.VerifyIncomingGoalNamesDuplicate(goals, batchResult);
            GoalValidator.VerifyGoalType(goals, batchResult);
            GoalValidator.VerifyGoalCategoryForGoalType(goals, batchResult, skipGoalCategoryValidation);

            List <(long, decimal?, decimal?)> goalRevenueChange;
            GoalValidator.VerifyBoostRevenueUpdate(context, goals, getGoalsResult, batchResult, pilotOfCustomerDict, out goalRevenueChange);

            if (context.Logger != null)
            {
                context.Logger.LogInfo($"skipGoalCategoryValidation={skipGoalCategoryValidation ?? false}");
            }

            goalValidator.ValidateUpdateGoals(context, goals, getGoalsResult.Entities, batchResult, pilotOfCustomerDict, skipCurrencyCodeValidation, pilotOfAccountDict);

            if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
            {
                var existedGoalsCollection = GetGoalsByCustomerIdAccountId(context, EventRequestClientType.BingAds, goalTypeFilter: null);
                GoalValidator.VerifiedSameCategoryAndTagForAutoGoal(context, existedGoalsCollection.Entities.ToList(), goals, batchResult);
            }

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            this.CreateSystemManagedTagsForGoals(context, goals, getGoalsResult.Entities.Values.ToDictionary(c => c.Id.Value, c => c), batchResult);

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            var existingClarityEventDeletionGoals = GetGoalsNeedingClarityEventDeletion(goals, getGoalsResult.Entities, batchResult);
            if (existingClarityEventDeletionGoals.Count > 0)
            {
                var clarityResult = DeleteClarityEventInfoWithRetry(context.Logger, existingClarityEventDeletionGoals).WaitAndUnwrapException();
                if (clarityResult.Failed)
                {
                    batchResult.AddErrors(clarityResult.Errors);
                    return batchResult;
                }
            }

            List<long> goalListWithScopeChange = null;

            goalListWithScopeChange = new List<long>();
            for (int index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                {
                    continue;
                }
                if (goals[index].IsAccountLevel != getGoalsResult.Entities[index].IsAccountLevel && goals[index].Id != null)
                {
                    goalListWithScopeChange.Add((long)goals[index].Id);
                }
            }

            var daoResult = new BatchResult<long>();

            var goalLineItemContainer = new LineItemContainer<EventTracking.Goal>(goals.ToList(), batchResult.BatchErrors);
            var usedTagIds = goalLineItemContainer.Where(g => g.LineItem.TagId.HasValue).Select(g => g.LineItem.TagId.Value)
                .Union(getGoalsResult.Entities.Values.Where(g => g.TagId.HasValue).Select(g => g.TagId.Value))
                .ToHashSet();
            var tagIds = allSharedTags.Where(t => usedTagIds.Contains(t.Id.Value)).Select(t => t.Id.Value).ToList();
            daoResult = dao.UpdateUetGoals(context, goalLineItemContainer, pilotOfCustomerDict, tagIds, goalsWithScopeChange: goalListWithScopeChange);
            batchResult.Merge(daoResult);

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            foreach (var change in goalRevenueChange)
            {
                context.Logger.LogInfo($"The user update the goal revenue. The userid is {context.UserId}, the customerid is {context.AdvertiserCustomerId}, the goalid is {change.Item1}. The old revenue is {change.Item2}, the new revenue is {change.Item3}");
            }

            //enable account properties after updated goal successfully
            var tags = getTagResult.Entities ?? new List<EventTracking.Tag>();
            this.AddAccountProperty(context, goals, batchResult, tags);

            var tagIdsWithSharingScope = allSharedTags.Select(e => e.Id.Value).ToHashSet();
            UpdateUetTagUsedByCount(BasicCallContext.Create(context), batchResult.BatchErrors.Keys.ToHashSet(), goalLineItemContainer, getGoalsResult.Entities, tagIdsWithSharingScope);

            return batchResult;
        }

        private static List<EventTracking.Goal> GetGoalsNeedingClarityEventDeletion(EventTracking.Goal[] goals, Dictionary<int, EventTracking.Goal> existingGoals, BatchResult<long> batchResult)
        {
            var goalsToUpdate = new List<EventTracking.Goal>();
            for (var index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;
                var goal = goals[index];
                var existingGoal = existingGoals[index];

                if (string.IsNullOrEmpty(goal.ClarityEventDefinitionId) && !string.IsNullOrEmpty(existingGoal.ClarityEventDefinitionId))
                {
                    goalsToUpdate.Add(existingGoal);
                }
                // On TagId change, delete old clarity event
                else if (!string.IsNullOrEmpty(existingGoal.ClarityEventDefinitionId) && goal.TagId.HasValue && existingGoal.TagId.HasValue && goal.TagId.Value != existingGoal.TagId.Value) {
                    goalsToUpdate.Add(existingGoal);
                    goal.ClarityEventDefinitionId = null;
                    goal.ClarityEventDefinitionObject = null;
                }
            }
            return goalsToUpdate;
        }

        private void ForceBackfillPropertiesForUpdateGoals(EventTracking.Goal[] goals, Dictionary<int, EventTracking.Goal> existingGoals, BatchResult<long> batchResult)
        {
            for (var index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;
                var goal = goals[index];
                var existingGoal = existingGoals[index];
                // ensure the merged goal has valid flag here
                goal.ExcludeFromBidding = goal.ExcludeFromBidding ?? existingGoal.ExcludeFromBidding ?? false;

                // ToDo: this can be removed when UI supported the AttributionModelType field.
                // force backfill AttributionModelType field, because UI will not pass this field before the feature enabled in UI.
                goal.AttributionModelType = goal.AttributionModelType ?? existingGoal.AttributionModelType;

                SetDefaultOperatorEnum(goal);

                // If goal type is null, we fill it using class name.
                FillGoalEntityTypeUsingODataType(goal);
            }
        }

        private void BackfillGoalsForUpdateGoals(CustomerCallContext context, EventTracking.Goal[] goals, Dictionary<int, EventTracking.Goal> existingGoals, BatchResult<long> batchResult, IDictionary<int, bool> PilotOfCustomerDict)
        {
            //This is for API scenario to backfill properties
            for (int index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;

                var goal = goals[index];
                var existingGoal = existingGoals[index];
                var type = goal.Type;
                var originalType = existingGoal.Type;

                //Back fill common value 
                if (string.IsNullOrWhiteSpace(goal.Name))
                {
                    goal.Name = existingGoal.Name;
                }

                goal.LookbackWindowDays = goal.LookbackWindowDays ?? existingGoal.LookbackWindowDays;
                goal.LookbackWindowHours = goal.LookbackWindowHours ?? existingGoal.LookbackWindowHours;
                goal.LookbackWindowMinutes = goal.LookbackWindowMinutes ?? existingGoal.LookbackWindowMinutes;

                goal.GoalCategory = goal.GoalCategory ?? existingGoal.GoalCategory;
                goal.AttributionModelType = goal.AttributionModelType ?? existingGoal.AttributionModelType;

                if ((type == EventTracking.GoalEntityType.ApplicationInstallGoal && originalType == EventTracking.GoalEntityType.ApplicationInstallGoal)
                    || (type == EventTracking.GoalEntityType.InStoreTransactionGoal && originalType == EventTracking.GoalEntityType.InStoreTransactionGoal)
                    || (type == EventTracking.GoalEntityType.OfflineConversionGoal && originalType == EventTracking.GoalEntityType.OfflineConversionGoal)
                    || (type == EventTracking.GoalEntityType.InStoreVisitGoal && originalType == EventTracking.GoalEntityType.InStoreVisitGoal))
                {
                    goal.TagId = existingGoal.TagId;
                }
                else if (type == EventTracking.GoalEntityType.ApplicationInstallGoal &&
                         originalType != EventTracking.GoalEntityType.ApplicationInstallGoal)
                {
                    //Add a system tag ID, will add in UpdateGoalsEO
                }
                else if (goal.TagId == null)
                {
                    goal.TagId = existingGoal.TagId;
                }

                //IsAccountLevel, couldn't update except other goal convert to app install goal
                if (type != EventTracking.GoalEntityType.ApplicationInstallGoal)
                {
                    goal.IsAccountLevel = existingGoal.IsAccountLevel;
                }

                if (goal.Revenue == null)
                {
                    goal.Revenue = existingGoal.Revenue;
                }
                else
                {
                    goal.Revenue.Type = goal.Revenue.Type ?? existingGoal.Revenue.Type;

                    if (goal.Revenue.Value == null && goal.Revenue.Type != EventTracking.GoalValueType.NoValue)
                    {
                        goal.Revenue.Value = existingGoal.Revenue.Value;
                    }

                    if (DynamicConfigValues.EnableGoalNullableProperties)
                    {
                        if (goal.Revenue.CurrencyCode == null)
                        {
                            SetCurrencyCode(goal, existingGoal, context);
                        }
                    }
                    else
                    {
                        if (goal.Revenue.Value != null && goal.Revenue.CurrencyCode == null
                            && goal.IsAccountLevel &&
                            (goal.Type == EventTracking.GoalEntityType.DestinationGoal ||
                             goal.Type == EventTracking.GoalEntityType.EventGoal ||
                             goal.Type == EventTracking.GoalEntityType.OfflineConversionGoal))
                        {
                            if (existingGoal.Revenue.CurrencyCode == null)
                            {
                                AddDefaultValueToCurrencyCode(context, goal);
                            }
                            else
                            {
                                goal.Revenue.CurrencyCode = existingGoal.Revenue.CurrencyCode;
                            }
                        }

                        if (goal.Revenue.Value != null && goal.Revenue.CurrencyCode == null
                            && (goal.Type == EventTracking.GoalEntityType.OfflineConversionGoal
                            || goal.Type == EventTracking.GoalEntityType.DestinationGoal
                            || goal.Type == EventTracking.GoalEntityType.EventGoal))
                        {
                            goal.Revenue.CurrencyCode = existingGoal.Revenue.CurrencyCode;
                        }

                        if ((goal.Type == EventTracking.GoalEntityType.InStoreTransactionGoal || goal.Type == EventTracking.GoalEntityType.InStoreVisitGoal) && goal.Revenue.CurrencyCode == null)
                        {
                            goal.Revenue.CurrencyCode = existingGoal.Revenue.CurrencyCode;
                        }
                    }
                }

                if (goal.ConversionCountType == null)
                {
                    goal.ConversionCountType = (type == EventTracking.GoalEntityType.ApplicationInstallGoal)
                        ? EventTracking.ConversionCountType.All
                        : existingGoal.ConversionCountType;
                }

                goal.Status = goal.Status ?? existingGoal.Status;

                if (originalType == type)//Back fill specific value
                {
                    if (type == EventTracking.GoalEntityType.DestinationGoal)
                    {
                        var destinationGoal = (EventTracking.DestinationGoal)goal;
                        var existingDestinationGoal = (EventTracking.DestinationGoal)existingGoal;
                        if (string.IsNullOrWhiteSpace(destinationGoal.UrlString))
                        {
                            destinationGoal.UrlString = existingDestinationGoal.UrlString;
                        }
                        if (destinationGoal.Operator == EventTracking.ExpressionOperator.NoExpression)
                        {
                            destinationGoal.Operator = existingDestinationGoal.Operator;
                        }
                    }
                    else if (type == EventTracking.GoalEntityType.DurationGoal)
                    {
                        var durationGoal = (EventTracking.DurationGoal)goal;
                        var existingDurationGoal = (EventTracking.DurationGoal)existingGoal;
                        durationGoal.Hours = durationGoal.Hours ?? existingDurationGoal.Hours;
                        durationGoal.Minutes = durationGoal.Minutes ?? existingDurationGoal.Minutes;
                        durationGoal.Seconds = durationGoal.Seconds ?? existingDurationGoal.Seconds;
                        if (durationGoal.Operator == EventTracking.ValueOperator.NoValue)
                        {
                            durationGoal.Operator = existingDurationGoal.Operator;
                        }
                    }
                    else if (type == EventTracking.GoalEntityType.PageViewsPerVisitGoal)
                    {
                        var pageViewGoal = (EventTracking.PageViewsPerVisitGoal)goal;
                        var existingPageViewGoal = (EventTracking.PageViewsPerVisitGoal)existingGoal;
                        if (pageViewGoal.PageViews == null)
                        {
                            pageViewGoal.PageViews = existingPageViewGoal.PageViews;
                        }
                        if (pageViewGoal.Operator == EventTracking.ValueOperator.NoValue)
                        {
                            pageViewGoal.Operator = existingPageViewGoal.Operator;
                        }
                    }
                    else if (type == EventTracking.GoalEntityType.EventGoal)
                    {
                        var eventGoal = (EventTracking.EventGoal)goal;
                        var existingEventGoal = (EventTracking.EventGoal)existingGoal;
                        if (DynamicConfigValues.EnableGoalNullableProperties)
                        {
                            if (string.IsNullOrWhiteSpace(eventGoal.Action))
                            {
                                eventGoal.Action = existingEventGoal.Action;
                            }
                            if (string.IsNullOrWhiteSpace(eventGoal.Category))
                            {
                                eventGoal.Category = existingEventGoal.Category;
                            }
                            if (string.IsNullOrWhiteSpace(eventGoal.Label))
                            {
                                eventGoal.Label = existingEventGoal.Label;
                            }
                            if (eventGoal.Value == null)
                            {
                                eventGoal.Value = existingEventGoal.Value;
                            }

                            if (!string.IsNullOrEmpty(eventGoal.Action) && eventGoal.ActionOperator == EventTracking.ExpressionOperator.NoExpression)
                            {
                                if (existingEventGoal.ActionOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.ActionOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                else
                                {
                                    eventGoal.ActionOperator = existingEventGoal.ActionOperator;
                                }
                            }

                            if (!string.IsNullOrEmpty(eventGoal.Category) && eventGoal.CategoryOperator == EventTracking.ExpressionOperator.NoExpression)
                            {
                                if (existingEventGoal.CategoryOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.CategoryOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                else
                                {
                                    eventGoal.CategoryOperator = existingEventGoal.CategoryOperator;
                                }
                            }

                            if (!string.IsNullOrEmpty(eventGoal.Label) && eventGoal.LabelOperator == EventTracking.ExpressionOperator.NoExpression)
                            {
                                if (existingEventGoal.LabelOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.LabelOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                else
                                {
                                    eventGoal.LabelOperator = existingEventGoal.LabelOperator;
                                }
                            }

                            if (eventGoal.Value != null && eventGoal.ValueOperator == EventTracking.ValueOperator.NoValue)
                            {
                                if (existingEventGoal.ValueOperator == EventTracking.ValueOperator.NoValue)
                                {
                                    eventGoal.ValueOperator = EventTracking.ValueOperator.NoValue;
                                }
                                else
                                {
                                    eventGoal.ValueOperator = existingEventGoal.ValueOperator;
                                }
                            }
                        }
                        else
                        {
                            if (string.IsNullOrWhiteSpace(eventGoal.Action) &&
                                eventGoal.ActionOperator == EventTracking.ExpressionOperator.NoExpression &&
                                string.IsNullOrWhiteSpace(eventGoal.Category) &&
                                eventGoal.CategoryOperator == EventTracking.ExpressionOperator.NoExpression &&
                                string.IsNullOrWhiteSpace(eventGoal.Label) &&
                                eventGoal.LabelOperator == EventTracking.ExpressionOperator.NoExpression &&
                                eventGoal.Value == null &&
                                eventGoal.ValueOperator == EventTracking.ValueOperator.NoValue)
                            {
                                //All reuse existing value
                                eventGoal.Action = existingEventGoal.Action;
                                eventGoal.ActionOperator = existingEventGoal.ActionOperator;
                                eventGoal.Category = existingEventGoal.Category;
                                eventGoal.CategoryOperator = existingEventGoal.CategoryOperator;
                                eventGoal.Label = existingEventGoal.Label;
                                eventGoal.LabelOperator = existingEventGoal.LabelOperator;
                                eventGoal.Value = existingEventGoal.Value;
                                eventGoal.ValueOperator = existingEventGoal.ValueOperator;
                            }
                            else
                            {
                                //add default value
                                if (!string.IsNullOrEmpty(eventGoal.Category) && eventGoal.CategoryOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.CategoryOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                if (!string.IsNullOrEmpty(eventGoal.Action) && eventGoal.ActionOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.ActionOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                if (!string.IsNullOrEmpty(eventGoal.Label) && eventGoal.LabelOperator == EventTracking.ExpressionOperator.NoExpression)
                                {
                                    eventGoal.LabelOperator = EventTracking.ExpressionOperator.EqualsTo;
                                }
                                if (eventGoal.Value != null && eventGoal.ValueOperator == EventTracking.ValueOperator.NoValue)
                                {
                                    eventGoal.ValueOperator = EventTracking.ValueOperator.EqualTo;
                                }
                            }
                        }
                    }
                    else if (type == EventTracking.GoalEntityType.ApplicationInstallGoal)
                    {
                        var appGoal = (EventTracking.ApplicationInstallGoal)goal;
                        var existingAppGoal = (EventTracking.ApplicationInstallGoal)existingGoal;
                        if (string.IsNullOrWhiteSpace(appGoal.ApplicationPlatform))
                        {
                            appGoal.ApplicationPlatform = existingAppGoal.ApplicationPlatform;
                        }
                        if (string.IsNullOrWhiteSpace(appGoal.ApplicationStoreId))
                        {
                            appGoal.ApplicationStoreId = existingAppGoal.ApplicationStoreId;
                        }
                    }
                }
                else // Add default value
                {
                    AddDefaultValueForSpecificGoalProperty(goal);
                }

                bool autoConversionEnabled = false;
                PilotOfCustomerDict?.TryGetValue((int)CustomerFeatureFlag.AutoConversion, out autoConversionEnabled);
                if (autoConversionEnabled)
                {
                    goal.GoalSourceId = goal.GoalSourceId ?? existingGoal.GoalSourceId;
                    goal.IsAutoGoal = goal.IsAutoGoal ?? existingGoal.IsAutoGoal;
                }
            }
        }

        private void FillGoalEntityTypeUsingODataType(EventTracking.Goal goal)
        {
            if (goal.Type != null) return;
            
            var goalTypeFromOData = goal.GetType().Name;
            if(Enum.TryParse(goalTypeFromOData, out GoalEntityType goalEntityType))
            {
                goal.Type = goalEntityType;
            }
        }

        private void UpdateUetTagUsedByCount(BasicCallContext context, HashSet<int> failedItems, LineItemContainer<EventTracking.Goal> goalLineItemContainer, Dictionary<int, EventTracking.Goal> existingGoals, HashSet<long> tagIdsWithSharingScopeHashSet)
        {
            var goalsToDecreaseUsedByCount = new List<EventTracking.Goal>();
            var goalsToIncreaseUsedByCount = new List<EventTracking.Goal>();
            foreach (var goalLineItem in goalLineItemContainer)
            {
                if (!failedItems.Contains(goalLineItem.LineItemId)
                    && existingGoals.ContainsKey(goalLineItem.LineItemId)
                    && existingGoals[goalLineItem.LineItemId].Id.HasValue
                    && goalLineItem.LineItem.Id.HasValue
                    && existingGoals[goalLineItem.LineItemId].Id.Value == goalLineItem.LineItem.Id.Value)
                {
                    var existingGoal = existingGoals[goalLineItem.LineItemId];
                    var newGoal = goalLineItem.LineItem;
                    if (existingGoal.TagId.HasValue && newGoal.TagId.HasValue
                        && existingGoal.TagId.Value != newGoal.TagId.Value)
                    {
                        if (tagIdsWithSharingScopeHashSet.Contains(newGoal.TagId.Value))
                        {
                            goalsToIncreaseUsedByCount.Add(newGoal);
                        }
                        if (tagIdsWithSharingScopeHashSet.Contains(existingGoal.TagId.Value))
                        {
                            goalsToDecreaseUsedByCount.Add(existingGoal);
                        }
                    }
                }
            }

            InlineUpdateUsedBy(context,
                new List<Tuple<List<EventTracking.Goal>, bool>>
                {
                    Tuple.Create(goalsToDecreaseUsedByCount, true),
                    Tuple.Create(goalsToIncreaseUsedByCount, false)
                },
                tagIdsWithSharingScopeHashSet);
        }

        private void SetCurrencyCode(EventTracking.Goal goal, EventTracking.Goal existingGoal, CustomerCallContext context)
        {
            if (goal.Revenue.Value == null)
            {
                return;
            }

            if (goal.Type == GoalEntityType.SmartGoal)
            {
                return;
            }

            if (GoalValidator.IsAvailableForCurrencyCode(goal.Type.Value))
            {
                if (goal.IsAccountLevel)
                {
                    if (existingGoal.Revenue.CurrencyCode != null)
                    {
                        goal.Revenue.CurrencyCode = existingGoal.Revenue.CurrencyCode;
                    }
                    else
                    {
                        AddDefaultValueToCurrencyCode(context, goal);
                    }
                }
                else
                {
                    // If some customer level goal should not be assigned currency code in this way, it will fail in Validation.
                    // Users must pass a currency code for this goal.
                    // e.g. Update a customer level Duration Goal -> URL Goal without new currency code.
                    // Duration Goal don't have currency but URL Goal must have one. Customer level goal forbids adding default currency code. So it will fail.
                    goal.Revenue.CurrencyCode = existingGoal.Revenue.CurrencyCode;
                }
            }
        }
    }
}
