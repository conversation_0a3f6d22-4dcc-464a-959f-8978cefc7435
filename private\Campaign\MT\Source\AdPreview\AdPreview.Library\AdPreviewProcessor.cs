﻿namespace Microsoft.Ads.AdPreview
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using System.Web;
    using Microsoft.AdCenter.Shared.Logging.KustoLogProvider;
    using Microsoft.Ads.AdPreview.Clients;
    using Microsoft.Ads.AdPreview.Models;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// Processor to connect cache and clients when fetching ad recommendations
    /// </summary>
    public class AdPreviewProcessor
    {
        private readonly ILogger<AdPreviewProcessor> logger;
        private readonly ICacheClient cache;
        private readonly IRecommendationsBlobClient blobClient;
        private readonly IAIGCClient aigcClient;
        private readonly AIGCConfig config;
        private readonly IBusinessInfoExtractor businessExtractor;
        private readonly ICCMCClient emailClient;

        public AdPreviewProcessor(
            IAIGCClient aigcClient,
            IBusinessInfoExtractor businessExtractor,
            ICacheClient cache,
            IRecommendationsBlobClient blobClient,
            ICCMCClient emailClient,
            AIGCConfig config,
            ILogger<AdPreviewProcessor> logger)
        {
            this.logger = logger;
            this.cache = cache;
            this.blobClient = blobClient;
            this.aigcClient = aigcClient;
            this.businessExtractor = businessExtractor;
            this.emailClient = emailClient;
            this.config = config;
        }

        /// <summary>
        /// Look up recommendations in cache
        /// If not found, return empty recommendations
        /// if error response if lookupid is null or empty
        /// </summary>
        /// <param name="lookupId"></param>
        /// <param name="useMock">if true, mock response will be returned</param>
        /// <returns></returns>
        public IActionResult LookUp(string lookupId, bool useMock)
        {
            if (string.IsNullOrEmpty(lookupId))
            {
                return CreateErrorResponse(AdPreviewErrors.InvalidPayload, AdPreviewErrors.InvalidLookupId);
            }

            if (useMock)
            {
                bool isNoImageScenario = lookupId == "noImageMockCacheId";
                var website = "https://noodleboatthai.com";
                return new JsonResult(MockAIGCClient.GetMockCacheResponse(website, isNoImageScenario));
            }

            var res = cache.Lookup<AdPreviewCacheObj>(lookupId);

            if (string.IsNullOrEmpty(res?.Website))
            {
                logger.LogInformation("[CacheMiss] Returning empty recommendations");
            }

            return new JsonResult(res ?? new AdPreviewCacheObj());
        }

        /// <summary>
        /// Fetches recommendations for given website and prompt
        ///  - If content was recently fetched then response is returned from cache
        ///  - else response is fetched from AIGC API
        ///  - Response is returned along with a look up id
        /// 
        /// Returns error response if website is not a well-formed url
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetRecommendationsAsync(AdPreviewRequest req, bool useMock)
        {
            string validationError = string.Empty;
            if (req == null || !req.Validate(out validationError))
            {
                return CreateErrorResponse(AdPreviewErrors.InvalidPayload, validationError);
            }

            var lookupId = req.GenerateSignature(config.HmacKey);
            logger.LogProperty("LookupId", lookupId);

            // Mock response for gated tests
            if (useMock)
            {
                bool isNoImageScenario = req.Website == "https://mock_url_no_image.com";
                var mockRes = MockAIGCClient.GetMockCacheResponse(req.Website, isNoImageScenario);
                return new JsonResult(mockRes.ToAdPreviewResponse(lookupId));
            }

            var cacheObj = cache.Lookup<AdPreviewCacheObj>(lookupId);

            if (cacheObj?.Recommendations != null)
            {
                logger.LogInformation("[CacheHit] Returning response from cache. Response: {cacheValue}", cacheObj);
                return new JsonResult(cacheObj?.ToAdPreviewResponse(lookupId));
            }

            var recommendations = new List<Recommendations>();

            logger.LogInformation("[CacheMiss] Fetching recommendations from AIGC service");

            var extractionTask = GetBusinessDetailsAsync(req.Website);
            var recommendationTask = CallAIGCAsync(req);

            await Task.WhenAll(extractionTask, recommendationTask).ConfigureAwait(false);
            BusinessDetails businessDetails = extractionTask.Result;
            var pmaxRec = recommendationTask.Result;
            ProblemDetails knownError = MapErrors(pmaxRec.Errors);

            if (pmaxRec.Errors?.Count > 0 && !string.IsNullOrEmpty(knownError?.Title))
            {
                return CreateErrorResponse(knownError?.Title, knownError.Detail);
            }

            if (pmaxRec.HasErrors)
            {
                return CreateInternalErrorResponse();
            }

            recommendations.Add(pmaxRec);

            var isSaved = cache.Save(lookupId, CreateCacheValue(req, recommendations, businessDetails), expiry: null);
            if (!isSaved)
            {
                logger.LogWarning("Cache save failed");
            }

            return new JsonResult(new AdPreviewResponse
            {
                LookupId = HttpUtility.UrlEncode(lookupId),
                BusinessDetails = businessDetails,
                Recommendations = recommendations
            });
        }

        /// <summary>
        /// Saves the users information along with generated recommendations to storage
        /// Emails these recommendations to user
        /// This allows user to continue the sign up process at a later time
        /// </summary>
        /// <param name="lookupId"></param>
        /// <param name="resumeUrl"></param>
        /// <param name="request"></param>
        /// <param name="emailVersion">2 respresents emails with consent experience</param>
        /// <returns></returns>
        public async Task<IActionResult> OnboardUserAsync(string lookupId, string resumeUrl, UserDetails request, int emailVersion, bool useMock = false)
        {
            var errorMessage = ValidateOnboardPayload(request?.Email, lookupId, resumeUrl);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return CreateErrorResponse(AdPreviewErrors.InvalidPayload, errorMessage);
            }

            var cacheObj = cache.Lookup<AdPreviewCacheObj>(lookupId);
            if (useMock)
            {
                cacheObj = MockAIGCClient.GetMockCacheResponse("https://noodleboatthai.com", isNoImageScenario: false);
            }

            if (cacheObj == null)
            {
                return CreateErrorResponse(AdPreviewErrors.NotFound, AdPreviewErrors.RecommendationsNotFound);
            }

            var onboardReq = new AdPreviewOnboardRequest
            {
                Email = request.Email,
                Website = cacheObj?.Website,
                Prompt = cacheObj?.Prompt,
            };

            var signature = onboardReq.GenerateSignature(config.HmacKey);
            var blobData = cacheObj.ToAdPreviewBlobObj(request);
            var isEmailSent = false;

            if (config.CCMCConfig.IsEmailV2Enabled && emailVersion == 2)
            {
                request.Country = request?.Country?.ToUpper();
                errorMessage = ValidateOnboardV2Payload(request);
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    return CreateErrorResponse(AdPreviewErrors.InvalidPayload, errorMessage);
                }

                blobData.HasUserConsent = emailClient.AddUserConsent(request);
                blobData.UserDetails = request;

                var emailPayload = blobData.ToEmailXmlPayloadV2(signature, resumeUrl);

                isEmailSent = emailClient.SendEmailV2(
                                    AdPreviewNotificationType.PmaxLandingPageSendAdPreviewEmail,
                                    request.Email,
                                    emailPayload.ToString());
            }
            else if (config.CCMCConfig.IsEmailEnabled)
            {
                var emailPayload = blobData.ToEmailXmlPayload(signature, resumeUrl);

                isEmailSent = await emailClient.SendEmailAsync(
                                    AdPreviewNotificationType.PmaxLandingPageSendAdPreviewEmail,
                                    request.Email,
                                    emailPayload.ToString()).ConfigureAwait(false);
            }
            else
            {
                logger.LogInformation("Email not enabled. version: {emailVersion}", emailVersion);
            }

            var isBlobSaved = await blobClient.UploadRecommendationsAsync(
                                    blobData,
                                    lookupId,
                                    overwrite: true).ConfigureAwait(false);

            return isBlobSaved && isEmailSent ? new OkObjectResult(new
            {
                Signature = HttpUtility.UrlEncode(signature),
            }) : CreateInternalErrorResponse();
        }

        /// <summary>
        /// Validates hmac signature and returns recommendations if validation suceeds
        /// Returns 400 response if validation fails
        /// Returns 401 response if lookupid is null or hmac auth check fails
        /// </summary>
        /// <param name="lookupId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<IActionResult> ContinueSignupAsync(string lookupId, string signature, string email)
        {
            var errorMessage = ValidatePayload(email, lookupId);
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return CreateErrorResponse(AdPreviewErrors.InvalidPayload, errorMessage);
            }

            var recommendations = await blobClient.DownloadRecommendationsAsync(lookupId).ConfigureAwait(false);

            if (recommendations == null)
            {
                return CreateErrorResponse(AdPreviewErrors.NotFound, AdPreviewErrors.RecommendationsNotFound);
            }

            var request = new AdPreviewOnboardRequest
            {
                Email = email,
                Website = recommendations.Website,
                Prompt = recommendations.Prompt,
            };

            // Authorization check
            if (string.IsNullOrEmpty(signature) || (signature != request.GenerateSignature(config.HmacKey)))
            {
                return new UnauthorizedResult();
            }

            return new JsonResult(recommendations);
        }

        private async Task<BusinessDetails> GetBusinessDetailsAsync(string website)
        {
            var business = await businessExtractor.ExtractBusinessDetailsAsync(website).ConfigureAwait(false);

            return new BusinessDetails
            {
                BusinessName = business?.BusinessName,
                Address = business?.Addresses?.FirstOrDefault()?.FormattedAddress,
                PhoneNumber = business?.PhoneNumber,
                InternationalPhoneNumber = business?.InternationalPhoneNumber,
            };
        }

        private async Task<Recommendations> CallAIGCAsync(AdPreviewRequest request)
        {
            var response = new Recommendations();

            try
            {
                response = await aigcClient.FetchRecommendationsAsync(
                                    RecommendationTypes.Pmax,
                                    request.Website,
                                    request.Prompt).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                response.HasErrors = true;
            }

            return response;
        }

        private IActionResult CreateErrorResponse(string title, string detail = null)
        {
            logger.LogWarning("ErrorResponse: {Error}", detail ?? title);
            return new BadRequestObjectResult(new ProblemDetails
            {
                Title = title,
                Detail = detail
            });
        }

        private IActionResult CreateInternalErrorResponse()
        {
            var error = new ProblemDetails
            {
                Title = AdPreviewErrors.InternalError
            };

            return new ObjectResult(error)
            {
                StatusCode = (int)HttpStatusCode.InternalServerError,
            };
        }

        private AdPreviewCacheObj CreateCacheValue(AdPreviewRequest req, List<Recommendations> res, BusinessDetails business)
        {
            return new AdPreviewCacheObj
            {
                Website = req.Website,
                Prompt = req.Prompt,
                BusinessDetails = business,
                Recommendations = res
            };
        }

        public ProblemDetails MapErrors(List<AIGCError> errors)
        {
            if (errors != null && errors.Any())
            {
                var errorMsg = string.Join(';', errors.Select(error => error.Message));

                if (errorMsg.Contains("Cannot get HTMLContent", StringComparison.InvariantCultureIgnoreCase))
                {
                    return new ProblemDetails
                    {
                        Title = AdPreviewErrors.UnavailableWebsite,
                    };
                }

                if (errorMsg.Contains("Inappropriate content detected", StringComparison.InvariantCultureIgnoreCase))
                {
                    return new ProblemDetails
                    {
                        Title = AdPreviewErrors.InappropriateContent,
                    };
                }

                List<string> papyrusErrs = [
                    "Papyrus failed to generate selling points",
                    "Editorial check filtered"
                ];

                if (papyrusErrs.Any(err => errorMsg.Contains(err, StringComparison.InvariantCultureIgnoreCase)))
                {
                    return new ProblemDetails
                    {
                        Title = AdPreviewErrors.InsufficientContent,
                    };
                }
            }

            return default;
        }

        public string ValidatePayload(string email, string lookupId)
        {
            if (string.IsNullOrEmpty(lookupId))
            {
                return AdPreviewErrors.InvalidLookupId;
            }

            if (string.IsNullOrEmpty(email))
            {
                return AdPreviewErrors.InvalidEmail;
            }

            return default;
        }

        public string ValidateOnboardPayload(string email, string lookupId, string url)
        {
            var error = ValidatePayload(email, lookupId);
            if (!string.IsNullOrEmpty(error))
            {
                return error;
            }

            if (string.IsNullOrEmpty(url) || !Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                return AdPreviewErrors.InvalidUrl;
            }

            return default;
        }

        public string ValidateOnboardV2Payload(UserDetails userDetails)
        {
            bool isValidLocale = ConsentUtil.TransformAndValidateLocale(userDetails?.Locale, logger, out string validLocale);
            
            if (string.IsNullOrEmpty(userDetails.Country) || string.IsNullOrEmpty(userDetails.Locale)
                || !ConsentUtil.IsSupportedCountry(userDetails.Country)
                || !isValidLocale)
            {
                return AdPreviewErrors.InvalidConsent;
            }

            if (isValidLocale)
            {
                userDetails.Locale = validLocale;
            }

            return default;
        }
    }
}
