﻿namespace Service.CampaignMT;

using System;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;
using Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13;

public static partial class RestApiGeneration
{
    public static class Microsoft_AdCenter_Advertiser_CampaignManagement_Api_DataContracts_V13_EntityModifiers
    {
        private static void AddPrivateField(JsonTypeInfo jsonTypeInfo, Type containingType, string fieldName, string jsonName)
        {
            var field = containingType.GetField(fieldName, BindingFlags.Instance | BindingFlags.NonPublic);
            var jsonPropertyInfo = jsonTypeInfo.CreateJsonPropertyInfo(field.FieldType, jsonName);
            jsonPropertyInfo.Get = field.GetValue;
            jsonPropertyInfo.Set = field.SetValue;
            jsonTypeInfo.Properties.Add(jsonPropertyInfo);
        }

        private static void AddPrivateProperty(JsonTypeInfo jsonTypeInfo, Type containingType, string fieldName, string jsonName)
        {
            var property = containingType.GetProperty(fieldName, BindingFlags.Instance | BindingFlags.NonPublic);
            var jsonPropertyInfo = jsonTypeInfo.CreateJsonPropertyInfo(property.PropertyType, jsonName);
            jsonPropertyInfo.Get = property.GetValue;
            jsonPropertyInfo.Set = property.SetValue;
            jsonTypeInfo.Properties.Add(jsonPropertyInfo);
        }

        public static void CustomizeEntities(JsonTypeInfo jsonTypeInfo)
        {
            if (jsonTypeInfo.Kind != JsonTypeInfoKind.Object)
                return;

            if (CustomizeActions.TryGetValue(jsonTypeInfo.Type, out var customize))
            {
                customize(jsonTypeInfo);
            }
        }

        private static Dictionary<Type, Action<JsonTypeInfo>> CustomizeActions = new Dictionary<Type, Action<JsonTypeInfo>>
        {
            { typeof(AccountNegativeKeywordList), static t => CustomizeAccountNegativeKeywordList(t) },
            { typeof(AccountPlacementExclusionList), static t => CustomizeAccountPlacementExclusionList(t) },
            { typeof(AccountPlacementInclusionList), static t => CustomizeAccountPlacementInclusionList(t) },
            { typeof(ActionAdExtension), static t => CustomizeActionAdExtension(t) },
            { typeof(AdExtensionIdToEntityIdAssociation), static t => CustomizeAdExtensionIdToEntityIdAssociation(t) },
            { typeof(AdGroup), static t => CustomizeAdGroup(t) },
            { typeof(AdRecommendationImageSuggestion), static t => CustomizeAdRecommendationImageSuggestion(t) },
            { typeof(AdRecommendationVideoSuggestion), static t => CustomizeAdRecommendationVideoSuggestion(t) },
            { typeof(AgeCriterion), static t => CustomizeAgeCriterion(t) },
            { typeof(AgeDimension), static t => CustomizeAgeDimension(t) },
            { typeof(AnnotationOptOut), static t => CustomizeAnnotationOptOut(t) },
            { typeof(AppAdExtension), static t => CustomizeAppAdExtension(t) },
            { typeof(AppInstallAd), static t => CustomizeAppInstallAd(t) },
            { typeof(AppInstallGoal), static t => CustomizeAppInstallGoal(t) },
            { typeof(AppSetting), static t => CustomizeAppSetting(t) },
            { typeof(AssetGroup), static t => CustomizeAssetGroup(t) },
            { typeof(AssetGroupListingGroup), static t => CustomizeAssetGroupListingGroup(t) },
            { typeof(AudienceCriterion), static t => CustomizeAudienceCriterion(t) },
            { typeof(AudienceDimension), static t => CustomizeAudienceDimension(t) },
            { typeof(BiddableAdGroupCriterion), static t => CustomizeBiddableAdGroupCriterion(t) },
            { typeof(BiddableCampaignCriterion), static t => CustomizeBiddableCampaignCriterion(t) },
            { typeof(BidMultiplier), static t => CustomizeBidMultiplier(t) },
            { typeof(BMCStore), static t => CustomizeBMCStore(t) },
            { typeof(BrandItem), static t => CustomizeBrandItem(t) },
            { typeof(BrandKit), static t => CustomizeBrandKit(t) },
            { typeof(BrandList), static t => CustomizeBrandList(t) },
            { typeof(CallAdExtension), static t => CustomizeCallAdExtension(t) },
            { typeof(CalloutAdExtension), static t => CustomizeCalloutAdExtension(t) },
            { typeof(CallToActionSetting), static t => CustomizeCallToActionSetting(t) },
            { typeof(Campaign), static t => CustomizeCampaign(t) },
            { typeof(CampaignAdGroupIds), static t => CustomizeCampaignAdGroupIds(t) },
            { typeof(CashbackAdjustment), static t => CustomizeCashbackAdjustment(t) },
            { typeof(CombinedList), static t => CustomizeCombinedList(t) },
            { typeof(CommissionBiddingScheme), static t => CustomizeCommissionBiddingScheme(t) },
            { typeof(CoOpSetting), static t => CustomizeCoOpSetting(t) },
            { typeof(CostPerSaleBiddingScheme), static t => CustomizeCostPerSaleBiddingScheme(t) },
            { typeof(CustomAudience), static t => CustomizeCustomAudience(t) },
            { typeof(CustomerList), static t => CustomizeCustomerList(t) },
            { typeof(CustomEventsRule), static t => CustomizeCustomEventsRule(t) },
            { typeof(DataTableAdExtension), static t => CustomizeDataTableAdExtension(t) },
            { typeof(DayTimeCriterion), static t => CustomizeDayTimeCriterion(t) },
            { typeof(DealCriterion), static t => CustomizeDealCriterion(t) },
            { typeof(DeviceCriterion), static t => CustomizeDeviceCriterion(t) },
            { typeof(DisclaimerAdExtension), static t => CustomizeDisclaimerAdExtension(t) },
            { typeof(DisclaimerSetting), static t => CustomizeDisclaimerSetting(t) },
            { typeof(DurationGoal), static t => CustomizeDurationGoal(t) },
            { typeof(DynamicFeedSetting), static t => CustomizeDynamicFeedSetting(t) },
            { typeof(DynamicSearchAd), static t => CustomizeDynamicSearchAd(t) },
            { typeof(DynamicSearchAdsSetting), static t => CustomizeDynamicSearchAdsSetting(t) },
            { typeof(EditorialReasonCollection), static t => CustomizeEditorialReasonCollection(t) },
            { typeof(EnhancedCpcBiddingScheme), static t => CustomizeEnhancedCpcBiddingScheme(t) },
            { typeof(EventGoal), static t => CustomizeEventGoal(t) },
            { typeof(ExpandedTextAd), static t => CustomizeExpandedTextAd(t) },
            { typeof(Experiment), static t => CustomizeExperiment(t) },
            { typeof(FileImportJob), static t => CustomizeFileImportJob(t) },
            { typeof(FileImportOption), static t => CustomizeFileImportOption(t) },
            { typeof(FilterLinkAdExtension), static t => CustomizeFilterLinkAdExtension(t) },
            { typeof(FixedBid), static t => CustomizeFixedBid(t) },
            { typeof(FlyerAdExtension), static t => CustomizeFlyerAdExtension(t) },
            { typeof(GenderCriterion), static t => CustomizeGenderCriterion(t) },
            { typeof(GenderDimension), static t => CustomizeGenderDimension(t) },
            { typeof(GenreCriterion), static t => CustomizeGenreCriterion(t) },
            { typeof(GoogleImportJob), static t => CustomizeGoogleImportJob(t) },
            { typeof(GoogleImportOption), static t => CustomizeGoogleImportOption(t) },
            { typeof(HotelAd), static t => CustomizeHotelAd(t) },
            { typeof(HotelAdvanceBookingWindowCriterion), static t => CustomizeHotelAdvanceBookingWindowCriterion(t) },
            { typeof(HotelCheckInDateCriterion), static t => CustomizeHotelCheckInDateCriterion(t) },
            { typeof(HotelCheckInDayCriterion), static t => CustomizeHotelCheckInDayCriterion(t) },
            { typeof(HotelDateSelectionTypeCriterion), static t => CustomizeHotelDateSelectionTypeCriterion(t) },
            { typeof(HotelGroup), static t => CustomizeHotelGroup(t) },
            { typeof(HotelLengthOfStayCriterion), static t => CustomizeHotelLengthOfStayCriterion(t) },
            { typeof(HotelSetting), static t => CustomizeHotelSetting(t) },
            { typeof(Image), static t => CustomizeImage(t) },
            { typeof(ImageAdExtension), static t => CustomizeImageAdExtension(t) },
            { typeof(ImageAsset), static t => CustomizeImageAsset(t) },
            { typeof(ImageMediaRepresentation), static t => CustomizeImageMediaRepresentation(t) },
            { typeof(ImpressionBasedRemarketingList), static t => CustomizeImpressionBasedRemarketingList(t) },
            { typeof(InheritFromParentBiddingScheme), static t => CustomizeInheritFromParentBiddingScheme(t) },
            { typeof(InMarketAudience), static t => CustomizeInMarketAudience(t) },
            { typeof(InStoreTransactionGoal), static t => CustomizeInStoreTransactionGoal(t) },
            { typeof(Keyword), static t => CustomizeKeyword(t) },
            { typeof(LeadFormAdExtension), static t => CustomizeLeadFormAdExtension(t) },
            { typeof(LocationAdExtension), static t => CustomizeLocationAdExtension(t) },
            { typeof(LocationCriterion), static t => CustomizeLocationCriterion(t) },
            { typeof(LocationIntentCriterion), static t => CustomizeLocationIntentCriterion(t) },
            { typeof(LogoAdExtension), static t => CustomizeLogoAdExtension(t) },
            { typeof(ManualCpaBiddingScheme), static t => CustomizeManualCpaBiddingScheme(t) },
            { typeof(ManualCpcBiddingScheme), static t => CustomizeManualCpcBiddingScheme(t) },
            { typeof(ManualCpmBiddingScheme), static t => CustomizeManualCpmBiddingScheme(t) },
            { typeof(ManualCpvBiddingScheme), static t => CustomizeManualCpvBiddingScheme(t) },
            { typeof(MaxClicksBiddingScheme), static t => CustomizeMaxClicksBiddingScheme(t) },
            { typeof(MaxConversionsBiddingScheme), static t => CustomizeMaxConversionsBiddingScheme(t) },
            { typeof(MaxConversionValueBiddingScheme), static t => CustomizeMaxConversionValueBiddingScheme(t) },
            { typeof(MaxRoasBiddingScheme), static t => CustomizeMaxRoasBiddingScheme(t) },
            { typeof(MediaMetaData), static t => CustomizeMediaMetaData(t) },
            { typeof(MediaRepresentation), static t => CustomizeMediaRepresentation(t) },
            { typeof(NegativeAdGroupCriterion), static t => CustomizeNegativeAdGroupCriterion(t) },
            { typeof(NegativeCampaignCriterion), static t => CustomizeNegativeCampaignCriterion(t) },
            { typeof(NegativeKeyword), static t => CustomizeNegativeKeyword(t) },
            { typeof(NegativeKeywordList), static t => CustomizeNegativeKeywordList(t) },
            { typeof(NegativeSite), static t => CustomizeNegativeSite(t) },
            { typeof(NewCustomerAcquisitionGoalSetting), static t => CustomizeNewCustomerAcquisitionGoalSetting(t) },
            { typeof(NewsAdExtension), static t => CustomizeNewsAdExtension(t) },
            { typeof(NumberRuleItem), static t => CustomizeNumberRuleItem(t) },
            { typeof(OfflineConversion), static t => CustomizeOfflineConversion(t) },
            { typeof(OfflineConversionAdjustment), static t => CustomizeOfflineConversionAdjustment(t) },
            { typeof(OfflineConversionGoal), static t => CustomizeOfflineConversionGoal(t) },
            { typeof(PagesViewedPerVisitGoal), static t => CustomizePagesViewedPerVisitGoal(t) },
            { typeof(PageVisitorsRule), static t => CustomizePageVisitorsRule(t) },
            { typeof(PageVisitorsWhoDidNotVisitAnotherPageRule), static t => CustomizePageVisitorsWhoDidNotVisitAnotherPageRule(t) },
            { typeof(PageVisitorsWhoVisitedAnotherPageRule), static t => CustomizePageVisitorsWhoVisitedAnotherPageRule(t) },
            { typeof(PercentCpcBiddingScheme), static t => CustomizePercentCpcBiddingScheme(t) },
            { typeof(PerformanceMaxSetting), static t => CustomizePerformanceMaxSetting(t) },
            { typeof(PlacementCriterion), static t => CustomizePlacementCriterion(t) },
            { typeof(PlacementExclusionList), static t => CustomizePlacementExclusionList(t) },
            { typeof(PriceAdExtension), static t => CustomizePriceAdExtension(t) },
            { typeof(ProductAd), static t => CustomizeProductAd(t) },
            { typeof(ProductAudience), static t => CustomizeProductAudience(t) },
            { typeof(ProductCondition), static t => CustomizeProductCondition(t) },
            { typeof(ProductPartition), static t => CustomizeProductPartition(t) },
            { typeof(ProductScope), static t => CustomizeProductScope(t) },
            { typeof(ProfileCriterion), static t => CustomizeProfileCriterion(t) },
            { typeof(ProfileDimension), static t => CustomizeProfileDimension(t) },
            { typeof(PromotionAdExtension), static t => CustomizePromotionAdExtension(t) },
            { typeof(RadiusCriterion), static t => CustomizeRadiusCriterion(t) },
            { typeof(RateBid), static t => CustomizeRateBid(t) },
            { typeof(RemarketingList), static t => CustomizeRemarketingList(t) },
            { typeof(ResponsiveAd), static t => CustomizeResponsiveAd(t) },
            { typeof(ResponsiveSearchAd), static t => CustomizeResponsiveSearchAd(t) },
            { typeof(ResponsiveSearchAdsSetting), static t => CustomizeResponsiveSearchAdsSetting(t) },
            { typeof(ReviewAdExtension), static t => CustomizeReviewAdExtension(t) },
            { typeof(SharedEntityAssociation), static t => CustomizeSharedEntityAssociation(t) },
            { typeof(ShoppingSetting), static t => CustomizeShoppingSetting(t) },
            { typeof(SimilarRemarketingList), static t => CustomizeSimilarRemarketingList(t) },
            { typeof(Site), static t => CustomizeSite(t) },
            { typeof(SitelinkAdExtension), static t => CustomizeSitelinkAdExtension(t) },
            { typeof(StoreCriterion), static t => CustomizeStoreCriterion(t) },
            { typeof(StringRuleItem), static t => CustomizeStringRuleItem(t) },
            { typeof(StructuredSnippetAdExtension), static t => CustomizeStructuredSnippetAdExtension(t) },
            { typeof(TargetCpaBiddingScheme), static t => CustomizeTargetCpaBiddingScheme(t) },
            { typeof(TargetImpressionShareBiddingScheme), static t => CustomizeTargetImpressionShareBiddingScheme(t) },
            { typeof(TargetRoasBiddingScheme), static t => CustomizeTargetRoasBiddingScheme(t) },
            { typeof(TargetSetting), static t => CustomizeTargetSetting(t) },
            { typeof(TextAd), static t => CustomizeTextAd(t) },
            { typeof(TextAsset), static t => CustomizeTextAsset(t) },
            { typeof(ThirdPartyMeasurementSetting), static t => CustomizeThirdPartyMeasurementSetting(t) },
            { typeof(TopicCriterion), static t => CustomizeTopicCriterion(t) },
            { typeof(UrlGoal), static t => CustomizeUrlGoal(t) },
            { typeof(VanityPharmaSetting), static t => CustomizeVanityPharmaSetting(t) },
            { typeof(VerifiedTrackingSetting), static t => CustomizeVerifiedTrackingSetting(t) },
            { typeof(VideoAdExtension), static t => CustomizeVideoAdExtension(t) },
            { typeof(VideoAsset), static t => CustomizeVideoAsset(t) },
            { typeof(Webpage), static t => CustomizeWebpage(t) },
            { typeof(WebpageCondition), static t => CustomizeWebpageCondition(t) }
        };

        private static void CustomizeAccountNegativeKeywordList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AccountNegativeKeywordList";
                        break;
                }
            }
        }

        private static void CustomizeAccountPlacementExclusionList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AccountPlacementExclusionList";
                        break;
                }
            }
        }

        private static void CustomizeAccountPlacementInclusionList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AccountPlacementInclusionList";
                        break;
                }
            }
        }

        private static void CustomizeActionAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ActionAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeAdExtensionIdToEntityIdAssociation(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeAdGroup(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ExactMatchBid":
                    case "PhraseMatchBid":
                    case "BroadMatchBid":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "AdScheduleUseSearcherTimeZone":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AdGroupType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "CpvBid":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "CpmBid":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "McpaBid":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "MultimediaAdsBidAdjustment":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "CommissionRate":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "PercentCpcBid":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UseOptimizedTargeting":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UsePredictiveTargeting":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "FrequencyCapSettings":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdGroup), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeAdRecommendationImageSuggestion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Internal_Template":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ImageMetadata":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
             
            AddPrivateProperty(jsonTypeInfo, typeof(AdRecommendationImageSuggestion), "__do_not_use_Internal_Template", "Internal_Template");
            jsonTypeInfo.Properties[jsonTypeInfo.Properties.Count - 1].ShouldSerialize = (_, value) => value != null;
            jsonTypeInfo.Properties[jsonTypeInfo.Properties.Count - 1].IsRequired = false;
        }

        private static void CustomizeAdRecommendationVideoSuggestion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Internal_Template":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
             
            AddPrivateProperty(jsonTypeInfo, typeof(AdRecommendationVideoSuggestion), "__do_not_use_Internal_Template", "Internal_Template");
            jsonTypeInfo.Properties[jsonTypeInfo.Properties.Count - 1].ShouldSerialize = (_, value) => value != null;
            jsonTypeInfo.Properties[jsonTypeInfo.Properties.Count - 1].IsRequired = false;
        }

        private static void CustomizeAgeCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AgeCriterion";
                        break;
                }
            }
        }

        private static void CustomizeAgeDimension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceGroupDimensionType.Age;
                        break;
                }
            }
        }

        private static void CustomizeAnnotationOptOut(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "PositionInRequest":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
        }

        private static void CustomizeAppAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AppAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeAppInstallAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.AppInstall;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeAppInstallGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.AppInstall;
                        break;
                }
            }
        }

        private static void CustomizeAppSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "AppStore":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => !EqualityComparer<AppStore>.Default.Equals(default, (AppStore)value);
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AppId":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AppSetting";
                        break;
                }
            }
        }

        private static void CustomizeAssetGroup(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Videos":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AssetGroupSearchThemes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AssetGroupUrlTargets":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AssetGroup), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeAssetGroupListingGroup(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ListingGroupPath":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeAudienceCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "AudienceCriterion";
                        break;
                }
            }
        }

        private static void CustomizeAudienceDimension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceGroupDimensionType.Audience;
                        break;
                }
            }
        }

        private static void CustomizeBiddableAdGroupCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "CriterionCashback":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "BiddableAdGroupCriterion";
                        break;
                }
            }
        }

        private static void CustomizeBiddableCampaignCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "CriterionCashback":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "BiddableCampaignCriterion";
                        break;
                }
            }
        }

        private static void CustomizeBidMultiplier(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "BidMultiplier";
                        break;
                }
            }
        }

        private static void CustomizeBMCStore(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "StoreUrl":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeBrandItem(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "BrandItem";
                        break;
                }
            }
        }

        private static void CustomizeBrandKit(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "BusinessName":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "LandScapeLogos":
                        jsonPropertyInfo.Name = "LandscapeLogos";
                        break;
                    case "BrandVoice":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeBrandList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "BrandList";
                        break;
                }
            }
        }

        private static void CustomizeCallAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CallAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeCalloutAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CalloutAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeCallToActionSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "CallToActionOptOut":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CallToActionSetting";
                        break;
                }
            }
        }

        private static void CustomizeCampaign(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "AdScheduleUseSearcherTimeZone":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "BidStrategyId":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "MultimediaAdsBidAdjustment":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalIds":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "DealIds":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsDealCampaign":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "StartDate":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "EndDate":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UseCampaignLevelDates":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                }
            }
        }

        private static void CustomizeCampaignAdGroupIds(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ActiveAdGroupsOnly":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeCashbackAdjustment(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CashbackAdjustment";
                        break;
                }
            }
        }

        private static void CustomizeCombinedList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.CombinedList;
                        break;
                }
            }
        }

        private static void CustomizeCommissionBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CommissionBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeCoOpSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CoOpSetting";
                        break;
                }
            }
        }

        private static void CustomizeCostPerSaleBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CostPerSale";
                        break;
                }
            }
        }

        private static void CustomizeCustomAudience(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.Custom;
                        break;
                }
            }
        }

        private static void CustomizeCustomerList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.CustomerList;
                        break;
                }
            }
        }

        private static void CustomizeCustomEventsRule(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "CustomEvents";
                        break;
                }
            }
        }

        private static void CustomizeDataTableAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DataTableAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeDayTimeCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DayTimeCriterion";
                        break;
                }
            }
        }

        private static void CustomizeDealCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DealCriterion";
                        break;
                }
            }
        }

        private static void CustomizeDeviceCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DeviceCriterion";
                        break;
                }
            }
        }

        private static void CustomizeDisclaimerAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DisclaimerAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeDisclaimerSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DisclaimerSetting";
                        break;
                }
            }
        }

        private static void CustomizeDurationGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.Duration;
                        break;
                }
            }
        }

        private static void CustomizeDynamicFeedSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DynamicFeedSetting";
                        break;
                }
            }
        }

        private static void CustomizeDynamicSearchAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.DynamicSearch;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeDynamicSearchAdsSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "DynamicDescriptionEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "DynamicSearchAdsSetting";
                        break;
                }
            }
        }

        private static void CustomizeEditorialReasonCollection(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Reasons":
                        jsonPropertyInfo.Set = typeof(EditorialReasonCollection).GetProperty("Reasons", BindingFlags.Instance | BindingFlags.Public).SetValue;
                        break;
                }
            }
        }

        private static void CustomizeEnhancedCpcBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "EnhancedCpc";
                        break;
                }
            }
        }

        private static void CustomizeEventGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.Event;
                        break;
                }
            }
        }

        private static void CustomizeExpandedTextAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.ExpandedText;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeExperiment(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Status":
                        jsonPropertyInfo.Name = "ExperimentStatus";
                        break;
                }
            }
        }

        private static void CustomizeFileImportJob(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "NotificationEmail":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "FileImportJob";
                        break;
                }
            }
        }

        private static void CustomizeFileImportOption(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "FileImportOption";
                        break;
                }
            }
        }

        private static void CustomizeFilterLinkAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "FilterLinkAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeFixedBid(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "FixedBid";
                        break;
                }
            }
        }

        private static void CustomizeFlyerAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "FlyerAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeGenderCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "GenderCriterion";
                        break;
                }
            }
        }

        private static void CustomizeGenderDimension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceGroupDimensionType.Gender;
                        break;
                }
            }
        }

        private static void CustomizeGenreCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "GenreCriterion";
                        break;
                }
            }
        }

        private static void CustomizeGoogleImportJob(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "NotificationEmail":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "GoogleImportJob";
                        break;
                }
            }
        }

        private static void CustomizeGoogleImportOption(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "NewAccountNegativeKeywords":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewImageAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewLogoAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewLeadFormAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewBrandSuitability":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewConversionGoals":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "NewCarouselAd":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateAccountNegativeKeywords":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateConversionGoals":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateImageAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateLogoAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateLeadFormAdExtensions":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateBrandSuitability":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateAdCustomizerAttributes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "SearchAndReplaceForCustomParameters":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "SearchAndReplaceForFinalURLSuffix":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AutoDeviceBidOptimization":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AdScheduleUseSearcherTimezone":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "PauseAIMAdGroupIfAllAudienceCriterionNotImported":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "RenameCampaignNameWithSuffix":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateAdUrls":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateSitelinkUrls":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "UpdateAssetAutomationCampaignSetting":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "GoogleImportOption";
                        break;
                }
            }
        }

        private static void CustomizeHotelAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.Hotel;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeHotelAdvanceBookingWindowCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelAdvanceBookingWindowCriterion";
                        break;
                }
            }
        }

        private static void CustomizeHotelCheckInDateCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelCheckInDateCriterion";
                        break;
                }
            }
        }

        private static void CustomizeHotelCheckInDayCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelCheckInDayCriterion";
                        break;
                }
            }
        }

        private static void CustomizeHotelDateSelectionTypeCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelDateSelectionTypeCriterion";
                        break;
                }
            }
        }

        private static void CustomizeHotelGroup(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelGroup";
                        break;
                }
            }
        }

        private static void CustomizeHotelLengthOfStayCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelLengthOfStayCriterion";
                        break;
                }
            }
        }

        private static void CustomizeHotelSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "HotelSetting";
                        break;
                }
            }
        }

        private static void CustomizeImage(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Text":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "Image";
                        break;
                }
            }
        }

        private static void CustomizeImageAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Images":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "DisplayText":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Layouts":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "SourceType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ImageAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeImageAsset(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "TargetWidth":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "TargetHeight":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ImageAsset";
                        break;
                }
            }
        }

        private static void CustomizeImageMediaRepresentation(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ImageMediaRepresentation";
                        break;
                }
            }
        }

        private static void CustomizeImpressionBasedRemarketingList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "CampaignIds":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AdGroupIds":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.ImpressionBasedRemarketingList;
                        break;
                }
            }
        }

        private static void CustomizeInheritFromParentBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "InheritedBidStrategyType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "InheritFromParent";
                        break;
                }
            }
        }

        private static void CustomizeInMarketAudience(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.InMarket;
                        break;
                }
            }
        }

        private static void CustomizeInStoreTransactionGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.InStoreTransaction;
                        break;
                }
            }
        }

        private static void CustomizeKeyword(JsonTypeInfo jsonTypeInfo)
        { 
            AddPrivateField(jsonTypeInfo, typeof(Keyword), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeLeadFormAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "LeadFormAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeLocationAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "LocationAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeLocationCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "LocationCriterion";
                        break;
                }
            }
        }

        private static void CustomizeLocationIntentCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "LocationIntentCriterion";
                        break;
                }
            }
        }

        private static void CustomizeLogoAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "LogoAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeManualCpaBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ManualCpi":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ManualCpaBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeManualCpcBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ManualCpc":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ManualCpc";
                        break;
                }
            }
        }

        private static void CustomizeManualCpmBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ManualCpm";
                        break;
                }
            }
        }

        private static void CustomizeManualCpvBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ManualCpv";
                        break;
                }
            }
        }

        private static void CustomizeMaxClicksBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "MaxClicks";
                        break;
                }
            }
        }

        private static void CustomizeMaxConversionsBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "TargetCpa":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "MaxConversions";
                        break;
                }
            }
        }

        private static void CustomizeMaxConversionValueBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "MaxCpc":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "MaxConversionValueBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeMaxRoasBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "MaxRoasBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeMediaMetaData(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Text":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeMediaRepresentation(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "MediaRepresentation";
                        break;
                }
            }
        }

        private static void CustomizeNegativeAdGroupCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NegativeAdGroupCriterion";
                        break;
                }
            }
        }

        private static void CustomizeNegativeCampaignCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NegativeCampaignCriterion";
                        break;
                }
            }
        }

        private static void CustomizeNegativeKeyword(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NegativeKeyword";
                        break;
                }
            }
        }

        private static void CustomizeNegativeKeywordList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NegativeKeywordList";
                        break;
                }
            }
        }

        private static void CustomizeNegativeSite(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NegativeSite";
                        break;
                }
            }
        }

        private static void CustomizeNewCustomerAcquisitionGoalSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NewCustomerAcquisitionGoalSetting";
                        break;
                }
            }
        }

        private static void CustomizeNewsAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "NewsAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeNumberRuleItem(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "Number";
                        break;
                }
            }
        }

        private static void CustomizeOfflineConversion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "HashedEmailAddress":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "HashedPhoneNumber":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeOfflineConversionAdjustment(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "HashedEmailAddress":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "HashedPhoneNumber":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeOfflineConversionGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "IsExternallyAttributed":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.OfflineConversion;
                        break;
                }
            }
        }

        private static void CustomizePagesViewedPerVisitGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.PagesViewedPerVisit;
                        break;
                }
            }
        }

        private static void CustomizePageVisitorsRule(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "NormalForm":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PageVisitors";
                        break;
                }
            }
        }

        private static void CustomizePageVisitorsWhoDidNotVisitAnotherPageRule(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PageVisitorsWhoDidNotVisitAnotherPage";
                        break;
                }
            }
        }

        private static void CustomizePageVisitorsWhoVisitedAnotherPageRule(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PageVisitorsWhoVisitedAnotherPage";
                        break;
                }
            }
        }

        private static void CustomizePercentCpcBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PercentCpcBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizePerformanceMaxSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "PageFeedIds":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AutoGeneratedTextOptOut":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AutoGeneratedImageOptOut":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "CostPerSaleOptOut":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PerformanceMaxSetting";
                        break;
                }
            }
        }

        private static void CustomizePlacementCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PlacementCriterion";
                        break;
                }
            }
        }

        private static void CustomizePlacementExclusionList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PlacementExclusionList";
                        break;
                }
            }
        }

        private static void CustomizePriceAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PriceAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeProductAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.Product;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeProductAudience(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.Product;
                        break;
                }
            }
        }

        private static void CustomizeProductCondition(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Operator":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeProductPartition(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ProductPartition";
                        break;
                }
            }
        }

        private static void CustomizeProductScope(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ProductScope";
                        break;
                }
            }
        }

        private static void CustomizeProfileCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ProfileCriterion";
                        break;
                }
            }
        }

        private static void CustomizeProfileDimension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceGroupDimensionType.Profile;
                        break;
                }
            }
        }

        private static void CustomizePromotionAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Occasion":
                        jsonPropertyInfo.Name = "PromotionOccasion";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "PromotionAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeRadiusCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "RadiusCriterion";
                        break;
                }
            }
        }

        private static void CustomizeRateBid(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "RateBid";
                        break;
                }
            }
        }

        private static void CustomizeRemarketingList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.RemarketingList;
                        break;
                }
            }
        }

        private static void CustomizeResponsiveAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Videos":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "LongHeadlines":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "ImpressionTrackingUrls":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "VerifiedTrackingSettings":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AdSubType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.ResponsiveAd;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeResponsiveSearchAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.ResponsiveSearch;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeResponsiveSearchAdsSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ResponsiveSearchAdsSetting";
                        break;
                }
            }
        }

        private static void CustomizeReviewAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ReviewAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeSharedEntityAssociation(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "SharedEntityCustomerId":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }

        private static void CustomizeShoppingSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ShoppableAdsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "FeedLabel":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ShoppingSetting";
                        break;
                }
            }
        }

        private static void CustomizeSimilarRemarketingList(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AudienceType.SimilarRemarketingList;
                        break;
                }
            }
        }

        private static void CustomizeSite(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "forwardCompatibilityMap":
                        jsonPropertyInfo.Name = "ForwardCompatibilityMap";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "Site";
                        break;
                }
            }
        }

        private static void CustomizeSitelinkAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "SitelinkAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeStoreCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "StoreCriterion";
                        break;
                }
            }
        }

        private static void CustomizeStringRuleItem(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "String";
                        break;
                }
            }
        }

        private static void CustomizeStructuredSnippetAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Category":
                        jsonPropertyInfo.Name = "Header";
                        break;
                    case "Texts":
                        jsonPropertyInfo.Name = "Values";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "StructuredSnippetAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeTargetCpaBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TargetCpa";
                        break;
                }
            }
        }

        private static void CustomizeTargetImpressionShareBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TargetImpressionShareBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeTargetRoasBiddingScheme(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TargetRoasBiddingScheme";
                        break;
                }
            }
        }

        private static void CustomizeTargetSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TargetSetting";
                        break;
                }
            }
        }

        private static void CustomizeTextAd(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ForwardCompatibilityMap":
                        jsonTypeInfo.Properties.RemoveAt(i);
                        break;
                }
            }
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => AdType.Text;
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(Ad), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeTextAsset(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TextAsset";
                        break;
                }
            }
        }

        private static void CustomizeThirdPartyMeasurementSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "ThirdPartyMeasurementSetting";
                        break;
                }
            }
        }

        private static void CustomizeTopicCriterion(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "TopicCriterion";
                        break;
                }
            }
        }

        private static void CustomizeUrlGoal(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "ViewThroughConversionWindowInMinutes":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "GoalCategory":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "AttributionModelType":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsEnhancedConversionsEnabled":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "IsAutoGoal":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => ConversionGoalType.Url;
                        break;
                }
            }
        }

        private static void CustomizeVanityPharmaSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "DisplayUrlMode":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "WebsiteDescription":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "VanityPharmaSetting";
                        break;
                }
            }
        }

        private static void CustomizeVerifiedTrackingSetting(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "VerifiedTrackingSetting";
                        break;
                }
            }
        }

        private static void CustomizeVideoAdExtension(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "VideoName":
                        jsonPropertyInfo.Name = "Name";
                        break;
                    case "AltText":
                        jsonPropertyInfo.Name = "AlternativeText";
                        break;
                    case "Type":
                        jsonPropertyInfo.Get = _ => "VideoAdExtension";
                        break;
                }
            } 
            AddPrivateField(jsonTypeInfo, typeof(AdExtension), "forwardCompatibilityMap", "ForwardCompatibilityMap");
        }

        private static void CustomizeVideoAsset(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "VideoAsset";
                        break;
                }
            }
        }

        private static void CustomizeWebpage(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Type":
                        jsonPropertyInfo.Get = _ => "Webpage";
                        break;
                }
            }
        }

        private static void CustomizeWebpageCondition(JsonTypeInfo jsonTypeInfo)
        {
            for (int i = jsonTypeInfo.Properties.Count - 1; i >= 0; i--)
            {
                var jsonPropertyInfo = jsonTypeInfo.Properties[i];
                switch (jsonPropertyInfo.Name)
                {
                    case "Operator":
                        jsonPropertyInfo.ShouldSerialize = (_, value) => value != null;
                        jsonPropertyInfo.IsRequired = false;
                        break;
                }
            }
        }
    }
}