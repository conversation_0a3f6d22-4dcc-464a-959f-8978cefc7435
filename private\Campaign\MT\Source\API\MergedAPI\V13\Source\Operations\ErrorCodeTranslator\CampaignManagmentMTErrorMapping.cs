﻿using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Microsoft.AdCenter.Shared.Api.V13;
using Microsoft.AdCenter.Shared.Api.V13.Logging;
using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
using ErrorSource = Microsoft.AdCenter.Shared.Api.V13.DataContracts.ErrorSource;

namespace Microsoft.AdCenter.Advertiser.CampaignManagement.Api.Operations.ErrorCodeTranslator
{

    /// <summary>
    /// This class does the MT error code to Api error code mapping.
    /// </summary>
    public static class CampaignManagmentMTErrorMapping
    {
        private static Dictionary<int, int> errorMapping = new Dictionary<int, int>();
        private static bool errorOccured;

        //This is used for siteplacement deprecation phase I. Will delete it in phase II.
        public const int MTErrorInvalidBiddingModel = 10017;

        //static constructor
        [SuppressMessage("Microsoft.Performance", "CA1810:InitializeReferenceTypeStaticFieldsInline")]
        static CampaignManagmentMTErrorMapping()
        {
            errorOccured = false;
            AddMapping(CampaignManagementErrorCode.InvalidParameters, ErrorCodes.InvalidParameters);
            AddMapping(CampaignManagementErrorCode.AccountWordBreakerLocationIdIsNull, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.InternalError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AccountIdInvalid, ErrorCodes.InvalidAccountId);
            AddMapping(CampaignManagementErrorCode.AdvertiserCustomerIdInvalid, ErrorCodes.InvalidCustomerId);
            AddMapping(CampaignManagementErrorCode.CampaignOperationIsDeprecated, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AccountIdMissingForAggregatorUser, ErrorCodes.AccountIdMissingInRequestHeader);
            AddMapping(CampaignManagementErrorCode.AccountInvalidLifecycleStatus, ErrorCodes.InvalidAccountStatus);
            AddMapping(CampaignManagementErrorCode.AccountInvalidFinancialStatus, ErrorCodes.InvalidAccountStatus);
            AddMapping(CampaignManagementErrorCode.RequestSecurityTicketNull, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.RequestSecurityTicketRolesNull, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.RequestSecurityTicketUserInfoNull, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.CampaignIdAndAccountIdAreBothNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdGroupDoesNotBelongToAccount, ErrorCodes.InvalidAdGroupId);
            AddMapping(CampaignManagementErrorCode.KeywordDoesNotBelongToAccount, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidSearchTerm, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidImportFilePath, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidImportId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.GoogleSyncUserIsNotAuthorized, ErrorCodes.ImportCredentialIdInvalid);
            AddMapping(CampaignManagementErrorCode.GoogleSyncUserPermissionDenied, ErrorCodes.ImportCredentialIdInvalid);
            AddMapping(CampaignManagementErrorCode.ImportFromAccountIdInvalid, ErrorCodes.ImportFromAccountIdInvalid);
            AddMapping(CampaignManagementErrorCode.ImportJobCannotUpdateFromApi, ErrorCodes.ImportJobCannotUpdateFromApi);
            AddMapping(CampaignManagementErrorCode.CustomerNotInGoogleImportApiPilot, ErrorCodes.GoogleDirectImportNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CustomerNotInFileImportApiPilot, ErrorCodes.FileImportNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.TaskSchedulingInvalid, ErrorCodes.ImportSchedulingInvalid);
            AddMapping(CampaignManagementErrorCode.TaskNameInvalid, ErrorCodes.ImportNameInvalid);
            AddMapping(CampaignManagementErrorCode.ImportAlreadyInProgressForAccount, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportIdInvalidForAccount, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportEntityStatusNotAllowed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportEntityFilterInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportValidParentNotFound, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportCampaignTimeZoneNotSpecified, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportCampaignMonthlyBudgetNotSpecified, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportAdTypeInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportCampaignUnknownTimeZone, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportInvalidFileFormat, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportCampaignIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportAdgroupIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TooMuchDataToDownload, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TooMuchExcelDataToDownload, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TooMuchDataForInlineDownload, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportTotalRowsExceedLimit, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportCampaignTargetAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportStatusInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportLocationTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportUnknownConversionTracking, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportUnknownBudgetType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportNotSupportedAdGroupColumnPlacementMaxCpc, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportNotSupportedAdGroupCpmPricingModel, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportNotSupportedShoppingShowcaseAdGroupType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportUnknownMatchType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportMatchTypeRequiredOnKeywordDelete, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportSiteLinkMissingText, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportSiteLinkMissingDestinationUrl, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportSiteLinkTextTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportSiteLinkDestinationUrlTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportDuplicateColumnMapping, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportLocationExtensionMapIconNameTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdIdAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportLanguageTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportPublisherCountriesTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ImportTextAdRequiredDataMissing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidImportJobId, ErrorCodes.InvalidImportJobId);
            AddMapping(CampaignManagementErrorCode.ImportJobNameEmpty, ErrorCodes.ImportJobNameNull);
            AddMapping(CampaignManagementErrorCode.CacheIdNotFound, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBudgetChangeSettings, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterIdsIsEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersListIsEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterHasNoExpressions, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterHasInvalidExpressions, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterExpressionsExceedsMaxCount, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterExpressionsHasInvalidValueType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersExceedsMaxCount, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersNameAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersNameLengthExceeded, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterExpressionsValueLengthExceeded, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersColumnIsNotFilterableInMetadata, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FiltersNameIsEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.PublisherCountriesNotAllowedForAdcenterManaged, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FilterOperationInvalidForDataType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidOrderId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.HoldAlreadyReleased, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.HoldTimeRequired, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.FraudEvaluationResultsIsNullOrEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AssetAdsRelationshipsIsNullOrEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AssetIdsIsNullOrEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdTypeNotEnabledForMarket, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.JustificationTextMissingForInlineAppeal, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DatesMustBeNullWhenAdRotationIsOptimizedForClicks, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdRotationStartDateMustBeBeforeAdGroupEndDate, ErrorCodes.CannotSetStartOrEndDateForAdRotation);
            AddMapping(CampaignManagementErrorCode.AdRotationStartDateMustBeAfterAdGroupStartDate, ErrorCodes.CannotSetStartOrEndDateForAdRotation);
            AddMapping(CampaignManagementErrorCode.AdRotationEndDateMustBeAfterStartDate, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidAdRotationType, ErrorCodes.InvalidAdRotationType);
            AddMapping(CampaignManagementErrorCode.MatchTypeNotUpdated, ErrorCodes.MatchTypeNotUpdated);
            AddMapping(CampaignManagementErrorCode.ImportInvalidColumnMapping, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NoImportColumnIsMapped, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AccountNotEligbleToCreateInlineAppeal, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.IPExclusionLimitExceeded, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.IPAddressInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidMaxLocations, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TimeIntervalNotSupportedForPublisherMonetization, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidSuggestionType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.UnsupportedCountryForSuggestionType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBidAmount, ErrorCodes.InvalidBidAmount);
            AddMapping(CampaignManagementErrorCode.UnsupportedAdPosition, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidCategoryId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.UnsupportedMatchType, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.GeoLocationNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidGeoLocation, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidMaxLocalBudgetOptions, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBudget, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidNumberOfTileIds, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidLocalBuinessName, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TimeIntervalNotSupported, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InValidLandScapeGenerated, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NoKewordsinBothBags, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.ZeroRowsFromDB, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.LowCPC, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BillingCallbackEntitiesNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BillingCallbackEntityLimitExceeded, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DuplicateCampaignId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BillingCallbackEntityNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchTermsNotEnabledForCustomer, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchCampaignsFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchAdGroupsFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchAdsFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchKeywordsFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchTextTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.SearchContextInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryIDNotValid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryMappingAddFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryMappingUpdateFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryMappingGetByCustomerIDFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryMappingGetbyIdsFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryAdGroupSetBatchLimitExceed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryAdGroupInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryToAdgroupMappingGetByCustomerIDFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NoCategoriesFound, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryToAdgroupMappingAddFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryClickRangeUpdateFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TagLineTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessToCategoryClickRangeMappingGetByCustomerIDFailed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryBingPlacesInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryDataDetailsMissing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InValidCountForCategoryAdExtension, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidTemplateId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidAdId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.MissingAdIdInTemplateTable, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CategoryIdDeleted, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessIdCategoryIdAlreadyPresent, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessIdCategoryIdNotAvaliable, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBingPlaceBusiness, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessIdAlreadyPresent, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessIdNotPresent, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidMaxCpc, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DuplicateBusinessListing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidCategorySelections, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidId, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidStatus, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidRadius, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidNegativeKeyword, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessAddress, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidCategoryOrSubcategory, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessListNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TextAdForExpressIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidMonthyBudget, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessInfoIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AddressIsNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidStateOrProvince, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DuplicateCategorySelections, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessNameWithInvalidMinLength, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessNameWithInvalidMaxLength, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.BusinessNameWithInvalidControlCharacters, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidCountryCode, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidTargetAddress, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AddressCityInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AddressStateOrProvinceInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessName, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidState, ErrorCodes.InvalidState);
            AddMapping(CampaignManagementErrorCode.InvalidCity, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.UserIsNotAuthorized, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidPostalCode, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidResellerCustomer, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidGeoCode, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CloudTableFailure, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.ViewThroughAccountSettingValueInvalid, ErrorCodes.ViewThroughAccountSettingValueInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForViewThroughConversion, ErrorCodes.CustomerNotEligibleForViewThroughConversion);
            AddMapping(CampaignManagementErrorCode.LinkedInInferenceAccountSettingValueInvalid, ErrorCodes.ProfileExpansionSettingInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForLinkedInTargeting, ErrorCodes.CustomerNotEligibleForProfileTargeting);
            AddMapping(CampaignManagementErrorCode.InvalidGoalViewThroughLookbackWindow, ErrorCodes.InvalidViewThroughConversionWindowInMinutes);
            AddMapping(CampaignManagementErrorCode.ViewThroughConversionNotApplicableToGoalType, ErrorCodes.ViewThroughConversionNotApplicableToGoalType);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForConversionGoalSelection, ErrorCodes.CustomerNotEligibleForConversionGoalSelection);

            //Targeting Improvement Errors
            AddMapping(CampaignManagementErrorCode.InvalidDayTimeTarget, ErrorCodes.InvalidDayTimeTarget);
            AddMapping(CampaignManagementErrorCode.TargetsDayTimeBidBatchLimitExceeded, ErrorCodes.TargetsDayTimeBidBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetDayTimeIntervalInvalid, ErrorCodes.TargetDayTimeIntervalInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForDayTimePilot, ErrorCodes.CustomerNotEnabledForDayTimePilot);
            AddMapping(CampaignManagementErrorCode.DuplicatePostalCodeTarget, ErrorCodes.DuplicatePostalCodeTarget);
            AddMapping(CampaignManagementErrorCode.PostalCodeNotSupportedForCustomer, ErrorCodes.PostalCodeNotSupportedForCustomer);
            AddMapping(CampaignManagementErrorCode.TargetDayTimeAndDayHourAreExclusive, ErrorCodes.TargetDayTimeAndDayHourAreExclusive);
            AddMapping(CampaignManagementErrorCode.TargetDayTimeOverlapping, ErrorCodes.TargetDayTimeOverlapping);
            AddMapping(CampaignManagementErrorCode.InvalidDayTimeTargetBidAdjustment, ErrorCodes.InvalidDayTimeTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.CampaignServiceInvalidTarget, ErrorCodes.InvalidTarget);
            AddMapping(CampaignManagementErrorCode.InvalidGeolocationsFileVersion, ErrorCodes.InvalidVersion);
            AddMapping(CampaignManagementErrorCode.InvalidLanguageLocale, ErrorCodes.InvalidLanguageLocale);
            AddMapping(CampaignManagementErrorCode.InvalidGeolocationsFileFormat, ErrorCodes.InvalidGeolocationsFileFormat);

            //Topic targeting errors
            AddMapping(CampaignManagementErrorCode.InvalidTopicIdTarget, ErrorCodes.InvalidTopicIdTarget);
            AddMapping(CampaignManagementErrorCode.InvalidTopicTargetBidAdjustment, ErrorCodes.InvalidTopicTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.AccountIsNotEligibleForTopicTargeting, ErrorCodes.AccountIsNotEligibleForTopicTargeting);

            AddMapping(CampaignManagementErrorCode.InvalidTargetCombination, ErrorCodes.InvalidTargetCombination);

            //AdExtensionDeviceTarget Errors
            AddMapping(CampaignManagementErrorCode.AdExtensionDeviceTargetEntityLimitExceeded, ErrorCodes.AdExtensionDeviceTargetEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AdExtensionDeviceTargeEntityIdsNullOrEmpty, ErrorCodes.AdExtensionDeviceTargeEntityIdsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionDeviceTargeEntityIdsInvalid, ErrorCodes.AdExtensionDeviceTargeEntityIdsInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForAdExtensionDeviceTargetPilot, ErrorCodes.CustomerNotEnabledForAdExtensionDeviceTargetPilot);

            //Errors during Appeal Creation
            AddMapping(CampaignManagementErrorCode.EditorialAppealEntityErrorUndefined, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.EditorialAppealCreationFault, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.EditorialAppealResponseProcessingFault, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.EditorialUnsupportedAppealEntity, ErrorCodes.EntityTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.EditorialAppealCreationQuotaExceeded, ErrorCodes.EditorialAppealCreationQuotaExceeded);
            AddMapping(CampaignManagementErrorCode.EditorialAppealCreationQuotaExceededForLast24Hours, ErrorCodes.EditorialAppealCreationQuotaExceededForLast24Hours);
            AddMapping(CampaignManagementErrorCode.EditorialAppealEntityAlreadyAppealed, ErrorCodes.EditorialAppealEntityAlreadyAppealed);

            // These errors should not occur. Hence mapping them to Internal Error
            AddMapping(CampaignManagementErrorCode.PermissionDenied, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.CustomerLocked, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AdvertiserCustomerInvalidFinancialstatus, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AgencyCustomerInvalidLifecycleStatus, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AgencyCustomerLocked, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.SaleshouseCustomerInvalidFinancialStatus, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.SaleshouseCustomerLocked, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.BilltoCustomerInvalidLifecycleStatus, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.BilltoCustomerLocked, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.BilltoCustomerInvalidFinancialStatus, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.SalesHouseCustomerLifeCycleStatusInvalid, ErrorCodes.InternalError);

            AddMapping(CampaignManagementErrorCode.AccountLocked, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.SystemInReadOnlyMode, ErrorCodes.SystemInReadOnlyMode);
            AddMapping(CampaignManagementErrorCode.AdvertiserCustomerInvalidLifecycleStatus, ErrorCodes.InvalidCustomerStatus);

            AddMapping(CampaignManagementErrorCode.NegativeKeywordTooLong, ErrorCodes.InvalidNegativeKeyword);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordsTotalLengthExceeded, ErrorCodes.NegativeKeywordsTotalLengthExceeded);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordHasInvalidChars, ErrorCodes.InvalidNegativeKeyword);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordMatchesKeyword, ErrorCodes.NegativeKeywordMatchesKeyword);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordsEntityLimitExceeded, ErrorCodes.NegativeKeywordsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordsLimitExceeded, ErrorCodes.NegativeKeywordsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordNotPassed, ErrorCodes.NegativeKeywordsNotPassed);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordItemExceedsLimit, ErrorCodes.NegativeKeywordsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordHasInvalidMatchTypeFormat, ErrorCodes.NegativeKeywordHasInvalidMatchTypeFormat);
            AddMapping(CampaignManagementErrorCode.EditorialAdExists, ErrorCodes.DuplicateAd);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdExists, ErrorCodes.DefaultAdExists);
            AddMapping(CampaignManagementErrorCode.EditorialSyntaxErrorInTitle, ErrorCodes.SyntaxErrorInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialSyntaxErrorInText, ErrorCodes.SyntaxErrorInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialSyntaxErrorInDisplayUrl, ErrorCodes.SyntaxErrorInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInTitle, ErrorCodes.ForbiddenTextInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInText, ErrorCodes.ForbiddenTextInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInDisplayUrl, ErrorCodes.ForbiddenTextInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInKeywordText, ErrorCodes.ForbiddenWordInKeywordText);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInParam1, ErrorCodes.ForbiddenWordInParam1);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInParam2, ErrorCodes.ForbiddenWordInParam2);
            AddMapping(CampaignManagementErrorCode.EditorialForbiddenKeywordInParam3, ErrorCodes.ForbiddenWordInParam3);
            AddMapping(CampaignManagementErrorCode.EditorialIncorrectAdFormatInTitle, ErrorCodes.IncorrectAdFormatInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialIncorrectAdFormatInText, ErrorCodes.IncorrectAdFormatInText);
            AddMapping(CampaignManagementErrorCode.EditorialIncorrectAdFormatInDisplayUrl, ErrorCodes.IncorrectAdFormatInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInTitle, ErrorCodes.TooMuchAdTextInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInText, ErrorCodes.TooMuchAdTextInText);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInDisplayUrl, ErrorCodes.TooMuchAdTextInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInDestinationUrl, ErrorCodes.TooMuchAdTextInDestinationUrl);
            AddMapping(CampaignManagementErrorCode.EditorialNotEnoughAdText, ErrorCodes.NotEnoughAdText);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInTitle, ErrorCodes.ExclusiveWordInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInText, ErrorCodes.ExclusiveWordInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInDisplayUrl, ErrorCodes.ExclusiveWordInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInKeywordText, ErrorCodes.ExclusiveWordInKeywordText);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInParam1, ErrorCodes.ExclusiveWordInParam1);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInParam2, ErrorCodes.ExclusiveWordInParam2);
            AddMapping(CampaignManagementErrorCode.EditorialExclusiveKeywordInParam3, ErrorCodes.ExclusiveWordInParam3);
            AddMapping(CampaignManagementErrorCode.EditorialInvalidDisplayUrlFormat, ErrorCodes.InvalidAdDisplayUrlFormat);
            AddMapping(CampaignManagementErrorCode.EditorialInvalidDestinationUrlFormat, ErrorCodes.InvalidAdDestinationUrlFormat);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdSyntaxErrorInTitle, ErrorCodes.DefaultAdSyntaxErrorInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdSyntaxErrorInText, ErrorCodes.DefaultAdSyntaxErrorInText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdSyntaxErrorInDisplayUrl, ErrorCodes.DefaultAdSyntaxErrorInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdForbiddenKeywordInTitle, ErrorCodes.DefaultAdForbiddenWordInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdForbiddenKeywordInText, ErrorCodes.DefaultAdForbiddenWordInText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdForbiddenKeywordInDisplayUrl, ErrorCodes.DefaultAdForbiddenWordInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdIncorrectAdFormatInTitle, ErrorCodes.DefaultAdIncorrectAdFormatInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdIncorrectAdFormatInText, ErrorCodes.DefaultAdIncorrectAdFormatInText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdIncorrectAdFormatInDisplayUrl, ErrorCodes.DefaultAdIncorrectAdFormatInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdTooMuchAdTextInTitle, ErrorCodes.DefaultAdTooMuchTextInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdTooMuchAdTextInText, ErrorCodes.DefaultAdTooMuchTextInText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdTooMuchAdTextInDisplayUrl, ErrorCodes.DefaultAdTooMuchTextInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdTooMuchAdTextInDestinationUrl, ErrorCodes.DefaultAdTooMuchTextInDestinationUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdNotEnoughAdText, ErrorCodes.DefaultAdNotEnoughAdText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdExclusiveKeywordInTitle, ErrorCodes.DefaultAdExclusiveWordInTitle);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdExclusiveKeywordInText, ErrorCodes.DefaultAdExclusiveWordInText);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdExclusiveKeywordInDisplayUrl, ErrorCodes.DefaultAdExclusiveWordInDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdInvalidDisplayUrlFormat, ErrorCodes.DefaultAdInvalidDisplayUrlFormat);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdInvalidDestinationUrlFormat, ErrorCodes.DefaultAdInvalidDestinationUrlFormat);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInTitleAcrossAllAssociations, ErrorCodes.TooMuchTextInTitleAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInTextAcrossAllAssociations, ErrorCodes.TooMuchTextInTextAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchAdTextInDisplayUrlAcrossAllAssociations, ErrorCodes.TooMuchTextInDisplayUrlAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumHasNoDigits, ErrorCodes.InvalidPhoneNumber);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumHasInvalidChars, ErrorCodes.InvalidPhoneNumber);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumNotAllowedForCountry, ErrorCodes.PhoneNumberNotAllowedForCountry);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumBlocked, ErrorCodes.BlockedPhoneNumber);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumTooMuchText, ErrorCodes.TooMuchAdTextInPhoneNumber);
            AddMapping(CampaignManagementErrorCode.EditorialBusinessNameTooMuchText, ErrorCodes.TooMuchAdTextInBusinessName);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumNotAllowedInAdTitle, ErrorCodes.PhoneNumberNotAllowedInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumNotAllowedInAdText, ErrorCodes.PhoneNumberNotAllowedInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumNotAllowedInAdDisplayUrl, ErrorCodes.PhoneNumberNotAllowedInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialPhoneNumNotAllowedInAdBusinessName, ErrorCodes.PhoneNumberNotAllowedInAdBusinessName);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdTitle, ErrorCodes.EditorialErrorInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdText, ErrorCodes.EditorialErrorInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdDisplayUrl, ErrorCodes.EditorialErrorInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdDestinationUrl, ErrorCodes.EditorialErrorInAdDestinationUrl);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdBusinessName, ErrorCodes.EditorialErrorInAdBusinessName);
            AddMapping(CampaignManagementErrorCode.EditorialErrorInAdPhoneNumber, ErrorCodes.EditorialErrorInAdPhoneNumber);
            AddMapping(CampaignManagementErrorCode.EditorialAdTitleBlankAcrossAllAssociations, ErrorCodes.EditorialAdTitleBlankAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialAdTitleBlank, ErrorCodes.EditorialAdTitleBlank);
            AddMapping(CampaignManagementErrorCode.EditorialAdTextBlankAcrossAllAssociations, ErrorCodes.EditorialAdTextBlankAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialAdTextBlank, ErrorCodes.EditorialAdTextBlank);
            AddMapping(CampaignManagementErrorCode.EditorialAdDisplayUrlBlankAcrossAllAssociations, ErrorCodes.EditorialAdDisplayUrlBlankAcrossAllAssociations);
            AddMapping(CampaignManagementErrorCode.EditorialAdDisplayUrlBlank, ErrorCodes.EditorialAdDisplayUrlBlank);
            AddMapping(CampaignManagementErrorCode.EditorialAdDestinationUrlBlank, ErrorCodes.EditorialAdDestinationUrlBlank);
            AddMapping(CampaignManagementErrorCode.EditorialDefaultAdTooMuchAdTextInBusinessName, ErrorCodes.DefaultAdTooMuchTextInBusniessName);
            AddMapping(CampaignManagementErrorCode.EditorialGenericError, ErrorCodes.EditorialGenericError);
            AddMapping(CampaignManagementErrorCode.EditorialError, ErrorCodes.EditorialGenericError);
            AddMapping(CampaignManagementErrorCode.UserNotFoundOrInactive, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.NotAllowedToDoCurrentOperation, ErrorCodes.CannotPerformCurrentOperation);

            AddMapping(CampaignManagementErrorCode.CampaignsNotPassed, ErrorCodes.CampaignsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.CampaignNameMissing, ErrorCodes.InvalidCampaignName);
            AddMapping(CampaignManagementErrorCode.CampaignNameTooLong, ErrorCodes.InvalidCampaignName);
            AddMapping(CampaignManagementErrorCode.CampaignNameHasInvalidChars, ErrorCodes.InvalidCampaignName);
            AddMapping(CampaignManagementErrorCode.CampaignBudgetAmountIsAboveLimit, ErrorCodes.InvalidMonthlyBudget);
            AddMapping(CampaignManagementErrorCode.CampaignWithIdAlreadyExists, ErrorCodes.DuplicateCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignIdInvalid, ErrorCodes.InvalidCampaignId);
            AddMapping(CampaignManagementErrorCode.CampaignTimestampMismatch, ErrorCodes.ConcurrentStoreModification);
            AddMapping(CampaignManagementErrorCode.CampaignCannotPauseResumeDueToInvalidStatus, ErrorCodes.InvalidCampaignStatus);
            AddMapping(CampaignManagementErrorCode.CampaignBudgetAmountIsBelowConfiguredLimit, ErrorCodes.InvalidMonthlyBudget);
            AddMapping(CampaignManagementErrorCode.CampaignHasInvalidStatus, ErrorCodes.InvalidCampaignStatus);
            AddMapping(CampaignManagementErrorCode.CampaignIsDeleted, ErrorCodes.InvalidCampaignStatus);
            AddMapping(CampaignManagementErrorCode.CampaignWithNameAlreadyExists, ErrorCodes.DuplicateCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignBudgetLessThanAdGroupBudget, ErrorCodes.CampaignBudgetLessThanAdGroupBudget);
            AddMapping(CampaignManagementErrorCode.CampaignNotEligibleToModifyCashBack, ErrorCodes.CampaignNotEligibleForCashBack);
            AddMapping(CampaignManagementErrorCode.CampaignBudgetAmountIsLessThanSpendAmount, ErrorCodes.CampaignBudgetAmountIsLessThanSpendAmount);
            AddMapping(CampaignManagementErrorCode.SmartCampaignNotEnabledForCustomer, ErrorCodes.NotSupportedForThisCampaignType); // Reuse existing error code and do not expose smart campaign to external users
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForSmartCampaign, ErrorCodes.EntityTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.BusinessIdInvalid, ErrorCodes.EntityIdInvalid);

            AddMapping(CampaignManagementErrorCode.PhoneExtensionDataNotNull, ErrorCodes.PhoneExtensionDataNotNull);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleToSetClickToCallOnly, ErrorCodes.CustomerNotEligibleToSetClickToCallOnly);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionInvalidCountry, ErrorCodes.PhoneExtensionInvalidCountry);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionPhoneNumberHasInvalidChars, ErrorCodes.PhoneExtensionPhoneNumberHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionPhoneNumberMissing, ErrorCodes.PhoneExtensionPhoneNumberMissing);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionPhoneNumberTooLong, ErrorCodes.PhoneExtensionPhoneNumberTooLong);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionPhoneNumberInvalid, ErrorCodes.PhoneExtensionPhoneNumberInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleToSetAdExtension, ErrorCodes.CustomerNotEligibleToSetAdExtension);
            AddMapping(CampaignManagementErrorCode.BusinessLocationReadOnlyForLocationAdExtensionV2Pilot, ErrorCodes.BusinessLocationReadOnlyForLocationAdExtensionV2Pilot);
            AddMapping(CampaignManagementErrorCode.LocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot, ErrorCodes.LocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot);
            AddMapping(CampaignManagementErrorCode.PhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot, ErrorCodes.PhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot);

            AddMapping(CampaignManagementErrorCode.WrongPartitionId, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.DbMigrationLockOperationError, ErrorCodes.InternalError);

            // These are mapped to Internal Error, since this error should never be reaching us.
            AddMapping(CampaignManagementErrorCode.CampaignStartDateLaterThanAdGroupStartDate, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.CampaignDatesOverlapwithAdGroupDates, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.TimestampRequiredForEdit, ErrorCodes.ConcurrentStoreModification);
            AddMapping(CampaignManagementErrorCode.MarketCountryOrLanguageForAccountInvalid, ErrorCodes.InternalError);

            AddMapping(CampaignManagementErrorCode.CampaignCannotModifyStartDate, ErrorCodes.CannotChangeTimezoneOrStartDateWithActiveAdGroups);
            AddMapping(CampaignManagementErrorCode.MaxActiveCampaignsLimitReached, ErrorCodes.MaxCampaignsReached);
            AddMapping(CampaignManagementErrorCode.CampaignDailyTargetBudgetAmountIsInvalid, ErrorCodes.InvalidDailyBudget);
            AddMapping(CampaignManagementErrorCode.CampaignAlreadyExists, ErrorCodes.CampaignAlreadyExists);
            AddMapping(CampaignManagementErrorCode.CampaignBudgetTypeInvalid, ErrorCodes.InvalidBudgetType);
            AddMapping(CampaignManagementErrorCode.KeywordVariantMatchTypePilotNotEnabledForCustomer, ErrorCodes.CampaignServiceKeywordVariantMatchNotEnabledForPilot);

            AddMapping(CampaignManagementErrorCode.AdGroupsNotPassed, ErrorCodes.AdGroupsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdGroupNameMissing, ErrorCodes.InvalidAdGroupName);
            AddMapping(CampaignManagementErrorCode.AdGroupNameTooLong, ErrorCodes.InvalidAdGroupName);
            AddMapping(CampaignManagementErrorCode.AdGroupNameHasInvalidChars, ErrorCodes.InvalidAdGroupName);
            AddMapping(CampaignManagementErrorCode.UserNotAuthorizedForDistributionChannel, ErrorCodes.UserNotAuthorizedForDistributionChannel);

            // This error should not occur. Hence mapped to Internal Error
            AddMapping(CampaignManagementErrorCode.DistributionSiteNotAuthorizedForKeywordLanguage, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AdGroupDistributionChannelNoLanguage, ErrorCodes.InternalError);

            AddMapping(CampaignManagementErrorCode.AdGroupMediumMissing, ErrorCodes.AdGroupMissingAdMedium);
            AddMapping(CampaignManagementErrorCode.AdGroupUserNotAllowedContentMedium, ErrorCodes.AdGroupUserNotAllowedContentMedium);
            AddMapping(CampaignManagementErrorCode.AdGroupStartDateLessThanCurrentDate, ErrorCodes.AdGroupStartDateLessThanCurrentDate);
            AddMapping(CampaignManagementErrorCode.AdGroupStartDateLessThanEndDate, ErrorCodes.AdGroupEndDateShouldBeAfterStartDate);
            AddMapping(CampaignManagementErrorCode.MaxNoOfAdGroupsExceededForCampaign, ErrorCodes.MaxAdGroupsReached);
            AddMapping(CampaignManagementErrorCode.AdGroupAlreadyExists, ErrorCodes.DuplicateAdGroup);
            AddMapping(CampaignManagementErrorCode.AdGroupInExpiredStateCannotBeUpdated, ErrorCodes.CannotUpdateAdGroupInExpiredState);
            AddMapping(CampaignManagementErrorCode.AdGroupInSubmittedStateCannotBeUpdated, ErrorCodes.CannotUpdateAdGroupInSubmittedState);
            AddMapping(CampaignManagementErrorCode.AdGroupCannotPauseResumeDueToInvalidStatus, ErrorCodes.CannotOperateOnAdGroupInCurrentState);
            AddMapping(CampaignManagementErrorCode.AdGroupHasInvalidStatus, ErrorCodes.CannotOperateOnAdGroupInCurrentState);
            AddMapping(CampaignManagementErrorCode.AdGroupIsDeleted, ErrorCodes.CannotOperateOnAdGroupInCurrentState);
            AddMapping(CampaignManagementErrorCode.AdGroupIdInvalid, ErrorCodes.InvalidAdGroupId);
            AddMapping(CampaignManagementErrorCode.AdGroupWithIdAlreadyExists, ErrorCodes.DuplicateAdGroup);
            AddMapping(CampaignManagementErrorCode.AdGroupInvalidMedium, ErrorCodes.AdGroupInvalidMedium);
            AddMapping(CampaignManagementErrorCode.AdGroupInvalidDistributionChannel, ErrorCodes.AdGroupInvalidDistributionChannel);
            AddMapping(CampaignManagementErrorCode.AdGroupNotValidInGivenCampaign, ErrorCodes.InvalidAdGroupId);
            AddMapping(CampaignManagementErrorCode.AdGroupCannotBeSubmittedWithoutAtleastOneAdAndOneKeyword, ErrorCodes.NeedAtleastOneAdAndOneKeywordToSubmit);
            AddMapping(CampaignManagementErrorCode.AdGroupStartDateCannotBeEarlierThanSubmitDate, ErrorCodes.AdGroupStartDateCannotBeEarlierThanSubmitDate);
            AddMapping(CampaignManagementErrorCode.AdGroupExpired, ErrorCodes.AdGroupExpired);
            AddMapping(CampaignManagementErrorCode.AdGroupPricingModelCpmRequiresContentMedium, ErrorCodes.AdGroupPricingModelCpmRequiresContentMedium);
            AddMapping(CampaignManagementErrorCode.AdGroupTimestampMismatch, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdGroupMediumNotAllowedForImageAds, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdGroupInvalidDistributionChannelForSpecifiedMedium, ErrorCodes.AdGroupMediumNotAllowedForDistributionChannel);
            AddMapping(CampaignManagementErrorCode.AdGroupInvalidMediumForCustomer, ErrorCodes.AdGroupInvalidMediumForCustomer);
            AddMapping(CampaignManagementErrorCode.AdGroupBiddingSchemeNotSupported, ErrorCodes.AdGroupBiddingSchemeNotSupported);
            AddMapping(CampaignManagementErrorCode.InvalidSearchBids, ErrorCodes.InvalidSearchBids);
            AddMapping(CampaignManagementErrorCode.InvalidContentBid, ErrorCodes.InvalidContentBid);
            AddMapping(CampaignManagementErrorCode.InvalidBid, ErrorCodes.InvalidBid);
            AddMapping(CampaignManagementErrorCode.InvalidPricingModel, ErrorCodes.InvalidPricingModel);
            AddMapping(CampaignManagementErrorCode.AdGroupPricingModelCpmIsNotEnabledForCustomer, ErrorCodes.AdGroupPricingModelCpmIsNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdGroupPricingModelValueEmpty, ErrorCodes.AdGroupPricingModelIsNull);
            AddMapping(CampaignManagementErrorCode.AdGroupBidsUpdateDisabledForOptimizationManagedAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AdGroupMediumUpdateDisabledForOptimizationManagedAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.CanSetBidTargetToBehaviorSegmentOnlyForContentAdGroups, ErrorCodes.TypeCanBeBehavioralBidOnlyForContentAdGroups);
            AddMapping(CampaignManagementErrorCode.CannotChangeMediumForThisBidTarget, ErrorCodes.CannotUpdateAdDistributionForThisType);
            AddMapping(CampaignManagementErrorCode.TooManyAdGroupsInAccount, ErrorCodes.TooManyAdGroupsInAccount);
            AddMapping(CampaignManagementErrorCode.NegativeSiteURLExceedMaxSubDirectories, ErrorCodes.NegativeSiteURLExceedMaxSubDirectories);
            AddMapping(CampaignManagementErrorCode.NegativeSiteURLExceedMaxSubDomains, ErrorCodes.NegativeSiteURLExceedMaxSubDomains);
            AddMapping(CampaignManagementErrorCode.NegativeSiteCannotBeOwnedOrOperatedSite, ErrorCodes.NegativeSiteCannotBeOwnedOrOperatedSite);
            AddMapping(CampaignManagementErrorCode.NegativeSiteEntityLimitExceeded, ErrorCodes.NegativeSiteEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.CannotSetNetworkForContentAdGroup, ErrorCodes.NetworkShouldBeNullForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.AdGroupMediumNotAllowedForRichSearchAds, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.RichSearchAdAssetIdInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdgroupHasRichSearchAdMediumCannotBeChanged, ErrorCodes.MediumChangeNotAllowedForAdgroupWithRichSearchAds);
            AddMapping(CampaignManagementErrorCode.AdgroupHasNonRichSearchAdCannotHaveRichSearchAd, ErrorCodes.RichSearchAdsExclusiveInAdGroup);
            AddMapping(CampaignManagementErrorCode.MissingPublisherCountries, ErrorCodes.MissingPublisherCountries);
            AddMapping(CampaignManagementErrorCode.AdGroupMediumNotAllowedForPublisherCountries, ErrorCodes.AdGroupMediumNotAllowedForPublisherCountries);
            AddMapping(CampaignManagementErrorCode.MissingLanguage, ErrorCodes.MissingLanguage);
            AddMapping(CampaignManagementErrorCode.MultiplePublisherCountriesNotAllowed, ErrorCodes.MultiplePublisherCountriesNotAllowed);
            AddMapping(CampaignManagementErrorCode.PublisherCountriesUpdateNotAllowed, ErrorCodes.PublisherCountriesUpdateNotAllowed);
            AddMapping(CampaignManagementErrorCode.AdGroupNetworkValueNotAllowedForPublisherCountries, ErrorCodes.AdGroupNetworkValueNotAllowedForPublisherCountries);
            AddMapping(CampaignManagementErrorCode.IdShouldBeNullOnAdd, ErrorCodes.IdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.MatchTypeRequiredForAdd, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.BidAmountNotAllowedForAmountSourceUseFromAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KeywordByMatchTypePilotNotEnabledForCustomer, ErrorCodes.KeywordByMatchTypePilotNotEnabledForCustomer);

            AddMapping(CampaignManagementErrorCode.InvalidVersionToken, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.UnsupportedLcid, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.KeywordsNotPassed, ErrorCodes.KeywordsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.KeywordIsBlank, ErrorCodes.NullKeyword);
            AddMapping(CampaignManagementErrorCode.KeywordTooLong, ErrorCodes.InvalidKeywordText);
            AddMapping(CampaignManagementErrorCode.KeywordInvalid, ErrorCodes.InvalidKeywordText);
            AddMapping(CampaignManagementErrorCode.TooManyKeywordsToUpdate, ErrorCodes.KeywordsArrayExceedsLimit);

            // bids errors
            AddMapping(CampaignManagementErrorCode.InvalidBidsForSearchAdGroup, ErrorCodes.InvalidBidAmountForSearchAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidBidsForContentAdGroup, ErrorCodes.InvalidBidAmountForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidBidsForHybridAdGroup, ErrorCodes.InvalidBidAmountForHybridAdGroup);

            AddMapping(CampaignManagementErrorCode.InvalidContentBidForSearchAdGroup, ErrorCodes.InvalidBidAmountForSearchAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidContentBidForContentAdGroup, ErrorCodes.InvalidBidAmountForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidContentBidForHybridAdGroup, ErrorCodes.InvalidBidAmountForHybridAdGroup);

            AddMapping(CampaignManagementErrorCode.InvalidExactBidForSearchAdGroup, ErrorCodes.InvalidBidAmountForSearchAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidExactBidForContentAdGroup, ErrorCodes.InvalidBidAmountForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidExactBidForHybridAdGroup, ErrorCodes.InvalidBidAmountForHybridAdGroup);

            AddMapping(CampaignManagementErrorCode.InvalidPhraseBidForSearchAdGroup, ErrorCodes.InvalidBidAmountForSearchAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidPhraseBidForContentAdGroup, ErrorCodes.InvalidBidAmountForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidPhraseBidForHybridAdGroup, ErrorCodes.InvalidBidAmountForHybridAdGroup);

            AddMapping(CampaignManagementErrorCode.InvalidBroadBidForSearchAdGroup, ErrorCodes.InvalidBidAmountForSearchAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidBroadBidForContentAdGroup, ErrorCodes.InvalidBidAmountForContentAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidBroadBidForHybridAdGroup, ErrorCodes.InvalidBidAmountForHybridAdGroup);

            AddMapping(CampaignManagementErrorCode.AdGroupExactBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupPhraseBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupBroadBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupContentBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);

            AddMapping(CampaignManagementErrorCode.AdGroupExactBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupPhraseBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupBroadBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.AdGroupContentBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);

            AddMapping(CampaignManagementErrorCode.Param1Invalid, ErrorCodes.InvalidParam1);
            AddMapping(CampaignManagementErrorCode.Param1TooLong, ErrorCodes.InvalidParam1);
            AddMapping(CampaignManagementErrorCode.Param2Invalid, ErrorCodes.InvalidParam2);
            AddMapping(CampaignManagementErrorCode.Param2TooLong, ErrorCodes.InvalidParam2);
            AddMapping(CampaignManagementErrorCode.Param3Invalid, ErrorCodes.InvalidParam3);
            AddMapping(CampaignManagementErrorCode.Param3TooLong, ErrorCodes.InvalidParam3);
            AddMapping(CampaignManagementErrorCode.KeywordIdInvalid, ErrorCodes.InvalidKeywordId);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordRequiresPartialMatchBid, ErrorCodes.NegativeKeywordRequiresPartialMatchBid);

            AddMapping(CampaignManagementErrorCode.BidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.KeywordExactBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.KeywordPhraseBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.KeywordBroadBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.KeywordContentBidAmountsLessThanFloorPrice, ErrorCodes.BidAmountsLessThanFloorPrice);

            AddMapping(CampaignManagementErrorCode.BidsAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.KeywordExactBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.KeywordPhraseBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.KeywordBroadBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.KeywordContentBidAmountsGreaterThanCeilingPrice, ErrorCodes.BidAmountsGreaterThanCeilingPrice);

            AddMapping(CampaignManagementErrorCode.KeywordAlreadyExists, ErrorCodes.DuplicateKeyword);
            AddMapping(CampaignManagementErrorCode.MaxKeywordsLimitExceededForCustomer, ErrorCodes.MaxKeywordsReachedForCustomer);
            AddMapping(CampaignManagementErrorCode.MaxKeywordsLimitExceededForAccount, ErrorCodes.MaxKeywordsReachedForAccount);
            AddMapping(CampaignManagementErrorCode.MaxKeywordsLimitExceededForAdGroup, ErrorCodes.MaxKeywordsReachedForAdGroup);
            AddMapping(CampaignManagementErrorCode.KeywordDoesNotBelongToAdGroupId, ErrorCodes.KeywordDoesNotBelongToAdGroupId);
            AddMapping(CampaignManagementErrorCode.KeywordInInvalidStatus, ErrorCodes.InvalidKeywordStatus);
            AddMapping(CampaignManagementErrorCode.KeywordIsDeleted, ErrorCodes.InvalidKeywordStatus);
            AddMapping(CampaignManagementErrorCode.KeywordTextEditTransactionalDependencyFailed, ErrorCodes.KeywordTransactionalDependencyFailed);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForShoppingCampaign, ErrorCodes.EntityNotAllowedForShoppingCampaign);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForDynamicSearchAdsCampaign, ErrorCodes.EntityNotAllowedForDynamicSearchAdsCampaign);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForAudienceCampaign, ErrorCodes.EntityNotAllowedForAudienceCampaign);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForHotelCampaign, ErrorCodes.EntityNotAllowedForHotelCampaign);
            AddMapping(CampaignManagementErrorCode.AccountNotEligbleForKeywordLevelCashback, ErrorCodes.AccountNotEligbleForKeywordLevelCashback);
            AddMapping(CampaignManagementErrorCode.AccountNotEligbleToSetCashbackAmount, ErrorCodes.AccountNotEligibleToSetCashbackAmount);
            AddMapping(CampaignManagementErrorCode.CashbackInfoShouldBeNullForBackwardCompatability, ErrorCodes.CashbackInfoShouldBeNullForBackwardCompatability);
            AddMapping(CampaignManagementErrorCode.KeywordModificationDisabledForOptimizationManagedAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KeywordUpdateDisabledForUserManagedAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.EditorialTooMuchKeywordTextInDestinationUrl, ErrorCodes.TooMuchTextInKeywordDestinationUrl);
            AddMapping(CampaignManagementErrorCode.InvalidMatchTypes, ErrorCodes.InvalidMatchTypes);

            AddMapping(CampaignManagementErrorCode.AdsNotPassed, ErrorCodes.AdsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdTitleInvalid, ErrorCodes.InvalidAdTitle);
            AddMapping(CampaignManagementErrorCode.AdTextInvalid, ErrorCodes.InvalidAdText);
            AddMapping(CampaignManagementErrorCode.AdDisplayUrlInvalid, ErrorCodes.InvalidAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.AdDestinationUrlInvalid, ErrorCodes.InvalidAdDestinationUrl);
            AddMapping(CampaignManagementErrorCode.AdIdInvalid, ErrorCodes.InvalidAdId);
            AddMapping(CampaignManagementErrorCode.DuplicateAdId, ErrorCodes.DuplicateInAdIds);
            AddMapping(CampaignManagementErrorCode.MaxAdsLimitExceeded, ErrorCodes.MaxAdsReached);
            AddMapping(CampaignManagementErrorCode.AdAlreadyPresent, ErrorCodes.DuplicateAd);
            AddMapping(CampaignManagementErrorCode.AdUpdateEmpty, ErrorCodes.UpdateAdEmpty);
            AddMapping(CampaignManagementErrorCode.AdInInvalidStatus, ErrorCodes.CannotOperateOnAdInCurrentState);
            AddMapping(CampaignManagementErrorCode.AdDoesNotBelongToAccount, ErrorCodes.InvalidAdId);
            AddMapping(CampaignManagementErrorCode.AdTypeInvalidForThisOperation, ErrorCodes.AdTypeMismatch);
            AddMapping(CampaignManagementErrorCode.AdIsNull, ErrorCodes.NullAd);
            AddMapping(CampaignManagementErrorCode.MobileAdPhoneNumberInvalid, ErrorCodes.InvalidPhoneNumber);
            AddMapping(CampaignManagementErrorCode.MobileAdBusinessNameInvalid, ErrorCodes.InvalidBusinessName);
            AddMapping(CampaignManagementErrorCode.MobileRequiredDataMissing, ErrorCodes.MobileAdRequiredDataMissing);
            AddMapping(CampaignManagementErrorCode.MobileAdSupportedForSearchOnlyAdGroups, ErrorCodes.MobileAdSupportedForSearchOnlyAdGroups);
            AddMapping(CampaignManagementErrorCode.AdTypeInvalid, ErrorCodes.AdTypeInvalid);
            AddMapping(CampaignManagementErrorCode.AdTypeInvalidForCustomer, ErrorCodes.AdTypeInvalidForCustomer);
            AddMapping(CampaignManagementErrorCode.AdTypeInvalidForCampaign, ErrorCodes.AdTypeInvalidForCampaign);
            AddMapping(CampaignManagementErrorCode.AdTypeDoesNotMatchExistingValue, ErrorCodes.AdTypeDoesNotMatchExistingValue);
            AddMapping(CampaignManagementErrorCode.EntityDoesNotBelongToAdGroup, ErrorCodes.InvalidAdGroupId);
            AddMapping(CampaignManagementErrorCode.KeywordUpdateEmpty, ErrorCodes.UpdateKeywordEmpty);
            AddMapping(CampaignManagementErrorCode.AdDeleted, ErrorCodes.AdDeleted);
            AddMapping(CampaignManagementErrorCode.AdCannotPauseResumeDueToInvalidStatus, ErrorCodes.AdInInvalidStatus);
            AddMapping(CampaignManagementErrorCode.AdModificationDisabledForOptimizationManagedAdGroup, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.DevicePreferenceIncompatibleWithAdType, ErrorCodes.DevicePreferenceIncompatibleWithAdType);
            AddMapping(CampaignManagementErrorCode.InvalidAdDevicePreference, ErrorCodes.InvalidAdDevicePreference);

            AddMapping(CampaignManagementErrorCode.TargetNotPassed, ErrorCodes.NullTarget);
            AddMapping(CampaignManagementErrorCode.NoBidsInTarget, ErrorCodes.NoBidsInTarget);
            AddMapping(CampaignManagementErrorCode.InvalidDayTarget, ErrorCodes.InvalidDayTarget);
            AddMapping(CampaignManagementErrorCode.InvalidHourTarget, ErrorCodes.InvalidHourTarget);
            AddMapping(CampaignManagementErrorCode.InvalidLocationTarget, ErrorCodes.InvalidLocationTarget);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessLocationTarget, ErrorCodes.InvalidLocationTarget);
            AddMapping(CampaignManagementErrorCode.InvalidGeoCodeStatusForBusinessLocation, ErrorCodes.InvalidLatitudeLongitudeForBusinessLocation);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessLocationId, ErrorCodes.InvalidBusinessLocationId);
            AddMapping(CampaignManagementErrorCode.InvalidCustomLocationTarget, ErrorCodes.InvalidLocationTarget);
            AddMapping(CampaignManagementErrorCode.InvalidGenderTarget, ErrorCodes.InvalidGenderTarget);
            AddMapping(CampaignManagementErrorCode.InvalidAgeTarget, ErrorCodes.InvalidAgeTarget);

            AddMapping(CampaignManagementErrorCode.InvalidDealIdTarget, ErrorCodes.InvalidDealIdTarget);
            AddMapping(CampaignManagementErrorCode.InvalidGenreIdTarget, ErrorCodes.InvalidGenreIdTarget);
            AddMapping(CampaignManagementErrorCode.UnsupportedBiddingSchemeForDeal, ErrorCodes.UnsupportedBiddingSchemeForDeal);
            AddMapping(CampaignManagementErrorCode.AdgroupBidAmountLessThanDealAskPrice, ErrorCodes.AdgroupBidAmountLessThanDealAskPrice);
            AddMapping(CampaignManagementErrorCode.UnsupportedAgeTargetForDeal, ErrorCodes.UnsupportedAgeTargetForDeal);
            AddMapping(CampaignManagementErrorCode.UnsupportedGenderTargetForDeal, ErrorCodes.UnsupportedGenderTargetForDeal);
            AddMapping(CampaignManagementErrorCode.UnsupportedLocationTargetForDeal, ErrorCodes.UnsupportedLocationTargetForDeal);
            AddMapping(CampaignManagementErrorCode.UnsupportedDeviceTargetForDeal, ErrorCodes.UnsupportedDeviceTargetForDeal);
            AddMapping(CampaignManagementErrorCode.UnsupportedAudienceTargetForDeal, ErrorCodes.UnsupportedAudienceTargetForDeal);
            AddMapping(CampaignManagementErrorCode.InvalidAdQualityForDeal, ErrorCodes.InvalidAdQualityForDeal);
            AddMapping(CampaignManagementErrorCode.DealCampaignIsImmutable, ErrorCodes.DealCampaignIsImmutable);
            AddMapping(CampaignManagementErrorCode.EntityOnlySupportForDealCampaign, ErrorCodes.EntityOnlySupportForDealCampaign);
            AddMapping(CampaignManagementErrorCode.ShouldAssociateDealBeforeAddAdGroup, ErrorCodes.ShouldAssociateDealBeforeAddAdGroup);

            AddMapping(CampaignManagementErrorCode.AgeCriterionCannotBeEmptyForBrandAwarenessCampaign, ErrorCodes.AgeCriterionCannotBeEmptyForBrandAwarenessCampaign);
            AddMapping(CampaignManagementErrorCode.GenderCriterionCannotBeEmptyForBrandAwarenessCampaign, ErrorCodes.GenderCriterionCannotBeEmptyForBrandAwarenessCampaign);
            AddMapping(CampaignManagementErrorCode.DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign, ErrorCodes.DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign);
            AddMapping(CampaignManagementErrorCode.JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay, ErrorCodes.JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay);
            AddMapping(CampaignManagementErrorCode.ImpressionAudienceCannotBeUsedByDisplay, ErrorCodes.ImpressionAudienceCannotBeUsedByDisplay);
            AddMapping(CampaignManagementErrorCode.ImpressionAudienceCannotBeUsedByOLV, ErrorCodes.ImpressionAudienceCannotBeUsedByOLV);
            AddMapping(CampaignManagementErrorCode.ImpressionAudienceCannotBeUsedByCTV, ErrorCodes.ImpressionAudienceCannotBeUsedByCTV);
            AddMapping(CampaignManagementErrorCode.NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign, ErrorCodes.NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign);

            AddMapping(CampaignManagementErrorCode.DuplicateDayTarget, ErrorCodes.DuplicateDayTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateHourTarget, ErrorCodes.DuplicateHourTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateMetroAreaLocationTarget, ErrorCodes.DuplicateMetroAreaLocationTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateCountryLocationTarget, ErrorCodes.DuplicateCountryLocationTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateGenderTarget, ErrorCodes.DuplicateGenderTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateAgeTarget, ErrorCodes.DuplicateAgeTarget);
            AddMapping(CampaignManagementErrorCode.CountryAndMetroAreaTargetsMutuallyExclusive, ErrorCodes.CountryAndMetroAreaTargetsExclusive);
            AddMapping(CampaignManagementErrorCode.MetroAreaTargetsFromMultipleCountries, ErrorCodes.MetroTargetsFromMultipleCountries);
            AddMapping(CampaignManagementErrorCode.AssociationStatusNotPassed, ErrorCodes.StatusIsNull);
            AddMapping(CampaignManagementErrorCode.InvalidModifiedAfterDate, ErrorCodes.InvalidModifiedAfterDate);
            AddMapping(CampaignManagementErrorCode.IncrementalBudgetAmountRequiredForDayTarget, ErrorCodes.IncrementalBudgetAmountRequiredForDayTarget);

            AddMapping(CampaignManagementErrorCode.InvalidTargetRadius, ErrorCodes.InvalidTargetRadius);
            AddMapping(CampaignManagementErrorCode.InvalidTargetRadiusUnit, ErrorCodes.InvalidTargetRadius);
            AddMapping(CampaignManagementErrorCode.InvalidLatitude, ErrorCodes.InvalidLatitude);
            AddMapping(CampaignManagementErrorCode.LatitudeOutOfRange, ErrorCodes.InvalidLatitude);
            AddMapping(CampaignManagementErrorCode.InvalidLongitude, ErrorCodes.InvalidLongitude);
            AddMapping(CampaignManagementErrorCode.LongitudeOutOfRange, ErrorCodes.InvalidLongitude);
            AddMapping(CampaignManagementErrorCode.DuplicateSubGeographyTarget, ErrorCodes.DuplicateSubGeographyTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateCityTarget, ErrorCodes.DuplicateCityTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateBusinessLocationTarget, ErrorCodes.DuplicateBusinessLocationTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateCustomLocationTarget, ErrorCodes.DuplicateCustomLocationTarget);
            AddMapping(CampaignManagementErrorCode.InvalidLocationId, ErrorCodes.InvalidLocationId);
            AddMapping(CampaignManagementErrorCode.GeoLocationOptionsRequired, ErrorCodes.GeoLocationOptionsRequired);
            AddMapping(CampaignManagementErrorCode.UnsupportedCombinationOfLocationIdAndOptions, ErrorCodes.UnsupportedCombinationOfLocationIdAndOptions);
            AddMapping(CampaignManagementErrorCode.InvalidGeographicalLocationSearchString, ErrorCodes.InvalidGeographicalLocationSearchString);
            AddMapping(CampaignManagementErrorCode.GeoTargetsAndBusinessTargetsMutuallyExclusive, ErrorCodes.GeoTargetsAndBusinessTargetsMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.BusinessAndRadiusTargetsAllowedOnlyForSearchMedium, ErrorCodes.BusinessAndRadiusTargetsAllowedOnlyForSearchMedium);
            AddMapping(CampaignManagementErrorCode.BusinessLocationNotSet, ErrorCodes.BusinessLocationNotSet);
            AddMapping(CampaignManagementErrorCode.BusinessNameRequired, ErrorCodes.BusinessNameRequired);
            AddMapping(CampaignManagementErrorCode.LattitudeLongitudeRequired, ErrorCodes.LatitudeLongitudeRequired);
            AddMapping(CampaignManagementErrorCode.BusinessNameTooLong, ErrorCodes.BusinessNameTooLong);
            AddMapping(CampaignManagementErrorCode.DomainNameAlreadyTaken, ErrorCodes.DomainNameAlreadyTaken);
            AddMapping(CampaignManagementErrorCode.BusinessDescriptionTooLong, ErrorCodes.BusinessDescriptionTooLong);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessTypeId, ErrorCodes.InvalidBusinessTypeId);
            AddMapping(CampaignManagementErrorCode.InvalidPaymentTypeId, ErrorCodes.InvalidPaymentTypeId);
            AddMapping(CampaignManagementErrorCode.InvalidBusinessHoursEntry, ErrorCodes.InvalidBusinessHoursEntry);
            AddMapping(CampaignManagementErrorCode.AddressRequired, ErrorCodes.AddressRequired);
            AddMapping(CampaignManagementErrorCode.AddressTooLong, ErrorCodes.AddressTooLong);
            AddMapping(CampaignManagementErrorCode.CityNameRequired, ErrorCodes.CityNameRequired);
            AddMapping(CampaignManagementErrorCode.CityNameTooLong, ErrorCodes.CityNameTooLong);
            AddMapping(CampaignManagementErrorCode.CountryCodeRequired, ErrorCodes.CountryCodeRequired);
            AddMapping(CampaignManagementErrorCode.CountryCodeTooLong, ErrorCodes.CountryCodeTooLong);
            AddMapping(CampaignManagementErrorCode.StateOrProvinceRequired, ErrorCodes.StateOrProvinceRequired);
            AddMapping(CampaignManagementErrorCode.StateOrProvinceTooLong, ErrorCodes.StateOrProvinceTooLong);
            AddMapping(CampaignManagementErrorCode.LocationIsNotSpecified, ErrorCodes.LocationIsNotSpecified);
            AddMapping(CampaignManagementErrorCode.Open24HoursAndBusinessHoursMutuallyExclusive, ErrorCodes.Open24HoursAndBusinessHoursMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.BusinessNameAndAddressAlreadyExists, ErrorCodes.BusinessNameAndAddressAlreadyExists);
            AddMapping(CampaignManagementErrorCode.BusinessLocationListTooLong, ErrorCodes.BusinessLocationListTooLong);
            AddMapping(CampaignManagementErrorCode.InvalidCustomerId, ErrorCodes.InvalidCustomerId);
            AddMapping(CampaignManagementErrorCode.BusinessDomainAlreadySet, ErrorCodes.BusinessDomainAlreadySet);
            AddMapping(CampaignManagementErrorCode.OnlyOneBusinessLocationPerCustomerIsAllowed, ErrorCodes.OnlyOneBusinessLocationPerCustomerIsAllowed);
            AddMapping(CampaignManagementErrorCode.ZipOrPostalCodeTooLong, ErrorCodes.ZipOrPostalCodeTooLong);
            AddMapping(CampaignManagementErrorCode.IsLibraryTargetNotNull, ErrorCodes.IsLibraryTargetNotNull);
            AddMapping(CampaignManagementErrorCode.AddressInvalid, ErrorCodes.AddressInvalid);
            AddMapping(CampaignManagementErrorCode.BusinessAddressShouldBeValidForUpdate, ErrorCodes.BusinessAddressShouldBeValidForUpdate);

            AddMapping(CampaignManagementErrorCode.AdGroupGeoTargetLimitReached, ErrorCodes.GeoTargetsInAdGroupExceedsLimit);
            AddMapping(CampaignManagementErrorCode.OptimizationTargetNotSpecified, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.TargetsNotPassed, ErrorCodes.TargetsNotPassed);
            AddMapping(CampaignManagementErrorCode.TargetAlreadyExists, ErrorCodes.TargetAlreadyExists);
            AddMapping(CampaignManagementErrorCode.MaxTargetsLimitsExceeded, ErrorCodes.TargetsLimitReached);
            AddMapping(CampaignManagementErrorCode.InvalidTargetId, ErrorCodes.InvalidTargetId);
            AddMapping(CampaignManagementErrorCode.InvalidGeoLocationLevel, ErrorCodes.InvalidGeoLocationLevel);
            AddMapping(CampaignManagementErrorCode.TargetDeleted, ErrorCodes.InvalidTargetId);
            AddMapping(CampaignManagementErrorCode.AdGroupMediumInvalidWithBusinessTargets, ErrorCodes.AdGroupMediumInvalidWithBusinessTargets);
            AddMapping(CampaignManagementErrorCode.TargetsBatchLimitExceeded, ErrorCodes.TargetsArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.CustomerIdRequired, ErrorCodes.CustomerIdHasToBeSpecified);
            AddMapping(CampaignManagementErrorCode.TargetTypeAndVersionMismatch, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.TargetNotAssociatedWithEntity, ErrorCodes.TargetNotAssociatedWithEntity);
            AddMapping(CampaignManagementErrorCode.TargetAlreadyAssociatedWithEntity, ErrorCodes.TargetAlreadyAssociatedWithEntity);
            AddMapping(CampaignManagementErrorCode.TargetHasActiveAssociations, ErrorCodes.TargetHasActiveAssociations);
            AddMapping(CampaignManagementErrorCode.AssociatingNonLibraryTargetNotAllowed, ErrorCodes.AssociatingNonLibraryTargetNotAllowed);
            AddMapping(CampaignManagementErrorCode.TargetDoesNotExist, ErrorCodes.InvalidTarget);
            AddMapping(CampaignManagementErrorCode.InvalidTarget, ErrorCodes.InvalidTarget);
            AddMapping(CampaignManagementErrorCode.BTTargettingNotEnabledForPilot, ErrorCodes.BTTargettingNotEnabledForPilot);
            AddMapping(CampaignManagementErrorCode.InvalidBehaviorTarget, ErrorCodes.InvalidBehaviorTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateBehaviorTarget, ErrorCodes.DuplicateBehaviorTarget);
            AddMapping(CampaignManagementErrorCode.InvalidSegmentTarget, ErrorCodes.InvalidSegmentTarget);
            AddMapping(CampaignManagementErrorCode.DuplicateSegmentTarget, ErrorCodes.DuplicateSegmentTarget);
            AddMapping(CampaignManagementErrorCode.NegativeBiddingNotAllowedForThisTargetType, ErrorCodes.NegativeBiddingNotAllowedForThisTargetType);
            AddMapping(CampaignManagementErrorCode.InvalidCashbackTextinSegmentTarget, ErrorCodes.InvalidCashbackTextinSegmentTarget);
            AddMapping(CampaignManagementErrorCode.InvalidParam1inSegmentTarget, ErrorCodes.InvalidParam1inSegmentTarget);
            AddMapping(CampaignManagementErrorCode.InvalidParam2inSegmentTarget, ErrorCodes.InvalidParam2inSegmentTarget);
            AddMapping(CampaignManagementErrorCode.InvalidParam3inSegmentTarget, ErrorCodes.InvalidParam3inSegmentTarget);
            AddMapping(CampaignManagementErrorCode.InvalidSegmentParam1inSegmentTarget, ErrorCodes.InvalidSegmentParam1inSegmentTarget);
            AddMapping(CampaignManagementErrorCode.InvalidSegmentParam2inSegmentTarget, ErrorCodes.InvalidSegmentParam2inSegmentTarget);
            AddMapping(CampaignManagementErrorCode.CannotSpecifySegmentTargetsWithAnyOtherTarget, ErrorCodes.CannotSpecifySegmentTargetsWithAnyOtherTarget);
            AddMapping(CampaignManagementErrorCode.InvalidCashbackAmountInSegmentTarget, ErrorCodes.InvalidCashbackAmountInSegmentTarget);
            AddMapping(CampaignManagementErrorCode.TargetDoesNotBelongToCustomer, ErrorCodes.InvalidTargetId);
            AddMapping(CampaignManagementErrorCode.TargetAssociationsNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TargetAssociationNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.TargetAssociationDucplicate, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidDeviceTarget, ErrorCodes.InvalidDeviceTarget);
            AddMapping(CampaignManagementErrorCode.InvalidLocationTargetBidAdjustment, ErrorCodes.InvalidLocationTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidAgeTargetBidAdjustment, ErrorCodes.InvalidAgeTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidGenderTargetBidAdjustment, ErrorCodes.InvalidGenderTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidDayTargetBidAdjustment, ErrorCodes.InvalidDayTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidHourTargetBidAdjustment, ErrorCodes.InvalidHourTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidDeviceTargetBidAdjustment, ErrorCodes.InvalidDeviceTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidSegmentedTargetBidAdjustment, ErrorCodes.InvalidSegmentedTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.BidAdjustmentNullPassedForAddTarget, ErrorCodes.BidAdjustmentNullPassedForAddTarget);
            AddMapping(CampaignManagementErrorCode.RelatedCriterionTransactionError, ErrorCodes.RelatedCriterionTransactionError);

            AddMapping(CampaignManagementErrorCode.OSTargetingNotEnabledForPilot, ErrorCodes.OSTargetingNotEnabledForPilot);
            AddMapping(CampaignManagementErrorCode.DuplicateDeviceTarget, ErrorCodes.DuplicateDeviceTarget);
            AddMapping(CampaignManagementErrorCode.CustomerUnderMigration, ErrorCodes.CustomerDataBeingMigrated);
            AddMapping(CampaignManagementErrorCode.PhysicalIntentNotEnabledForPilot, ErrorCodes.PhysicalIntentNotEnabledForPilot);

            AddMapping(CampaignManagementErrorCode.BusinessLocationsNotPassed, ErrorCodes.BusinessLocationsNotPassed);
            AddMapping(CampaignManagementErrorCode.BusinessLocationHoursNotSet, ErrorCodes.InvalidBusinessLocationHours);
            AddMapping(CampaignManagementErrorCode.BusinessLocationHoursInvalid, ErrorCodes.InvalidBusinessLocationHours);
            AddMapping(CampaignManagementErrorCode.BusinessLocationBeginAndEndHoursMismatch, ErrorCodes.BusinessLocationBeginAndEndHoursMismatch);
            AddMapping(CampaignManagementErrorCode.BusinessLocationHoursDuplicate, ErrorCodes.DuplicateBusinessHours);
            AddMapping(CampaignManagementErrorCode.UnableToObtainLatLongInvalidAddress, ErrorCodes.InvalidAddress);
            AddMapping(CampaignManagementErrorCode.UnableToObtainLatLongVirtualEarthError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.UnableToDeleteAdAssociated, ErrorCodes.BusinessHasActiveAssociations);
            AddMapping(CampaignManagementErrorCode.MaxBusinessLocationTargetsLimitReachedForCustomer, ErrorCodes.BusinessLocationTargetsLimitReachedForCustomer);
            AddMapping(CampaignManagementErrorCode.MaxBusinessLocationTargetsLimitReachedForAdGroup, ErrorCodes.BusinessLocationTargetsLimitReachedForAdGroup);
            AddMapping(CampaignManagementErrorCode.MaxBusinessLocationTargetsLimitReachedForCampaign, ErrorCodes.BusinessLocationTargetsLimitReachedForCampaign);
            AddMapping(CampaignManagementErrorCode.InvalidSyncDateRange, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.OptimizationTargetInvalid, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.DuplicateBusinessLocation, ErrorCodes.DuplicateBusinessLocation);
            AddMapping(CampaignManagementErrorCode.MultipleDuplicateBusinessLocation, ErrorCodes.MultipleDuplicateBusinessLocation);
            AddMapping(CampaignManagementErrorCode.LocationTargetVersionIsNotSuported, ErrorCodes.LocationTargetVersionIsNotSupported);
            AddMapping(CampaignManagementErrorCode.BusinessLocationTargetingIsNotSupported, ErrorCodes.BusinessLocationTargetingIsNotSupported);

            AddMapping(CampaignManagementErrorCode.TargetsLocationBidBatchLimitExceeded, ErrorCodes.TargetsLocationBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsAgeBidBatchLimitExceeded, ErrorCodes.TargetsAgeBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsSegmentBidBatchLimitExceeded, ErrorCodes.TargetsSegmentBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsBehaviorBidBatchLimitExceeded, ErrorCodes.TargetsBehaviorBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsDayBidBatchLimitExceeded, ErrorCodes.TargetsDayBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsHourBidBatchLimitExceeded, ErrorCodes.TargetsHourBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetsGenderBidBatchLimitExceeded, ErrorCodes.TargetsGenderBidsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TargetingShouldBeExclusiveForThisTargetType, ErrorCodes.TargetingShouldBeExclusiveForThisTargetType);
            AddMapping(CampaignManagementErrorCode.BiddingOtherThanZeroNotAllowedForThisTargetType, ErrorCodes.BiddingOtherThanZeroNotAllowedForThisTargetType);
            AddMapping(CampaignManagementErrorCode.InvalidTargetName, ErrorCodes.InvalidTargetName);
            AddMapping(CampaignManagementErrorCode.DuplicatePaymentType, ErrorCodes.DuplicatePaymentTypes);

            AddMapping(CampaignManagementErrorCode.EmailInvalid, ErrorCodes.InvalidEmail);
            AddMapping(CampaignManagementErrorCode.EmailTooLong, ErrorCodes.EmailTooLong);
            AddMapping(CampaignManagementErrorCode.PhoneNumberInvalid, ErrorCodes.BusinessPhoneNumberInvalid);
            AddMapping(CampaignManagementErrorCode.PhoneNumberTooLong, ErrorCodes.PhoneNumberTooLong);


            AddMapping(CampaignManagementErrorCode.SegmentAlreadyExists, ErrorCodes.DuplicateSegmentName);
            AddMapping(CampaignManagementErrorCode.SegmentIdInvalid, ErrorCodes.InvalidSegmentId);
            AddMapping(CampaignManagementErrorCode.SegmentIdNotFound, ErrorCodes.InvalidSegmentId);
            AddMapping(CampaignManagementErrorCode.SegmentInputExceeded, ErrorCodes.SegmentsArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.SegmentNameHasInvalidChars, ErrorCodes.InvalidSegmentName);
            AddMapping(CampaignManagementErrorCode.SegmentNameMissing, ErrorCodes.InvalidSegmentName);
            AddMapping(CampaignManagementErrorCode.SegmentsNotPassed, ErrorCodes.SegmentsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SegmentOperationNotEnabledForPilot, ErrorCodes.SegmentOperationNotAllowedForPilot);
            AddMapping(CampaignManagementErrorCode.SegmentNameTooLong, ErrorCodes.InvalidSegmentName);
            AddMapping(CampaignManagementErrorCode.SegmentDoesNotBelongToCustomer, ErrorCodes.InvalidSegmentId);
            AddMapping(CampaignManagementErrorCode.MaxSegmentsForCustomerHasBeenReached, ErrorCodes.MaxSegmentsForCustomerHasBeenReached);
            AddMapping(CampaignManagementErrorCode.SegmentAlreadyDeleted, ErrorCodes.InvalidSegmentId);
            AddMapping(CampaignManagementErrorCode.SegmentNotAllowedForDistributionChannel, ErrorCodes.SegmentNotAllowedForDistributionChannel);

            AddMapping(CampaignManagementErrorCode.AssetIdInvalid, ErrorCodes.MediaIdInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDoesNotBelongToCustomer, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AssetIsAlreadyDeleted, ErrorCodes.MediaIdInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDoesNotMatchCAMSchema, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CAMSchemaIsNotSupported, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AssetTypeIsNotSupported, ErrorCodes.ImageTypeEnumInvalid);
            AddMapping(CampaignManagementErrorCode.ImageDataInvalid, ErrorCodes.ImageDataInvalid);
            AddMapping(CampaignManagementErrorCode.ImageMimeTypeInvalid, ErrorCodes.ImageMimeTypeInvalid);
            AddMapping(CampaignManagementErrorCode.ImageTooLarge, ErrorCodes.ImageTooLarge);
            AddMapping(CampaignManagementErrorCode.ImageOverweight, ErrorCodes.ImageOverweight);
            AddMapping(CampaignManagementErrorCode.AnimatedImageNotAllowed, ErrorCodes.AnimatedImageNotAllowed);
            AddMapping(CampaignManagementErrorCode.AssetIdsNotPassed, ErrorCodes.MediaIdsArrayIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AssetEntitiesNotPassed, ErrorCodes.MediaEntityArrayIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AssetEntityLimitExceeded, ErrorCodes.MediaEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AssetIsNull, ErrorCodes.MediaEntityIsNull);
            AddMapping(CampaignManagementErrorCode.CAMSchemaNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AssetDataIsNull, ErrorCodes.MediaEntityDataIsNull);
            AddMapping(CampaignManagementErrorCode.AssetDataInvalid, ErrorCodes.MediaEntityDataInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDataEncodingInvalid, ErrorCodes.MediaEntityDataEncodingInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDataInvalidWidth, ErrorCodes.MediaEntityDataWidthInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDataInvalidHeight, ErrorCodes.MediaEntityDataHeightInvalid);
            AddMapping(CampaignManagementErrorCode.AssetDataInvalidAspect, ErrorCodes.MediaEntityDataAspectRatioInvalid);
            AddMapping(CampaignManagementErrorCode.MediaEnabledEntitiesInvalid, ErrorCodes.MediaEnabledEntitiesInvalid);
            AddMapping(CampaignManagementErrorCode.MediaTypeNotSupportedByOperation, ErrorCodes.MediaTypeNotSupportedByOperation);
            AddMapping(CampaignManagementErrorCode.MediaFormatNotSupported, ErrorCodes.MediaFormatNotSupported);
            AddMapping(CampaignManagementErrorCode.AssetDoesNotBelongToAccount, ErrorCodes.MediaNotFoundInAccount);
            AddMapping(CampaignManagementErrorCode.CamAssetCannotBeDeleted, ErrorCodes.MediaTypeNotSupportedByOperation);
            AddMapping(CampaignManagementErrorCode.AssetIsUsed, ErrorCodes.MediaIsAssociated);
            AddMapping(CampaignManagementErrorCode.DuplicateAssetId, ErrorCodes.DuplicateInMediaIds);

            AddMapping(CampaignManagementErrorCode.NegativeSiteURLInvalid, ErrorCodes.InvalidNegativeSiteURL);
            AddMapping(CampaignManagementErrorCode.NegativeSiteURLTooLong, ErrorCodes.InvalidNegativeSiteURL);
            AddMapping(CampaignManagementErrorCode.NegativeSiteURLExceedMaxCount, ErrorCodes.NegativeSiteURLExceededMaxCount);

            AddMapping(CampaignManagementErrorCode.TargetGroupPermissionMismatch, ErrorCodes.TargetGroupAssignedEntitiesPermissionMismatch);
            AddMapping(CampaignManagementErrorCode.InvalidEntityState, ErrorCodes.InvalidEntityState);

            AddMapping(CampaignManagementErrorCode.CashbackAllowedOnlyForSearchMedium, (int)ErrorCodes.CashbackAllowedOnlyForSearchMedium);
            AddMapping(CampaignManagementErrorCode.CashbackNotAllowedForAdgroupsDistributionChannel, (int)ErrorCodes.CashbackNotAllowedForAdgroupsDistributionChannel);
            AddMapping(CampaignManagementErrorCode.CampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup, (int)ErrorCodes.CampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup);
            AddMapping(CampaignManagementErrorCode.AccountNotEligibleToModifyCashBack, (int)ErrorCodes.AccountNotEligibleToModifyCashBack);
            AddMapping(CampaignManagementErrorCode.InvalidCashbackAmount, (int)ErrorCodes.InvalidCashbackAmount);
            AddMapping(CampaignManagementErrorCode.CashbackTextTooLong, (int)ErrorCodes.CashbackTextTooLong);
            AddMapping(CampaignManagementErrorCode.CashBackStatusRequied, (int)ErrorCodes.CashBackStatusRequired);

            AddMapping(CampaignManagementErrorCode.InvalidBidForSitePlacement, (int)ErrorCodes.InvalidBidAmounts);
            AddMapping(CampaignManagementErrorCode.SitePlacementBidExceedsCeilingPrice, (int)ErrorCodes.InvalidBidAmounts);
            AddMapping(CampaignManagementErrorCode.InvalidSitePlacementPath, (int)ErrorCodes.InvalidUrl);
            AddMapping(CampaignManagementErrorCode.InvalidSiteId, (int)ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.InvalidMediumAndBiddingStrategyCombination, (int)ErrorCodes.InvalidMediumAndBiddingStrategyCombination);
            AddMapping((CampaignManagementErrorCode)MTErrorInvalidBiddingModel, (int)ErrorCodes.InvalidBiddingModel);
            AddMapping(CampaignManagementErrorCode.AdGroupAudienceCriterionBidTypeInvalid, (int)ErrorCodes.InvalidRemarketingTargetingSetting);

            AddMapping(CampaignManagementErrorCode.KeywordIdDuplicateInRequest, ErrorCodes.KeywordIdDuplicateInRequest);

            AddMapping(CampaignManagementErrorCode.CannotAddKeywordToAdGroupTypeMismatch, ErrorCodes.CannotAddKeywordToSpecifiedAdGroup);

            AddMapping(CampaignManagementErrorCode.CustomerAccountIdInvalid, ErrorCodes.InvalidAccountId);

            AddMapping(CampaignManagementErrorCode.InvalidLCIDForDistributionChannel, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdGroupBidIsBelowFloorPrice, ErrorCodes.InvalidBidAmount);
            AddMapping(CampaignManagementErrorCode.AgeAndGenderExclusiveTargetingIsNotEnabledForCustomer, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.OrderItemsNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CannotMixOrderItemsInInputArray, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.OrderItemIdInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.MultipleOrderItemTypesInCollection, ErrorCodes.InvalidIds);

            AddMapping(CampaignManagementErrorCode.InvalidOrderItem, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidOrderItemBids, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DuplicateNetwork, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidBidForNetwork, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NetworkBidExceedsCeilingPrice, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NetworkUpdateEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DistributionChannelsNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CouldNotLookupIdForDistributionChannel, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NodeIdsNotPassed, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.InvalidUrl, ErrorCodes.InvalidUrl);
            AddMapping(CampaignManagementErrorCode.DuplicateUrl, ErrorCodes.DuplicateUrl);
            AddMapping(CampaignManagementErrorCode.UncrawlableUrl, ErrorCodes.UncrawlableUrl);

            AddMapping(CampaignManagementErrorCode.NodeIdsListExceedsLimit, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.NodeIdsHasDuplicates, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.CannotAddNetworkToAdGroupTypeMismatch, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.CurrencyIdInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.PricesNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.InvalidPrice, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.EditorialKeywordParamDisallowedInAdTitle, ErrorCodes.EditorialErrorInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialKeywordParamDisallowedInAdText, ErrorCodes.EditorialErrorInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialKeywordParamDisallowedInAdDisplayUrl, ErrorCodes.EditorialErrorInAdDisplayUrl);

            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNotificationRequiredAdTitle, ErrorCodes.EditorialErrorInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNotificationRequiredAdText, ErrorCodes.EditorialErrorInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNotificationRequiredDisplayUrl, ErrorCodes.EditorialErrorInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNotificationRequiredDestinationUrl, ErrorCodes.EditorialErrorInAdDestinationUrl);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNotificationRequiredBusinessName, ErrorCodes.EditorialErrorInAdBusinessName);

            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNoNotificationAdTitle, ErrorCodes.EditorialErrorInAdTitle);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNoNotificationAdText, ErrorCodes.EditorialErrorInAdText);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNoNotificationDisplayUrl, ErrorCodes.EditorialErrorInAdDisplayUrl);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNoNotificationDestinationUrl, ErrorCodes.EditorialErrorInAdDestinationUrl);
            AddMapping(CampaignManagementErrorCode.EditorialAdultSensitiveTermsNoNotificationBusinessName, ErrorCodes.EditorialErrorInAdBusinessName);
            AddMapping(CampaignManagementErrorCode.EditorialAdDestinationUrlBlankAcrossAllAssociations, ErrorCodes.EditorialAdDestinationUrlBlank);

            AddMapping(CampaignManagementErrorCode.GoalNotPassed, ErrorCodes.GoalNotPassed);
            AddMapping(CampaignManagementErrorCode.GoalsListEmpty, ErrorCodes.GoalsArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.GoalNameMissing, ErrorCodes.GoalNameMissing);
            AddMapping(CampaignManagementErrorCode.GoalNameTooLong, ErrorCodes.GoalNameTooLong);
            AddMapping(CampaignManagementErrorCode.GoalNameAlreadyExists, ErrorCodes.GoalNameAlreadyExists);
            AddMapping(CampaignManagementErrorCode.GoalNameHasInvalidChars, ErrorCodes.GoalNameHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.DaysApplicableForConversionIsRequired, ErrorCodes.DaysApplicableForConversionIsRequired);
            AddMapping(CampaignManagementErrorCode.GoalIdIsRequired, ErrorCodes.GoalIdIsRequired);
            AddMapping(CampaignManagementErrorCode.GoalIdIsInvalid, ErrorCodes.InvalidGoalId);
            AddMapping(CampaignManagementErrorCode.GoalIdMustBeNull, ErrorCodes.GoalIdMustBeNull);
            AddMapping(CampaignManagementErrorCode.GoalIdAlreadyExists, ErrorCodes.GoalIdAlreadyExists);
            AddMapping(CampaignManagementErrorCode.GoalCostModelCannotbeCombinedWithNone, ErrorCodes.GoalCostModelCannotbeCombinedWithNone);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountRequiredForConstantModel, ErrorCodes.GoalRevenueAmountRequiredForConstantModel);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountMustBeNullForVariableModel, ErrorCodes.GoalRevenueAmountMustBeNullForVariableModel);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountMustBeNullForNullModel, ErrorCodes.GoalRevenueAmountMustBeNullForNullModel);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountValidOnlyForConstantModel, ErrorCodes.GoalRevenueAmountValidOnlyForConstantModel);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountLessThanMinimum, ErrorCodes.GoalRevenueAmountLessThanMinimum);
            AddMapping(CampaignManagementErrorCode.GoalRevenueAmountMoreThanMax, ErrorCodes.GoalRevenueAmountMoreThanMax);
            AddMapping(CampaignManagementErrorCode.GoalRevenueModelIsRequired, ErrorCodes.GoalRevenueModelIsRequired);
            AddMapping(CampaignManagementErrorCode.GoalCostModelIsRequired, ErrorCodes.GoalCostModelIsRequired);
            AddMapping(CampaignManagementErrorCode.MaxGoalsLimitExceededForAccount, ErrorCodes.MaxGoalsLimitExceededForAccount);

            // Stages
            AddMapping(CampaignManagementErrorCode.StageNotPassed, ErrorCodes.StepNotPassed);
            AddMapping(CampaignManagementErrorCode.StageNameMissing, ErrorCodes.StepNameMissing);
            AddMapping(CampaignManagementErrorCode.StageNameTooLong, ErrorCodes.StepNameTooLong);
            AddMapping(CampaignManagementErrorCode.StageNameAlreadyExists, ErrorCodes.StepNameAlreadyExists);
            AddMapping(CampaignManagementErrorCode.StageNameHasInvalidChars, ErrorCodes.StepNameHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.StageIdIsRequired, ErrorCodes.StepIdIsRequired);
            AddMapping(CampaignManagementErrorCode.StageIdMustBeNull, ErrorCodes.StepIdMustBeNull);
            AddMapping(CampaignManagementErrorCode.StageIdAlreadyExists, ErrorCodes.StepIdAlreadyExists);
            AddMapping(CampaignManagementErrorCode.StageTypeIsRequired, ErrorCodes.StepTypeIsRequired);
            AddMapping(CampaignManagementErrorCode.StageTypeMustBeNull, ErrorCodes.StepTypeMustBeNull);
            AddMapping(CampaignManagementErrorCode.ActionIdMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.PositionNumberIsRequired, ErrorCodes.PositionNumberIsRequired);
            AddMapping(CampaignManagementErrorCode.PositionNumberMustBeNull, ErrorCodes.PositionNumberMustBeNull);
            AddMapping(CampaignManagementErrorCode.PositionNumberAlreadyExists, ErrorCodes.PositionNumberAlreadyExists);
            AddMapping(CampaignManagementErrorCode.MaxStagesLimitExceededForGoal, ErrorCodes.MaxStepsLimitExceededForGoal);
            AddMapping(CampaignManagementErrorCode.AtleaseOneStageRequiredForGoal, ErrorCodes.AtleaseOneStepRequiredForGoal);
            AddMapping(CampaignManagementErrorCode.AtleaseOneConversionStageRequiredForGoal, ErrorCodes.AtleaseOneConversionStepRequiredForGoal);
            AddMapping(CampaignManagementErrorCode.OnlyOneLeadStageAllowedForGoal, ErrorCodes.OnlyOneLeadStepAllowedForGoal);
            AddMapping(CampaignManagementErrorCode.OnlyOneConversionStageAllowedForGoal, ErrorCodes.OnlyOneConversionStepAllowedForGoal);

            // Dimensions
            AddMapping(CampaignManagementErrorCode.DimensionNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionsListEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionNameMissing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionNameTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionNameAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionTypeAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionNameHasInvalidChars, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionNameMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionKeyMissing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionKeyMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionKeyTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionKeyAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionKeyHasInvalidChars, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionIdIsRequired, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionIdIsInvalid, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionIdMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionIdAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionTypeCannotBeUpdated, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.MaxDimensionsLimitExceededForAccount, ErrorCodes.FutureFeatureCode);

            // Dimension Value
            AddMapping(CampaignManagementErrorCode.DimensionValueNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueNameMissing, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueNameTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueNameAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueNameHasInvalidChars, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueKeyMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueKeyTooLong, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueKeyAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueKeyHasInvalidChars, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueKeyEmptyNotAllowed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueIdIsRequired, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueIdMustBeNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValueIdAlreadyExists, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.DimensionValuesListEmpty, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.MaxDimensionValuesLimitExceededForDimension, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AtleaseOneDimensionValueRequiredForDimension, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.FilterScoreNotPassed, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.MoreRecentVersionExists, ErrorCodes.FutureFeatureCode);

            AddMapping(CampaignManagementErrorCode.AccountsAndCampaignsMissing, ErrorCodes.AccountIdsCampaignsEmpty);
            AddMapping(CampaignManagementErrorCode.AccountsAndCampaignsLengthExceeded, ErrorCodes.AccountsAndCampaignsLengthExceeded);
            AddMapping(CampaignManagementErrorCode.CampaignsContainsNullScope, ErrorCodes.CampaignsContainsNullScope);
            AddMapping(CampaignManagementErrorCode.InvalidDownloadRequestId, ErrorCodes.InvalidDownloadRequestId);

            AddMapping(CampaignManagementErrorCode.KeywordCollectionExceededLimit, ErrorCodes.KeywordsArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.UrlNotPassed, ErrorCodes.InvalidUrl);
            AddMapping(CampaignManagementErrorCode.MaxKeywordsOutputExceededLimit, ErrorCodes.MaxKeywordsRequestedMoreThanSupportNumber);
            AddMapping(CampaignManagementErrorCode.InvalidMaxKeywordsOutput, ErrorCodes.InvalidMaxKeywords);
            AddMapping(CampaignManagementErrorCode.InvalidMinConfidenceScore, ErrorCodes.InvalidMinConfidenceScore);
            AddMapping(CampaignManagementErrorCode.LanguageNotSupported, ErrorCodes.LanguageNotSupported);
            AddMapping(CampaignManagementErrorCode.KSPTimeoutError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KSPCommunicationError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KSPProviderNotFound, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KSPProviderError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.KspStartDateNull, ErrorCodes.InvalidStartDate);
            AddMapping(CampaignManagementErrorCode.KspEndDateNull, ErrorCodes.InvalidEndDate);
            AddMapping(CampaignManagementErrorCode.KspStartDateBiggerThanEndDate, ErrorCodes.StartDateGreaterThanEndDate);
            AddMapping(CampaignManagementErrorCode.InvalidMaxSuggestionsPerKeyword, ErrorCodes.InvalidMaxSuggestionsPerKeyword);
            AddMapping(CampaignManagementErrorCode.MaxSuggestionsPerKeywordExceededLimit, ErrorCodes.InvalidMaxSuggestionsPerKeyword);
            AddMapping(CampaignManagementErrorCode.LanguageAndCountryNotSupported, ErrorCodes.LanguageAndCountryNotSupported);
            AddMapping(CampaignManagementErrorCode.MatchTypesNotPassed, ErrorCodes.InvalidMatchTypes);
            AddMapping(CampaignManagementErrorCode.PublisherCountriesCollectionSizeExceedsLimit, ErrorCodes.PublisherCountryArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.InvalidStartDateFormat, ErrorCodes.InvalidStartDate);
            AddMapping(CampaignManagementErrorCode.StartDateBiggerThanCurrentDate, ErrorCodes.InvalidStartDate);
            AddMapping(CampaignManagementErrorCode.InvalidEndDateFormat, ErrorCodes.InvalidEndDate);
            AddMapping(CampaignManagementErrorCode.EndDateBiggerThanCurrentDate, ErrorCodes.InvalidEndDate);

            // KWMT errors
            AddMapping(CampaignManagementErrorCode.FullSyncRequired, ErrorCodes.FullSyncRequired);
            AddMapping(CampaignManagementErrorCode.MultipleBidTypesNotAllowed, ErrorCodes.MultipleKeywordBidTypesNotAllowed);
            AddMapping(CampaignManagementErrorCode.KeywordAndMatchTypeCombinationAlreadyExists, ErrorCodes.KeywordAndMatchTypeCombinationAlreadyExists);
            AddMapping(CampaignManagementErrorCode.KeywordBidRequired, ErrorCodes.KeywordBidRequired);
            AddMapping(CampaignManagementErrorCode.ZeroBidAmountNotAllowed, ErrorCodes.KeywordZeroBidAmountNotAllowed);
            AddMapping(CampaignManagementErrorCode.MatchTypeChangeNotAllowedInUpdate, ErrorCodes.KeywordMatchTypeChangeNotAllowedInUpdate);

            // Y! migration and Analytics errors
            AddMapping(CampaignManagementErrorCode.ResumeFailedDueToMigrationErrors, ErrorCodes.ResumeFailedDueToMigrationErrors);
            AddMapping(CampaignManagementErrorCode.AnalyticsSettingCampaignLevelDeprecated, ErrorCodes.AnalyticsSettingCampaignLevelDeprecated);

            // Ad extension errors
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionStatus, ErrorCodes.InvalidAdExtensionStatus);
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionType, ErrorCodes.InvalidAdExtensionType);
            AddMapping(CampaignManagementErrorCode.AdExtensionSiteLinkArrayIsNullOrEmpty, ErrorCodes.AdExtensionSiteLinkArrayIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionSiteLinkIsNull, ErrorCodes.AdExtensionSiteLinkIsNull);
            AddMapping(CampaignManagementErrorCode.SiteLinkDestinationUrlNullOrEmpty, ErrorCodes.SiteLinkDestinationUrlNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SiteLinkDisplayTextNullOrEmpty, ErrorCodes.SiteLinkDisplayTextNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SiteLinkDestinationUrlTooLong, ErrorCodes.SiteLinkDestinationUrlTooLong);
            AddMapping(CampaignManagementErrorCode.SiteLinkDisplayTextTooLong, ErrorCodes.SiteLinkDisplayTextTooLong);
            AddMapping(CampaignManagementErrorCode.SiteLinkDisplayTextInvalid, ErrorCodes.SiteLinkDisplayTextInvalid);
            AddMapping(CampaignManagementErrorCode.SiteLinkDescription1TooLong, ErrorCodes.SiteLinkDescription1TooLong);
            AddMapping(CampaignManagementErrorCode.SiteLinkDescription2TooLong, ErrorCodes.SiteLinkDescription2TooLong);
            AddMapping(CampaignManagementErrorCode.SiteLinkDevicePreferenceInvalid, ErrorCodes.SiteLinkDevicePreferenceInvalid);
            AddMapping(CampaignManagementErrorCode.SiteLinkDescription1Invalid, ErrorCodes.SiteLinkDescription1Invalid);
            AddMapping(CampaignManagementErrorCode.SiteLinkDescription2Invalid, ErrorCodes.SiteLinkDescription2Invalid);
            AddMapping(CampaignManagementErrorCode.SiteLinkDescriptionAllOrNoneRequired, ErrorCodes.SiteLinkDescriptionAllOrNoneRequired);

            AddMapping(CampaignManagementErrorCode.TooManyAdExtensionsPerAccount, ErrorCodes.TooManyAdExtensionsPerAccount);
            AddMapping(CampaignManagementErrorCode.TooManySiteLinks, ErrorCodes.TooManySiteLinks);
            AddMapping(CampaignManagementErrorCode.AdExtensionsNullOrEmpty, ErrorCodes.AdExtensionsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionIsNull, ErrorCodes.AdExtensionIsNull);
            AddMapping(CampaignManagementErrorCode.AdExtensionsEntityLimitExceeded, ErrorCodes.AdExtensionsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LocationAdExtensionsEntityLimitExceeded, ErrorCodes.LocationAdExtensionsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SiteLinkDestinationUrlInvalid, ErrorCodes.SiteLinkDestinationUrlInvalid);
            AddMapping(CampaignManagementErrorCode.SiteLinkAdExtensionPilotNotEnabledForCustomer, ErrorCodes.SiteLinkAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.DuplicateSiteLink, ErrorCodes.DuplicateSiteLinksInAdExtension);
            AddMapping(CampaignManagementErrorCode.EnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer, ErrorCodes.EnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer);

            AddMapping(CampaignManagementErrorCode.AdExtensionIdInvalid, ErrorCodes.InvalidAdExtensionId);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdToCampaignIdAssociationsNotPassed, ErrorCodes.AdExtensionIdToCampaignIdAssociationsNotPassed);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdToCampaignIdAssociationNotPassed, ErrorCodes.AdExtensionIdToCampaignIdAssociationNotPassed);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdsNotPassed, ErrorCodes.AdExtensionIdsArrayIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.CannotAssignMoreThanOneAdExtensionTypeToAnEntity, ErrorCodes.CannotAssignMoreThanOneAdExtensionTypeToAnEntity);
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionAssociation, ErrorCodes.InvalidAdExtensionIdToCampaignIdAssociation);
            AddMapping(CampaignManagementErrorCode.DuplicateAdExtensionIdToCampaignIdAssociation, ErrorCodes.DuplicateInAdExtensionIdToCampaignIdAssociations);
            AddMapping(CampaignManagementErrorCode.DuplicateAdExtensionId, ErrorCodes.DuplicateInAdExtensionIds);

            AddMapping(CampaignManagementErrorCode.DuplicateAdExtensionIdToAccountIdAssociation, ErrorCodes.DuplicateInAdExtensionIdToAccountIdAssociations);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdToAccountIdAssociationNotPassed, ErrorCodes.AdExtensionIdToAccountIdAssociationNotPassed);
            AddMapping(CampaignManagementErrorCode.AdExtensionAccountAssociationEntityIdNotEqualToAccountId, ErrorCodes.AdExtensionAccountAssociationEntityIdNotEqualToAccountId);
            AddMapping(CampaignManagementErrorCode.AdExtensionAccountAssociationShouldHaveOneEntityId, ErrorCodes.AdExtensionAccountAssociationShouldHaveOneEntityId);

            AddMapping(CampaignManagementErrorCode.AdExtensionAddressIsNull, ErrorCodes.AdExtensionAddressIsNull);
            AddMapping(CampaignManagementErrorCode.AdExtensionStreetAddressNullOrEmpty, ErrorCodes.AdExtensionStreetAddressNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionStreetAddressTooLong, ErrorCodes.AdExtensionStreetAddressTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionStreetAddressInvalid, ErrorCodes.AdExtensionStreetAddressInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionStreetAddress2TooLong, ErrorCodes.AdExtensionStreetAddress2TooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionStreetAddress2Invalid, ErrorCodes.AdExtensionStreetAddress2Invalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionCityNameNullOrEmpty, ErrorCodes.AdExtensionCityNameNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionCityNameTooLong, ErrorCodes.AdExtensionCityNameTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionCityNameInvalid, ErrorCodes.AdExtensionCityNameInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionProvinceNameTooLong, ErrorCodes.AdExtensionProvinceNameTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionProvinceNameInvalid, ErrorCodes.AdExtensionProvinceNameInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionPostalCodeTooLong, ErrorCodes.AdExtensionPostalCodeTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionPostalCodeInvalid, ErrorCodes.AdExtensionPostalCodeInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionCountryCodeNull, ErrorCodes.AdExtensionCountryCodeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionCountryCodeWrongLength, ErrorCodes.AdExtensionCountryCodeWrongLength);
            AddMapping(CampaignManagementErrorCode.AdExtensionCountryCodeInvalid, ErrorCodes.AdExtensionCountryCodeInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionGeoPointIsNotNull, ErrorCodes.AdExtensionGeoPointIsNotNull);
            AddMapping(CampaignManagementErrorCode.AdExtensionGeoCodeStatusIsNotNull, ErrorCodes.AdExtensionGeoCodeStatusIsNotNull);
            AddMapping(CampaignManagementErrorCode.AdExtensionCompanyNameNullOrEmpty, ErrorCodes.AdExtensionCompanyNameNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionCompanyNameTooLong, ErrorCodes.AdExtensionCompanyNameTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionCompanyNameInvalid, ErrorCodes.AdExtensionCompanyNameInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionPhoneNumberTooLong, ErrorCodes.AdExtensionPhoneNumberTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionPhoneNumberInvalid, ErrorCodes.AdExtensionPhoneNumberInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionIconMediaIdInvalid, ErrorCodes.AdExtensionIconMediaIdInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionIconSizeTooLarge, ErrorCodes.AdExtensionIconTooLarge);
            AddMapping(CampaignManagementErrorCode.AdExtensionImageMediaIdInvalid, ErrorCodes.AdExtensionImageMediaIdInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionImageSizeTooLarge, ErrorCodes.AdExtensionImageTooLarge);
            AddMapping(CampaignManagementErrorCode.LocationAdExtensionPilotNotEnabledForCustomer, ErrorCodes.LocationAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CallAdExtensionPilotNotEnabledForCustomer, ErrorCodes.CallAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdExtensionProvinceCodeTooLong, ErrorCodes.AdExtensionProvinceCodeTooLong);
            AddMapping(CampaignManagementErrorCode.AdExtensionProvinceCodeInvalid, ErrorCodes.AdExtensionProvinceCodeInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionProvinceCodeRequiredIfProvinceNameEmpty, ErrorCodes.AdExtensionProvinceCodeRequiredIfProvinceNameEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionIconMediaUrlIsNotNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdExtensionImageMediaUrlIsNotNull, ErrorCodes.FutureFeatureCode);
            AddMapping(CampaignManagementErrorCode.AdExtensionPhoneNumberNullOrEmpty, ErrorCodes.AdExtensionPhoneNumberNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionTypeMismatch, ErrorCodes.AdExtensionTypeMismatch);
            AddMapping(CampaignManagementErrorCode.MaxCampaignIdsLimitExceeded, ErrorCodes.TooManyCampaignIds);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionPilotNotEnabledForCustomer, ErrorCodes.ProductListingAdPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionTooManyConditions, ErrorCodes.ProductAdExtensionTooManyConditions);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionTooManyProductConditionCollections, ErrorCodes.ProductAdExtensionTooManyProductConditionCollections);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionInvalidProductCollectionId, ErrorCodes.ProductAdExtensionInvalidStoreId);
            AddMapping(CampaignManagementErrorCode.CallTrackingNotEnabledForCustomer, ErrorCodes.CallTrackingNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled, ErrorCodes.CallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled);
            AddMapping(CampaignManagementErrorCode.CallAdExtensionCallTrackingNotSupportedForCountry, ErrorCodes.CallAdExtensionCallTrackingNotSupportedForCountry);
            AddMapping(CampaignManagementErrorCode.AdExtensionGeoPointInvalid, ErrorCodes.AdExtensionGeoPointInvalid);
            AddMapping(CampaignManagementErrorCode.AdExtensionPostalCodeNullOrEmpty, ErrorCodes.AdExtensionPostalCodeNullOrEmpty);

            AddMapping(CampaignManagementErrorCode.ProductAdExtensionProductConditionsArrayIsNullOrEmpty, ErrorCodes.ProductAdExtensionProductConditionsArrayIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionProductConditionIsNull, ErrorCodes.ProductAdExtensionProductConditionIsNull);
            AddMapping(CampaignManagementErrorCode.ProductConditionOperandInvalid, ErrorCodes.ProductAdExtensionOperandIsInvalid);
            AddMapping(CampaignManagementErrorCode.ProductConditionAttributeInvalid, ErrorCodes.ProductAdExtensionAttributeIsInvalid);
            AddMapping(CampaignManagementErrorCode.ProductConditionAttributeIsInvalid, ErrorCodes.ProductAdExtensionAttributeIsInvalid);
            AddMapping(CampaignManagementErrorCode.ProductConditionAttributeTooLong, ErrorCodes.ProductConditionAttributeTooLong);
            AddMapping(CampaignManagementErrorCode.ProductConditionDuplicateOperand, ErrorCodes.ProductConditionDuplicateOperand);
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionTypeFilter, ErrorCodes.InvalidAdExtensionTypeFilter);
            AddMapping(CampaignManagementErrorCode.ProductCollectionNameCannotBeSet, ErrorCodes.ProductAdExtensionStoreNameCannotBeSet);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionNameInvalid, ErrorCodes.ProductAdExtensionNameInvalid);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionNameTooLong, ErrorCodes.ProductAdExtensionNameTooLong);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionEditorialStatusCannotBeSet, ErrorCodes.InvalidAdGroupCriterionEditorialStatus);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionDuplicateProductFilter, ErrorCodes.ProductAdExtensionDuplicateProductFilter);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionDuplicateProductCondition, ErrorCodes.ProductAdExtensionDuplicateProductCondition);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionCannotHaveBothAllAndSpecificProducts, ErrorCodes.ProductAdExtensionCannotHaveBothAllAndSpecificProducts);
            AddMapping(CampaignManagementErrorCode.MultipleProductAdExtensionCannotBeAssignedToSingleCampaign, ErrorCodes.MultipleProductAdExtensionCannotBeAssignedToSingleEntity);
            //
            AddMapping(CampaignManagementErrorCode.InvalidMigrationStatusPassed, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.JustificationTextTooLongForInlineAppeal, ErrorCodes.JustificationTextTooLongForInlineAppeal);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleToSetAdRotation, ErrorCodes.AdRotationPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForLocationExclusion, ErrorCodes.LocationExclusionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.LocationExclusionsBatchLimitExceeded, ErrorCodes.LocationExclusionBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LocationExclusionsTargetAssociationsNotPassed, ErrorCodes.ExclusionToEntityAssociationCollectionIsNull);
            AddMapping(CampaignManagementErrorCode.InvalidLocationExclusion, ErrorCodes.LocationExclusionIsInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicateExcludedGeoEntity, ErrorCodes.DuplicateExcludedGeoTargets);
            AddMapping(CampaignManagementErrorCode.LocationExclusionNotPassed, ErrorCodes.LocationExclusionIsNull);
            AddMapping(CampaignManagementErrorCode.ExcludedGeoEntitiesBatchLimitExceeded, ErrorCodes.ExcludedGeoTargetsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ConflictWithLocationExclusion, ErrorCodes.ConflictWithLocationExclusion);
            AddMapping(CampaignManagementErrorCode.ConflictWithExclusionTarget, ErrorCodes.ConflictWithExclusionTarget);
            AddMapping(CampaignManagementErrorCode.ExcludedGeoEntityNotPassed, ErrorCodes.ExcludedGeoTargetIsNull);
            AddMapping(CampaignManagementErrorCode.AccountTooBigToDownload, ErrorCodes.AccountTooBigToDownload);
            AddMapping(CampaignManagementErrorCode.CampaignsTooBigToDownload, ErrorCodes.CampaignsTooBigToDownload);
            AddMapping(CampaignManagementErrorCode.InvalidExclusionBitMaskPassed, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.ProductAdExtensionProductConditionCollectionIsNull, ErrorCodes.ProductAdExtensionProductConditionCollectionIsNull);
            AddMapping(CampaignManagementErrorCode.ExtensionProductSelectionIsNull, ErrorCodes.ExtensionProductSelectionIsNull);
            AddMapping(CampaignManagementErrorCode.DataScopeInvalid, ErrorCodes.DataScopeInvalid);

            // AdExtensionSchedule
            AddMapping(CampaignManagementErrorCode.AdExtensionSchedulingPilotNotEnabledForCustomer, ErrorCodes.AdExtensionSchedulingPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdExtensionScheduleInvalidStartTime, ErrorCodes.AdExtensionScheduleInvalidStartTime);
            AddMapping(CampaignManagementErrorCode.AdExtensionScheduleInvalidEndTime, ErrorCodes.AdExtensionScheduleInvalidEndTime);
            AddMapping(CampaignManagementErrorCode.InvalidScheduleDayTimeRange, ErrorCodes.InvalidScheduleDayTimeRange);
            AddMapping(CampaignManagementErrorCode.ScheduleDayTimeRangesDayBatchLimitExceeded, ErrorCodes.ScheduleDayTimeRangesDayBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ScheduleDayTimeRangesOverlapping, ErrorCodes.ScheduleDayTimeRangesOverlapping);
            AddMapping(CampaignManagementErrorCode.InvalidScheduleDayTimeRangeInterval, ErrorCodes.InvalidScheduleDayTimeRangeInterval);
            AddMapping(CampaignManagementErrorCode.AdExtensionScheduleInvalidUseSearcherTimeZone, ErrorCodes.AdExtensionScheduleInvalidUseSearcherTimeZone);
            AddMapping(CampaignManagementErrorCode.ScheduleDaysNotInDateRange, ErrorCodes.ScheduleDaysNotInDateRange);

            //Image Ad Extension
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionPilotNotEnabledForCustomer, ErrorCodes.ImageAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionV2PilotNotEnabledForCustomer, ErrorCodes.ImageAdExtensionV2PilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CampaignImageAdExtensionPilotNotEnabledForCustomer, ErrorCodes.CampaignImageAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionAlternativeTextNullOrEmpty, ErrorCodes.ImageAdExtensionAlternativeTextNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionAlternativeTextInvalid, ErrorCodes.ImageAdExtensionAlternativeTextInvalid);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionAlternativeTextTooLong, ErrorCodes.ImageAdExtensionAlternativeTextTooLong);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDestinationUrlInvalid, ErrorCodes.ImageAdExtensionDestinationUrlInvalid);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDestinationUrlTooLong, ErrorCodes.ImageAdExtensionDestinationUrlTooLong);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionImageMediaIdInvalid, ErrorCodes.ImageAdExtensionImageMediaIdInvalid);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDescriptionInvalid, ErrorCodes.ImageAdExtensionDescriptionInvalid);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDescriptionTooLong, ErrorCodes.ImageAdExtensionDescriptionTooLong);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionTooManyImages, ErrorCodes.ImageAdExtensionTooManyImages);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionImageMediaIdsNullOrEmpty, ErrorCodes.ImageAdExtensionImageMediaIdsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDisplayTextNullOrEmpty, ErrorCodes.ImageAdExtensionDisplayTextNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDisplayTextInvalid, ErrorCodes.ImageAdExtensionDisplayTextInvalid);
            AddMapping(CampaignManagementErrorCode.ImageAdExtensionDisplayTextTooLong, ErrorCodes.ImageAdExtensionDisplayTextTooLong);
            AddMapping(CampaignManagementErrorCode.ImagesLimitExceededPerAccount, ErrorCodes.ImagesLimitExceededPerAccount);

            // App Ad Extension
            AddMapping(CampaignManagementErrorCode.AppAdExtensionPilotNotEnabledForCustomer, ErrorCodes.AppAdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionAppPlatformNullOrEmpty, ErrorCodes.AppAdExtensionAppPlatformNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionInvalidAppPlatform, ErrorCodes.AppAdExtensionInvalidAppPlatform);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionAppStoreIdNullOrEmpty, ErrorCodes.AppAdExtensionAppStoreIdNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionAppStoreIdTooLong, ErrorCodes.AppAdExtensionAppStoreIdTooLong);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionAppStoreIdInvalid, ErrorCodes.AppAdExtensionAppStoreIdInvalid);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDisplayTextNullOrEmpty, ErrorCodes.AppAdExtensionDisplayTextNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDisplayTextTooLong, ErrorCodes.AppAdExtensionDisplayTextTooLong);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDisplayTextInvalid, ErrorCodes.AppAdExtensionDisplayTextInvalid);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDestinationUrlNullOrEmpty, ErrorCodes.AppAdExtensionDestinationUrlNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDestinationUrlTooLong, ErrorCodes.AppAdExtensionDestinationUrlTooLong);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDestinationUrlInvalid, ErrorCodes.AppAdExtensionDestinationUrlInvalid);
            AddMapping(CampaignManagementErrorCode.AppAdExtensionDevicePreferenceInvalid, ErrorCodes.AppAdExtensionDevicePreferenceInvalid);
            AddMapping(CampaignManagementErrorCode.AppInstallTrackingPilotNotEnabledForCustomer, ErrorCodes.AppInstallTrackingPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AppNotFound, ErrorCodes.AppNotFound);

            AddMapping(CampaignManagementErrorCode.InvalidAssociationType, ErrorCodes.InvalidAssociationType);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdToAdGroupIdAssociationsNotPassed, ErrorCodes.AdExtensionIdToAdGroupIdAssociationArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdExtensionIdToAdGroupIdAssociationNotPassed, ErrorCodes.InvalidAdExtensionIdToAdGroupIdAssociation);
            AddMapping(CampaignManagementErrorCode.DuplicateAdExtensionIdToAdGroupIdAssociation, ErrorCodes.DuplicateInAdExtensionIdToAdGroupIdAssociations);
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionTypeForAdGroupAssociation, ErrorCodes.AdExtensionTypeNotAllowedForAdGroupAssociation);
            AddMapping(CampaignManagementErrorCode.InvalidAdExtensionTypeForAccountAssociation, ErrorCodes.AdExtensionTypeNotAllowedForAccountAssociation);
            AddMapping(CampaignManagementErrorCode.AdExtensionAssociationsLimitExceededPerEntityType, ErrorCodes.AdExtensionAssociationsLimitExceededPerEntityType);
            AddMapping(CampaignManagementErrorCode.AssociationsLimitExceededPerAdExtensionType, ErrorCodes.AssociationsLimitExceededPerAdExtensionType);

            // Ad Group Criterion
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIdInvalid, ErrorCodes.AdGroupCriterionIdInvalid);
            AddMapping(CampaignManagementErrorCode.ProductCriterionPilotNotEnabledForCustomer, ErrorCodes.ProductListingAdPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIdArrayNullOrEmpty, ErrorCodes.AdGroupCriterionIdArrayNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.DuplicateAdGroupCriterionId, ErrorCodes.DuplicateAdGroupCriterionId);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIdListExceedsLimit, ErrorCodes.AdGroupCriterionIdListExceedsLimit);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionsNullOrEmpty, ErrorCodes.AdGroupCriterionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionsEntityLimitExceeded, ErrorCodes.AdGroupCriterionsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIsNull, ErrorCodes.AdGroupCriterionIsNull);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidConditionType, ErrorCodes.AdGroupCriterionInvalidConditionType);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidBidType, ErrorCodes.AdGroupCriterionInvalidBidType);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidBidValue, ErrorCodes.AdGroupCriterionInvalidBidValue);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIdShouldBeNullOnAdd, ErrorCodes.AdGroupCriterionIdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.InvalidAdGroupCriterionStatus, ErrorCodes.InvalidAdGroupCriterionStatus);
            AddMapping(CampaignManagementErrorCode.InvalidAdGroupCriterionType, ErrorCodes.InvalidAdGroupCriterionType);
            AddMapping(CampaignManagementErrorCode.InvalidAdGroupCriterionCriterionType, ErrorCodes.InvalidAdGroupCriterionType);
            AddMapping(CampaignManagementErrorCode.ProductAdGroupCriterionTooManyConditions, ErrorCodes.ProductAdGroupCriterionTooManyConditions);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionTooManyConditions, ErrorCodes.ProductAdGroupCriterionTooManyConditions);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionProductConditionIsNull, ErrorCodes.AdGroupCriterionProductConditionIsNull);
            AddMapping(CampaignManagementErrorCode.ProductConditionAttributeNullOrEmpty, ErrorCodes.ProductConditionAttributeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIsDeleted, ErrorCodes.AdGroupCriterionIsDeleted);
            AddMapping(CampaignManagementErrorCode.DuplicateProductTarget, ErrorCodes.DuplicateProductTarget);
            AddMapping(CampaignManagementErrorCode.ProductAdPromotionalTextTooLong, ErrorCodes.ProductAdPromotionalTextTooLong);
            AddMapping(CampaignManagementErrorCode.LastSyncTimeTooOld, ErrorCodes.LastSyncTimeTooOld);
            AddMapping(CampaignManagementErrorCode.ProductAdPromotionalTextInvalid, ErrorCodes.ProductAdPromotionalTextInvalid);
            AddMapping(CampaignManagementErrorCode.ProductTargetDestinationUrlTooLong, ErrorCodes.ProductTargetDestinationUrlTooLong);
            AddMapping(CampaignManagementErrorCode.ProductValueTooLong, ErrorCodes.ProductValueTooLong);
            AddMapping(CampaignManagementErrorCode.ProductTargetParam1TooLong, ErrorCodes.InvalidProductTargetParam1);
            AddMapping(CampaignManagementErrorCode.ProductTargetParam2TooLong, ErrorCodes.InvalidProductTargetParam2);
            AddMapping(CampaignManagementErrorCode.ProductTargetParam3TooLong, ErrorCodes.InvalidProductTargetParam3);
            AddMapping(CampaignManagementErrorCode.CriterionTypeNotAllowed, ErrorCodes.CriterionTypeNotAllowed);
            AddMapping(CampaignManagementErrorCode.CriterionTypeMismatch, ErrorCodes.CriterionTypeMismatch);

            // for apply product partition.

            AddMapping(CampaignManagementErrorCode.AdGroupCriterionActionsNullOrEmpty, ErrorCodes.AdGroupCriterionActionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionActionIsNull, ErrorCodes.AdGroupCriterionActionNull);
            AddMapping(CampaignManagementErrorCode.CampaignIsNotOfTypeShopping, ErrorCodes.CampaignIsNotOfTypeShopping);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionCriterionIsNull, ErrorCodes.AdGroupCriterionIsNull);
            AddMapping(CampaignManagementErrorCode.InvalidProductPartitionType, ErrorCodes.ProductPartitionTypeInvalid);
            AddMapping(CampaignManagementErrorCode.CannotChangeAdgroupCriterionType, ErrorCodes.AdGroupCriterionTypeImmutable);
            AddMapping(CampaignManagementErrorCode.CannotModifyNegativeAdGroupCriterion, ErrorCodes.NegativeAdGroupCriterionImmutable);
            AddMapping(CampaignManagementErrorCode.ProductPartitionLimitExceededForAdGroup, ErrorCodes.AdGroupProductPartitionLimitExceeded);
            AddMapping(CampaignManagementErrorCode.InvalidProductConditionHierarchy, ErrorCodes.InvalidProductPartitionHierarchy);
            AddMapping(CampaignManagementErrorCode.ProductConditionEverythingElseMissing, ErrorCodes.RemainingProductsNodeMissingInProductPartition);
            AddMapping(CampaignManagementErrorCode.DuplicateProductConditions, ErrorCodes.DuplicateProductConditions);
            AddMapping(CampaignManagementErrorCode.ProductConditionOperandUnderSubDivisionMustBeSame, ErrorCodes.ProductPartitionSiblingsMustHaveSameOperand);
            AddMapping(CampaignManagementErrorCode.HeightOfProductPartitionTreeExceeededLimit, ErrorCodes.ProductPartitionTreeHeightLimitExceeeded);
            AddMapping(CampaignManagementErrorCode.DuplicateRootNodeForProductPartitionTree, ErrorCodes.ProductPartitionTreeRootAlreadyExists);
            AddMapping(CampaignManagementErrorCode.CannotAddChildrenToUnitNodeType, ErrorCodes.CannotAddChildrenToProductPartitionUnit);
            AddMapping(CampaignManagementErrorCode.ParentProductPartitionNodeDoesNotExist, ErrorCodes.ParentAdGroupCriterionIdInvalid);
            AddMapping(CampaignManagementErrorCode.CannotUpdateCriterionForProductPartition, ErrorCodes.ProductPartitionCriterionImmutable);
            AddMapping(CampaignManagementErrorCode.EverythingElseNodeCannotBeDeleted, ErrorCodes.RemainingProductsNodeRequired);
            AddMapping(CampaignManagementErrorCode.OperationsForTooManyAdgroups, ErrorCodes.CriterionActionsAdGroupLimitExceeded);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForShoppingCampaigns, ErrorCodes.ShoppingCampaignPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.InvalidProductConditionOperand, ErrorCodes.ProductConditionOperandInvalid);
            AddMapping(CampaignManagementErrorCode.InvalidProductConditionAttribute, ErrorCodes.ProductConditionAttributeInvalid);

            AddMapping(CampaignManagementErrorCode.DestinationUrlTooLong, ErrorCodes.DestinationUrlTooLong);
            AddMapping(CampaignManagementErrorCode.DestinationUrlProtocolInvalid, ErrorCodes.DestinationUrlProtocolInvalid);
            AddMapping(CampaignManagementErrorCode.DestinationUrlInvalid, ErrorCodes.DestinationUrlInvalid);
            AddMapping(CampaignManagementErrorCode.NonMatchingBraces, ErrorCodes.DestinationUrlInvalid);
            AddMapping(CampaignManagementErrorCode.CrossScriptingUnsafeUrl, ErrorCodes.DestinationUrlInvalid);

            AddMapping(CampaignManagementErrorCode.Param1NotSupportedForCriterionType, ErrorCodes.Param1NotSupportedForCriterionType);
            AddMapping(CampaignManagementErrorCode.Param2NotSupportedForCriterionType, ErrorCodes.Param2NotSupportedForCriterionType);
            AddMapping(CampaignManagementErrorCode.Param3NotSupportedForCriterionType, ErrorCodes.Param3NotSupportedForCriterionType);
            AddMapping(CampaignManagementErrorCode.ConcurrentStoreModification, ErrorCodes.ConcurrentStoreModification);
            AddMapping(CampaignManagementErrorCode.AnotherOperationForSameAdGroupHasError, ErrorCodes.RelatedProductPartitionActionError);

            //Bulk Upload errors
            AddMapping(CampaignManagementErrorCode.FailedBecauseOfOtherItem, ErrorCodes.BatchOperationFailedForItems);
            AddMapping(CampaignManagementErrorCode.RequestIdInvalid, ErrorCodes.InvalidRequestId);
            AddMapping(CampaignManagementErrorCode.InvalidItemToDelete, ErrorCodes.EntityNotFound);
            AddMapping(CampaignManagementErrorCode.UnknownRowType, ErrorCodes.UnknownTypeForRow);
            AddMapping(CampaignManagementErrorCode.ThrottlingLimitReached, ErrorCodes.NoMoreCallsPermittedForTheTimePeriod);
            AddMapping(CampaignManagementErrorCode.CannotRemoveLastGoodAdExtensionItem, ErrorCodes.CannotRemoveLastGoodAdExtensionItem);
            AddMapping(CampaignManagementErrorCode.FormatVersionNotSupported, ErrorCodes.FormatVersionNotSupported);
            AddMapping(CampaignManagementErrorCode.FormatVersionRequired, ErrorCodes.FormatVersionRequired);
            AddMapping(CampaignManagementErrorCode.UploadFileRowCountExceeded, ErrorCodes.FileRowCountExceeded);
            AddMapping(CampaignManagementErrorCode.UploadFileFormatNotSupported, ErrorCodes.InvalidFileExtension);

            //SharedEntity
            AddMapping(CampaignManagementErrorCode.SharedEntityNameNullOrEmpty, ErrorCodes.SharedEntityNameNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedEntityLimitExceeded, ErrorCodes.SharedEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SharedEntityInvalidType, ErrorCodes.SharedEntityInvalidType);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationsNullOrEmpty, ErrorCodes.SharedEntityAssociationsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.EntityTypeNotSupported, ErrorCodes.EntityTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.MultipleSharedEntityTypesNotAllowed, ErrorCodes.MultipleSharedEntityTypesNotAllowed);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationsBatchLimitExceeded, ErrorCodes.SharedEntityAssociationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationsListItemNullOrEmpty, ErrorCodes.SharedEntityAssociationsListItemNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationDuplicate, ErrorCodes.SharedEntityAssociationDuplicate);
            AddMapping(CampaignManagementErrorCode.SharedEntityIdsNullOrEmpty, ErrorCodes.SharedEntityIdsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedEntityNullOrEmpty, ErrorCodes.SharedEntityNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.DuplicateSharedEntityId, ErrorCodes.DuplicateSharedEntityId);
            AddMapping(CampaignManagementErrorCode.SharedEntityTypeNullOrEmpty, ErrorCodes.SharedEntityTypeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedListDeleted, ErrorCodes.SharedListDeleted);
            AddMapping(CampaignManagementErrorCode.SharedListsNullOrEmpty, ErrorCodes.SharedListsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedListNullOrEmpty, ErrorCodes.SharedListNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedListIdInvalid, ErrorCodes.SharedListIdInvalid);
            AddMapping(CampaignManagementErrorCode.SharedListDuplicate, ErrorCodes.SharedListDuplicate);
            AddMapping(CampaignManagementErrorCode.SharedListItemsNullOrEmpty, ErrorCodes.SharedListItemsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedListItemIdInvalid, ErrorCodes.SharedListItemIdInvalid);
            AddMapping(CampaignManagementErrorCode.SharedListItemIdsLimitExceeded, ErrorCodes.SharedListItemIdsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SharedEntitiesNullOrEmpty, ErrorCodes.SharedEntitiesNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.SharedEntityNameTooLong, ErrorCodes.SharedEntityNameTooLong);
            AddMapping(CampaignManagementErrorCode.SharedEntityNameInvalid, ErrorCodes.SharedEntityNameInvalid);
            AddMapping(CampaignManagementErrorCode.SharedListIdNotAllowed, ErrorCodes.SharedListIdNotAllowed);
            AddMapping(CampaignManagementErrorCode.SharedEntityIdInvalid, ErrorCodes.SharedEntityIdInvalid);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationDoesNotExist, ErrorCodes.SharedEntityAssociationDoesNotExist);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordsAccountLimitExceeded, ErrorCodes.NegativeKeywordsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SharedEntityAssociationsAccountLimitExceeded, ErrorCodes.SharedEntityAssociationsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.EntityBatchLimitExceeded, ErrorCodes.EntityBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.SharedEntityBatchLimitExceeded, ErrorCodes.SharedEntityBatchLimitExceeded);

            //structured negative keywords
            AddMapping(CampaignManagementErrorCode.MaxNegativeKeywordLimitExceededForList, ErrorCodes.MaxNegativeKeywordLimitExceededForList);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordDeleted, ErrorCodes.NegativeKeywordDeleted);
            AddMapping(CampaignManagementErrorCode.InvalidNegativeKeywordId, ErrorCodes.InvalidNegativeKeywordId);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordListWithActiveAssociationsCannotBeDeleted, ErrorCodes.NegativeKeywordListWithActiveAssociationsCannotBeDeleted);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordTypeInvalid, ErrorCodes.NegativeKeywordTypeInvalid);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordTextRequired, ErrorCodes.NegativeKeywordTextRequired);
            AddMapping(CampaignManagementErrorCode.DuplicateNegativeKeywordListName, ErrorCodes.DuplicateNegativeKeywordListName);
            AddMapping(CampaignManagementErrorCode.StructuredNegativeKeywordPilotNotEnabledForCustomer, ErrorCodes.StructuredNegativeKeywordPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordTooManyParentTypes, ErrorCodes.NegativeKeywordTooManyParentTypes);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordDuplicateFound, ErrorCodes.NegativeKeywordDuplicateFound);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordNotFound, ErrorCodes.NegativeKeywordNotFound);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordInvalidMatchType, ErrorCodes.NegativeKeywordInvalidMatchType);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordInvalidParentType, ErrorCodes.NegativeKeywordInvalidParentType);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordEntitiesNotPassed, ErrorCodes.NegativeKeywordEntitiesNotPassed);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordEntityNull, ErrorCodes.NegativeKeywordEntityNull);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordEntityTypesMismatch, ErrorCodes.NegativeKeywordEntityTypesMismatch);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordDuplicate, ErrorCodes.NegativeKeywordDuplicate);
            AddMapping(CampaignManagementErrorCode.NegativeKeywordsNotPassed, ErrorCodes.NegativeKeywordsNotPassed);

            AddMapping(CampaignManagementErrorCode.IdsNotPassed, ErrorCodes.IdsNotPassed);
            AddMapping(CampaignManagementErrorCode.DuplicateId, ErrorCodes.DuplicateId);
            AddMapping(CampaignManagementErrorCode.CampaignIdsNotPassed, ErrorCodes.CampaignIdsNotPassed);
            AddMapping(CampaignManagementErrorCode.AdGroupIdsNotPassed, ErrorCodes.AdGroupIdsNotPassed);
            AddMapping(CampaignManagementErrorCode.DuplicateAdgroupId, ErrorCodes.DuplicateInAdGroupIds);
            AddMapping(CampaignManagementErrorCode.IdTypeDoesNotMatchFilterType, ErrorCodes.EntityIdFilterMismatch);

            // Shopping campaign.
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleToAddShoppingCampaign, ErrorCodes.ShoppingCampaignPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CampaignHasDuplicateSettings, ErrorCodes.DuplicateSettingsInEntity);
            AddMapping(CampaignManagementErrorCode.CampaignSettingsMissing, ErrorCodes.CampaignSettingsRequired);
            AddMapping(CampaignManagementErrorCode.CampaignHasUnsupportedSettings, ErrorCodes.InvalidSettingForCampaignType);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidShoppingCampaignPriority, ErrorCodes.ShoppingCampaignPriorityInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidShoppingCampaignSalesCountry, ErrorCodes.ShoppingCampaignSalesCountryCodeInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidShoppingCampaignProviderId, ErrorCodes.ShoppingCampaignStoreIdInvalid);
            AddMapping(CampaignManagementErrorCode.ShoppingCampaignStoreIdInferredFromMapping, ErrorCodes.ShoppingCampaignStoreIdInferredFromMapping);
            AddMapping(CampaignManagementErrorCode.CampaignHasDraftStore, ErrorCodes.CampaignHasDraftStore);
            AddMapping(CampaignManagementErrorCode.CampaignShoppingCampaignSalesCountryShouldNotBeSet, ErrorCodes.SalesCountryCodeImmutable);
            AddMapping(CampaignManagementErrorCode.CampaignShoppingCampaignProviderIdShouldNotBeSet, ErrorCodes.StoreIdImmutable);
            AddMapping(CampaignManagementErrorCode.CampaignOnlyOneCampaignTypeShouldBeSet, ErrorCodes.CampaignTypeLimitExceeded);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidShoppingCampaignSubType, ErrorCodes.ShoppingCampaignSubTypeInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignCoOpCampaignShouldNotBeReturned, ErrorCodes.CoOpCampaignShouldNotBeReturned);
            AddMapping(CampaignManagementErrorCode.CoOpSettingBidOptionInvalid, ErrorCodes.CoOpSettingBidOptionInvalid);
            AddMapping(CampaignManagementErrorCode.CoOpSettingBidBoostValueInvalid, ErrorCodes.CoOpSettingBidBoostValueInvalid);
            AddMapping(CampaignManagementErrorCode.AccountNotInPilotForMMAV2, ErrorCodes.AccountNotInPilotForMMAV2);
            AddMapping(CampaignManagementErrorCode.CoOpSettingBidMaxValueInvalid, ErrorCodes.CoOpSettingBidMaxValueInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnableForInHousePromotion, ErrorCodes.CustomerNotEnableForInHousePromotion);
            AddMapping(CampaignManagementErrorCode.InvalidNetwork, ErrorCodes.InvalidNetwork);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForSponsoredProductAdsV2, ErrorCodes.CustomerNotEnabledForSponsoredProductAdsV2);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForSmartShoppignCampaign, ErrorCodes.CustomerNotEnabledForSmartShoppignCampaign);
            AddMapping(CampaignManagementErrorCode.CustomerHasNoUETForMaxConversionValueBiddingScheme, ErrorCodes.CustomerHasNoUETForMaxConversionValueBiddingScheme);
            AddMapping(CampaignManagementErrorCode.AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme, ErrorCodes.AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme);
            AddMapping(CampaignManagementErrorCode.SmartShoppingCampaignLimitExceeded, ErrorCodes.SmartShoppingCampaignLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ShoppingSmartAdsBidAdjustmentNotSupported, ErrorCodes.ShoppingSmartAdsBidAdjustmentNotSupported);
            AddMapping(CampaignManagementErrorCode.ShoppingSmartAdsEntityNotSupported, ErrorCodes.ShoppingSmartAdsEntityNotSupported);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForFeedLabel, ErrorCodes.CustomerNotEnabledForFeedLabel);
            AddMapping(CampaignManagementErrorCode.ShoppingSettingsFeedLabelInvalidLength, ErrorCodes.ShoppingSettingsFeedLabelInvalidLength);
            AddMapping(CampaignManagementErrorCode.ShoppingSettingsFeedLabelInvalidCharacters, ErrorCodes.ShoppingSettingsFeedLabelInvalidCharacters);
            AddMapping(CampaignManagementErrorCode.ShoppingSettingsFeedLabelAndSalesCountryIncompatible, ErrorCodes.ShoppingSettingsFeedLabelAndSalesCountryIncompatible);

            // CampaignCriterion.
            AddMapping(CampaignManagementErrorCode.ProductCampaignCriterionPilotNotEnabledForCustomer, ErrorCodes.ShoppingCampaignPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionsNullOrEmpty, ErrorCodes.CampaignCriterionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionsEntityLimitExceeded, ErrorCodes.CampaignCriterionsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionTypeInvalid, ErrorCodes.CampaignCriterionTypeInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionIsNull, ErrorCodes.CampaignCriterionIsNull);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionIdShouldBeNullOnAdd, ErrorCodes.CampaignCriterionIdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.DuplicateCampaignCriterionId, ErrorCodes.DuplicateCampaignCriterionId);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionIdInvalid, ErrorCodes.CampaignCriterionIdInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionInvalidConditionType, ErrorCodes.CampaignCriterionProductConditionInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignTypeIsNotShoppingCampaign, ErrorCodes.CampaignTypeIsNotShoppingCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionCriterionIsNullOrEmpty, ErrorCodes.CampaignCriterionCriterionIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionTooManyConditions, ErrorCodes.CampaignCriterionTooManyConditions);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionDuplicateCampaignId, ErrorCodes.CampaignCriterionDuplicateCampaignId);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionIdArrayNullOrEmpty, ErrorCodes.CampaignCriterionIdArrayNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionIdListExceedsLimit, ErrorCodes.CampaignCriterionIdsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ProductConditionIsNull, ErrorCodes.ProductConditionIsNull);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionProductConditionDuplicateOperand, ErrorCodes.CampaignCriterionDuplicateProductConditionOperand);
            AddMapping(CampaignManagementErrorCode.ProductCampaignCriterionAlreadyExist, ErrorCodes.CampaignCriterionAlreadyExists);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionBidMultiplierValueNotSet, ErrorCodes.CampaignCriterionBidMultiplierValueNotSet);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignCriterionStatus, ErrorCodes.CampaignCriterionStatusInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicateCampaignCriterionAudienceAssociation, ErrorCodes.DuplicateCampaignCriterion);
            AddMapping(CampaignManagementErrorCode.CampaignAlreadyHasAdGroupAudienceCriterion, ErrorCodes.CampaignAlreadyHasAdGroupAudienceCriterion);
            AddMapping(CampaignManagementErrorCode.AllNullCampaignCriterionTypesNotAllowedOnCreate, ErrorCodes.AllNullCampaignCriterionTypesNotAllowedOnCreate);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionAudienceAssociationNotExist, ErrorCodes.CampaignCriterionDoesNotExist);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet, ErrorCodes.CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet);

            //Generic validation Errors (AdExtension Framework)
            AddMapping(CampaignManagementErrorCode.AdExtensionPilotNotEnabledForCustomer, ErrorCodes.AdExtensionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdExtensionCampaignAssociationPilotNotEnabledForCustomer, ErrorCodes.AdExtensionCampaignAssociationPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdExtensionAdGroupAssociationPilotNotEnabledForCustomer, ErrorCodes.AdExtensionAdGroupAssociationPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AdExtensionAccountAssociationPilotNotEnabledForCustomer, ErrorCodes.AdExtensionAccountAssociationPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.ValueTooShort, ErrorCodes.ValueTooShort);
            AddMapping(CampaignManagementErrorCode.ValueTooLong, ErrorCodes.ValueTooLong);
            AddMapping(CampaignManagementErrorCode.ValueOutOfRange, ErrorCodes.ValueOutOfRange);
            AddMapping(CampaignManagementErrorCode.ValueIsMissing, ErrorCodes.ValueIsMissing);
            AddMapping(CampaignManagementErrorCode.InvalidValue, ErrorCodes.InvalidValue);
            AddMapping(CampaignManagementErrorCode.InvalidHeader, ErrorCodes.InvalidHeader);
            AddMapping(CampaignManagementErrorCode.InvalidStructuredSnippetCharacter, ErrorCodes.InvalidStructuredSnippetCharacter);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnableForNativeAdsPilot, ErrorCodes.CustomerNotEnableForNativeAdsPilot);
            AddMapping(CampaignManagementErrorCode.InvalidNativeBidAdjustmentValue, ErrorCodes.InvalidAudienceAdsBidAdjustmentValue);
            AddMapping(CampaignManagementErrorCode.TooManyStructuredSnippetText, ErrorCodes.TooManyStructuredSnippetText);
            AddMapping(CampaignManagementErrorCode.TooFewStructuredSnippetText, ErrorCodes.TooFewStructuredSnippetText);
            AddMapping(CampaignManagementErrorCode.ValueImmutable, ErrorCodes.ValueImmutable);

            //Upgraded Urls
            AddMapping(CampaignManagementErrorCode.InvalidUrlScheme, ErrorCodes.InvalidUrlScheme);
            AddMapping(CampaignManagementErrorCode.TrackingTemplateTooLong, ErrorCodes.TrackingTemplateTooLong);
            AddMapping(CampaignManagementErrorCode.MissingLandingPageUrlTag, ErrorCodes.MissingLandingPageUrlTag);
            AddMapping(CampaignManagementErrorCode.CountExceedsLimit, ErrorCodes.CountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.InvalidCharactersInKey, ErrorCodes.InvalidCharactersInKey);
            AddMapping(CampaignManagementErrorCode.InvalidCharactersInValue, ErrorCodes.InvalidCharactersInValue);
            AddMapping(CampaignManagementErrorCode.InvalidTag, ErrorCodes.InvalidTag);
            AddMapping(CampaignManagementErrorCode.InvalidOsType, ErrorCodes.InvalidOsType);
            AddMapping(CampaignManagementErrorCode.KeyTooLong, ErrorCodes.KeyTooLong);
            AddMapping(CampaignManagementErrorCode.UrlTooLong, ErrorCodes.UrlTooLong);
            AddMapping(CampaignManagementErrorCode.DuplicateOsTypeForAppUrl, ErrorCodes.DuplicateOsTypeForAppUrl);
            AddMapping(CampaignManagementErrorCode.KeyNullOrEmpty, ErrorCodes.KeyNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ValueNullOrEmpty, ErrorCodes.ValueNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ParameterIsNull, ErrorCodes.ParameterIsNull);
            AddMapping(CampaignManagementErrorCode.UpgradedUrlsPilotNotEnabledForCustomer, ErrorCodes.UpgradedUrlsPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter, ErrorCodes.FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter);
            AddMapping(CampaignManagementErrorCode.FinalUrlRequiredWhenUsingMobileFinalUrl, ErrorCodes.FinalUrlRequiredWhenUsingMobileFinalUrl);
            AddMapping(CampaignManagementErrorCode.MobileFinalUrlNotAllowedWithMobileDevicePreference, ErrorCodes.MobileFinalUrlNotAllowedWithMobileDevicePreference);
            AddMapping(CampaignManagementErrorCode.BothDestinationUrlAndFinalUrlNotAllowed, ErrorCodes.BothDestinationUrlAndFinalUrlNotAllowed);
            AddMapping(CampaignManagementErrorCode.BothDestinationUrlAndUrlCustomParameterNotAllowed, ErrorCodes.BothDestinationUrlAndUrlCustomParameterNotAllowed);
            AddMapping(CampaignManagementErrorCode.DuplicatesInField, ErrorCodes.DuplicatesInField);
            AddMapping(CampaignManagementErrorCode.DuplicatedTextField, ErrorCodes.DuplicatedTextField);
            AddMapping(CampaignManagementErrorCode.BothDestinationUrlAndTrackingTemplateNotAllowed, ErrorCodes.BothDestinationUrlAndTrackingTemplateNotAllowed);
            AddMapping(CampaignManagementErrorCode.FinalUrlAndMobileUrlNotAllowedForProductPartition, ErrorCodes.CampaignServiceFinalUrlAndMobileUrlNotAllowedForProductPartition);
            AddMapping(CampaignManagementErrorCode.TemplateAndParametersNotAllowedForProductPartitionSubdivision, ErrorCodes.CampaignServiceTemplateAndParametersNotAllowedForProductPartitionSubdivision);
            AddMapping(CampaignManagementErrorCode.InvalidCustomParametersText, ErrorCodes.InvalidCustomParametersText);
            AddMapping(CampaignManagementErrorCode.UpgradedUrlsPilotNotEnabledCustomParametersNotImported, ErrorCodes.UpgradedUrlsPilotNotEnabledCustomParametersNotImported);
            AddMapping(CampaignManagementErrorCode.MissingRequiredParameterOrString, ErrorCodes.MissingRequiredParameterOrString);
            AddMapping(CampaignManagementErrorCode.InvalidQueryDelimiterPlacement, ErrorCodes.InvalidQueryDelimiterPlacement);
            AddMapping(CampaignManagementErrorCode.InvalidAppId, ErrorCodes.InvalidAppId);

            AddMapping(CampaignManagementErrorCode.AppInstallAdAppPlatformInvalid, ErrorCodes.AppInstallAdAppPlatformInvalid);
            AddMapping(CampaignManagementErrorCode.AppInstallAdAppPlatformNullOrEmpty, ErrorCodes.AppInstallAdAppPlatformNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppInstallAdAppStoreIdInvalid, ErrorCodes.AppInstallAdAppStoreIdInvalid);
            AddMapping(CampaignManagementErrorCode.AppInstallAdAppStoreIdTooMuchText, ErrorCodes.AppInstallAdAppStoreIdTooMuchText);
            AddMapping(CampaignManagementErrorCode.AppInstallAdAppStoreIdIsNullOrEmpty, ErrorCodes.AppInstallAdAppStoreIdIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AppInstallAdUpdateAppPlatformChanged, ErrorCodes.AppInstallAdUpdateAppPlatformChanged);
            AddMapping(CampaignManagementErrorCode.AppInstallAdUpdateAppStoreIdChanged, ErrorCodes.AppInstallAdUpdateAppStoreIdChanged);

            AddMapping(CampaignManagementErrorCode.BiddingSchemeNotEnabledForCustomer, ErrorCodes.BiddingSchemeNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.UnsupportedBiddingScheme, ErrorCodes.UnsupportedBiddingScheme);
            AddMapping(CampaignManagementErrorCode.UnsupportedGoogleBidStrategyType, ErrorCodes.UnsupportedGoogleBidStrategyType);
            AddMapping(CampaignManagementErrorCode.TargetCpaIsRequired, ErrorCodes.TargetCpaIsRequired);
            AddMapping(CampaignManagementErrorCode.MaxCpcExceedsMonthlyBudget, ErrorCodes.MaxCpcExceedsMonthlyBudget);
            AddMapping(CampaignManagementErrorCode.InheritFromParentBiddingSchemeNotAllowedForCampaign, ErrorCodes.InheritFromParentBiddingSchemeNotAllowedForCampaign);
            AddMapping(CampaignManagementErrorCode.SupportOnlyManualAndInheritFromParentBiddingStrategy, ErrorCodes.SupportOnlyManualAndInheritFromParentBiddingStrategy);
            AddMapping(CampaignManagementErrorCode.EnhancedCpcNotAllowedForShoppingCampaign, ErrorCodes.EnhancedCpcNotAllowedForShoppingCampaign);
            AddMapping(CampaignManagementErrorCode.MaxCpcLessThanOrEqualToZero, ErrorCodes.MaxCpcLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.TargetCpaLessThanOrEqualToZero, ErrorCodes.TargetCpaLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.TargetCpaExceedsMonthlyBudget, ErrorCodes.TargetCpaExceedsMonthlyBudget);
            AddMapping(CampaignManagementErrorCode.UnsupportedBiddingSchemeForMonthlyBudgetType, ErrorCodes.UnsupportedBiddingSchemeForMonthlyBudgetType);
            AddMapping(CampaignManagementErrorCode.MaxCpcBidAmountsLessThanFloorPrice, ErrorCodes.MaxCpcBidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.MaxCpcBidsAmountsGreaterThanCeilingPrice, ErrorCodes.MaxCpcBidsAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.TargetCpaBidAmountsLessThanFloorPrice, ErrorCodes.TargetCpaBidAmountsLessThanFloorPrice);
            AddMapping(CampaignManagementErrorCode.TargetCpaBidsAmountsGreaterThanCeilingPrice, ErrorCodes.TargetCpaBidsAmountsGreaterThanCeilingPrice);
            AddMapping(CampaignManagementErrorCode.NoEnoughConversionForMaxConversionsBiddingScheme, ErrorCodes.NoEnoughConversionForMaxConversionsBiddingScheme);
            AddMapping(CampaignManagementErrorCode.NoEnoughConversionForTargetCpaBiddingScheme, ErrorCodes.NoEnoughConversionForTargetCpaBiddingScheme);
            AddMapping(CampaignManagementErrorCode.MaxConversionAndSharedBudgetAreMutuallyExclusive, ErrorCodes.MaxConversionAndSharedBudgetAreMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.TargetCpaAndSharedBudgetAreMutuallyExclusive, ErrorCodes.TargetCpaAndSharedBudgetAreMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.MaxConversionsNotEnabledForDynamicSearchAds, ErrorCodes.MaxConversionsNotEnabledForDynamicSearchAds);
            AddMapping(CampaignManagementErrorCode.TargetCpaNotEnabledForDynamicSearchAds, ErrorCodes.TargetCpaNotEnabledForDynamicSearchAds);
            AddMapping(CampaignManagementErrorCode.MaxConversionsNotEnabledForTheMarkets, ErrorCodes.MaxConversionsNotEnabledForTheMarkets);
            AddMapping(CampaignManagementErrorCode.TargetCpaNotEnabledForTheMarkets, ErrorCodes.TargetCpaNotEnabledForTheMarkets);
            AddMapping(CampaignManagementErrorCode.BiddingSchemeAndSharedBudgetAreMutuallyExclusive, ErrorCodes.BiddingSchemeAndSharedBudgetAreMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.SharedBudgetAndBiddingSchemeAreMutuallyExclusive, ErrorCodes.SharedBudgetAndBiddingSchemeAreMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.BiddingSchemeNotEnabledForTheLocations, ErrorCodes.BiddingSchemeNotEnabledForTheLocations);
            AddMapping(CampaignManagementErrorCode.LocationNotEnabledForTheBiddingScheme, ErrorCodes.LocationNotEnabledForTheBiddingScheme);
            AddMapping(CampaignManagementErrorCode.NotEnoughConversionsForTargetRoasBiddingScheme, ErrorCodes.NotEnoughConversionsForTargetRoasBiddingScheme);
            AddMapping(CampaignManagementErrorCode.NotEnoughRevenueForTargetRoasBiddingScheme, ErrorCodes.NotEnoughRevenueForTargetRoasBiddingScheme);
            AddMapping(CampaignManagementErrorCode.InvalidTargetRoasValue, ErrorCodes.InvalidTargetRoasValue);
            AddMapping(CampaignManagementErrorCode.CustomerNotInPilotForTargetRoas, ErrorCodes.CustomerNotInPilotForTargetRoas);
            AddMapping(CampaignManagementErrorCode.BidStrategyUnchangedBecauseNotEnoughRevenue, ErrorCodes.BidStrategyUnchangedBecauseNotEnoughRevenue);
            AddMapping(CampaignManagementErrorCode.ConversionGoalCriteriaNotMetForBiddingScheme, ErrorCodes.ConversionGoalCriteriaNotMetForBiddingScheme);
            AddMapping(CampaignManagementErrorCode.CustomerNotInPilotForMaxConversionValue, ErrorCodes.CustomerNotInPilotForMaxConversionValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetAdPositionValue, ErrorCodes.InvalidTargetAdPositionValue);
            AddMapping(CampaignManagementErrorCode.TargetImpressionShareIsRequired, ErrorCodes.TargetImpressionShareIsRequired);
            AddMapping(CampaignManagementErrorCode.InvalidTargetImpressionShareValue, ErrorCodes.InvalidTargetImpressionShareValue);
            AddMapping(CampaignManagementErrorCode.CustomerNotInPilotForTargetImpressionShare, ErrorCodes.CustomerNotInPilotForTargetImpressionShare);
            AddMapping(CampaignManagementErrorCode.InvalidMaxCpcValue, ErrorCodes.InvalidMaxCpcValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetCpaValue, ErrorCodes.InvalidTargetCpaValue);
            AddMapping(CampaignManagementErrorCode.InvalidManualCpcValue, ErrorCodes.InvalidManualCpcValue);
            AddMapping(CampaignManagementErrorCode.ManualCpcIsRequired, ErrorCodes.ManualCpcIsRequired);
            AddMapping(CampaignManagementErrorCode.ManualCpcLessThanOrEqualToZero, ErrorCodes.ManualCpcLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.MaxCpmLessThanOrEqualToZero, ErrorCodes.MaxCpmLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.InvalidMaxCpmValue, ErrorCodes.InvalidMaxCpmValue);
            AddMapping(CampaignManagementErrorCode.MaxCpcGreaterThanCeiling, ErrorCodes.MaxCpcGreaterThanCeiling);
            AddMapping(CampaignManagementErrorCode.MaxCpmGreaterThanCeiling, ErrorCodes.MaxCpmGreaterThanCeiling);

            AddMapping(CampaignManagementErrorCode.InvalidAudienceId, ErrorCodes.InvalidAudienceId);
            AddMapping(CampaignManagementErrorCode.AudienceIdsNotPassed, ErrorCodes.AudienceIdsNotPassed);
            AddMapping(CampaignManagementErrorCode.DuplicateAudienceId, ErrorCodes.DuplicateAudienceId);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionDuplicateAudienceIds, ErrorCodes.DuplicateAudienceId);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceName, ErrorCodes.InvalidAudienceName);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidAudienceName, ErrorCodes.InvalidAudienceName);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionAudienceAssociationsCannotBeNull, ErrorCodes.AdGroupCriterionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidBidMultiplierValue, ErrorCodes.InvalidAudienceCriterionBidAdjustment);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionInvalidAudienceId, ErrorCodes.InvalidAudienceId);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionAudienceAssociationNotExist, ErrorCodes.AdGroupCriterionDoesNotExist);
            AddMapping(CampaignManagementErrorCode.AudienceCanNotAssociateWithContentOnlyAdGroup, ErrorCodes.CannotAssociateAudienceWithContentOnlyAdGroup);
            AddMapping(CampaignManagementErrorCode.DuplicateAdGroupCriterionAudienceAssociation, ErrorCodes.DuplicateAdGroupCriterion);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceTagId, ErrorCodes.InvalidRemarketingListTagId);
            AddMapping(CampaignManagementErrorCode.DuplicateAudienceName, ErrorCodes.DuplicateAudienceName);
            AddMapping(CampaignManagementErrorCode.AudienceCannotBeDeletedDueToExistingAssociation, ErrorCodes.AudienceCannotBeDeletedDueToExistingAdGroupCriterion);
            AddMapping(CampaignManagementErrorCode.AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociation, ErrorCodes.AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociations);
            AddMapping(CampaignManagementErrorCode.MaxAudiencesPerCustomerLimitReached, ErrorCodes.MaxAudiencesPerCustomerLimitReached);
            AddMapping(CampaignManagementErrorCode.MaxAudiencesPerAccountLimitReached, ErrorCodes.MaxAudiencesPerAccountLimitReached);
            AddMapping(CampaignManagementErrorCode.MaxAudienceCriterionsPerAccountLimitReached, ErrorCodes.MaxAudienceCriterionsPerAccountLimitReached);
            AddMapping(CampaignManagementErrorCode.MaxCriterionLimitExceededForCustomer, ErrorCodes.MaxCriterionLimitExceededForCustomer);
            AddMapping(CampaignManagementErrorCode.MaxInMarketAudienceExclusionPerAccountLimitReached, ErrorCodes.MaxInMarketAudienceExclusionPerAccountLimitReached);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceCustomEventExression, ErrorCodes.InvalidRemarketingListRule);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceCustomEventValue, ErrorCodes.InvalidRemarketingListRuleItem);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceConstraints, ErrorCodes.InvalidRemarketingListRule);
            AddMapping(CampaignManagementErrorCode.AudienceIsAccountLevelCannotBeUpdated, ErrorCodes.AudienceCanNotChangeScopeOnUpdate);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceType, ErrorCodes.InvalidAudienceType);
            AddMapping(CampaignManagementErrorCode.InvalidAudience, ErrorCodes.InvalidAudienceType);
            AddMapping(CampaignManagementErrorCode.CannotDeleteAudience, ErrorCodes.InMarketAudienceCouldNotBeDeleted);
            AddMapping(CampaignManagementErrorCode.RemarketingSimilarAudienceCannotBeDeleted, ErrorCodes.SimilarRemarketingListCouldNotBeDeleted);
            AddMapping(CampaignManagementErrorCode.AudienceIdNotMatchAudienceTypeFilter, ErrorCodes.AudienceIdDoNotMatchAudienceType);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionIdNotMatchCriterionType, ErrorCodes.AdGroupCriterionIdNotMatchCriterionType);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionBidMultiplierValueNotSet, ErrorCodes.AdGroupCriterionBidMultiplierValueNotSet);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionTransactionAcrossAdGroups, ErrorCodes.AdGroupCriterionTransactionAcrossAdGroups);
            AddMapping(CampaignManagementErrorCode.AudienceDeliveryChannelNotMatchCampaignType, ErrorCodes.AudienceDeliveryChannelNotMatchCampaignType);
            AddMapping(CampaignManagementErrorCode.InvalidActionType, ErrorCodes.InvalidProductAudienceType);
            AddMapping(CampaignManagementErrorCode.DuplicateProductAudience, ErrorCodes.DuplicateProductAudience);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForSimilarAudience, ErrorCodes.CustomerNotEligibleForSimilarRemarketingList);
            AddMapping(CampaignManagementErrorCode.AdGroupAlreadyHasCampaignAudienceCriterion, ErrorCodes.AdGroupAlreadyHasCampaignAudienceCriterion);
            AddMapping(CampaignManagementErrorCode.IllegalAudienceAssociationConversionFromExclusion, ErrorCodes.IllegalAudienceAssociationConversionFromExclusion);
            AddMapping(CampaignManagementErrorCode.MdsServiceError, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AccountLevelAudienceAcccountInfoCannotBeNull, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AudienceAccountInfoCannotBeUpdatedForOtherAccounts, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AccountIdIsRequiredWhenDeleteMultipleAccountLevelAudeince, ErrorCodes.InternalError);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet, ErrorCodes.AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet);
            AddMapping(CampaignManagementErrorCode.CustomerIsNotEligibleForImpressionBasedRemarketingList, ErrorCodes.CustomerIsNotEligibleForImpressionBasedRemarketingList);
            AddMapping(CampaignManagementErrorCode.CustomerCannotOperateImpressionBasedRemarketingList, ErrorCodes.CustomerCannotOperateImpressionBasedRemarketingList);
            AddMapping(CampaignManagementErrorCode.InvalidEntityIdForImpressionBasedRemarketingList, ErrorCodes.InvalidEntityIdForImpressionBasedRemarketingList);
            AddMapping(CampaignManagementErrorCode.InvalidEntityTypeForImpressionBasedRemarketingList, ErrorCodes.InvalidEntityTypeForImpressionBasedRemarketingList);
            AddMapping(CampaignManagementErrorCode.ImpressionBasedRemarketingListCanOnlyBeEditedByCreator, ErrorCodes.ImpressionBasedRemarketingListCanOnlyBeEditedByCreator);
            AddMapping(CampaignManagementErrorCode.ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator, ErrorCodes.ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator);
            AddMapping(CampaignManagementErrorCode.DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList, ErrorCodes.DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList);

            // Share Library
            AddMapping(CampaignManagementErrorCode.InvalidCustomerIdAccountIdForSharing, ErrorCodes.InvalidCustomerAccountShare);
            AddMapping(CampaignManagementErrorCode.SharingScopeEntityScopeDoesNotMatch, ErrorCodes.CustomerShareEntityScopeDoesNotMatch);
            AddMapping(CampaignManagementErrorCode.SharedLibraryUserPermissionDenied, ErrorCodes.CustomerAccountSharePermissionDenied);

            AddMapping(CampaignManagementErrorCode.BudgetPilotNotEnabledForCustomer, ErrorCodes.BudgetPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.BudgetsNullOrEmpty, ErrorCodes.BudgetsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.BudgetIsNull, ErrorCodes.BudgetIsNull);
            AddMapping(CampaignManagementErrorCode.BudgetIdShouldBeNullOnAdd, ErrorCodes.BudgetIdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.BudgetNameMissing, ErrorCodes.BudgetNameMissing);
            AddMapping(CampaignManagementErrorCode.BudgetNameTooLong, ErrorCodes.BudgetNameTooLong);
            AddMapping(CampaignManagementErrorCode.BudgetNameHasInvalidChars, ErrorCodes.BudgetNameInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicateBudgetName, ErrorCodes.DuplicateBudgetName);
            AddMapping(CampaignManagementErrorCode.BudgetIsSharedWithCampaigns, ErrorCodes.BudgetIsSharedWithCampaigns);
            AddMapping(CampaignManagementErrorCode.BudgetTypeCannotBeNullOnAdd, ErrorCodes.BudgetTypeCannotBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.MonthlyBudgetNotAllowed, ErrorCodes.MonthlyBudgetNotAllowed);
            AddMapping(CampaignManagementErrorCode.BudgetAmountMissing, ErrorCodes.BudgetAmountMissing);
            AddMapping(CampaignManagementErrorCode.BudgetAmountIsAboveLimit, ErrorCodes.BudgetAmountIsAboveLimit);
            AddMapping(CampaignManagementErrorCode.BudgetAmountIsBelowLimit, ErrorCodes.BudgetAmountIsBelowLimit);
            AddMapping(CampaignManagementErrorCode.BudgetOperationsBatchLimitExceeded, ErrorCodes.BudgetBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.BudgetEntityLimitExceeded, ErrorCodes.BudgetEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.DuplicateBudgetId, ErrorCodes.DuplicateBudgetId);
            AddMapping(CampaignManagementErrorCode.BudgetIdInvalid, ErrorCodes.BudgetIdInvalid);
            AddMapping(CampaignManagementErrorCode.BudgetTypeSwitchFromSharedToMonthlyNotSupported, ErrorCodes.CannotUpdateSharedDailyBudgetToUnsharedMonthlyBudget);
            AddMapping(CampaignManagementErrorCode.DailyBudgetAmountInvalidForSharedBudget, ErrorCodes.CannotUpdateSharedBudget);
            AddMapping(CampaignManagementErrorCode.MonthlyBudgetAmountInvalidForSharedBudget, ErrorCodes.CannotUpdateSharedBudget);
            AddMapping(CampaignManagementErrorCode.BudgetTypeInvalidForSharedBudget, ErrorCodes.CannotUpdateSharedBudget);
            AddMapping(CampaignManagementErrorCode.SharedBudgetNotAllowedWithPerformanceTargetCampaign, ErrorCodes.SharedBudgetNotAllowedWithPerformanceTargetCampaign);

            AddMapping(CampaignManagementErrorCode.EntityAlreadyExists, ErrorCodes.EntityAlreadyExists);
            AddMapping(CampaignManagementErrorCode.DuplicateEntity, ErrorCodes.DuplicateEntity);
            AddMapping(CampaignManagementErrorCode.EntityDoesNotExist, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.MaxLimitReached, ErrorCodes.MaxLimitReached);
            AddMapping(CampaignManagementErrorCode.EntityIdNotPassed, ErrorCodes.EntityIdNotPassed);
            AddMapping(CampaignManagementErrorCode.CampaignCriterionActionsNullOrEmpty, ErrorCodes.CampaignCriterionActionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OSNameIsNotSupported, ErrorCodes.OSNameIsNotSupported);
            AddMapping(CampaignManagementErrorCode.RelatedCriterionActionError, ErrorCodes.RelatedCriterionActionError);
            AddMapping(CampaignManagementErrorCode.InvalidCriterionId, ErrorCodes.InvalidCriterionId);
            AddMapping(CampaignManagementErrorCode.CriterionDoesNotMatch, ErrorCodes.CriterionDoesNotMatch);
            AddMapping(CampaignManagementErrorCode.IncompleteDeviceCriterionSet, ErrorCodes.IncompleteDeviceCriterionSet);
            AddMapping(CampaignManagementErrorCode.LocationIntentCriterionCannotBeDeleted, ErrorCodes.LocationIntentCriterionCannotBeDeleted);
            AddMapping(CampaignManagementErrorCode.InvalidIntentOption, ErrorCodes.LocationIntentCriterionInvalid);
            AddMapping(CampaignManagementErrorCode.BidsMustBeEqualForDeviceType, ErrorCodes.BidsMustBeEqualForDeviceType);
            AddMapping(CampaignManagementErrorCode.BidAdjustmentNotSupportedForSmartCampaignCriterion, ErrorCodes.NotSupportedForThisCampaignType);

            // Search Ads
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForCampaignAutomatedCallToActionOptOut, ErrorCodes.AccountNotEnabledForCampaignAutomatedCallToActionOptOut);

            //Dynamic Search Ads
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdsSettingNotAllowedForUpdate, ErrorCodes.DynamicSearchAdsCampaignSettingNotAllowedForUpdate);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidDynamicSearchAdsDomainName, ErrorCodes.InvalidDynamicSearchAdsCampaignDomainName);
            AddMapping(CampaignManagementErrorCode.CampaignInvalidDynamicSearchAdsLanguage, ErrorCodes.InvalidDynamicSearchAdsCampaignLanguage);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicSearchAds, ErrorCodes.CustomerNotEligibleForDynamicSearchAds);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign, ErrorCodes.DynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign);
            AddMapping(CampaignManagementErrorCode.CannotUpdateCriterionForWebpageCriterion, ErrorCodes.CannotUpdateCriterionForWebpageCriterion);
            AddMapping(CampaignManagementErrorCode.BiddingSchemeMustInheritFromParentEntity, ErrorCodes.BiddingSchemeMustInheritFromParentEntity);
            AddMapping(CampaignManagementErrorCode.SubstitutionNotSupportedForDynamicSearchAd, ErrorCodes.SubstitutionNotSupportedForDynamicSearchAd);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionAlreadyExists, ErrorCodes.WebpageCriterionAlreadyExists);
            AddMapping(CampaignManagementErrorCode.MaxCampaignWebpageCriterionsLimitExceededForCampaign, ErrorCodes.MaxCampaignWebpageCriterionsLimitExceededForCampaign);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdPath1Invalid, ErrorCodes.DynamicSearchAdPath1Invalid);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdPath2Invalid, ErrorCodes.DynamicSearchAdPath2Invalid);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdPath1TooLong, ErrorCodes.DynamicSearchAdPath1TooLong);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdPath2TooLong, ErrorCodes.DynamicSearchAdPath2TooLong);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdPath2SetWithoutPath1, ErrorCodes.DynamicSearchAdPath2SetWithoutPath1);
            AddMapping(CampaignManagementErrorCode.BiddableOrNegativeStatusCannotBeUpdated, ErrorCodes.BiddableOrNegativeStatusCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdTextPart2PilotNotEnabledForCustomer, ErrorCodes.DynamicSearchAdTextPart2PilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdTextPart2TooLong, ErrorCodes.DynamicSearchAdTextPart2TooLong);
            AddMapping(CampaignManagementErrorCode.DynamicSearchAdTextPart2Invalid, ErrorCodes.DynamicSearchAdTextPart2Invalid);
            AddMapping(CampaignManagementErrorCode.DSADomainLanguagesPhase2PilotNotEnabledForCustomer, ErrorCodes.DSADomainLanguagesPhase2PilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.DSADomainLanguagesPhase3PilotNotEnabledForCustomer, ErrorCodes.DSADomainLanguagesPhase3PilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.DSADomainLanguagesPhase4PilotNotEnabledForCustomer, ErrorCodes.DSADomainLanguagesPhase4PilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain, ErrorCodes.WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter, ErrorCodes.WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter);

            // Dynamic search ads mixed mode
            AddMapping(CampaignManagementErrorCode.CampaignServiceAccountNotInPilotForMixedModeCampaign, ErrorCodes.AccountNotInPilotForMixedModeCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignServiceInvalidAdGroupTypeForCampaignType, ErrorCodes.InvalidAdGroupTypeForCampaignType);
            AddMapping(CampaignManagementErrorCode.CampaignServiceCannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings, ErrorCodes.CannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings);
            AddMapping(CampaignManagementErrorCode.CampaignServiceDynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup, ErrorCodes.DynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup);
            AddMapping(CampaignManagementErrorCode.CampaignServiceAdTypeInvalidForAdGroup, ErrorCodes.AdTypeInvalidForAdGroup);
            AddMapping(CampaignManagementErrorCode.CampaignServiceInvalidCriterionForCampaignType, ErrorCodes.InvalidCriterionForCampaignType);
            AddMapping(CampaignManagementErrorCode.CampaignServiceInvalidCriterionForAdGroupType, ErrorCodes.InvalidCriterionForAdGroupType);
            AddMapping(CampaignManagementErrorCode.CampaignServiceDynamicSearchAdsNotSupportedForDisclaimerCampaign, ErrorCodes.DynamicSearchAdsNotSupportedForDisclaimerCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignServiceEntityNotAllowedForDynamicSearchAdsAdGroup, ErrorCodes.EntityNotAllowedForDynamicSearchAdsAdGroup);
            AddMapping(CampaignManagementErrorCode.CampaignServiceAdGroupTypeInvalid, ErrorCodes.AdGroupTypeInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignServiceCannotAddCriterionToCampaignWithoutDynamicSearchSettings, ErrorCodes.CannotAddCriterionToCampaignWithoutDynamicSearchSettings);

            //Expanded Text Ad
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart1Invalid, ErrorCodes.ExpandedTextAdTitlePart1Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart1TooLong, ErrorCodes.ExpandedTextAdTitlePart1TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart2Invalid, ErrorCodes.ExpandedTextAdTitlePart2Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart2TooLong, ErrorCodes.ExpandedTextAdTitlePart2TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart3Invalid, ErrorCodes.ExpandedTextAdTitlePart3Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTitlePart3TooLong, ErrorCodes.ExpandedTextAdTitlePart3TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTextPart1Invalid, ErrorCodes.ExpandedTextAdTextPart1Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTextPart1TooLong, ErrorCodes.ExpandedTextAdTextPart1TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTextPart2Invalid, ErrorCodes.ExpandedTextAdTextPart2Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdTextPart2TooLong, ErrorCodes.ExpandedTextAdTextPart2TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdPath1Invalid, ErrorCodes.ExpandedTextAdPath1Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdPath1TooLong, ErrorCodes.ExpandedTextAdPath1TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdPath2Invalid, ErrorCodes.ExpandedTextAdPath2Invalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdPath2TooLong, ErrorCodes.ExpandedTextAdPath2TooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdPath2SetWithoutPath1, ErrorCodes.ExpandedTextAdPath2SetWithoutPath1);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCombinedTitleTooLong, ErrorCodes.ExpandedTextAdCombinedTitleTooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdFinalUrlDomainTooLong, ErrorCodes.ExpandedTextAdFinalUrlDomainTooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdDisplayUrlDomainTooLong, ErrorCodes.ExpandedTextAdDomainTooLong);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdDisplayUrlDomainInvalid, ErrorCodes.ExpandedTextAdDomainInvalid);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdDisplayUrlDomainExtractionFailed, ErrorCodes.InvalidFinalUrlsText);
            //Expanded Text Ad - Functions
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdInvalidFunctionFormat, ErrorCodes.ExpandedTextAdInvalidFunctionFormat);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdUnknownFunction, ErrorCodes.ExpandedTextAdUnknownFunction);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdMissingDelimiterBetweenFunctions, ErrorCodes.ExpandedTextAdMissingDelimiterBetweenFunctions);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownInvalidDateTime, ErrorCodes.ExpandedTextAdCountdownInvalidDateTime);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownInvalidLanguageCode, ErrorCodes.ExpandedTextAdCountdownInvalidLanguageCode);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownInvalidDaysBefore, ErrorCodes.ExpandedTextAdCountdownInvalidDaysBefore);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownDaysBeforeOutOfRange, ErrorCodes.ExpandedTextAdCountdownDaysBeforeOutOfRange);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownInvalidParameters, ErrorCodes.ExpandedTextAdCountdownInvalidParameters);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownPastDateTime, ErrorCodes.ExpandedTextAdCountdownPastDateTime);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdCountdownInvalidDefaultText, ErrorCodes.ExpandedTextAdCountdownInvalidDefaultText);
            AddMapping(CampaignManagementErrorCode.MaxAdsLimitExceededForCustomer, ErrorCodes.MaxAdsReachedForCustomer);
            AddMapping(CampaignManagementErrorCode.MaxAdsLimitExceededForAccount, ErrorCodes.MaxAdsReachedForAccount);
            AddMapping(CampaignManagementErrorCode.ExpandedTextAdDefaultTextRequiredForKeywordParameter, ErrorCodes.ExpandedTextDefaultTextRequiredForKeyword);
            AddMapping(CampaignManagementErrorCode.AdCustomizerFeedNameMissing, ErrorCodes.AdCustomizerFeedNameMissing);
            AddMapping(CampaignManagementErrorCode.AdCustomizerFeedAttributeMissing, ErrorCodes.AdCustomizerFeedAttributeMissing);
            AddMapping(CampaignManagementErrorCode.AdCustomizerDefaultValueMissing, ErrorCodes.AdCustomizerDefaultValueMissing);
            AddMapping(CampaignManagementErrorCode.FeedPerAdLimitExceeded, ErrorCodes.FeedPerAdLimitExceeded);
            AddMapping(CampaignManagementErrorCode.FeedNameDoesNotExist, ErrorCodes.FeedNameDoesNotExist);
            AddMapping(CampaignManagementErrorCode.InvalidFeedForAdType, ErrorCodes.InvalidFeedForAdType);
            AddMapping(CampaignManagementErrorCode.FeedAttributeDoesNotExist, ErrorCodes.FeedAttributeDoesNotExist);
            AddMapping(CampaignManagementErrorCode.InvalidFeedAttributeForAdType, ErrorCodes.InvalidFeedAttributeForAdType);
            AddMapping(CampaignManagementErrorCode.InvalidFeedAttributeTypeInCountdown, ErrorCodes.InvalidFeedAttributeTypeInCountdown);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedItemRowWithSameKeyAttributes, ErrorCodes.KeyFeedItemAttributeValueConfliction);

            //Webpage campaign criterion
            AddMapping(CampaignManagementErrorCode.CampaignTypeIsNotDynamicSearchAdsCampaign,
                ErrorCodes.CampaignTypeIsNotDynamicSearchAdsCampaign);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionParameterIsNull,
                ErrorCodes.WebpageCriterionParameterIsNull);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionNameInvalid,
                ErrorCodes.WebpageCriterionNameInvalid);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionConditionsContainDuplicateValues,
                ErrorCodes.WebpageCriterionConditionsContainDuplicateValues);
            AddMapping(CampaignManagementErrorCode.TooManyWebpageCriterionConditions,
                ErrorCodes.TooManyWebpageCriterionConditions);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionConditionInvalid,
                ErrorCodes.WebpageCriterionConditionInvalid);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionWebpageConditionOperandInvalid,
                ErrorCodes.WebpageCriterionWebpageConditionOperandInvalid);
            AddMapping(CampaignManagementErrorCode.WebpageCriterionWebpageConditionArgumentInvalid,
                ErrorCodes.WebpageCriterionWebpageConditionArgumentInvalid);
            AddMapping(CampaignManagementErrorCode.NegativeWebpageCriterionConditionIsNullOrEmpty,
                ErrorCodes.NegativeWebpageCriterionConditionIsNullOrEmpty);

            //Webpage adgroup criterion
            AddMapping(CampaignManagementErrorCode.InvalidUpgradedUrlsForWebpageCriterion,
                ErrorCodes.InvalidUpgradedUrlsForWebpageCriterion);

            //Conversion Tracking
            AddMapping(CampaignManagementErrorCode.TagsNotPassed, ErrorCodes.UetTagsNotPassed);
            AddMapping(CampaignManagementErrorCode.InvalidTagName, ErrorCodes.InvalidUetTagName);
            AddMapping(CampaignManagementErrorCode.InvalidTagDescription, ErrorCodes.InvalidUetTagDescription);
            AddMapping(CampaignManagementErrorCode.InvalidTagId, ErrorCodes.InvalidUetTagId);
            AddMapping(CampaignManagementErrorCode.DuplicateTagId, ErrorCodes.DuplicateUetTagId);
            AddMapping(CampaignManagementErrorCode.TagIdDoesNotExist, ErrorCodes.InvalidUetTagId);
            AddMapping(CampaignManagementErrorCode.TagWithSameNameAlreadyExistsUnderCustomer, ErrorCodes.UetTagNameAlreadyExists);
            AddMapping(CampaignManagementErrorCode.MaxUetTagPerCustomerLimitReached, ErrorCodes.TotalUetTagsExceedLimit);
            AddMapping(CampaignManagementErrorCode.TagIdsNotPassed, ErrorCodes.UetTagIdsNotPassed);
            AddMapping(CampaignManagementErrorCode.TagDoesNotExistForPassedGoal, ErrorCodes.InvalidUetTagId);
            AddMapping(CampaignManagementErrorCode.GoalWithSameNameAlreadyExistsUnderTag, ErrorCodes.ConversionGoalNameAlreadyExists);
            AddMapping(CampaignManagementErrorCode.GoalsNotPassed, ErrorCodes.ConversionGoalsNotPassed);
            AddMapping(CampaignManagementErrorCode.GoalNameNullOrEmpty, ErrorCodes.ConversionGoalNameNotPassed);
            AddMapping(CampaignManagementErrorCode.GoalNameHasInvalidCharacters, ErrorCodes.InvalidConversionGoalName);
            AddMapping(CampaignManagementErrorCode.GoalNameIsTooLong, ErrorCodes.InvalidConversionGoalName);
            AddMapping(CampaignManagementErrorCode.InvalidGoalLookbackWindow, ErrorCodes.InvalidConversionGoalConversionWindow);
            AddMapping(CampaignManagementErrorCode.GoalsBatchSizeExceedsLimit, ErrorCodes.ConversionGoalArrayExceedsLimit);
            AddMapping(CampaignManagementErrorCode.InvalidGoalStatus, ErrorCodes.InvalidConversionGoalStatus);
            AddMapping(CampaignManagementErrorCode.InvalidGoalValue, ErrorCodes.InvalidConversionGoalRevenueValue);
            AddMapping(CampaignManagementErrorCode.DestinationGoalUrlStringNullOrEmpty, ErrorCodes.UrlGoalUrlExpressionNotPassed);
            AddMapping(CampaignManagementErrorCode.DestinationGoalUrlStringHasInvalidCharacters, ErrorCodes.InvalidUrlGoalUrlExpression);
            AddMapping(CampaignManagementErrorCode.DestinationGoalUrlStringTooLong, ErrorCodes.InvalidUrlGoalUrlExpression);
            AddMapping(CampaignManagementErrorCode.InvalidDurationGoalDuration, ErrorCodes.InvalidDurationGoalDurationTime);
            AddMapping(CampaignManagementErrorCode.InvalidEventGoalCategory, ErrorCodes.InvalidEventGoalCategoryExpression);
            AddMapping(CampaignManagementErrorCode.InvalidEventGoalAction, ErrorCodes.InvalidEventGoalActionExpression);
            AddMapping(CampaignManagementErrorCode.InvalidEventGoalLabel, ErrorCodes.InvalidEventGoalLabelExpression);
            AddMapping(CampaignManagementErrorCode.InvalidEventGoalValue, ErrorCodes.InvalidEventGoalValue);
            AddMapping(CampaignManagementErrorCode.InvalidPageViews, ErrorCodes.InvalidMinimumPagesViewedForGoal);
            AddMapping(CampaignManagementErrorCode.InvalidGoalId, ErrorCodes.InvalidConversionGoalId);
            AddMapping(CampaignManagementErrorCode.DuplicateGoalId, ErrorCodes.DuplicateConversionGoalId);
            AddMapping(CampaignManagementErrorCode.AppInstalGoalAppPlatformInvalid, ErrorCodes.InvalidAppInstallGoalAppPlatform);
            AddMapping(CampaignManagementErrorCode.AppInstalGoalAppStoreIdNullOrEmpty, ErrorCodes.AppInstallGoalStoreIdNotPassed);
            AddMapping(CampaignManagementErrorCode.AppInstalGoalAppStoreIdHasInvalidCharacters, ErrorCodes.InvalidAppInstallGoalStoreId);
            AddMapping(CampaignManagementErrorCode.AppInstalGoalAppStoreIdTooLong, ErrorCodes.InvalidAppInstallGoalStoreId);
            AddMapping(CampaignManagementErrorCode.MaxConversionGoalPerAccountLimitReached, ErrorCodes.TotalConversionGoalsExceedAccountLimit);
            AddMapping(CampaignManagementErrorCode.MaxConversionGoalPerCustomerLimitReached, ErrorCodes.TotalConversionGoalsExceedAccountLimit);
            AddMapping(CampaignManagementErrorCode.AppInstalGoalAppPlatformNullOrEmpty, ErrorCodes.AppInstallAdAppPlatformNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.InvalidGoalValueType, ErrorCodes.InvalidConversionGoalRevenueType);
            AddMapping(CampaignManagementErrorCode.EventGoalExpressionWithOperatorNotPassed, ErrorCodes.EventGoalExpressionWithOperatorNotPassed);
            AddMapping(CampaignManagementErrorCode.InvalidConversionCountType, ErrorCodes.InvalidAppInstallGoalCountType);
            AddMapping(CampaignManagementErrorCode.InvalidAppInstallGoalScope, ErrorCodes.InvalidAppInstallGoalScope);
            AddMapping(CampaignManagementErrorCode.GoalEntityTypeNotInTypeFilter, ErrorCodes.ConversionGoalTypesDoNotMatchExistingValue);
            AddMapping(CampaignManagementErrorCode.InvalidGoalEntityType, ErrorCodes.ConversionGoalTypeNotMatched);
            AddMapping(CampaignManagementErrorCode.CampaignLanguagesNotEnabledForCustomer, ErrorCodes.CampaignLanguagesNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.AllCampaignLanguagesCannotBeRemoved, ErrorCodes.AllCampaignLanguagesCannotBeRemoved);
            AddMapping(CampaignManagementErrorCode.CampaignLanguageSpecifiedMoreThanOnce, ErrorCodes.CampaignLanguageSpecifiedMoreThanOnce);
            AddMapping(CampaignManagementErrorCode.InvalidGoalValueCurrencyCode, ErrorCodes.InvalidGoalCurrencyCode);
            AddMapping(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldBeNull, ErrorCodes.GoalCurrencyCodeIsNotNull);
            AddMapping(CampaignManagementErrorCode.LanguageUpdateNotAllowed, ErrorCodes.LanguageUpdateNotAllowed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForLocalInventoryAds, ErrorCodes.CustomerNotEnabledForLocalInventoryAds);
            AddMapping(CampaignManagementErrorCode.GoalValueCurrencyCodeShouldNotBeNull, ErrorCodes.GoalCurrencyCodeIsNull);
            AddMapping(CampaignManagementErrorCode.GoalTypeCannotBeChanged, ErrorCodes.GoalTypeCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.InStoreTransactionGoalShouldBeAcrossAllAccounts, ErrorCodes.InStoreTransactionGoalScopeInvalid);
            AddMapping(CampaignManagementErrorCode.OnlyOneInStoreTransactionGoalBeAllowedPerCustomer, ErrorCodes.OnlyOneInStoreTransactionGoalAllowedPerCustomer);
            AddMapping(CampaignManagementErrorCode.InStoreTransactionPilotNotEnabledForCustomer, ErrorCodes.InStoreTransactionPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.InvalidOfflineConversionGoalScope, ErrorCodes.CustomerScopeNotSupportedForConversionGoalType);
            AddMapping(CampaignManagementErrorCode.CannotUpdateAssociationStatusDueToTagNotAvailable, ErrorCodes.CannotUpdateCriterionStatusDueToTagNotAvailable);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForExternalAttribution, ErrorCodes.CustomerNotEligibleForExternalAttribution);
            AddMapping(CampaignManagementErrorCode.AttributionModelTypeCannotBeUpdated, ErrorCodes.IsExternallyAttributedCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForShoppableAds, ErrorCodes.AccountNotEnabledForShoppableAds);
            AddMapping(CampaignManagementErrorCode.GoalLevelCannotBeChanged, ErrorCodes.GoalLevelCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.GoalLevelCannotBeDowngraded, ErrorCodes.GoalLevelCannotBeDowngraded);

            // Offline conversion
            AddMapping(CampaignManagementErrorCode.OfflineConversionMSClickIdNullOrEmpty, ErrorCodes.OfflineConversionMicrosoftClickIdNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OfflineConversionMSClickIdInvalid, ErrorCodes.OfflineConversionMicrosoftClickIdInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionNameNullOrEmpty, ErrorCodes.OfflineConversionNameNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OfflineConversionNameInvalid, ErrorCodes.OfflineConversionNameInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionTimeInvalid, ErrorCodes.OfflineConversionTimeInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionTimeNullOrEmpty, ErrorCodes.OfflineConversionTimeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OfflineConversionTimeOutOfWindow, ErrorCodes.OfflineConversionTimeOutOfWindow);
            AddMapping(CampaignManagementErrorCode.OfflineConversionValueInvalid, ErrorCodes.OfflineConversionValueInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionCurrencyCodeInvalid, ErrorCodes.OfflineConversionCurrencyCodeInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionsNullOrEmpty, ErrorCodes.OfflineConversionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OfflineConversionIsNull, ErrorCodes.OfflineConversionIsNull);
            AddMapping(CampaignManagementErrorCode.OfflineConversionGServerError, ErrorCodes.OfflineConversionsApplyFailed);
            AddMapping(CampaignManagementErrorCode.OfflineConversionBatchSizeExceedsLimit, ErrorCodes.OfflineConversionsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.FutureConversionTimeCannotBeSet, ErrorCodes.FutureConversionTimeCannotBeSet);
            AddMapping(CampaignManagementErrorCode.OfflineConversionGoalTooRecent, ErrorCodes.OfflineConversionNotAcceptedForGoal);
            AddMapping(CampaignManagementErrorCode.ConversionTimeEarlierThanClickTime, ErrorCodes.ConversionTimeEarlierThanClickTime);
            AddMapping(CampaignManagementErrorCode.ClickIdDateTimeOutsideGoalConversionWindow, ErrorCodes.ClickIdDateTimeOutsideGoalConversionWindow);
            AddMapping(CampaignManagementErrorCode.OfflineConversionInvalidAdjustmentType, ErrorCodes.OfflineConversionInvalidAdjustmentType);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentTimeNullOrEmpty, ErrorCodes.OfflineConversionAdjustmentTimeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentTimeEarlierThanConversionTime, ErrorCodes.OfflineConversionAdjustmentTimeEarlierThanConversionTime);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentTimeInvalid, ErrorCodes.OfflineConversionAdjustmentTimeInvalid);
            AddMapping(CampaignManagementErrorCode.OfflineConversionFutureAdjustmentTimeCannotBeSet, ErrorCodes.OfflineConversionFutureAdjustmentTimeCannotBeSet);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentTimeOutOfWindow, ErrorCodes.OfflineConversionAdjustmentTimeOutOfWindow);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentValueNotExpected, ErrorCodes.OfflineConversionAdjustmentValueNotExpected);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentValueRequired, ErrorCodes.OfflineConversionAdjustmentValueRequired);
            AddMapping(CampaignManagementErrorCode.OfflineConversionCurrencyValueRequired, ErrorCodes.OfflineConversionCurrencyValueRequired);
            AddMapping(CampaignManagementErrorCode.OfflineConversionRestateRetractNotSupported, ErrorCodes.OfflineConversionRestateRetractNotSupported);
            AddMapping(CampaignManagementErrorCode.ScheduledOfflineConversionUploadUnableToFetchFile, ErrorCodes.ScheduledOfflineConversionUploadUnableToFetchFile);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdditionColumnsNotExpectedInHeader, ErrorCodes.OfflineConversionAdditionColumnsNotExpectedInHeader);
            AddMapping(CampaignManagementErrorCode.OfflineConversionAdjustmentColumnsNotExpectedInHeader, ErrorCodes.OfflineConversionAdjustmentColumnsNotExpectedInHeader);
            AddMapping(CampaignManagementErrorCode.ExternalAttributionRequiredFieldEmpty, ErrorCodes.ExternalAttributionRequiredFieldEmpty);
            AddMapping(CampaignManagementErrorCode.ExternalAttributionModelTooLong, ErrorCodes.ExternalAttributionModelTooLong);
            AddMapping(CampaignManagementErrorCode.ExternalAttributionCreditValueInvalid, ErrorCodes.ExternalAttributionCreditValueInvalid);
            AddMapping(CampaignManagementErrorCode.GoalNotEligibleForExternalAttribution, ErrorCodes.GoalNotEligibleForExternalAttribution);
            AddMapping(CampaignManagementErrorCode.NotEligibleForEnhancedConversions, ErrorCodes.NotEligibleForEnhancedConversions);
            AddMapping(CampaignManagementErrorCode.ShouldAcceptTermsBeforeUsingEnhancedConversions, ErrorCodes.ShouldAcceptTermsBeforeUsingEnhancedConversions);
            AddMapping(CampaignManagementErrorCode.ConversionEmailAddressIsNotHashed, ErrorCodes.ConversionEmailAddressIsNotHashed);
            AddMapping(CampaignManagementErrorCode.ConversionPhoneNumberIsNotHashed, ErrorCodes.ConversionPhoneNumberIsNotHashed);

            // Online Conversion
            AddMapping(CampaignManagementErrorCode.OnlineConversionIsNull, ErrorCodes.OnlineConversionIsNull);
            AddMapping(CampaignManagementErrorCode.OnlineConversionAdjustmentValueInvalid, ErrorCodes.OnlineConversionAdjustmentValueInvalid);
            AddMapping(CampaignManagementErrorCode.OnlineConversionCurrencyCodeInvalid, ErrorCodes.OnlineConversionCurrencyCodeInvalid);
            AddMapping(CampaignManagementErrorCode.OnlineConversionAdjustmentTimeInvalid, ErrorCodes.OnlineConversionAdjustmentTimeInvalid);
            AddMapping(CampaignManagementErrorCode.OnlineConversionFutureAdjustmentTimeCannotBeSet, ErrorCodes.OnlineConversionFutureAdjustmentTimeCannotBeSet);
            AddMapping(CampaignManagementErrorCode.OnlineConversionsNullOrEmpty, ErrorCodes.OnlineConversionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OnlineConversionBatchSizeExceedsLimit, ErrorCodes.OnlineConversionBatchSizeExceedsLimit);
            AddMapping(CampaignManagementErrorCode.OnlineConversionAdjustmentValueNotExpected, ErrorCodes.OnlineConversionAdjustmentValueNotExpected);
            AddMapping(CampaignManagementErrorCode.OnlineConversionAdjustmentValueRequired, ErrorCodes.OnlineConversionAdjustmentValueRequired);
            AddMapping(CampaignManagementErrorCode.OnlineConversionAdjustmentTypeIsNull, ErrorCodes.OnlineConversionAdjustmentTypeIsNull);
            AddMapping(CampaignManagementErrorCode.OnlineConversionInvalidAdjustmentType, ErrorCodes.OnlineConversionInvalidAdjustmentType);
            AddMapping(CampaignManagementErrorCode.OnlineConversionNameNullOrEmpty, ErrorCodes.OnlineConversionNameNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.TransactionIdIsNull, ErrorCodes.TransactionIdIsNull);
            AddMapping(CampaignManagementErrorCode.TransactionIdIsInvalid, ErrorCodes.TransactionIdIsInvalid);
            AddMapping(CampaignManagementErrorCode.OnlineConversionInvalidTimeZone, ErrorCodes.OnlineConversionInvalidTimeZone);
            AddMapping(CampaignManagementErrorCode.OnlineConversionNotEnabled, ErrorCodes.OnlineConversionNotEnabled);
            AddMapping(CampaignManagementErrorCode.OnlineConversionNameNotFound, ErrorCodes.OnlineConversionNameNotFound);
            AddMapping(CampaignManagementErrorCode.OnlineConversionHashedEmailAddressNotExpected, ErrorCodes.OnlineConversionHashedEmailAddressNotExpected);
            AddMapping(CampaignManagementErrorCode.OnlineConversionHashedEmailPhoneNumberNotExpected, ErrorCodes.OnlineConversionHashedPhoneNumberNotExpected);

            // Auto Conversion
            AddMapping(CampaignManagementErrorCode.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal, ErrorCodes.SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal);
            AddMapping(CampaignManagementErrorCode.InvalidEventParameterForAutoGoal, ErrorCodes.InvalidEventParameterForAutoGoal);
            AddMapping(CampaignManagementErrorCode.InvalidGoalTypeForAutoGoal, ErrorCodes.InvalidGoalTypeForAutoGoal);
            AddMapping(CampaignManagementErrorCode.IsAutoGoalFieldCannotBeChanged, ErrorCodes.IsAutoGoalFieldCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.EventParameterNotAllowToChangeForAutoGoal, ErrorCodes.EventParameterNotAllowToChangeForAutoGoal);
            AddMapping(CampaignManagementErrorCode.GoalCategoryCannotBeChangedForAutoGoal, ErrorCodes.GoalCategoryCannotBeChangedForAutoGoal);
            AddMapping(CampaignManagementErrorCode.MustHaveCategoryForAutoGoal, ErrorCodes.MustHaveCategoryForAutoGoal);
            AddMapping(CampaignManagementErrorCode.TagNotAllowToChangeForAutoGoal, ErrorCodes.TagNotAllowToChangeForAutoGoal);

            // Final URL suffix
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForFinalUrlSuffixPhase1Pilot, ErrorCodes.CustomerNotEnabledForFinalUrlSuffixPhase1Pilot);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForFinalUrlSuffixPhase2Pilot, ErrorCodes.CustomerNotEnabledForFinalUrlSuffixPhase2Pilot);
            AddMapping(CampaignManagementErrorCode.FinalUrlRequiredWhenUsingFinalUrlSuffix, ErrorCodes.FinalUrlRequiredWhenUsingFinalUrlSuffix);

            // Labels
            AddMapping(CampaignManagementErrorCode.LabelsListIsNullOrEmpty, ErrorCodes.LabelsListIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.LabelsEntityLimitExceeded, ErrorCodes.LabelsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelIsNull, ErrorCodes.LabelIsNull);
            AddMapping(CampaignManagementErrorCode.LabelIdShouldBeNullOnAdd, ErrorCodes.IdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.DuplicateLabelId, ErrorCodes.DuplicateLabelId);
            AddMapping(CampaignManagementErrorCode.LabelIdInvalid, ErrorCodes.LabelIdInvalid);
            AddMapping(CampaignManagementErrorCode.LabelNameInvalid, ErrorCodes.LabelNameInvalid);
            AddMapping(CampaignManagementErrorCode.LabelNameLengthExceeded, ErrorCodes.LabelNameLengthExceeded);
            AddMapping(CampaignManagementErrorCode.LabelNameDuplicate, ErrorCodes.LabelNameDuplicate);
            AddMapping(CampaignManagementErrorCode.LabelDescriptionLengthExceeded, ErrorCodes.LabelDescriptionLengthExceeded);
            AddMapping(CampaignManagementErrorCode.LabelColorCodeInvalid, ErrorCodes.LabelColorCodeInvalid);
            AddMapping(CampaignManagementErrorCode.LabelDescriptionInvalid, ErrorCodes.LabelDescriptionInvalid);
            AddMapping(CampaignManagementErrorCode.LabelPilotNotEnabledForCustomer, ErrorCodes.LabelPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.LabelAssociationListIsNullOrEmpty, ErrorCodes.LabelAssociationListIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.LabelAssociationsBatchLimitExceeded, ErrorCodes.LabelAssociationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelAssociationIsNull, ErrorCodes.LabelAssociationIsNull);
            AddMapping(CampaignManagementErrorCode.DuplicateLabelAssociation, ErrorCodes.DuplicateLabelAssociation);
            AddMapping(CampaignManagementErrorCode.LabelAssociationsPerEntityLimitExceeded, ErrorCodes.LabelAssociationsPerEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelAssociationDoesNotExist, ErrorCodes.LabelAssociationDoesNotExist);
            AddMapping(CampaignManagementErrorCode.LabelCampaignAssociationsAccountLimitExceeded, ErrorCodes.LabelCampaignAssociationsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelAdGroupAssociationsAccountLimitExceeded, ErrorCodes.LabelAdGroupAssociationsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelAdAssociationsAccountLimitExceeded, ErrorCodes.LabelAdAssociationsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelKeywordAssociationsAccountLimitExceeded, ErrorCodes.LabelKeywordAssociationsAccountLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelBatchLimitExceeded, ErrorCodes.LabelBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.LabelAssociationEntityTypeInvalid, ErrorCodes.LabelAssociationEntityTypeInvalid);
            AddMapping(CampaignManagementErrorCode.LabelAssociationEntityIdInvalid, ErrorCodes.LabelAssociationEntityIdInvalid);
            AddMapping(CampaignManagementErrorCode.LoadLabelAssociationsEntityIdInvalid, ErrorCodes.EntityIdInvalid);
            AddMapping(CampaignManagementErrorCode.LabelIdsBatchLimitExceeded, ErrorCodes.LabelIdsBatchLimitExceeded);

            // Price Extension
            AddMapping(CampaignManagementErrorCode.DuplicateHeaders, ErrorCodes.DuplicateHeaders);
            AddMapping(CampaignManagementErrorCode.TooFewPriceTableRows, ErrorCodes.TooFewPriceTableRows);
            AddMapping(CampaignManagementErrorCode.NegativePrice, ErrorCodes.NegativePrice);
            AddMapping(CampaignManagementErrorCode.CurrencyCodeNotSupported, ErrorCodes.CurrencyCodeNotSupported);

            //Account Properties
            AddMapping(CampaignManagementErrorCode.MsClickIdTaggingEnabledValueInvalid, ErrorCodes.MSCLKIDAutoTaggingEnabledValueInvalid);
            AddMapping(CampaignManagementErrorCode.CannotUpdateAccountPropertyByCustomerIdForThisPropertyName, ErrorCodes.CannotUpdateAccountProperty);

            //AIM
            AddMapping(CampaignManagementErrorCode.EntityIdInvalid, ErrorCodes.EntityIdInvalid);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForAudienceCampaign, ErrorCodes.CustomerNotEligibleForAudienceCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignLanguageShouldIncludeAll, ErrorCodes.CampaignLanguageShouldIncludeAll);
            AddMapping(CampaignManagementErrorCode.NotSupportedForThisCampaignType, ErrorCodes.NotSupportedForThisCampaignType);
            AddMapping(CampaignManagementErrorCode.AdGroupLanguageNotSupported, ErrorCodes.AdGroupLanguageNotSupported);
            AddMapping(CampaignManagementErrorCode.AdModificationNotAllowedOnThisCampaign, ErrorCodes.AdModificationNotAllowedOnThisCampaign);
            AddMapping(CampaignManagementErrorCode.InvalidFunctionFormat, ErrorCodes.InvalidFunctionFormat);
            AddMapping(CampaignManagementErrorCode.UnknownFunction, ErrorCodes.UnknownFunction );
            AddMapping(CampaignManagementErrorCode.MissingDelimiterBetweenFunctions, ErrorCodes.MissingDelimiterBetweenFunctions);
            AddMapping(CampaignManagementErrorCode.CountDownInvalidDateTime, ErrorCodes.CountDownInvalidDateTime);
            AddMapping(CampaignManagementErrorCode.CountDownInvalidDaysBefore, ErrorCodes.CountDownInvalidDaysBefore);
            AddMapping(CampaignManagementErrorCode.CountDownDaysBeforeOutOfRange, ErrorCodes.CountDownDaysBeforeOutOfRange);
            AddMapping(CampaignManagementErrorCode.CountDownPastDateTime, ErrorCodes.CountDownPastDateTime);
            AddMapping(CampaignManagementErrorCode.CountDownInvalidDefaultText, ErrorCodes.CountDownInvalidDefaultText);
            AddMapping(CampaignManagementErrorCode.CountDownInvalidLanguageCode, ErrorCodes.CountDownInvalidLanguageCode);
            AddMapping(CampaignManagementErrorCode.CountDownInvalidParameters, ErrorCodes.CountDownInvalidParameters);
            AddMapping(CampaignManagementErrorCode.AdDisplayUrlDomainExtractionFailed, ErrorCodes.AdDisplayUrlDomainExtractionFailed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForProductAudience, ErrorCodes.CustomerNotEligibleForProductAudience);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForEnhancedResponsiveAd, ErrorCodes.CustomerNotEligibleForEnhancedResponsiveAd);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdInvalidImage, ErrorCodes.ResponsiveAdInvalidImage);
            AddMapping(CampaignManagementErrorCode.InvalidImage, ErrorCodes.InvalidImage);
            AddMapping(CampaignManagementErrorCode.DuplicateImage, ErrorCodes.DuplicateImage);
            AddMapping(CampaignManagementErrorCode.RequiredImageMissing, ErrorCodes.RequiredImageMissing);
            AddMapping(CampaignManagementErrorCode.InvalidImageExtensionLayout, ErrorCodes.InvalidImageExtensionLayout);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdDuplicateImage, ErrorCodes.ResponsiveAdDuplicateImage);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdRequiredImageMissing, ErrorCodes.ResponsiveAdRequiredImageMissing);
            AddMapping(CampaignManagementErrorCode.AIMCampaignLevelAudienceTargetingNotEnabled, ErrorCodes.AIMCampaignLevelAudienceTargetingNotEnabled);
            AddMapping(CampaignManagementErrorCode.SearchStringNotSufficient, ErrorCodes.SearchStringNotSufficient);
            AddMapping(CampaignManagementErrorCode.NotEligibleForAudienceCampaignSubType, ErrorCodes.NotEligibleForAudienceCampaignSubType);
            AddMapping(CampaignManagementErrorCode.LocationFunctionInvalidParameters, ErrorCodes.LocationFunctionInvalidParameters);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdInvalidVideo, ErrorCodes.ResponsiveAdInvalidVideo);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoInvalidStatus, ErrorCodes.ResponsiveAdVideoInvalidStatus);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoWidthTooSmall, ErrorCodes.ResponsiveAdVideoWidthTooSmall);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoHeightTooSmall, ErrorCodes.ResponsiveAdVideoHeightTooSmall);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoInvalidAspectRatio, ErrorCodes.ResponsiveAdVideoInvalidAspectRatio);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoInvalidDuration, ErrorCodes.ResponsiveAdVideoInvalidDuration);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoBitRateTooSmall, ErrorCodes.ResponsiveAdVideoBitRateTooSmall);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoSourceLengthTooLarge, ErrorCodes.ResponsiveAdVideoSourceLengthTooLarge);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdVideoUnsupportedFileFormat, ErrorCodes.ResponsiveAdVideoUnsupportedFileFormat);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdDuplicateVideo, ErrorCodes.ResponsiveAdDuplicateVideo);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdRequiredVideoMissing, ErrorCodes.ResponsiveAdRequiredVideoMissing);
            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForAutoBiddingForAudienceNetwork, ErrorCodes.AccountNotEligibleForAutoBiddingForAudienceNetwork);
            AddMapping(CampaignManagementErrorCode.IncludeAutoBiddingViewThroughConversionsValueInvalid, ErrorCodes.IncludeAutoBiddingViewThroughConversionsValueInvalid);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForBusinessAttributes, ErrorCodes.AccountNotEnabledForBusinessAttributes);
            AddMapping(CampaignManagementErrorCode.BusinessAttributesValueInvalid, ErrorCodes.BusinessAttributesValueInvalid);
            AddMapping(CampaignManagementErrorCode.OptOutFromMCMValueInvalid, ErrorCodes.OptOutFromMCMValueInvalid);
            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForOptOutFromMCM, ErrorCodes.AccountNotEligibleForOptOutFromMCM);
            AddMapping(CampaignManagementErrorCode.NetflixTCAcceptedValueInvalid, ErrorCodes.NetflixTCAcceptedValueInvalid);
            AddMapping(CampaignManagementErrorCode.UnsupportedSettingInBrandAwarenessVideoAds, ErrorCodes.UnsupportedSettingInBrandAwarenessVideoAds);
            AddMapping(CampaignManagementErrorCode.InvalidBitRate, ErrorCodes.InvalidBitRate);
            AddMapping(CampaignManagementErrorCode.OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds, ErrorCodes.OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds);
            AddMapping(CampaignManagementErrorCode.InvalidCriterionBidAdjustmentValue, ErrorCodes.InvalidCriterionBidAdjustmentValue);
            AddMapping(CampaignManagementErrorCode.UnsupportedSettingInDisplayAds, ErrorCodes.UnsupportedSettingInDisplayAds);
            AddMapping(CampaignManagementErrorCode.UnsupportedSettingInMultiFormatAds, ErrorCodes.UnsupportedSettingInMultiFormatAds);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForDisplayCampaign, ErrorCodes.AccountNotEnabledForDisplayCampaign);
            AddMapping(CampaignManagementErrorCode.VerifiedTrackingSettingsIsNotAllowed, ErrorCodes.VerifiedTrackingSettingsIsNotAllowed);
            AddMapping(CampaignManagementErrorCode.EmptyVerifiedTrackingSettings, ErrorCodes.EmptyVerifiedTrackingSettings);
            AddMapping(CampaignManagementErrorCode.TooManyItemsInVerifiedTrackingSettings, ErrorCodes.TooManyItemsInVerifiedTrackingSettings);
            AddMapping(CampaignManagementErrorCode.NotEnabledForHTML5Asset, ErrorCodes.NotEnabledForHTML5Asset);


            // Hotel Ads
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForHotelAds, ErrorCodes.CustomerNotEligibleForHotelAds);
            AddMapping(CampaignManagementErrorCode.TooManyValuesInRequest, ErrorCodes.TooManyValues);
            AddMapping(CampaignManagementErrorCode.TooManyValues, ErrorCodes.TooManyValues);
            AddMapping(CampaignManagementErrorCode.DuplicateValues, ErrorCodes.DuplicateValues);
            AddMapping(CampaignManagementErrorCode.InvalidBidMultiplier, ErrorCodes.InvalidBidMultiplier);
            AddMapping(CampaignManagementErrorCode.ImmutableProperty, ErrorCodes.ImmutableProperty);
            AddMapping(CampaignManagementErrorCode.InvalidSubAccount, ErrorCodes.InvalidSubAccount);
            AddMapping(CampaignManagementErrorCode.SubAccountWithNameAlreadyExists, ErrorCodes.EntityAlreadyExists);
            AddMapping(CampaignManagementErrorCode.VerticalCampaignNotExist, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.VerticalCampaignIsDeleted, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.MaxActiveVerticalCampaignsLimitReached, ErrorCodes.MaxActiveSubAccountsLimitReached);
            AddMapping(CampaignManagementErrorCode.VerticalItemNotExist, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.VerticalItemIsDeleted, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.NoDefaultVerticalItemGroupExists, ErrorCodes.NoDefaultHotelGroupExists);
            AddMapping(CampaignManagementErrorCode.VerticalItemGroupNotExist, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.InvalidHotelGroup, ErrorCodes.InvalidHotelGroup);
            AddMapping(CampaignManagementErrorCode.HotelGroupWithNameAlreadyExists, ErrorCodes.EntityAlreadyExists);
            AddMapping(CampaignManagementErrorCode.VerticalItemGroupIsDeleted, ErrorCodes.EntityDoesNotExist);
            AddMapping(CampaignManagementErrorCode.DefaultVerticalItemGroupNotAllowedToUpdate, ErrorCodes.DefaultHotelGroupUpdateNotAllowed);
            AddMapping(CampaignManagementErrorCode.UnsupportedAssociationType, ErrorCodes.UnsupportedAssociationType);
            AddMapping(CampaignManagementErrorCode.DuplicateHotelId, ErrorCodes.DuplicateHotelId);
            AddMapping(CampaignManagementErrorCode.HotelGroupHasActiveAssociations, ErrorCodes.HotelGroupHasActiveAssociations);
            AddMapping(CampaignManagementErrorCode.HotelCampaignNotEnabledForAccount, ErrorCodes.HotelCampaignNotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.MaxPercentCpcLessThanOrEqualToZero, ErrorCodes.MaxPercentCpcLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.MaxPercentCpcGreaterThanOneThousand, ErrorCodes.MaxPercentCpcGreaterThanOneThousand);
            AddMapping(CampaignManagementErrorCode.CommissionRateIsRequired, ErrorCodes.CommissionRateIsRequired);
            AddMapping(CampaignManagementErrorCode.CommissionRateLessThanOrEqualToZero, ErrorCodes.CommissionRateLessThanOrEqualToZero);
            AddMapping(CampaignManagementErrorCode.CommissionRateGreaterThanOneHundred, ErrorCodes.CommissionRateGreaterThanOneHundred);
            AddMapping(CampaignManagementErrorCode.SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed, ErrorCodes.SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed);
            AddMapping(CampaignManagementErrorCode.OnlySupportedBMValueIsDecreaseby100OrIncreaseby0, ErrorCodes.OnlySupportedBMValueIsDecreaseby100OrIncreaseby0);
            AddMapping(CampaignManagementErrorCode.MissingPercentCpcBiddingScheme, ErrorCodes.MissingPercentCpcBiddingScheme);
            AddMapping(CampaignManagementErrorCode.InvalidHotelSetting, ErrorCodes.InvalidHotelSetting);
            AddMapping(CampaignManagementErrorCode.HotelSettingCanNotBeChanged, ErrorCodes.HotelSettingCanNotBeChanged);

            // Feed Management Service
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDsaPageFeed, ErrorCodes.CustomerNotEligibleForDsaPageFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForFeedService, ErrorCodes.CustomerNotEligibleForFeedService);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedAssociation, ErrorCodes.DuplicateFeedAssociation);
            AddMapping(CampaignManagementErrorCode.FeedAssociationDoesNotExists, ErrorCodes.FeedAssociationDoesNotExist);
            AddMapping(CampaignManagementErrorCode.InvalidFeedId, ErrorCodes.InvalidFeedId);
            AddMapping(CampaignManagementErrorCode.FeedAssociationLimitationReached, ErrorCodes.FeedAssociationLimitationReached);
            AddMapping(CampaignManagementErrorCode.AdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed, ErrorCodes.AdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed);
            AddMapping(CampaignManagementErrorCode.InvalidFeedCustomAttributesDefinitionText, ErrorCodes.InvalidFeedCustomAttributesDefinitionText);
            AddMapping(CampaignManagementErrorCode.FeedTypeNotSupportedForBulkUpload, ErrorCodes.FeedTypeNotSupportedForBulkUpload);
            AddMapping(CampaignManagementErrorCode.DualFeedNotSupported, ErrorCodes.DualFeedNotSupported);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedName, ErrorCodes.DuplicateFeedName);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedId, ErrorCodes.DuplicateFeedId);
            AddMapping(CampaignManagementErrorCode.InvalidFeedAttribute, ErrorCodes.InvalidFeedAttribute);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedAttributeName, ErrorCodes.DuplicateFeedAttributeName);
            AddMapping(CampaignManagementErrorCode.InvalidFeedAttributeType, ErrorCodes.InvalidFeedAttributeType);
            AddMapping(CampaignManagementErrorCode.ScheduleNotAllowedForFeedType, ErrorCodes.ScheduleNotAllowedForFeedType);
            AddMapping(CampaignManagementErrorCode.UrlNotAllowedForFeedType, ErrorCodes.UrlNotAllowedForFeedType);
            AddMapping(CampaignManagementErrorCode.InvalidFeedAttributeMapping, ErrorCodes.InvalidFeedAttributeMapping);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedPropertyId, ErrorCodes.DuplicateFeedPropertyId);
            AddMapping(CampaignManagementErrorCode.InvalidFeedType, ErrorCodes.InvalidFeedType);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForAdCustomizersFeed, ErrorCodes.CustomerNotEligibleForAdCustomizersFeed);
            AddMapping(CampaignManagementErrorCode.DuplicateFeedItemId, ErrorCodes.DuplicateFeedItemId);
            AddMapping(CampaignManagementErrorCode.TargetFeedStatusInvalid, ErrorCodes.TargetFeedStatusInvalid);
            AddMapping(CampaignManagementErrorCode.TargetFeedInvalid, ErrorCodes.TargetFeedInvalid);
            AddMapping(CampaignManagementErrorCode.InvalidFeedItemAttributeValue, ErrorCodes.InvalidFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidBooleanFeedItemAttributeValue, ErrorCodes.InvalidBooleanFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidInt64FeedItemAttributeValue, ErrorCodes.InvalidInt64FeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidDoubleFeedItemAttributeValue, ErrorCodes.InvalidDoubleFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidStringFeedItemAttributeValue, ErrorCodes.InvalidStringFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidUrlFeedItemAttributeValue, ErrorCodes.InvalidUrlFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidDateTimeFeedItemAttributeValue, ErrorCodes.InvalidDateTimeFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidInt64ListFeedItemAttributeValue, ErrorCodes.InvalidInt64ListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidDoubleListFeedItemAttributeValue, ErrorCodes.InvalidDoubleListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidStringListFeedItemAttributeValue, ErrorCodes.InvalidStringListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidBooleanListFeedItemAttributeValue, ErrorCodes.InvalidBooleanListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidUrlListFeedItemAttributeValue, ErrorCodes.InvalidUrlListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidDateTimeListFeedItemAttributeValue, ErrorCodes.InvalidDateTimeListFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidPriceFeedItemAttributeValue, ErrorCodes.InvalidPriceFeedItemAttributeValue);
            AddMapping(CampaignManagementErrorCode.FeedItemMaxLimitReached, ErrorCodes.FeedItemMaxLimitReached);
            AddMapping(CampaignManagementErrorCode.InvalidFeedItemId, ErrorCodes.InvalidFeedItemId);
            AddMapping(CampaignManagementErrorCode.AccountLevelFeedLimitationReached, ErrorCodes.AccountLevelFeedLimitationReached);
            AddMapping(CampaignManagementErrorCode.AttributeLimitationPerFeedReached, ErrorCodes.AttributeLimitationPerFeedReached);
            AddMapping(CampaignManagementErrorCode.InvalidPageFeedLabel, ErrorCodes.InvalidPageFeedLabel);
            AddMapping(CampaignManagementErrorCode.TooManyPageFeedLabels, ErrorCodes.TooManyPageFeedLabels);
            AddMapping(CampaignManagementErrorCode.PageFeedLabelTooLong, ErrorCodes.PageFeedLabelTooLong);
            AddMapping(CampaignManagementErrorCode.PageFeedUrlTooLong, ErrorCodes.PageFeedUrlTooLong);
            AddMapping(CampaignManagementErrorCode.InvalidPageFeedUrl, ErrorCodes.InvalidPageFeedUrl);
            AddMapping(CampaignManagementErrorCode.InvalidCustomIdAttributeValue, ErrorCodes.InvalidCustomIdAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidDevicePreferenceAttributeValue, ErrorCodes.InvalidDevicePreferenceAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetAdGroupAttributeValue, ErrorCodes.InvalidTargetAdGroupAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetAudienceIdAttributeValue, ErrorCodes.InvalidTargetAudienceIdAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetCampaignAttributeValue, ErrorCodes.InvalidTargetCampaignAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetKeywordAttributeValue, ErrorCodes.InvalidTargetKeywordAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetKeywordMatchTypeAttributeValue, ErrorCodes.InvalidTargetKeywordMatchTypeAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetKeywordTextAttributeValue, ErrorCodes.InvalidTargetKeywordTextAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetLocationAttributeValue, ErrorCodes.InvalidTargetLocationAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetLocationRestrictionAttributeValue, ErrorCodes.InvalidTargetLocationRestrictionAttributeValue);
            AddMapping(CampaignManagementErrorCode.NestedParameterInCustomAttributeNotSupported, ErrorCodes.NestedParameterInCustomAttributeNotSupported);
            AddMapping(CampaignManagementErrorCode.KeyFeedItemAttributeValueConfliction, ErrorCodes.KeyFeedItemAttributeValueConfliction);
            AddMapping(CampaignManagementErrorCode.CannotUseStandardFeedAttributeName, ErrorCodes.CannotUseStandardFeedAttributeName);
            AddMapping(CampaignManagementErrorCode.CannotUseTargetingFeedAttributeName, ErrorCodes.CannotUseTargetingFeedAttributeName);
            AddMapping(CampaignManagementErrorCode.KeyPropertyCannotBeUpdated, ErrorCodes.KeyPropertyCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.CannotUpdateFeedAttributeKey, ErrorCodes.CannotUpdateFeedAttributeKey);
            AddMapping(CampaignManagementErrorCode.CustomIdAttributeShouldBeOfStringType, ErrorCodes.CustomIdAttributeShouldBeOfStringType);
            AddMapping(CampaignManagementErrorCode.AttributeValueStringTooLong, ErrorCodes.AttributeValueStringTooLong);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataFeed, ErrorCodes.CustomerNotEligibleForDynamicDataFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataAutosAggregateFeed, ErrorCodes.CustomerNotEligibleForDynamicDataAutosAggregateFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataAutosListingFeed, ErrorCodes.CustomerNotEligibleForDynamicDataAutosListingFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataCreditCardsFeed, ErrorCodes.CustomerNotEligibleForDynamicDataCreditCardsFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataCruisesFeed, ErrorCodes.CustomerNotEligibleForDynamicDataCruisesFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataCustomFeed, ErrorCodes.CustomerNotEligibleForDynamicDataCustomFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataEventsFeed, ErrorCodes.CustomerNotEligibleForDynamicDataEventsFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataHealthInsuranceFeed, ErrorCodes.CustomerNotEligibleForDynamicDataHealthInsuranceFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed, ErrorCodes.CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataMortgageLendersFeed, ErrorCodes.CustomerNotEligibleForDynamicDataMortgageLendersFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataProfessionalServiceFeed, ErrorCodes.CustomerNotEligibleForDynamicDataProfessionalServiceFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataToursAndActivitiesFeed, ErrorCodes.CustomerNotEligibleForDynamicDataToursAndActivitiesFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataDebitCardsFeed, ErrorCodes.CustomerNotEligibleForDynamicDataDebitCardsFeed);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDynamicDataJobListingsFeed, ErrorCodes.CustomerNotEligibleForDynamicDataJobListingsFeed);
            AddMapping(CampaignManagementErrorCode.InvalidPageFeedAdTitle, ErrorCodes.InvalidPageFeedAdTitle);
            AddMapping(CampaignManagementErrorCode.PageFeedAdTitleTooLong, ErrorCodes.PageFeedAdTitleTooLong);
            AddMapping(CampaignManagementErrorCode.InvalidFeedIdsForAssociation, ErrorCodes.InvalidFeedIdsForAssociation);
            AddMapping(CampaignManagementErrorCode.FeedItemCountExceedFeedTypeLevelLimitation, ErrorCodes.FeedItemCountExceedFeedTypeLevelLimitation);
            AddMapping(CampaignManagementErrorCode.AttributeValueLengthExceeded, ErrorCodes.AttributeValueLengthExceeded);
            AddMapping(CampaignManagementErrorCode.CustomAttributeValuesEmpty, ErrorCodes.CustomAttributeValuesEmpty);
            AddMapping(CampaignManagementErrorCode.TargetAdgroupWithoutTargetCampaign, ErrorCodes.TargetAdgroupWithoutTargetCampaign);
            AddMapping(CampaignManagementErrorCode.InvalidEndDateAttributeValue, ErrorCodes.InvalidEndDateAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidSchedulingAttributeValue, ErrorCodes.InvalidSchedulingAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidStartDateAttributeValue, ErrorCodes.InvalidStartDateAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidTargetLocationIdAttributeValue, ErrorCodes.InvalidTargetLocationIdAttributeValue);
            AddMapping(CampaignManagementErrorCode.InvalidFeedItemLifeCycleStatus, ErrorCodes.InvalidFeedItemLifeCycleStatus);
            AddMapping(CampaignManagementErrorCode.FeedReferencedInCampaign, ErrorCodes.FeedReferencedInCampaign);
            AddMapping(CampaignManagementErrorCode.FeedReferencedInAd, ErrorCodes.FeedReferencedInAd);
            AddMapping(CampaignManagementErrorCode.PageFeedUrlContainsManualTaggingParameters, ErrorCodes.PageFeedUrlContainsManualTaggingParameters);
            AddMapping(CampaignManagementErrorCode.PageFeedUrlContainsInvalidCharacters, ErrorCodes.PageFeedUrlContainsInvalidCharacters);

            AddMapping(CampaignManagementErrorCode.ExperimentsListIsNullOrEmpty, ErrorCodes.ExperimentsListIsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ExperimentIsNull, ErrorCodes.ExperimentIsNull);
            AddMapping(CampaignManagementErrorCode.ExperimentsEntityLimitExceeded, ErrorCodes.ExperimentsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ExperimentNameIsSameAsBaseCampaignName, ErrorCodes.ExperimentNameIsSameAsBaseCampaignName);
            AddMapping(CampaignManagementErrorCode.ExperimentStartDateGreaterThanEndDate, ErrorCodes.ExperimentStartDateGreaterThanEndDate);
            AddMapping(CampaignManagementErrorCode.ExperimentEndDateLessThanToday, ErrorCodes.ExperimentEndDateLessThanToday);
            AddMapping(CampaignManagementErrorCode.ExperimentStartDateLessThanToday, ErrorCodes.ExperimentStartDateLessThanToday);
            AddMapping(CampaignManagementErrorCode.ExperimentTrafficSplitPercentInvalid, ErrorCodes.ExperimentTrafficSplitPercentInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentNameIsEmpty, ErrorCodes.ExperimentNameIsEmpty);
            AddMapping(CampaignManagementErrorCode.ExperimentIdInvalid, ErrorCodes.ExperimentIdInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicateExperimentIds, ErrorCodes.DuplicateExperimentIds);
            AddMapping(CampaignManagementErrorCode.ExperimentIdListNullOrEmpty, ErrorCodes.ExperimentIdListNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ExperimentTimeperiodOverlapping, ErrorCodes.ExperimentTimeperiodOverlapping);
            AddMapping(CampaignManagementErrorCode.ExperimentBaseCampaignIdIsLocked, ErrorCodes.ExperimentBaseCampaignIdIsLocked);
            AddMapping(CampaignManagementErrorCode.ExperimentCampaignIdInvalid, ErrorCodes.ExperimentCampaignIdInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentsEntityLimitPerCampaignExceeded, ErrorCodes.ExperimentsEntityLimitPerCampaignExceeded);
            AddMapping(CampaignManagementErrorCode.ExperimentPilotNotEnabledForCustomer, ErrorCodes.ExperimentPilotNotEnabledForCustomer);
            AddMapping(CampaignManagementErrorCode.ExperimentStatusInvalid, ErrorCodes.ExperimentStatusInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentBaseCampaignIsExperimentCampaign, ErrorCodes.ExperimentBaseCampaignIsExperimentCampaign);
            AddMapping(CampaignManagementErrorCode.ExperimentStartDateCannotBeChanged, ErrorCodes.ExperimentStartDateCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.ExperimentEndDateCannotBeChanged, ErrorCodes.ExperimentEndDateCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.ExperimentStartDateInvalid, ErrorCodes.ExperimentStartDateInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentBaseCampaignIdInvalid, ErrorCodes.ExperimentBaseCampaignIdInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignOrExperimentWithNameAlreadyExists, ErrorCodes.CampaignOrExperimentWithNameAlreadyExists);
            AddMapping(CampaignManagementErrorCode.BaseCampaignTypeInvalid, ErrorCodes.BaseCampaignTypeInvalid);
            AddMapping(CampaignManagementErrorCode.BaseCampaignBudgetTypeInvalid, ErrorCodes.BaseCampaignBudgetTypeInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentNameTooLong, ErrorCodes.ExperimentNameTooLong);
            AddMapping(CampaignManagementErrorCode.ExperimentNameHasInvalidCharacters, ErrorCodes.ExperimentNameHasInvalidCharacters);
            AddMapping(CampaignManagementErrorCode.ExperimentCampaignInvalid, ErrorCodes.ExperimentCampaignInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentBaseCampaignIdCannotBeChanged, ErrorCodes.ExperimentBaseCampaignIdCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.ExperimentTrafficSplitPercentCannotBeChanged, ErrorCodes.ExperimentTrafficSplitPercentCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.EndedExperimentCannotBeChanged, ErrorCodes.EndedExperimentCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.ExperimentCampaignCannotBeUpdated, ErrorCodes.ExperimentCampaignCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.ExperimentBaseCampaignCannotBeChangedToSharedBudget, ErrorCodes.ExperimentBaseCampaignCannotBeChangedToSharedBudget);
            AddMapping(CampaignManagementErrorCode.ExperimentTypeInvalid, ErrorCodes.ExperimentTypeInvalid);
            AddMapping(CampaignManagementErrorCode.ExperimentTypeCannotBeChanged, ErrorCodes.ExperimentTypeCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.CampaignServiceExperimentDoesNotSupportMixedModeCampaign, ErrorCodes.ExperimentDoesNotSupportMixedModeCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignServiceDSASettingCannotBeAddedToExperimentRelatedCampaign, ErrorCodes.DSASettingCannotBeAddedToExperimentRelatedCampaign);
            AddMapping(CampaignManagementErrorCode.CampaignServiceMaxDSAAutoTargetPerAccountLimitReached, ErrorCodes.MaxDSAAutoTargetPerAccountLimitReached);
            AddMapping(CampaignManagementErrorCode.CampaignServiceDynamicSearchAdCampaignCreationNotAllowed, ErrorCodes.DynamicSearchAdCampaignCreationNotAllowed);
            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForDynamicDescription, ErrorCodes.AccountNotEligibleForDynamicDescription);

            //Responsive Search Ad
            AddMapping(CampaignManagementErrorCode.TextAssetLimitReachedForAccount, ErrorCodes.TextAssetLimitReachedForAccount);
            AddMapping(CampaignManagementErrorCode.TextAssetDoesNotExist, ErrorCodes.TextAssetDoesNotExist);
            AddMapping(CampaignManagementErrorCode.TextAssetsNotPassed, ErrorCodes.TextAssetsNotPassed);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsHeadlinesNullOrEmpty, ErrorCodes.ResponsiveSearchAdHeadlinesNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDuplicateHeadlines, ErrorCodes.ResponsiveSearchAdDuplicateHeadlines);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionsNullOrEmpty, ErrorCodes.ResponsiveSearchAdDescriptionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionsDuplication, ErrorCodes.ResponsiveSearchAdDuplicateDescriptions);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsHeadlinesPinnedFieldMismatch, ErrorCodes.ResponsiveSearchAdHeadlinesPinnedFieldMismatch);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionsPinnedFieldMismatch, ErrorCodes.ResponsiveSearchAdDescriptionsPinnedFieldMismatch);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsInvalidHeadline, ErrorCodes.ResponsiveSearchAdInvalidHeadline);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsInvalidDescription, ErrorCodes.ResponsiveSearchAdInvalidDescription);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsInvalidPinnedField, ErrorCodes.ResponsiveSearchAdPinnedFieldInvalid);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsHeadlineTooLong, ErrorCodes.ResponsiveSearchAdHeadlineTooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionTooLong, ErrorCodes.ResponsiveSearchAdDescriptionTooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsHeadlinesMaxCountExceeded, ErrorCodes.ResponsiveSearchAdHeadlinesMaxCountExceeded);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionsMaxCountExceeded, ErrorCodes.ResponsiveSearchAdDescriptionsMaxCountExceeded);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsHeadlinesLessThanMinRequired, ErrorCodes.ResponsiveSearchAdHeadlinesLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdNotEnoughHeadlinesForPositions, ErrorCodes.ResponsiveSearchAdHeadlinesLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsDescriptionsLessThanMinRequired, ErrorCodes.ResponsiveSearchAdDescriptionsLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdNotEnoughDescriptionsForPositions, ErrorCodes.ResponsiveSearchAdDescriptionsLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdDefaultTextRequiredForKeywordParameter, ErrorCodes.ResponsiveSearchAdDefaultTextRequiredForKeyword);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdsBothCountDownAndGlobalCountDown, ErrorCodes.ResponsiveSearchAdsBothCountDownAndGlobalCountDown);

            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdPath1Invalid, ErrorCodes.ResponsiveSearchAdPath1Invalid);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdPath1TooLong, ErrorCodes.ResponsiveSearchAdPath1TooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdPath2Invalid, ErrorCodes.ResponsiveSearchAdPath2Invalid);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdPath2TooLong, ErrorCodes.ResponsiveSearchAdPath2TooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdPath2SetWithoutPath1, ErrorCodes.ResponsiveSearchAdPath2SetWithoutPath1);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdFinalUrlDomainTooLong, ErrorCodes.ResponsiveSearchAdFinalUrlDomainTooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdDisplayUrlDomainTooLong, ErrorCodes.ResponsiveSearchAdDisplayUrlDomainTooLong);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdDisplayUrlDomainInvalid, ErrorCodes.ResponsiveSearchAdDisplayUrlDomainInvalid);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdDisplayUrlDomainExtractionFailed, ErrorCodes.AdDisplayUrlDomainExtractionFailed);

            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdInvalidFunctionFormat, ErrorCodes.InvalidFunctionFormat);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdUnknownFunction, ErrorCodes.UnknownFunction);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdMissingDelimiterBetweenFunctions, ErrorCodes.MissingDelimiterBetweenFunctions);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownInvalidDateTime, ErrorCodes.CountDownInvalidDateTime);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownInvalidLanguageCode, ErrorCodes.CountDownInvalidLanguageCode);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownInvalidDaysBefore, ErrorCodes.CountDownInvalidDaysBefore);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownDaysBeforeOutOfRange, ErrorCodes.CountDownDaysBeforeOutOfRange);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownInvalidParameters, ErrorCodes.CountDownInvalidParameters);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownPastDateTime, ErrorCodes.CountDownPastDateTime);
            AddMapping(CampaignManagementErrorCode.ResponsiveSearchAdCountdownInvalidDefaultText, ErrorCodes.CountDownInvalidDefaultText);
            AddMapping(CampaignManagementErrorCode.AdCustomizerNotSupportedForAdType, ErrorCodes.AdCustomizerNotSupportedForAdType);
            AddMapping(CampaignManagementErrorCode.MaxRSAAdsLimitReachedInAdGroup, ErrorCodes.MaxActiveResponsiveSearchAdsPerAdgroupLimitReached);
            AddMapping(CampaignManagementErrorCode.MaxMMAAdsLimitReachedInAdGroup, ErrorCodes.MaxActiveMultiMediaAdsPerAdgroupLimitReached);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdDefaultTextRequiredForKeywordParameter, ErrorCodes.ResponsiveAdDefaultTextRequiredForKeyword);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdHeadlinesLessThanMinRequired, ErrorCodes.ResponsiveAdHeadlinesLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdLongHeadlinesLessThanMinRequired, ErrorCodes.ResponsiveAdLongHeadlinesLessThanMinRequired);
            AddMapping(CampaignManagementErrorCode.ResponsiveAdDescriptionsLessThanMinRequired, ErrorCodes.ResponsiveAdDescriptionsLessThanMinRequired); 
            AddMapping(CampaignManagementErrorCode.ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed, ErrorCodes.ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed);
            AddMapping(CampaignManagementErrorCode.AttributeNameDoesNotExist, ErrorCodes.AttributeNameDoesNotExist);
            AddMapping(CampaignManagementErrorCode.FetchAttributesFailed, ErrorCodes.FetchAttributesFailed);
            AddMapping(CampaignManagementErrorCode.SystemGeneratedAssetNotAllowed, ErrorCodes.SystemGeneratedAssetNotAllowed);
            AddMapping(CampaignManagementErrorCode.ImageDoesntMeetMinPixelRequirements, ErrorCodes.ImageDoesntMeetMinPixelRequirements);
            AddMapping(CampaignManagementErrorCode.ImageDoesntFitAspectRatio, ErrorCodes.ImageDoesntFitAspectRatio);
			AddMapping(CampaignManagementErrorCode.AssetCropSettingInvalid, ErrorCodes.AssetCropSettingInvalid);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForRSAAutoGeneratedAssets, ErrorCodes.AccountNotEnabledForRSAAutoGeneratedAssets);
            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForVanityPharmaWebsiteDescriptions, ErrorCodes.CustomerNotEnabledForVanityPharmaWebsiteDescriptions);
            AddMapping(CampaignManagementErrorCode.InvalidWebsiteDescriptionForDisplayUrlMode, ErrorCodes.InvalidWebsiteDescriptionForDisplayUrlMode);

            //Feed
            AddMapping(CampaignManagementErrorCode.FeedItemScheduleInvalidStartTime, ErrorCodes.FeedItemScheduleInvalidStartTime);
            AddMapping(CampaignManagementErrorCode.FeedItemScheduleInvalidEndTime, ErrorCodes.FeedItemScheduleInvalidEndTime);

            //CustomerListItem
            AddMapping(CampaignManagementErrorCode.InvalidCustomerListId, ErrorCodes.InvalidCustomerListId);
            AddMapping(CampaignManagementErrorCode.TermsAndConditionsNotAccepted, ErrorCodes.TermsAndConditionsNotAccepted);

            //CombinedList
            AddMapping(CampaignManagementErrorCode.AudienceSetsIsEmpty, ErrorCodes.CombinationRulesNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AudienceSetsIsTooLarge, ErrorCodes.CombinationRulesExceedsLimit);
            AddMapping(CampaignManagementErrorCode.NoAudienceSelected, ErrorCodes.NoAudienceSelected);
            AddMapping(CampaignManagementErrorCode.InvalidAudienceSetOperator, ErrorCodes.InvalidCombinationRuleOperator);
            AddMapping(CampaignManagementErrorCode.AllNOTOperatorIsNotAllowed, ErrorCodes.OnlyNotCombinationUnsupported);
            AddMapping(CampaignManagementErrorCode.AudienceTypeIsNotSupportedForANDOperator, ErrorCodes.AndCombinationUnsupportedForAudienceType);
            AddMapping(CampaignManagementErrorCode.AudienceTypeIsNotSupportedForOROperator, ErrorCodes.OrCombinationUnsupportedForAudienceType);
            AddMapping(CampaignManagementErrorCode.AudienceTypeIsNotSupportedForNOTOperator, ErrorCodes.NotCombinationUnsupportedForAudienceType);
            AddMapping(CampaignManagementErrorCode.SimilarAudienceCanOnlyBeInSingleORSet, ErrorCodes.SimilarAudienceCanOnlyBeInSingleORSet);
            AddMapping(CampaignManagementErrorCode.CustomerListsCanOnlyBeCombinedWithOtherCustomerLists, ErrorCodes.CustomerListsCanOnlyBeCombinedWithOtherCustomerLists);
            AddMapping(CampaignManagementErrorCode.AudienceCannotBeDeletedDueToUsedByCombinedList, ErrorCodes.AudienceUsedByCombinedListCannotBeDeleted);
            AddMapping(CampaignManagementErrorCode.AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList, ErrorCodes.AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList);
            AddMapping(CampaignManagementErrorCode.CombinedListCanOnlyBeEditedByCreator, ErrorCodes.CombinedListCanOnlyBeEditedByOwner);
            AddMapping(CampaignManagementErrorCode.CombinedListCanOnlyBeDeletedByCreator, ErrorCodes.CombinedListCanOnlyBeDeletedByOwner);
            AddMapping(CampaignManagementErrorCode.CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience, ErrorCodes.CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience);
            AddMapping(CampaignManagementErrorCode.CombinedAudienceForLocationNotAllowed, ErrorCodes.CombinedAudienceForLocationNotAllowed);

            // IF Functions
            AddMapping(CampaignManagementErrorCode.IFFunctionCustomerNotInPilot, ErrorCodes.IFFunctionCustomerNotInPilot);
            AddMapping(CampaignManagementErrorCode.IFFunctionIncorrectSyntaxForDevice, ErrorCodes.IFFunctionIncorrectSyntaxForDevice);
            AddMapping(CampaignManagementErrorCode.IFFunctionIncorrectSyntaxForAudience, ErrorCodes.IFFunctionIncorrectSyntaxForAudience);
            AddMapping(CampaignManagementErrorCode.IFFunctionSomeHaveDefaultValueButNotAll, ErrorCodes.IFFunctionSomeHaveDefaultValueButNotAll);
            AddMapping(CampaignManagementErrorCode.IFFunctionInvalidAudience, ErrorCodes.IFFunctionInvalidAudience);
            AddMapping(CampaignManagementErrorCode.IFFunctionDuplicateAudiences, ErrorCodes.IFFunctionDuplicateAudiences);
            AddMapping(CampaignManagementErrorCode.IFFunctionSpecialCharactersAreNotEscaped, ErrorCodes.IFFunctionSpecialCharactersAreNotEscaped);
            AddMapping(CampaignManagementErrorCode.IFFunctionNestingNotAllowed, ErrorCodes.IFFunctionNestingNotAllowed);
            AddMapping(CampaignManagementErrorCode.IFFunctionSpecialCharactersNotAllowed, ErrorCodes.IFFunctionSpecialCharactersNotAllowed);
            AddMapping(CampaignManagementErrorCode.IFFunctionInvalidSyntax, ErrorCodes.IFFunctionInvalidSyntax);
            AddMapping(CampaignManagementErrorCode.IFFunctionNumAudiencesExceedsMaxForAd, ErrorCodes.IFFunctionNumAudiencesExceedsMaxForAd);
            AddMapping(CampaignManagementErrorCode.IFFunctionAudiencesExceedsMaxFieldLength, ErrorCodes.IFFunctionAudiencesExceedsMaxFieldLength);
            AddMapping(CampaignManagementErrorCode.IFFunctionErrorGettingAudiences, ErrorCodes.IFFunctionErrorGettingAudiences);

            AddMapping(CampaignManagementErrorCode.CustomerNotInLanguagePilot, ErrorCodes.CustomerNotInLanguagePilot);

            //Disclaimer Ads
            AddMapping(CampaignManagementErrorCode.BulkUploadNotSupportedForDisclaimerAds, ErrorCodes.BulkUploadNotSupportedForDisclaimerAds);
            AddMapping(CampaignManagementErrorCode.DisclaimerSettingCannotBeUpdated, ErrorCodes.DisclaimerSettingCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForDisclaimerAds, ErrorCodes.CustomerNotEligibleForDisclaimerAds);
            AddMapping(CampaignManagementErrorCode.DisclaimerLayoutMissing, ErrorCodes.DisclaimerLayoutMissing);
            AddMapping(CampaignManagementErrorCode.InvalidDisclaimerLayout, ErrorCodes.InvalidDisclaimerLayout);
            AddMapping(CampaignManagementErrorCode.NullOrEmptyDisclaimerPopupText, ErrorCodes.NullOrEmptyDisclaimerPopupText);
            AddMapping(CampaignManagementErrorCode.DisclaimerLineTextShouldbeEmpty, ErrorCodes.DisclaimerLineTextShouldbeEmpty);
            AddMapping(CampaignManagementErrorCode.NullOrEmptyDisclaimerLineText, ErrorCodes.NullOrEmptyDisclaimerLineText);
            AddMapping(CampaignManagementErrorCode.DisclaimerPopupTextShouldbeEmpty, ErrorCodes.DisclaimerPopupTextShouldbeEmpty);
            AddMapping(CampaignManagementErrorCode.DisclaimerFinalUrlMissing, ErrorCodes.DisclaimerFinalUrlMissing);
            AddMapping(CampaignManagementErrorCode.OnlyOneDisclaimerFinalUrlIsAllowed, ErrorCodes.OnlyOneDisclaimerFinalUrlIsAllowed);
            AddMapping(CampaignManagementErrorCode.OnlyOneDisclaimerFinalMobileUrlIsAllowed, ErrorCodes.OnlyOneDisclaimerFinalMobileUrlIsAllowed);
            AddMapping(CampaignManagementErrorCode.DisclaimerTitleNotAllowedForLineText, ErrorCodes.DisclaimerTitleNotAllowedForLineText);
            AddMapping(CampaignManagementErrorCode.EntityOnlyAllowedForDisclaimerCampaign, ErrorCodes.EntityOnlyAllowedForDisclaimerCampaign);
            AddMapping(CampaignManagementErrorCode.AppInstallAdNotSupportedForDisclaimerCampaign, ErrorCodes.AppInstallAdNotSupportedForDisclaimerCampaign);
            AddMapping(CampaignManagementErrorCode.ExperimentNotSupportedForDisclaimerCampaign, ErrorCodes.ExperimentNotSupportedForDisclaimerCampaign);
            AddMapping(CampaignManagementErrorCode.AdTypeNotSupportedForDisclaimerCampaign, ErrorCodes.AdTypeNotSupportedForDisclaimerCampaign);
            AddMapping(CampaignManagementErrorCode.DisclaimerTitleIsRequiredForPopupText, ErrorCodes.DisclaimerTitleIsRequiredForPopupText);
            AddMapping(CampaignManagementErrorCode.InvalidDisclaimerTitle, ErrorCodes.InvalidDisclaimerTitle);

            // Exclusion Lists
            AddMapping(CampaignManagementErrorCode.NotInPilotForManagerAccountSharedWebsiteExclusions, ErrorCodes.NotInPilotForManagerAccountSharedWebsiteExclusions);
            AddMapping(CampaignManagementErrorCode.DuplicateSharedListName, ErrorCodes.DuplicateSharedListName);
            AddMapping(CampaignManagementErrorCode.MaxListItemLimitExceededForList, ErrorCodes.MaxListItemLimitExceededForList);
            AddMapping(CampaignManagementErrorCode.DuplicateListItemInList, ErrorCodes.DuplicateListItemInList);
            AddMapping(CampaignManagementErrorCode.InvalidListItemTypeForList, ErrorCodes.InvalidListItemTypeForList);
            AddMapping(CampaignManagementErrorCode.SharedEntitiesWithActiveAssociationsCannotBeDeleted, ErrorCodes.SharedEntitiesWithActiveAssociationsCannotBeDeleted);
            AddMapping(CampaignManagementErrorCode.SharedListItemNotInList, ErrorCodes.SharedListItemNotInList);
            AddMapping(CampaignManagementErrorCode.SharedListItemBatchLimitExceeded, ErrorCodes.SharedListItemBatchLimitExceeded);

            AddMapping(CampaignManagementErrorCode.CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid, ErrorCodes.CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignSubType, ErrorCodes.InvalidCampaignSubType);

            // Promotion AdExtensions
            AddMapping(CampaignManagementErrorCode.PromotionValueNotSet, ErrorCodes.PromotionValueNotSet);
            AddMapping(CampaignManagementErrorCode.PromotionPercentAndMoneyValueSet, ErrorCodes.PromotionPercentAndMoneyValueSet);
            AddMapping(CampaignManagementErrorCode.PromotionOrdersOverAndPromoCodeSet, ErrorCodes.PromotionOrdersOverAndPromoCodeSet);
            AddMapping(CampaignManagementErrorCode.PromotionValueNegative, ErrorCodes.PromotionValueNegative);
            AddMapping(CampaignManagementErrorCode.PromotionOrdersOverNegative, ErrorCodes.PromotionOrdersOverNegative);
            AddMapping(CampaignManagementErrorCode.PromotionDatesInvalid, ErrorCodes.PromotionDatesInvalid);
            AddMapping(CampaignManagementErrorCode.CurrencyCodeSetWithoutMonetaryValue, ErrorCodes.CurrencyCodeSetWithoutMonetaryValue);
            AddMapping(CampaignManagementErrorCode.AdScheduleTimeZoneSettingNotInPilot, ErrorCodes.AdScheduleTimeZoneSettingNotInPilot);

            //FilterLink AdExtensions
            AddMapping(CampaignManagementErrorCode.InvalidFilterLinkTextCharacter, ErrorCodes.InvalidFilterLinkTextCharacter);
            AddMapping(CampaignManagementErrorCode.TooFewFilterLinkText, ErrorCodes.TooFewFilterLinkText);
            AddMapping(CampaignManagementErrorCode.TooManyFilterLinkText, ErrorCodes.TooManyFilterLinkText);
            AddMapping(CampaignManagementErrorCode.FinalUrlandTextNotMatch, ErrorCodes.FinalUrlandTextNotMatch);
            AddMapping(CampaignManagementErrorCode.EmptyElementInListNotAllowed, ErrorCodes.EmptyElementInListNotAllowed);

            AddMapping(CampaignManagementErrorCode.KeywordSubstitutionNotSupported, ErrorCodes.KeywordSubstitutionNotSupported);

            AddMapping(CampaignManagementErrorCode.InvalidGoalCategory, ErrorCodes.InvalidGoalCategory);
            AddMapping(CampaignManagementErrorCode.InvalidRegularExpression, ErrorCodes.InvalidRegularExpression);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForGoalCategory, ErrorCodes.CustomerNotEligibleForGoalCategory);
            AddMapping(CampaignManagementErrorCode.InvalidCategoryForGoalType, ErrorCodes.InvalidCategoryForGoalType);

            //Flyer extension
            AddMapping(CampaignManagementErrorCode.FlyerExtensionInvalidAssetId, ErrorCodes.FlyerExtensionInvalidAssetId);
            AddMapping(CampaignManagementErrorCode.FlyerAdExtensionsAssetLimitExceeded, ErrorCodes.FlyerAdExtensionsAssetLimitExceeded);
            AddMapping(CampaignManagementErrorCode.FlyerExtensionImageTooSmall, ErrorCodes.FlyerExtensionImageTooSmall);
            AddMapping(CampaignManagementErrorCode.FlyerExtensionInvalidStoreId, ErrorCodes.FlyerExtensionInvalidStoreId);
            AddMapping(CampaignManagementErrorCode.FlyerExtensionSchedulingStartAndEndDateRequired, ErrorCodes.FlyerExtensionSchedulingStartAndEndDateRequired);
            AddMapping(CampaignManagementErrorCode.FlyerExtensionEndDateRangeExceeded, ErrorCodes.FlyerExtensionEndDateRangeExceeded);
            AddMapping(CampaignManagementErrorCode.FlyerExtensionStoreIdCannotBeModified, ErrorCodes.FlyerExtensionStoreIdCannotBeModified);

            //Video
            AddMapping(CampaignManagementErrorCode.VideoSourceIsNull, ErrorCodes.VideoSourceIsNull);
            AddMapping(CampaignManagementErrorCode.VideoUrlDataIsNull, ErrorCodes.VideoUrlDataIsNull);
            AddMapping(CampaignManagementErrorCode.VideoDescriptionDataIsNull, ErrorCodes.VideoDescriptionDataIsNull);
            AddMapping(CampaignManagementErrorCode.VideoUrlTextTooLong, ErrorCodes.VideoUrlTextTooLong);
            AddMapping(CampaignManagementErrorCode.VideoDescriptionTextTooLong, ErrorCodes.VideoDescriptionTextTooLong);
            AddMapping(CampaignManagementErrorCode.VideoSourceLimitExceeded, ErrorCodes.VideoSourceLimitExceeded);
            AddMapping(CampaignManagementErrorCode.VideoLimitExceededPerAccount, ErrorCodes.VideoLimitExceededPerAccount);
            AddMapping(CampaignManagementErrorCode.VideoScanTypeNotSupported, ErrorCodes.VideoScanTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.InvalidFrameRate, ErrorCodes.InvalidFrameRate);
            AddMapping(CampaignManagementErrorCode.OnlySingleTrackSupported, ErrorCodes.OnlySingleTrackSupported);
            AddMapping(CampaignManagementErrorCode.VideoAudioDurationMustMatch, ErrorCodes.VideoAudioDurationMustMatch);
            AddMapping(CampaignManagementErrorCode.ChromaSubsamplingNotSupported, ErrorCodes.ChromaSubsamplingNotSupported);
            AddMapping(CampaignManagementErrorCode.UnsupportedNumberOfAudioChannels, ErrorCodes.UnsupportedNumberOfAudioChannels);
            AddMapping(CampaignManagementErrorCode.VideoProfileNotSupported, ErrorCodes.VideoProfileNotSupported);
            AddMapping(CampaignManagementErrorCode.InvalidAudioBitRate, ErrorCodes.InvalidAudioBitRate);
            AddMapping(CampaignManagementErrorCode.UnsupportedAudioBitDepth, ErrorCodes.UnsupportedAudioBitDepth);
            AddMapping(CampaignManagementErrorCode.UnsupportedAudioSampleRate, ErrorCodes.UnsupportedAudioSampleRate);
            AddMapping(CampaignManagementErrorCode.UnsupportedFrameRateMode, ErrorCodes.UnsupportedFrameRateMode);

            // Portfolio Bid Strategy
            AddMapping(CampaignManagementErrorCode.DuplicatePortfolioBidStrategyName, ErrorCodes.DuplicatePortfolioBidStrategyName);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyEntityLimitExceeded, ErrorCodes.PortfolioBidStrategyEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyNameTooLong, ErrorCodes.PortfolioBidStrategyNameTooLong);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyNameMissing, ErrorCodes.PortfolioBidStrategyNameMissing);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyNameHasInvalidChars, ErrorCodes.PortfolioBidStrategyNameHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategiesAreNullOrEmpty, ErrorCodes.PortfolioBidStrategiesAreNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyOperationsBatchLimitExceeded, ErrorCodes.PortfolioBidStrategyOperationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyIsNull, ErrorCodes.PortfolioBidStrategyIsNull);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyIdShouldBeNullOnAdd, ErrorCodes.PortfolioBidStrategyIdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyIdInvalid, ErrorCodes.PortfolioBidStrategyIdInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicatePortfolioBidStrategyId, ErrorCodes.DuplicatePortfolioBidStrategyId);
            AddMapping(CampaignManagementErrorCode.BidStrategyTypeCannotBeNullOnAdd, ErrorCodes.BidStrategyTypeCannotBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyIsAssociatedWithActiveCampaigns, ErrorCodes.PortfolioBidStrategyIsAssociatedWithActiveCampaigns);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyTypeCannotBeChanged, ErrorCodes.PortfolioBidStrategyTypeCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.AccountNotInPilotForPortfolioBidStrategy, ErrorCodes.AccountNotInPilotForPortfolioBidStrategy);
            AddMapping(CampaignManagementErrorCode.UnsupportedBidStrategyTypeForPortfolioBidStrategy, ErrorCodes.UnsupportedBidStrategyTypeForPortfolioBidStrategy);
            AddMapping(CampaignManagementErrorCode.CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity, ErrorCodes.CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity);
            AddMapping(CampaignManagementErrorCode.UnsupportedCampaignTypeForPortfolioBidStrategy, ErrorCodes.UnsupportedCampaignTypeForPortfolioBidStrategy);
            AddMapping(CampaignManagementErrorCode.CampaignTypeAndBidStrategyTypeAreMutuallyExclusive, ErrorCodes.CampaignTypeAndBidStrategyTypeAreMutuallyExclusive);
            AddMapping(CampaignManagementErrorCode.PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged, ErrorCodes.PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.CampaignTypeNotMatchCurrentPortfolioBidStrategy, ErrorCodes.CampaignTypeNotMatchCurrentPortfolioBidStrategy);
            AddMapping(CampaignManagementErrorCode.CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid, ErrorCodes.CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid);

            // Video Ads
            AddMapping(CampaignManagementErrorCode.CampaignSubtypeNotAllowedInCampaignType, ErrorCodes.CampaignSubtypeNotAllowedInCampaignType);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForVideoCampaign, ErrorCodes.AccountNotEnabledForVideoCampaign);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForCustomVideoAssetThumbnail, ErrorCodes.AccountNotEnabledForCustomVideoAssetThumbnail);
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForMultiFormatAds, ErrorCodes.AccountNotEnabledForMultiFormatAds);
            AddMapping(CampaignManagementErrorCode.AdSubTypeCannotBeChanged, ErrorCodes.AdSubTypeCannotBeChanged);

            //Video extension
            AddMapping(CampaignManagementErrorCode.VideoExtensionThumbnailRequired, ErrorCodes.VideoExtensionThumbnailRequired);
            AddMapping(CampaignManagementErrorCode.VideoExtensionInvalidImageFormat, ErrorCodes.VideoExtensionInvalidImageFormat);
            AddMapping(CampaignManagementErrorCode.VideoExtensionThumbnailTooSmall, ErrorCodes.VideoExtensionThumbnailTooSmall);
            AddMapping(CampaignManagementErrorCode.VideoExtensionInvalidAspectRatio, ErrorCodes.VideoExtensionInvalidAspectRatio);
            AddMapping(CampaignManagementErrorCode.VideoExtensionInvalidThumbnailId, ErrorCodes.VideoExtensionInvalidThumbnailId);
            AddMapping(CampaignManagementErrorCode.VideoExtensionVideoTooSmall, ErrorCodes.VideoExtensionVideoTooSmall);
            AddMapping(CampaignManagementErrorCode.VideoExtensionInvalidVideoDuration, ErrorCodes.VideoExtensionInvalidVideoDuration);
            AddMapping(CampaignManagementErrorCode.VideoExtensionInvalidVideoId, ErrorCodes.VideoExtensionInvalidVideoId);
            AddMapping(CampaignManagementErrorCode.VideoExtensionThumbnailIdAndUrlSet, ErrorCodes.VideoExtensionThumbnailIdAndUrlSet);
            AddMapping(CampaignManagementErrorCode.VideoExtensionVideoProcessingFailed, ErrorCodes.VideoExtensionVideoProcessingFailed);

            // LeadForm AdExtensions
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionActionNameRequired, ErrorCodes.LeadFormExtensionActionNameRequired);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionConfirmationUrlRequired, ErrorCodes.LeadFormExtensionConfirmationUrlRequired);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionInvalidEmails, ErrorCodes.LeadFormExtensionInvalidEmails);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionInvalidLeadDelivery, ErrorCodes.LeadFormExtensionInvalidLeadDelivery);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionQuestionUpdatesNotSupported, ErrorCodes.LeadFormExtensionQuestionUpdatesNotSupported);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionDuplicateQuestionId, ErrorCodes.LeadFormExtensionDuplicateQuestionId);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionInvalidQuestionId, ErrorCodes.LeadFormExtensionInvalidQuestionId);
            AddMapping(CampaignManagementErrorCode.EntityOnlyAllowedForSearchOrPMaxCampaigns, ErrorCodes.EntityOnlyAllowedForSearchOrPMaxCampaigns);
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionNotFound, ErrorCodes.LeadFormExtensionNotFound);
            AddMapping(CampaignManagementErrorCode.OnlyMaxClicksBiddingSchemeForLeadFormExtension, ErrorCodes.OnlyMaxClicksBiddingSchemeForLeadFormExtension);            
            AddMapping(CampaignManagementErrorCode.LeadFormExtensionMultipleChoiceAnswersNotAllowed, ErrorCodes.LeadFormExtensionMultipleChoiceAnswersNotAllowed);

            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForPersonalizedOffers, ErrorCodes.AccountNotEnabledForPersonalizedOffers);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersCampaignTypeNotSupported, ErrorCodes.PersonalizedOffersCampaignTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersCashbackBudgetInvalid, ErrorCodes.PersonalizedOffersCashbackBudgetInvalid);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersCashbackPercentInvalid, ErrorCodes.PersonalizedOffersCashbackPercentInvalid);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersCampaignBudgetRequired, ErrorCodes.PersonalizedOffersCampaignBudgetRequired);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersAdGroupTypeNotSupported, ErrorCodes.PersonalizedOffersAdGroupTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersScopeNotSupportedForCampaignType, ErrorCodes.PersonalizedOffersScopeNotSupportedForCampaignType);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersCouponsNotSupportedForCampaignType, ErrorCodes.PersonalizedOffersCouponsNotSupportedForCampaignType);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersNotEnabledForCampaign, ErrorCodes.PersonalizedOffersNotEnabledForCampaign);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersSponsoredPromotionsInvalid, ErrorCodes.PersonalizedOffersSponsoredPromotionsInvalid);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersUnsupportedOperation, ErrorCodes.PersonalizedOffersUnsupportedOperation);
            AddMapping(CampaignManagementErrorCode.PersonalizedOffersSponsoredPromotionsOnly, ErrorCodes.PersonalizedOffersSponsoredPromotionsOnly);
            
            // Performance Max
            AddMapping(CampaignManagementErrorCode.PerformanceMaxCampaignsNotEnabledForAccount, ErrorCodes.PerformanceMaxCampaignsNotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.MaxCpcNotSupportedForCampaignType, ErrorCodes.MaxCpcNotSupportedForCampaignType);
            AddMapping(CampaignManagementErrorCode.PerformanceMaxCampaignFinalUrlExpansionOptedOut, ErrorCodes.PerformanceMaxCampaignFinalUrlExpansionOptedOut);
            AddMapping(CampaignManagementErrorCode.DuplicateAudienceGroupName, ErrorCodes.DuplicateAudienceGroupName);
            AddMapping(CampaignManagementErrorCode.AudienceGroupEntityLimitExceeded, ErrorCodes.AudienceGroupEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AudienceGroupIdInvalid, ErrorCodes.AudienceGroupIdInvalid);
            AddMapping(CampaignManagementErrorCode.UnsupportedCampaignTypeForAssetGroup, ErrorCodes.UnsupportedCampaignTypeForAssetGroup);
            AddMapping(CampaignManagementErrorCode.AssetGroupsAreNullOrEmpty, ErrorCodes.AssetGroupsAreNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AssetGroupIsNull, ErrorCodes.AssetGroupIsNull);
            AddMapping(CampaignManagementErrorCode.AssetGroupOperationsBatchLimitExceeded, ErrorCodes.AssetGroupOperationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.Path2SetWithoutPath1, ErrorCodes.Path2SetWithoutPath1);
            AddMapping(CampaignManagementErrorCode.FinalUrlRequired, ErrorCodes.FinalUrlRequired);
            AddMapping(CampaignManagementErrorCode.DomainInvalid, ErrorCodes.DomainInvalid);
            AddMapping(CampaignManagementErrorCode.DomainTooLong, ErrorCodes.DomainTooLong);
            AddMapping(CampaignManagementErrorCode.DomainExtractionFailed, ErrorCodes.DomainExtractionFailed);
            AddMapping(CampaignManagementErrorCode.AssetFieldWithRequiredLengthMinimumNotMet, ErrorCodes.AssetFieldWithRequiredLengthMinimumNotMet);
            AddMapping(CampaignManagementErrorCode.AssetFieldMinimumPerSubTypeNotMet, ErrorCodes.AssetFieldMinimumPerSubTypeNotMet);
            AddMapping(CampaignManagementErrorCode.AssetFieldLimitPerSubTypeExceeded, ErrorCodes.AssetFieldLimitPerSubTypeExceeded);
            AddMapping(CampaignManagementErrorCode.StartDateLessThanCurrentDate, ErrorCodes.StartDateLessThanCurrentDate);
            AddMapping(CampaignManagementErrorCode.EndDateLessThanStartDate, ErrorCodes.EndDateLessThanStartDate);
            AddMapping(CampaignManagementErrorCode.StartDateCannotBeChanged, ErrorCodes.StartDateCannotBeChanged);
            AddMapping(CampaignManagementErrorCode.AssetGroupAudienceGroupAssociationDuplicate, ErrorCodes.AssetGroupAudienceGroupAssociationDuplicate);
            AddMapping(CampaignManagementErrorCode.AssetGroupAudienceGroupAssociationDoesNotExist, ErrorCodes.AssetGroupAudienceGroupAssociationDoesNotExist);
            AddMapping(CampaignManagementErrorCode.OperationsForTooManyAssetGroups, ErrorCodes.OperationsForTooManyAssetGroups);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingGroupsEntityLimitExceeded, ErrorCodes.AssetGroupListingGroupsEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingGroupIsNull, ErrorCodes.AssetGroupListingGroupIsNull);
            AddMapping(CampaignManagementErrorCode.DuplicateAssetGroupListingGroupIds, ErrorCodes.DuplicateAssetGroupListingGroupIds);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingGroupActionIsNull, ErrorCodes.AssetGroupListingGroupActionIsNull);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingGroupActionsNullOrEmpty, ErrorCodes.AssetGroupListingGroupActionsNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AnotherOperationForSameAssetGroupHasError, ErrorCodes.AnotherOperationForSameAssetGroupHasError);
            AddMapping(CampaignManagementErrorCode.AssetGroupIdInvalid, ErrorCodes.AssetGroupIdInvalid);
            AddMapping(CampaignManagementErrorCode.CampaignTypeIsNotPerformanceMax, ErrorCodes.CampaignTypeIsNotPerformanceMax);
            AddMapping(CampaignManagementErrorCode.ListingGoupLimitExceededForAssetGroup, ErrorCodes.ListingGoupLimitExceededForAssetGroup);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingGroupIdInvalid, ErrorCodes.AssetGroupListingGroupIdInvalid);
            AddMapping(CampaignManagementErrorCode.AssetGroupListingTypeInvalid, ErrorCodes.AssetGroupListingTypeInvalid);
            AddMapping(CampaignManagementErrorCode.DuplicateRootNodeForListingGroupTree, ErrorCodes.DuplicateRootNodeForListingGroupTree);
            AddMapping(CampaignManagementErrorCode.ParentListingGroupNodeDoesNotExist, ErrorCodes.ParentListingGroupNodeDoesNotExist);
            AddMapping(CampaignManagementErrorCode.HeightOfListingGroupTreeLimitExceeded, ErrorCodes.HeightOfListingGroupTreeLimitExceeded);
            AddMapping(CampaignManagementErrorCode.ProductConditionHierarchyInvalid, ErrorCodes.ProductConditionHierarchyInvalid);
            AddMapping(CampaignManagementErrorCode.EverythingElseNodeMissing, ErrorCodes.EverythingElseNodeMissing);
            AddMapping(CampaignManagementErrorCode.UpdateIsNotSupportedForListingGroupNode, ErrorCodes.UpdateIsNotSupportedForListingGroupNode);
            AddMapping(CampaignManagementErrorCode.FailedToGetSalesCountryFromCampaignSettings, ErrorCodes.FailedToGetSalesCountryFromCampaignSettings);
            AddMapping(CampaignManagementErrorCode.InvalidSalesCountry, ErrorCodes.InvalidSalesCountry);
            AddMapping(CampaignManagementErrorCode.BidAdjustmentNotSupportedForPerformanceMaxCampaign, ErrorCodes.BidAdjustmentNotSupportedForPerformanceMaxCampaign);
            AddMapping(CampaignManagementErrorCode.ManualTaggingDetectedInQueryParameters, ErrorCodes.ManualTaggingDetectedInQueryParameters);

            AddMapping(CampaignManagementErrorCode.SearchThemeEntityLimitExceeded, ErrorCodes.SearchThemeEntityLimitExceeded);
            AddMapping(CampaignManagementErrorCode.TooLongSearchTheme, ErrorCodes.TooLongSearchTheme);
            AddMapping(CampaignManagementErrorCode.SearchThemeNameMissing, ErrorCodes.SearchThemeNameMissing);
            AddMapping(CampaignManagementErrorCode.SearchThemeNameHasInvalidChars, ErrorCodes.SearchThemeNameHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.DuplicateSearchThemeName, ErrorCodes.DuplicateSearchThemeName);
            
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetValueDuplicated, ErrorCodes.AssetGroupUrlTargetValueDuplicated);
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetDuplicated, ErrorCodes.AssetGroupUrlTargetDuplicated);
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetInvalid, ErrorCodes.AssetGroupUrlTargetInvalid);
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetOperatorInvalid, ErrorCodes.AssetGroupUrlTargetOperatorInvalid);
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetConditionInvalid, ErrorCodes.AssetGroupUrlTargetConditionInvalid);
            AddMapping(CampaignManagementErrorCode.AssetGroupUrlTargetValueInvalid, ErrorCodes.AssetGroupUrlTargetValueInvalid);
            
            AddMapping(CampaignManagementErrorCode.AudienceGroupNameTooLong, ErrorCodes.AudienceGroupNameTooLong);
            AddMapping(CampaignManagementErrorCode.AudienceGroupNameMissing, ErrorCodes.AudienceGroupNameMissing);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForPmaxCampaign, ErrorCodes.EntityNotAllowedForPmaxCampaign);
            AddMapping(CampaignManagementErrorCode.AudienceGroupIdShouldBeNullOnAdd, ErrorCodes.AudienceGroupIdShouldBeNullOnAdd);
            AddMapping(CampaignManagementErrorCode.AudienceGroupsAreNullOrEmpty, ErrorCodes.AudienceGroupsAreNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AudienceGroupOperationsBatchLimitExceeded, ErrorCodes.AudienceGroupOperationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AudienceGroupIsNull, ErrorCodes.AudienceGroupIsNull);
            AddMapping(CampaignManagementErrorCode.DuplicateAudienceGroupId, ErrorCodes.DuplicateAudienceGroupId);
            AddMapping(CampaignManagementErrorCode.DimensionsCannotBeNull, ErrorCodes.DimensionsCannotBeNull);
            AddMapping(CampaignManagementErrorCode.UnsupportedAgeRangeForAudienceGroup, ErrorCodes.UnsupportedAgeRangeForAudienceGroup);
            AddMapping(CampaignManagementErrorCode.UnsupportedGenderTypeForAudienceGroup, ErrorCodes.UnsupportedGenderTypeForAudienceGroup);

            AddMapping(CampaignManagementErrorCode.AudienceGroupNameHasInvalidChars, ErrorCodes.AudienceGroupNameHasInvalidChars);
            AddMapping(CampaignManagementErrorCode.AudienceNotFound, ErrorCodes.AudienceNotFound);
            AddMapping(CampaignManagementErrorCode.AudienceTypeNotSupported, ErrorCodes.AudienceTypeNotSupported);
            AddMapping(CampaignManagementErrorCode.DuplicateAudience, ErrorCodes.DuplicateAudience);
            AddMapping(CampaignManagementErrorCode.TooManyAudiences, ErrorCodes.TooManyAudiences);
            AddMapping(CampaignManagementErrorCode.TooManyDimensionsOfSameType, ErrorCodes.TooManyDimensionsOfSameType);
            AddMapping(CampaignManagementErrorCode.AudienceGroupInUse, ErrorCodes.AudienceGroupInUse);
            AddMapping(CampaignManagementErrorCode.SharedBudgetNotSupportedForCampaignType, ErrorCodes.SharedBudgetNotSupportedForCampaignType);
            AddMapping(CampaignManagementErrorCode.PerformanceMaxCampaignLimitExceeded, ErrorCodes.PerformanceMaxCampaignLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AssetGroupAlreadyExists, ErrorCodes.AssetGroupAlreadyExists);
            AddMapping(CampaignManagementErrorCode.AudienceGroupWithActiveAssociationsCannotBeDeleted, ErrorCodes.AudienceGroupWithActiveAssociationsCannotBeDeleted);
            AddMapping(CampaignManagementErrorCode.AssetGroupLimitExceededForCampaign, ErrorCodes.AssetGroupLimitExceededForCampaign);
            AddMapping(CampaignManagementErrorCode.EntityDoesNotBelongToCampaign, ErrorCodes.EntityDoesNotBelongToCampaign);
            AddMapping(CampaignManagementErrorCode.UnsupportedCampaignTypeForAdGroup, ErrorCodes.UnsupportedCampaignTypeForAdGroup);
            AddMapping(CampaignManagementErrorCode.AssetGroupInvalidStatus, ErrorCodes.AssetGroupInvalidStatus);
            AddMapping(CampaignManagementErrorCode.DomainDoesNotMatchCampaignStoreDomain, ErrorCodes.DomainDoesNotMatchCampaignStoreDomain);
            AddMapping(CampaignManagementErrorCode.CampaignStoreDoesNotHaveDomain, ErrorCodes.CampaignStoreDoesNotHaveDomain);

            AddMapping(CampaignManagementErrorCode.InvalidVideoAsset, ErrorCodes.InvalidVideoAsset);
            AddMapping(CampaignManagementErrorCode.DuplicateVideoAsset, ErrorCodes.DuplicateVideoAsset);
            AddMapping(CampaignManagementErrorCode.VideoInvalidStatus, ErrorCodes.VideoInvalidStatus);
            AddMapping(CampaignManagementErrorCode.VideoWidthTooSmall, ErrorCodes.VideoWidthTooSmall);
            AddMapping(CampaignManagementErrorCode.VideoHeightTooSmall, ErrorCodes.VideoHeightTooSmall);
            AddMapping(CampaignManagementErrorCode.VideoInvalidAspectRatio, ErrorCodes.VideoInvalidAspectRatio);
            AddMapping(CampaignManagementErrorCode.VideoInvalidDuration, ErrorCodes.VideoInvalidDuration);
            AddMapping(CampaignManagementErrorCode.VideoBitRateTooSmall, ErrorCodes.VideoBitRateTooSmall);
            AddMapping(CampaignManagementErrorCode.VideoSourceLengthTooLarge, ErrorCodes.VideoSourceLengthTooLarge);
            AddMapping(CampaignManagementErrorCode.VideoUnsupportedFileFormat, ErrorCodes.VideoUnsupportedFileFormat);
            AddMapping(CampaignManagementErrorCode.VideoAsAssetNotEnabledForAccount, ErrorCodes.VideoAsAssetNotEnabledForAccount);

            AddMapping(CampaignManagementErrorCode.TaskThrottlingLimitReached, ErrorCodes.TaskThrottlingLimitReached);

            AddMapping(CampaignManagementErrorCode.AssetFieldMinimumNotMet, ErrorCodes.AssetFieldMinimumNotMet);
            AddMapping(CampaignManagementErrorCode.AssetFieldLimitExceeded, ErrorCodes.AssetFieldLimitExceeded);

            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForImageAutoRetrieve, ErrorCodes.AccountNotEnabledForImageAutoRetrieve);

            AddMapping(CampaignManagementErrorCode.TargetCostPerSaleInvalid, ErrorCodes.TargetCostPerSaleInvalid);
            AddMapping(CampaignManagementErrorCode.CostPerSaleBiddingSchemeCannotBeUpdated, ErrorCodes.CostPerSaleBiddingSchemeCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.InvalidShoppingCampaignPriorityForCostPerSale, ErrorCodes.InvalidShoppingCampaignPriorityForCostPerSale);
            AddMapping(CampaignManagementErrorCode.DailyBudgetAmountLessThanTargetCostPerSale, ErrorCodes.DailyBudgetAmountLessThanTargetCostPerSale);
            AddMapping(CampaignManagementErrorCode.CostPerSaleNotWorkWithShoppableAds, ErrorCodes.CostPerSaleNotWorkWithShoppableAds);

            AddMapping(CampaignManagementErrorCode.TargetRoasNotSupportedForPersonalizedOffers, ErrorCodes.TargetRoasNotSupportedForPersonalizedOffers);
            AddMapping(CampaignManagementErrorCode.BidCannotBeManagedForBiddingScheme, ErrorCodes.BidCannotBeManagedForBiddingScheme);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForRemarketingListBasedParameters, ErrorCodes.CustomerNotEligibleForRemarketingListBasedParameters);

            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForCampaignConversionGoal, ErrorCodes.AccountNotEligibleForCampaignConversionGoal);
            AddMapping(CampaignManagementErrorCode.DuplicateCampaignConversionGoal, ErrorCodes.DuplicateCampaignConversionGoal);
            AddMapping(CampaignManagementErrorCode.StoreVisitNotSupportForCampaignConversionGoal, ErrorCodes.StoreVisitNotSupportForCampaignConversionGoal);
            AddMapping(CampaignManagementErrorCode.CampaignConversionGoalNotExist, ErrorCodes.CampaignConversionGoalNotExist);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignConversionGoalSubType, ErrorCodes.InvalidCampaignConversionGoalSubType);
            AddMapping(CampaignManagementErrorCode.CampaignNotEligibleForCampaignConversionGoal, ErrorCodes.CampaignNotEligibleForCampaignConversionGoal);

            AddMapping(CampaignManagementErrorCode.EnableMMAUnderDSAAdgroupsValueInvalid, ErrorCodes.EnableMMAUnderDSAAdgroupsValueInvalid);

            //AdCustomizerAttribute
            AddMapping(CampaignManagementErrorCode.RSAAdCustomizerAttributeTypeChangedInUpdate, ErrorCodes.RSAAdCustomizerAttributeTypeChangedInUpdate);
            AddMapping(CampaignManagementErrorCode.RSAAdCustomizerAttributeCountMoreThanLimit, ErrorCodes.RSAAdCustomizerAttributeCountMoreThanLimit);
            AddMapping(CampaignManagementErrorCode.RSAAdCustomizerInvalidAttributeType, ErrorCodes.RSAAdCustomizerInvalidAttributeType);
            AddMapping(CampaignManagementErrorCode.AttributeNameLengthExceeded, ErrorCodes.AttributeNameLengthExceeded);
            AddMapping(CampaignManagementErrorCode.InvalidAdcustomizerAttributeId, ErrorCodes.InvalidAdcustomizerAttributeId);
            AddMapping(CampaignManagementErrorCode.AttributeNameMissing, ErrorCodes.AttributeNameMissing);
            AddMapping(CampaignManagementErrorCode.AttributeReferencedInAd, ErrorCodes.AttributeReferencedInAd);

            //HotelV2
            AddMapping(CampaignManagementErrorCode.InvalidBidTypeForCampaignBiddingScheme, ErrorCodes.InvalidBidTypeForCampaignBiddingScheme);
            AddMapping(CampaignManagementErrorCode.CampaignIsNotOfTypeHotel, ErrorCodes.CampaignIsNotOfTypeHotel);
            AddMapping(CampaignManagementErrorCode.InvalidHotelListingType, ErrorCodes.InvalidHotelListingType);
            AddMapping(CampaignManagementErrorCode.AdGroupCriterionHotelListingIsNull, ErrorCodes.AdGroupCriterionHotelListingIsNull);
            AddMapping(CampaignManagementErrorCode.InvalidHotelListingOperand, ErrorCodes.InvalidHotelListingOperand);
            AddMapping(CampaignManagementErrorCode.InvalidHotelListingAttribute, ErrorCodes.InvalidHotelListingAttribute);
            AddMapping(CampaignManagementErrorCode.FinalUrlAndMobileUrlNotAllowedForHotelListing, ErrorCodes.FinalUrlAndMobileUrlNotAllowedForHotelGroup);
            AddMapping(CampaignManagementErrorCode.DuplicateRootNodeForHotelListingTree, ErrorCodes.DuplicateRootNodeForHotelGroupTree);
            AddMapping(CampaignManagementErrorCode.ParentHotelListingGroupNodeDoesNotExist, ErrorCodes.ParentHotelGroupNodeDoesNotExist);
            AddMapping(CampaignManagementErrorCode.HeightOfHotelListingTreeExceeededLimit, ErrorCodes.HeightOfHotelGroupTreeExceeededLimit);
            AddMapping(CampaignManagementErrorCode.HotelListingOperandUnderSubDivisionMustBeSame, ErrorCodes.HotelListingOperandUnderSubDivisionMustBeSame);
            AddMapping(CampaignManagementErrorCode.DuplicateHotelListing, ErrorCodes.DuplicateHotelListing);
            AddMapping(CampaignManagementErrorCode.InvalidHotelListingHierarchy, ErrorCodes.InvalidHotelGroupHierarchy);
            AddMapping(CampaignManagementErrorCode.HotelListingGroupLimitExceededForAdGroup, ErrorCodes.HotelGroupLimitExceededForAdGroup);
            AddMapping(CampaignManagementErrorCode.InvalidAdGroupCriterionRateBidValue, ErrorCodes.InvalidAdGroupCriterionRateBidValue);
            AddMapping(CampaignManagementErrorCode.HotelListingEverythingElseMissing, ErrorCodes.HotelGroupEverythingElseMissing);
            AddMapping(CampaignManagementErrorCode.InvalidLocationNodeInvalidParentLocation, ErrorCodes.InvalidLocationNodeInvalidParentLocation);

            AddMapping(CampaignManagementErrorCode.InvalidAdvanceBookingWindowTarget, ErrorCodes.InvalidAdvanceBookingWindowTarget);
            AddMapping(CampaignManagementErrorCode.InvalidCheckInDayTarget, ErrorCodes.InvalidCheckInDayTarget);
            AddMapping(CampaignManagementErrorCode.InvalidLengthOfStayTarget, ErrorCodes.InvalidLengthOfStayTarget);
            AddMapping(CampaignManagementErrorCode.InvalidCheckInDateTarget, ErrorCodes.InvalidCheckInDateTarget);
            AddMapping(CampaignManagementErrorCode.InvalidDateSelectionTypeCriterion, ErrorCodes.InvalidDateSelectionTypeTarget);
            AddMapping(CampaignManagementErrorCode.InvalidAdvanceBookingWindowTargetBidAdjustment, ErrorCodes.InvalidAdvanceBookingWindowTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidCheckInDayTargetBidAdjustment, ErrorCodes.InvalidCheckInDayTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidLengthOfStayTargetBidAdjustment, ErrorCodes.InvalidLengthOfStayTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidCheckInDateTargetBidAdjustment, ErrorCodes.InvalidCheckInDateTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidDateSelectionTypeTargetBidAdjustment, ErrorCodes.InvalidDateSelectionTypeTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.InvalidTargetTypeForCommisionBiddingScheme, ErrorCodes.InvalidTargetTypeForCommisionBiddingScheme);
            AddMapping(CampaignManagementErrorCode.ConflictWithAdvanceBookingWindow, ErrorCodes.AdvanceBookingWindowTargetConflict);
            AddMapping(CampaignManagementErrorCode.ConflictWithLengthOfStay, ErrorCodes.LengthOfStayTargetConflict);
            AddMapping(CampaignManagementErrorCode.ConflictWithCheckInDate, ErrorCodes.CheckInDateTargetConflict);

            AddMapping(CampaignManagementErrorCode.CannotSetBiddingSchemeForHotelAdGroup, ErrorCodes.CannotSetBiddingSchemeForHotelAdGroup);

            AddMapping(CampaignManagementErrorCode.CustomerNotEnabledForMultiChannelCampaign, ErrorCodes.CustomerNotEnabledForMultiChannelCampaign);
            AddMapping(CampaignManagementErrorCode.SmartShoppingCampaignCreationNotSupported, ErrorCodes.SmartShoppingCampaignCreationNotSupported);
            AddMapping(CampaignManagementErrorCode.LegacyWinstoreAdsCampaignCreationNotSupported, ErrorCodes.LegacyWinstoreAdsCampaignCreationNotSupported);
            AddMapping(CampaignManagementErrorCode.PlacementTargetingNotSupported, ErrorCodes.PlacementTargetingNotSupported);

            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForFrequencyCap, ErrorCodes.AccountNotEligibleForFrequencyCap);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignSubtypeForFrequencyCap, ErrorCodes.InvalidCampaignSubtypeForFrequencyCap);
            AddMapping(CampaignManagementErrorCode.InvalidFrequencyCapSettings, ErrorCodes.InvalidFrequencyCapSettings);
            AddMapping(CampaignManagementErrorCode.CallToActionNotSupported, ErrorCodes.CallToActionNotSupported);
            AddMapping(CampaignManagementErrorCode.CampaignAssociationsLimitExceeded, ErrorCodes.CampaignAssociationsLimitExceeded);
            AddMapping(CampaignManagementErrorCode.InvalidAssociation, ErrorCodes.InvalidAssociation);
            AddMapping(CampaignManagementErrorCode.SeasonalityAdjustmentTimestampMismatch, ErrorCodes.SeasonalityAdjustmentTimestampMismatch);
            AddMapping(CampaignManagementErrorCode.EntitiesAreNullOrEmpty, ErrorCodes.EntitiesAreNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.OperationsBatchLimitExceeded, ErrorCodes.OperationsBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.NameIsEmpty, ErrorCodes.NameIsEmpty);
            AddMapping(CampaignManagementErrorCode.NameExceededMaxLen, ErrorCodes.NameExceededMaxLen);
            AddMapping(CampaignManagementErrorCode.DescriptionIsNull, ErrorCodes.DescriptionIsNull);
            AddMapping(CampaignManagementErrorCode.DescriptionExceededMaxLen, ErrorCodes.DescriptionExceededMaxLen);
            AddMapping(CampaignManagementErrorCode.InvalidAdjustmentPercentage, ErrorCodes.InvalidAdjustmentPercentage);
            AddMapping(CampaignManagementErrorCode.DateShouldNotBeNull, ErrorCodes.DateShouldNotBeNull);
            AddMapping(CampaignManagementErrorCode.DateGranularityCanOnlyBeToHours, ErrorCodes.DateGranularityCanOnlyBeToHours);
            AddMapping(CampaignManagementErrorCode.InvalidDateRange, ErrorCodes.InvalidDateRange);
            AddMapping(CampaignManagementErrorCode.DeviceTypeFilterCannotBeNone, ErrorCodes.DeviceTypeFilterCannotBeNone);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignAssociationsAndCampaignTypeFilterCombination, ErrorCodes.InvalidCampaignAssociationsAndCampaignTypeFilterCombination);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignAssociationsLength, ErrorCodes.InvalidCampaignAssociationsLength);
            AddMapping(CampaignManagementErrorCode.InvalidCampaignType, ErrorCodes.InvalidCampaignType);
            AddMapping(CampaignManagementErrorCode.SeasonalityAdjustmentExceedLimit, ErrorCodes.SeasonalityAdjustmentExceedLimit);
            AddMapping(CampaignManagementErrorCode.InvalidDataExclusionId, ErrorCodes.InvalidDataExclusionId);
            AddMapping(CampaignManagementErrorCode.DataExclusionTimestampMismatch, ErrorCodes.DataExclusionTimestampMismatch);
            AddMapping(CampaignManagementErrorCode.DataExclusionAdjustmentPercentageShouldBeZero, ErrorCodes.DataExclusionAdjustmentPercentageShouldBeZero);
            AddMapping(CampaignManagementErrorCode.DataExclusionExceedLimit, ErrorCodes.DataExclusionExceedLimit);
            AddMapping(CampaignManagementErrorCode.StartDateComesAfterEndDate, ErrorCodes.StartDateComesAfterEndDate);
            AddMapping(CampaignManagementErrorCode.DuplicateItemsInBatch, ErrorCodes.DuplicateItemsInBatch);
            AddMapping(CampaignManagementErrorCode.InvalidSeasonalityAdjustmentId, ErrorCodes.InvalidSeasonalityAdjustmentId);
            AddMapping(CampaignManagementErrorCode.InvalidBrandId, ErrorCodes.InvalidIds);
            AddMapping(CampaignManagementErrorCode.OnlyOneInStoreVisitGoalBeAllowedPerCustomer, ErrorCodes.OnlyOneInStoreVisitGoalBeAllowedPerCustomer);
            AddMapping(CampaignManagementErrorCode.SmartGoalShouldBeOnlyOne, ErrorCodes.OnlyOneSmartGoalBeAllowedPerAccount);
            AddMapping(CampaignManagementErrorCode.GoalIsReadOnly, ErrorCodes.GoalIsReadOnly);
            AddMapping(CampaignManagementErrorCode.SmartGoalCouldNotBeEditInSomeParameters, ErrorCodes.SmartGoalCouldNotBeEditInSomeParameters);
            AddMapping(CampaignManagementErrorCode.AttributionModelTypeNotApplicableToGoalType, ErrorCodes.AttributionModelTypeNotApplicableToGoalType);
            AddMapping(CampaignManagementErrorCode.DuplicateGoalName, ErrorCodes.DuplicateGoalName);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForInStoreVisitGoal, ErrorCodes.CustomerNotEligibleForInStoreVisitGoal);
            AddMapping(CampaignManagementErrorCode.CustomerNotEligibleForProductConversionGoal, ErrorCodes.CustomerNotEligibleForProductConversionGoal);

            // App Campaigns
            AddMapping(CampaignManagementErrorCode.AppCampaignsNotEnabledForAccount, ErrorCodes.AppCampaignsNotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForAppCampaign, ErrorCodes.EntityNotAllowedForAppCampaign);
            AddMapping(CampaignManagementErrorCode.TrackingTemplateIsRequiredForMobileAppCampaign, ErrorCodes.TrackingTemplateIsRequiredForMobileAppCampaign);

            // Conversion Value Rule
            AddMapping(CampaignManagementErrorCode.ExceedsMaximumNumberOfRules, ErrorCodes.ExceedsMaximumNumberOfRules);
            AddMapping(CampaignManagementErrorCode.DuplicateRuleName, ErrorCodes.DuplicateRuleName);
            AddMapping(CampaignManagementErrorCode.RuleIdNotFound, ErrorCodes.RuleIdNotFound);
            AddMapping(CampaignManagementErrorCode.LocationTypeMismatch, ErrorCodes.LocationTypeMismatch);
            AddMapping(CampaignManagementErrorCode.AudienceTypeMismatch, ErrorCodes.AudienceTypeMismatch);
            AddMapping(CampaignManagementErrorCode.ConditionOverlap, ErrorCodes.ConditionOverlap);
            AddMapping(CampaignManagementErrorCode.LocationHierarchyIssue, ErrorCodes.LocationHierarchyIssue);
            AddMapping(CampaignManagementErrorCode.EntityIsEmptyOrNull, ErrorCodes.EntityIsEmptyOrNull);
            AddMapping(CampaignManagementErrorCode.EmptyPropertyNotAllowed, ErrorCodes.EmptyPropertyNotAllowed);
            AddMapping(CampaignManagementErrorCode.CurrencyCodeShouldNotBeNullForAdd, ErrorCodes.CurrencyCodeShouldNotBeNullForAdd);
            AddMapping(CampaignManagementErrorCode.PrimaryConditionShouldNotBeNull, ErrorCodes.PrimaryConditionShouldNotBeNull);
            AddMapping(CampaignManagementErrorCode.DuplicatedRuleId, ErrorCodes.DuplicateRuleId);
            AddMapping(CampaignManagementErrorCode.ConversionValueRuleEnabled, ErrorCodes.ConversionValueRuleNotEnabled);
            AddMapping(CampaignManagementErrorCode.ConditionTypeNotAllowed, ErrorCodes.ConditionTypeNotAllowed);

            // Custom Segmetment
            AddMapping(CampaignManagementErrorCode.ExceedMaxCustomSegmentCriterionCountPerAdGroup, ErrorCodes.ExceedMaxCustomSegmentCriterionCountPerAdGroup);
            AddMapping(CampaignManagementErrorCode.CustomSegmentOnlySupportAudienceCampaign, ErrorCodes.CustomSegmentOnlySupportAudienceCampaign);
            AddMapping(CampaignManagementErrorCode.CustomerIsNotEligibleForKeywordTargeting, ErrorCodes.CustomerIsNotEligibleForKeywordTargeting);
            AddMapping(CampaignManagementErrorCode.NegativeAdGroupCriterionIsNotSupportedByCustomSegment, ErrorCodes.NegativeAdGroupCriterionIsNotSupportedByCustomSegment);
            AddMapping(CampaignManagementErrorCode.BlockedSegmentIdsInvalid,
                ErrorCodes.BlockedSegmentIdsInvalid);
            AddMapping(CampaignManagementErrorCode.AccountNotEligibleForBrandsafetyFeature,
                ErrorCodes.AccountNotEligibleForBrandsafetyFeature);
            AddMapping(CampaignManagementErrorCode.DuplicatedBlockedSegmentIds,
                ErrorCodes.DuplicatedBlockedSegmentIds);
            AddMapping(CampaignManagementErrorCode.CustomSegmentOnlySupportAccountLevel,
                ErrorCodes.CustomSegmentOnlySupportAccountLevel);
            AddMapping(CampaignManagementErrorCode.CustomSegmentNotFound, ErrorCodes.CustomSegmentNotFound);
            AddMapping(CampaignManagementErrorCode.InValidCustomSegmentId, ErrorCodes.InValidCustomSegmentId);
            AddMapping(CampaignManagementErrorCode.DeletedCustomSegment, ErrorCodes.DeletedCustomSegment);
            AddMapping(CampaignManagementErrorCode.CustomSegmentCatalogIsEmpty, ErrorCodes.CustomSegmentCatalogIsEmpty);

            // LinkedIn Campaign
            AddMapping(CampaignManagementErrorCode.LinkedInCampaignsNotEnabledForAccount, ErrorCodes.LinkedInCampaignsNotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.EntityNotAllowedForLinkedInCampaign, ErrorCodes.EntityNotAllowedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.TrackingTemplateNotSupportedForLinkedInCampaign, ErrorCodes.TrackingTemplateNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.FinalUrlSuffixNotSupportedForLinkedInCampaign, ErrorCodes.FinalUrlSuffixNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.UrlCustomParametersSupportedForLinkedInCampaign, ErrorCodes.UrlCustomParametersSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign, ErrorCodes.AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.AdGroupDeletionIsNotSupportedForLinkedInCampaign, ErrorCodes.AdGroupDeletionIsNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.AdGroupStartDateCannotBeChangedForLinkedInCampaign, ErrorCodes.AdGroupStartDateCannotBeChangedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.MultiLanguagesNotSupportedForLinkedInCampaign, ErrorCodes.MultiLanguagesNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.CannotEditLinkedInCampaignLanguage, ErrorCodes.CannotEditLinkedInCampaignLanguage);
            AddMapping(CampaignManagementErrorCode.BidAdjustmentNotSupportedForLinkedInCampaign, ErrorCodes.BidAdjustmentNotSupportedForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.NotSupportedConversionGoalTypeForLinkedInCampaign, ErrorCodes.NotSupportedConversionGoalTypeForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.NotSupportedConversionGoalScopeForLinkedInCampaign, ErrorCodes.NotSupportedConversionGoalScopeForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.NotSupportedGoalCountTypeForLinkedInCampaign, ErrorCodes.NotSupportedGoalCountTypeForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.NotSupportedExcludeFromBiddingValueForLinkedInCampaign, ErrorCodes.NotSupportedExcludeFromBiddingValueForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.NotSupportedGoalAttributionModelForLinkedInCampaign, ErrorCodes.NotSupportedGoalAttributionModelForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.LinkedInCampaignUndeleteNotAllowed, ErrorCodes.LinkedInCampaignUndeleteNotAllowed);
            AddMapping(CampaignManagementErrorCode.NotSupportedGoalClickLookbackWindowForLinkedInCampaign, ErrorCodes.NotSupportedGoalClickLookbackWindowForLinkedInCampaign);
            AddMapping(CampaignManagementErrorCode.LinkedInCampaignAudienceEstimationBelowThreshold, ErrorCodes.LinkedInCampaignAudienceEstimationBelowThreshold);
            AddMapping(CampaignManagementErrorCode.LinkedInCampaignAdInReviewCannotPause, ErrorCodes.LinkedInCampaignAdInReviewCannotPause);
            AddMapping(CampaignManagementErrorCode.ApplyLinkedInOfflineConversionFailed, ErrorCodes.ApplyLinkedInOfflineConversionFailed);
            AddMapping(CampaignManagementErrorCode.ApplyLinkedInOfflineConversionInternalError, ErrorCodes.ApplyLinkedInOfflineConversionInternalError);

            // Placement Criterion
            AddMapping(CampaignManagementErrorCode.ContentTargetingOnlySupportAudienceCampaign, ErrorCodes.ContentTargetingOnlySupportAudienceCampaign);
            AddMapping(CampaignManagementErrorCode.AccountIsNotEligibleForPlacementTargeting, ErrorCodes.AccountIsNotEligibleForPlacementTargeting);
            AddMapping(CampaignManagementErrorCode.InvalidPlacementIdTarget, ErrorCodes.InvalidPlacementIdTarget);
            AddMapping(CampaignManagementErrorCode.InvalidPlacementTargetBidAdjustment, ErrorCodes.InvalidPlacementTargetBidAdjustment);
            AddMapping(CampaignManagementErrorCode.AccountIsNotEligibleForSubPlacementTargeting, ErrorCodes.AccountIsNotEligibleForSubPlacementTargeting);

            // PMax New Customer Acquisition
            AddMapping(CampaignManagementErrorCode.PmaxNewCustomerAcquisitionNotEnabled, ErrorCodes.PmaxNewCustomerAcquisitionNotEnabled);
            AddMapping(CampaignManagementErrorCode.InvalidAdditionalValue, ErrorCodes.InvalidAdditionalValue);
            AddMapping(CampaignManagementErrorCode.NewCustomerAcquisitionGoalDoesNotExist, ErrorCodes.NewCustomerAcquisitionGoalDoesNotExist);
            AddMapping(CampaignManagementErrorCode.NewCustomerAcquisitionAudienceCountExceedsLimit, ErrorCodes.NewCustomerAcquisitionAudienceCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.DuplicateNewCustomerAcquisitionGoal, ErrorCodes.DuplicateNewCustomerAcquisitionGoal);
            AddMapping(CampaignManagementErrorCode.NewCustomerAcquisitionNoPurchaseGoal, ErrorCodes.NewCustomerAcquisitionNoPurchaseGoal);
            AddMapping(CampaignManagementErrorCode.InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode, ErrorCodes.InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode);
            AddMapping(CampaignManagementErrorCode.InvalidNewCustomerAcquisitionGoalId, ErrorCodes.InvalidNewCustomerAcquisitionGoalId);
            AddMapping(CampaignManagementErrorCode.PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign, ErrorCodes.PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign);
            AddMapping(CampaignManagementErrorCode.InvalidAdditionalConversionValue, ErrorCodes.InvalidAdditionalConversionValue);
            AddMapping(CampaignManagementErrorCode.AudienceAssociationRequiredForNewCustomerAcquisitionGoal, ErrorCodes.AudienceAssociationRequiredForNewCustomerAcquisitionGoal);
            AddMapping(CampaignManagementErrorCode.CampaignLevelAdditionalValueNotSupportedForBidOnlyMode, ErrorCodes.CampaignLevelAdditionalValueNotSupportedForBidOnlyMode);

            // Brand Kit
            AddMapping(CampaignManagementErrorCode.BrandKitNotEnabledForAccount, ErrorCodes.BrandKitNotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.InvalidBrandKitId, ErrorCodes.InvalidBrandKitId);
            AddMapping(CampaignManagementErrorCode.DuplicateInBrandKitIds, ErrorCodes.DuplicateInBrandKitIds);
            AddMapping(CampaignManagementErrorCode.InvalidBrandKitColorCode, ErrorCodes.InvalidBrandKitColorCode);
            AddMapping(CampaignManagementErrorCode.InvalidBrandKitFontTypeface, ErrorCodes.InvalidBrandKitFontTypeface);
            AddMapping(CampaignManagementErrorCode.InvalidBrandKitFontWeight, ErrorCodes.InvalidBrandKitFontWeight);
            AddMapping(CampaignManagementErrorCode.InvalidBrandKitFontTextAssetType, ErrorCodes.InvalidBrandKitFontTextAssetType);
            AddMapping(CampaignManagementErrorCode.BrandKitNameTooLong, ErrorCodes.BrandKitNameTooLong);
            AddMapping(CampaignManagementErrorCode.BrandKitNameMissing, ErrorCodes.BrandKitNameMissing);
            AddMapping(CampaignManagementErrorCode.BrandKitPaletteColorCountExceedsLimit, ErrorCodes.BrandKitPaletteColorCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.BrandKitColorNameTooLong, ErrorCodes.BrandKitColorNameTooLong);
            AddMapping(CampaignManagementErrorCode.BrandKitImagesCountExceedsLimit, ErrorCodes.BrandKitImagesCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.BrandKitSquareLogosCountExceedsLimit, ErrorCodes.BrandKitSquareLogosCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.BrandKitLandscapeLogosCountExceedsLimit, ErrorCodes.BrandKitLandscapeLogosCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.BrandKitIdsArrayShouldNotBeNullOrEmpty, ErrorCodes.BrandKitIdsArrayShouldNotBeNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.BrandKitColorsArrayShouldNotBeNullorEmpty, ErrorCodes.BrandKitColorsArrayShouldNotBeNullorEmpty);
            AddMapping(CampaignManagementErrorCode.BrandKitBusinessNameTooLong, ErrorCodes.BrandKitBusinessNameTooLong);
            AddMapping(CampaignManagementErrorCode.BrandKitPhase2NotEnabledForAccount, ErrorCodes.BrandKitPhase2NotEnabledForAccount);
            AddMapping(CampaignManagementErrorCode.BrandVoicePersonalityTooLong, ErrorCodes.BrandVoicePersonalityTooLong);
            AddMapping(CampaignManagementErrorCode.BrandVoiceTonesCountExceedsLimit, ErrorCodes.BrandVoiceTonesCountExceedsLimit);
            AddMapping(CampaignManagementErrorCode.BrandVoiceTonesTooLong, ErrorCodes.BrandVoiceTonesTooLong);
            AddMapping(CampaignManagementErrorCode.BrandKitPaletteNameTooLong, ErrorCodes.BrandKitPaletteNameTooLong);

            // Asset Recommendation
            AddMapping(CampaignManagementErrorCode.GenerateImagesAIGCNoRecommendationWithBrand, ErrorCodes.InvalidParameters);

            // Lifetime budget
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForCampaignLevelDates, ErrorCodes.AccountNotEnabledForCampaignLevelDates);
            AddMapping(CampaignManagementErrorCode.CampaignLevelDatesNotEnabled, ErrorCodes.CampaignLevelDatesNotEnabled);
            AddMapping(CampaignManagementErrorCode.CampaignStartDateNotSet, ErrorCodes.CampaignStartDateNotSet);
            AddMapping(CampaignManagementErrorCode.CampaignEndDateNotSet, ErrorCodes.CampaignEndDateNotSet);
            AddMapping(CampaignManagementErrorCode.CampaignEndDateExceedsOneYear, ErrorCodes.CampaignEndDateExceedsOneYear);
            AddMapping(CampaignManagementErrorCode.CannotUpdateStartDateAfterCampaignStart, ErrorCodes.CannotUpdateStartDateAfterCampaignStart);
            AddMapping(CampaignManagementErrorCode.CannotUpdateBudgetTypeAfterCampaignStart, ErrorCodes.CannotUpdateBudgetTypeAfterCampaignStart);
            AddMapping(CampaignManagementErrorCode.CannotUpdateUseCampaignLevelAfterCampaignStart, ErrorCodes.CannotUpdateUseCampaignLevelAfterCampaignStart);
            AddMapping(CampaignManagementErrorCode.AdGroupLevelDatesBudgetTypeCannotBeUpdated, ErrorCodes.AdGroupLevelDatesBudgetTypeCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.AdGroupLevelDatesCannotBeUpdated, ErrorCodes.AdGroupLevelDatesCannotBeUpdated);
            AddMapping(CampaignManagementErrorCode.CampaignSubTypeNotSupportedForCampaignLevelDates, ErrorCodes.CampaignSubTypeNotSupportedForCampaignLevelDates);

            // Clipchamp
            AddMapping(CampaignManagementErrorCode.AccountNotEnabledForVideoAdsGeneration, ErrorCodes.AccountNotEnabledForVideoAdsGeneration);
            AddMapping(CampaignManagementErrorCode.NoAudioMatchesFilter, ErrorCodes.NoAudioMatchesFilter);

            // Annotations
            AddMapping(CampaignManagementErrorCode.InvalidExclusionTypeIdOrSubTypeId, ErrorCodes.InvalidExclusionTypeIdOrSubTypeId);
            AddMapping(CampaignManagementErrorCode.AnnotationOptOutJustificationTextTooLong, ErrorCodes.AnnotationOptOutJustificationTextTooLong);
            AddMapping(CampaignManagementErrorCode.AnnotationOptOutBatchLimitExceeded, ErrorCodes.AnnotationOptOutBatchLimitExceeded);
            AddMapping(CampaignManagementErrorCode.AnnotationOptOutCollectionNullOrEmpty, ErrorCodes.AnnotationOptOutCollectionNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AnnotationOptOutJustificationTextNullOrEmpty, ErrorCodes.AnnotationOptOutJustificationTextNullOrEmpty);
            AddMapping(CampaignManagementErrorCode.AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut, ErrorCodes.AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut);

            if (errorOccured)
            {
                throw new ApplicationException("ArgumentException while mapping MT error codes to API error codes");
            }
        }

        private static void AddMapping(CampaignManagementErrorCode mtErrorCode, int apiErrCode)
        {
            try
            {
                errorMapping.Add((int)mtErrorCode, apiErrCode);
            }
            catch (ArgumentException argEx)
            {
                //logging and swallowing error
                ApiLogger.LogApplicationError(AdvertiserLogContext.DefaultAppContext(), string.Format("Error mapping mt error code {0} to api error code {1}, error message: {2}", mtErrorCode, apiErrCode, argEx.Message), ErrorSource.CampaignAPI);
                errorOccured = true;
            }
        }

        /// <summary>
        /// Looks up the api error code for the given middle tier error code.
        /// </summary>
        /// <exception cref="InvalidOperationException">ErrorMapping is not initialized.</exception>
        /// <param name="mtCode">middle tier error code.</param>
        /// <returns>api error code.</returns>
        public static int GetApiError(int mtCode, ILogContext logContext = null)
        {
            int apiCode = ErrorCodes.InternalError;

            if (errorMapping.TryGetValue(mtCode, out apiCode))
            {
                return apiCode;
            }
            string message = string.Format(CultureInfo.InvariantCulture, "No mapping for MT error: {0}", mtCode);

            ApiLogger.LogApplicationError(logContext ?? AdvertiserLogContext.DefaultAppContext(), message, ErrorSource.CampaignAPI);
            return apiCode;
        }

        public static bool ApiErrorExists(CampaignManagementErrorCode mtCode)
        {
            int apiCode;
            return errorMapping.TryGetValue((int)mtCode, out apiCode);
        }

    }

}


