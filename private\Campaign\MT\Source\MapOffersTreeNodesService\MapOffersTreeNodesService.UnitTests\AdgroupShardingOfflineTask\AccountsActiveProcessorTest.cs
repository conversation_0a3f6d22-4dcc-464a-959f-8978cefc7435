﻿namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors
{
    using System.Data;
    using Microsoft.Data.SqlClient;
    using System.Threading.Tasks;
    using ConnectionResolvers;
    using GenericFramework.QueueProcessorRuntime;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.DAO;
    using Microsoft.Advertising.ServiceLocation;
    using Unity;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Rhino.Mocks;
    using UnityServiceLocator = Microsoft.Advertising.ServiceLocation.UnityServiceLocator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.UnitTest;
    using UnitTests.AdgroupShardingOfflineTask;
    using System.Collections.Generic;
    using System;
    using System.Linq;
    using System.Data.Common;
    using AppConfigurationManager;

    [TestClass]
    public class AccountsActiveProcessorTest
    {
        private IUnityContainer container;
        private ILogShared logger;
        private IJobBatchRequestContext requestContext;
        private ICampaignDBQueueStore store;

        [TestInitialize]
        public void SetUp()
        {
            this.container = ((UnityServiceLocator)ServiceLocator.Current).Container;
            
            this.logger = MockRepository.GenerateMock<ILogAdvertiserCampaign>();
            this.logger.Expect(s => s.CallTrackingData).Return(new CallTrackingData());
            this.container.RegisterInstance<ILogAdvertiserCampaign>((ILogAdvertiserCampaign)this.logger);
            this.store = MockRepository.GenerateMock<ICampaignDBQueueStore>();
        }

        [TestMethod]
        public void AccountsActiveProcessorTest_Job_Success()
        {

           JobBatch jobBatch = new JobBatch(this.store);
            jobBatch.AddJob(new Job(1, 1, 1, -1, null, null, 1));
            jobBatch.AddJob(new Job(2, 1, 2, -1, null, null, 1));

            InterfaceBindings.RemoveBinding(typeof(IDaoBase));
            var mockDaoBase = MockRepository.GenerateMock<IDaoBase>();
            mockDaoBase.Expect(
                x => x.SetDetails(Arg<ILogShared>.Is.Equal(logger),
                Arg<IConnectionProvider>.Is.Anything,
                Arg<SqlCommand>.Matches(
                    c => c.CommandText.Contains("prc_PublicGetStatusFromAccountsActive")
                        && ((DataTable)c.Parameters["@AccountIDs"].Value).Rows.Count == 2),
                Arg<IDependencyData>.Is.Anything)).Repeat.Once();
            mockDaoBase.Expect(x => x.ExecuteDataReaderAsync()).Repeat.Once().IgnoreArguments()
                .Return(Task.Run(
                    () =>
                    {
                        DataTable dt = ResultSet(2);

                        return (DbDataReader)dt.CreateDataReader();
                    }));

            mockDaoBase.Expect(
                x => x.SetDetails(Arg<ILogShared>.Is.Equal(logger),
                Arg<IConnectionProvider>.Is.Anything,
                Arg<SqlCommand>.Matches(
                    c => c.CommandText.Contains("prc_PublicSetStatusOnAccountsActive")
                        && ((DataTable)c.Parameters["@AccountsActive"].Value).Rows.Count == 2),
                Arg<IDependencyData>.Is.Anything)).Repeat.Once();

            mockDaoBase.Expect(
                x => x.ExecuteNonQueryAsync()).Return(Task.FromResult<int>(1)).Repeat.Once();
            InterfaceBindings.AddBinding(typeof(IDaoBase), typeof(IDaoBase), mockDaoBase);

            AccountsActiveProcessor underTest = new AccountsActiveProcessor();

            this.requestContext = new MockedJobBatchRequestContext(
                null,
                new MockedCampaignTaskShardConnectionResolver(jobBatch.Jobs),
                new MockedAdgroupTaskShardConnectionResolver(jobBatch.Jobs),
                new MockedMainConnectionResolver(jobBatch.Jobs),
                new MockedLibraryTaskShardConnectionResolver(jobBatch.Jobs));

            var processTask = underTest.ProcessAsync(logger, jobBatch, requestContext);

            processTask.Wait();
            Assert.AreEqual(Status.Successful, processTask.Result.JobStatus);
            mockDaoBase.VerifyAllExpectations();
        }

        private static DataTable CreateAccountsActiveTypeDataTable(IEnumerable<AccountsActiveItem> accountsActiveItems)
        {
            var table = new DataTable("AccountsActiveTableType");
            table.Columns.Add(DBColumnName.AccountId, typeof(int));
            table.Columns.Add(DBColumnName.IsActive, typeof(int));
            table.Columns.Add(DBColumnName.LastUpdatedProcessId, typeof(long));

            foreach (AccountsActiveItem item in accountsActiveItems)
            {
                DataRow row = table.NewRow();
                row[DBColumnName.AccountId] = item.AccountId;
                row[DBColumnName.IsActive] = (byte)(item.IsActive ? 1 : 0);

                if (item.LastUpdatedProcessId.HasValue)
                    row[DBColumnName.LastUpdatedProcessId] = item.LastUpdatedProcessId.Value;
                else
                    row[DBColumnName.LastUpdatedProcessId] = DBNull.Value;

                table.Rows.Add(row);
            }

            return table;
        }

        private class AccountsActiveItem
        {
            public int AccountId;

            public bool IsActive;

            public long? LastUpdatedProcessId;

        }

        private DataTable ResultSet(int max)
        {
            var items = Enumerable.Range(1, max).Select(i =>
            {
                return new AccountsActiveItem
                {
                    AccountId = i,
                    IsActive = true,
                    LastUpdatedProcessId = i
                };
            });

            return CreateAccountsActiveTypeDataTable(items);
        }
    }
}
