@Context(campaignmanagement)
Enums {
  AdEditorialStatus : string { #SpecialProperty };
  AdStatus : string { #SpecialProperty };
  AdType : string { #SpecialProperty };
  AdSubType : string { #SpecialProperty };
  CallToAction : string { #SpecialProperty };
  LanguageName : string { #SpecialProperty };
  AssetLinkEditorialStatus : string { #SpecialProperty };
  DynamicSearchAdsSource : string { #SpecialProperty };
  CriterionTypeGroup : string { #SpecialProperty };
  BidOption : string { #SpecialProperty };
  HotelAdGroupType : string { #SpecialProperty };
  VanityPharmaDisplayUrlMode : string { #SpecialProperty };
  VanityPharmaWebsiteDescription : string { #SpecialProperty };
  AppStore : string { #SpecialProperty };
  BudgetLimitType : string { #SpecialProperty };
  CampaignStatus : string { #SpecialProperty };
  CampaignType : string { #SpecialProperty };
  CampaignAdditionalField : string { #SpecialProperty };
  AdRotationType : string { #SpecialProperty };
  FrequencyCapTimeGranularity : string { #SpecialProperty };
  Network : string { #SpecialProperty };
  AdGroupPrivacyStatus : string { #SpecialProperty };
  AdGroupStatus : string { #SpecialProperty };
  AdGroupAdditionalField : string { #SpecialProperty };
  CompressionType : string { #SpecialProperty };
  AdAdditionalField : string { #SpecialProperty };
  KeywordEditorialStatus : string { #SpecialProperty };
  MatchType : string { #SpecialProperty };
  KeywordStatus : string { #SpecialProperty };
  KeywordAdditionalField : string { #SpecialProperty };
  EntityType : string { #SpecialProperty };
  AppealStatus : string { #SpecialProperty };
  MigrationStatus : string { #SpecialProperty };
  AccountPropertyName : string { #SpecialProperty };
  Day : string { #SpecialProperty };
  Minute : string { #SpecialProperty };
  AdExtensionStatus : string { #SpecialProperty };
  BusinessGeoCodeStatus : string { #SpecialProperty };
  ActionAdExtensionActionType : string { #SpecialProperty };
  PriceExtensionType : string { #SpecialProperty };
  PriceQualifier : string { #SpecialProperty };
  PriceUnit : string { #SpecialProperty };
  PromotionDiscountModifier : string { #SpecialProperty };
  PromotionOccasion : string { #SpecialProperty };
  AdExtensionHeaderType : string { #SpecialProperty };
  AdExtensionsTypeFilter : string { #SpecialProperty };
  AdExtensionAdditionalField : string { #SpecialProperty };
  AssociationType : string { #SpecialProperty };
  AdExtensionEditorialStatus : string { #SpecialProperty };
  MediaEnabledEntityFilter : string { #SpecialProperty };
  MediaAdditionalField : string { #SpecialProperty };
  AdGroupCriterionType : string { #SpecialProperty };
  CriterionAdditionalField : string { #SpecialProperty };
  ProductPartitionType : string { #SpecialProperty };
  HotelListingType : string { #SpecialProperty };
  HotelDateSelectionType : string { #SpecialProperty };
  WebpageConditionOperand : string { #SpecialProperty };
  WebpageConditionOperator : string { #SpecialProperty };
  AgeRange : string { #SpecialProperty };
  GenderType : string { #SpecialProperty };
  DistanceUnit : string { #SpecialProperty };
  IntentOption : string { #SpecialProperty };
  AudienceType : string { #SpecialProperty };
  ProfileType : string { #SpecialProperty };
  AdGroupCriterionStatus : string { #SpecialProperty };
  AdGroupCriterionEditorialStatus : string { #SpecialProperty };
  ItemAction : string { #SpecialProperty };
  AssetGroupListingType : string { #SpecialProperty };
  AssetGroupListingGroupAdditionalField : string { #SpecialProperty };
  BMCStoreAdditionalField : string { #SpecialProperty };
  BMCStoreSubType : string { #SpecialProperty };
  EntityScope : string { #SpecialProperty };
  CampaignCriterionStatus : string { #SpecialProperty };
  CampaignCriterionType : string { #SpecialProperty };
  PortfolioBidStrategyAdditionalField : string { #SpecialProperty };
  AudienceGroupDimensionType : string { #SpecialProperty };
  AudienceGroupAdditionalField : string { #SpecialProperty };
  GenericEntityStatus : string { #SpecialProperty };
  AssetGroupEditorialStatus : string { #SpecialProperty };
  AssetGroupStatus : string { #SpecialProperty };
  AssetGroupAdditionalField : string { #SpecialProperty };
  NormalForm : string { #SpecialProperty };
  StringOperator : string { #SpecialProperty };
  NumberOperator : string { #SpecialProperty };
  ProductAudienceType : string { #SpecialProperty };
  LogicalOperator : string { #SpecialProperty };
  ImpressionBasedEntityType : string { #SpecialProperty };
  AudienceAdditionalField : string { #SpecialProperty };
  CustomerListActionType : string { #SpecialProperty };
  CustomerListItemSubType : string { #SpecialProperty };
  UetTagTrackingStatus : string { #SpecialProperty };
  ConversionGoalType : string { #SpecialProperty };
  ConversionGoalAdditionalField : string { #SpecialProperty };
  AttributionModelType : string { #SpecialProperty };
  ConversionGoalCountType : string { #SpecialProperty };
  ConversionGoalCategory : string { #SpecialProperty };
  ConversionGoalRevenueType : string { #SpecialProperty };
  ConversionGoalStatus : string { #SpecialProperty };
  ConversionGoalTrackingStatus : string { #SpecialProperty };
  ExpressionOperator : string { #SpecialProperty };
  ValueOperator : string { #SpecialProperty };
  ImportAdditionalField : string { #SpecialProperty };
  ImportEntityType : string { #SpecialProperty };
  DeviceType : string { #SpecialProperty };
  AdRecommendationTextTone : string { #SpecialProperty };
  AdRecommendationAdditionalField : string { #SpecialProperty };
  AdRecommendationImageField : string { #SpecialProperty };
  AdRecommendationTextField : string { #SpecialProperty };
  AdRecommendationVideoType : string { #SpecialProperty };
  AdRecommendationImageRefineType : string { #SpecialProperty };
  LocationType : string { #SpecialProperty };
  ConversionValueRuleOperator : string { #SpecialProperty };
  ConversionValueRuleStatus : string { #SpecialProperty };
}
