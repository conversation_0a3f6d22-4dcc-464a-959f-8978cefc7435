@Context(reporting)
Enums {
  ReportFormat : string { #SpecialProperty };
  ReportAggregation : string { #SpecialProperty };
  AccountPerformanceReportColumn : string { #SpecialProperty };
  AccountStatusReportFilter : string { #SpecialProperty };
  AdDistributionReportFilter : string { #SpecialProperty };
  DeviceOSReportFilter : string { #SpecialProperty };
  DeviceTypeReportFilter : string { #SpecialProperty };
  ReportTimePeriod : string { #SpecialProperty };
  ReportTimeZone : string { #SpecialProperty };
  CampaignPerformanceReportColumn : string { #SpecialProperty };
  CampaignStatusReportFilter : string { #SpecialProperty };
  AdDynamicTextPerformanceReportColumn : string { #SpecialProperty };
  AdGroupStatusReportFilter : string { #SpecialProperty };
  AdStatusReportFilter : string { #SpecialProperty };
  AdTypeReportFilter : string { #SpecialProperty };
  KeywordStatusReportFilter : string { #SpecialProperty };
  LanguageReportFilter : string { #SpecialProperty };
  AdGroupPerformanceReportColumn : string { #SpecialProperty };
  AdPerformanceReportColumn : string { #SpecialProperty };
  KeywordPerformanceReportColumn : string { #SpecialProperty };
  BidMatchTypeReportFilter : string { #SpecialProperty };
  BidStrategyTypeReportFilter : string { #SpecialProperty };
  DeliveredMatchTypeReportFilter : string { #SpecialProperty };
  SortOrder : string { #SpecialProperty };
  DestinationUrlPerformanceReportColumn : string { #SpecialProperty };
  BudgetSummaryReportColumn : string { #SpecialProperty };
  AgeGenderAudienceReportColumn : string { #SpecialProperty };
  ProfessionalDemographicsAudienceReportColumn : string { #SpecialProperty };
  UserLocationPerformanceReportColumn : string { #SpecialProperty };
  PublisherUsagePerformanceReportColumn : string { #SpecialProperty };
  AssetGroupStatusReportFilter : string { #SpecialProperty };
  SearchQueryPerformanceReportColumn : string { #SpecialProperty };
  ConversionPerformanceReportColumn : string { #SpecialProperty };
  GoalsAndFunnelsReportColumn : string { #SpecialProperty };
  NegativeKeywordConflictReportColumn : string { #SpecialProperty };
  SearchCampaignChangeHistoryReportColumn : string { #SpecialProperty };
  ChangeTypeReportFilter : string { #SpecialProperty };
  ChangeEntityReportFilter : string { #SpecialProperty };
  AdExtensionByAdReportColumn : string { #SpecialProperty };
  AdExtensionByKeywordReportColumn : string { #SpecialProperty };
  AudiencePerformanceReportColumn : string { #SpecialProperty };
  AdExtensionDetailReportColumn : string { #SpecialProperty };
  ShareOfVoiceReportColumn : string { #SpecialProperty };
  ProductDimensionPerformanceReportColumn : string { #SpecialProperty };
  ProductPartitionPerformanceReportColumn : string { #SpecialProperty };
  CampaignTypeReportFilter : string { #SpecialProperty };
  ProductPartitionUnitPerformanceReportColumn : string { #SpecialProperty };
  ProductSearchQueryPerformanceReportColumn : string { #SpecialProperty };
  ProductMatchCountReportColumn : string { #SpecialProperty };
  ProductNegativeKeywordConflictReportColumn : string { #SpecialProperty };
  CallDetailReportColumn : string { #SpecialProperty };
  GeographicPerformanceReportColumn : string { #SpecialProperty };
  DSASearchQueryPerformanceReportColumn : string { #SpecialProperty };
  DSAAutoTargetPerformanceReportColumn : string { #SpecialProperty };
  DynamicAdTargetStatusReportFilter : string { #SpecialProperty };
  DSACategoryPerformanceReportColumn : string { #SpecialProperty };
  HotelDimensionPerformanceReportColumn : string { #SpecialProperty };
  HotelGroupPerformanceReportColumn : string { #SpecialProperty };
  AssetGroupPerformanceReportColumn : string { #SpecialProperty };
  SearchInsightPerformanceReportColumn : string { #SpecialProperty };
  AssetPerformanceReportColumn : string { #SpecialProperty };
  CategoryInsightsReportColumn : string { #SpecialProperty };
  CategoryClickCoverageReportColumn : string { #SpecialProperty };
  CombinationPerformanceReportColumn : string { #SpecialProperty };
  AppsPerformanceReportColumn : string { #SpecialProperty };
  FeedItemPerformanceReportColumn : string { #SpecialProperty };
  TravelQueryInsightReportColumn : string { #SpecialProperty };
  ReportRequestStatusType : string { #SpecialProperty };
}
