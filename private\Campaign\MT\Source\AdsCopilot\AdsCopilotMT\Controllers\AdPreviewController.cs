﻿namespace Microsoft.Ads.CopilotMT.Controllers
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AdCenter.Shared.Logging.KustoLogProvider;
    using Microsoft.Ads.AdPreview;
    using Microsoft.Ads.AdPreview.Models;
    using Microsoft.Ads.CopilotMT.Model;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.RateLimiting;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;

    [AllowAnonymous]
    [Route("api/asset")]
    [ApiController]
    [EnableRateLimiting(BotConstants.IP_RATE_LIMIT_POLICY)]
    public class AdPreviewController : ControllerBase
    {
        private readonly ILogger<AdPreviewController> logger;
        private readonly AdPreviewProcessor processor;
        private DynamicConfig dynamicConfig;

        public AdPreviewController(
            AdPreviewProcessor processor,
            IOptionsMonitor<DynamicConfig> dynamicConfigMonitor,
            ILogger<AdPreviewController> logger)
        {
            this.logger = logger;
            this.processor = processor;

            // Fetches the current values of the Dynamic Config using IOptionsSnapshot
            dynamicConfig = dynamicConfigMonitor.CurrentValue;
            dynamicConfigMonitor.OnChange((config, args) =>
            {
                dynamicConfig = config;
                logger.ConfigChangeLogger(config);
            });
        }

        [HttpGet]
        public async Task<IActionResult> GetAsync(
            [FromQuery] string lookupId,
            [FromQuery] string email,
            [FromQuery] string signature,
            [FromQuery] bool useMock)
        {
            logger.LogInformation("AdPreviewController.Get called with lookupId: {lookupId}, email: {email}, signature: {signature}", lookupId, email, signature);
            logger.LogProperty("LookupId", lookupId);
            if (dynamicConfig.TurnOffAdPreview)
            {
                return CreateTurnOffResponse();
            }

            if (string.IsNullOrEmpty(email) && string.IsNullOrEmpty(signature))
            {
                return processor.LookUp(lookupId, useMock);
            }

            // User is continuing signup from email
            logger.LogProperty("Email", email);
            return await processor.ContinueSignupAsync(lookupId, signature, email).ConfigureAwait(false);
        }

        [HttpPost]
        public async Task<IActionResult> PostAsync(
            [FromBody] AdPreviewRequest previewRequest,
            [FromQuery] bool useMock)
        {
            logger.LogProperty("Website", previewRequest?.Website);
            logger.LogProperty("Prompt", previewRequest?.Prompt);
            logger.LogInformation("AdPreviewController.Post called");

            if (dynamicConfig.TurnOffAdPreview)
            {
                return CreateTurnOffResponse();
            }

            return await processor.GetRecommendationsAsync(previewRequest, useMock).ConfigureAwait(false);
        }

        [HttpPost]
        [Route("onboard")]
        public async Task<IActionResult> OnboardAsync(
            [FromQuery] string lookupId, 
            [FromQuery] string resumeUrl,
            [FromQuery] int emailVersion,
            [FromQuery] bool useMock,
            [FromBody] UserDetails userDetails)
        {
            logger.LogProperty("Email", userDetails?.Email);
            logger.LogInformation("AdPreviewController.Onboard called with lookupId: {lookupId} payload: {payload}", lookupId, userDetails);
            if (dynamicConfig.TurnOffAdPreview)
            {
                return CreateTurnOffResponse();
            }

            return await processor.OnboardUserAsync(lookupId, resumeUrl, userDetails, emailVersion, useMock).ConfigureAwait(false);
        }

        private IActionResult CreateTurnOffResponse()
        {
            logger.LogInformation("API is turned off, returning ServiceUnavailable");
            var error = new ProblemDetails
            {
                Title = "ServiceUnavailable",
                Detail = "API has been turned off"
            };

            return new ObjectResult(error)
            {
                StatusCode = (int)HttpStatusCode.ServiceUnavailable
            };
        }
    }
}
