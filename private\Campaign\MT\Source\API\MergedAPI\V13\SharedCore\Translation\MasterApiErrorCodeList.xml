﻿<?xml version="1.0" encoding="utf-8" ?>

<ApiErrorCodes>
  <ErrorCodesByCategory>
    <category name="Common">
      <error name="InternalError" errorcode="InternalError" code="0" message="An internal error has occurred." />
      <error name="NullRequest" errorcode="NullRequest" code="100" message="The request message is null." />
      <error name="LoginFailed" errorcode="InvalidCredentials" code="105" message="Authentication failed. Either supplied credentials are invalid or the account is inactive." />
      <error name="UserIsNotAuthorized" errorcode="UserIsNotAuthorized" code="106" message="The user does not represent a authorized developer." />
      <error name="QuotaNotAvailable" errorcode="QuotaNotAvailable" code="107" message="There is not enough quota available for the authorized developer." />
      <error name="InvalidDateObject" errorcode="InvalidDateObject" code="113" message="The Date object specified in the request is not valid." />
      <error name="RequestMissingHeaders" errorcode="RequestMissingHeaders" code="116" message="Required headers are missing in the request." />

      <!--General api parameter validation errors-->
      <error name="ApiInputValidationError" errorcode="ApiInputValidationError" code="201" message="One or more input elements failed validation." />
      <error name="MTBusinessOperationError" errorcode="ApiExecutionError" code="202" message="The operation cannot be completed by backend." />
      <error name="NullParameter" errorcode="NullParameter" code ="203" message="The parameter or field must not be null." />
      <error name="OperationNotSupported" errorcode="OperationNotSupported" code="204" message="This operation is not supported." />
      <error name="InvalidVersion" errorcode="InvalidVersion" code="205" message="This is not a valid version." />
      <error name="NullArrayArgument" errorcode="NullArrayArgument" code="206" message="Null array argument is not allowed." />
      <error name="ConcurrentRequestOverLimit" errorcode="ConcurrentRequestOverLimit" code ="207" message="The maximum number of allowed concurrent requests has been exceeded." />
      <error name="InvalidAccount" errorcode="InvalidAccount" code="208" message="The account is invalid." />
      <error name="TimestampNotMatch" errorcode="TimestampNotMatch" code="209" message="The timestamp does not match."/>
      <error name="EntityNotExistent" errorcode="EntityNotExistent" code="210" message="The entity does not exist." />
      <error name="NameTooLong" errorcode="NameTooLong" code="211" message="The specified name is too long." />
      <error name="EntityTypeNotSupported" errorcode="CampaignServiceEntityTypeNotSupported" code="212" message="The specified entity type is not supported for this operation." />
      <error name="InvalidParameters" errorcode="InvalidParameters" code ="213" message="The input contains invalid parameters." />
      <error name="ApiVersionNoLongerSupported" errorcode="ApiVersionNoLongerSupported" code="303" message="This version of the Bing Ads API is no longer supported. Please migrate to the latest version of the API. For more details, visit https://go.microsoft.com/fwlink/?linkid=862106." />

      <error name="FilterListOverLimit" errorcode="FilterListOverLimit" code="512" message="The associated filter list is over size limit." />
      <error name="EntityIdsArrayExceedsLimit" errorcode="CampaignServiceEntityIdsArrayExceedsLimit" code="513" message="The list size is over limit."/>
      <error name="IdsNotPassed" errorcode="IdsNotPassed" code="514" message="Ids passed are null or empty." />
      <error name="DuplicateId" errorcode="DuplicateId" code="515" message="Duplicate IDs are contained in the request."/>
      <error name="EntityIdFilterMismatch" errorcode="EntityIdFilterMismatch" code="516" message="The type of the entity that was specified by ID does not match provided entity filter type."/>
      <error name="EntityIdInvalid" errorcode="CampaignServiceEntityIdInvalid " code="517" message="The entity identifier is invalid."/>
      <error name="SearchStringNotSufficient" errorcode="SearchStringNotSufficient" code="518" message="Search string is not sufficient."/>
    </category>

    <category name="CampaignManagement">
      <!--Common-->
      <error name="CannotChangeStatusOnUpdate" errorcode="CampaignServiceCannotChangeStatusOnUpdate" code="1001" message="Status cannot be changed on Update; instead, use Pause or Resume APIs."/>
      <error name="CannotSpecifyStatusOnAdd" errorcode="CampaignServiceCannotSpecifyStatusOnAdd" code="1003" message="Status should be null on Add operations."/>
      <error name="IdShouldBeNullOnAdd" errorcode="CampaignServiceIdShouldBeNullOnAdd" code="1004" message="ID should be null on Add operations."/>
      <error name="InvalidNegativeKeyword" errorcode="CampaignServiceInvalidNegativeKeyword" code="1005" message="The negative keyword is invalid."/>
      <error name="NegativeKeywordsTotalLengthExceeded" errorcode="CampaignServiceNegativeKeywordsTotalLengthExceeded" code="1006" message="The total length of the negative keywords has been exceeded."/>
      <error name="NegativeKeywordMatchesKeyword" errorcode="CampaignServiceNegativeKeywordMatchesKeyword" code="1007" message="The negative keyword matches a keyword."/>
      <error name="InvalidAccountStatus" errorcode="CampaignServiceInvalidAccountStatus" code="1008" message="The account status is invalid for the current operation."/>
      <error name="AccountIdMissingInRequestHeader" errorcode="CampaignServiceAccountIdMissingInRequestHeader" code="1009" message="For aggregator users account id of end customer should be supplied in request header."/>
      <error name="SystemInReadOnlyMode" errorcode="CampaignServiceSystemInReadOnlyMode" code="1010" message="The system is currently in read-only mode (only Get operations are supported)."/>
      <error name="FutureFeatureCode" errorcode="CampaignServiceFutureFeatureCode" code="1011" message="An internal error has occurred."/>
      <error name="InvalidNegativeSiteURL" errorcode="CampaignServiceInvalidNegativeSiteURL" code="1012" message="The negative site url is invalid."/>
      <error name="NegativeSiteURLExceededMaxCount" errorcode="CampaignServiceNegativeSiteURLExceededMaxCount" code="1013" message="Negative site urls exceeded maximum allowed limit."/>
      <error name="TimeZoneValueInvalid" errorcode="CampaignServiceTimeZoneValueInvalid" code="1014" message="Passed TimeZone value is invalid. Please refer to documentation for list of valid values."/>
      <error name="CurrencyValueInvalid" errorcode="CampaignServiceCurrencyValueInvalid" code="1015" message="Passed Currency value is invalid. Please refer to documentation for list of valid values."/>
      <error name="InvalidEntityState" code="1016" errorcode="CampaignServiceInvalidEntityState" message="Passed entity state is invalid. Please refer to documentation for list of valid values for given entity."/>
      <error name="NegativeKeywordsLimitExceeded" errorcode="CampaignServiceNegativeKeywordsLimitExceeded" code="1032" message="Negative keywords exceeded maximum allowed limit."/>
      <error name="NegativeKeywordsEntityLimitExceeded" errorcode="CampaignServiceNegativeKeywordsEntityLimitExceeded" code="1033" message="Number of entities with maximum negative keywords has exceeded limit."/>
      <error name="NegativeKeywordsNotPassed" errorcode="CampaignServiceNegativeKeywordsNotPassed" code="1034" message="Negative keywords passed is null."/>
      <error name="NegativeSiteCannotBeOwnedOrOperatedSite" errorcode="CampaignServiceNegativeSiteCannotBeOwnedOrOperatedSite" code="1035" message="Negative site specified cannot be an owned or operated site."/>
      <error name="NegativeSiteURLExceedMaxSubDirectories" errorcode="CampaignServiceNegativeSiteURLExceedMaxSubDirectories" code="1036" message="Negative site specified exceeds maximum number of subdirectories."/>
      <error name="NegativeSiteURLExceedMaxSubDomains" errorcode="CampaignServiceNegativeSiteURLExceedMaxSubDomains" code="1037" message="Negative site specified exceeds maximum number of subdomains."/>
      <error name="InvalidCustomerStatus" errorcode="CampaignServiceInvalidCustomerStatus" code="1038" message="The customer status is invalid for the current operation."/>
      <error name="NegativeKeywordHasInvalidMatchTypeFormat" errorcode="CampaignServiceNegativeKeywordHasInvalidMatchTypeFormat" code="1039" message="Negative keyword has invalid match type format."/>
      <error name="NegativeSiteEntityLimitExceeded" errorcode="CampaignServiceNegativeSiteEntityLimitExceeded" code="1040" message="The size of the response exceeds the maximum allowed. Please decrease the size of your request and try again."/>
      <error name="DevicePreferenceNotSupported" errorcode="DevicePreferenceNotSupported" code="1041" message="The Device Preference property is not supported."/>
      <error name="EditorialValidationError" errorcode="CampaignServiceEditorialValidationError" code="1042" message="The specified entity did not pass editorial validation. Please see the ReasonCode element of this error object for details."/>
      <error name="EntityAlreadyExists" errorcode="CampaignServiceEntityAlreadyExists" code="1043" message="The specified entity already exists. Please see the ReasonCode element of this error object for details."/>
      <error name="EntityDoesNotExist" errorcode="CampaignServiceEntityDoesNotExist" code="1044" message="The specified entity doesnot exist. Please see the ReasonCode element of this error object for details."/>
      <error name="MaxLimitReached" errorcode="CampaignServiceEntityMaxLimitReached" code="1045" message="The system limit for the submitted entity type has already been reached. Please see the ReasonCode element of this error object for details."/>
      <error name="EntityIdNotPassed" errorcode="CampaignServiceEntityIdNotPassed" code="1046" message="The entity ID field cannot be null or empty.. Please see the ReasonCode element of this error object for details."/>
      <error name="InvalidCpcBids" errorcode="CampaignServiceInvalidCpcBids" code="1047" message="The CPC bids associated with the entity are not valid."/>
      <error name="CustomerNotInLanguagePilot" errorcode="CustomerNotInLanguagePilot" code="526" message="The customer is not in pilot for this language."/>
      <error name="InvalidCustomerAccountShare" errorcode="CampaignServiceInvalidCustomerAccountShare" code="1048" message="You cannot share with the customer or account."/>
      <error name="CustomerAccountSharePermissionDenied" errorcode="CampaignServiceCustomerAccountSharePermissionDenied" code="1049" message="You don't have permission for this operation."/>

      <!-- Cashback -->
      <error name="CashbackAllowedOnlyForSearchMedium" errorcode="CampaignServiceCashbackAllowedOnlyForSearchMedium" code="1019" message="Invalid Medium for Cashback."/>
      <error name="CashbackNotAllowedForAdgroupsDistributionChannel" errorcode="CampaignServiceCashbackNotAllowedForAdgroupsDistributionChannel" code="1020" message="Invalid Distribution channel for Cashback."/>
      <error name="AccountNotEligbleForKeywordLevelCashback" errorcode="CampaignServiceAccountNotEligbleForKeywordLevelCashback" code="1021" message="Account not eligble for Keyword level Cashback."/>
      <error name="CampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup" errorcode="CampaignServiceCampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup" code="1022" message="Parent entity needs to enable Cashback before enabling at the child level."/>
      <error name="AccountNotEligibleToModifyCashBack" errorcode="CampaignServiceAccountNotEligibleToModifyCashBack" code="1023" message="Account not eligible for CashBack."/>
      <error name="AccountNotEligibleToSetCashbackAmount" errorcode="CampaignServiceAccountNotEligibleToSetCashbackAmount" code="1024" message="Account not eligible for CashBack Amount."/>
      <error name="InvalidCashbackAmount" errorcode="CampaignServiceInvalidCashbackAmount" code="1025" message="Invalid Cashback amount."/>
      <error name="CashbackTextTooLong" errorcode="CampaignServiceCashbackTextTooLong" code="1026" message="Invalid Cashback text."/>
      <error name="CashBackStatusRequired" errorcode="CampaignServiceCashBackStatusRequired" code="1027" message="Invalid Cashback status."/>
      <error name="CashbackInfoShouldBeNullForBackwardCompatability" errorcode="CampaignServiceCashbackInfoShouldBeNullForBackwardCompatability" code="1028" message="Cashback information should be null for backwards compatibility."/>
      <error name="CustomerIdHasToBeSpecified" errorcode="CampaignServiceCustomerIdHasToBeSpecified" code="1029" message="Customer Id has to be specified for this operation."/>
      <error name="AccountIdHasToBeSpecified" errorcode="CampaignServiceAccountIdHasToBeSpecified" code="1030" message="Account Id has to be specified for this operation."/>
      <error name="CannotPerformCurrentOperation" errorcode="CampaignServiceCannotPerformCurrentOperation" code="1031" message="The current operation cannot be completed due to some invalid customer or user information provided."/>

      <!--Campaign-->
      <error name="InvalidCampaignId" errorcode="CampaignServiceInvalidCampaignId" code="1100" message="The campaign ID is invalid."/>
      <error name="InvalidCampaignName" errorcode="CampaignServiceInvalidCampaignName" code="1101" message="The campaign name is invalid."/>
      <error name="InvalidAccountId" errorcode="CampaignServiceInvalidAccountId" code="1102" message="The account ID is invalid."/>
      <error name="NullCampaign" errorcode="CampaignServiceNullCampaign" code="1103" message="Campaign is null."/>
      <error name="InvalidMonthlyBudget" errorcode="CampaignServiceInvalidMonthlyBudget" code="1105" message="The campaign monthly budget is invalid."/>
      <error name="InvalidDailyBudget" errorcode="CampaignServiceInvalidDailyBudget" code="1106" message="The campaign daily budget is invalid."/>
      <error name="DuplicateInCampaignIds" errorcode="CampaignServiceDuplicateCampaignIds" code="1107" message="Duplicate IDs are contained in the array of campaigns."/>
      <error name="InvalidTimeZone" errorcode="CampaignServiceTimeZoneNotEnabled" code="1109" message="TimeZoneType Enabled should be set for the campaign."/>
      <error name="InvalidDaylightSaving" errorcode="CampaignServiceDaylightSavingNotEnabled" code="1110" message="Daylight saving time should be set for campaign."/>
      <error name="ConversionTrackingScriptNonNull" errorcode="CampaignServiceInvalidConversionTrackingScriptSet" code="1111" message="Conversion Tracking Script should not be set for the campaign."/>
      <error name="CampaignsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceCampaignsArrayShouldNotBeNullOrEmpty" code="1113" message="Campaigns array should not be null or empty."/>
      <error name="CampaignsArrayExceedsLimit" errorcode="CampaignServiceCampaignsArrayExceedsLimit" code="1114" message="The list of campaigns exceeds the limit."/>
      <error name="DuplicateCampaign" errorcode="CampaignServiceCannotCreateDuplicateCampaign" code="1115" message="Trying to create a duplicate campaign."/>
      <error name="MaxCampaignsReached" errorcode="CampaignServiceMaximumCampaignsReached" code="1117" message="Cannot add any more campaigns to this account."/>
      <error name="CampaignIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceCampaignIdsArrayShouldNotBeNullOrEmpty" code="1118" message="Campaign Ids array should not be null or empty."/>
      <error name="CampaignIdsArrayExceedsLimit" errorcode="CampaignServiceCampaignIdsArrayExceedsLimit" code="1119" message="The list of campaign Ids exceeds the limit."/>
      <error name="InvalidCampaignStatus" errorcode="CampaignServiceInvalidCampaignStatus" code="1120" message="The campaign status is invalid for the current operation."/>
      <error name="InvalidBudgetType" errorcode="CampaignServiceInvalidBudgetType" code="1121" message="The campaign budget type is invalid or not specified"/>
      <error name="CampaignNotEligibleForCashBack" errorcode="CampaignServiceCampaignNotEligibleForCashBack" code="1122" message="The campaign is not eligible for cashback"/>
      <error name="CampaignBudgetAmountIsLessThanSpendAmount" errorcode="CampaignServiceCampaignBudgetAmountIsLessThanSpendAmount" code="1123" message="The monthly campaign budget is less than the amount already spent for the current month."/>
      <error name="CampaignAlreadyExists" errorcode="CampaignServiceCampaignAlreadyExists" code="1129" message="The campaign already exists."/>
      <error name="NullCampaignNegativeKeywords" errorcode="CampaignServiceNullCampaignNegativeKeywords" code="1130" message="CampaignNegativeKeywords is null."/>
      <error name="CannotChangeTimezoneOrStartDateWithActiveAdGroups" errorcode="CampaignServiceCannotChangeTimezoneOrStartDateWithActiveAdGroups" code="1131" message="Campaign Timezone/Start Date are not updatable as not all AdGroups are in draft state."/>
      <error name="NullCampaignNegativeSites" errorcode="CampaignServiceNullCampaignNegativeSites" code="1132" message="CampaignSiteExclusions is null." />
      <error name="AdExtensionsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdExtensionsArrayShouldNotBeNullOrEmpty" code="1133" message="AdExtensions property is null." />
      <error name="AdExtensionIsNull" errorcode="CampaignServiceAdExtensionIsNull" code="1134" message="One of AdExtensions is null." />
      <error name="AdExtensionsEntityLimitExceeded" errorcode="CampaignServiceAdExtensionsEntityLimitExceeded" code="1135" message="AdExtensions array exceeds the limit." />

      <error name="PhoneExtensionDataNotNull" errorcode="CampaignServicePhoneExtensionDataNotNull" code="1136" message="PhoneExtension data should be empty (null) when disabling phone extension." />
      <error name="CustomerNotEligibleToSetClickToCallOnly" errorcode="CampaignServiceCustomerNotEligibleToSetClickToCallOnly" code="1137" message="Customer account is not enabled to set PhoneExtension.EnableClickToCallOnly." />
      <error name="PhoneExtensionInvalidCountry" errorcode="CampaignServicePhoneExtensionInvalidCountry" code="1138" message="PhoneExtension.Country should not be null." />
      <error name="PhoneExtensionPhoneNumberHasInvalidChars" errorcode="CampaignServicePhoneExtensionPhoneNumberHasInvalidChars" code="1139" message="PhoneExtension.Phone is invalid." />
      <error name="PhoneExtensionPhoneNumberMissing" errorcode="CampaignServicePhoneExtensionPhoneNumberMissing" code="1140" message="PhoneExtension.Phone should not be null." />
      <error name="PhoneExtensionPhoneNumberTooLong" errorcode="CampaignServicePhoneExtensionPhoneNumberTooLong" code="1141" message="PhoneExtension.Phone length exceeds the limit." />
      <error name="CustomerNotEligibleToSetAdExtension" errorcode="CampaignServiceCustomerNotEligibleToSetAdExtension" code="1142" message="Customer account is not enabled to set AdExtension." />
      <error name="PhoneExtensionPhoneNumberInvalid" errorcode="CampaignServicePhoneExtensionPhoneNumberInvalid" code="1143" message="Phone number provided in the PhonExtension is invalid." />
      <error name="BusinessLocationReadOnlyForLocationAdExtensionV2Pilot" errorcode="CampaignServiceBusinessLocationReadOnlyForLocationAdExtensionV2Pilot" code="1144" message="Business locations are read-only and cannot be added, updated, or deleted." />
      <error name="LocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot" errorcode="CampaignServiceLocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot" code="1145" message="Location extensions v1 are read-only and cannot be enabled or disabled." />
      <error name="PhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot" errorcode="CampaignServicePhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot" code="1146" message="Phone extensions v1 are read-only and cannot be enabled, disabled or updated." />
      <error name="CampaignServiceInvalidKeywordVariantMatchEnabledValue" errorcode="CampaignServiceInvalidKeywordVariantMatchEnabledValue" code="1147" message="The value specified for Keyword Variant Match Type is Invalid." />

      <error name="DuplicateSettingsInEntity" errorcode="CampaignServiceDuplicateSettingsInEntity" code="1149" message="You may not specify duplicate settings for an entity." />
      <error name="CampaignSettingsRequired" errorcode="CampaignServiceCampaignSettingsRequired" code="1150" message="The required campaign settings were not specified." />
      <error name="InvalidSettingForCampaignType" errorcode="CampaignServiceInvalidSettingForCampaignType" code="1151" message="One or more settings are not supported for the campaign type." />
      <error name="ShoppingCampaignPriorityInvalid" errorcode="CampaignServiceShoppingCampaignPriorityInvalid" code="1152" message="The priority of the shopping campaign is invalid." />
      <error name="ShoppingCampaignSalesCountryCodeInvalid" errorcode="CampaignServiceShoppingCampaignSalesCountryCodeInvalid" code="1153" message="The sales country/region code of the shopping campaign is invalid." />
      <error name="ShoppingCampaignStoreIdInvalid" errorcode="CampaignServiceCampaignShoppingCampaignStoreIdInvalid" code="1154" message="The store ID of the shopping campaign is invalid." />
      <error name="CampaignTypeImmutable" errorcode="CampaignServiceCampaignTypeImmutable" code="1155" message="The campaign type field of a campaign cannot be updated." />
      <error name="SalesCountryCodeImmutable" errorcode="CampaignServiceSalesCountryCodeImmutable" code="1156" message="The sales country/region code of a shopping campaign cannot be updated." />
      <error name="StoreIdImmutable" errorcode="CampaignServiceStoreIdImmutable" code="1157" message="The store ID of a shopping campaign cannot be updated." />
      <error name="CampaignTypeLimitExceeded" errorcode="CampaignServiceCampaignTypeLimitExceeded" code="1158" message="You can specify a maximum of one campaign type for a campaign." />
      <error name="CannotUpdateSharedBudget" errorcode="CampaignServiceCannotUpdateSharedBudget" code="1159" message="You cannot update shared budget through Campaign Entity, please use Budget entity to update shared budget." />
      <error name="MonthlyBudgetNotSupported" errorcode="CampaignServiceMonthlyBudgetNotSupported" code="1160" message="Monthly budget is deprecated and cannot be retrieved through this API version." />

      <error name="ShoppingCampaignSubTypeInvalid" errorcode="CampaignServiceShoppingCampaignSubTypeInvalid" code="1161" message="The subtype of the shopping campaign is invalid." />
      <error name="CoOpCampaignShouldNotBeReturned" errorcode="CampaignServiceCoOpCampaignShouldNotBeReturned" code="1162" message="Users need to ask for Co-Op campaigns explicitly." />
      <error name="CampaignInvalidTargetSetting" errorcode="CampaignInvalidTargetSetting" code="1163" message="The campaign target setting is invalid." />
      <error name="CustomerNotEnabledForSponsoredProductAdsV2" errorcode="CustomerNotEnabledForSponsoredProductAdsV2" code="1164" message="Customer is not enabled for Sponsored Product Ads V2 pilot." />
      <error name="CustomerNotEnabledForSmartShoppignCampaign" errorcode="CustomerNotEnabledForSmartShoppignCampaign" code="1165" message="Customer is not enabled for Smart Shopping Campaign V2 pilot." />
      <error name="CustomerHasNoUETForMaxConversionValueBiddingScheme" errorcode="CustomerHasNoUETForMaxConversionValueBiddingScheme" code="1166" message="Customer do not setup UET tag for Max Conversion Value Bidding Scheme." />
      <error name="AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme" errorcode="AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme" code="1167" message="Customer do not setup Conversion goal with revenue for Max Conversion Value Bidding Scheme." />
      <error name="SmartShoppingCampaignLimitExceeded" errorcode="SmartShoppingCampaignLimitExceeded" code="1168" message="At most support 100 smart shopping campaigns under singel account." />
      <error name="ShoppingSmartAdsBidAdjustmentNotSupported" errorcode="ShoppingSmartAdsBidAdjustmentNotSupported" code="1169" message="Bid Adjustments are not supported for this criterion type in a Smart Shopping campaign." />
      <error name="ShoppingSmartAdsEntityNotSupported" errorcode="ShoppingSmartAdsEntityNotSupported" code="1170" message="This entity type is not supported for Smart Shopping campaigns." />
      <error name="SmartShoppingUpdateInvalidDeleteItIfWantImportSuccess" errorcode="SmartShoppingUpdateInvalidDeleteItIfWantImportSuccess" code="1171" message="Update campaign priority to highest(3) failed. If it is for smart shopping campaign google Import, you can delete this campaign in bing ads first, then  import this campaign as new one." />
      <error name="ShoppingCampaignStoreIdInferredFromMapping" errorcode="ShoppingCampaignStoreIdInferredFromMapping" code="1172" message="Store not specified in Import Options. Suggested store selected for imported shopping campaigns." />
      <error name="CampaignHasDraftStore" errorcode="CampaignHasDraftStore" code="1173" message="This campaign has a draft store. Complete store setup to run this campaign." />
      <error name="CampaignServiceKeywordVariantMatchNotEnabledForPilot" errorcode="CampaignServiceKeywordVariantMatchNotEnabledForPilot" code="3510" message="Customer not in Pilot to use Keyword Variant Match Type Feature" />
      <error name="CampaignSubtypeNotAllowedInCampaignType" errorcode="CampaignSubtypeNotAllowedInCampaignType" code="3511" message="The campaign subtype is not allowed in current campaign type." />
      <error name="AccountNotEnabledForVideoCampaign" errorcode="AccountNotEnabledForVideoCampaign" code="3512" message="The account is not enabled to create video campaign." />
      <error name="AccountNotEnabledForCustomVideoAssetThumbnail" errorcode="AccountNotEnabledForCustomVideoAssetThumbnail" code="3513" message="The account is not in the pilot to customize thumbnail in video assets."/>
      <error name="CustomerNotEnabledForMultiChannelCampaign" errorcode="CustomerNotEnabledForMultiChannelCampaign" code="67312" message="The customer is not in the pilot to enable multi channel campaign."/>
      <error name="SmartShoppingCampaignCreationNotSupported" errorcode="SmartShoppingCampaignCreationNotSupported" code="1174" message="The creation of new Smart Shopping Campaigns is currently not supported." />
      <error name="LegacyWinstoreAdsCampaignCreationNotSupported" errorcode="LegacyWinstoreAdsCampaignCreationNotSupported" code="1175" message="The creation of Legacy Winstore Ads Campaigns is deprecated." />
      <error name="PlacementTargetingNotSupported" errorcode="PlacementTargetingNotSupported" code="1176" message="Placement targeting not supported without pilot" />

      <!--AdGroup-->
      <error name="NullAdGroup" errorcode="CampaignServiceNullAdGroup" code="1200" message="AdGroup is null."/>
      <error name="InvalidAdGroupId" errorcode="CampaignServiceInvalidAdGroupId" code="1201" message="The AdGroup ID is invalid."/>
      <error name="InvalidAdGroupName" errorcode="CampaignServiceInvalidAdGroupName" code="1202" message="The AdGroup name is invalid."/>
      <error name="DuplicateInAdGroupIds" errorcode="CampaignServiceDuplicateInAdGroupIds" code="1203" message="Duplicate IDs are contained in the array of ad groups."/>
      <error name="AdGroupEndDateShouldBeAfterStartDate" errorcode="CampaignServiceAdGroupEndDateShouldBeAfterStartDate" code="1204" message="The AdGroup End date should be after the Start date."/>
      <error name="CannotUpdateLanguageAndRegion" errorcode="CampaignServiceCannotUpdateLanguageAndRegion" code="1205" message="The AdGroup LanguageAndRegion cannot be updated."/>
      <error name="CampaignBudgetLessThanAdGroupBudget" errorcode="CampaignServiceCampaignBudgetLessThanAdGroupBudget" code="1208" message="The AdGroup's budget is more than the campaign's budget."/>
      <error name="AdGroupsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdGroupsArrayShouldNotBeNullOrEmpty" code="1209" message="AdGroups array should not be null or empty."/>
      <error name="AdGroupsArrayExceedsLimit" errorcode="CampaignServiceAdGroupsArrayExceedsLimit" code="1210" message="The list of adGroups exceeds the limit."/>
      <error name="AdGroupUserNotAllowedContentMedium" errorcode="CampaignServiceAdGroupUserNotAllowedContentMedium" code="1211" message="The AdGroup user cannot use Content Medium."/>
      <error name="AdGroupStartDateLessThanCurrentDate" errorcode="CampaignServiceAdGroupStartDateLessThanCurrentDate" code="1212" message="The AdGroup Start Date should be after current date."/>
      <error name="MaxAdGroupsReached" errorcode="CampaignServiceMaximumAdGroupsReached" code="1213" message="Cannot add any more adgroups to this campaign."/>
      <error name="DuplicateAdGroup" errorcode="CampaignServiceCannotCreateDuplicateAdGroup" code="1214" message="Trying to create a duplicate adgroup."/>
      <error name="CannotUpdateAdGroupInExpiredState" errorcode="CampaignServiceCannotUpdateAdGroupInExpiredState" code="1215" message="Cannot update adgroup which is in expired state."/>
      <error name="CannotUpdateAdGroupInSubmittedState" errorcode="CampaignServiceCannotUpdateAdGroupInSubmittedState" code="1216" message="Cannot update adgroup which is in submitted state."/>
      <error name="CannotOperateOnAdGroupInCurrentState" errorcode="CampaignServiceCannotOperateOnAdGroupInCurrentState" code="1217" message="Cannot operate on adgroup in current state."/>
      <error name="AdGroupIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdGroupIdsArrayShouldNotBeNullOrEmpty" code="1218" message="AdGroup Ids array should not be null or empty."/>
      <error name="AdGroupIdsArrayExceedsLimit" errorcode="CampaignServiceAdGroupIdsArrayExceedsLimit" code="1219" message="The list of adGroup Ids exceeds the limit."/>
      <error name="MissingDistributionChannel" errorcode="CampaignServiceMissingDistributionChannel" code="1220" message="The LanguageAndRegion property, representing the distribution channel, is missing."/>
      <error name="AdGroupInvalidDistributionChannel" errorcode="CampaignServiceAdGroupInvalidDistributionChannel" code="1221" message="The AdGroup.LanguageAndRegion value is invalid."/>
      <error name="AdGroupInvalidMedium" errorcode="CampaignServiceAdGroupInvalidMedium" code="1222" message="The AdGroup.Medium value is invalid."/>
      <error name="AdGroupMediumNotAllowedForDistributionChannel" errorcode="CampaignServiceAdGroupMediumNotAllowedForDistributionChannel"  code="1223" message="The Content AdDistribution is not enabled for your market."/>
      <error name="AdGroupMissingAdMedium" errorcode="CampaignServiceAdGroupMissingAdMedium" code="1224" message="The medium is required value while creating a new ad group."/>
      <error name="UserNotAuthorizedForDistributionChannel" errorcode="CampaignServiceUserNotAuthorizedForDistributionChannel" code="1225" message="The user is not authorized to use the specified LanguageAndRegion for the ad group."/>
      <error name="NeedAtleastOneAdAndOneKeywordToSubmit" errorcode="CampaignServiceNeedAtleastOneAdAndOneKeywordToSubmit" code="1226" message="An ad group can be submitted only if there is an ad and keyword associated with it."/>
      <error name="AdGroupStartDateCannotBeEarlierThanSubmitDate" errorcode="CampaignServiceAdGroupStartDateCannotBeEarlierThanSubmitDate" code="1227" message="An ad group's start date cannot be earlier than the submitted date."/>
      <error name="CannotSetPricingModelOnAdGroup" errorcode="CampaignServiceCannotSetPricingModelOnAdGroup" code="1228" message="Pricing Model is not currently supported on the AdGroup."/>
      <error name="AdGroupExpired" errorcode="CampaignServiceAdGroupExpired" code ="1231" message="Operation not valid for an expired ad group"/>
      <error name="AdGroupInvalidStartDate" errorcode="CampaignServiceAdGroupInvalidStartDate" code ="1232" message="The start date specified in the ad group is not valid."/>
      <error name="AdGroupInvalidEndDate" errorcode="CampaignServiceAdGroupInvalidEndDate" code ="1233" message="The end date specified in the ad group is not valid."/>
      <error name="AdGroupPricingModelCpmRequiresContentMedium" errorcode="CampaignServiceAdGroupPricingModelCpmRequiresContentMedium" code ="1234" message="Pricing Model CPM is allowed only if the medium is Content for the Ad Group."/>
      <error name="AdGroupInvalidMediumForCustomer" errorcode="CampaignServiceAdGroupInvalidMediumForCustomer" code ="1235" message="Customer account is not enabled to create adgroups in this medium."/>
      <error name="AdGroupPricingModelCpmIsNotEnabledForCustomer" errorcode="CampaignServiceAdGroupPricingModelCpmIsNotEnabledForCustomer" code ="1236" message="Customer account is not enabled to create adgroups with Pricing Model CPM."/>
      <error name="AdGroupPricingModelIsNull" errorcode="CampaignServiceAdGroupPricingModelIsNull" code ="1237" message="PricingModel field is empty. Please set appropriate value when creating an AdGroup entity."/>
      <error name="TypeCanBeBehavioralBidOnlyForContentAdGroups" errorcode="CampaignServiceTypeCanBeBehavioralBidOnlyForContentAdGroups" code ="1239" message="Type can be set to BehavioralBid for only content adgroups."/>
      <error name="CannotUpdateBiddingModel" errorcode="CampaignServiceCannotUpdateBiddingModel" code ="1241" message="Cannot update bidding model of an adgroup."/>
      <error name="CannotUpdateAdDistributionForThisType" errorcode="CampaignServiceCannotUpdateAdDistributionForThisType" code ="1242" message="Cannot update the AdDistribution for this type of adgroup."/>
      <error name="TooManyAdGroupsInAccount" errorcode="CampaignServiceTooManyAdGroupsInAccount" code ="1243" message="Adgroups limit per account is exceeded."/>
      <error name="NullAdGroupNegativeKeywords" errorcode="AdGroupServiceNullAdGroupNegativeKeywords" code="1244" message="AdGroupNegativeKeywords is null."/>
      <error name="NegativeSiteUrlsNotPassed" errorcode="AdGroupServiceNegativeSiteUrlsNotPassed" code="1245" message="AdGroupNegative site Urls is null."/>
      <error name="AdGroupNetworksArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdGroupNetworksArrayShouldNotBeNullOrEmpty" code="1246" message="AdGroupNetworks array should not be null or empty."/>
      <error name="AdGroupNetworksArrayExceedsLimit" errorcode="CampaignServiceAdGroupNetworksArrayExceedsLimit" code="1247" message="The list of AdGroupNetworks exceeds the limit."/>
      <error name="NetworkShouldBeNullForContentAdGroup" errorcode="CampaignServiceNetworkShouldBeNullForContentAdGroup" code="1248" message="Network should be null for content adgroups."/>
      <error name="NullNetwork" errorcode="CampaignServiceNullNetwork" code="1249" message="Network is set to null."/>
      <error name="NegativeSitesArrayShouldNotBeNullOrEmpty" errorcode="AdGroupServiceNegativeSitesArrayShouldNotBeNullOrEmpty" code="1250" message="AdGroup Negative Sites cannot be null or empty." />
      <error name="NegativeSitesEntityLimitExceeded" errorcode="AdGroupServiceNegativeSitesEntityLimitExceeded" code="1251" message="AdGroup Negative Sites array size exceeded limit." />
      <error name="NullAdGroupNegativeSites" errorcode="AdGroupServiceNullAdGroupNegativeSites" code="1252" message="AdGroupNegativeSites is null." />
      <error name="AdGroupNetworkValueNotAllowedForPublisherCountries" errorcode="CampaignServiceAdGroupNetworkValueNotAllowedForPublisherCountries" code="1253" message="The network provided is not allowed for specified publisher countries or ad group publisher language."/>
      <error name="MissingPublisherCountries" errorcode="CampaignServiceMissingPublisherCountries" code="1254" message="The publisher countries are not provided."/>
      <error name="AdGroupMediumNotAllowedForPublisherCountries" errorcode="CampaignServiceAdGroupMediumNotAllowedForPublisherCountries" code="1256" message="The medium provided is not allowed for specified publisher countries."/>
      <error name="MissingLanguage" errorcode="CampaignServiceMissingLanguage" code="1257" message="The language is not provided."/>
      <error name="MultiplePublisherCountriesNotAllowed" errorcode="CampaignServiceMultiplePublisherCountriesNotAllowed" code="1258" message="Only one publisher country/region can be specified."/>
      <error name="PublisherCountriesUpdateNotAllowed" errorcode="CampaignServicePublisherCountriesUpdateNotAllowed" code="1259" message="The publisher countries cannot be updated."/>
      <error name="LanguageUpdateNotAllowed" errorcode="CampaignServiceLanguageUpdateNotAllowed" code="1260" message="The language cannot be updated."/>
      <error name="AdGroupAdRotationArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdGroupAdRotationArrayShouldNotBeNullOrEmpty" code="1261" message="AdGroupAdRotation array should not be null or empty."/>
      <error name="AdGroupAdRotationsArrayExceedsLimit" errorcode="CampaignServiceAdGroupAdRotationsArrayExceedsLimit" code="1262" message="AdGroupAdRotations array exceeds the limit."/>
      <error name="CannotSetStartOrEndDateForAdRotation" errorcode="CampaignServiceCannotSetStartOrEndDateForAdRotation" code="1263" message="Cannot set the Start or End Date of Ad Rotation feature for the ad group."/>
      <error name="AdRotationPilotNotEnabledForCustomer" errorcode="CampaignServiceAdRotationPilotNotEnabledForCustomer" code="1264" message="The customer is not a member of the Ad Rotation pilot program."/>
      <error name="InvalidAdGroupStatus" errorcode="CampaignServiceInvalidAdGroupStatus" code="1266" message="The ad group status is not valid for the requested operation."/>
      <error name="InvalidBiddingModel" errorcode="CampaignServiceInvalidBiddingModel" code ="1267" message="The bidding model value of the ad group is invalid."/>
      <error name="InvalidRemarketingTargetingSetting" errorcode="CampaignServiceInvalidRemarketingTargetingSetting" code ="1268" message="The remarketing targeting setting is invalid."/>
      <error name="InvalidAdRotationType" errorcode="CampaignServiceInvalidAdRotationType" code="1269" message="Ad Rotation type is invalid."/>
      <error name="InvalidSettingsInEntity" errorcode="CampaignServiceInvalidSettingsInEntity" code="1270" message="One or more settings are not supported for the entity type."/>
      <error name="CoOpSettingBidOptionInvalid" errorcode="CampaignServiceInvalidCoOpSettingBidOption" code="1271" message="The bid option in co-op setting is invalid."/>
      <error name="CoOpSettingBidBoostValueInvalid" errorcode="CampaignServiceInvalidCoOpSettingBidBoostValue" code="1272" message="The bid boost value in co-op setting is invalid."/>
      <error name="CoOpSettingBidMaxValueInvalid" errorcode="CampaignServiceInvalidCoOpSettingBidMaxValue" code="1273" message="The bid max value in co-op setting is invalid."/>
      <error name="CustomerNotEnableForInHousePromotion" errorcode="CampaignServiceInHousePromotionNotEnabledForCustomer" code="1274" message="The customer is not a member of the In-House promotion pilot program."/>
      <error name="InvalidNetwork" errorcode="CampaignServiceInvalidNetwork" code="1275" message="The network is invalid."/>
      <error name="AdGroupBiddingSchemeNotSupported" errorcode="AdGroupBiddingSchemeNotSupported" code="1276" message="The bidding scheme of the ad group is not supported."/>
      <error name="InvalidSearchBids" errorcode="InvalidSearchBids" code="1277" message="Search Bid is invalid for this enity."/>
      <error name="InvalidContentBid" errorcode="InvalidContentBid" code="1278" message="Content Bid is invalid for this enity."/>
      <error name="InvalidBid" errorcode="InvalidBid" code="1279" message="Bid is invalid for this enity."/>
      <error name="InvalidPricingModel" errorcode="InvalidPricingModel" code="1280" message="The pricing model is invalid for this enity."/>
      <error name="AccountNotInPilotForMMAV2" errorcode="AccountNotInPilotForMMAV2" code="1281" message="The account is not enabled for MMA V2."/>
      <error name="AdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed" errorcode="CampaignServiceAdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed" code="5813" message="The ad group language cannot be removed until campaign language updates have been processed. Please try again in 12 hours."/>

      <!-- Ad -->
      <error name="NullAd" errorcode="CampaignServiceNullAd" code="1300" message="Ad is null."/>
      <error name="InvalidAdTitle" errorcode="CampaignServiceInvalidAdTitle" code="1301" message="The Ad's title is invalid."/>
      <error name="InvalidAdDestinationUrl" errorcode="CampaignServiceInvalidAdDestinationUrl" code="1302" message="Invalid URL has been specified."/>
      <error name="AdIdIsNull" errorcode="CampaignServiceAdIdIsNull" code="1303" message="The Ad's Id field should be set."/>
      <error name="AdIdIsNonNull" errorcode="CampaignServiceAdIdIsNonNull" code="1304" message="The Ad's Id field should not be set."/>
      <error name="AdTypeIsNonNull" errorcode="CampaignServiceAdTypeIsNonNull" code="1305" message="The Ad's Type field should not be set."/>
      <error name="InvalidAdText" errorcode="CampaignServiceInvalidAdText" code="1306" message="The Ad's Text field contains an invalid value."/>
      <error name="InvalidAdDisplayUrl" errorcode="CampaignServiceInvalidAdDisplayUrl" code="1307" message="The Ad's DisplayUrl field contains an invalid value."/>
      <error name="InvalidAdId" errorcode="CampaignServiceInvalidAdId" code="1308" message="The Ad Id is invalid."/>
      <error name="DuplicateInAdIds" errorcode="CampaignServiceDuplicateInAdIds" code="1309" message="Duplicate IDs are contained in the array of AdIds."/>
      <error name="AdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdsArrayShouldNotBeNullOrEmpty" code="1310" message="Ads array should not be null or empty."/>
      <error name="AdsArrayExceedsLimit" errorcode="CampaignServiceAdsArrayExceedsLimit" code="1311" message="The list of ads exceeds the limit."/>
      <error name="MaxAdsReached" errorcode="CampaignServiceMaxAdsReached" code="1312" message="Cannot add any more ads to this AdGroup."/>
      <error name="DuplicateAd" errorcode="CampaignServiceDuplicateAd" code="1313" message="Trying to create a duplicate ad."/>
      <error name="DefaultAdExists" errorcode="CampaignServiceDefaultAdExists" code="1314" message="A default Ad already exists."/>
      <error name="SyntaxErrorInAdTitle" errorcode="CampaignServiceSyntaxErrorInAdTitle" code="1315" message="There is a syntax error in the Ad title."/>
      <error name="SyntaxErrorInAdText" errorcode="CampaignServiceSyntaxErrorInAdText" code="1316" message="There is a syntax error in the Ad Text."/>
      <error name="SyntaxErrorInAdDisplayUrl" errorcode="CampaignServiceSyntaxErrorInAdDisplayUrl" code="1317" message="There is a syntax error in the Ad Display URL."/>
      <error name="ForbiddenTextInAdTitle" errorcode="CampaignServiceForbiddenTextInAdTitle" code="1318" message="There is some forbidden text in the Ad title."/>
      <error name="ForbiddenTextInAdText" errorcode="CampaignServiceForbiddenTextInAdText" code="1319" message="There is some forbidden text in the Ad Text."/>
      <error name="ForbiddenTextInAdDisplayUrl" errorcode="CampaignServiceForbiddenTextInAdDisplayUrl" code="1320" message="There is some forbidden text in the Ad Display URL."/>
      <error name="IncorrectAdFormatInTitle" errorcode="CampaignServiceIncorrectAdFormatInTitle" code="1321" message="The ad format in the title is incorrect."/>
      <error name="IncorrectAdFormatInText" errorcode="CampaignServiceIncorrectAdFormatInText" code="1322" message="The ad format in the text is incorrect."/>
      <error name="IncorrectAdFormatInDisplayUrl" errorcode="CampaignServiceIncorrectAdFormatInDisplayUrl" code="1323" message="The ad format in the display URL is incorrect."/>
      <error name="TooMuchAdTextInTitle" errorcode="CampaignServiceTooMuchAdTextInTitle" code="1324" message="The Ad title has too much text."/>
      <error name="TooMuchAdTextInText" errorcode="CampaignServiceTooMuchAdTextInText" code="1325" message="The Ad text is too long."/>
      <error name="TooMuchAdTextInDisplayUrl" errorcode="CampaignServiceTooMuchAdTextInDisplayUrl" code="1326" message="The Ad Display URL has too much text."/>
      <error name="TooMuchAdTextInDestinationUrl" errorcode="CampaignServiceTooMuchAdTextInDestinationUrl" code="1327" message="The Ad Destination URL has too much text."/>
      <error name="NotEnoughAdText" errorcode="CampaignServiceNotEnoughAdText" code="1328" message="The combination of the ad title and the ad text does not meet the requirement for the minimum number of words."/>
      <error name="ExclusiveWordInAdTitle" errorcode="CampaignServiceExclusiveWordInAdTitle" code="1329" message="The Ad title contains a reserved word."/>
      <error name="ExclusiveWordInAdText" errorcode="CampaignServiceExclusiveWordInAdText" code="1330" message="The Ad text contains a reserved word."/>
      <error name="ExclusiveWordInAdDisplayUrl" errorcode="CampaignServiceExclusiveWordInAdDisplayUrl" code="1331" message="The Ad display Url contains a reserved word."/>
      <error name="InvalidAdDisplayUrlFormat" errorcode="CampaignServiceInvalidAdDisplayUrlFormat" code="1332" message="The Ad display Url format is invalid."/>
      <error name="DefaultAdSyntaxErrorInTitle" errorcode="CampaignServiceDefaultAdSyntaxErrorInTitle" code="1333" message="There is a syntax error in the title of the default Ad."/>
      <error name="DefaultAdSyntaxErrorInText" errorcode="CampaignServiceDefaultAdSyntaxErrorInText" code="1334" message="There is a syntax error in the text of the default Ad."/>
      <error name="DefaultAdSyntaxErrorInDisplayUrl" errorcode="CampaignServiceDefaultAdSyntaxErrorInDisplayUrl" code="1335" message="There is a syntax error in the display URL of the default Ad."/>
      <error name="DefaultAdForbiddenWordInTitle" errorcode="CampaignServiceDefaultAdForbiddenWordInTitle" code="1336" message="There is some forbidden text in the title of the default Ad."/>
      <error name="DefaultAdForbiddenWordInText" errorcode="CampaignServiceDefaultAdForbiddenWordInText" code="1337" message="There is some forbidden text in the default Ad."/>
      <error name="DefaultAdForbiddenWordInDisplayUrl" errorcode="CampaignServiceDefaultAdForbiddenWordInDisplayUrl" code="1338" message="There is some forbidden text in the Display URL of the default Ad."/>
      <error name="DefaultAdIncorrectAdFormatInTitle" errorcode="CampaignServiceDefaultAdIncorrectAdFormatInTitle" code="1339" message="The ad format of the title is incorrect for the default Ad."/>
      <error name="DefaultAdIncorrectAdFormatInText" errorcode="CampaignServiceDefaultAdIncorrectAdFormatInText" code="1340" message="The ad format of the text is incorrect for the default Ad."/>
      <error name="DefaultAdIncorrectAdFormatInDisplayUrl" errorcode="CampaignServiceDefaultAdIncorrectAdFormatInDisplayUrl" code="1341" message="The ad format of the display URL is incorrect for the default Ad."/>
      <error name="DefaultAdTooMuchTextInTitle" errorcode="CampaignServiceDefaultAdTooMuchTextInTitle" code="1342" message="There is too much text in the title for the default Ad."/>
      <error name="DefaultAdTooMuchTextInText" errorcode="CampaignServiceDefaultAdTooMuchTextInText" code="1343" message="There is too much text for the default Ad."/>
      <error name="DefaultAdTooMuchTextInDisplayUrl" errorcode="CampaignServiceDefaultAdTooMuchTextInDisplayUrl" code="1344" message="There is too much text in the display URL for the default Ad."/>
      <error name="DefaultAdTooMuchTextInDestinationUrl" errorcode="CampaignServiceDefaultAdTooMuchTextInDestinationUrl" code="1345" message="There is too much text in the destination URL for the default Ad."/>
      <error name="DefaultAdNotEnoughAdText" errorcode="CampaignServiceDefaultAdNotEnoughAdText" code="1346" message="There is not enough text in the default Ad."/>
      <error name="DefaultAdExclusiveWordInTitle" errorcode="CampaignServiceDefaultAdExclusiveWordInTitle" code="1347" message="A reserved word has been used in the title of the default Ad."/>
      <error name="DefaultAdExclusiveWordInText" errorcode="CampaignServiceDefaultAdExclusiveWordInText" code="1348" message="A reserved word has been used in the text of the default Ad."/>
      <error name="DefaultAdExclusiveWordInDisplayUrl" errorcode="CampaignServiceDefaultAdExclusiveWordInDisplayUrl" code="1349" message="A reserved word has been used in the display URL of the default Ad."/>
      <error name="DefaultAdInvalidDisplayUrlFormat" errorcode="CampaignServiceDefaultAdInvalidDisplayUrlFormat" code="1350" message="The format for the display URL in the default Ad is invalid."/>
      <error name="AdIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdIdsArrayShouldNotBeNullOrEmpty" code="1351" message="Ad Ids array should not be null or empty."/>
      <error name="AdIdsArrayExceedsLimit" errorcode="CampaignServiceAdIdsArrayExceedsLimit" code="1352" message="The list of ad Ids exceeds the limit."/>
      <error name="TooMuchTextInTitleAcrossAllAssociations" errorcode="CampaignServiceTooMuchTextInTitleAcrossAllAssociations" code="1353" message="There is too much text in the title across all keyword-ad associations."/>
      <error name="TooMuchTextInTextAcrossAllAssociations" errorcode="CampaignServiceTooMuchTextInTextAcrossAllAssociations" code="1354" message="There is too much text across all keyword-ad associations."/>
      <error name="TooMuchTextInDisplayUrlAcrossAllAssociations" errorcode="CampaignServiceTooMuchTextInDisplayUrlAcrossAllAssociations" code="1355" message="There is too much text in the display URL across all keyword-ad associations."/>
      <error name="NothingToUpdateInAdRequest" errorcode="CampaignServiceNothingToUpdateInAdRequest" code="1356" message="There is no information to update in the given ad request."/>
      <error name="CannotOperateOnAdInCurrentState" errorcode="CampaignServiceCannotOperateOnAdInCurrentState" code="1357" message="Cannot operate on ad in current state."/>
      <error name="DefaultAdInvalidDestinationUrlFormat" errorcode="CampaignServiceDefaultAdInvalidDestinationUrlFormat" code="1358" message="The format for the destination URL in the default Ad is invalid."/>
      <error name="InvalidAdDestinationUrlFormat" errorcode="CampaignServiceInvalidAdDestinationUrlFormat" code="1359" message="The Ad destination Url format is invalid."/>
      <error name="AdTypeDoesNotMatch" errorcode="CampaignServiceAdTypeDoesNotMatch" code="1360" message="The Ad's Type field should not be set or should match the type of the Ad."/>
      <error name="InvalidBusinessName" errorcode="CampaignServiceInvalidBusinessName" code="1361" message="The Ad's business name is not valid."/>
      <error name="InvalidPhoneNumber" errorcode="CampaignServiceInvalidPhoneNumber" code="1362" message="The Ad's phone number is not valid."/>
      <error name="MobileAdRequiredDataMissing" errorcode="CampaignServiceMobileAdRequiredDataMissing" code="1363" message="For a mobile ad, either phone number and business name or destination url and display url need to be supplied."/>
      <error name="MobileAdSupportedForSearchOnlyAdGroups" errorcode="CampaignServiceMobileAdSupportedForSearchOnlyAdGroups" code="1364" message="A mobile ad may be added only to ad groups with AdDistribution = Search"/>
      <error name="AdTypeMismatch" errorcode="CampaignServiceAdTypeMismatch" code="1365" message="Existing Ad has different type than one passed for the same ID."/>
      <error name="AdTypeInvalidForCustomer" errorcode="CampaignServiceAdTypeInvalidForCustomer" code="1366" message="Customer account is not enabled to create ads of this type"/>
      <error name="TooMuchAdTextInBusinessName" errorcode="CampaignServiceTooMuchAdTextInBusinessName" code="1367" message="The business name has too much text"/>
      <error name="TooMuchAdTextInPhoneNumber" errorcode="CampaignServiceTooMuchAdTextInPhoneNumber" code="1368" message="The phone number has too much text"/>
      <error name="PhoneNumberNotAllowedForCountry" errorcode="CampaignServicePhoneNumberNotAllowedForCountry" code="1370" message="Phone number is not valid for this country/region."/>
      <error name="BlockedPhoneNumber" errorcode="CampaignServiceBlockedPhoneNumber" code="1371" message="Phone number is blocked."/>
      <error name="PhoneNumberNotAllowedInAdTitle" errorcode="CampaignServicePhoneNumberNotAllowedInAdTitle" code="1372" message="Phone number is not allowed in ad title."/>
      <error name="PhoneNumberNotAllowedInAdText" errorcode="CampaignServicePhoneNumberNotAllowedInAdText" code="1373" message="Phone number is not allowed in ad text."/>
      <error name="PhoneNumberNotAllowedInAdDisplayUrl" errorcode="CampaignServicePhoneNumberNotAllowedInAdDisplayUrl" code="1374" message="Phone number is not allowed in ad display url."/>
      <error name="PhoneNumberNotAllowedInAdBusinessName" errorcode="CampaignServicePhoneNumberNotAllowedInAdBusinessName" code="1375" message="Phone number is not allowed in ad business name."/>
      <error name="EditorialErrorInAdTitle" errorcode="CampaignServiceEditorialErrorInAdTitle" code="1376" message="Ad title failed editorial validation."/>
      <error name="EditorialErrorInAdText" errorcode="CampaignServiceEditorialErrorInAdText" code="1377" message="Ad text failed editorial validation."/>
      <error name="EditorialErrorInAdDisplayUrl" errorcode="CampaignServiceEditorialErrorInAdDisplayUrl" code="1378" message="Ad display url failed editorial validation."/>
      <error name="EditorialErrorInAdDestinationUrl" errorcode="CampaignServiceEditorialErrorInAdDestinationUrl" code="1379" message="Ad destination url failed editorial validation."/>
      <error name="EditorialErrorInAdBusinessName" errorcode="CampaignServiceEditorialErrorInAdBusinessName" code="1380" message="Ad business name failed editorial validation."/>
      <error name="EditorialErrorInAdPhoneNumber" errorcode="CampaignServiceEditorialErrorInAdPhoneNumber" code="1381" message="Ad phone number failed editorial validation."/>
      <error name="InvalidAdStatus" errorcode="CampaignServiceInvalidAdStatus" code="1382" message="Setting Status not allowed."/>
      <error name="InvalidAdEditorialStatus" errorcode="CampaignServiceInvalidAdEditorialStatus" code="1383" message="Setting Editorial Status is not allowed."/>
      <error name="CannotSetExemptionRequestOnAd" errorcode="CampaignServiceCannotSetExemptionRequestOnAd" code="1384" message="Exemption Request for keyword is not currently supported and hence cannot be set."/>
      <error name="UpdateAdEmpty" errorcode="CampaignServiceUpdateAdEmpty" code="1385" message="Update ad entity has no data"/>
      <error name="AdTypeDoesNotMatchExistingValue" errorcode="CampaignServiceAdTypeDoesNotMatchExistingValue" code="1386" message="The requested ad type does not match the existing ad type. For example the App Install ad type was specified, but the ad identifier refers to an Expanded Text ad."/>
      <error name="EditorialAdTitleBlankAcrossAllAssociations" errorcode="CampaignServiceEditorialAdTitleBlankAcrossAllAssociations" code="1387" message="Ad title is blank for all keywords"/>
      <error name="EditorialAdTitleBlank" errorcode="CampaignServiceEditorialAdTitleBlank" code="1388" message="Keyword would make title blank for at least one Ad"/>
      <error name="EditorialAdTextBlankAcrossAllAssociations" errorcode="CampaignServiceEditorialAdTextBlankAcrossAllAssociations" code="1389" message="Ad text is blank for all keywords"/>
      <error name="EditorialAdTextBlank" errorcode="CampaignServiceEditorialAdTextBlank" code="1390" message="Keyword would make text blank for at least one Ad"/>
      <error name="EditorialAdDisplayUrlBlankAcrossAllAssociations" errorcode="CampaignServiceEditorialAdDisplayUrlBlankAcrossAllAssociations" code="1391" message="Ad display URL is blank for all keywords"/>
      <error name="EditorialAdDisplayUrlBlank" errorcode="CampaignServiceEditorialAdDisplayUrlBlank" code="1392" message="Keyword would make display URL blank for at least one Ad"/>
      <error name="EditorialAdDestinationUrlBlank" errorcode="CampaignServiceEditorialAdDestinationUrlBlank" code="1393" message="Keyword would make destination URL blank for at least one Ad"/>
      <error name="AdDeleted" errorcode="CampaignServiceAdDeleted" code="1394" message="The ad is already deleted or does not exist."/>
      <error name="AdInInvalidStatus" errorcode="CampaignServiceAdInInvalidStatus" code="1395" message="The Ad Status is Invalid for the current operation."/>
      <error name="DefaultAdTooMuchTextInBusniessName" errorcode="CampaignServiceDefaultAdTooMuchTextInBusniessName" code="1396" message="There is too much text in the business name for the default Ad."/>
      <error name="EditorialGenericError" errorcode="CampaignServiceEditorialGenericError" code="1397" message="The given data failed some editorial checks."/>
      <error name="ProductAdPromotionalTextTooLong" errorcode="CampaignServiceProductAdPromotionalTextTooLong" code="1398" message="The promotional text of Product Ad is too long."/>
      <error name="AdTypeInvalidForCampaign" errorcode="CampaignServiceAdTypeInvalidForCampaign" code="1399" message="Campaign type is not enabled to create ads of this type"/>
      <error name="NonNullImageUrl" errorcode="CampaignServiceNonNullImageUrl" code="2802" message="Image URL is read-only field and should not be specified."/>
      <error name="DefaultAdTooMuchTextInAltText" errorcode="CampaignServiceDefaultAdTooMuchTextInAltText" code="2803" message="There is too much text in the alternate text for the default Ad."/>
      <error name="TooMuchAdTextInAltText" errorcode="CampaignServiceTooMuchAdTextInAltText" code="2804" message="The Ad alternate text has too much text."/>
      <error name="AdTypeInvalid" errorcode="CampaignServiceAdTypeInvalid" code="2805" message="Creating or updating ads of this type is not allowed."/>
      <error name="RichSearchAdsExclusiveInAdGroup" errorcode="CampaignServiceRichSearchAdExclusiveInAdGroup" code="2806" message="AdGroup with Rich Search Ads cannot have any other type of Ads in the AdGroup."/>
      <error name="MediumChangeNotAllowedForAdgroupWithRichSearchAds" errorcode="CampaignServiceMediumChangeNotAllowedForAdgroupWithRichSearchAd" code="2807" message="Changing medium of Adgroup having Rich Search Ad is not allowed."/>
      <error name="ResumeFailedDueToMigrationErrors" errorcode="CampaignServiceResumeFailedDueToMigrationErrors" code="2809" message="Resume failed because the entity had migration errors."/>
      <error name="DevicePreferenceIncompatibleWithAdType" errorcode="DevicePreferenceIncompatibleWithAdType" code ="5000" message="Device Preference cannot be set for Ad type of this type."/>
      <error name="InvalidAdDevicePreference" errorcode="InvalidAdDevicePreference" code ="5001" message="Device Preference passed in was invalid."/>
      <error name="ProductAdPromotionalTextInvalid" errorcode="CampaignServiceProductAdPromotionalTextInvalid" code="5002" message="The promotional text of Product Ad is not valid."/>

      <error name="AppInstallAdAppPlatformNullOrEmpty" errorcode="CampaignServiceAppInstallAdAppPlatformNullOrEmpty" code="5005" message="The app platform cannot be null or empty."/>
      <error name="AppInstallAdAppPlatformInvalid" errorcode="CampaignServiceAppInstallAdAppPlatformInvalid" code="5006" message="The app platform is invalid."/>
      <error name="AppInstallAdAppStoreIdIsNullOrEmpty" errorcode="CampaignServiceAppInstallAdAppStoreIdIsNullOrEmpty" code="5007" message="The app store ID cannot be null or empty."/>
      <error name="AppInstallAdAppStoreIdTooMuchText" errorcode="CampaignServiceAppInstallAdAppStoreIdTooMuchText" code="5008" message="The app store ID is too long."/>
      <error name="AppInstallAdAppStoreIdInvalid" errorcode="CampaignServiceAppInstallAdAppStoreIdInvalid" code="5009" message="The app store ID is invalid."/>
      <error name="AppInstallAdUpdateAppPlatformChanged" errorcode="CampaignServiceAppInstallAdUpdateAppPlatformChanged" code="5010" message="The app platform cannot be modified."/>
      <error name="AppInstallAdUpdateAppStoreIdChanged" errorcode="CampaignServiceAppInstallAdUpdateAppStoreIdChanged" code="5011" message="The app store ID cannot be modified."/>
      <error name="InvalidAdFormatPreference" errorcode="InvalidAdFormatPreference" code ="5012" message="AdFormatPreference passed in was invalid."/>
      <error name="CustomerNotEnabledForLocalInventoryAds" errorcode="CustomerNotEnabledForLocalInventoryAds" code ="4576" message="Customer is Not Enabled For LocalInventoryAds"/>
      <error name="AccountNotEnabledForShoppableAds" errorcode="AccountNotEnabledForShoppableAds" code ="4577" message="Account is Not Enabled For Shoppable Ads"/>
      <error name="AppNotFound" errorcode="AppNotFound" code ="4578" message="App is not found for this app campaign"/>
        
      <!--Expanded Text Ad-->
      <error name="ExpandedTextAdTitlePart1TooLong" errorcode="CampaignServiceExpandedTextAdTitlePart1TooLong" code="5013" message="Ad title part 1 is over the character limit."/>
      <error name="ExpandedTextAdTitlePart2TooLong" errorcode="CampaignServiceExpandedTextAdTitlePart2TooLong" code="5014" message="Ad title part 2 is over the character limit."/>
      <error name="ExpandedTextAdTitlePart1Invalid" errorcode="CampaignServiceExpandedTextAdTitlePart1Invalid" code="5015" message="Ad title part 1 is not valid."/>
      <error name="ExpandedTextAdTitlePart2Invalid" errorcode="CampaignServiceExpandedTextAdTitlePart2Invalid" code="5016" message="Ad title part 2 is not valid."/>
      <error name="ExpandedTextAdPath1TooLong" errorcode="CampaignServiceExpandedTextAdPath1TooLong" code="5017" message="Path 1 is over the character limit."/>
      <error name="ExpandedTextAdPath2TooLong" errorcode="CampaignServiceExpandedTextAdPath2TooLong" code="5018" message="Path 2 is over the character limit."/>
      <error name="ExpandedTextAdPath1Invalid" errorcode="CampaignServiceExpandedTextAdPath1Invalid" code="5019" message="Path 1 is not valid."/>
      <error name="ExpandedTextAdPath2Invalid" errorcode="CampaignServiceExpandedTextAdPath2Invalid" code="5020" message="Path 2 is not valid."/>
      <error name="ExpandedTextAdPath2SetWithoutPath1" errorcode="CampaignServiceExpandedTextAdPath2SetWithoutPath1" code="5021" message="Path2 is set without path1."/>
      <error name="CustomerNotEnabledForExpandedTextAds" errorcode="CustomerNotEnabledForExpandedTextAds" code="5022" message="The Expanded Text Ad pilot is not enabled for customer."/>
      <error name="ExpandedTextAdCombinedTitleTooLong" errorcode="CampaignServiceExpandedTextAdCombinedTitleTooLong" code="5023" message="Combined Title 1 and Title 2 exceeds the system limit."/>
      <error name="ExpandedTextAdFinalUrlDomainTooLong" errorcode="CampaignServiceExpandedTextAdFinalUrlDomainTooLong" code="5024" message="Final Url domain is too long to be used as Ad Display URL."/>
      <!--Expanded Text Ad - Vanity URLs-->
      <error name="ExpandedTextAdDisplayUrlDomainTooLong" errorcode="CampaignServiceExpandedTextAdDisplayUrlDomainTooLong" code="5025" message="Display Url domain is too long."/>
      <error name="ExpandedTextAdDisplayUrlDomainInvalid" errorcode="CampaignServiceExpandedTextAdDisplayUrlDomainInvalid" code="5026" message="Display Url domain is Invalid."/>
      <!--Expanded Text Ad - Functions-->
      <error name="ExpandedTextAdInvalidFunctionFormat" errorcode="CampaignServiceExpandedTextAdInvalidFunctionFormat" code="5028" message="The syntax of your function contains invalid formatting, likely caused by a missing }."/>
      <error name="ExpandedTextAdUnknownFunction" errorcode="CampaignServiceExpandedTextAdUnknownFunction" code="5029" message="One or more functions are invalid or not supported."/>
      <error name="ExpandedTextAdMissingDelimiterBetweenFunctions" errorcode="CampaignServiceExpandedTextAdMissingDelimiterBetweenFunctions" code="5030" message="You need to have at least one character between any two functions."/>
      <!--Expanded Text Ad - Functions - Countdown-->
      <error name="ExpandedTextAdCountdownInvalidDateTime" errorcode="CampaignServiceExpandedTextAdCountdownInvalidDateTime" code="5031" message="Your countdown function contains an invalid date and/or time."/>
      <error name="ExpandedTextAdCountdownInvalidLanguageCode" errorcode="CampaignServiceExpandedTextAdCountdownInvalidLanguageCode" code="5032" message="Your countdown function contains an invalid language code."/>
      <error name="ExpandedTextAdCountdownInvalidDaysBefore" errorcode="CampaignServiceExpandedTextAdCountdownInvalidDaysBefore" code="5033" message="Your countdown function contains an invalid days-before value."/>
      <error name="ExpandedTextAdCountdownDaysBeforeOutOfRange" errorcode="CampaignServiceExpandedTextAdCountdownDaysBeforeOutOfRange" code="5034" message="The days-before value in your countdown function is out of range."/>
      <error name="ExpandedTextAdCountdownInvalidParameters" errorcode="CampaignServiceExpandedTextAdCountdownInvalidParameters" code="5035" message="A countdown function must have at least one parameter and no more than three."/>
      <error name="ExpandedTextAdCountdownPastDateTime" errorcode="CampaignServiceExpandedTextAdCountdownPastDateTime" code="5036" message="Your countdown function contains a date and/or time in the past."/>
      <error name="ExpandedTextAdCountdownInvalidDefaultText" errorcode="CampaignServiceExpandedTextAdCountdownInvalidDefaultText" code="5037" message="Default value is not allowed in countdown function."/>
      <error name="ExpandedTextDefaultTextRequiredForKeyword" errorcode="CampaignServiceExpandedTextDefaultTextRequiredForKeyword" code="5038" message="When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}"/>
      <error name="MaxAdsReachedForCustomer" errorcode="CampaignServiceMaxAdsReachedForCustomer" code="5039" message="Cannot add any more Ads to this customer."/>
      <error name="MaxAdsReachedForAccount" errorcode="CampaignServiceMaxAdsReachedForAccount" code="5040" message="Cannot add any more Ads to this account."/>
      <!--Expanded Text Ad - Domain-->
      <error name="ExpandedTextAdDomainTooLong" errorcode="CampaignServiceExpandedTextAdDomainTooLong" code="5041" message="The expanded text ad domain is too long."/>
      <error name="ExpandedTextAdDomainInvalid" errorcode="CampaignServiceExpandedTextAdDomainInvalid" code="5042" message="The expanded text ad domain is invalid."/>

      <!--Expanded Text Ad - Adcustomizers-->
      <error name="AdCustomizerFeedNameMissing" errorcode="CampaignServiceAdCustomizerFeedNameMissing" code="5043" message="The feed name in the ad customizer function is missing."/>
      <error name="AdCustomizerFeedAttributeMissing" errorcode="CampaignServiceAdCustomizerFeedAttributeMissing" code="5044" message="The attribute name in the ad customizer function is missing."/>
      <error name="AdCustomizerDefaultValueMissing" errorcode="CampaignServiceAdCustomizerDefaultValueMissing" code="5045" message="The default value in the ad customizer function is missing."/>
      <error name="FeedPerAdLimitExceeded" errorcode="CampaignServiceFeedPerAdLimitExceeded" code="5046" message="Only one feed name can be referenced in the ad."/>
      <error name="FeedNameDoesNotExist" errorcode="CampaignServiceFeedNameDoesNotExist" code="5047" message="The feed name does not exist."/>
      <error name="InvalidFeedForAdType" errorcode="CampaignServiceInvalidFeedForAdType" code="5048" message="The feed is not supported for the ad type."/>
      <error name="FeedAttributeDoesNotExist" errorcode="CampaignServiceFeedAttributeDoesNotExist" code="5049" message="The attribute does not exist in the feed."/>
      <error name="InvalidFeedAttributeForAdType" errorcode="CampaignServiceInvalidFeedAttributeForAdType" code="5050" message="The feed attribute is not supported for the ad type."/>
      <error name="InvalidFeedAttributeTypeInCountdown" errorcode="CampaignServiceInvalidFeedAttributeTypeInCountdown" code="5051" message="The feed attribute in the COUNTDOWN or GLOBAL_COUNTDOWN function should be of DateTime type only."/>

      <!--Responsive Search Ad - Adcustomizers-->
        <error name="RSAAdCustomizerAttributeTypeChangedInUpdate" errorcode="CampaignServiceRSAAdCustomizerAttributeTypeChangedInUpdate" code="65302" message="The attribute data type can't be changed."/>
	    <error name="RSAAdCustomizerAttributeCountMoreThanLimit" errorcode="CampaignServiceRSAAdCustomizerAttributeCountMoreThanLimit" code="65303" message="The AdCustomizer Attribute Count exceeds the system limit."/>
	    <error name="RSAAdCustomizerInvalidAttributeType" errorcode="CampaignServiceRSAAdCustomizerInvalidAttributeType" code="65304" message="attribute data type is invalid."/>
        <error name="AttributeNameDoesNotExist" errorcode="CampaignServiceAttributeNameDoesNotExist" code="65305" message="The attribute name does not exist in the account."/>
        <error name="FetchAttributesFailed" errorcode="CampaignServiceFetchAttributesFailed" code="65306" message="Failed to load existing attributes."/>
	    <error name="AttributeNameLengthExceeded" errorcode="CampaignServiceAttributeNameLengthExceeded" code="65307" message="attribute data type is invalid."/>
	    <error name="InvalidAdcustomizerAttributeId" errorcode="CampaignServiceInvalidAdcustomizerAttributeId" code="65308" message="The attributeId is invalid."/>
        <error name="AttributeNameMissing" errorcode="CampaignServiceAttributeNameMissing" code="65309" message="Attribute must have a name."/>
        <error name="AttributeReferencedInAd" errorcode="CampaignServiceAttributeReferencedInAd" code="65310" message="There exists ad associated with this attribute."/>

        <!--Expanded Text Ad V2: support 3rd title and 2nd description -->
      <error name="ExpandedTextAdTitlePart3TooLong" errorcode="CampaignServiceExpandedTextAdTitlePart3TooLong" code="5052" message="Ad title part 3 is over the character limit."/>
      <error name="ExpandedTextAdTitlePart3Invalid" errorcode="CampaignServiceExpandedTextAdTitlePart3Invalid" code="5053" message="Ad title part 3 is not valid."/>
      <error name="ExpandedTextAdTextPart1TooLong" errorcode="CampaignServiceExpandedTextAdTextPart1TooLong" code="5054" message="Ad text part 1 is over the character limit."/>
      <error name="ExpandedTextAdTextPart1Invalid" errorcode="CampaignServiceExpandedTextAdTextPart1Invalid" code="5055" message="Ad text part 1 is not valid."/>
      <error name="ExpandedTextAdTextPart2TooLong" errorcode="CampaignServiceExpandedTextAdTextPart2TooLong" code="5056" message="Ad text part 2 is over the character limit."/>
      <error name="ExpandedTextAdTextPart2Invalid" errorcode="CampaignServiceExpandedTextAdTextPart2Invalid" code="5057" message="Ad text part 2 is not valid."/>

      <!--Target-->
      <error name="NullTarget" errorcode="CampaignServiceNullTarget" code="1400" message="Target is null."/>
      <error name="InvalidTargetId" errorcode="CampaignServiceInvalidTargetId" code="1401" message="The Target ID is invalid."/>
      <error name="NoBidsInTarget" errorcode="CampaignServiceNoBidsInTarget" code="1402" message="There are no bids in target."/>
      <error name="InvalidDayTarget" errorcode="CampaignServiceInvalidDayTarget" code="1403" message="The day target specified is invalid."/>
      <error name="InvalidHourTarget" errorcode="CampaignServiceInvalidHourTarget" code="1404" message="The hour target specified is invalid."/>
      <error name="InvalidLocationTarget" errorcode="CampaignServiceInvalidLocationTarget" code="1405" message="The location target specified is invalid."/>
      <error name="InvalidGenderTarget" errorcode="CampaignServiceInvalidGenderTarget" code="1406" message="The gender target specified is invalid."/>
      <error name="InvalidAgeTarget" errorcode="CampaignServiceInvalidAgeTarget" code="1407" message="The age target specified is invalid."/>
      <error name="DuplicateDayTarget" errorcode="CampaignServiceDuplicateDayTarget" code="1408" message="The day target specified is a duplicate."/>
      <error name="DuplicateHourTarget" errorcode="CampaignServiceDuplicateHourTarget" code="1409" message="The hour target specified is a duplicate."/>
      <error name="DuplicateMetroAreaLocationTarget" errorcode="CampaignServiceDuplicateMetroAreaLocationTarget" code="1410" message="The metro area target specified is a duplicate."/>
      <error name="DuplicateCountryLocationTarget" errorcode="CampaignServiceDuplicateCountryLocationTarget" code="1411" message="The country/region target specified is a duplicate."/>
      <error name="DuplicateGenderTarget" errorcode="CampaignServiceDuplicateGenderTarget" code="1412" message="The gender target specified is a duplicate."/>
      <error name="DuplicateAgeTarget" errorcode="CampaignServiceDuplicateAgeTarget" code="1413" message="The age target specified is a duplicate."/>
      <error name="CountryAndMetroAreaTargetsExclusive" errorcode="CampaignServiceCountryAndMetroAreaTargetsExclusive" code="1414" message="The country/region and metro targets should be mutually exclusive."/>
      <error name="MetroTargetsFromMultipleCountries" errorcode="CampaignServiceMetroTargetsFromMultipleCountries" code="1415" message="The metro targets specified should be from the same country/region."/>
      <error name="IncrementalBudgetAmountRequiredForDayTarget" errorcode="CampaignServiceIncrementalBudgetAmountRequiredForDayTarget" code="1416" message="When day target is specified, incremental budget amount should be not null and greater than zero"/>
      <error name="GeoTargetsInAdGroupExceedsLimit" errorcode="CampaignServiceGeoTargetsInAdGroupExceedsLimit" code="1417" message="The number of location targets in the adgroup exceeds the limit."/>
      <error name="DuplicateInTargetIds" code="1418" errorcode="CampaignServiceDuplicateInTargetIds" message="Duplicate Ids are contained in the array of targets."/>
      <error name="TargetGroupAssignedEntitiesPermissionMismatch" code="1419" errorcode="CampaignServiceTargetGroupAssignedEntitiesPermissionMismatch" message="User does not have permission to all entities that this TargetGroup is assigned."/>
      <error name="TargetsNotPassed" code="1420" errorcode="CampaignServiceTargetsNotPassed" message="Targets passed is null."/>
      <error name="TargetAlreadyExists" code="1421" errorcode="CampaignServiceTargetAlreadyExists" message="Target already exists."/>
      <error name="TargetsLimitReached" code="1422" errorcode="CampaignServiceTargetsLimitReached" message="Targets limit has been reached."/>
      <error name="InvalidGeoLocationLevel" code="1423" errorcode="CampaignServiceInvalidGeoLocationLevel" message="Invalid Geo Location level."/>
      <error name="AdGroupMediumInvalidWithBusinessTargets" code="1424" errorcode="CampaignServiceAdGroupMediumInvalidWithBusinessTargets" message="The adGroup medium is invalid for Business Targets."/>
      <error name="TargetsArrayExceedsLimit" code="1425" errorcode="CampaignServiceTargetsArrayExceedsLimit" message="The list of targets exceeds the limit."/>
      <error name="TargetNotAssociatedWithEntity" code="1426" errorcode="CampaignServiceTargetNotAssociatedWithEntity" message="Target is not associated with the entity."/>
      <error name="TargetAlreadyAssociatedWithEntity" code="1427" errorcode="CampaignServiceTargetAlreadyAssociatedWithEntity" message="A target is already associated with the entity."/>
      <error name="TargetHasActiveAssociations" code="1428" errorcode="CampaignServiceTargetHasActiveAssociations" message="This target is associated with entities."/>
      <error name="InvalidTarget" code="1429" errorcode="CampaignServiceInvalidTarget" message="The target is invalid."/>
      <error name="BTTargettingNotEnabledForPilot" code="1430" errorcode="CampaignServiceBTTargettingNotEnabledForPilot" message="The Behavioral Targeting pilot is not enabled for the customer."/>
      <error name="InvalidBehaviorTarget" code="1431" errorcode="CampaignServiceInvalidBehaviorTarget" message="Behavior target is invalid."/>
      <error name="DuplicateBehaviorTarget" code="1432" errorcode="CampaignServiceDuplicateBehaviorTarget" message="There is a duplicate in the behavior target ."/>
      <error name="InvalidSegmentTarget" code="1433" errorcode="CampaignServiceInvalidSegmentTarget" message="Invalid segment target."/>
      <error name="DuplicateSegmentTarget" code="1434" errorcode="CampaignServiceDuplicateSegmentTarget" message="There is a duplicate in the segment target."/>
      <error name="NegativeBiddingNotAllowedForThisTargetType" code="1435" errorcode="CampaignServiceNegativeBiddingNotAllowedForThisTargetType" message="Negative Bidding is not allowed for this type of target."/>
      <error name="InvalidCashbackTextinSegmentTarget" code="1436" errorcode="CampaignServiceInvalidCashbackTextinSegmentTarget" message="Invalid cashback text in segment target."/>
      <error name="InvalidParam1inSegmentTarget" code="1437" errorcode="CampaignServiceInvalidParam1inSegmentTarget" message="Invalid param1 in segment target."/>
      <error name="InvalidParam2inSegmentTarget" code="1438" errorcode="CampaignServiceInvalidParam2inSegmentTarget" message="Invalid param2 in segment target."/>
      <error name="InvalidParam3inSegmentTarget" code="1439" errorcode="CampaignServiceInvalidParam3inSegmentTarget" message="Invalid param3 in segment target."/>
      <error name="InvalidSegmentParam1inSegmentTarget" code="1440" errorcode="CampaignServiceInvalidSegmentParam1inSegmentTarget" message="Invalid segment param1 in segment target."/>
      <error name="InvalidSegmentParam2inSegmentTarget" code="1441" errorcode="CampaignServiceInvalidSegmentParam2inSegmentTarget" message="Invalid segment param2 in segment target."/>
      <error name="CannotSpecifySegmentTargetsWithAnyOtherTarget" code="1442" errorcode="CampaignServiceCannotSpecifySegmentTargetsWithAnyOtherTarget" message="Cannot specify segment target with any other target."/>
      <error name="BusinessLocationsNotPassed" code="1443" errorcode="CampaignServiceBusinessLocationsNotPassed" message="Business locations not passed."/>
      <error name="InvalidBusinessLocationHours" code="1444" errorcode="CampaignServiceInvalidBusinessLocationHours" message="Invalid Business Location Hours."/>
      <error name="InvalidAddress" code="1445" errorcode="CampaignServiceInvalidAddress" message="Invalid address specified."/>
      <error name="BusinessLocationTargetsLimitReachedForCustomer" code="1446" errorcode="CampaignServiceBusinessLocationTargetsLimitReachedForCustomer" message="Business location targets limit has been reached for customer."/>
      <error name="BusinessLocationTargetsLimitReachedForAdGroup" code="1447" errorcode="CampaignServiceBusinessLocationTargetsLimitReachedForAdGroup" message="Business location targets limit has been reached for adgroup."/>
      <error name="BusinessLocationTargetsLimitReachedForCampaign" code="1448" errorcode="CampaignServiceBusinessLocationTargetsLimitReachedForCampaign" message="Business location targets limit has been reached for campaign."/>

      <error name="TargetsAgeBidsBatchLimitExceeded" code="1449" errorcode="CampaignServiceTargetsAgeBidsBatchLimitExceeded" message="Maximum number of bids for Age is exceeded."/>
      <error name="TargetsDayBidsBatchLimitExceeded" code="1450" errorcode="CampaignServiceTargetsDayBidsBatchLimitExceeded" message="Maximum number of bids for Day is exceeded."/>
      <error name="TargetsHourBidsBatchLimitExceeded" code="1451" errorcode="CampaignServiceTargetsHourBidsBatchLimitExceeded" message="Maximum number of bids for Hour is exceeded."/>
      <error name="TargetsGenderBidsBatchLimitExceeded" code="1452" errorcode="CampaignServiceTargetsGenderBidsBatchLimitExceeded" message="Maximum number of bids for Gender is exceeded."/>
      <error name="TargetsLocationBidsBatchLimitExceeded" code="1453" errorcode="CampaignServiceTargetsLocationBidsBatchLimitExceeded" message="Maximum number of bids for Location is exceeded."/>
      <error name="TargetsSegmentBidsBatchLimitExceeded" code="1454" errorcode="CampaignServiceTargetsSegmentBidsBatchLimitExceeded" message="Maximum number of bids for Segment is exceeded."/>
      <error name="TargetsBehaviorBidsBatchLimitExceeded" code="1455" errorcode="CampaignServiceTargetsBehaviorBidsBatchLimitExceeded" message="Maximum number of bids for Behavior is exceeded."/>

      <error name="InvalidBusinessLocationId" code="1456" errorcode="CampaignServiceInvalidBusinessLocationId" message="Business location target ID is invalid."/>
      <error name="InvalidTargetRadius" code="1457" errorcode="CampaignServiceInvalidTargetRadius" message="Target radius is invalid. Valid values are 5, 10, 20, .. 100."/>
      <error name="InvalidLatitude" code="1458" errorcode="CampaignServiceInvalidLatitude" message="Passed value for latitude is invalid."/>
      <error name="InvalidLongitude" code="1459" errorcode="CampaignServiceInvalidLongitude" message="Passed value for longitude is invalid."/>
      <error name="DuplicateSubGeographyTarget" code="1460" errorcode="CampaignServiceDuplicateSubGeographyTarget" message="The subgeography target is duplicated."/>
      <error name="DuplicateCityTarget" code="1461" errorcode="CampaignServiceDuplicateCityTarget" message="The city target is duplicated."/>
      <error name="DuplicateBusinessLocationTarget" code="1462" errorcode="CampaignServiceDuplicateBusinessLocationTarget" message="The business location target is duplicated."/>
      <error name="DuplicateCustomLocationTarget" code="1463" errorcode="CampaignServiceDuplicateCustomLocationTarget" message="The custom location target is duplicated."/>
      <error name="InvalidLocationId" code="1464" errorcode="CampaignServiceInvalidLocationId" message="Location ID is invalid."/>
      <error name="GeoLocationOptionsRequired" code="1465" errorcode="CampaignServiceGeoLocationOptionsRequired" message="Geo location options required."/>
      <error name="UnsupportedCombinationOfLocationIdAndOptions" code="1466" errorcode="CampaignServiceUnsupportedCombinationOfLocationIdAndOptions" message="Unsupported combination of location Id and options."/>
      <error name="InvalidGeographicalLocationSearchString" code="1467" errorcode="CampaignServiceInvalidGeographicalLocationSearchString" message="Invalid geographical location search string."/>
      <error name="GeoTargetsAndBusinessTargetsMutuallyExclusive" code="1468" errorcode="CampaignServiceGeoTargetsAndBusinessTargetsMutuallyExclusive" message="Geo targets (Country/Region/State/MetroArea/City) and Business/Radius targets are mutually exclusive."/>
      <error name="BusinessLocationNotSet" code="1469" errorcode="CampaignServiceBusinessLocationNotSet" message="Business location is not set."/>
      <error name="BusinessNameRequired" code="1470" errorcode="CampaignServiceBusinessNameRequired" message="Business name is required."/>
      <error name="LatitudeLongitudeRequired" code="1471" errorcode="CampaignServiceLatitudeLongitudeRequired" message="Latitude and longitude are required."/>
      <error name="BusinessNameTooLong" code="1472" errorcode="CampaignServiceBusinessNameTooLong" message="Business name is too long."/>
      <error name="DomainNameAlreadyTaken" code="1473" errorcode="CampaignServiceDomainNameAlreadyTaken" message="Domain name is already taken."/>
      <error name="BusinessDescriptionTooLong" code="1474" errorcode="CampaignServiceBusinessDescriptionTooLong" message="Business description is too long."/>
      <error name="InvalidBusinessTypeId" code="1475" errorcode="CampaignServiceInvalidBusinessTypeId" message="Business type ID is invalid."/>
      <error name="InvalidPaymentTypeId" code="1476" errorcode="CampaignServiceInvalidPaymentTypeId" message="Payment type ID is invalid."/>
      <error name="InvalidBusinessHoursEntry" code="1477" errorcode="CampaignServiceInvalidBusinessHoursEntry" message="Business hours entry is invalid."/>
      <error name="AddressRequired" code="1478" errorcode="CampaignServiceAddressRequired" message="Address is required."/>
      <error name="AddressTooLong" code="1479" errorcode="CampaignServiceAddressTooLong" message="Address is too long."/>
      <error name="CityNameRequired" code="1480" errorcode="CampaignServiceCityNameRequired" message="City name is required."/>
      <error name="CityNameTooLong" code="1481" errorcode="CampaignServiceCityNameTooLong" message="City name is too long."/>
      <error name="CountryCodeRequired" code="1482" errorcode="CampaignServiceCountryCodeRequired" message="Country/region code is required."/>
      <error name="CountryCodeTooLong" code="1483" errorcode="CampaignServiceCountryCodeTooLong" message="Country/region code is too long."/>
      <error name="StateOrProvinceRequired" code="1484" errorcode="CampaignServiceStateOrProvinceRequired" message="StateOrProvince field is required."/>
      <error name="StateOrProvinceTooLong" code="1485" errorcode="CampaignServiceStateOrProvinceTooLong" message="StateOrProvince field is too long."/>
      <error name="LocationIsNotSpecified" code="1486" errorcode="CampaignServiceLocationIsNotSpecified" message="Location is not specified."/>
      <error name="Open24HoursAndBusinessHoursMutuallyExclusive" code="1487" errorcode="CampaignServiceOpen24HoursAndBusinessHoursMutuallyExclusive" message="Open24Hours and business hours are mutually exclusive."/>
      <error name="BusinessNameAndAddressAlreadyExists" code="1488" errorcode="CampaignServiceBusinessNameAndAddressAlreadyExists" message="Business name and address already exists."/>
      <error name="BusinessLocationListTooLong" code="1489" errorcode="CampaignServiceBusinessLocationListTooLong" message="Business location list is too long."/>
      <error name="InvalidCustomerId" code="1490" errorcode="CampaignServiceInvalidCustomerId" message="Customer ID is invalid."/>
      <error name="InvalidDomainName" code="1491" errorcode="InvalidDomainName" message="Domain name invalid."/>
      <error name="BusinessDomainNameNotSet" code="1492" errorcode="CampaignServiceBusinessDomainNameNotSet" message="Business domain name is not set."/>
      <error name="BusinessDomainTimestampMismatch" code="1493" errorcode="CampaignServiceBusinessDomainTimestampMismatch" message="Business domain timestamp mismatch."/>
      <error name="TimestampRequiredForBusinessDomainNameModification" code="1494" errorcode="CampaignServiceTimestampRequiredForBusinessDomainNameModification" message="Timestamp is required for business domain name modification."/>
      <error name="BusinessDomainAlreadySet" code="1495" errorcode="CampaignServiceBusinessDomainAlreadySet" message="Business domain is already set."/>
      <error name="DomainNameUnknown" code="1496" errorcode="CampaignServiceDomainNameUnknown" message="Domain name is unknown."/>
      <error name="OnlyOneBusinessLocationPerCustomerIsAllowed" code="1497" errorcode="CampaignServiceOnlyOneBusinessLocationPerCustomerIsAllowed" message="Only one business location per customer is allowed."/>
      <error name="BiddingOtherThanZeroNotAllowedForThisTargetType" code="1498" errorcode="CampaignServiceBiddingOtherThanZeroNotAllowedForThisTargetType" message="Only zero incremental bid is allowed for this target type."/>
      <error name="TargetingShouldBeExclusiveForThisTargetType" code="1499" errorcode="CampaignServiceTargetingShouldBeExclusiveForThisTargetType" message="You can't target all locations for this target type."/>
      <error name="InvalidCashbackAmountInSegmentTarget" code="2900" errorcode="CampaignServiceInvalidCashbackAmountInSegmentTarget" message="Invalid cashback amount for the Segment target."/>
      <error name="BusinessLocationBeginAndEndHoursMismatch" code="2901" errorcode="CampaignServiceBusinessLocationBeginAndEndHoursMismatch" message="Business hours are mismatched. Please make sure begin hour is earlier than end hour."/>
      <error name="InvalidTargetName" errorcode="CampaignServiceInvalidTargetName" code="2902" message="The Target name is invalid."/>
      <error name="DuplicatePaymentTypes" errorcode="CampaignServiceDuplicatePaymentTypes" code="2903" message="Payment types specified has duplciate value(s)."/>
      <error name="TargetInvalidForCustomer" errorcode="CampaignServiceTargetInvalidForCustomer" code ="2904" message="Customer account is not enabled to use one or more of target types in the target."/>
      <error name="InvalidEmail" errorcode="CampaignServiceInvalidEmail" code ="2905" message="Entered email address is invalid."/>
      <error name="EmailTooLong" errorcode="CampaignServiceEmailTooLong" code ="2906" message="Entered email address is too long."/>
      <error name="BusinessPhoneNumberInvalid" errorcode="CampaignServiceBusinessPhoneNumberInvalid" code ="2907" message="Entered phone number is invalid."/>
      <error name="PhoneNumberTooLong" errorcode="CampaignServicePhoneNumberTooLong" code ="2908" message="Entered phone number is too long."/>
      <error name="IsLibraryTargetNotNull" errorcode="CampaignServiceIsLibraryTargetNotNull" code="2909" message="Setting IsLibraryTarget flag is not allowed."/>
      <error name="InvalidLatitudeLongitudeForBusinessLocation" errorcode="CampaignServiceInvalidLatitudeLongitudeForBusinessLocation" code="2910" message="Latitude/Longitude of business location is invalid. Please make sure that business location has valid latitude/longitude."/>
      <error name="ZipOrPostalCodeTooLong" errorcode="CampaignServiceZipOrPostalCodeTooLong" code ="2914" message="Data in ZipOrPostalCode field is too long."/>
      <error name="BusinessDescriptionRequired" code="2915" errorcode="CampaignServiceBusinessDescriptionRequired" message="Business description is required."/>
      <error name="DuplicateBusinessHours" code="2916" errorcode="CampaignServiceDuplicateBusinessHours" message="Duplicates are present in the array of business hours."/>
      <error name="AddressInvalid" code="2917" errorcode="CampaignServiceAddressInvalid" message="Address is invalid."/>
      <error name="BusinessAndRadiusTargetsAllowedOnlyForSearchMedium" code="2918" errorcode="CampaignServiceBusinessAndRadiusTargetsAllowedOnlyForSearchMedium" message="Invalid Medium for Business/Radius targets."/>
      <error name="AssociatingNonLibraryTargetNotAllowed" code="2919" errorcode="CampaignServiceAssociatingNonLibraryTargetNotAllowed" message="Only targets in target library can be associated with an AdGroup or Campaign."/>
      <error name="BusinessAddressShouldBeValidForUpdate" code="2920" errorcode="CampaignServiceBusinessAddressShouldBeValidForUpdate" message="Business address cannot be updated with invalid value."/>
      <error name="BusinessHasActiveAssociations" code="2921" errorcode="CampaignServiceBusinessHasActiveAssociations" message="This business is associated with entities."/>
      <error name="InvalidDeviceTarget" errorcode="CampaignServiceInvalidDeviceTarget" code="2922" message="The device target specified is invalid."/>
      <error name="DuplicateDeviceTarget" errorcode="CampaignServiceDuplicateDeviceTarget" code="2923" message="The device target specified is a duplicate."/>
      <error name="RadiusTargetIdCannotBeSet" errorcode="CampaignServiceRadiusTargetIdCannotbeSet" code="2925" message="The radius target id is not used currently."/>
      <error name="RadiusTargetNameCannotBeSet" errorcode="CampaignServiceRadiusTargetNameCannotbeSet" code="2926" message="The radius target name is not used currently."/>
      <error name="OSTargetingNotEnabledForPilot" errorcode="CampaignServiceOSTargetingNotEnabledForPilot" code="2927" message="OS Targeting is not enabled for the current customer."/>
      <error name="CustomerDataBeingMigrated" errorcode="CampaignServiceCustomerDataBeingMigrated" code="2928" message="You cannot modify data for this customer at the moment, as it is under migration."/>
      <error name="PhysicalIntentNotEnabledForPilot" errorcode="CampaignServicePhysicalIntentNotEnabledForPilot" code="2929" message="Setting PhysicalIntent is not enabled for the current customer."/>
      <error name="DuplicateBusinessLocation" code="2930" errorcode="CampaignServiceDuplicateBusinessLocation" message="The business location is duplicated."/>
      <error name="MultipleDuplicateBusinessLocation" code="2931" errorcode="CampaignServiceMultipleDuplicateBusinessLocation" message="Multiple duplicate business location exist in the system."/>
      <error name="LocationTargetVersionIsNotSupported" code="2932" errorcode="CampaignServiceLocationTargetVersionIsNotSupported" message="The version number of the location target codes is not supported."/>
      <error name="BusinessLocationTargetingIsNotSupported" code="2933" errorcode="CampaignServiceBusinessLocationTargetingIsNotSupported" message="Business location targeting is no longer supported."/>
      <error name="InvalidDeviceTargetBidAdjustment" errorcode="CampaignServiceInvalidDeviceTargetBidAdjustment" code="2934" message="The value of device target bid adjustment is not valid."/>
      <error name="InvalidLocationTargetBidAdjustment" errorcode="CampaignServiceInvalidLocationTargetBidAdjustment" code="2935" message="The value of location target bid adjustment is not valid."/>
      <error name="InvalidAgeTargetBidAdjustment" errorcode="CampaignServiceInvalidAgeTargetBidAdjustment" code="2936" message="The value of age target bid adjustment is not valid."/>
      <error name="InvalidGenderTargetBidAdjustment" errorcode="CampaignServiceInvalidGenderTargetBidAdjustment" code="2937" message="The value of gender target bid adjustment is not valid."/>
      <error name="InvalidDayTargetBidAdjustment" errorcode="CampaignServiceInvalidDayTargetBidAdjustment" code="2938" message="The value of day target bid adjustment is not valid."/>
      <error name="InvalidHourTargetBidAdjustment" errorcode="CampaignServiceInvalidHourTargetBidAdjustment" code="2939" message="The value of hour target bid adjustment is not valid."/>
      <error name="InvalidSegmentedTargetBidAdjustment" errorcode="CampaignServiceInvalidSegmentedTargetBidAdjustment" code="2940" message="The value of segmented target bid adjustment is not valid."/>
      <error name="BidAdjustmentNullPassedForAddTarget" errorcode="CampaignServiceBidAdjustmentNullPassedForAddTarget" code="2941" message="The null value for bid adjustment is not valid."/>
      <error name="InvalidTargetBidExclusion" errorcode="CampaignServiceInvalidTargetBidExclusion" code="2942" message="The IsExcluded element of the specified target bid is reserved for future use and may not be set to true."/>
      <!-- Targeting Improvements April 2014-->
      <error name="InvalidTargetRadiusUnit" errorcode="CampaignServiceRadiusUnitInvalid" code="2943" message="You must set either Miles or Kilometers as the radius unit of a radius target bid."/>
      <error name="InvalidDayTimeTarget" errorcode="CampaignServiceDayTimeTargetInvalid" code="2944" message="The DayTimeTarget is not Valid."/>
      <error name="TargetsDayTimeBidBatchLimitExceeded" errorcode="CampaignServiceTargetsDayTimeBidBatchLimitExceeded" code="2945" message="The number of bids in the DayTimeTarget would be exceeded."/>
      <error name="TargetDayTimeIntervalInvalid" errorcode="CampaignServiceDayTimeTargetBidIntervalInvalid" code="2946" message="The From or To interval of the DayTimeTargetBid is not valid."/>
      <error name="CustomerNotEnabledForDayTimePilot" errorcode="CampaignServiceDayTimePilotNotEnabledForCustomer" code="2947" message="The customer is not in Pilot to use DayTime Targets."/>
      <error name="DuplicatePostalCodeTarget" errorcode="CampaignServiceDuplicatePostalCodeTarget" code="2948" message="The PostalCode Target Bids contain Duplicate PostalCodes."/>
      <error name="PostalCodeNotSupportedForCustomer" errorcode="CampaignServicePostalCodePilotNotEnabledForCustomer" code="2949" message="The customer is not in Pilot to use PostalCode Targets."/>
      <error name="TargetDayTimeAndDayHourAreExclusive" errorcode="CampaignServiceTargetDayTimeAndDayHourAreExclusive" code="2950" message="DayHour and DayTime targets are Exclusive"/>
      <error name="TargetDayTimeOverlapping" errorcode="CampaignServiceDayTimeTargetBidsIntervalOverlap" code="2951" message="The DayTime Target intervals overlap."/>
      <error name="InvalidDayTimeTargetBidAdjustment" errorcode="CampaignServiceDayTimeTargetBidAdjustmentInvalid" code="2952" message="The DayTime Target BidAdjustment is invalid."/>
      <error name="OSNameIsNotSupported" errorcode="CampaignServiceOSNameIsNotSupported" code="2953" message="OSName is not supported"/>
      <error name="RelatedCriterionActionError" errorcode="CampaignServiceRelatedCriterionActionError" code="2954" message="The target cannot be migrated to criterion because of an error with another target subtype."/>
      <error name="InvalidCriterionId" errorcode="CampaignServiceInvalidCriterionId" code="2955" message="The Criterion ID is invalid."/>
      <error name="CriterionDoesNotMatch" errorcode="CampaignServiceCriterionDoesNotMatch" code="2956" message="The Existing Criterion is different from the one passed for the same ID"/>
      <error name="IncompleteDeviceCriterionSet" errorcode="CampaignServiceIncompleteDeviceCriterionSet" code="2957" message="The entity must either be explicitly associated with all device criterion types (Computers, Tablets, Smartphones) or should not have any device criterion."/>
      <error name="InvalidGeolocationsFileVersion" code="2958" errorcode="InvalidGeolocationsFileVersion" message="This is not a valid Geolocations.csv file version."/>
      <error name="InvalidLanguageLocale" code="2959" errorcode="InvalidLanguageLocale" message="This is not a valid LanguageLocale."/>
      <error name="InvalidGeolocationsFileFormat" code="2969" errorcode="InvalidGeolocationsFileFormat" message="This is not a valid Geolocations file format."/>

      <error name="InvalidTargetCombination" code="2970" errorcode="InvalidTargetCombination" message="The target combination is invalid."/>

      <!--AdExtensionDeviceTarget-->
      <error name="AdExtensionDeviceTargetEntityLimitExceeded" errorcode="CampaignServiceAdExtensionDeviceTargetEntityLimitExceeded" code="2960" message="AdExtensions DeviceTarget Entities array exceeds the limit." />
      <error name="AdExtensionDeviceTargeEntityIdsNullOrEmpty" errorcode="CampaignServiceAdExtensionDeviceTargeEntityIdsNullOrEmpty" code="2961" message="AdExtensions DeviceTarget EntityIds cannot be null." />
      <error name="AdExtensionDeviceTargeEntityIdsInvalid" errorcode="CampaignServiceAdExtensionDeviceTargeEntityIdsInvalid" code="2962" message="AdExtensions DeviceTarget EntityIds invalid." />
      <error name="AdExtensionDeviceTargeValueInvalid" errorcode="CampaignServiceAdExtensionDeviceTargeValueInvalidInvalid" code="2963" message="AdExtensions DeviceTarget value invalid." />
      <error name="CustomerNotEnabledForAdExtensionDeviceTargetPilot" errorcode="CampaignServiceCustomerNotEnabledForAdExtensionDeviceTargetPilot" code="2964" message="Customer not enabled for AdExtensionDeviceTarge pilot" />

      <error name="LocationIntentCriterionCannotBeDeleted" errorcode="CampaignServiceLocationIntentCriterionCannotBeDeleted" code="2965" message="Location intent criterion cannot be deleted."/>
      <error name="LocationIntentCriterionInvalid" errorcode="CampaignServiceLocationIntentCriterionInvalid" code="2966" message="The location intent option is invalid."/>
      <error name="BidsMustBeEqualForDeviceType" errorcode="CampaignServiceBidsMustBeEqualForDeviceType" code="2967" message="The bids must be equal for the same device type."/>
      <error name="CustomerNotEnabledForCountyTargets" errorcode="CampaignServiceCustomerNotEnabledForCountyTargets" code="2968" message="The customer is not in pilot to use County Targets."/>

      <!--Keyword-->
      <error name="NullKeyword" errorcode="CampaignServiceNullKeyword" code="1500" message="Keyword is null."/>
      <error name="InvalidKeywordId" errorcode="CampaignServiceInvalidKeywordId" code="1501" message="The Keyword ID is invalid."/>
      <error name="DuplicateInKeywordIds" errorcode="CampaignServiceDuplicateInKeywordIds" code="1502" message="Duplicate IDs are contained in the array of keywords."/>
      <error name="InvalidKeywordText" errorcode="CampaignServiceInvalidKeywordText" code="1503" message="The Keyword text is invalid."/>
      <error name="CannotChangeTextOnUpdate" errorcode="CampaignServiceCannotChangeTextOnUpdate" code="1504" message="The Keyword text cannot be changed on Update."/>
      <error name="KeywordsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceKeywordsArrayShouldNotBeNullOrEmpty" code="1505" message="Keywords array should not be null or empty."/>
      <error name="KeywordsArrayExceedsLimit" errorcode="CampaignServiceKeywordsArrayExceedsLimit" code="1506" message="The list of keywords exceeds the limit."/>
      <error name="InvalidBidAmounts" errorcode="CampaignServiceInvalidBidAmounts" code="1507" message="The bid amounts are invalid."/>
      <error name="InvalidBidAmountForSearchAdGroup" errorcode="CampaignServiceInvalidBidAmountForSearchAdGroup" code="1508" message="The bid amounts are invalid for Search adgroup."/>
      <error name="InvalidBidAmountForContentAdGroup" errorcode="CampaignServiceInvalidBidAmountForContentAdGroup" code="1509" message="The bid amounts are invalid for Content adgroup."/>
      <error name="InvalidBidAmountForHybridAdGroup" errorcode="CampaignServiceInvalidBidAmountForHybridAdGroup" code="1510" message="The bid amounts are invalid for Hybrid adgroup."/>
      <error name="InvalidParam1" errorcode="CampaignServiceInvalidParam1" code="1511" message="The Param 1 is invalid."/>
      <error name="InvalidParam2" errorcode="CampaignServiceInvalidParam2" code="1512" message="The Param 2 is invalid."/>
      <error name="InvalidParam3" errorcode="CampaignServiceInvalidParam3" code="1513" message="The Param 3 is invalid."/>
      <error name="NegativeKeywordRequiresPartialMatchBid" errorcode="CampaignServiceNegativeKeywordRequiresPartialMatchBid" code="1514" message="The Negative keywords requires partial match bid."/>
      <error name="BidAmountsLessThanFloorPrice" errorcode="CampaignServiceBidAmountsLessThanFloorPrice" code="1515" message="Bid amounts are less than floor price."/>
      <error name="BidAmountsGreaterThanCeilingPrice" errorcode="CampaignServiceBidAmountsGreaterThanCeilingPrice" code="1516" message="Bid amounts are greater than ceiling price."/>
      <error name="DuplicateKeyword" errorcode="CampaignServiceDuplicateKeyword" code="1517" message="Trying to create a duplicate keyword."/>
      <error name="MaxKeywordsReachedForAccount" errorcode="CampaignServiceMaxKeywordsReachedForAccount" code="1518" message="Cannot add any more keywords to this account."/>
      <error name="MaxKeywordsReachedForAdGroup" errorcode="CampaignServiceMaxKeywordsReachedForAdGroup" code="1519" message="Cannot add any more keywords to this adgroup."/>
      <error name="ForbiddenWordInKeywordText" errorcode="CampaignServiceForbiddenWordInKeywordText" code="1520" message="There is some forbidden text in the Keyword."/>
      <error name="ForbiddenWordInParam1" errorcode="CampaignServiceForbiddenWordInParam1" code="1521" message="There is some forbidden text in the parameter 1."/>
      <error name="ForbiddenWordInParam2" errorcode="CampaignServiceForbiddenWordInParam2" code="1522" message="There is some forbidden text in the parameter 2."/>
      <error name="ForbiddenWordInParam3" errorcode="CampaignServiceForbiddenWordInParam3" code="1523" message="There is some forbidden text in the parameter 3."/>
      <error name="ExclusiveWordInKeywordText" errorcode="CampaignServiceExclusiveWordInKeywordText" code="1524" message="A reserved word has been used in the Keyword."/>
      <error name="ExclusiveWordInParam1" errorcode="CampaignServiceExclusiveWordInParam1" code="1525" message="A reserved word has been used in the parameter 1."/>
      <error name="ExclusiveWordInParam2" errorcode="CampaignServiceExclusiveWordInParam2" code="1526" message="A reserved word has been used in the parameter 2."/>
      <error name="ExclusiveWordInParam3" errorcode="CampaignServiceExclusiveWordInParam3" code="1527" message="A reserved word has been used in the parameter 3."/>
      <error name="KeywordDoesNotBelongToAdGroupId" errorcode="CampaignServiceKeywordDoesNotBelongToAdGroupId" code="1528" message="Keyword specified does not belong to the AdGroup."/>
      <error name="KeywordIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceKeywordIdsArrayShouldNotBeNullOrEmpty" code="1529" message="Keyword Ids array should not be null or empty."/>
      <error name="KeywordIdsArrayExceedsLimit" errorcode="CampaignServiceKeywordIdsArrayExceedsLimit" code="1530" message="The list of keyword Ids exceeds the limit."/>
      <error name="InvalidKeywordStatus" errorcode="CampaignServiceInvalidKeywordStatus" code="1531" message="The keyword status is invalid for the current operation."/>
      <error name="InvalidKeywordEditorialStatus" errorcode="CampaignServiceInvalidKeywordEditorialStatus" code="1532" message="Setting Editorial Status is not allowed."/>
      <error name="CannotSetExemptionRequestOnKeyword" errorcode="CampaignServiceCannotSetExemptionRequestOnKeyword" code="1533" message="Exemption Request for ad is not currently supported and hence cannot be set."/>
      <error name="UpdateKeywordEmpty" errorcode="CampaignServiceUpdateKeywordEmpty" code="1534" message="Update keyword entity has no data"/>
      <error name="KeywordIdDuplicateInRequest" errorcode="CampaignServiceDuplicateKeywordIdInRequest" code="1536" message="Duplicate Keyword Ids in the request."/>
      <error name="CannotAddKeywordToSpecifiedAdGroup" errorcode="CampaignServiceCCannotAddKeywordToSpecifiedAdGroup" code="1537" message="Cannot add keyword to an adgroup whose bidding model type is not Keyword."/>
      <error name="InvalidBidAmount" errorcode="CampaignServiceInvalidBidAmount" code="1538" message="The bid amount is invalid."/>
      <error name="MaxKeywordsReachedForCustomer" errorcode="CampaignServiceMaxKeywordsReachedForCustomer" code="1539" message="Cannot add any more keywords to this customer."/>

      <error name="MultipleKeywordBidTypesNotAllowed" errorcode="CampaignServiceMultipleKeywordBidTypesNotAllowed" code="1541" message="You must set only one of the match type bids (for example, ExactMatchBid) in the Keyword object to a non-null value. To specify multiple match type bids for a keyword, you must create a Keyword object for each keyword and match type combination."/>
      <error name="KeywordAndMatchTypeCombinationAlreadyExists " errorcode="CampaignServiceKeywordAndMatchTypeCombinationAlreadyExists" code="1542" message="A keyword with the specified match type already exists."/>
      <error name="KeywordBidRequired" errorcode="CampaignServiceKeywordBidRequired" code="1543" message="The Keyword object does not contain a valid and non-null bid for the specified match type."/>
      <error name="KeywordZeroBidAmountNotAllowed" errorcode="CampaignServiceKeywordZeroBidAmountNotAllowed" code="1544" message="You cannot set the amount of a match type bid to zero (0)."/>
      <error name="KeywordMatchTypeChangeNotAllowedInUpdate" errorcode="CampaignServiceKeywordMatchTypeChangeNotAllowedInUpdate" code="1545" message="You cannot change the match type on which you bid when you update a Keyword object."/>
      <error name="KeywordByMatchTypePilotNotEnabledForCustomer" errorcode="CampaignServiceKeywordByMatchTypePilotNotEnabledForCustomer" code="1546" message="The KeywordByMatchType pilot is not enabled for the given customer."/>
      <error name="MatchTypeNotUpdated" errorcode="CampaignServiceMatchTypeNotUpdated" code="1548" message="You cannot set UpdateMatchType to true unless you specify a new match type bid."/>
      <error name="KeywordDestinationUrlsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceKeywordDestinationUrlsArrayShouldNotBeNullOrEmpty" code="1549" message="You need to pass an array for KeywordDestinationUrls."/>
      <error name="TooMuchTextInKeywordDestinationUrl" errorcode="CampaignServiceTooMuchTextInKeywordDestinationUrl" code="1550" message="Too much text is provided in keyword destination URL."/>
      <error name="KeywordTransactionalDependencyFailed" errorcode="CampaignServiceKeywordTransactionalDependencyFailed" code="1551" message="This operation is transaction dependent on another failed operation"/>
      <error name="MatchTypeRequired" errorcode="CampaignServiceMatchTypeRequired" code="1552" message="The match type is required."/>

      <!--KeywordAdAssociation-->
      <error name="InvalidStatus" errorcode="CampaignServiceInvalidStatus" code="1600" message="The Keyword ad association status is invalid."/>
      <error name="StatusIsNull" errorcode="CampaignServiceStatusIsNull" code="1601" message="The Keyword ad association status is not passed."/>
      <error name="InvalidModifiedAfterDate" errorcode="CampaignServiceInvalidModifiedAfterDate" code="1602" message="The Modified-After date is invalid."/>
      <error name="ModifiedAfterDateNotInUtc" errorcode="CampaignServiceModifiedAfterDateNotInUtc" code="1603" message="The Modified-After date is not in UTC."/>
      <error name="EntityNotAllowedForShoppingCampaign" errorcode="CampaignServiceEntityNotAllowedForShoppingCampaign" code="1604" message="You cannot add this entity to a campaign of type Shopping."/>
      <error name="EntityNotAllowedForDynamicSearchAdsCampaign" errorcode="CampaignServiceEntityNotAllowedForDynamicSearchAdsCampaign" code="1605" message="You cannot add this entity to a campaign of type DynamicSearchAds."/>
      <error name="DynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign" errorcode="CampaignServiceDynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign" code="1606" message="You cannot add Dynamic Search Ad to a non DynamicSearchAdscampaign."/>
      <error name="EntityNotAllowedForAudienceCampaign" errorcode="CampaignServiceEntityNotAllowedForAudienceCampaign" code="1607" message="You cannot add this entity to a campaign of type Audience."/>

      <!--KeywordEstimate-->
      <error name="NullKeywordBid" errorcode="CampaignServiceNullKeywordBid" code="1700" message="KeywordBid is null."/>
      <error name="KeywordBidsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceKeywordBidsArrayShouldNotBeNullOrEmpty" code="1701" message="KeywordBids array should not be null or empty."/>
      <error name="KeywordBidsArrayExceedsLimit" errorcode="CampaignServiceKeywordBidsArrayExceedsLimit" code="1702" message="The list of keywordBids exceeds the limit."/>
      <error name="KeywordBidsInvalidKeyword" errorcode="CampaignServiceKeywordBidsInvalidKeyword" code="1703" message="Invalid Keyword in the bid."/>
      <error name="AtleastOneKeywordBidShouldBeSpecified" errorcode="CampaignServiceAtleastOneKeywordBidShouldBeSpecified" code="1704" message="Please specify at least one of the keyword bids."/>
      <error name="InvalidLanguageAndRegionValue" errorcode="InvalidLanguageAndRegionValue" code="1705" message="Invalid value passed for LanguageAndRegion. Please refer to documentation for valid values or leave empty."/>

      <!--Entity-->
      <error name="InvalidEntity" errorcode="CampaignServiceInvalidEntity" code="1706" message ="Invalid EntityId passed in the array of EntityIds."/>
      <error name="DuplicateEntity" errorcode="CampaignServiceDuplicateEntity" code ="1707" message="Duplicate EntityID passed in the array of EntityIds."/>
      <error name="JustificationTextMissingForInlineAppeal" errorcode="CampaignServiceJustificationTextMissingForInlineAppeal" code ="1708" message ="Appeal creation failed because JustificationText is missing."/>
      <error name="JustificationTextTooLongForInlineAppeal" errorcode="CampaignServiceJustificationTextTooLongForInlineAppeal" code ="1709" message="Appeal creation failed because Justification text was too long (should be less than 2000 characters)."/>
      <error name="EditorialAppealCreationQuotaExceeded" errorcode="CampaignServiceAppealCreationQuotaExceeded" code ="1710" message="Appeal creation quota Exceeded."/>
      <error name="EditorialAppealCreationQuotaExceededForLast24Hours" errorcode="CampaignServiceAppealCreationQuotaExceededForLast24Hours" code ="1711" message="Appeal creation quota Exceeded for last 24 hours."/>
      <error name="EditorialAppealEntityAlreadyAppealed" errorcode="CampaignServiceEditorialAppealEntityAlreadyAppealed" code ="1712" message="Appeal already created for the entity."/>

      <!--Segment-->
      <error name="NullSegment" errorcode="CampaignServiceNullSegment" code="1900" message="Segment is null."/>
      <error name="InvalidSegmentId" errorcode="CampaignServiceInvalidSegmentId" code="1901" message="The Segment Id is invalid."/>
      <error name="DuplicateInSegmentIds" errorcode="CampaignServiceDuplicateInSegmentIds" code="1902" message="Duplicate Ids are contained in the array of segments."/>
      <error name="InvalidSegmentName" errorcode="CampaignServiceInvalidSegmentName" code="1903" message="The Segment name is invalid."/>
      <error name="InvalidUserHash" errorcode="CampaignServiceInvalidUserHash" code="1904" message="The user hash specified is invalid."/>
      <error name="SegmentsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceSegmentsArrayShouldNotBeNullOrEmpty" code="1905" message="Segments array should not be null or empty."/>
      <error name="SegmentsArrayExceedsLimit" errorcode="CampaignServiceSegmentsArrayExceedsLimit" code="1906" message="The list of segments exceeds the limit."/>
      <error name="DuplicateSegmentName" errorcode="CampaignServiceDuplicateSegmentName" code="1907" message="There is a duplicate segment name in the array."/>
      <error name="SegmentOperationNotAllowedForPilot" errorcode="CampaignServiceSegmentOperationNotAllowedForPilot" code="1908" message="Customer Account is not enabled to perform operations on segments."/>
      <error name="UserHashArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceUserHashArrayShouldNotBeNullOrEmpty" code="1909" message="User Hash array should not be null or empty."/>
      <error name="UserHashArrayExceedsLimit" errorcode="CampaignServiceUserHashArrayExceedsLimit" code="1910" message="The list of User Hashes exceeds the limit."/>
      <error name="SegmentIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceSegmentIdsArrayShouldNotBeNullOrEmpty" code="1911" message="Segment Ids array should not be null or empty."/>
      <error name="SegmentIdsArrayExceedsLimit" errorcode="CampaignServiceSegmentIdsArrayExceedsLimit" code="1912" message="The list of segment Ids exceeds the limit."/>
      <error name="MaxSegmentsForCustomerHasBeenReached" errorcode="CampaignServiceMaxSegmentsForCustomerHasBeenReached" code="1913" message="The limit of segments for the customer has been reached."/>
      <error name="SegmentNotAllowedForDistributionChannel" errorcode="CampaignServiceSegmentNotAllowedForDistributionChannel" code="1914" message="Segment targeting not available for this ad group's DistributionChannel."/>

      <error name="InvalidUrl" code="2611" errorcode="CampaignServiceInvalidUrl" message="Invalid URL has been specified."/>
      <error name="DuplicateUrl" errorcode="CampaignServiceDuplicateUrl" code="2619" message="Found a duplicate Url in the input."/>
      <error name="UncrawlableUrl" errorcode="CampaignServiceUncrawlableUrl" code="2629" message="The provided URL was not crawlable."/>

      <!--BehavioralBid-->
      <error name="NullBehavioralBid" code="2700" errorcode="CampaignServiceNullBehavioralBid" message="BehavioralBid is null."/>
      <error name="InvalidBehavioralBidId" code="2701" errorcode="CampaignServiceInvalidBehavioralBidId" message="The BehavioralBidId is invalid."/>
      <error name="DuplicateInBehavioralBidIds" code="2702" errorcode="CampaignServiceDuplicateInBehavioralBidIds" message="Duplicate Ids are contained in the array of BehavioralBids."/>
      <error name="BehavioralBidsArrayShouldNotBeNullOrEmpty" code="2703" errorcode="CampaignServiceBehavioralBidsArrayShouldNotBeNullOrEmpty" message="BehavioralBids array should not be null or empty."/>
      <error name="BehavioralBidsArrayExceedsLimit" code="2704" errorcode="CampaignServiceBehavioralBidsArrayExceedsLimit" message="The list of BehavioralBids exceeds the limit."/>
      <error name="BehavioralBidOperationNotAllowedForPilot" code="2705" errorcode="CampaignServiceBehavioralBidOperationNotAllowedForPilot" message="Customer Account is not enabled to perform operations on BehavioralBids."/>
      <error name="BehavioralBidIdsArrayShouldNotBeNullOrEmpty" code="2706" errorcode="CampaignServiceBehavioralBidIdsArrayShouldNotBeNullOrEmpty" message="BehavioralBid Ids array should not be null or empty."/>
      <error name="BehavioralBidIdsArrayExceedsLimit" code="2707" errorcode="CampaignServiceBehavioralBidIdsArrayExceedsLimit" message="The list of BehavioralBid Ids exceeds the limit."/>
      <error name="InvalidBehavioralBidName" code="2708" errorcode="CampaignServiceInvalidBehavioralBidName" message="The behavioral bid name is invalid."/>
      <error name="CannotChangeBehavioralName" code="2709" errorcode="CampaignServiceCannotChangeBehavioralName" message="The behavioral name cannot be changed."/>
      <error name="NothingToUpdateInBehavioralBidRequest" errorcode="CampaignServiceNothingToUpdateInBehavioralBidRequest" code="2710" message="There is no information to update in the given behavioral bid request."/>
      <error name="CannotAddBehavioralBidToSpecifiedAdGroup" errorcode="CampaignServiceCannotAddBehavioralBidToSpecifiedAdGroup" code="2711" message="Cannot add behavioral bids to an adgroup whose bidding model type is not BehavioralBid."/>
      <error name="BTBiddingNotEnabledForPilot" errorcode="CampaignServiceBTBiddingNotEnabledForPilot" code="2712" message="The BT Bidding pilot is not enabled for the customer."/>
      <error name="DuplicateBehavioralBid" errorcode="CampaignServiceDuplicateBehavioralBid" code="2713" message="Trying to add a duplicate behavioral bid."/>
      <error name="InvalidBehavioralBidStatus" errorcode="CampaignServiceInvalidBehavioralBidStatus" code="2714" message="The behavioral Bid status is invalid for the current operation."/>
      <error name="InvalidMediumAndBiddingStrategyCombination" errorcode="CampaignServiceInvalidMediumAndBiddingStrategyCombination" code="2715" message="Invalid ad group medium and bidding strategy combination."/>
      <error name="CannotChangeSegmentId" code="2716" errorcode="CampaignServiceCannotChangeSegmentId" message="The segment Id cannot be changed."/>
      <error name="NameAndSegmentIdMutuallyExclusive" code="2717" errorcode="CampaignServiceNameAndSegmentIdMutuallyExclusive" message="Either segment Id or name must be passed."/>
      <error name="InvalidIds" code="2718" errorcode="CampaignServiceInvalidIds" message="Invalid Ids are passed."/>

      <!-- Exclusions -->
      <error name="InvalidGeoEntityType" errorcode="CampaignServiceInvalidGeoEntityType" code="2810" message="The Geo Entity Type is invalid."/>
      <error name="ExcludedLocationNameIsNullOrEmpty" errorcode="CampaignServiceExcludedLocationNameIsNullOrEmpty" code="2811" message="The Excluded Location name is null or empty."/>
      <error name="LocationExclusionIsNull" errorcode="CampaignServiceLocationExclusionIsNull" code="2812" message="The Location Exclusion parameter is null."/>
      <error name="ExclusionTypeMappedToIncorrectAssociatedEntity" errorcode="CampaignServiceExclusionTypeMappedToIncorrectAssociatedEntity" code="2813" message="The exclusion type is mapped to an incorrect associated entity."/>
      <error name="ExclusionTypeIsInvalid" errorcode="CampaignServiceExclusionTypeIsInvalid" code="2814" message="One or more of the specified exclusion types are not valid."/>
      <error name="LocationExclusionBatchLimitExceeded" errorcode="CampaignServiceLocationExclusionBatchLimitExceeded" code="2815" message="The batch limit for setting Location Exclusions against targets has been exceeded. Valid batch limit is 1."/>
      <error name="AssociatedEntityIsNull" errorcode="CampaignServiceAssociatedEntityIsNull" code="2816" message="The entity to associate the exclusion with cannot be null."/>
      <error name="ExclusionIsNull" errorcode="CampaignServiceAssociatedExclusionIsNull" code="2817" message="The exclusion to associate the entity with cannot be null."/>
      <error name="ExclusionToEntityAssociationIsNull" errorcode="CampaignServiceAssociatedExclusionToEntityAssociationIsNull" code="2818" message="The exclusion to entity association cannot be null."/>
      <error name="ExclusionToEntityAssociationCollectionIsNull" errorcode="CampaignServiceAssociatedExclusionToEntityAssociationCollectionIsNull" code="2819" message="The collection of exclusion to entity associations cannot be null."/>
      <error name="LocationExclusionPilotNotEnabledForCustomer" errorcode="CampaignServiceLocationExclusionPilotNotEnabledForCustomer" code="2820" message="The customer is not a member of the Location Exclusion pilot program."/>
      <error name="ExcludedGeoTargetsBatchLimitExceeded" errorcode="CampaignServiceLocationExcludedGeoTargetsBatchLimitExceeded" code="2821" message="The batch limit for Excluded Geo Targets in a Location Exclusion has been exceeded. Valid batch limit is 255."/>
      <error name="ConflictWithLocationExclusion" errorcode="CampaignServiceConflictWithLocationExclusion" code="2822" message="The location target conflicts with location exclusion." />
      <error name="DuplicateExcludedGeoTargets" errorcode="CampaignServiceDuplicateExcludedGeoTargets" code="2823" message="The excluded geo targets cannot have duplicate entries." />
      <error name="LocationExclusionIsInvalid" errorcode="CampaignServiceLocationExclusionIsInvalid" code="2824" message="The Location exclusion parameter is invalid." />
      <error name="ExcludedGeoTargetIsNull" errorcode="CampaignServiceExcludedGeoTargetIsNullOrEmpty" code="2825" message="The excluded geo target parameter is null." />
      <error name="EntityTypeIsInvalid" errorcode="CampaignServiceEntityTypeIsInvalid" code="2826" message="The entity type parameter is invalid." />
      <error name="EntitiesArrayExceedsLimit" errorcode="CampaignServiceEntitiesArrayExceedsLimit" code="2827" message="The list of entities exceeds the limit." />
      <error name="ExcludedRadiusTargetSetNotEnabled" errorcode="CampaignServiceExcludedRadiusTargetSetNotEnabled" code="2828" message="The set operation on excluded radius target is not enabled." />
      <error name="EntitiesArrayIsNullOrEmpty" errorcode="CampaignServiceEntitiesArrayIsNullOrEmpty" code="2829" message="The list of entities cannot be null or empty." />
      <error name="ConflictWithExclusionTarget" errorcode="CampaignServiceConflictWithExclusionTarget" code="2830" message="The target conflicts with exclusion target." />

      <!--Paging-->
      <error name="PagingInfoInvalid" errorcode="CampaignServicePagingInfoInvalid" code="3032" message="Paging info is invalid."/>

      <!--Normalization-->
      <error name="TextTooLong" errorcode="CampaignServiceTextTooLong" code="3101" message="The text is too long."/>
      <error name="StringsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceStringsArrayShouldNotBeNullOrEmpty" code="3102" message="Strings array should not be null or empty."/>
      <error name="StringsArrayExceedsLimit" errorcode="CampaignServiceStringsArrayExceedsLimit" code="3103" message="The list of strings exceeds the limit."/>
      <error name="NullText" errorcode="CampaignServiceNullText" code="3104" message="The text is null."/>

      <!--BulkApi-->
      <error name="AccountIdsLengthExceeded" errorcode="CampaignServiceAccountIdsLengthExceeded" code="3201" message="The list of account IDs exceeds the maximum number allowed."/>
      <error name="AccountIdsCampaignsEmpty" errorcode="CampaignServiceAccountIdsCampaignsEmpty" code="3202" message="AccountIds and Campaigns arrays are empty. At least one of them should be populated."/>
      <error name="CampaignsContainsMultipleAccounts" errorcode="CampaignServiceCampaignsContainsMultipleAccounts" code="3203" message="The list of campaigns must belong to the same account."/>
      <error name="CampaignsContainsNullScope" errorcode="CampaignServiceCampaignsContainsNullScope" code="3204" message="The list of campaigns cannot contain null items."/>
      <error name="InvalidDownloadRequestId" errorcode="CampaignServiceInvalidDownloadRequestId" code="3205" message="The DownloadRequestId is invalid."/>
      <error name="AccountsAndCampaignsLengthExceeded" errorcode="CampaignServiceAccountsAndCampaignsLengthExceeded" code="3206" message="The lengths of campaigns and accounts put together is exceeded."/>
      <error name="AccountTooBigToDownload" errorcode="CampaignServiceAccountTooBigToDownload" code="3207" message="The account has more keywords than allowed per request. Please call DownloadCampaignsByCampaignIds to download the account's campaigns in multiple requests. The Details field contains the campaign identifiers under the account."/>
      <error name="LastSyncTimeCannotBeInTheFuture" errorcode="CampaignServiceLastSyncTimeCannotBeInTheFuture" code="3208" message="The last sync time cannot be later than now."/>
      <error name="LastSyncTimeTooOld" errorcode="CampaignServiceLastSyncTimeTooOld" code="3209" message="The last sync time cannot be earlier than 30 days"/>
      <error name="MinimumRequiredEntitiesNotSpecified" errorcode="BulkServiceMinimumRequiredEntitiesNotSpecified" code="3210" message="The minimum set of required entities should be specified for bulk download."/>
      <error name="BulkDownloadUnsupportedEntities" errorcode="CampaignServiceBulkDownloadUnsupportedEntities" code="3211" message="One or more specified entities are not supported for bulk download."/>
      <error name="InvalidSyncTimeForDataScopeSelected" errorcode="BulkServiceInvalidSyncTimeForDataScopeSelected" code="3213" message="The LastSyncTimeInUTC element should be null when including performance statistics, bid suggestions, or quality score in the requested data scope."/>
      <error name="InvalidBulkCustomDateRange" errorcode="BulkServiceInvalidCustomDateRange" code="3215" message="The start and end date for performance statistics should be valid and within the supported range."/>
      <error name="CampaignsTooBigToDownload" errorcode="BulkServiceCampaignsTooBigToDownload" code="3216" message="The campaigns included in the download have more keywords than allowed per request. Please call DownloadCampaignsByCampaignIds with fewer campaigns."/>
      <error name="FormatVersionNotSupported" errorcode="BulkServiceFormatVersionNotSupported" code="3217" message="The specified format version is not supported."/>
      <error name="EntityNotSupportedForFormatVersion" errorcode="BulkServiceEntityNotSupportedForFormatVersion" code="3218" message="One or more bulk download entities are not supported with the specified format version."/>
      <error name="FormatVersionRequired" errorcode="BulkServiceFormatVersionRequired" code="3219" message="The format version is required."/>
      <error name="InvalidBulkUploadUrl" errorcode="BulkServiceInvalidBulkUploadUrl" code="3220" message="Invalid bulk upload URL."/>
      <error name="NoFileFound" errorcode="BulkServiceNoFileFound" code="3221" message="No file uploaded."/>
      <error name="MultipleFilesFound" errorcode="BulkServiceMultipleFilesFound" code="3222" message="More than one file uploaded."/>
      <error name="InvalidFileExtension" errorcode="BulkServiceInvalidFileExtension" code="3223" message="Unrecognised file extension."/>
      <error name="UrlAlreadyUsedForUpload" errorcode="BulkServiceUrlAlreadyUsedForUpload" code="3224" message="The URL has already been used for file upload."/>
      <error name="FileRowCountExceeded" errorcode="BulkServiceFileRowCountExceeded" code="3225" message="The number of rows in the file has exceeded the maximum allowed value."/>
      <error name="UrlExpired" errorcode="BulkServiceUrlExpired" code="3226" message="The URL has expired and cannot be used for uploads."/>
      <error name="TooManyRequests" errorcode="BulkServiceTooManyRequests" code="3227" message="Too many requests."/>
      <error name="DataScopeInvalid" errorcode="BulkServiceDataScopeInvalid" code="3228" message="The specified DataScope value is either invalid or no longer supported."/>
      <error name="EntityRecordsArrayIsNullOrEmpty" errorcode="BulkServiceEntityRecordsArrayIsNullOrEmpty" code="3229" message="The entity records cannot be null or empty."/>
      <error name="EntityRecordsArrayLimitExceeded" errorcode="BulkServiceEntityRecordsArrayLimitExceeded" code="3230" message="The list of entity records exceeds the limit."/>

      <!--Analytics-->
      <error name="InvalidGoalId" errorcode="CampaignServiceInvalidGoalId" code="3300" message="The Goal Id is invalid."/>
      <error name="DuplicateInGoalIds" errorcode="CampaignServiceDuplicateInGoalIds" code="3301" message="Duplicate IDs are contained in the array of goals."/>
      <error name="GoalsArrayShouldNotBeNullOrEmpty" code="3302" errorcode="CampaignServiceGoalsArrayShouldNotBeNullOrEmpty" message="Goals array should not be null or empty."/>
      <error name="GoalsArrayExceedsLimit" errorcode="CampaignServiceGoalsArrayExceedsLimit" code="3303" message="The list of goals exceeds the limit."/>
      <error name="GoalIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceGoalIdsArrayShouldNotBeNullOrEmpty" code="3304" message="Goal Ids array should not be null or empty."/>
      <error name="AccountIdsArrayExceedsLimit" errorcode="CampaignServiceAccountIdsArrayExceedsLimit" code="3305" message="The list of account Ids exceeds the limit."/>
      <error name="AccountIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAccountIdsArrayShouldNotBeNullOrEmpty" code="3306" message="Account Ids array should not be null or empty."/>
      <error name="AccountAnalyticsTypesArrayExceedsLimit" errorcode="CampaignServiceAccountAnalyticsTypesArrayExceedsLimit" code="3307" message="The list of AccountAnalyticsTypes exceeds the limit."/>
      <error name="AccountAnalyticsTypesArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAccountAnalyticsTypesArrayShouldNotBeNullOrEmpty" code="3308" message="AccountAnalyticsTypes array should not be null or empty."/>
      <error name="GoalNotPassed" errorcode="CampaignServiceGoalNotPassed" code="3309" message="Goal passed is null."/>
      <error name="GoalNameMissing" errorcode="CampaignServiceGoalNameMissing" code="3310" message="Goal name is required."/>
      <error name="GoalNameTooLong" errorcode="CampaignServiceGoalNameTooLong" code="3311" message="Goal name too long."/>
      <error name="GoalNameAlreadyExists" errorcode="CampaignServiceGoalNameAlreadyExists" code="3312" message="Duplicate goal name."/>
      <error name="GoalNameHasInvalidChars" errorcode="CampaignServiceGoalNameHasInvalidChars" code="3313" message="Goal name has invalid characters."/>
      <error name="DaysApplicableForConversionIsRequired" errorcode="CampaignServiceDaysApplicableForConversionIsRequired" code="3314" message="DaysApplicableForConversion is required."/>
      <error name="GoalIdIsRequired" errorcode="CampaignServiceGoalIdIsRequired" code="3315" message="Goal Id is required."/>
      <error name="GoalIdMustBeNull" errorcode="CampaignServiceGoalIdMustBeNull" code="3316" message="Goal Id must be null."/>
      <error name="GoalIdAlreadyExists" errorcode="CampaignServiceGoalIdAlreadyExists" code="3317" message="Duplicate goal Id."/>
      <error name="GoalCostModelCannotbeCombinedWithNone" errorcode="CampaignServiceGoalCostModelCannotbeCombinedWithNone" code="3318" message="Cost model cannot be combined with none."/>
      <error name="GoalRevenueAmountRequiredForConstantModel" errorcode="CampaignServiceGoalRevenueAmountRequiredForConstantModel" code="3319" message="Amount is required for constant revenue model."/>
      <error name="GoalRevenueAmountMustBeNullForVariableModel" errorcode="CampaignServiceGoalRevenueAmountMustBeNullForVariableModel" code="3320" message="Amount must be null for variable revenue model."/>
      <error name="GoalRevenueAmountMustBeNullForNullModel" errorcode="CampaignServiceGoalRevenueAmountMustBeNullForNullModel" code="3321" message="Amount must be null for null revenue model."/>
      <error name="GoalRevenueAmountValidOnlyForConstantModel" errorcode="CampaignServiceGoalRevenueAmountValidOnlyForConstantModel" code="3322" message="Amount is valid only for constant revenue model."/>
      <error name="GoalRevenueAmountLessThanMinimum" errorcode="CampaignServiceGoalRevenueAmountLessThanMinimum" code="3323" message="Revenue amount is less than minimum allowed."/>
      <error name="GoalRevenueAmountMoreThanMax" errorcode="CampaignServiceGoalRevenueAmountMoreThanMax" code="3324" message="Revenue amount is more than maximum allowed."/>
      <error name="GoalRevenueModelIsRequired" errorcode="CampaignServiceGoalRevenueModelIsRequired" code="3325" message="Revenue model is required."/>
      <error name="GoalCostModelIsRequired" errorcode="CampaignServiceGoalCostModelIsRequired" code="3326" message="Cost model is required."/>
      <error name="MaxGoalsLimitExceededForAccount" errorcode="CampaignServiceMaxGoalsLimitExceededForAccount" code="3327" message="Limit of goals for account has been exceeded."/>
      <error name="StepNotPassed" errorcode="CampaignServiceStepNotPassed" code="3328" message="Step passed is null."/>
      <error name="StepNameMissing" errorcode="CampaignServiceStepNameMissing" code="3329" message="Step name is required."/>
      <error name="StepNameTooLong" errorcode="CampaignServiceStepNameTooLong" code="3330" message="Goal name too long."/>
      <error name="StepNameAlreadyExists" errorcode="CampaignServiceStepNameAlreadyExists" code="3331" message="Duplicate goal name."/>
      <error name="StepNameHasInvalidChars" errorcode="CampaignServiceStepNameHasInvalidChars" code="3332" message="Goal name has invalid characters."/>
      <error name="StepIdIsRequired" errorcode="CampaignServiceStepIdIsRequired" code="3333" message="Goal Id is required."/>
      <error name="StepIdMustBeNull" errorcode="CampaignServiceStepIdMustBeNull" code="3334" message="Goal Id must be null."/>
      <error name="StepIdAlreadyExists" errorcode="CampaignServiceStepIdAlreadyExists" code="3335" message="Duplicate goal Id."/>
      <error name="StepTypeIsRequired" errorcode="CampaignServiceStepTypeIsRequired" code="3336" message="Step type is required."/>
      <error name="StepTypeMustBeNull" errorcode="CampaignServiceStepTypeMustBeNull" code="3337" message="Step type must be null."/>
      <error name="PositionNumberIsRequired" errorcode="CampaignServicePositionNumberIsRequired" code="3338" message="Position number is required."/>
      <error name="PositionNumberMustBeNull" errorcode="CampaignServicePositionNumberMustBeNull" code="3339" message="Position number must be null."/>
      <error name="PositionNumberAlreadyExists" errorcode="CampaignServicePositionNumberAlreadyExists" code="3340" message="Position number must be unique."/>
      <error name="MaxStepsLimitExceededForGoal" errorcode="CampaignServiceMaxStepsLimitExceededForGoal" code="3341" message="Limit of steps per goal has been exceeded."/>
      <error name="AtleaseOneStepRequiredForGoal" errorcode="CampaignServiceAtleaseOneStepRequiredForGoal" code="3342" message="Atleast one step is required for a goal."/>
      <error name="AtleaseOneConversionStepRequiredForGoal" errorcode="CampaignServiceAtleaseOneConversionStepRequiredForGoal" code="3343" message="Atleast one conversion step is required for a goal."/>
      <error name="OnlyOneLeadStepAllowedForGoal" errorcode="CampaignServiceOnlyOneLeadStepAllowedForGoal" code="3344" message="Only one lead step is allowed for a goal."/>
      <error name="OnlyOneConversionStepAllowedForGoal" errorcode="CampaignServiceOnlyOneConversionStepAllowedForGoal" code="3345" message="Only one lead step is allowed for a goal."/>
      <error name="AnalyticsSettingCampaignLevelDeprecated" errorcode="CampaignServiceAnalyticsSettingCampaignLevelDeprecated" code="3346" message="Analytics settings deprecated at campaign level."/>
      <error name="InvalidGoalCategory" errorcode="InvalidGoalCategory" code="3347" message="The Goal Category is invalid."/>
      <error name="InvalidRegularExpression" errorcode="InvalidRegularExpression" code="3350" message="The regular expression is invalid."/>
      <error name="CustomerNotEligibleForGoalCategory" errorcode="CustomerNotEligibleForGoalCategory" code="3348" message="Customer not eligible for Goal Category."/>
      <error name="InvalidCategoryForGoalType " errorcode="InvalidCategoryForGoalType " code="3349" message="The requested category is not valid for this goal type."/>

      <!--AdIntelligence-->
      <error name="LanguageNotSupported" errorcode="CampaignServiceLanguageNotSupported" code="3400" message="This language is not supported."/>
      <error name="MaxKeywordsRequestedMoreThanSupportNumber" errorcode="CampaignServiceMaxKeywordsRequestedMoreThanSupportNumber" code="3401" message="The maximum keywords requested is more than the supported maximum."/>
      <error name="InvalidMaxKeywords" errorcode="CampaignServiceInvalidMaxKeywords" code="3402" message="The maximum keywords requested is invalid."/>
      <error name="InvalidMinConfidenceScore" errorcode="CampaignServiceInvalidMinConfidenceScore" code="3403" message="The minimum confidence score is invalid."/>
      <error name="InvalidStartDate" errorcode="CampaignServiceInvalidStartDate" code="3404" message="The start date is invalid."/>
      <error name="InvalidEndDate" errorcode="CampaignServiceInvalidEndDate" code="3405" message="The end date is invalid."/>
      <error name="StartDateGreaterThanEndDate" errorcode="CampaignServiceStartDateGreaterThanEndDate" code="3406" message="The start date is later than the end date."/>
      <error name="InvalidMaxSuggestionsPerKeyword" errorcode="CampaignServiceInvalidMaxSuggestionsPerKeyword" code="3407" message="The maximum suggestions per keyword is invalid."/>
      <error name="LanguageAndCountryNotSupported" errorcode="CampaignServiceLanguageAndCountryNotSupported" code="3408" message="The langauge and publisher country/region combination is invalid."/>
      <error name="InvalidMatchTypes" errorcode="CampaignServiceInvalidMatchTypes" code="3409" message="The matchTypes is invalid."/>
      <error name="PublisherCountryArrayExceedsLimit" errorcode="CampaignServicePublisherCountryArrayExceedsLimit" code="3410" message="The publisher country/region array specified exceeds limit."/>
      <error name="InvalidDate" errorcode="CampaignServiceInvalidDate" code="3411" message="The date provided is invalid."/>
      <error name="InvalidLanguage" errorcode="CampaignServiceInvalidLanguage" code="3412" message="The language provided is invalid."/>
      <error name="InvalidPublisherCountry" errorcode="CampaignServiceInvalidPublisherCountry" code="3413" message="The publisher country/region provided is invalid."/>

      <!--Bulk API-->
      <error name="BulkApiNotEnabledForPilot" errorcode="CampaignServiceBulkApiNotEnabledForPilot" code="3500" message="The customer is not a member of the bulk download pilot program."/>
      <error name="BulkUploadFeatureNotEnabledForCustomer" errorcode="CampaignServiceBulkUploadFeatureNotEnabledForCustomer" code="3501" message="The customer is not a member of the bulk upload program."/>

      <!--Account Level APIs-->
      <error name="AccountIdsBatchLimitExceeded" errorcode="CampaignServiceAccountIdsBatchLimitExceeded" code="3600" message="The list of account IDs exceeds the maximum number of IDs allowed. The Details field contains the maximum number of IDs."/>
      <error name="DuplicateInAccountIds" errorcode="CampaignServiceDuplicateInAccountIds" code="3601" message="The list of account IDs contains duplicates. Please ensure that the list contains only unique account IDs."/>
      <error name="InvalidMigrationTypeFilter" errorcode="CampaignServiceInvalidMigrationTypeFilter" code="3602" message="The migration filter value is not valid."/>
      <error name="FullSyncRequired" errorcode="CampaignServiceFullSyncRequired" code="3603" message="A full sync on the account is required."/>
      <error name="AccountPropertyNameInvalid" errorcode="CampaignServiceAccountPropertyNameInvalid" code="3604" message="AccountPropertyName is invalid."/>
      <error name="MSCLKIDAutoTaggingEnabledValueInvalid" errorcode="CampaignServiceMSCLKIDAutoTaggingEnabledValueInvalid" code="3605" message="The value of MSCLKIDAutoTaggingEnabled is invalid."/>
      <error name="AccountPropertiesNullOrEmpty" errorcode="CampaignServiceAccountPropertiesNullOrEmpty" code="3606" message="AccountProperties cannot be null or empty."/>
      <error name="AccountPropertyIsNull" errorcode="CampaignServiceAccountPropertyIsNull" code="3607" message="AccountProperty is null."/>
      <error name="AccountPropertyNamesNullOrEmpty" errorcode="CampaignServiceAccountPropertyNamesNullOrEmpty" code="3608" message="AccountPropertyNames cannot be null or empty."/>
      <error name="DuplicateAccountPropertyName" errorcode="CampaignServiceDuplicateAccountPropertyName" code="3609" message="The list of AccountProperty contains duplicate AccountPropertyName."/>
      <error name="CannotUpdateAccountProperty" errorcode="CampaignServiceCannotUpdateAccountProperty" code="3610" message="The account property cannot be changed."/>

      <!--Ad Extensions (range = 3800 to 3999)-->
      <error name="InvalidAdExtensionStatus" errorcode="CampaignServiceInvalidAdExtensionStatus" code="3800" message="The specified status value is not allowed for the operation being performed."/>
      <error name="InvalidAdExtensionType" errorcode="CampaignServiceInvalidAdExtensionType" code="3801" message="The type of ad extension is not valid."/>
      <error name="AdExtensionSiteLinkArrayIsNullOrEmpty" errorcode="CampaignServiceAdExtensionSiteLinkArrayIsNullOrEmpty" code="3802" message="The list of site links cannot be null or empty."/>
      <error name="AdExtensionSiteLinkIsNull" errorcode="CampaignServiceAdExtensionSiteLinkIsNull" code="3803" message="The list of site links cannot contain a null site link. The Details element contains the index of the site link."/>
      <error name="SiteLinkDestinationUrlNullOrEmpty" errorcode="CampaignServiceSiteLinkDestinationUrlNullOrEmpty" code="3804" message="The site link's destination URL cannot be null or empty. The Details element contains the index of the site link."/>
      <error name="SiteLinkDisplayTextNullOrEmpty" errorcode="CampaignServiceSiteLinkDisplayTextNullOrEmpty" code="3805" message="The site link's display text cannot be null or empty. The Details element contains the index of the site link."/>
      <error name="SiteLinkDestinationUrlTooLong" errorcode="CampaignServiceSiteLinkDestinationUrlTooLong" code="3806" message="The site link's destination URL is too long. The Details element contains the index of the site link."/>
      <error name="SiteLinkDisplayTextTooLong" errorcode="CampaignServiceSiteLinkDisplayTextTooLong" code="3807" message="The site link's display text is too long. The Details element contains the index of the site link."/>
      <error name="TooManyAdExtensionsPerAccount" errorcode="CampaignServiceTooManyAdExtensionsPerAccount" code="3808" message="Adding the ad extensions to the account will exceed the maximum number of ad extensions that you can add to the account."/>
      <error name="TooManySiteLinks" errorcode="CampaignServiceTooManySiteLinks" code="3809" message="The ad extension contains too many site links."/>
      <error name="AdExtensionIdToCampaignIdAssociationArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdExtensionIdToCampaignIdAssociationArrayShouldNotBeNullOrEmpty" code="3810" message="The list of ad extension to campaign associations cannot be null or empty."/>
      <error name="AdExtensionIdToCampaignIdAssociationArrayLimitExceeded" errorcode="CampaignServiceAdExtensionIdToCampaignIdAssociationArrayLimitExceeded" code="3811" message="The list of ad extension to campaign associations is too long."/>
      <error name="InvalidAdExtensionId" errorcode="CampaignServiceInvalidAdExtensionId" code="3812" message="The specified ad extension ID is not valid."/>
      <error name="InvalidAdExtensionIdToCampaignIdAssociation" errorcode="CampaignServiceInvalidAdExtensionIdToCampaignIdAssociation" code="3813" message="The ad extension Id to campaign Id association provided is not valid."/>
      <error name="InvalidAdExtensionTypeFilter" errorcode="CampaignServiceInvalidAdExtensionTypeFilter" code="3814" message="The specified type filter is not valid."/>
      <error name="AdExtensionIdsArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdExtensionIdArrayShouldNotBeNullOrEmpty" code="3815" message="The list of ad extension IDs cannot be null or empty."/>
      <error name="AdExtensionIdsArrayExceedsLimit" errorcode="CampaignServiceAdExtensionIdsArrayExceedsLimit" code="3816" message="The list of ad extension IDs is too long."/>
      <error name="DuplicateInAdExtensionIds" errorcode="CampaignServiceDuplicateInAdExtensionIds" code="3817" message="The list of ad extension IDs contains duplicates."/>
      <error name="CannotAssignMoreThanOneAdExtensionTypeToAnEntity" errorcode="CampaignServiceCannotAssignMoreThanOneAdExtensionTypeToAnEntity" code="3818" message="The entity to which the ad extension is assigned to already has an adextension of the same type. Please select a different ad extension type."/>
      <error name="AdExtensionIdsArrayIsNullOrEmpty" errorcode="CampaignServiceAdExtensionIdsArrayIsNullOrEmpty" code="3819" message="The Ad extension Id array is null or has no elements."/>
      <error name="AdExtensionIdToCampaignIdAssociationNotPassed" errorcode="CampaignServiceAdExtensionIdToCampaignIdAssociationNotPassed" code="3820" message="The campaign Id to Ad extension Id association is null."/>
      <error name="AdExtensionIdToCampaignIdAssociationsNotPassed" errorcode="CampaignServiceAdExtensionIdToCampaignIdAssociationsNotPassed" code="3821" message="The array of campaign Id to Ad extension Id association is null or has no elements."/>
      <error name="SiteLinkAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceSiteLinkAdExtensionPilotNotEnabledForCustomer" code="3822" message="The customer is not part of the SiteLink AdExtension pilot program."/>
      <error name="SiteLinkDestinationUrlInvalid" errorcode="CampaignServiceSiteLinkDestinationUrlInvalid" code="3823" message="The site link's destination URL has invalid characters. The Details field contains the index of the site link element which has the error."/>
      <error name="TooManyCampaignIds" errorcode="CampaignServiceTooManyCampaignIds" code="3824" message="The Campaign Id array has too many elements."/>
      <error name="DuplicateInAdExtensionIdToCampaignIdAssociations" errorcode="CampaignServiceDuplicateInAdExtensionIdToCampaignIdAssociations" code="3825" message="The list of ad extension to campaign associations contains duplicates."/>
      <error name="AdExtensionVersionCannotBeSet" errorcode="CampaignServiceAdExtensionVersionCannotBeSet" code="3826" message="Version is read-only and must be null."/>
      <error name="DuplicateSiteLinksInAdExtension" errorcode="CampaignServiceDuplicateSiteLinksInAdExtension" code="3827" message="The list of site links contains duplicates."/>
      <error name="AdExtensionAddressIsNull" errorcode="CampaignServiceAdExtensionAddressIsNull" code="3828" message="The address cannot be null."/>
      <error name="AdExtensionStreetAddressNullOrEmpty" errorcode="CampaignServiceAdExtensionStreetAddressNullOrEmpty" code="3829" message="The street address cannot be null or empty."/>
      <error name="AdExtensionStreetAddressTooLong" errorcode="CampaignServiceAdExtensionStreetAddressTooLong" code="3830" message="The street address is too long."/>
      <error name="AdExtensionStreetAddressInvalid" errorcode="CampaignServiceAdExtensionStreetAddressInvalid" code="3831" message="The street address contains invalid characters."/>
      <error name="AdExtensionStreetAddress2TooLong" errorcode="CampaignServiceAdExtensionStreetAddress2TooLong" code="3832" message="The street address2 is too long."/>
      <error name="AdExtensionStreetAddress2Invalid" errorcode="CampaignServiceAdExtensionStreetAddress2Invalid" code="3833" message="The street address2 contains invalid characters."/>
      <error name="AdExtensionCityNameNullOrEmpty" errorcode="CampaignServiceAdExtensionCityNameNullOrEmpty" code="3834" message="The city name cannot be null or empty."/>
      <error name="AdExtensionCityNameTooLong" errorcode="CampaignServiceAdExtensionCityNameTooLong" code="3835" message="The city name is too long."/>
      <error name="AdExtensionCityNameInvalid" errorcode="CampaignServiceAdExtensionCityNameInvalid" code="3836" message="The city name contains invalid characters."/>
      <error name="AdExtensionProvinceNameTooLong" errorcode="CampaignServiceAdExtensionProvinceNameTooLong" code="3837" message="The province name is too long."/>
      <error name="AdExtensionProvinceNameInvalid" errorcode="CampaignServiceAdExtensionProvinceNameInvalid" code="3838" message="The province name contains invalid characters."/>
      <error name="AdExtensionPostalCodeNullOrEmpty" errorcode="CampaignServiceAdExtensionPostalCodeNullOrEmpty" code="3839" message="The postal code cannot be null or empty."/>
      <error name="AdExtensionPostalCodeTooLong" errorcode="CampaignServiceAdExtensionPostalCodeTooLong" code="3840" message="The postal code is too long."/>
      <error name="AdExtensionPostalCodeInvalid" errorcode="CampaignServiceAdExtensionPostalCodeInvalid" code="3841" message="The postal code contains invalid characters."/>
      <error name="AdExtensionCountryCodeNullOrEmpty" errorcode="CampaignServiceAdExtensionCountryCodeNullOrEmpty" code="3842" message="The country/region code cannot be null or empty."/>
      <error name="AdExtensionCountryCodeWrongLength" errorcode="CampaignServiceAdExtensionCountryCodeWrongLength" code="3843" message="The country/region code has wrong length."/>
      <error name="AdExtensionCountryCodeInvalid" errorcode="CampaignServiceAdExtensionCountryCodeInvalid" code="3844" message="The country/region code is not valid."/>
      <error name="AdExtensionGeoPointIsNotNull" errorcode="CampaignServiceAdExtensionGeoPointIsNotNull" code="3845" message="The GeoPoint element must be null."/>
      <error name="AdExtensionGeoCodeStatusIsNotNull" errorcode="CampaignServiceAdExtensionGeoCodeStatusIsNotNull" code="3846" message="The GeoCodeStatus element must be null."/>
      <error name="AdExtensionCompanyNameNullOrEmpty" errorcode="CampaignServiceAdExtensionCompanyNameNullOrEmpty" code="3847" message="The company name cannot be null or empty."/>
      <error name="AdExtensionCompanyNameTooLong" errorcode="CampaignServiceAdExtensionCompanyNameTooLong" code="3848" message="The company name is too long."/>
      <error name="AdExtensionCompanyNameInvalid" errorcode="CampaignServiceAdExtensionCompanyNameInvalid" code="3849" message="The company name contains invalid characters."/>
      <error name="AdExtensionPhoneNumberTooLong" errorcode="CampaignServiceAdExtensionPhoneNumberTooLong" code="3850" message="The phone number is too long."/>
      <error name="AdExtensionPhoneNumberInvalid" errorcode="CampaignServiceAdExtensionPhoneNumberInvalid" code="3851" message="The phone number is invalid."/>
      <error name="AdExtensionIconMediaIdInvalid" errorcode="CampaignServiceAdExtensionIconMediaIdInvalid" code="3852" message="The ad extension icon media ID is invalid."/>
      <error name="AdExtensionIconTooLarge" errorcode="CampaignServiceAdExtensionIconTooLarge" code="3853" message="The ad extension icon is too large."/>
      <error name="AdExtensionImageMediaIdInvalid" errorcode="CampaignServiceAdExtensionImageMediaIdInvalid" code="3854" message="The ad extension image media ID is invalid."/>
      <error name="AdExtensionImageTooLarge" errorcode="CampaignServiceAdExtensionImageTooLarge" code="3855" message="The ad extension image is too large."/>
      <error name="LocationAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceLocationAdExtensionPilotNotEnabledForCustomer" code="3856" message="The customer is not a member of the Location Ad Extension v2 pilot program."/>
      <error name="CallAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceCallAdExtensionPilotNotEnabledForCustomer" code="3857" message="The customer is not a member of the Call Ad Extension v2 pilot program."/>
      <error name="AdExtensionProvinceCodeTooLong" errorcode="CampaignServiceAdExtensionProvinceCodeTooLong" code="3858" message="The province code is too long."/>
      <error name="AdExtensionProvinceCodeInvalid" errorcode="CampaignServiceAdExtensionProvinceCodeInvalid" code="3859" message="The province code contains invalid characters."/>
      <error name="AdExtensionProvinceCodeRequiredIfProvinceNameEmpty" errorcode="CampaignServiceAdExtensionProvinceCodeRequiredIfProvinceNameEmpty" code="3860" message="The province code is required if the province name is empty (for the specified country/region)."/>
      <error name="AdExtensionPhoneNumberNullOrEmpty" errorcode="CampaignServiceAdExtensionPhoneNumberNullOrEmpty" code="3861" message="The phone number cannot be null or empty."/>
      <error name="LocationAdExtensionsEntityLimitExceeded" errorcode="CampaignServiceLocationAdExtensionsEntityLimitExceeded" code="3862" message="The number of location extensions in the ad extensions list, exceeds the maximum allowed." />
      <error name="ProductListingAdPilotNotEnabledForCustomer" errorcode="CampaignServiceProductListingAdPilotNotEnabledForCustomer" code="3863" message="The customer is not part of the Product Listing Ad program."/>
      <error name="ProductAdExtensionTooManyProductConditionCollections" errorcode="CampaignServiceProductAdExtensionTooManyProductConditionCollections" code="3864" message="The list of product condition collections is too long."/>
      <error name="ProductAdExtensionTooManyConditions" errorcode="CampaignServiceProductAdExtensionTooManyConditions" code="3865" message="The list of conditions in the product condition collection is too long."/>
      <error name="ProductAdExtensionInvalidStoreId" errorcode="CampaignServiceProductAdExtensionInvalidStoreId" code="3866" message="The store ID is not valid."/>
      <error name="ProductAdExtensionProductConditionsArrayIsNullOrEmpty" errorcode="CampaignServiceProductAdExtensionProductConditionsArrayIsNullOrEmpty" code="3867" message="The list of conditions in the production condition collection cannot be null or empty."/>
      <error name="ProductAdExtensionProductConditionIsNull" errorcode="CampaignServiceProductAdExtensionProductConditionIsNull" code="3868" message="The product condition cannot be null."/>
      <error name="ProductAdExtensionOperandIsInvalid" errorcode="CampaignServiceProductAdExtensionOperandIsInvalid" code="3869" message="The product condition's operand is not valid."/>
      <error name="ProductAdExtensionAttributeIsInvalid" errorcode="CampaignServiceProductAdExtensionAttributeIsInvalid" code="3870" message="The product condition's attribute is not valid."/>
      <error name="ProductConditionAttributeTooLong" errorcode="CampaignServiceProductConditionAttributeTooLong" code="3871" message="The product condition's attribute is too long."/>
      <error name="ProductConditionDuplicateOperand" errorcode="CampaignServiceProductConditionDuplicateOperand" code="3872" message="The product condition's operand is duplicate."/>
      <error name="ProductAdExtensionStoreNameCannotBeSet" errorcode="CampaignServiceProductAdExtensionStoreNameCannotBeSet" code="3873" message="Store Name is read-only and cannot be set."/>
      <error name="AdExtensionTypeMismatch" errorcode="CampaignServiceAdExtensionTypeMismatch" code="3874" message="Existing ad extension has different type that one passed for the same ID."/>
      <error name="CallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled" errorcode="CampaignServiceCallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled" code="3875" message="RequireTollFreeTrackingNumber must be null when call tracking is not enabled"/>
      <error name="CallTrackingNotEnabledForCustomer " errorcode="CampaignServiceCallTrackingNotEnabledForCustomer" code="3876" message="The customer is not a member of the Call Tracking pilot program."/>
      <error name="CallAdExtensionCallTrackingNotSupportedForCountry" errorcode="CampaignServiceCallAdExtensionCallTrackingNotSupportedForCountry" code="3877" message="Call tracking is not supported for selected country/region."/>
      <error name="AdExtensionGeoPointInvalid" errorcode="CampaignServiceAdExtensionGeoPointInvalid" code="3878" message="The geo point coordinates are invalid."/>
      <error name="ProductAdExtensionNameTooLong" errorcode="CampaignServiceProductAdExtensionNameTooLong" code="3879" message="The name of Product Extension is too long."/>
      <error name="ProductAdExtensionNameInvalid" errorcode="CampaignServiceProductAdExtensionNameInvalid" code="3880" message="The name of Product Extension is invalid."/>
      <error name="DuplicateProductTarget" errorcode="CampaignServiceDuplicateProductTarget" code="3881" message="The list of product targets contains duplicates."/>
      <error name="ProductAdExtensionProductConditionCollectionIsNull" errorcode="CampaignServiceProductAdExtensionProductConditionCollectionIsNull" code="3882" message="The product condition collection cannot be null."/>
      <error name="InvalidAssociationType" errorcode="CampaignServiceInvalidAssociationType" code="3883" message="The specified association type is not valid."/>
      <error name="AdExtensionIdToAdGroupIdAssociationArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdExtensionIdToAdGroupIdAssociationArrayShouldNotBeNullOrEmpty" code="3884" message="The list of ad extension to ad group associations cannot be null or empty."/>
      <error name="AdExtensionIdToAdGroupIdAssociationArrayLimitExceeded" errorcode="CampaignServiceAdExtensionIdToAdGroupIdAssociationArrayLimitExceeded" code="3885" message="The list of ad extension to ad group associations is too long."/>
      <error name="InvalidAdExtensionIdToAdGroupIdAssociation" errorcode="CampaignServiceInvalidAdExtensionIdToAdGroupIdAssociation" code="3886" message="The ad extension Id to ad group Id association provided is not valid."/>
      <error name="DuplicateInAdExtensionIdToAdGroupIdAssociations" errorcode="CampaignServiceDuplicateInAdExtensionIdToAdGroupIdAssociations" code="3887" message="The list of ad extension to ad group associations contains duplicates."/>
      <error name="AdExtensionTypeNotAllowedForAdGroupAssociation" errorcode="CampaignServiceAdExtensionTypeNotAllowedForAdGroupAssociation" code="3888" message="Ad extension type not allowed for ad group association."/>
      <error name="ProductAdExtensionDuplicateProductFilter" errorcode="CampaignServiceProductAdExtensionDuplicateProductFilter" code="3889" message="Duplicate product filter provided."/>
      <error name="ProductAdExtensionDuplicateProductCondition" errorcode="CampaignServiceProductAdExtensionDuplicateProductCondition" code="3890" message="Duplicate product condition provided."/>
      <error name="ProductAdExtensionCannotHaveBothAllAndSpecificProducts" errorcode="CampaignServiceProductAdExtensionCannotHaveBothAllAndSpecificProducts" code="3891" message="Product ad extension cannot have all and specific products specified."/>
      <error name="MultipleProductAdExtensionCannotBeAssignedToSingleEntity" errorcode="CampaignServiceMultipleProductAdExtensionCannotBeAssignedToSingleEntity" code="3892" message="Multiple product ad extensions cannot be assigned to a single entity."/>
      <error name="ExtensionProductSelectionIsNull" errorcode="CampaignServiceExtensionProductSelectionIsNull" code="3893" message="Product Selection is null."/>
      <error name="ProductTargetDestinationUrlTooLong" errorcode="CampaignServiceProductTargetDestinationUrlTooLong" code="3894" message="Product Target destination URL is too long."/>
      <error name="ProductValueTooLong" errorcode="CampaignServiceExtensionProductValueTooLong" code="3895" message="Product Value is too long."/>
      <error name="InvalidProductTargetParam1" errorcode="CampaignServiceInvalidProductTargetParam1" code="3896" message="Param1 in product target is invalid."/>
      <error name="InvalidProductTargetParam2" errorcode="CampaignServiceInvalidProductTargetParam2" code="3897" message="Param2 in product target is invalid."/>
      <error name="InvalidProductTargetParam3" errorcode="CampaignServiceInvalidProductTargetParam3" code="3898" message="Param3 in product target is invalid."/>

      <error name="AdExtensionIdToAccountIdAssociationArrayShouldNotBeNullOrEmpty" errorcode="CampaignServiceAdExtensionIdToAccountIdAssociationArrayShouldNotBeNullOrEmpty" code="3899" message="The list of ad extension to account associations cannot be null or empty."/>
      <error name="AdExtensionIdToAccountIdAssociationArrayLimitExceeded" errorcode="CampaignServiceAdExtensionIdToAccountIdAssociationArrayLimitExceeded" code="3900" message="The list of ad extension to account associations is too long."/>
      <error name="InvalidAdExtensionIdToAccountIdAssociation" errorcode="CampaignServiceInvalidAdExtensionIdToAccountIdAssociation" code="3901" message="The ad extension Id to account Id association provided is not valid."/>
      <error name="DuplicateInAdExtensionIdToAccountIdAssociations" errorcode="CampaignServiceDuplicateInAdExtensionIdToAccountIdAssociations" code="3902" message="The list of ad extension to account associations contains duplicates."/>
      <error name="AdExtensionTypeNotAllowedForAccountAssociation" errorcode="CampaignServiceAdExtensionTypeNotAllowedForAccountAssociation" code="3903" message="Ad extension type not allowed for account association."/>
      <error name="DevicePreferenceIncompatibleWithAdExtensionType" errorcode="CampaignServiceDevicePreferenceIncompatibleWithAdExtensionType" code="3904" message="DevicePreference is incompatible with this ad extension type."/>
      <error name="AdExtensionIdToAccountIdAssociationNotPassed" errorcode="CampaignServiceAdExtensionIdToAccountIdAssociationNotPassed" code="3905" message="The account Id to Ad extension Id association is null"/>
      <error name="AdExtensionAccountAssociationEntityIdNotEqualToAccountId" errorcode="CampaignServiceAdExtensionAccountAssociationEntityIdNotEqualToAccountId" code="3906" message="Entity id shoud be same with account id in Ad extension account association"/>
      <error name="AdExtensionAccountAssociationShouldHaveOneEntityId" errorcode="CampaignServiceAdExtensionAccountAssociationShouldHaveOneEntityId" code="3907" message="There should be only one entity id in Ad extension account association"/>

      <!-- Ad Extension Schedule Errors -->
      <error name="AdExtensionSchedulingPilotNotEnabledForCustomer" errorcode="CampaignServiceAdExtensionSchedulingPilotNotEnabledForCustomer" code="3930" message="The customer is not part of the AdExtension Scheduling pilot program."/>
      <error name="AdExtensionScheduleInvalidStartTime" errorcode="CampaignServiceAdExtensionScheduleInvalidStartTime" code="3931" message="Start date cannot be earlier than today."/>
      <error name="AdExtensionScheduleInvalidEndTime" errorcode="CampaignServiceAdExtensionScheduleInvalidEndTime" code="3932" message="The End time cannot be earlier than today"/>
      <error name="InvalidScheduleDayTimeRange" errorcode="CampaignServiceInvalidScheduleDayTimeRange" code="3933" message="The schedule day and time is invalid. For example, the end date cannot be earlier than the start date."/>
      <error name="ScheduleDayTimeRangesDayBatchLimitExceeded" errorcode="CampaignServiceScheduleDayTimeRangesDayBatchLimitExceeded" code="3934" message="Max of 6 time intervals per day."/>
      <error name="ScheduleDayTimeRangesOverlapping" errorcode="CampaignServiceScheduleDayTimeRangesOverlapping" code="3935" message="Ad schedules should be non-overlapping"/>
      <error name="InvalidScheduleDayTimeRangeInterval" errorcode="CampaignServiceInvalidScheduleDayTimeRangeInterval" code="3936" message="interval should be greater than zero"/>
      <error name="AdExtensionScheduleInvalidUseSearcherTimeZone" errorcode="AdExtensionScheduleInvalidUseSearcherTimeZone" code="3937" message="UseSearcherTimeZone should be null when startDate and endDate and DayTimeRanges are null"/>
      <error name="AdExtensionTypeNotSupportAdExtensionLevelSchedule" errorcode="AdExtensionTypeNotSupportAdExtensionLevelSchedule" code="3938" message="This AdExtension type don't support AdExtension Level Schedule"/>
      <error name="AdExtensionTypeNotSupportSchedule" errorcode="AdExtensionTypeNotSupportSchedule" code="3939" message="This AdExtension type don't support AdExtension Schedule"/>
      <error name="ScheduleDaysNotInDateRange" errorcode="ScheduleDaysNotInDateRange" code="3940" message="Schedule days not in date range"/>

      <error name="SiteLinkDescriptionAllOrNoneRequired" errorcode="CampaignServiceSiteLinkDescriptionAllOrNoneRequired" code="3948" message="You must specify both sitelink description elements or do not specify either. The Details element contains the index of the site link."/>
      <error name="EnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceEnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer" code="3949" message="The customer is not part of the Enhanced SiteLink AdExtension pilot program."/>
      <error name="SiteLinkDescription1TooLong" errorcode="CampaignServiceSiteLinkDescription1TooLong" code="3950" message="The site link's Description1 is too long. The Details element contains the index of the site link."/>
      <error name="SiteLinkDescription2TooLong" errorcode="CampaignServiceSiteLinkDescription2TooLong" code="3951" message="The site link's Description2 is too long. The Details element contains the index of the site link."/>
      <error name="SiteLinkDescription1Invalid" errorcode="CampaignServiceSiteLinkDescription1Invalid" code="3952" message="The site link's Description1 contains invalid characters. The Details element contains the index of the site link."/>
      <error name="SiteLinkDescription2Invalid" errorcode="CampaignServiceSiteLinkDescription2Invalid" code="3953" message="The site link's Description2 contains invalid characters. The Details element contains the index of the site link."/>
      <error name="SiteLinkDevicePreferenceInvalid" errorcode="CampaignServiceSiteLinkDevicePreferenceInvalid" code="3954" message="The site link's DevicePreference value is invalid. The Details element contains the index of the site link."/>
      <error name="SiteLinkDisplayTextInvalid" errorcode="CampaignServiceSiteLinkDisplayTextInvalid" code="3955" message="The site link's display text contains invalid characters. The Details element contains the index of the site link."/>

      <!-- Image AdExtension Errors range 3956 to 3969-->
      <error name="ImageAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceImageAdExtensionPilotNotEnabledForCustomer" code="3956" message="The customer is not part of the Image AdExtension pilot program."/>
      <error name="CampaignImageAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceCampaignImageAdExtensionPilotNotEnabledForCustomer" code="3957" message="The customer is not part of the Campaign level Image AdExtension pilot program."/>
      <error name="ImageAdExtensionAlternativeTextNullOrEmpty" errorcode="CampaignServiceImageAdExtensionAlternativeTextNullOrEmpty" code="3960" message="The image ad extension's alternative text cannot be null or empty."/>
      <error name="ImageAdExtensionAlternativeTextInvalid" errorcode="CampaignServiceImageAdExtensionAlternativeTextInvalid" code="3961" message="The image ad extension's alternative text contains invalid characters."/>
      <error name="ImageAdExtensionAlternativeTextTooLong" errorcode="CampaignServiceImageAdExtensionAlternativeTextTooLong" code="3962" message="The image ad extension's alternative text is too long."/>
      <error name="ImageAdExtensionDestinationUrlInvalid" errorcode="CampaignServiceImageAdExtensionDestinationUrlInvalid" code="3963" message="The image ad extension's destination URL contains invalid characters."/>
      <error name="ImageAdExtensionDestinationUrlTooLong" errorcode="CampaignServiceImageAdExtensionDestinationUrlTooLong" code="3964" message="The image ad extension's destination URL is too long."/>
      <error name="ImageAdExtensionImageMediaIdInvalid" errorcode="CampaignServiceImageAdExtensionMediaIdInvalid" code="3966" message="The image ad extension's media id is invalid."/>
      <error name="AdExtensionAssociationsLimitExceededPerEntityType" errorcode="CampaignServiceAdExtensionAssociationsLimitExceededPerEntityType" code="3967" message="The maximum number of campaigns or ad groups are already associated with ad extensions of this type. Please refer to documentation for entity association limits."/>
      <error name="AssociationsLimitExceededPerAdExtensionType" errorcode="CampaignServiceAssociationsLimitExceededPerAdExtensionType" code="3968" message="The Account or campaign or ad group cannot be associated with an additional ad extension of this type. Please refer to documentation for entity and ad extension association limits."/>

      <!-- App Ad Extension Errors -->
      <error name="AppAdExtensionPilotNotEnabledForCustomer" errorcode="CampaignServiceAppAdExtensionPilotNotEnabledForCustomer" code="3969" message="The customer is not part of the App AdExtension pilot program."/>
      <error name="AppAdExtensionAppPlatformNullOrEmpty" errorcode="CampaignServiceAppAdExtensionAppPlatformNullOrEmpty" code="3970" message="The app platform cannot be null or empty. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionInvalidAppPlatform" errorcode="CampaignServiceAppAdExtensionInvalidAppPlatform" code="3971" message="The app platform is invalid. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionAppStoreIdNullOrEmpty" errorcode="CampaignServiceAppAdExtensionAppStoreIdNullOrEmpty" code="3972" message="The app network ad extension's app store ID cannot be null or empty. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionAppStoreIdTooLong" errorcode="CampaignServiceAppAdExtensionAppStoreIdTooLong" code="3973" message="The app ad extension's app store ID is too long. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionAppStoreIdInvalid" errorcode="CampaignServiceAppAdExtensionAppStoreIdInvalid" code="3974" message="The app ad extension's app store ID is invalid. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDisplayTextNullOrEmpty" errorcode="CampaignServiceAppAdExtensionDisplayTextNullOrEmpty" code="3975" message="The app network ad extension's display text cannot be null or empty. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDisplayTextTooLong" errorcode="CampaignServiceAppAdExtensionDisplayTextTooLong" code="3976" message="The app ad extension's display text is too long. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDisplayTextInvalid" errorcode="CampaignServiceAppAdExtensionDisplayTextInvalid" code="3977" message="The app ad extension's display text is invalid. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDestinationUrlNullOrEmpty" errorcode="CampaignServiceAppAdExtensionDestinationUrlNullOrEmpty" code="3978" message="The app network ad extension's destination URL cannot be null or empty. The Details element contains the index of the ad extension."/>
      <error name="AppAdExtensionDestinationUrlTooLong" errorcode="CampaignServiceAppAdExtensionDestinationUrlTooLong" code="3979" message="The app ad extension's URL is too long. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDestinationUrlInvalid" errorcode="CampaignServiceAppAdExtensionDestinationUrlInvalid" code="3980" message="The app or measurement URL is incorrect, or doesn't correspond to the specified app ID or package name. The Details element contains the index of the app ad extension."/>
      <error name="AppAdExtensionDevicePreferenceInvalid" errorcode="CampaignServiceAppAdExtensionDevicePreferenceInvalid" code="3981" message="The app ad extension device preference is invalid. The Details element contains the index of the app ad extension."/>
      <error name="AppInstallTrackingPilotNotEnabledForCustomer" errorcode="CampaignServiceAppInstallTrackingPilotNotEnabledForCustomer" code="3982" message="The customer is not part of the Application Installation Tracking pilot program."/>

      <!-- Image Ad Extension Description Errors -->
      <error name="ImageAdExtensionDescriptionInvalid" errorcode="CampaignServiceImageAdExtensionDescriptionInvalid" code="3990" message="The image ad extension's description is invalid."/>
      <error name="ImageAdExtensionDescriptionTooLong" errorcode="CampaignServiceImageAdExtensionDescriptionTooLong" code="3991" message="The image ad extension's description is too long."/>
      <error name="ImageAdExtensionTooManyImages" errorcode="CampaignServiceImageAdExtensionTooManyImages" code="3992" message="The image ad extension's ImageMediaIds contain too many images."/>
      <error name="ImageAdExtensionImageMediaIdsNullOrEmpty" errorcode="CampaignServiceImageAdExtensionImageMediaIdsNullOrEmpty" code="3993" message="The image ad extension's ImageMediaIds is NULL or empty."/>
      <error name="ImageAdExtensionImageAltTextInvalid" errorcode="CampaignServiceImageAdExtensionImageAltTextInvalid" code="3994" message="The image ad extension's ImageAltText is invalid."/>
      <error name="ImageAdExtensionImageAltTextTooLong" errorcode="CampaignServiceImageAdExtensionImageAltTextTooLong" code="3995" message="The image ad extension's ImageAltText is too long."/>

      <!--Image/Video API (range = 4000 to 4099)-->
      <error name="MediaIdInvalid" errorcode="CampaignServiceMediaIdInvalid" code="4000" message="An image or icon with the specified ID was not found in the account's media library."/>
      <error name="ImageDataInvalid" errorcode="CampaignServiceImageDataInvalid" code="4001" message="The image data is not valid. The string must be base64 and cannot be empty or null."/>
      <error name="ImageTypeEnumInvalid" errorcode="CampaignServiceImageTypeEnumInvalid" code="4002" message="The image type is not valid."/>
      <error name="ImageMimeTypeInvalid" errorcode="CampaignServiceImageMimeTypeInvalid" code="4003" message="The image's MIME type is not supported."/>
      <error name="ImageTooLarge" errorcode="CampaignServiceImageTooLarge" code="4004" message="The width and height of the image is greater than the maximum allowed."/>
      <error name="ImageOverweight" errorcode="CampaignServiceImageOverweight" code="4005" message="The image's binary size (in megabytes) is greater than the maximum allowed."/>
      <error name="AnimatedImageNotAllowed" errorcode="CampaignServiceAnimatedImageNotAllowed" code="4006" message="Animated images are not allowed."/>
      <error name="MediaTypeInvalid" errorcode="CampaignServiceMediaTypeInvalid" code="4007" message="The type of media is not valid."/>
      <error name="MediaIdsArrayIsNullOrEmpty" errorcode="CampaignServiceMediaIdsArrayIsNullOrEmpty" code="4008" message="The list of media IDs cannot be null or empty."/>
      <error name="MediaIdsLimitExceeded" errorcode="CampaignServiceMediaIdsLimitExceeded" code="4009" message="The list of media IDs exceeds the limit."/>
      <error name="DuplicateInMediaIds" errorcode="CampaignServiceDuplicateInMediaIds" code="4010" message="The list of media IDs contains duplicates."/>
      <error name="MediaEntityArrayIsNullOrEmpty" errorcode="CampaignServiceMediaEntityArrayIsNullOrEmpty" code="4011" message="The list of media cannot be null or empty."/>
      <error name="MediaEntityLimitExceeded" errorcode="CampaignServiceMediaEntityLimitExceeded" code="4012" message="The list of media exceeds the limit."/>
      <error name="MediaEntityIsNull" errorcode="CampaignServiceMediaEntityIsNull" code="4013" message="One of the media entities is null."/>
      <error name="MediaEntityDataIsNull" errorcode="CampaignServiceMediaEntityDataIsNull" code="4014" message="Data in one of the media entities is null."/>
      <error name="MediaEntityDataInvalid" errorcode="CampaignServiceMediaDataInvalid" code="4015" message="Data in one or more the media is not a supported format."/>
      <error name="MediaEnabledEntitiesInvalid" errorcode="CampaignServiceMediaEnabledEntitiesFilterInvalid" code="4016" message="The specified media enabled entities filter is invalid."/>
      <error name="MediaTypeNotSupportedByOperation" errorcode="CampaignServiceMediaTypeInvalidForOperation" code="4017" message="The specified media is not supported by this operation."/>
      <error name="MediaEntityDataEncodingInvalid" errorcode="CampaignServiceMediaDataEncodingInvalid" code="4018" message="Data in one of the media entities is not valid format."/>
      <error name="MediaEntityDataWidthInvalid" errorcode="CampaignServiceMediaWidthInvalid" code="4019" message="Data in one or more media has invalid Width."/>
      <error name="MediaEntityDataHeightInvalid" errorcode="CampaignServiceMediaHeightInvalid" code="4020" message="Data in one or more media has invalid Height."/>
      <error name="MediaEntityDataAspectRatioInvalid" errorcode="CampaignServiceMediaDataAspectRatioInvalid" code="4021" message="Data in one or more media has invalid aspect ratio."/>
      <error name="MediaFormatNotSupported" errorcode="CampaignServiceMediaFormatNotSupported" code="4022" message="The media you attempted to add is not represented by a supported data format. Please refer to documentation for the supported media formats."/>
      <error name="MediaNotFoundInAccount" errorcode="CampaignServiceMediaNotFoundInAccount" code="4023" message="The media is not in the media library for the specified account."/>
      <error name="MediaIsAssociated" errorcode="CampaignServiceMediaIsAssociated" code="4024" message="The media you attempted to delete is still associated with one or more entities."/>
      <error name="InvalidImage" errorcode="InvalidImage" code="4025" message="Image is invalid."/>
      <error name="DuplicateImage" errorcode="DuplicateImage" code="4026" message="Image is duplicate."/>
      <error name="RequiredImageMissing" errorcode="RequiredImageMissing" code="4027" message="Required image is missing."/>
      <error name="InvalidImageExtensionLayout" errorcode="InvalidImageExtensionLayout" code="4036" message="Invalid ImageAdExtension Layout."/>
      <error name="ImagesLimitExceededPerAccount" errorcode="ImagesLimitExceededPerAccount" code="4028" message="Limit of Images Saved per Account reached"/>
      <error name="VideoSourceIsNull" errorcode="VideoSourceIsNull" code="4029" message="One of the video source entities is null"/>
      <error name="VideoUrlDataIsNull" errorcode="VideoUrlDataIsNull" code="4030" message="One of the video urls is null"/>
      <error name="VideoDescriptionDataIsNull" errorcode="VideoDescriptionDataIsNull" code="4031" message="One of the video descriptions is null"/>
      <error name="VideoUrlTextTooLong" errorcode="VideoUrlTextTooLong" code="4032" message="One of the video urls is null"/>
      <error name="VideoDescriptionTextTooLong" errorcode="VideoDescriptionTextTooLong" code="4034" message="One of the video descriptions is null"/>
      <error name="VideoSourceLimitExceeded" errorcode="VideoSourceLimitExceeded" code="4035" message="The list of VideoSource exceeds the limit"/>
      <error name="VideoLimitExceededPerAccount" errorcode="VideoLimitExceededPerAccount" code="4037" message="Max videos per account limit is reached."/>
     <error name="VideoScanTypeNotSupported" errorcode="VideoScanTypeNotSupported" code="4038" message="The video scan type is not supported"/>
     <error name="InvalidFrameRate" errorcode="InvalidFrameRate" code="4039" message="The frame rate of the video is not supported"/>
     <error name="OnlySingleTrackSupported" errorcode="OnlySingleTrackSupported" code="4040" message="Only single track is supported in the video"/>
     <error name="VideoAudioDurationMustMatch" errorcode="VideoAudioDurationMustMatch" code="4041" message="The duration of the video and audio must match"/>
     <error name="ChromaSubsamplingNotSupported" errorcode="ChromaSubsamplingNotSupported" code="4042" message="The chroma subsampling in the video is not supported"/>
     <error name="UnsupportedNumberOfAudioChannels" errorcode="UnsupportedNumberOfAudioChannels" code="4043" message="The number of audio channels in the video is not supported"/>
     <error name="VideoProfileNotSupported" errorcode="VideoProfileNotSupported" code="4044" message="The video profile is not supported"/>
     <error name="InvalidAudioBitRate" errorcode="InvalidAudioBitRate" code="4045" message="The audio bit rate in the video is invalid"/>
     <error name="UnsupportedAudioBitDepth" errorcode="UnsupportedAudioBitDepth" code="4046" message="The audio bit depth in the video is not supported"/>
     <error name="UnsupportedAudioSampleRate" errorcode="UnsupportedAudioSampleRate" code="4047" message="The audio sample rate in the video is not supported"/>
     <error name="UnsupportedFrameRateMode" errorcode="UnsupportedFrameRateMode" code="4048" message="The frame rate mode in the video is not supported"/>

        <!-- Ad Group Criterion API (range = 4100 to 4199 -->
      <error name="AdGroupCriterionIdInvalid" errorcode="CampaignServiceAdGroupCriterionIdInvalid" code="4100" message="The ad group criterion ID is not valid." />
      <error name="AdGroupCriterionIdArrayNullOrEmpty" errorcode="CampaignServiceAdGroupCriterionIdArrayNullOrEmpty" code="4101" message="The list of ad group criterion IDs cannot be null or empty." />
      <error name="DuplicateAdGroupCriterionId" errorcode="CampaignServiceDuplicateAdGroupCriterionId" code="4102" message="The list of ad group criterion IDs cannot contain duplicates." />
      <error name="AdGroupCriterionIdListExceedsLimit" errorcode="CampaignServiceAdGroupCriterionIdListExceedsLimit" code="4103" message="The list of ad group criterion IDs is too long." />
      <error name="AdGroupCriterionsNullOrEmpty" errorcode="CampaignServiceAdGroupCriterionsNullOrEmpty" code="4104" message="The list of ad group criterions cannot be null or empty." />
      <error name="AdGroupCriterionsEntityLimitExceeded" errorcode="CampaignServiceAdGroupCriterionsEntityLimitExceeded" code="4105" message="The list of ad group criterions is too long." />
      <error name="AdGroupCriterionIsNull" errorcode="CampaignServiceAdGroupCriterionIsNull" code="4106" message="The ad group criterion cannot be null." />
      <error name="AdGroupCriterionInvalidConditionType" errorcode="CampaignServiceAdGroupCriterionInvalidConditionType" code="4107" message="The condition of Ad Group Criterion is invalid." />
      <error name="AdGroupCriterionInvalidBidType" errorcode="CampaignServiceAdGroupCriterionInvalidBidType" code="4108" message="The ad group criterion's bid type is not valid." />
      <error name="AdGroupCriterionInvalidBidValue" errorcode="CampaignServiceAdGroupCriterionInvalidBidValue" code="4109" message="The ad group criterion's bid value is not valid." />
      <error name="AdGroupCriterionIdShouldBeNullOnAdd" errorcode="CampaignServiceAdGroupCriterionIdShouldBeNullOnAdd" code="4110" message="The ad group criterion's ID is read-only and must be null." />
      <error name="InvalidAdGroupCriterionStatus" errorcode="CampaignServiceInvalidAdGroupCriterionStatus" code="4111" message="The ad group criterion's status is not valid." />
      <error name="InvalidAdGroupCriterionType" errorcode="CampaignServiceInvalidAdGroupCriterionType" code="4112" message="The ad group criterion's type is not valid." />
      <error name="ProductAdGroupCriterionTooManyConditions" errorcode="CampaignServiceProductAdGroupCriterionTooManyConditions" code="4113" message="The list of product conditions is too long." />
      <error name="AdGroupCriterionProductConditionIsNull" errorcode="CampaignServiceAdGroupCriterionProductConditionIsNull" code="4114" message="The product condition cannot be null." />
      <error name="AdGroupCriterionTypeInvalid" errorcode="CampaignServiceAdGroupCriterionTypeInvalid" code="4115" message="The criterion's type is not valid for this operation." />
      <error name="ProductConditionAttributeNullOrEmpty" errorcode="CampaignServiceProductConditionAttributeNullOrEmpty" code="4116" message="The product condition's attribute cannot be null or empty." />
      <error name="AdGroupCriterionIsDeleted" errorcode="CampaignServiceAdGroupCriterionIsDeleted" code="4117" message="The ad group criterion is deleted." />
      <error name="InvalidAdGroupCriterionEditorialStatus" errorcode="CampaignServiceInvalidAdGroupCriterionEditorialStatus" code="4118" message="Setting Editorial Status is not allowed." />
      <error name="AdGroupCriterionDoesNotExist" errorcode="CampaignServiceAdGroupCriterionDoesNotExist" code="4151" message="The ad group criterion does not exist."/>
      <error name="DuplicateAdGroupCriterion" errorcode="CampaignServiceDuplicateAdGroupCriterion" code="4152" message="Duplicate ad group criterions are not allowed."/>
      <error name="AdgroupCriterionIdIsNotNull" errorcode="CampaignServiceAdgroupCriterionIdIsNotNull" code="4153" message="The ad group criterion's Id must be null."/>
      <error name="AdGroupCriterionIdIsNull" errorcode="CampaignServiceAdGroupCriterionIdIsNull" code="4154" message="The ad group criterion's Id cannot be null."/>
      <error name="AdGroupCriterionIdNotMatchCriterionType" errorcode="CampaignServiceAdGroupCriterionIdNotMatchCriterionType" code="4155" message="The ad group criterion's Id don't match criterion type."/>
      <error name="AdGroupCriterionBidMultiplierValueNotSet" errorcode="CampaignServiceAdGroupCriterionBidMultiplierValueNotSet" code="4156" message="The BidMultiplier is not set for AdGroup Criterion."/>
      <error name="AdGroupCriterionCriterionIsNullOrEmpty" errorcode="CampaignServiceAdGroupCriterionCriterionIsNullOrEmpty" code="4157" message="The criterion of ad group criterion cannot be null."/>
      <error name="RelatedCriterionTransactionError" errorcode="CampaignServiceRelatedCriterionTransactionError" code="4158" message="Transaction failed because one or more criterion actions failed in the same transaction."/>
      <error name="AdGroupCriterionTransactionAcrossAdGroups" errorcode="CampaignServiceAdGroupCriterionTransactionAcrossAdGroups" code="4159" message="Criterion actions in the same transaction should be in the same AdGroup."/>
      <error name="AudienceDeliveryChannelNotMatchCampaignType" errorcode="CampaignServiceAudienceDeliveryChannelNotMatchCampaignType" code="4160" message="The audience's delivery channel didn't match campaign type."/>
      <error name="InvalidProductAudienceType" errorcode="InvalidProductAudienceType" code="4161" message="The product audience's type is invalid."/>
      <error name="DuplicateProductAudience" errorcode="DuplicateProductAudience" code="4162" message="Duplicate product audience."/>
      <error name="AdGroupAlreadyHasCampaignAudienceCriterion" errorcode="AdGroupAlreadyHasCampaignAudienceCriterion" code="4163" message="The ad group already has a campaign audience criterion. Please remove first."/>
      <error name="AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet" errorcode="AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet" code="4164" message="The BidMultiplier and CashbackAdjustment are not set for AdGroup Criterion."/>
      <error name="ContentTargetingOnlySupportAudienceCampaign" errorcode="ContentTargetingOnlySupportAudienceCampaign" code="4165" message="Content Targeting Only Support Audience Campaign." />
      <error name="AccountIsNotEligibleForPlacementTargeting" errorcode="AccountIsNotEligibleForPlacementTargeting" code="4166" message="Account Is Not Eligible For Placement Targeting." />
      <error name="InvalidPlacementIdTarget" errorcode="InvalidPlacementIdTarget" code="4167" message="Invalid PlacementId Target." />
      <error name="InvalidPlacementTargetBidAdjustment" errorcode="InvalidPlacementTargetBidAdjustment" code="4168" message="Invalid Placement Target Bid Adjustment." />
      <error name="AccountIsNotEligibleForSubPlacementTargeting" errorcode="AccountIsNotEligibleForSubPlacementTargeting" code="4169" message="Account Is Not Eligible For Sub Placement Targeting." />
       
      <error name="InvalidTopicIdTarget" errorcode="InvalidTopicIdTarget" code="4173" message="The Topic ID target is invalid." />
      <error name="InvalidTopicTargetBidAdjustment" errorcode="InvalidTopicTargetBidAdjustment" code="4174" message="The Topic target bid adjustment is invalid." />
      <error name="AccountIsNotEligibleForTopicTargeting" errorcode="AccountIsNotEligibleForTopicTargeting" code="4175" message="The account is not eligible for Topic targeting." />
       
        <!-- Product Partition -->
      <error name="AdGroupCriterionActionsNullOrEmpty" errorcode="CampaignServiceAdGroupCriterionActionsNullOrEmpty" code="4119" message="The list of ad group criterion actions cannot be null or empty." />
      <error name="AdGroupCriterionActionNull" errorcode="CampaignServiceAdGroupCriterionActionNull" code="4120" message="The ad group criterion action cannot be null." />
      <error name="CampaignIsNotOfTypeShopping" errorcode="CampaignServiceCampaignIsNotOfTypeShopping" code="4121" message="The Campaign must be of type Shopping for this operation." />
      <error name="ProductPartitionTypeInvalid" errorcode="CampaignServiceProductPartitionTypeInvalid" code="4123" message="The product partition type is invalid." />
      <error name="AdGroupCriterionTypeImmutable" errorcode="CampaignServiceAdGroupCriterionTypeImmutable" code="4124" message="The ad group criterion type cannot be updated." />
      <error name="NegativeAdGroupCriterionImmutable" errorcode="CampaignServiceNegativeAdGroupCriterionImmutable" code="4125" message="The negative ad group criterion cannot be updated." />
      <error name="AdGroupProductPartitionLimitExceeded" errorcode="CampaignServiceAdGroupProductPartitionLimitExceeded" code="4126" message="The specified ad group cannot contain additional product partitions." />
      <error name="InvalidProductPartitionHierarchy" errorcode="CampaignServiceInvalidProductPartitionHierarchy" code="4127" message="Adding the product partition would result in an invalid product partition hierarchy." />
      <error name="RemainingProductsNodeMissingInProductPartition" errorcode="CampaignServiceRemainingProductsNodeMissingInProductPartition" code="4128" message="The product partition is missing a product condition that specifies the remaining products." />
      <error name="DuplicateProductConditions" errorcode="CampaignServiceDuplicateProductConditions" code="4129" message="Children of a product partition node cannot contain duplicate product conditions." />
      <error name="ProductPartitionSiblingsMustHaveSameOperand " errorcode="CampaignServiceProductPartitionSiblingsMustHaveSameOperand" code="4130" message="All children of a product partition node must have the same operand." />
      <error name="ProductPartitionTreeHeightLimitExceeeded" errorcode="CampaignServiceProductPartitionTreeHeightLimitExceeeded" code="4131" message="The height of the product partition tree would have exceeded the limit." />
      <error name="ProductPartitionTreeRootAlreadyExists" errorcode="CampaignServiceProductPartitionTreeRootAlreadyExists" code="4132" message="The ad group already has a product partition tree root node." />
      <error name="CannotAddChildrenToProductPartitionUnit" errorcode="CampaignServiceCannotAddChildrenToProductPartitionUnit" code="4133" message="You can only add child nodes to a parent of type subdivision." />
      <error name="ParentAdGroupCriterionIdInvalid" errorcode="CampaignServiceParentAdGroupCriterionIdInvalid" code="4134" message="The requested parent ad group criterion does not exist in the product partition tree." />
      <error name="ProductPartitionCriterionImmutable" errorcode="CampaignServiceProductPartitionCriterionImmutable" code="4135" message="The criterion field of a product partition cannot be updated." />
      <error name="RemainingProductsNodeRequired" errorcode="CampaignServiceRemainingProductsNodeRequired" code="4136" message="You must add a replacement product partition node before it can be deleted from the subdivision." />
      <error name="CriterionActionsAdGroupLimitExceeded" errorcode="CampaignServiceCriterionActionsAdGroupLimitExceeded" code="4137" message="Too many ad groups were specified in the list of ad group criterion actions." />
      <error name="ProductConditionOperandInvalid" errorcode="CampaignServiceProductConditionOperandInvalid" code="4139" message="The product condition's operand is not valid." />
      <error name="ProductConditionAttributeInvalid" errorcode="CampaignServiceProductConditionAttributeInvalid" code="4140" message="The product condition's attribute is not valid." />
      <error name="CriterionTypeNotAllowed" errorcode="CampaignServiceCriterionTypeNotAllowed" code="4141" message="The criterion object passed is not allowed for the operation." />
      <error name="CriterionTypeMismatch" errorcode="CampaignServiceCriterionTypeMismatch" code="4142" message="The criterion type specified does not match the criterion object passed." />
      <error name="DestinationUrlProtocolInvalid" errorcode="CampaignServiceDestinationUrlProtocolInvalid" code="4143" message="The destination URL protocol is invalid." />
      <error name="DestinationUrlInvalid" errorcode="CampaignServiceDestinationUrlInvalid" code="4144" message="The destination URL is invalid." />
      <error name="DestinationUrlTooLong" errorcode="CampaignServiceDestinationUrlTooLong" code="4145" message="The destination URL is too long." />
      <error name="Param1NotSupportedForCriterionType" errorcode="CampaignServiceParam1NotSupportedForCriterionType" code="4146" message="Param1 is not supported for this criterion type." />
      <error name="Param2NotSupportedForCriterionType" errorcode="CampaignServiceParam2NotSupportedForCriterionType" code="4147" message="Param2 is not supported for this criterion type." />
      <error name="Param3NotSupportedForCriterionType" errorcode="CampaignServiceParam3NotSupportedForCriterionType" code="4148" message="Param3 is not supported for this criterion type." />
      <error name="ConcurrentStoreModification" errorcode="CampaignServiceConcurrentStoreModification" code="4149" message="The data that you tried to modify was instead modified by another operation during the attempted processing of this service operation." />
      <error name="RelatedProductPartitionActionError" errorcode="CampaignServiceRelatedProductPartitionActionError" code="4150" message="Product partition action for the same ad group has an error." />
      <error name="ProductConditionOperatorInvalid" errorcode="CampaignServiceProductConditionOperatorInvalid" code="4170" message="The product condition's operator is not valid." />
      <error name="CustomerNotEligibleForEnhancedProductAdsFilter" errorcode="CampaignServiceCustomerNotEligibleForEnhancedProductAdsFilter" code="4171" message="Customer not eligible for enhanced product ads filter." />
      <error name="ProductConditionOperatorNotSupportedForThisCampaignType" errorcode="CampaignServiceProductConditionOperatorNotSupportedForThisCampaignType" code="4172" message="The product condition operator is not supported for this campaign type." />

        <!-- Bulk Upload (range = 4200 to 4299)-->
      <error name="BatchOperationFailedForItems" errorcode="BulkServiceBatchOperationFailedForItems" code="4200" message="Failed due to other items in the batch."/>
      <error name="InvalidRequestId" errorcode="BulkServiceInvalidRequestId" code="4201" message="The specified RequestId is not valid."/>
      <error name="EntityNotFound" errorcode="BulkServiceEntityNotFound" code="4202" message="The entity specified by the row could not be found."/>
      <error name="UnknownTypeForRow" errorcode="BulkServiceUnknownTypeForRow" code="4203" message="The Type column of the row was not recognized and could not be inferred from the contents."/>
      <error name="NoMoreCallsPermittedForTheTimePeriod" errorcode="BulkServiceNoMoreCallsPermittedForTheTimePeriod" code="4204" message="No more bulk upload or download calls will be permitted for this account for the current time period. If you have reached your bulk upload limit, the bulk download operations may still be available, or vice versa."/>
      <error name="CannotRemoveLastGoodAdExtensionItem" errorcode="BulkServiceCannotRemoveLastGoodAdExtensionItem" code="4205" message="Cannot remove the last valid item in an adextension."/>
      <error name="InvalidCustomParametersText" errorcode="CampaignServiceInvalidCustomParametersText" code="4206" message="The text format of Custom Parameters is invalid."/>
      <error name="InvalidFinalUrlsText" errorcode="CampaignServiceInvalidFinalUrlsText" code="4207" message="The text format of Final Url is invalid."/>
      <error name="InvalidMobileFinalUrlsText" errorcode="CampaignServiceInvalidMobileFinalUrlsText" code="4208" message="The text format of Mobile Final Url is invalid."/>

      <!-- Shared Entities and Structured Negative Keywords (range 4301 - 4399)-->
      <error name="SharedEntityNameNullOrEmpty" errorcode="CampaignServiceSharedEntityNameNullOrEmpty" code="4301" message="Shared entity Name field is a required."/>
      <error name="SharedEntityLimitExceeded" errorcode="CampaignServiceSharedEntityLimitExceeded" code="4302" message="Your account already has the maximum number of shared entity lists."/>
      <error name="SharedEntityInvalidType" errorcode="CampaignServiceSharedEntityInvalidType" code="4303" message="The specified shared entity type is invalid or not currently supported."/>
      <error name="SharedEntityAssociationsNullOrEmpty" errorcode="CampaignServiceSharedEntityAssociationsNullOrEmpty" code="4304" message="Shared entity associations are required."/>
      <error name="MultipleSharedEntityTypesNotAllowed" errorcode="CampaignServiceMultipleSharedEntityTypesNotAllowed" code="4306" message="Multiple types of shared entity are not allowed in one call."/>
      <error name="SharedEntityAssociationsBatchLimitExceeded" errorcode="CampaignServiceSharedEntityAssociationsBatchLimitExceeded" code="4307" message="Too many shared entity associations in one batch."/>
      <error name="SharedEntityAssociationsListItemNullOrEmpty" errorcode="CampaignServiceSharedEntityAssociationsListItemNullOrEmpty" code="4308" message="Shared entity associations collection cannot be null or empty"/>
      <error name="SharedEntityAssociationDuplicate" errorcode="CampaignServiceSharedEntityAssociationDuplicate" code="4309" message="Shared entity is a duplicate of a existing association."/>
      <error name="SharedEntityIdsNullOrEmpty" errorcode="CampaignServiceSharedEntityIdsNullOrEmpty" code="4310" message="Shared entity Id field cannot be null or empty."/>
      <error name="SharedEntityNullOrEmpty" errorcode="CampaignServiceSharedEntityNullOrEmpty" code="4311" message="Shared Entity cannot be null or empty"/>
      <error name="DuplicateSharedEntityId" errorcode="CampaignServiceDuplicateSharedEntityId" code="4312" message="Duplicate shared entity Ids are not allowed."/>
      <error name="SharedEntityTypeNullOrEmpty" errorcode="CampaignServiceSharedEntityTypeNullOrEmpty" code="4313" message="Shared entity type is a required field."/>
      <error name="SharedListDeleted" errorcode="CampaignServiceSharedListDeleted" code="4314" message="Deleted shared entity lists cannot be retrieved or modified."/>
      <error name="SharedListsNullOrEmpty" errorcode="CampaignServiceSharedListsNullOrEmpty" code="4315" message="Shared entity lists are required."/>
      <error name="SharedListNullOrEmpty" errorcode="CampaignServiceSharedListNullOrEmpty" code="4316" message="Shared entity list is a required field."/>
      <error name="SharedListIdInvalid" errorcode="CampaignServiceSharedListIdInvalid" code="4317" message="Shared entity list Id is not valid"/>
      <error name="SharedListDuplicate" errorcode="CampaignServiceSharedListDuplicate" code="4318" message="Duplicate shared list identifiers are not allowed."/>
      <error name="SharedListItemsNullOrEmpty" errorcode="CampaignServiceSharedListItemsNullOrEmpty" code="4319" message="Shared entity list items are a required field."/>
      <error name="SharedListItemIdInvalid" errorcode="CampaignServiceListItemIdInvalid" code="4320" message="Shared entity list Id is not a valid Id"/>
      <error name="SharedListItemIdsLimitExceeded" errorcode="CampaignServiceListItemIdsLimitExceeded" code="4321" message="The maximum number of shared entity list identifiers per call has been exceeded."/>
      <error name="SharedEntitiesNullOrEmpty" errorcode="CampaignServiceSharedEntitiesNullOrEmpty" code="4322" message="Shared entities is a required field."/>
      <error name="SharedEntityNameTooLong" errorcode="CampaignServiceSharedEntityNameTooLong" code="4323" message="Shared entity name is too long."/>
      <error name="SharedEntityNameInvalid" errorcode="CampaignServiceSharedEntityNameInvalid" code="4324" message="Shared entity name is invalid"/>
      <error name="SharedListIdNotAllowed" errorcode="CampaignServiceSharedListIdNotAllowed" code="4325" message="Shared entity list Id is not allowed"/>
      <error name="SharedEntityIdInvalid" errorcode="CampaignServiceSharedEntityIdInvalid" code="4326" message="Shared entity id is invalid"/>
      <error name="MaxNegativeKeywordLimitExceededForList" errorcode="CampaignServiceMaxNegativeKeywordLimitExceededForList" code="4328" message="Adding the negative keyword to the list will exceed the maximum number of negative keywords that you can add to the list."/>
      <error name="NegativeKeywordDeleted" errorcode="CampaignServiceNegativeKeywordDeleted" code="4329" message="Cannot modify a deleted negative keyword"/>
      <error name="InvalidNegativeKeywordId" errorcode="CampaignServiceInvalidNegativeKeywordId" code="4330" message="Negative keyword Id is invalid"/>
      <error name="NegativeKeywordListWithActiveAssociationsCannotBeDeleted" errorcode="CampaignServiceNegativeKeywordListWithActiveAssociationsCannotBeDeleted" code="4331" message="A list with an active association cannot be deleted."/>
      <error name="NegativeKeywordTypeInvalid" errorcode="CampaignServiceNegativeKeywordTypeInvalid" code="4332" message="Negative Keyword Type field is invalid"/>
      <error name="NegativeKeywordTextRequired" errorcode="CampaignServiceNegativeKeywordTextRequired" code="4333" message="Negative Keyword text is required"/>
      <error name="DuplicateNegativeKeywordListName" errorcode="CampaignServiceDuplicateNegativeKeywordListName" code="4334" message="Duplicate negative keyword list names are not allowed"/>
      <error name="StructuredNegativeKeywordPilotNotEnabledForCustomer" errorcode="CampaignServiceStructuredNegativeKeywordPilotNotEnabledForCustomer" code="4336" message="The Structured Negative Keyword Pilot is not enabled for this customer."/>
      <error name="NegativeKeywordTooManyParentTypes" errorcode="CampaignServiceNegativeKeywordTooManyParentTypes" code="4337" message="You cannot mix the parent types for the Negative Keywords in this calls."/>
      <error name="NegativeKeywordDuplicateFound" errorcode="CampaignServiceNegativeKeywordDuplicateFound" code="4338" message="Duplicate negative keyword already exists in this list."/>
      <error name="NegativeKeywordNotFound" errorcode="CampaignServiceNegativeKeywordNotFound" code="4339" message="No negative keywords were found."/>
      <error name="NegativeKeywordInvalidMatchType" errorcode="CampaignServiceNegativeKeywordInvalidMatchType" code="4340" message="The match type for this keyword was invalid."/>
      <error name="NegativeKeywordInvalidParentType" errorcode="CampaignServiceNegativeKeywordInvalidParentType" code="4341" message="The parent type for this negative keyword is invalid."/>
      <error name="NegativeKeywordEntitiesNotPassed" errorcode="CampaignServiceNegativeKeywordEntitiesNullOrEmpty" code="4342" message="Negative Keywords entities are required for this call"/>
      <error name="NegativeKeywordEntityNull" errorcode="CampaignServiceNegativeKeywordEntityNull" code="4343" message="Negative Keyword cannot be null"/>
      <error name="NegativeKeywordEntityTypesMismatch" errorcode="CampaignServiceMultipleEntityTypesNotAllowed" code="4344" message="Negative Keyword types cannot be added to multiple entity types in one call."/>
      <error name="NegativeKeywordDuplicate" errorcode="CampaignServiceDuplicateNegativeKeyword" code="4345" message="Duplicate negative keyword are not allowed."/>
      <error name="SharedEntityAssociationDoesNotExist" errorcode="CampaignServiceSharedEntityAssociationDoesNotExist" code="4346" message="Shared entity association does not exisit"/>
      <error name="CampaignIdsNotPassed" errorcode="CampaignServiceCampaignIdsNullOrEmpty" code="4347" message="Campaign Ids passed are null or empty." />
      <error name="AdGroupIdsNotPassed" errorcode="CampaignServiceAdGroupIdsNullOrEmpty" code="4348" message="AdGroup Ids passed are null or empty."/>
      <error name="SharedEntityBatchLimitExceeded" errorcode="CampaignServiceSharedEntityBatchLimitExceeded" code="4349" message="Too many shared entity ids in one batch."/>
      <error name="EntityBatchLimitExceeded" errorcode="CampaignServiceEntityBatchLimitExceeded" code="4350" message="Too many entity ids in one batch."/>
      <error name="NegativeKeywordsAccountLimitExceeded" errorcode="CampaignServiceNegativeKeywordsAccountLimitExceeded" code="4351" message="Negative keyword limit at account level exceeded."/>
      <error name="SharedEntityAssociationsAccountLimitExceeded" errorcode="CampaignServiceSharedEntityAssociationsAccountLimitExceeded" code="4352" message="Shared entity association limit at account level exceeded."/>
      <error name="NotInPilotForManagerAccountSharedWebsiteExclusions" errorcode="CampaignServiceNotInPilotForManagerAccountSharedWebsiteExclusions" code="4353" message="The customer is not in pilot for Manager account website exclusion lists."/>
      <error name="DuplicateSharedListName" errorcode="CampaignServiceDuplicateSharedListName" code="4354" message="Shared list with the same name already exists."/>
      <error name="MaxListItemLimitExceededForList" errorcode="CampaignServiceMaxListItemLimitExceededForList" code="4355" message="Limit for shared list items in list has been reached."/>
      <error name="DuplicateListItemInList" errorcode="CampaignServiceDuplicateListItemInList" code="4356" message="A matching list item already exists."/>
      <error name="InvalidListItemTypeForList" errorcode="CampaignServiceInvalidListItemTypeForList" code="4357" message="The list item type is not valid for the list type."/>
      <error name="SharedEntitiesWithActiveAssociationsCannotBeDeleted" errorcode="CampaignServiceSharedEntitiesWithActiveAssociationsCannotBeDeleted" code="4358" message="All associations must be removed before the shared entity can be deleted."/>
      <error name="SharedListItemNotInList" errorcode="CampaignServiceSharedListItemNotInList" code="4359" message="The list item was not found in the list."/>
      <error name="SharedListItemBatchLimitExceeded" errorcode="CampaignServiceSharedListItemBatchLimitExceeded" code="4360" message="The list item batch limit is exceeded."/>
      <error name="InvalidCampaignSubType" errorcode="InvalidCampaignSubType" code="4361" message="Invalid Campaign SubType for this operation."/>

      <!-- CampaignCriterion (range 4500 - 4599)-->
      <error name="ShoppingCampaignPilotNotEnabledForCustomer" errorcode="ShoppingCampaignPilotNotEnabledForCustomer" code="4500" message="The customer is not part of the shopping campaign pilot program."/>
      <error name="CampaignCriterionsNullOrEmpty" errorcode="CampaignCriterionsNullOrEmpty" code="4501" message="The list of campaign criterion cannot be null or empty."/>
      <error name="CampaignCriterionsLimitExceeded" errorcode="CampaignCriterionsLimitExceeded" code="4502" message="The list of campaign criterion is too long."/>
      <error name="CampaignCriterionTypeInvalid" errorcode="CampaignCriterionTypeInvalid" code="4503" message="The campaign criterion's type is not valid."/>
      <error name="CampaignCriterionIsNull" errorcode="CampaignCriterionIsNull" code="4504" message="The campaign criterion cannot be null."/>
      <error name="CampaignCriterionIdShouldBeNullOnAdd" errorcode="CampaignCriterionIdShouldBeNullOnAdd" code="4505" message="The campaign criterion's ID is read-only and must be null."/>
      <error name="DuplicateCampaignCriterionId" errorcode="DuplicateCampaignCriterionId" code="4506" message=" The list of campaign criterion IDs cannot contain duplicates."/>
      <error name="CampaignCriterionIdInvalid " errorcode="CampaignCriterionIdInvalid" code="4507" message="The campaign criterion ID is not valid."/>
      <error name="CampaignCriterionProductConditionInvalid" errorcode="CampaignCriterionProductConditionInvalid" code="4508" message="The product condition of the campaign criterion is invalid."/>
      <error name="CampaignTypeIsNotShoppingCampaign" errorcode="CampaignTypeIsNotShoppingCampaign" code="4509" message="Campaign has to be of type Shopping for this operation."/>
      <error name="CampaignCriterionCriterionIsNullOrEmpty" errorcode="CampaignCriterionCriterionIsNullOrEmpty" code="4510" message="The Criterion of campaign criterion cannot be null or empty."/>
      <error name="CampaignCriterionTooManyConditions" errorcode="CampaignCriterionTooManyConditions" code="4511" message="The list of product conditions for the campaign criterion is too long."/>
      <error name="CampaignCriterionDuplicateCampaignId" errorcode="CampaignCriterionDuplicateCampaignId" code="4512" message="Duplicate campaign IDs were specified in the list of campaign criterion."/>
      <error name="CampaignCriterionIdArrayNullOrEmpty" errorcode="CampaignCriterionIdArrayNullOrEmpty" code="4513" message="The list of campaign criterion IDs cannot be null or empty."/>
      <error name="CampaignCriterionIdsLimitExceeded" errorcode="CampaignCriterionIdsLimitExceeded" code="4514" message="The list of campaign criterion IDs is too long."/>
      <error name="ProductConditionIsNull" errorcode="ProductConditionIsNull" code="4515" message="The product condition cannot be null."/>
      <error name="CampaignCriterionDuplicateProductConditionOperand" errorcode="CampaignCriterionDuplicateProductConditionOperand" code="4516" message="The product condition's operand is already in use for the campaign criterion."/>
      <error name="CampaignCriterionAlreadyExists" errorcode="CampaignCriterionAlreadyExists" code="4517" message="The campaign already has a campaign criterion. Only one campaign criterion is allowed per campaign."/>
      <error name="CampaignCriterionActionsNullOrEmpty" errorcode="CampaignServiceCampaignCriterionActionsNullOrEmpty" code="4518" message="The list of CampaignCriterionActions is NullOrEmpty. Please see the ReasonCode element of this error object for details."/>
      <error name="CampaignCriterionBidMultiplierValueNotSet" errorcode="CampaignServiceCampaignCriterionBidMultiplierValueNotSet" code="4519" message="The BidMultiplier is not set for Campaign Criterion."/>
      <error name="CampaignCriterionStatusInvalid" errorcode="CampaignCriterionStatusInvalid" code="4520" message="The campaign criterion's status is not valid." />
      <error name="DuplicateCampaignCriterion" errorcode="DuplicateCampaignCriterion" code="4521" message="Duplicate campaign criterions are not allowed."/>
      <error name="CampaignAlreadyHasAdGroupAudienceCriterion" errorcode="CampaignAlreadyHasAdGroupAudienceCriterion" code="4522" message="The campaign already has an ad group audience criterion. Please remove first"/>
      <error name="AllNullCampaignCriterionTypesNotAllowedOnCreate" errorcode="AllNullCampaignCriterionTypesNotAllowedOnCreate" code="4523" message="The campaign criterion's type cannot be all null or empty on creation"/>
      <error name="CampaignCriterionDoesNotExist" errorcode="CampaignServiceCampaignCriterionCampaignCriterionDoesNotExist" code="4526" message="The campaign criterion does not exist."/>
      <error name="CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet" errorcode="CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet" code="4527" message="The BidMultiplier and CashbackAdjustment are not set for Campaign Criterion."/>
      <!-- Common Validation Errors (range = 4400 to 4499)-->
      <error name="AdExtensionPilotNotEnabledForCustomer" errorcode="AdExtensionPilotNotEnabledForCustomer" code="4400" message="Customer is not in pilot for this adextension type."/>
      <error name="AdExtensionCampaignAssociationPilotNotEnabledForCustomer" errorcode="AdExtensionCampaignAssociationPilotNotEnabledForCustomer" code="4401" message="Customer is not in pilot for this adextension type campaign association."/>
      <error name="AdExtensionAdGroupAssociationPilotNotEnabledForCustomer" errorcode="AdExtensionAdGroupAssociationPilotNotEnabledForCustomer" code="4402" message="Customer is not in pilot for this adextension type adgroup association."/>
      <error name="ValueTooShort" errorcode="ValueTooShort" code="4403" message="Value is too short."/>
      <error name="ValueTooLong" errorcode="ValueTooLong" code="4404" message="Value is too long."/>
      <error name="ValueOutOfRange" errorcode="ValueOutOfRange" code="4405" message="Value is out of valid range."/>
      <error name="ValueIsMissing" errorcode="ValueIsMissing" code="4406" message="Required value is missing."/>
      <error name="InvalidValue" errorcode="InvalidValue" code="4407" message="Value is invalid."/>
      <error name="CustomerNotEnableForNativeAdsPilot" errorcode="CustomerNotEnableForNativeAdsPilot" code="4408" message="Customer Not Enable For NativeAds Pilot."/>
      <error name="InvalidAudienceAdsBidAdjustmentValue" errorcode="InvalidAudienceAdsBidAdjustmentValue" code="4409" message="AudienceAdsBidAdjustment value or flag is invalid."/>
      <error name="InvalidHeader" errorcode="InvalidHeader" code="4410" message="Header is invalid."/>
      <error name="InvalidStructuredSnippetCharacter" errorcode="InvalidStructuredSnippetCharacter" code="4411" message="Structured Snippet values do not support commas or semicolons."/>
      <error name="TooManyStructuredSnippetText" errorcode="TooManyStructuredSnippetText" code="4412" message="Too many structured snippet texts."/>
      <error name="TooFewStructuredSnippetText" errorcode="TooFewStructuredSnippetText" code="4413" message="Too few structured snippet text."/>
      <error name="AdExtensionAccountAssociationPilotNotEnabledForCustomer" errorcode="AdExtensionAccountAssociationPilotNotEnabledForCustomer" code="4414" message="Customer is not in pilot for this adextension type account association."/>
      <error name="ValueImmutable" errorcode="ValueImmutable" code="4415" message="Value cannot be updated."/>
      <error name="AssetFieldMinimumNotMet" errorcode="AssetFieldMinimumNotMet" code="4416" message="Minimum number of assets for field not met."/>
      <error name="AssetFieldLimitExceeded" errorcode="AssetFieldLimitExceeded" code="4417" message="Excceded maximum number of assets allowed in field."/>
      <error name="InvalidState" errorcode="InvalidState" code="4418" message="Resulting state would be invalid."/>
      <error name="DuplicatedTextField" errorcode="DuplicatedTextField" code="4419" message="Duplicate values in MMA text fields are not allowed."/>

      <!-- Upgraded Urls (range = 4600 to 4699) -->
      <error name="InvalidUrlScheme" errorcode="InvalidUrlScheme" code="4600" message="Url scheme is invalid."/>
      <error name="TrackingTemplateTooLong" errorcode="TrackingTemplateTooLong" code="4601" message="Tracking url template is too long."/>
      <error name="MissingLandingPageUrlTag" errorcode="MissingLandingPageUrlTag" code="4602" message="Landing page url tag is missing."/>
      <error name="CountExceedsLimit" errorcode="CountExceedsLimit" code="4603" message="The list exceeds maximun allowed limit."/>
      <error name="InvalidCharactersInKey" errorcode="InvalidCharactersInKey" code="4604" message="Key contains invalid characters."/>
      <error name="InvalidCharactersInValue" errorcode="InvalidCharactersInValue" code="4605" message="Value contains invalid characters."/>
      <error name="InvalidTag" errorcode="InvalidTag" code="4606" message="Value contains invalid tag."/>
      <error name="InvalidOsType" errorcode="InvalidOsType" code="4607" message="Os type is invalid."/>
      <error name="KeyTooLong" errorcode="KeyTooLong" code="4608" message="Key is too long."/>
      <error name="UrlTooLong" errorcode="UrlTooLong" code="4609" message="Url is too long."/>
      <error name="DuplicateOsTypeForAppUrl" errorcode="DuplicateOsTypeForAppUrl" code="4610" message="Duplicate os type for app url."/>
      <error name="KeyNullOrEmpty" errorcode="KeyNullOrEmpty" code="4611" message="Key is null or empty."/>
      <error name="ValueNullOrEmpty" errorcode="ValueNullOrEmpty" code="4612" message="Value is null or empty."/>
      <error name="ParameterIsNull" errorcode="ParameterIsNull" code="4613" message="Parameter is null."/>
      <error name="UpgradedUrlsPilotNotEnabledForCustomer" errorcode="UpgradedUrlsPilotNotEnabledForCustomer" code="4614" message="UpgradedUrls pilot is not enabled for customer."/>
      <error name="FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter" errorcode="FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter" code="4615" message="Final Url is required when using tracking url template or custom parameter."/>
      <error name="FinalUrlRequiredWhenUsingMobileFinalUrl" errorcode="FinalUrlRequiredWhenUsingMobileFinalUrl" code="4616" message="Final Url is required when using mobile final url."/>
      <error name="MobileFinalUrlNotAllowedWithMobileDevicePreference" errorcode="MobileFinalUrlNotAllowedWithMobileDevicePreference" code="4617" message="Mobile final url is not allowed with mobile device preference."/>
      <error name="BothDestinationUrlAndFinalUrlNotAllowed" errorcode="BothDestinationUrlAndFinalUrlNotAllowed" code="4618" message="Having both destination url and final url is not allowed."/>
      <error name="BothDestinationUrlAndUrlCustomParameterNotAllowed" errorcode="BothDestinationUrlAndUrlCustomParameterNotAllowed" code="4619" message="Having both destination url and url custom parameter is not allowed."/>
      <error name="DuplicatesInField" errorcode="DuplicatesInField" code="4620" message="Duplicate values in field are not allowed."/>
      <error name="BothDestinationUrlAndTrackingTemplateNotAllowed" errorcode="BothDestinationUrlAndTrackingTemplateNotAllowed" code="4621" message="Having both destination url and tracking template is not allowed."/>
      <error name="CampaignServiceFinalUrlAndMobileUrlNotAllowedForProductPartition" errorcode="CampaignServiceFinalUrlAndMobileUrlNotAllowedForProductPartition" code="4622" message="Final Urls and Mobile Final Urls are not allowed in a product partition."/>
      <error name="CampaignServiceTemplateAndParametersNotAllowedForProductPartitionSubdivision" errorcode="CampaignServiceTemplateAndParametersNotAllowedForProductPartitionSubdivision" code="4623" message="Tracking template and custom parameters cannot be added to a product partition subdivision."/>
      <error name="UpgradedUrlsPilotNotEnabledCustomParametersNotImported" errorcode="UpgradedUrlsPilotNotEnabledCustomParametersNotImported" code="4624" message="The customer is not in the Upgraded URLs pilot. The tracking template from this entity is imported as a destination url instead of being imported as a tracking template. If you tried to import any custom parameters, they were not imported."/>
      <error name="MissingRequiredParameterOrString" errorcode="MissingRequiredParameterOrString" code="4632" message="Your tracking template doesn't match the expected format, missing required parameters or string."/>
      <error name="InvalidQueryDelimiterPlacement" errorcode="InvalidQueryDelimiterPlacement" code="4633" message="Your tracking template doesn't match the expected format."/>
      <error name="InvalidAppId " errorcode="InvalidAppId" code="4634" message="Your tracking template contains an invalid App ID."/>
      <!-- FinalUrlSuffix -->
      <error name="FinalUrlRequiredWhenUsingFinalUrlSuffix" errorcode="FinalUrlRequiredWhenUsingFinalUrlSuffix" code="4625" message="Final Url is required when using final url suffix."/>
      <error name="FinalUrlSuffixInvalidCharacters" errorcode="FinalUrlSuffixInvalidCharacters" code="4626" message="Final Url Suffix contains invalid characters."/>
      <error name="FinalUrlSuffixInvalidParameters" errorcode="FinalUrlSuffixInvalidParameters" code="4627" message="Final Url Suffix contains invalid parameters."/>
      <error name="BothDestinationUrlAndFinalUrlSuffixNotAllowed" errorcode="BothDestinationUrlAndFinalUrlSuffixNotAllowed" code="4628" message="Having both destination url and final url suffix is not allowed."/>
      <error name="FinalUrlSuffixTooLong" errorcode="FinalUrlSuffixTooLong" code="4629" message="The Final Url Suffix is too long"/>
      <error name="CustomerNotEnabledForFinalUrlSuffixPhase1Pilot" errorcode="CustomerNotEnabledForFinalUrlSuffixPhase1Pilot" code="4630" message="Final Url Suffix phase 1 pilot is not enabled for customer"/>
      <error name="CustomerNotEnabledForFinalUrlSuffixPhase2Pilot" errorcode="CustomerNotEnabledForFinalUrlSuffixPhase2Pilot" code="4631" message="Final Url Suffix phase 2 pilot is not enabled for customer"/>

      <!--AutoBidding (range = 4700 to 4799 )-->
      <error name="BiddingSchemeNotEnabledForCustomer" errorcode="BiddingSchemeNotEnabledForCustomer" code="4700" message="The customer is not enabled for this bidding scheme."/>
      <error name="UnsupportedBiddingScheme" errorcode="UnsupportedBiddingScheme" code="4701" message="The bidding scheme is not supported."/>
      <error name="TargetCpaIsRequired" errorcode="TargetCpaIsRequired" code="4702" message="TargetCpa is required for this bidding scheme."/>
      <error name="MaxCpcExceedsMonthlyBudget" errorcode="MaxCpcExceedsMonthlyBudget" code="4703" message="MaxCpc exceeds campaign monthly budget."/>
      <error name="InheritFromParentBiddingSchemeNotAllowedForCampaign" errorcode="InheritFromParentBiddingSchemeNotAllowedForCampaign" code="4704" message="InheritFromParentBiddingScheme is not allowed to set to campaign and can only be applied in ad group and keyword entities."/>
      <error name="SupportOnlyManualAndInheritFromParentBiddingStrategy" errorcode="SupportOnlyManualAndInheritFromParentBiddingStrategy" code="4705" message="Only Manual and InheritFromParent bid strategy is allowed."/>
      <error name="EnhancedCpcNotAllowedForShoppingCampaign" errorcode="EnhancedCpcNotAllowedForShoppingCampaign" code="4706" message="Enhanced CPC is not allowed for shopping campaign."/>
      <error name="UnsupportedGoogleBidStrategyType" errorcode="UnsupportedGoogleBidStrategyType" code="4707" message="The Bid Strategy Type from Google is unsupported."/>
      <error name="MaxCpcLessThanOrEqualToZero" errorcode="MaxCpcLessThanOrEqualToZero" code="4708" message="MaxCpc Less Than Or Equal To Zero."/>
      <error name="TargetCpaLessThanOrEqualToZero" errorcode="TargetCpaLessThanOrEqualToZero" code="4709" message="TargetCpa Less Than Or Equal To Zero."/>
      <error name="TargetCpaExceedsMonthlyBudget" errorcode="TargetCpaExceedsMonthlyBudget" code="4710" message="TargetCpa Exceeds Monthly Budget."/>
      <error name="UnsupportedBiddingSchemeForMonthlyBudgetType" errorcode="UnsupportedBiddingSchemeForMonthlyBudgetType" code="4711" message="Unsupported BiddingScheme For Campaign with Monthly Budget Type."/>
      <error name="MaxCpcBidAmountsLessThanFloorPrice" errorcode="MaxCpcBidAmountsLessThanFloorPrice" code="4712" message="MaxCpc Bid Amounts Less Than Floor Price."/>
      <error name="MaxCpcBidsAmountsGreaterThanCeilingPrice" errorcode="MaxCpcBidsAmountsGreaterThanCeilingPrice" code="4713" message="MaxCpc Bids Amounts Greater Than Ceiling Price."/>
      <error name="TargetCpaBidAmountsLessThanFloorPrice" errorcode="TargetCpaBidAmountsLessThanFloorPrice" code="4714" message="TargetCpa Bid Amounts Less Than Floor Price."/>
      <error name="TargetCpaBidsAmountsGreaterThanCeilingPrice" errorcode="TargetCpaBidsAmountsGreaterThanCeilingPrice" code="4715" message="TargetCpa Bids Amounts Greater Than Ceiling Price."/>
      <error name="NoEnoughConversionForMaxConversionsBiddingScheme" errorcode="NoEnoughConversionForMaxConversionsBiddingScheme" code="4716" message="No enough conversion for MaxConversions bidding scheme."/>
      <error name="NoEnoughConversionForTargetCpaBiddingScheme" errorcode="NoEnoughConversionForTargetCpaBiddingScheme" code="4717" message="No enough conversion for TargetCPA bidding scheme."/>
      <error name="MaxConversionAndSharedBudgetAreMutuallyExclusive" errorcode="MaxConversionAndSharedBudgetAreMutuallyExclusive" code="4718" message="MaxConversions bidding scheme and Shared Budget feature are mutually exclusive."/>
      <error name="TargetCpaAndSharedBudgetAreMutuallyExclusive" errorcode="TargetCpaAndSharedBudgetAreMutuallyExclusive" code="4719" message="TargetCPA bidding scheme and Shared Budget feature are mutually exclusive."/>
      <error name="MaxConversionsNotEnabledForDynamicSearchAds" errorcode="MaxConversionsNotEnabledForDynamicSearchAds" code="4720" message="MaxConversions bidding scheme is not enabled for dynamic search ads."/>
      <error name="TargetCpaNotEnabledForDynamicSearchAds" errorcode="TargetCpaNotEnabledForDynamicSearchAds" code="4721" message="TargetCPA bidding scheme is not enabled for dynamic search ads."/>
      <error name="MaxConversionsNotEnabledForTheMarkets" errorcode="MaxConversionsNotEnabledForTheMarkets" code="4722" message="MaxConversions bidding scheme is not enabled for the markets(locations)."/>
      <error name="TargetCpaNotEnabledForTheMarkets" errorcode="TargetCpaNotEnabledForTheMarkets" code="4723" message="TargetCPA bidding scheme is not enabled for the markets(locations)."/>
      <error name="BiddingSchemeAndSharedBudgetAreMutuallyExclusive" errorcode="BiddingSchemeAndSharedBudgetAreMutuallyExclusive" code="4724" message="This bidding scheme and Shared Budget are mutually exclusive."/>
      <error name="SharedBudgetAndBiddingSchemeAreMutuallyExclusive" errorcode="SharedBudgetAndBiddingSchemeAreMutuallyExclusive" code="4725" message="Shared Budget and existed bidding scheme are mutually exclusive."/>
      <error name="BiddingSchemeNotEnabledForTheLocations" errorcode="BiddingSchemeNotEnabledForTheLocations" code="4726" message="The bidding scheme is not enabled for the markets(locations)."/>
      <error name="LocationNotEnabledForTheBiddingScheme" errorcode="LocationNotEnabledForTheBiddingScheme" code="4727" message="The location is not enabled for the bidding scheme."/>
      <error name="NotEnoughConversionsForTargetRoasBiddingScheme" errorcode="NotEnoughConversionsForTargetRoasBiddingScheme" code="4728" message="There must be at least 15 conversions in the last 30 days to use this bid strategy."/>
      <error name="NotEnoughRevenueForTargetRoasBiddingScheme" errorcode="NotEnoughRevenueForTargetRoasBiddingScheme" code="4729" message="There must be revenue tracking set up and revenue greater than zero in the last 30 days to use this bid strategy."/>
      <error name="InvalidTargetRoasValue" errorcode="InvalidTargetRoasValue" code="4730" message="Target ROAS(Revenue on Ad Spend) needs to be a positive integer."/>
      <error name="CustomerNotInPilotForTargetRoas" errorcode="CustomerNotInPilotForTargetRoas" code="4731" message="Customer not in pilot for Target ROAS(Revenue on Ad Spend) bid strategy."/>
      <error name="BidStrategyUnchangedBecauseNotEnoughRevenue" errorcode="BidStrategyUnchangedBecauseNotEnoughRevenue" code="4732" message="Import will not change bid strategy because there is no revenue"/>
      <error name="ConversionGoalCriteriaNotMetForBiddingScheme" errorcode="ConversionGoalCriteriaNotMetForBiddingScheme" code="4733" message="There must be a conversion goal set up to use this bid strategy."/>
      <error name="CustomerNotInPilotForMaxConversionValue" errorcode="CustomerNotInPilotForMaxConversionValue" code="4734" message="The customer is not in pilot for Max Conversion Value."/>
      <error name="CustomerNotInPilotForTargetImpressionShare" errorcode="CustomerNotInPilotForTargetImpressionShare" code="4735" message="The customer is not in pilot for the Target Impression Share bid strategy type."/>
      <error name="InvalidTargetAdPositionValue" errorcode="InvalidTargetAdPositionValue" code="4736" message="Target Ad Position is invalid."/>
      <error name="TargetImpressionShareIsRequired" errorcode="TargetImpressionShareIsRequired" code="4737" message="Target Impression Share is required for this bidding scheme."/>
      <error name="InvalidTargetImpressionShareValue" errorcode="InvalidTargetImpressionShareValue" code="4738" message="Target Impression Share needs to be a positive percentage value."/>
      <error name="TargetCostPerSaleInvalid" errorcode="TargetCostPerSaleInvalid" code="4739" message="Target cost per sale value is invalid."/>
      <error name="CostPerSaleBiddingSchemeCannotBeUpdated" errorcode="CostPerSaleBiddingSchemeCannotBeUpdated" code="4740" message="Cost per sale bidding scheme cannot be updated."/>
      <error name="InvalidShoppingCampaignPriorityForCostPerSale" errorcode="InvalidShoppingCampaignPriorityForCostPerSale" code="4741" message="Campaign priority must be set to highest for cost per sale campaign."/>  
      <error name="BidCannotBeManagedForBiddingScheme" errorcode="BidCannotBeManagedForBiddingScheme" code="4742" message="Bid cannot be managed for selected bidding scheme."/>
      <error name="InvalidMaxCpcValue" errorcode="InvalidMaxCpcValue" code="4743" message="MaxCpc value is invalid."/>
      <error name="InvalidTargetCpaValue" errorcode="InvalidTargetCpaValue" code="4744" message="TargetCpa value is invalid."/>
      <error name="ImmutableBiddingScheme" errorcode="ImmutableBiddingScheme" code="4745" message="Bidding scheme cannot be updated."/>
      <error name="DailyBudgetAmountLessThanTargetCostPerSale" errorcode="DailyBudgetAmountLessThanTargetCostPerSale" code="4746" message="Daily budget is less than CPS bid value."/>
      <error name="CostPerSaleNotWorkWithShoppableAds" errorcode="CostPerSaleNotWorkWithShoppableAds" code="4747" message="CPS bid strategy does not work with collection campaigns. Please change the bid strategy or de-select collection campaigns."/>
      <error name="InvalidManualCpcValue" errorcode="InvalidManualCpcValue" code="4748" message="ManualCPC value is invalid."/>
      <error name="ManualCpcIsRequired" errorcode="ManualCpcIsRequired" code="4749" message="ManualCPC bid value is required for this bidding scheme.."/>
      <error name="ManualCpcLessThanOrEqualToZero" errorcode="ManualCpcLessThanOrEqualToZero" code="4750" message="ManualCPC less than or equal to zero"/>
      <error name="MaxCpmLessThanOrEqualToZero" errorcode="MaxCpmLessThanOrEqualToZero" code="4751" message="MaxCpm Less Than Or Equal To Zero."/>
      <error name="InvalidMaxCpmValue" errorcode="InvalidMaxCpcValue" code="4752" message="MaxCpm value is invalid."/>
      <error name="MaxCpcGreaterThanCeiling" errorcode="MaxCpcGreaterThanCeiling" code="4753" message="MaxCpc Greater Than Ceiling."/>
      <error name="MaxCpmGreaterThanCeiling" errorcode="MaxCpmGreaterThanCeiling" code="4754" message="MaxCpm Greater Than Ceiling."/>

      <!--RemarketingList (range = 4800 to 4899 ) Those are APIV10 error code. For Bulk Schema 4.0 needs those error code, so include them in API V13.-->
      <error name="InvalidRemarketingListRuleItem" errorcode="InvalidRemarketingListRuleItem" code="4814" message="The remarketing list rule item is invalid."/>
      <error name="TooManyRuleItemsError" errorcode="TooManyRuleItemsError" code="4815" message="Too many rule items were specified in the remarketing list for the operation."/>
      <error name="TooManyRuleItemGroupsError" errorcode="TooManyRuleItemGroupsError" code="4816" message="Too many rule item groups were specified in the remarketing list for the operation."/>
      <error name="InvalidRemarketingListTagId" errorcode="InvalidRemarketingListTagId" code="4826" message="Remarketing list tag id is invalid or not specified."/>
      <error name="InvalidRemarketingListRule" errorcode="InvalidRemarketingListRule" code="4827" message="Remarketing list rule is invalid or not specified."/>

      <!--Audience (range = 4835 to 4899 )-->
      <error name="InvalidAudienceId" errorcode="InvalidAudienceId" code="4835" message="The audience ID is invalid."/>
      <error name="AudienceIdsNotPassed" errorcode="AudienceIdsNotPassed" code="4836" message="Audience IDs are required."/>
      <error name="DuplicateAudienceId" errorcode="DuplicateAudienceId" code="4837" message="Duplicate audience IDs are not allowed."/>
      <error name="InvalidAudienceName" errorcode="InvalidAudienceName" code="4838" message="The audience name is invalid."/>
      <error name="InvalidAudienceCriterionBidAdjustment" errorcode="InvalidAudienceCriterionBidAdjustment" code="4839" message="The ad group audience criterion's bid adjustment is invalid."/>
      <error name="CannotAssociateAudienceWithContentOnlyAdGroup" errorcode="CannotAssociateAudienceWithContentOnlyAdGroup" code="4840" message="Audiences cannot be associated with content only ad groups."/>
      <error name="InvalidAudienceDescription" errorcode="InvalidAudienceDescription" code="4841" message="Audience description is invalid."/>
      <error name="InvalidAudienceMembershipDuration" errorcode="InvalidAudienceMembershipDuration" code="4842" message="Audience membership duration is invalid."/>
      <error name="AudienceParentIdDoesNotMatchScope" errorcode="AudienceParentIdDoesNotMatchScope" code="4843" message="Audience parent id does not match the scope."/>
      <error name="AudiencesArrayShouldNotBeNullOrEmpty" errorcode="AudiencesArrayShouldNotBeNullOrEmpty" code="4844" message="Audiences are required."/>
      <error name="AudienceIsNull" errorcode="AudienceIsNull" code="4845" message="Audience cannot be null."/>
      <error name="AudienceIdIsNull" errorcode="AudienceIdIsNull" code="4846" message="Audience id cannot be null."/>
      <error name="AudienceIdIsNotNull" errorcode="AudienceIdIsNotNull" code="4847" message="Audience id must be null."/>
      <error name="InvalidAudienceParentId" errorcode="InvalidAudienceParentId" code="4848" message="Audience parent id is invalid or not specified."/>
      <error name="InvalidAudienceScope" errorcode="InvalidAudienceScope" code="4849" message="Audience scope is invalid or not specified."/>
      <error name="AudiencesArrayExceedsLimit" errorcode="AudiencesArrayExceedsLimit" code="4850" message="Audiences array exceeds the limit size."/>
      <error name="AudienceIdsArrayShouldNotBeNullOrEmpty" errorcode="AudienceIdsArrayShouldNotBeNullOrEmpty" code="4851" message="Audience ids array should not be null or empty."/>
      <error name="AudienceIdsArrayExceedsLimit" errorcode="AudienceIdsArrayExceedsLimit" code="4852" message="Audience ids array exceeds the limit size."/>
      <error name="DuplicateAudienceName" errorcode="DuplicateAudienceName" code="4853" message="Audience name is duplicate."/>
      <error name="AudienceCannotBeDeletedDueToExistingAdGroupCriterion" errorcode="AudienceCannotBeDeletedDueToExistingAdGroupCriterion" code="4854" message="Audience cannot be deleted due to existing association."/>
      <error name="AudienceCanNotChangeScopeOnUpdate" errorcode="AudienceCanNotChangeScopeOnUpdate" code="4855" message="Audience scope cannot be changed on update."/>
      <error name="InMarketAudienceIsReadOnly" errorcode="InMarketAudienceIsReadOnly" code="4856" message="In-market audience is read-only."/>
      <error name="CustomAudienceCouldNotBeAdded" errorcode="CustomAudienceCouldNotBeAdded" code="4857" message="Could not add custom audience."/>
      <error name="MaxCustomAudiencesPerCustomerLimitReached" errorcode="MaxCustomAudiencesPerCustomerLimitReached" code="4858" message="Max custom audiences per customer limit is reached."/>
      <error name="InvalidAudienceType" errorcode="InvalidAudienceType" code="4859" message="Audience Type is invalid."/>
      <error name="CustomAudienceAndInMarketAudienceCouldNotBeDeleted" errorcode="CustomAudienceAndInMarketAudienceCouldNotBeDeleted" code="4860" message="Could not delete custom audience and in-market audience."/>
      <error name="AudienceIdDoNotMatchAudienceType" errorcode="AudienceIdDoNotMatchAudienceType" code="4861" message="Audience Id do not match audience type."/>
      <error name="MaxAudienceCriterionsPerAccountLimitReached" errorcode="MaxAudienceCriterionsPerAccountLimitReached" code="4862" message="Max audience criterions per account limit is reached."/>
      <error name="MaxRemarketingListAssociationsPerAccountLimitReached" errorcode="MaxRemarketingListAssociationsPerAccountLimitReached" code="4863" message="Max remarketing list associations per account limit is reached."/>
      <error name="InMarketAudienceCouldNotBeDeleted" errorcode="InMarketAudienceCouldNotBeDeleted" code="4864" message="Could not delete in-market audience."/>
      <error name="MaxAudiencesPerAccountLimitReached" errorcode="MaxAudiencesPerAccountLimitReached" code="4865" message="Max audiences per account limit is reached."/>
      <error name="MaxAudiencesPerCustomerLimitReached" errorcode="MaxAudiencesPerCustomerLimitReached" code="4866" message="Max audiences per customer limit is reached."/>
      <error name="MaxInMarketAudienceExclusionPerAccountLimitReached" errorcode="MaxInMarketAudienceExclusionPerAccountLimitReached" code="4867" message="Max in-market audience exclusion per account limit is reached."/>
      <error name="SimilarRemarketingListIsReadOnly" errorcode="SimilarRemarketingListIsReadOnly" code="4868" message="Similar remarketing list is read-only."/>
      <error name="SimilarRemarketingListCouldNotBeDeleted" errorcode="SimilarRemarketingListCouldNotBeDeleted" code="4869" message="Could not delete similar remarketing list."/>
      <error name="CustomerNotEligibleForSimilarRemarketingList" errorcode="CustomerNotEligibleForSimilarRemarketingList" code="4870" message="Customer is not eligible for similar remarketing list."/>
      <error name="AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociations" errorcode="AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociations" code="4871" message="Audience cannot be deleted due to its paired similar audience has associations."/>
      <error name="IllegalAudienceAssociationConversionFromExclusion" errorcode="IllegalAudienceAssociationConversionFromExclusion" code="4872" message="Audience exclusions can't be changed to enabled or paused."/>
      <error name="CustomerShareEntityScopeDoesNotMatch" errorcode="CustomerShareEntityScopeDoesNotMatch" code="4873" message="Customer share is not supported for this entity scope."/>
      <error name="MaxCriterionLimitExceededForCustomer" errorcode="MaxCriterionLimitExceededForCustomer" code="4874" message="Max criterions per customer limit is reached."/>
      <error name="CustomerIsNotEligibleForImpressionBasedRemarketingList" errorcode="CustomerIsNotEligibleForImpressionBasedRemarketingList" code="4875" message="Customer Is Not Eligible For ImpressionBasedRemarketingList."/>
      <error name="CustomerCannotOperateImpressionBasedRemarketingList" errorcode="CustomerCannotOperateImpressionBasedRemarketingList" code="4876" message="Customer Cannot Operate ImpressionBasedRemarketingList."/>
      <error name="InvalidEntityIdForImpressionBasedRemarketingList" errorcode="InvalidEntityIdForImpressionBasedRemarketingList" code="4877" message="Invalid EntityId For ImpressionBasedRemarketingList."/>
      <error name="InvalidEntityTypeForImpressionBasedRemarketingList" errorcode="InvalidEntityTypeForImpressionBasedRemarketingList" code="4878" message="Invalid Entity Type For ImpressionBasedRemarketingList."/>
      <error name="ImpressionBasedRemarketingListCanOnlyBeEditedByCreator" errorcode="ImpressionBasedRemarketingListCanOnlyBeEditedByCreator" code="4879" message="ImpressionBasedRemarketingList Can Only Be Edited By Creator."/>
      <error name="ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator" errorcode="ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator" code="4880" message="ImpressionBasedRemarketingList Can Only Be Deleted By Creator."/>
      <error name="DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList" errorcode="DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList" code="4881" message="Duplicated EntityId And Type For ImpressionBasedRemarketingList."/>

      <!--Budget (range = 4900 to 4999 )-->
      <error name="BudgetPilotNotEnabledForCustomer" errorcode="CampaignServiceBudgetPilotNotEnabledForCustomer" code="4900" message="Customer not in Shared Budget pilot."/>
      <error name="BudgetsNullOrEmpty" errorcode="CampaignServiceBudgetsNullOrEmpty" code="4901" message="The budget list should not be null or empty."/>
      <error name="BudgetIsNull" errorcode="CampaignServiceBudgetIsNull" code="4902" message="The budget should not be null."/>
      <error name="BudgetIdShouldBeNullOnAdd" errorcode="CampaignServiceBudgetIdShouldBeNullOnAdd" code="4903" message="The budget id is read-only and must be null."/>
      <error name="BudgetNameMissing" errorcode="CampaignServiceBudgetNameMissing" code="4904" message="The budget name should not be null or empty."/>
      <error name="BudgetNameTooLong" errorcode="CampaignServiceBudgetNameTooLong" code="4905" message="The budget name is too long."/>
      <error name="BudgetNameInvalid" errorcode="CampaignServiceBudgetNameInvalid" code="4906" message="The budget name has invalid characters."/>
      <error name="DuplicateBudgetName" errorcode="CampaignServiceDuplicateBudgetName" code="4907" message="The budget name already exists."/>
      <error name="BudgetTypeCannotBeNullOnAdd" errorcode="CampaignServiceBudgetTypeCannotBeNullOnAdd" code="4908" message="The budget type must be specified."/>
      <error name="MonthlyBudgetNotAllowed" errorcode="CampaignServiceMonthlyBudgetNotAllowed" code="4909" message="Monthly budget type is not a valid Shared Budget type."/>
      <error name="BudgetAmountMissing" errorcode="CampaignServiceBudgetAmountMissing" code="4910" message="The budget amount is not provided."/>
      <error name="BudgetAmountIsAboveLimit" errorcode="CampaignServiceBudgetAmountIsAboveLimit" code="4911" message="The budget amount is above the limit."/>
      <error name="BudgetAmountIsBelowLimit" errorcode="CampaignServiceBudgetAmountIsBelowLimit" code="4912" message="The budget amount is below the limit."/>
      <error name="BudgetBatchLimitExceeded" errorcode="CampaignServiceBudgetBatchLimitExceeded" code="4913" message="The budget list exceeds the maximum batch size."/>
      <error name="BudgetEntityLimitExceeded" errorcode="CampaignServiceBudgetEntityLimitExceeded" code="4914" message="The number of budgets for the account has been exceeded."/>
      <error name="DuplicateBudgetId" errorcode="CampaignServiceDuplicateBudgetId" code="4915" message="The budget list contains duplicate budget ids."/>
      <error name="BudgetIdInvalid" errorcode="CampaignServiceBudgetIdInvalid" code="4916" message="The budget id is invalid."/>
      <error name="CannotUpdateSharedDailyBudgetToUnsharedMonthlyBudget" errorcode="CampaignServiceCannotUpdateSharedDailyBudgetToUnsharedMonthlyBudget" code="4917" message="The campaign is using a shared daily budget, therefore it can't be updated to use an unshared monthly budget. You can use an unshared daily budget instead."/>
      <error name="BudgetIsSharedWithCampaigns" errorcode="CampaignServiceBudgetIsSharedWithCampaigns" code="4918" message="The budget is shared with at least one campaign, therefore it can't be deleted."/>
      <error name="SharedBudgetNotAllowedWithPerformanceTargetCampaign" errorcode="CampaignServiceSharedBudgetNotAllowedWithPerformanceTargetCampaign" code="4919" message="The campaign associated with a Performance Target cannot use a shared budget."/>

      <!--Dynamic Search Ads (range = 5100 to 5199)-->
      <error name="CustomerNotEligibleForDynamicSearchAds" errorcode="CustomerNotEligibleForDynamicSearchAds" code="5100" message="Customer Not Eligible For Dynamic Search Ads"/>
      <error name="DynamicSearchAdsCampaignSettingNotAllowedForUpdate" errorcode="DynamicSearchAdsSettingNotAllowedForUpdate" code="5101" message="The domain name and language settings of a Dynamic Search Ads campaign cannot be updated."/>
      <error name="InvalidDynamicSearchAdsCampaignDomainName" errorcode="CampaignInvalidDynamicSearchAdsDomainName" code="5102" message="Domain name of Dynamic Search Ads campaign is invalid"/>
      <error name="InvalidDynamicSearchAdsCampaignLanguage" errorcode="CampaignInvalidDynamicSearchAdsLanguage" code="5103" message="Language of Dynamic Search Ads campaign is invalid"/>
      <error name="MaxCampaignWebpageCriterionsLimitExceededForCampaign" errorcode="MaxCampaignWebpageCriterionsLimitExceededForCampaign" code="5104" message="Max campaign webpage criterion limit exceeded for campaign"/>
      <error name="BiddableOrNegativeStatusCannotBeUpdated" errorcode="BiddableOrNegativeStatusCannotBeUpdated" code="5105" message="Cannot update biddable criterion to negative and vice versa"/>
      <error name="DynamicSearchAdPath1TooLong" errorcode="DynamicSearchAdPath1TooLong" code="5106" message="Path 1 is over the character limit."/>
      <error name="DynamicSearchAdPath2TooLong" errorcode="DynamicSearchAdPath2TooLong" code="5107" message="Path 2 is over the character limit."/>
      <error name="DynamicSearchAdPath1Invalid" errorcode="DynamicSearchAdPath1Invalid" code="5108" message="Path 1 is not valid."/>
      <error name="DynamicSearchAdPath2Invalid" errorcode="DynamicSearchAdPath2Invalid" code="5109" message="Path 2 is not valid."/>
      <error name="DynamicSearchAdPath2SetWithoutPath1" errorcode="DynamicSearchAdPath2SetWithoutPath1" code="5110" message="The path2 is set without setting path1."/>
      <error name="DynamicSearchAdTextPart2PilotNotEnabledForCustomer" errorcode="DynamicSearchAdTextPart2PilotNotEnabledForCustomer" code="5111" message="The customer is not in pilot for the Dynamic Search Ads Text Part 2 feature."/>
      <error name="DynamicSearchAdTextPart2TooLong" errorcode="DynamicSearchAdTextPart2TooLong" code="5112" message="Ad text part 2 is over the character limit."/>
      <error name="DynamicSearchAdTextPart2Invalid" errorcode="DynamicSearchAdTextPart2Invalid" code="5113" message="Ad text part 2 is not valid."/>
      <error name="DSADomainLanguagesPhase2PilotNotEnabledForCustomer" errorcode="DSADomainLanguagesPhase2PilotNotEnabledForCustomer" code="5114" message="The customer is not enabled for the DSA domain languages Phase 2 pilot program." />
      <error name="AdGroupTypeInvalid" errorcode="AdGroupTypeInvalid" code="5115" message="The ad group type field of the ad group is invalid." />
      <error name="AdGroupTypeImmutable" errorcode="AdGroupTypeImmutable" code="5116" message="The ad group type field of an ad group cannot be updated." />
      <error name="AccountNotInPilotForMixedModeCampaign" errorcode="AccountNotInPilotForMixedModeCampaign" code="5117" message="The account is not in the mixed mode campaign pilot." />
      <error name="InvalidAdGroupTypeForCampaignType" errorcode="InvalidAdGroupTypeForCampaignType" code="5118" message="The ad group type is not valid for the campaign type." />
      <error name="CannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings" errorcode="CannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings" code="5119" message="Dynamic search ad groups cannot be added to campaigns without dynamic search settings." />
      <error name="DynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup" errorcode="DynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup" code="5120" message="Dynamic search ads cannot be added to non dynamic search ad groups." />
      <error name="AdTypeInvalidForAdGroup" errorcode="AdTypeInvalidForAdGroup" code="5121" message="The ad type is not valid for the ad group type." />
      <error name="InvalidCriterionForCampaignType" errorcode="InvalidCriterionForCampaignType" code="5122" message="The criterion is not valid for the campaign type." />
      <error name="InvalidCriterionForAdGroupType" errorcode="InvalidCriterionForAdGroupType" code="5123" message="The criterion is not valid for the ad group type." />
      <error name="DynamicSearchAdsNotSupportedForDisclaimerCampaign" errorcode="DynamicSearchAdsNotSupportedForDisclaimerCampaign" code="5124" message="Dynamic search ads are not supported for disclaimer campaigns." />
      <error name="EntityNotAllowedForDynamicSearchAdsAdGroup" errorcode="EntityNotAllowedForDynamicSearchAdsAdGroup" code="5125" message="The entity is not allowed for dynamic search ad groups." />
      <error name="DSADomainLanguagesPhase3PilotNotEnabledForCustomer" errorcode="DSADomainLanguagesPhase3PilotNotEnabledForCustomer" code="5126" message="The customer is not enabled for the DSA domain languages Phase 3 pilot program." />
      <error name="DSADomainLanguagesPhase4PilotNotEnabledForCustomer" errorcode="DSADomainLanguagesPhase4PilotNotEnabledForCustomer" code="5127" message="The customer is not enabled for the DSA domain languages Phase 4 pilot program." />
      <error name="CannotAddCriterionToCampaignWithoutDynamicSearchSettings" errorcode="CannotAddCriterionToCampaignWithoutDynamicSearchSettings" code="5128" message="Criterion cannot be added to campaigns without dynamic search settings." />
      <error name="MaxDSAAutoTargetPerAccountLimitReached" errorcode="MaxDSAAutoTargetPerAccountLimitReached" code="5129" message="The maximum number of auto targets per account is exceeded." />
      <error name="DynamicSearchAdCampaignCreationNotAllowed" errorcode="DynamicSearchAdCampaignCreationNotAllowed" code="5130" message="Dynamic search ad campaign creation is no longer allowed. Please create a search campaign with DSA settings instead." />
      <error name="AccountNotEligibleForDynamicDescription" errorcode="AccountNotEligibleForDynamicDescription" code="5131" message="Account is not eligible for Dynamic Description in Dynamic Search Ads." />
      <!--Webpage Criterion (range = 5200 to 5299)-->
      <error name="CampaignTypeIsNotDynamicSearchAdsCampaign" errorcode="CampaignTypeIsNotDynamicSearchAdsCampaign" code="5200" message="Corresponding CampaignType is not Dynamic Search Ads campaign"/>
      <error name="WebpageCriterionParameterIsNull" errorcode="WebpageCriterionParameterIsNull" code="5201" message="Parameter of webpage criterion is null"/>
      <error name="WebpageCriterionNameInvalid" errorcode="WebpageCriterionNameInvalid" code="5202" message="Criterion name of webpage criterion is invalid"/>
      <error name="WebpageCriterionConditionsContainDuplicateValues" errorcode="WebpageCriterionConditionsContainDuplicateValues" code="5203" message="Conditions of webpage criterion contain duplicate values"/>
      <error name="TooManyWebpageCriterionConditions" errorcode="TooManyWebpageCriterionConditions" code="5204" message="Number of criterion conditions exceed limiation"/>
      <error name="WebpageCriterionConditionInvalid" errorcode="WebpageCriterionConditionInvalid" code="5205" message="Webpage criterion condition is invalid"/>
      <error name="WebpageCriterionWebpageConditionOperandInvalid" errorcode="WebpageCriterionWebpageConditionOperandInvalid" code="5206" message="Operand of webpage criterion condition is invalid"/>
      <error name="WebpageCriterionWebpageConditionArgumentInvalid" errorcode="WebpageCriterionWebpageConditionArgumentInvalid" code="5207" message="Argument of webpage criterion condition is invalid"/>
      <error name="NegativeWebpageCriterionConditionIsNullOrEmpty" errorcode="NegativeWebpageCriterionConditionIsNullOrEmpty" code="5208" message="Negative webpage criterion condition is null or empty"/>
      <error name="CannotUpdateCriterionForWebpageCriterion" errorcode="CannotUpdateCriterionForWebpageCriterion" code="5209" message="Webpage criterion condition cannot be updated"/>
      <error name="BiddingSchemeMustInheritFromParentEntity" errorcode="BiddingSchemeMustInheritFromParentEntity" code="5210" message="The bidding scheme of webpage criterion must inherit from parent entity"/>
      <error name="SubstitutionNotSupportedForDynamicSearchAd" errorcode="SubstitutionNotSupportedForDynamicSearchAd" code="5211" message="Dynamic Search Ads do not support keyword substitution"/>
      <error name="WebpageCriterionAlreadyExists" errorcode="WebpageCriterionAlreadyExists" code="5212" message="Webpage criterion already exists"/>
      <error name="InvalidUpgradedUrlsForWebpageCriterion" errorcode="InvalidUpgradedUrlsForWebpageCriterion" code="5213" message="You cannot set Final URLs for a webpage criterion. Webpage criterion only support tracking template and custom parameters for Upgraded URLs."/>
      <error name="WebpageCriterionWebpageConditionOperatorInvalid" errorcode="WebpageCriterionWebpageConditionOperatorInvalid" code="5214" message="Operator of webpage criterion condition is invalid"/>
      <error name="WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain" errorcode="WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain" code="5215" message="Url Equals value should use the same domain you used in your campaign settings."/>
      <error name="AccountIsNotInPilotForWebpageCriterionWebpageConditionOperatorOrUrlEquals" errorcode="AccountIsNotInPilotForWebpageCriterionWebpageConditionOperatorOrUrlEquals" code="5216" message="Account is not in Pilot for webpage criterion webpage condition operator or URL Equals"/>
      <error name="WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter" errorcode="WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter" code="5217" message="The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options." />
	  
      <!--Conversion Tracking (range = 5300 to 5399)-->
      <error name="InvalidUetTagId" errorcode="InvalidUetTagId" code="5300" message="The UET Tag ID is invalid."/>
      <error name="DuplicateUetTagId" errorcode="DuplicateUetTagId" code="5301" message="Duplicate UET Tag IDs are not allowed."/>
      <error name="DuplicateUetTagName" errorcode="DuplicateUetTagName" code="5302" message="Duplicate UET Tag names are not allowed."/>
      <error name="UetTagNameAlreadyExists" errorcode="UetTagNameAlreadyExists" code="5303" message="The UET Tag with the same name already exists."/>
      <error name="UetTagIdsExceedsLimit" errorcode="UetTagIdsExceedsLimit" code="5304" message="The list of UET Tag IDs exceeds the limit."/>
      <error name="UetTagNameNotPassed" errorcode="UetTagNameNotPassed" code="5305" message="The UET Tag name is required."/>
      <error name="InvalidUetTagName" errorcode="InvalidUetTagName" code="5306" message="The UET Tag name is empty or exceeds the length limit."/>
      <error name="InvalidUetTagDescription" errorcode="InvalidUetTagDescription" code="5307" message="The UET Tag description exceeds the length limit."/>
      <error name="UetTagsNotPassed" errorcode="UetTagsNotPassed" code="5308" message="The UET Tags are required."/>
      <error name="UetTagArrayExceedsLimit" errorcode="UetTagArrayExceedsLimit" code="5309" message="The list of UET Tags exceeds the limit."/>
      <error name="UetTagIsNull" errorcode="UetTagIsNull" code="5310" message="The UET Tag is required."/>
      <error name="UetTagIdIsNotNull" errorcode="UetTagIdIsNotNull" code="5311" message="The UET Tag ID should be null."/>
      <error name="UetTagIdIsNull" errorcode="UetTagIdIsNull" code="5312" message="The UET Tag ID is required."/>
      <error name="TotalUetTagsExceedLimit" errorcode="TotalUetTagsExceedLimit" code="5313" message="The total UET Tag count would exceed the limit."/>
      <error name="InvalidConversionGoalId" errorcode="InvalidConversionGoalId" code="5314" message="The Conversion Goal ID is invalid."/>
      <error name="DuplicateConversionGoalId" errorcode="DuplicateConversionGoalId" code="5315" message="Duplicate Conversion Goal IDs are not allowed."/>
      <error name="DuplicateConversionGoalName" errorcode="DuplicateConversionGoalName" code="5316" message="Duplicate Conversion Goal names are not allowed."/>
      <error name="ConversionGoalNameAlreadyExists" errorcode="ConversionGoalNameAlreadyExists" code="5317" message="The Conversion Goal with the same name already exists for the customer."/>
      <error name="ConversionGoalIdArrayExceedsLimit" errorcode="ConversionGoalIdArrayExceedsLimit" code="5318" message="The list of Conversion Goal IDs exceeds the limit."/>
      <error name="ConversionGoalNameNotPassed" errorcode="ConversionGoalNameNotPassed" code="5319" message="The Conversion Goal name is required."/>
      <error name="InvalidConversionGoalName" errorcode="InvalidConversionGoalName" code="5320" message="The Conversion Goal name is invalid or exceeds the length limit."/>
      <error name="ConversionGoalsNotPassed" errorcode="ConversionGoalsNotPassed" code="5321" message="The Conversion Goals are required."/>
      <error name="ConversionGoalArrayExceedsLimit" errorcode="ConversionGoalArrayExceedsLimit" code="5322" message="The list of Conversion Goals exceeds the limit."/>
      <error name="ConversionGoalIsNull" errorcode="ConversionGoalIsNull" code="5323" message="The Conversion Goal is required."/>
      <error name="ConversionGoalIdIsNotNull" errorcode="ConversionGoalIdIsNotNull" code="5324" message="The Conversion Goal ID must be null."/>
      <error name="ConversionGoalIdIsNull" errorcode="ConversionGoalIdIsNull" code="5325" message="The Conversion Goal ID is required."/>
      <error name="TotalConversionGoalsExceedAccountLimit" errorcode="TotalConversionGoalsExceedAccountLimit" code="5326" message="The total Conversion Goal count would exceed the account level limit."/>
      <error name="TotalConversionGoalsExceedCustomerLimit" errorcode="TotalConversionGoalsExceedCustomerLimit" code="5327" message="The total Conversion Goal count would exceed the customer level limit."/>
      <error name="InvalidConversionGoalStatus" errorcode="InvalidConversionGoalStatus" code="5328" message="The Conversion Goal status is invalid."/>
      <error name="ConversionGoalTypesNotPassed" errorcode="ConversionGoalTypesNotPassed" code="5329" message="The Conversion Goal types are required."/>
      <error name="UetTagIdsNotPassed" errorcode="UetTagIdsNotPassed" code="5330" message="The UET Tag IDs are required."/>
      <error name="ConversionGoalTypeNotMatched" errorcode="ConversionGoalTypeNotMatched" code="5331" message="The Conversion Goal type should be matched to the goal entity."/>
      <error name="InvalidConversionGoalRevenueType" errorcode="InvalidConversionGoalRevenueType" code="5332" message="The Conversion Goal revenue type is invalid."/>
      <error name="InvalidConversionGoalRevenueValue" errorcode="InvalidConversionGoalRevenueValue" code="5333" message="The Conversion Goal revenue value is invalid."/>
      <error name="UrlGoalUrlExpressionNotPassed" errorcode="UrlGoalUrlExpressionNotPassed" code="5334" message="The Url Expression for Url Goal is required."/>
      <error name="AppInstallGoalStoreIdNotPassed" errorcode="AppInstallGoalStoreIdNotPassed" code="5335" message="The Store ID for App Install Goal is required."/>
      <error name="EventGoalExpressionWithOperatorNotPassed" errorcode="EventGoalExpressionWithOperatorNotPassed" code="5336" message="At least one expression and operator pair is required for an Event Goal."/>
      <error name="InvalidConversionGoalConversionWindow" errorcode="InvalidConversionGoalConversionWindow" code="5337" message="The Conversion Window for Conversion Goal is invalid."/>
      <error name="InvalidDurationGoalDurationTime" errorcode="InvalidDurationGoalDurationTime" code="5338" message="The Duration Time for Duration Goal is invalid."/>
      <error name="InvalidMinimumPagesViewedForGoal" errorcode="InvalidMinimumPagesViewedForGoal" code="5339" message="The Minimum Pages Viewed is invalid for the goal."/>
      <error name="InvalidAppInstallGoalAppPlatform" errorcode="InvalidAppInstallGoalAppPlatform" code="5340" message="The App Platform for App Install Goal is invalid."/>
      <error name="InvalidUrlGoalUrlExpression" errorcode="InvalidUrlGoalUrlExpression" code="5341" message="The Url Expression for Url Goal is empty or exceeds the length limit."/>
      <error name="InvalidEventGoalCategoryExpression" errorcode="InvalidEventGoalCategoryExpression" code="5342" message="The Category Expression for Event Goal is empty or exceeds the length limit."/>
      <error name="InvalidEventGoalActionExpression" errorcode="InvalidEventGoalActionExpression" code="5343" message="The Action Expression for Event Goal is empty or exceeds the length limit."/>
      <error name="InvalidEventGoalLabelExpression" errorcode="InvalidEventGoalLabelExpression" code="5344" message="The Label Expression for Event Goal is empty or exceeds the length limit."/>
      <error name="InvalidEventGoalValue" errorcode="InvalidEventGoalValue" code="5345" message="The Value for Event Goal is invalid."/>
      <error name="InvalidAppInstallGoalStoreId" errorcode="InvalidAppInstallGoalStoreId" code="5346" message="The Store ID for App Install Goal is invalid."/>
      <error name="ConversionGoalTypesDoNotMatchExistingValue" errorcode="ConversionGoalTypesDoNotMatchExistingValue" code="5347" message="The requested conversion goal types do not match the existing conversion goal type."/>
      <error name="InvalidAppInstallGoalScope" errorcode="InvalidAppInstallGoalScope" code="5348" message="App Install Goal should scope to the customer."/>
      <error name="InvalidAppInstallGoalCountType" errorcode="InvalidAppInstallGoalCountType" code="5349" message="The count type for App Install Goal should be All."/>
      <error name="InvalidGoalCurrencyCode" errorcode="InvalidGoalCurrencyCode" code="5351" message="The currency code of the goal is invalid."/>
      <error name="GoalCurrencyCodeIsNotNull" errorcode="GoalCurrencyCodeNotSupported" code="5352" message="The currency code is not supported for this goal type."/>
      <error name="GoalCurrencyCodeIsNull" errorcode="GoalCurrencyCodeIsNull" code="5355" message="The currency code is required for this goal type."/>
      <error name="GoalTypeCannotBeChanged" errorcode="GoalTypeCannotBeChanged" code="5356" message="The goal type can't be changed for In-Store Transaction Goal or Offline Conversion Goal."/>
      <error name="InStoreTransactionGoalScopeInvalid" errorcode="InStoreTransactionGoalScopeInvalid" code="5357" message="In-Store Transaction goals must be set to Customer level scope."/>
      <error name="OnlyOneInStoreTransactionGoalAllowedPerCustomer" errorcode="OnlyOneInStoreTransactionGoalAllowedPerCustomer" code="5358" message="Only one In-Store Transaction Goal can be created per customer."/>
      <error name="InStoreTransactionPilotNotEnabledForCustomer" errorcode="InStoreTransactionPilotNotEnabledForCustomer" code="5359" message="In-Store Transaction pilot not enabled for current customer."/>
      <error name="CustomerScopeNotSupportedForConversionGoalType" errorcode="CustomerScopeNotSupportedForConversionGoalType" code="5360" message="The customer scope is not supported for this conversion goal type."/>
      <error name="CannotUpdateCriterionStatusDueToTagNotAvailable" errorcode="CannotUpdateCriterionStatusDueToTagNotAvailable" code="5361" message="Cannot update criterion status due to the UET Tag of the associated audience is not available."/>
      <error name="CustomerNotEligibleForExternalAttribution" errorcode="CustomerNotEligibleForExternalAttribution" code="5362" message="Customer Not Eligible For External Attribution."/>
      <error name="IsExternallyAttributedCannotBeChanged" errorcode="IsExternallyAttributedCannotBeChanged" code="5363" message="The property IsExternallyAttributed can not be changed."/>
      <error name="GoalLevelCannotBeChanged" errorcode="GoalLevelCannotBeChanged" code="5364" message="The conversion goal level cannot be changed. Please create a new goal."/>
      <error name="GoalLevelCannotBeDowngraded" errorcode="GoalLevelCannotBeDowngraded" code="5365" message="The conversion goal level cannot be downgraded once it's been created. Please create a new goal."/>
      
      <error name="InvalidTagTrackingCode" errorcode="InvalidTagTrackingCode" code="8500" message="Tag tracking code length doesn't match requirements."/>
      <error name="InvalidTagStatus" errorcode="InvalidTagStatus" code="8501" message="Invalid tag status."/>
      <error name="TagsBatchSizeExceesdLimit" errorcode="TagsBatchSizeExceesdLimit" code="8502" message="Too many tags passed in."/>
      <error name="InvalidDestinationGoalExpressionOperator" errorcode="InvalidDestinationGoalExpressionOperator" code="8503" message="Invalid destination goal expression operator."/>
      <error name="InvalidDurationGoalValueOperator" errorcode="InvalidDurationGoalValueOperator" code="8504" message="Invalid duration goal value operator."/>
      <error name="InvalidEventGoalCategoryOperator" errorcode="InvalidEventGoalCategoryOperator" code="8505" message="Invalid event goal category operator."/>
      <error name="InvalidEventGoalActionOperator" errorcode="InvalidEventGoalActionOperator" code="8506" message="Invalid event goal action operator."/>
      <error name="InvalidEventGoalLabelOperator" errorcode="InvalidEventGoalLabelOperator" code="8507" message="Invalid event goal label operator."/>
      <error name="InvalidEventGoalValueOperator" errorcode="InvalidEventGoalValueOperator" code="8508" message="Invalid event goal value operator."/>
      <error name="InvalidPageViewsPerVisitValueOperator" errorcode="InvalidPageViewsPerVisitValueOperator" code="8509" message="Invalid page views per visit value operator."/>
      <error name="InvalidProductConversionGoal" errorcode="InvalidProductConversionGoal" code="8511" message="Invalid product conversion goal."/>
      <error name="AttributionModelTypeCannotBeUpdated" errorcode="AttributionModelTypeCannotBeUpdated" code="8514" message="Attribution Model Type cannot be updated."/>
      <error name="SmartGoalShouldBeAccountLevel" errorcode="SmartGoalShouldBeAccountLevel" code="8517" message="Smart Goal Should Be Account Level."/>
      <error name="SmartGoalShouldBeOnlyOne" errorcode="SmartGoalShouldBeOnlyOne" code="8518" message="Smart Goal should be only one for one account."/>
      <error name="SmartGoalCouldNotBeCreatedByCustomer" errorcode="SmartGoalCouldNotBeCreatedByCustomer" code="8520" message="Smart Goal could not be created by customer."/>
      <error name="OnlyOneInStoreTransactionGoalBeAllowedPerCustomer" errorcode="OnlyOneInStoreTransactionGoalBeAllowedPerCustomer" code="8521" message="Only one in-store transaction goal be allowed per customer."/>
      <error name="AccountNotEligibleForSmartGoal" errorcode="AccountNotEligibleForSmartGoal" code="8522" message="Smart Goal is not available for the account."/>
        <!--Campaign Languages-->
      <error name="CampaignLanguagesNotEnabledForCustomer" errorcode="CampaignLanguagesNotEnabledForCustomer" code="5350" message="The customer is not enabled for campaign languages."/>
      <error name="AllCampaignLanguagesCannotBeRemoved" errorcode="AllCampaignLanguagesCannotBeRemoved" code="5353" message="All campaign languages cannot be removed."/>
      <error name="CampaignLanguageSpecifiedMoreThanOnce" errorcode="CampaignLanguageSpecifiedMoreThanOnce" code="5354" message="Campaign language specified more than once."/>

      <!--Label (range = 5400 to 5499 )-->
      <error name="LabelsListIsNullOrEmpty" errorcode="CampaignServiceLabelsListIsNullOrEmpty" code="5400" message="You must include one or more labels."/>
      <error name="LabelsEntityLimitExceeded" errorcode="CampaignServiceLabelsEntityLimitExceeded" code="5401" message="You cannot add any more labels to the account."/>
      <error name="LabelIsNull" errorcode="CampaignServiceLabelIsNull" code="5402" message="The label is required."/>
      <error name="DuplicateLabelId" errorcode="CampaignServiceDuplicateLabelId" code="5404" message="Duplicate label identifiers cannot be included in the same request."/>
      <error name="LabelIdInvalid" errorcode="CampaignServiceLabelIdInvalid" code="5405" message="The label identifier is invalid."/>
      <error name="LabelNameInvalid" errorcode="CampaignServiceLabelNameInvalid" code="5406" message="The label name is invalid."/>
      <error name="LabelNameLengthExceeded" errorcode="CampaignServiceLabelNameLengthExceeded" code="5407" message="The length of the label name is too long."/>
      <error name="LabelNameDuplicate" errorcode="CampaignServiceLabelNameDuplicate" code="5408" message="Duplicate label names are not allowed in the same account."/>
      <error name="LabelDescriptionLengthExceeded" errorcode="CampaignServiceLabelDescriptionLengthExceeded" code="5409" message="The length of the label description is too long."/>
      <error name="LabelColorCodeInvalid" errorcode="CampaignServiceLabelColorCodeInvalid" code="5410" message="The label color code is invalid."/>
      <error name="LabelDescriptionInvalid" errorcode="CampaignServiceLabelDescriptionInvalid" code="5411" message="The label description is invalid."/>
      <error name="LabelPilotNotEnabledForCustomer" errorcode="CampaignServiceLabelPilotNotEnabledForCustomer" code="5412" message="The customer is not enabled for the labels feature."/>
      <error name="LabelAssociationListIsNullOrEmpty" errorcode="CampaignServiceLabelAssociationListIsNullOrEmpty" code="5413" message="You must include one or more label associations."/>
      <error name="LabelAssociationsBatchLimitExceeded" errorcode="CampaignServiceLabelAssociationsBatchLimitExceeded" code="5414" message="The maximum number of label associations per request is exceeded."/>
      <error name="LabelAssociationIsNull" errorcode="CampaignServiceLabelAssociationIsNull" code="5415" message="The label association is required."/>
      <error name="DuplicateLabelAssociation" errorcode="CampaignServiceDuplicateLabelAssociation" code="5416" message="Duplicate label associations are not allowed."/>
      <error name="LabelAssociationsPerEntityLimitExceeded" errorcode="CampaignServiceLabelAssociationsPerEntityLimitExceeded" code="5417" message="You cannot associate any more labels with the entity."/>
      <error name="LabelAssociationDoesNotExist" errorcode="CampaignServiceLabelAssociationDoesNotExist" code="5418" message="The label association does not exist."/>
      <error name="LabelCampaignAssociationsAccountLimitExceeded" errorcode="CampaignServiceLabelCampaignAssociationsAccountLimitExceeded" code="5419" message="The limit of label and campaign associations for the account would be exceeded."/>
      <error name="LabelAdGroupAssociationsAccountLimitExceeded" errorcode="CampaignServiceLabelAdGroupAssociationsAccountLimitExceeded" code="5420" message="The limit of label and ad group associations for the account would be exceeded."/>
      <error name="LabelAdAssociationsAccountLimitExceeded" errorcode="CampaignServiceLabelAdAssociationsAccountLimitExceeded" code="5421" message="The limit of label and ad associations for the account would be exceeded."/>
      <error name="LabelKeywordAssociationsAccountLimitExceeded" errorcode="CampaignServiceLabelKeywordAssociationsAccountLimitExceeded" code="5422" message="The limit of label and keyword associations for the account would be exceeded."/>
      <error name="LabelBatchLimitExceeded" errorcode="CampaignServiceLabelBatchLimitExceeded" code="5423" message="The maximum number of labels per request is exceeded."/>
      <error name="LabelAssociationEntityTypeInvalid" errorcode="CampaignServiceLabelAssociationEntityTypeInvalid" code="5424" message="Labels cannot be associated with this entity type."/>
      <error name="LabelAssociationEntityIdInvalid" errorcode="CampaignServiceLabelAssociationEntityIdInvalid" code="5425" message="The label association contains an invalid entity ID."/>
      <error name="LabelIdsBatchLimitExceeded" errorcode="CampaignServiceLabelIdsBatchLimitExceeded " code="5426" message="The maximum number of label ids per request is exceeded."/>

      <!--Campaign Languages (range = 5500 to 5599 ) -->
      <error name="DuplicateHeaders" errorcode="DuplicateHeaders" code="5500" message="Price tables rows of a price ad extension cannot contain duplicate header values."/>
      <error name="TooFewPriceTableRows" errorcode="TooFewPriceTableRows" code="5501" message="Price Ad Extension must have at least 3 price table rows."/>
      <error name="NegativePrice" errorcode="NegativePrice" code="5502" message="Cannot set negative price amount in price table row."/>
      <error name="CurrencyCodeNotSupported" errorcode="CurrencyCodeNotSupported" code="5503" message="The currency is not supported."/>

      <!--Offline Conversion (range = 5600 to 5699 ) -->
      <error name="OfflineConversionsNullOrEmpty" errorcode="OfflineConversionsNullOrEmpty" code="5600" message="The list of offline conversions cannot be null or empty."/>
      <error name="OfflineConversionsLimitExceeded" errorcode="OfflineConversionsLimitExceeded" code="5601" message="The list of offline conversions exceeds the limit."/>
      <error name="OfflineConversionIsNull" errorcode="OfflineConversionIsNull" code="5602" message="The offline conversion cannot be null."/>
      <error name="OfflineConversionMicrosoftClickIdNullOrEmpty" errorcode="OfflineConversionMicrosoftClickIdNullOrEmpty" code="5603" message="The Microsoft Click Id of offline conversion cannot be null or empty."/>
      <error name="OfflineConversionMicrosoftClickIdInvalid" errorcode="OfflineConversionMicrosoftClickIdInvalid" code="5604" message="The  Microsoft Click Id of offline conversion is invalid."/>
      <error name="OfflineConversionNameNullOrEmpty" errorcode="OfflineConversionNameNullOrEmpty" code="5605" message="The conversion name of offline conversion cannot be null or empty."/>
      <error name="OfflineConversionNameInvalid" errorcode="OfflineConversionNameInvalid" code="5606" message="The conversion name do not match an existing offline conversion goal."/>
      <error name="OfflineConversionTimeInvalid" errorcode="OfflineConversionTimeInvalid" code="5607" message="The conversion time of offline conversion is invalid."/>
      <error name="OfflineConversionTimeNullOrEmpty" errorcode="OfflineConversionTimeNullOrEmpty" code="5608" message="The conversion time of offline conversion cannot be null or empty ."/>
      <error name="OfflineConversionTimeOutOfWindow" errorcode="OfflineConversionTimeOutOfWindow" code="5609" message="A conversion time must have occurred within the past 90 days."/>
      <error name="OfflineConversionValueInvalid" errorcode="OfflineConversionValueInvalid" code="5610" message="The conversion value of offline conversion is invalid."/>
      <error name="OfflineConversionCurrencyCodeInvalid" errorcode="OfflineConversionCurrencyCodeInvalid" code="5611" message="The currency code of offline conversion is invalid."/>
      <error name="OfflineConversionsApplyFailed" errorcode="OfflineConversionsApplyFailed" code="5612" message="Failed to send offline conversions to internal server."/>
      <error name="FutureConversionTimeCannotBeSet" errorcode="FutureConversionTimeCannotBeSet" code="5614" message="A future conversion time cannot be set."/>
      <error name="OfflineConversionNotAcceptedForGoal" errorcode="OfflineConversionNotAcceptedForGoal" code="5615" message="Offline conversions cannot be accepted for the conversion goal at this time. You must wait 2 hours after the goal was created before uploading offline conversions."/>
      <error name="ConversionTimeEarlierThanClickTime" errorcode="ConversionTimeEarlierThanClickTime" code="5616" message="The conversion time must be later than the click time."/>
      <error name="ClickIdDateTimeOutsideGoalConversionWindow" errorcode="ClickIdDateTimeOutsideGoalConversionWindow" code="5617" message="The date and time for the click ID is outside the conversion window for this goal."/>
      <error name="OfflineConversionInvalidAdjustmentType" errorcode="OfflineConversionInvalidAdjustmentType" code="5618" message="The offline conversion adjustment type is invalid"/>
      <error name="OfflineConversionAdjustmentTimeNullOrEmpty" errorcode="OfflineConversionAdjustmentTimeNullOrEmpty" code="5619" message="The offline conversion adjustment time is empty"/>
      <error name="OfflineConversionAdjustmentTimeEarlierThanConversionTime" errorcode="OfflineConversionAdjustmentTimeEarlierThanConversionTime" code="5620" message="The offline conversion adjustment time is earlier than conversion time"/>
      <error name="OfflineConversionAdjustmentTimeInvalid" errorcode="OfflineConversionAdjustmentTimeInvalid" code="5621" message="The offline conversion adjustment time is invalid"/>
      <error name="OfflineConversionFutureAdjustmentTimeCannotBeSet" errorcode="OfflineConversionFutureAdjustmentTimeCannotBeSet" code="5622" message="The offline conversion adjustment time cannot be in the future"/>
      <error name="OfflineConversionAdjustmentTimeOutOfWindow" errorcode="OfflineConversionAdjustmentTimeOutOfWindow" code="5623" message="The offline conversion adjustment time is out of the window"/>
      <error name="OfflineConversionAdjustmentValueNotExpected" errorcode="OfflineConversionAdjustmentValueNotExpected" code="5624" message="The offline conversion adjustment value is not expected"/>
      <error name="OfflineConversionAdjustmentValueRequired" errorcode="OfflineConversionAdjustmentValueRequired" code="5625" message="The offline conversion adjustment value is required"/>
      <error name="OfflineConversionCurrencyValueRequired" errorcode="OfflineConversionCurrencyValueRequired" code="5626" message="The offline conversion currency value is required"/>
      <error name="OfflineConversionRestateRetractNotSupported" errorcode="OfflineConversionRestateRetractNotSupported" code="5627" message="The offline conversion restate or retract operation is not supported"/>
	    <error name="ScheduledOfflineConversionUploadUnableToFetchFile" errorcode="ScheduledOfflineConversionUploadUnableToFetchFile" code="5628" message="The Scheduled Offline Conversion pilot must be enabled when creating a Scheduled task"/>
      <error name="OfflineConversionAdditionColumnsNotExpectedInHeader" errorcode="OfflineConversionAdditionColumnsNotExpectedInHeader" code="5629" message="The Offline Conversion addition columns are not expected in an Adjustment row"/>
      <error name="OfflineConversionAdjustmentColumnsNotExpectedInHeader" errorcode="OfflineConversionAdjustmentColumnsNotExpectedInHeader" code="5630" message="The Offline Conversion adjustment columns are not expected in an Adjustment row"/>
      <error name="ExternalAttributionRequiredFieldEmpty " errorcode="ExternalAttributionRequiredFieldEmpty " code="65426" message="The Offline Conversion external attribution fields are invalid."/>
      <error name="ExternalAttributionModelTooLong" errorcode="ExternalAttributionModelTooLong" code="65427" message="The Offline Conversion ExternalAttributionModel value cannot be longer than 100 characters."/>
      <error name="ExternalAttributionCreditValueInvalid" errorcode="ExternalAttributionCreditValueInvalid" code="65428" message="The Offline Conversion ExternalAttributionCredit should be between 0 and 1."/>
      <error name="GoalNotEligibleForExternalAttribution" errorcode="GoalNotEligibleForExternalAttribution" code="65429" message="This Offline Conversion goal is not eligible for external attribution"/>
      <error name="ShouldAcceptTermsBeforeUsingEnhancedConversions" errorcode="ShouldAcceptTermsBeforeUsingEnhancedConversions" code="44944" message="Please accept the terms of Enhanced Conversions on the UI first."/>      
      <error name="NotEligibleForEnhancedConversions" errorcode="NotEligibleForEnhancedConversions" code="44945" message="Enhanced conversion is not eligible for this goal."/>      <error name="ConversionEmailAddressIsNotHashed" errorcode="ConversionEmailAddressIsNotHashed" code="44946" message="The conversion email address value is not hashed."/>
      <error name="ConversionPhoneNumberIsNotHashed" errorcode="ConversionPhoneNumberIsNotHashed" code="44947" message="The conversion phone number value is not hashed."/>
      <error name="OnlineConversionIsNull" errorcode="OnlineConversionIsNull" code="5650" message="The online conversion adjustment cannot be null."/>
      <error name="OnlineConversionAdjustmentValueInvalid" errorcode="OnlineConversionAdjustmentValueInvalid" code="5651" message="The online conversion adjustment value is invalid."/>
      <error name="OnlineConversionCurrencyCodeInvalid" errorcode="OnlineConversionCurrencyCodeInvalid" code="5652" message="The online conversion adjustment currency value is invalid."/>
      <error name="OnlineConversionAdjustmentTimeInvalid" errorcode="OnlineConversionAdjustmentTimeInvalid" code="5653" message="The online conversion adjustment time is invalid."/>
      <error name="OnlineConversionFutureAdjustmentTimeCannotBeSet" errorcode="OnlineConversionFutureAdjustmentTimeCannotBeSet " code="5654" message="The online conversion adjustment time cannot be in the future."/>
      <error name="OnlineConversionsNullOrEmpty" errorcode="OnlineConversionsNullOrEmpty" code="5655" message="The list of online conversion adjustments cannot be null or empty."/>
      <error name="OnlineConversionBatchSizeExceedsLimit" errorcode="OnlineConversionBatchSizeExceedsLimit" code="5656" message="The list of online conversion adjustments exceeds the limit."/>
      <error name="OnlineConversionAdjustmentValueNotExpected" errorcode="OnlineConversionAdjustmentValueNotExpected" code="5657" message="The online conversion adjustment value is not expected."/>
      <error name="OnlineConversionAdjustmentValueRequired" errorcode="OnlineConversionAdjustmentValueRequired" code="5658" message="The online conversion adjustment value is required."/>
      <error name="OnlineConversionAdjustmentTypeIsNull" errorcode="OnlineConversionAdjustmentTypeIsNull" code="5659" message="The online conversion adjustment type cannot be empty."/>
      <error name="OnlineConversionHashedEmailAddressNotExpected" errorcode="OnlineConversionHashedEmailAddressNotExpected" code="45026" message="Invalid column HashedEmailAddress in online conversion file"/>
      <error name="OnlineConversionHashedPhoneNumberNotExpected" errorcode="OnlineConversionHashedPhoneNumberNotExpected" code="45027" message="Invalid column HashedPhoneNumber in online conversion file"/>
      <error name="OnlineConversionInvalidAdjustmentType" errorcode="OnlineConversionInvalidAdjustmentType" code="5660" message="The online conversion adjustment type is invalid."/>
      <error name="OnlineConversionNameNullOrEmpty" errorcode="OnlineConversionNameNullOrEmpty" code="5661" message="The conversion name is empty."/>
      <error name="TransactionIdIsNull" errorcode="TransactionIdIsNull" code="5662" message="The transaction ID is empty."/>
      <error name="TransactionIdIsInvalid" errorcode="TransactionIdIsInvalid" code="5663" message="The transaction ID is invalid."/>
      <error name="OnlineConversionInvalidTimeZone" errorcode="OnlineConversionInvalidTimeZone " code="5664" message="The time zone is invalid."/>
      <error name="OnlineConversionNotEnabled" errorcode="OnlineConversionNotEnabled" code="5665" message="The feature Online Conversion Restate/Retract is not enabled."/>
      <error name="OnlineConversionNameNotFound" errorcode="OnlineConversionNameNotFound" code="5666" message="The conversion name do not match any existing online conversion goal."/>
      <error name="SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal" errorcode="SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal" code="5667" message="Can not have other goals that use same tag and category with an auto goal."/>
      <error name="InvalidEventParameterForAutoGoal" errorcode="InvalidEventParameterForAutoGoal" code="5668" message="Event goal parameter can not be assigned for auto goal."/>
      <error name="InvalidGoalTypeForAutoGoal" errorcode="InvalidGoalTypeForAutoGoal" code="5669" message="Only support event type for auto goal."/>
      <error name="IsAutoGoalFieldCannotBeChanged" errorcode="IsAutoGoalFieldCannotBeChanged" code="5670" message="Can not change isAutoGoal field."/>
      <error name="EventParameterNotAllowToChangeForAutoGoal" errorcode="EventParameterNotAllowToChangeForAutoGoal" code="5671" message="Can not change event parameter for auto goal." />
      <error name="GoalCategoryCannotBeChangedForAutoGoal" errorcode="GoalCategoryCannotBeChangedForAutoGoal" code="5672" message="Can not change goal category for auto goal." />
      <error name="MustHaveCategoryForAutoGoal" errorcode="MustHaveCategoryForAutoGoal" code="5673" message="Auto goal must have goal category." />
      <error name="TagNotAllowToChangeForAutoGoal" errorcode="TagNotAllowToChangeForAutoGoal" code="5674" message="Cannot change tag for auto goal." />

		  <!--AIM (range = 5700 to 5799 ) -->
      <error name="CustomerNotEligibleForAudienceCampaign" errorcode="CustomerNotEligibleForAudienceCampaign" code="5700" message="Customer Not Eligible For Audience Campaign."/>
      <error name="CampaignLanguageShouldIncludeAll" errorcode="CampaignLanguageShouldIncludeAll" code="5701" message="Campaign Languages should include All."/>
      <error name="AdGroupInvalidTargetSetting" errorcode="AdGroupInvalidTargetSetting" code="5702" message="The ad group target setting is invalid."/>
      <error name="NotSupportedForThisCampaignType" errorcode="NotSupportedForThisCampaignType" code="5703" message="Not Supported For This Campaign Type."/>
      <error name="AdGroupLanguageNotSupported" errorcode="AdGroupLanguageNotSupported" code="5704" message="Language for each ad group is not supported. You can set campaign languages."/>
      <error name="InvalidFunctionFormat" errorcode="InvalidFunctionFormat" code="5705" message="The syntax of your function contains invalid formatting, likely caused by a missing }."/>
      <error name="UnknownFunction" errorcode="UnknownFunction" code="5706" message="One or more functions are invalid or not supported."/>
      <error name="MissingDelimiterBetweenFunctions" errorcode="MissingDelimiterBetweenFunctions" code="5707" message="You need to have at least one character between any two functions."/>
      <error name="CountDownInvalidDateTime" errorcode="CountDownInvalidDateTime" code="5708" message="Your countdown function contains an invalid date and/or time."/>
      <error name="CountDownInvalidDaysBefore" errorcode="CountDownInvalidDaysBefore" code="5709" message="Your countdown function contains an invalid days-before value."/>
      <error name="CountDownDaysBeforeOutOfRange" errorcode="CountDownDaysBeforeOutOfRange" code="5710" message="The days-before value in your countdown function is out of range."/>
      <error name="CountDownPastDateTime" errorcode="CountDownPastDateTime" code="5711" message="Your countdown function contains a date and/or time in the past."/>
      <error name="CountDownInvalidDefaultText" errorcode="CountDownInvalidDefaultText" code="5712" message="Default value is not allowed in countdown function."/>
      <error name="CountDownInvalidLanguageCode" errorcode="CountDownInvalidLanguageCode" code="5713" message="Your countdown function contains an invalid language code."/>
      <error name="CountDownInvalidParameters" errorcode="CountDownInvalidParameters" code="5714" message="A countdown function must have at least one parameter and no more than three."/>
      <error name="InvalidCallToAction" errorcode="InvalidCallToAction" code="5715" message="The call to action is invalid."/>
      <error name="AdDisplayUrlDomainExtractionFailed" errorcode="AdDisplayUrlDomainExtractionFailed" code="5716" message="Extracting display url domain from final urls failed."/>
      <error name="AdModificationNotAllowedOnThisCampaign" errorcode="AdModificationNotAllowedOnThisCampaign" code="5717" message="Ad modification is not allowed on this campaign."/>
      <error name="InvalidProfileType" errorcode="InvalidProfileType" code="5718" message="This is not a valid ProfileType."/>
      <error name="CustomerNotEligibleForProductAudience" errorcode="CustomerNotEligibleForProductAudience" code="5719" message="Customer Not Eligible For Product Audience."/>
      <error name="CustomerNotEligibleForEnhancedResponsiveAd" errorcode="CustomerNotEligibleForEnhancedResponsiveAd" code="5720" message="Customer Not Eligible For Enhanced Responsive Ad."/>
      <error name="ResponsiveAdInvalidImage" errorcode="ResponsiveAdInvalidImage" code="5721" message="Image is invalid for Responsive Ad"/>
      <error name="ResponsiveAdDuplicateImage" errorcode="ResponsiveAdDuplicateImage" code="5722" message="Image is duplicate for Responsive Ad"/>
      <error name="ResponsiveAdRequiredImageMissing" errorcode="ResponsiveAdRequiredImageMissing" code="5723" message="Image required by Responsive Ad is missing."/>
      <error name="AIMCampaignLevelAudienceTargetingNotEnabled" errorcode="AIMCampaignLevelAudienceTargetingNotEnabled" code="5724" message="Audience campaign is not eligible to create campaign-level audience association."/>
      <error name="NotEligibleForAudienceCampaignSubType" errorcode="NotEligibleForAudienceCampaignSubType" code="5725" message="Account not in Pilot to use this sub type of AIM campaign" />
      <error name="LocationFunctionInvalidParameters" errorcode="LocationFunctionInvalidParameters" code="5726" message="A location function must have exactly one location level parameter."/>
      <error name="ResponsiveAdInvalidVideo" errorcode="ResponsiveAdInvalidVideo" code="5727" message="Video is invalid for Responsive Ad"/>
      <error name="ResponsiveAdDuplicateVideo" errorcode="ResponsiveAdDuplicateVideo" code="5728" message="Video is duplicate for Responsive Ad"/>
      <error name="ResponsiveAdRequiredVideoMissing" errorcode="ResponsiveAdRequiredVideoMissing" code="5729" message="Video required by Responsive Ad is missing."/>
      <error name="AccountNotEligibleForAutoBiddingForAudienceNetwork" errorcode="AccountNotEligibleForAutoBiddingForAudienceNetwork" code="5730" message="Account is not in pilot for auto bidding for audience network."/>
      <error name="IncludeAutoBiddingViewThroughConversionsValueInvalid" errorcode="IncludeAutoBiddingViewThroughConversionsValueInvalid" code="5731" message="Value for setting include auto bidding for view through conversions is invalid."/>
      <error name="BusinessAttributesValueInvalid" errorcode="BusinessAttributesValueInvalid" code="5732" message="The business attribute value is invalid."/>
      <error name="AccountNotEnabledForBusinessAttributes" errorcode="AccountNotEnabledForBusinessAttributes" code="5733" message="Account is not in pilot for business attributes."/>
      <error name="InvalidLeadGenSetting" errorcode="InvalidLeadGenSetting" code="5734" message="Lead Gen setting is invalid."/>
      <error name="CampaignLeadGenSettingIsImmutable" errorcode="CampaignLeadGenSettingIsImmutable" code="5740" message="Campaign Lead Gen setting is immutable."/>
      <error name="LeadGenCampaignOnlyAllowCPM" errorcode="LeadGenCampaignOnlyAllowCPM" code="5741" message="Lead Gen Campaign can only have CPM."/>
      <error name="AccountNotEligibleForOptOutFromMCM" errorcode="AccountNotEligibleForOptOutFromMCM" code="5743" message="Account is not in pilot for out out from MCM."/>
      <error name="OptOutFromMCMValueInvalid" errorcode="OptOutFromMCMValueInvalid" code="5744" message="Invalid OptOutFromMCM value."/>
      <error name="AccountNotEligibleForBoostTargeting" errorcode="AccountNotEligibleForBoostTargeting" code="5745" message="Account is not in pilot for out out from Boost Targeting."/>
      <error name="InvalidBoostPlacementSetting" errorcode="InvalidBoostPlacementSetting" code="5746" message="Invalid Boost Placement setting."/>
      <error name="EligibleTargetNeededForOptimizedTargetingOptout" errorcode="EligibleTargetNeededForOptimizedTargetingOptout" code="5747" message="Not allowed to turn off OptimizedTargeting when adgroup has no targets."/>
      <error name="AccountNotEligibleForFrequencyCap" errorcode="AccountNotEligibleForFrequencyCap" code="5748" message="Account is not in pilot for setting FrequencyCap."/>
      <error name="InvalidCampaignSubtypeForFrequencyCap" errorcode="InvalidCampaignSubtypeForFrequencyCap" code="5749" message="Invalid campaign subtype for FrequencyCap."/>
      <error name="InvalidFrequencyCapSettings" errorcode="InvalidFrequencyCapSettings" code="5750" message="Invalid FrequencyCap settings"/>
      <error name="UnsupportedSettingInBrandAwarenessVideoAds" errorcode="UnsupportedSettingInBrandAwarenessVideoAds" code="5751" message="Unsupported setting in brand awareness video ads."/>
      <error name="InvalidBitRate" errorcode="InvalidBitRate" code="5752" message="Unsupported BitRate in video ads."/>
      <error name="ResponsiveAdVideoInvalidStatus" errorcode="ResponsiveAdVideoInvalidStatus" code="5753" message="The status of the video is invalid."/>
      <error name="ResponsiveAdVideoWidthTooSmall" errorcode="ResponsiveAdVideoWidthTooSmall" code="5754" message="The width of the video is too small."/>
      <error name="ResponsiveAdVideoHeightTooSmall" errorcode="ResponsiveAdVideoHeightTooSmall" code="5755" message="The height of the video is too small."/>
      <error name="ResponsiveAdVideoInvalidAspectRatio" errorcode="ResponsiveAdVideoInvalidAspectRatio" code="5756" message="The aspect ratio of the video is invalid."/>
      <error name="ResponsiveAdVideoInvalidDuration" errorcode="ResponsiveAdVideoInvalidDuration" code="5757" message="The duration of the video is invalid."/>
      <error name="ResponsiveAdVideoBitRateTooSmall" errorcode="ResponsiveAdVideoBitRateTooSmall" code="5758" message="The bit rate of the video is too small."/>
      <error name="ResponsiveAdVideoSourceLengthTooLarge" errorcode="ResponsiveAdVideoSourceLengthTooLarge" code="5759" message="The source length of the video is too large."/>
      <error name="ResponsiveAdVideoUnsupportedFileFormat" errorcode="ResponsiveAdVideoUnsupportedFileFormat" code="5760" message="The file format of the video is unsupported."/>
      <error name="InvalidCriterionBidAdjustmentValue" errorcode="InvalidCriterionBidAdjustmentValue" code="5761" message="The value of target bid adjustment is not valid."/>
      <error name="OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds" errorcode="OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds" code="5762" message="The optimized targeting is not eligible for BrandAwarenessVideoAds campaign subtype."/>
      <error name="InvalidDealIdTarget" errorcode="InvalidDealIdTarget" code="5763" message="The DealId is invalid"/>
      <error name="UnsupportedSettingInDisplayAds" errorcode="UnsupportedSettingInDisplayAds" code="5764" message="Unsupported setting in display ads."/>
      <error name="AccountNotEnabledForDisplayCampaign" errorcode="AccountNotEnabledForDisplayCampaign" code="5765" message="Account not enabled for Display campaign."/>
      <error name="UnsupportedBiddingSchemeForDeal" errorcode="UnsupportedBiddingSchemeForDeal" code="5766" message="only CPM bidding Schema is supported for Deal now."/>
      <error name="UnsupportedAgeTargetForDeal" errorcode="UnsupportedAgeTargetForDeal" code="5767" message="Unsupported Age Target For Deal Requriement."/>
      <error name="UnsupportedGenderTargetForDeal" errorcode="UnsupportedGenderTargetForDeal" code="5768" message="Unsupported Gender Target For Deal Requriement."/>
      <error name="UnsupportedLocationTargetForDeal" errorcode="UnsupportedLocationTargetForDeal" code="5769" message="Unsupported Location Target For Deal Requriement."/>
      <error name="UnsupportedDeviceTargetForDeal" errorcode="UnsupportedDeviceTargetForDeal" code="5770" message="Unsupported Device Target For Deal Requriement."/>
      <error name="AdgroupBidAmountLessThanDealAskPrice" errorcode="AdgroupBidAmountLessThanDealAskPrice" code="5771" message="Adgroup BidAmount is  less than Deal AskPrice."/>
      <error name="InvalidAdQualityForDeal" errorcode="InvalidAdQualityForDeal" code="5772" message="Invalid AdQuality For Deal Video Requriement."/>
      <error name="VerifiedTrackingSettingsIsNotAllowed" errorcode="VerifiedTrackingSettingsIsNotAllowed" code="5773" message="VerifiedTrackingSettings is not allowed."/>
      <error name="EmptyVerifiedTrackingSettings" errorcode="EmptyVerifiedTrackingSettings" code="5774" message="Empty VerifiedTrackingSettings."/>
      <error name="TooManyItemsInVerifiedTrackingSettings" errorcode="TooManyItemsInVerifiedTrackingSettings" code="5775" message="Too many items in VerifiedTrackingSettings."/>
      <error name="VerifiedTrackingSettingInvalidFormat" errorcode="VerifiedTrackingSettingInvalidFormat" code="5776" message="VerifiedTrackingSetting Invalid Format."/>
        <error name="VideoDurationIsInvalidForDeal" errorcode="VideoDurationIsInvalidForDeal" code="5777" message="Video Duration Is Invalid For Deal."/>
      <error name="InvalidGenreIdTarget" errorcode="InvalidGenreIdTarget" code="5778" message="The GenreId is invalid"/>
      <error name="AgeCriterionCannotBeEmptyForBrandAwarenessCampaign" errorcode="AgeCriterionCannotBeEmptyForBrandAwarenessCampaign" code="5779" message="Age Criterion Cannot Be Empty For BrandAwarenessCampaign"/>
      <error name="GenderCriterionCannotBeEmptyForBrandAwarenessCampaign" errorcode="GenderCriterionCannotBeEmptyForBrandAwarenessCampaign" code="5780" message="Gender Criterion Cannot Be Empty For BrandAwarenessCampaign"/>
      <error name="DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign" errorcode="DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign" code="5781" message="Gender Criterion Cannot Be Empty For BrandAwarenessCampaign"/>
      <error name="JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay" errorcode="JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay" code="5782" message="Job And CompanyIMA Cannot Be Used By VideoAds And Display"/>
      <error name="ImpressionAudienceCannotBeUsedByDisplay" errorcode="ImpressionAudienceCannotBeUsedByDisplay" code="5783" message="Impression Audience Cannot Be Used By Display"/>
      <error name="ImpressionAudienceCannotBeUsedByOLV" errorcode="ImpressionAudienceCannotBeUsedByOLV" code="5784" message="Impression Audience Cannot Be Used By OLV"/>
      <error name="ImpressionAudienceCannotBeUsedByCTV" errorcode="ImpressionAudienceCannotBeUsedByCTV" code="5785" message="Impression Audience Cannot Be Used By CTV"/>
      <error name="AccountNotEnabledForMultiFormatAds" errorcode="AccountNotEnabledForMultiFormatAds" code="5786" message="Account not enabled for Multi-Format Ads cannot create Display or Video ads"/>
      <error name="AdSubTypeCannotBeChanged" errorcode="AdSubTypeCannotBeChanged" code="5787" message="AdSubType cannot be changed after created"/>  
      <error name="DealCampaignIsImmutable" errorcode="DealCampaignIsImmutable" code="5788" message="Deal Campaign Cannot Be changed"/>  
      <error name="EntityOnlySupportForDealCampaign" errorcode="EntityOnlySupportForDealCampaign" code="5789" message="IsDealCampaign Bit Mask Only Support For Deal Campaign"/>
      <error name="ShouldAssociateDealBeforeAddAdGroup" errorcode="ShouldAssociateDealBeforeAddAdGroup" code="5790" message="Deal Campaign must associate deals before attach other sub entity"/>
      <error name="NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign" errorcode="NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign" code="5791" message="Non-Audience Campaign Impression-Based Remarketing List Cannot Be Used by Non-Audience Campaign"/>
        <error name="UnsupportedSettingInMultiFormatAds" errorcode="UnsupportedSettingInMultiFormatAds" code="5792" message="Unsupported setting in multi format ads."/>
        <error name="NotEnabledForHTML5Asset" errorcode="NotEnabledForHTML5Asset" code="5793" message="Not Enabled For HTML5 Asset."/>
         
      <!--Hotel Ads (range = 5800 to 5899) -->
      <error name="CustomerNotEligibleForHotelAds" errorcode="CustomerNotEligibleForHotelAds" code="5800" message="Customer is not eligible for hotel ads."/>
      <error name="TooManyValues" errorcode="TooManyValues" code="5801" message="There are too many values in request."/>
      <error name="DuplicateValues" errorcode="DuplicateValues" code="5802" message="Duplicate values in request."/>
      <error name="InvalidBidMultiplier" errorcode="InvalidBidMultiplier" code="5803" message="The bid multiplier is invalid."/>
      <error name="ImmutableProperty" errorcode="ImmutableProperty" code="5804" message="The property is immutable."/>
      <error name="InvalidSubAccount" errorcode="InvalidSubAccount" code="5805" message="The sub account is invalid."/>
      <error name="MaxActiveSubAccountsLimitReached" errorcode="MaxActiveSubAccountsLimitReached" code="5806" message="The max active sub account count has reached limit."/>
      <error name="NoDefaultHotelGroupExists" errorcode="NoDefaultHotelGroupExists" code="5807" message="No default hotel group in sub account."/>
      <error name="DefaultHotelGroupUpdateNotAllowed" errorcode="DefaultHotelGroupUpdateNotAllowed" code="5808" message="Default hotel group not allow to update."/>
      <error name="UnsupportedAssociationType" errorcode="UnsupportedAssociationType" code="5809" message="The association type does not be supported."/>
      <error name="DuplicateHotelId" errorcode="DuplicateHotelId" code="5810" message="There are duplicate hotel id."/>
      <error name="HotelGroupHasActiveAssociations" errorcode="HotelGroupHasActiveAssociations" code="5811" message="Cannot delete hotel group with active associations."/>
      <error name="InvalidHotelGroup" errorcode="InvalidHotelGroup" code="5812" message="The hotel group is invalid."/>
      <error name="HotelCampaignNotEnabledForAccount" errorcode="HotelCampaignNotEnabledForAccount" code="5814" message="Customer is not enabled for hotel campaigns."/>
      <error name="MaxPercentCpcLessThanOrEqualToZero" errorcode="MaxPercentCpcLessThanOrEqualToZero" code="5815" message="MaxPercentCpc must be greater than 0."/>
      <error name="MaxPercentCpcGreaterThanOneThousand" errorcode="MaxPercentCpcGreaterThanOneThousand" code="5816" message="MaxPercentCpc must not be greater than 1000."/>
      <error name="CommissionRateIsRequired" errorcode="CommissionRateIsRequired" code="5817" message="CommissionRate is required for Commission Bidding Scheme."/>
      <error name="CommissionRateLessThanOrEqualToZero" errorcode="CommissionRateLessThanOrEqualToZero" code="5818" message="CommissionRate must be greater than 0."/>
      <error name="CommissionRateGreaterThanOneHundred" errorcode="CommissionRateGreaterThanOneHundred" code="5819" message="CommissionRate must not be greater than 100."/>
      <error name="SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed" errorcode="SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed" code="5820" message="Switching of Bid Type from PPS to Non PPS is not allowed."/>
      <error name="OnlySupportedBMValueIsDecreaseby100OrIncreaseby0" errorcode="OnlySupportedBMValueIsDecreaseby100OrIncreaseby0" code="5821" message="For PPS only Supported values for BM are increase by 0% or decrease by 100%"/>
      <error name="MissingPercentCpcBiddingScheme" errorcode="MissingPercentCpcBiddingScheme" code="5822" message="PercentCpcBid must be set for applible AdGroups when adding."/>
      <error name="InvalidHotelSetting" errorcode="InvalidHotelSetting" code="5823" message="HotelAdGroupType must be set to HPA, PPA, or both"/>
      <!--Hotel V2 -->
      <error name="InvalidAdvanceBookingWindowTarget" errorcode="InvalidAdvanceBookingWindowTarget" code="5824" message="Invalid Advance Booking Window Target"/>
      <error name="InvalidCheckInDayTarget" errorcode="InvalidCheckInDayTarget" code="5825" message="Invalid Check In Day Target"/>
      <error name="InvalidLengthOfStayTarget" errorcode="InvalidLengthOfStayTarget" code="5826" message="Invalid Length Of Stay Target"/>
      <error name="InvalidDateSelectionTypeTarget" errorcode="InvalidDateSelectionTypeTarget" code="5827" message="Invalid Date Selection Type Target"/>
      <error name="InvalidCheckInDateTarget" errorcode="InvalidCheckInDateTarget" code="5828" message="Invalid Check In Date Target"/>
      <error name="InvalidAdvanceBookingWindowTargetBidAdjustment" errorcode="InvalidAdvanceBookingWindowTargetBidAdjustment" code="5829" message="Invalid Advance Booking Window Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out."/>
      <error name="InvalidCheckInDayTargetBidAdjustment" errorcode="InvalidCheckInDayTargetBidAdjustment" code="5830" message="Invalid Check In Day Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out."/>
      <error name="InvalidLengthOfStayTargetBidAdjustment" errorcode="InvalidLengthOfStayTargetBidAdjustment" code="5831" message="Invalid Length Of Stay Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out."/>
      <error name="InvalidDateSelectionTypeTargetBidAdjustment" errorcode="InvalidDateSelectionTypeTargetBidAdjustment" code="5832" message="Invalid Date Selection Type Target Bid Adjustment. Value must be between -50 and 100."/>
      <error name="InvalidCheckInDateTargetBidAdjustment" errorcode="InvalidCheckInDateTargetBidAdjustment" code="5833" message="Invalid Check In Date Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out"/>
      <error name="AdvanceBookingWindowTargetConflict" errorcode="AdvanceBookingWindowTargetConflict" code="5834" message="Conflict with Advance Booking Window Targets. Targets must not overlap with each other"/>
      <error name="LengthOfStayTargetConflict" errorcode="LengthOfStayTargetConflict" code="5835" message="Conflict with Length of Stay Targets. Targets must not overlap with each other"/>
      <error name="CheckInDateTargetConflict" errorcode="CheckInDateTargetConflict" code="5836" message="Conflict with Check In Date Targets. Targets must not overlap with each other"/>
      <error name="EntityNotAllowedForHotelCampaign" errorcode="CampaignServiceEntityNotAllowedForHotelCampaign" code="5837" message="You cannot add this entity to a campaign of type Hotel."/>
      <error name="CannotSetBiddingSchemeForHotelAdGroup" errorcode="CannotSetBiddingSchemeForHotelAdGroup" code="5838" message="You cannot change the bidding scheme for hotel adgroups."/>
      <error name="HotelSettingCanNotBeChanged" errorcode="HotelSettingCanNotBeChanged" code="5839" message="Hotel setting can not be changed after hotel adgroup creation"/>
      <error name ="InvalidBidTypeForCampaignBiddingScheme" errorcode="InvalidBidTypeForCampaignBiddingScheme" code="5840" message="Invalid bid type for campaign bidding schema." />
      <error name ="CampaignIsNotOfTypeHotel" errorcode="CampaignIsNotOfTypeHotel" code="5841" message="Campaign is not of hotel campaign type." />
      <error name ="InvalidHotelListingType" errorcode="InvalidHotelListingType" code="5842" message="Invalid hotelListing type." />
      <error name ="AdGroupCriterionHotelListingIsNull" errorcode="AdGroupCriterionHotelListingIsNull" code="5843" message="HotelListing in adGroupCriterion is null." />
      <error name ="InvalidHotelListingOperand" errorcode="InvalidHotelListingOperand" code="5844" message="Invalid hotelListing operand." />
      <error name ="InvalidHotelListingAttribute" errorcode="InvalidHotelListingAttribute" code="5845" message="Invalid hotelListing attribute." />
      <error name ="FinalUrlAndMobileUrlNotAllowedForHotelGroup" errorcode="FinalUrlAndMobileUrlNotAllowedForHotelGroup" code="5846" message="FinalUrl and mobileUrl not allowed for hotel group." />
      <error name ="DuplicateRootNodeForHotelGroupTree" errorcode="DuplicateRootNodeForHotelGroupTree" code="5847" message="Duplicate root node for hotel group tree." />
      <error name ="ParentHotelGroupNodeDoesNotExist" errorcode="ParentHotelGroupNodeDoesNotExist" code="5848" message="Parent hotel group node does not exist." />
      <error name ="HeightOfHotelGroupTreeExceeededLimit" errorcode="HeightOfHotelGroupTreeExceeededLimit" code="5849" message="Height of hotel group tree exceeeded limit." />
      <error name ="HotelListingOperandUnderSubDivisionMustBeSame" errorcode="HotelListingOperandUnderSubDivisionMustBeSame" code="5850" message="HotelListing operand under same sub division must be same." />
      <error name ="DuplicateHotelListing" errorcode="DuplicateHotelListing" code="5851" message="Duplicate hotelListing." />
      <error name ="InvalidHotelGroupHierarchy" errorcode="InvalidHotelGroupHierarchy" code="5852" message="Invalid hotel group hierarchy." />
      <error name ="HotelGroupLimitExceededForAdGroup" errorcode="HotelGroupLimitExceededForAdGroup" code="5853" message="Hotel groups limit exceeded for adGroup." />
      <error name ="InvalidAdGroupCriterionRateBidValue" errorcode="InvalidAdGroupCriterionRateBidValue" code="5854" message="Invalid adGroup criterion rate bid value" />
      <error name ="HotelGroupEverythingElseMissing" errorcode="HotelGroupEverythingElseMissing" code="5855" message="Hotel group tree everything else node missing." />
      <error name ="InvalidLocationNodeInvalidParentLocation" errorcode="InvalidLocationNodeInvalidParentLocation" code="5856" message="Hotel group tree location node parent location invalid." />
      <error name ="InvalidTargetTypeForCommisionBiddingScheme" errorcode="InvalidTargetTypeForCommisionBiddingScheme" code="5857" message="Invalid target type for commission bidding scheme" />	
        
		  <!-- Feed Management (range = 5900 to 6100) -->
      <error name="CustomerNotEligibleForDsaPageFeed" errorcode="CustomerNotEligibleForDsaPageFeed" code="5900" message="Customer is not eligible for DSA page feed."/>
      <error name="CustomerNotEligibleForFeedService" errorcode="CustomerNotEligibleForFeedService" code="5901" message="Customer is not eligible for feed service."/>
      <error name="DuplicateFeedAssociation" errorcode="DuplicateFeedAssociation" code="5902" message="Duplicate feed associations are not allowed."/>
      <error name="FeedAssociationDoesNotExist" errorcode="FeedAssociationDoesNotExist" code="5903" message="The feed association does not exist."/>
      <error name="InvalidFeedId" errorcode="InvalidFeedId" code="5904" message="The feed identifier is invalid."/>
      <error name="FeedAssociationLimitationReached" errorcode="FeedAssociationLimitationReached" code="5905" message="The feed association count has reached limit."/>
      <error name="InvalidFeedCustomAttributesDefinitionText" errorcode="InvalidFeedCustomAttributesDefinitionText" code="5906" message="The definition text of feed attributes is invalid."/>
      <error name="FeedTypeNotSupportedForBulkUpload" errorcode="FeedTypeNotSupportedForBulkUpload" code="5907" message="The feed type is not supported for bulk upload."/>
      <error name="DualFeedNotSupported" errorcode="DualFeedNotSupported" code="5908" message="Dual feed type is currently not supported."/>
      <error name="DuplicateFeedId" errorcode="DuplicateFeedId" code="5909" message="Duplicate Feed Id."/>
      <error name="InvalidFeedAttribute" errorcode="InvalidFeedAttribute" code="5911" message="Invalid Feed Attribute."/>
      <error name="DuplicateFeedAttributeName" errorcode="DuplicateFeedAttributeName" code="5912" message="Duplicate Feed Attribute Name."/>
      <error name="InvalidFeedAttributeType" errorcode="InvalidFeedAttributeType" code="5913" message="Invalid Feed Attribute Type."/>
      <error name="ScheduleNotAllowedForFeedType" errorcode="ScheduleNotAllowedForFeedType" code="5914" message="Schedule Not Allowed For Feed Type."/>
      <error name="UrlNotAllowedForFeedType" errorcode="UrlNotAllowedForFeedType" code="5915" message="Url Not Allowed For Feed Type."/>
      <error name="InvalidFeedAttributeMapping" errorcode="InvalidFeedAttributeMapping" code="5916" message="Invalid Feed Attribute Mapping."/>
      <error name="DuplicateFeedPropertyId" errorcode="DuplicateFeedPropertyId" code="5918" message="Duplicate Feed Property Id."/>
      <error name="InvalidFeedType" errorcode="InvalidFeedType" code="5919" message="Invalid Feed Type."/>
      <error name="CustomerNotEligibleForAdCustomizersFeed" errorcode="CustomerNotEligibleForAdCustomizersFeed" code="5920" message="Customer is not eligible for Ad Customizer Feed."/>
      <error name="TargetFeedStatusInvalid" errorcode="TargetFeedStatusInvalid" code="5921" message="Target Feed Status Invalid."/>
      <error name="TargetFeedInvalid" errorcode="TargetFeedInvalid" code="5922" message="Target Feed Invalid."/>
      <error name="InvalidFeedItemAttributeValue" errorcode="InvalidFeedItemAttributeValue" code="5923" message="Invalid FeedItem Attribute Value."/>
      <error name="InvalidBooleanFeedItemAttributeValue" errorcode="InvalidBooleanFeedItemAttributeValue" code="5924" message="Invalid Boolean Feed Item Attribute Value."/>
      <error name="InvalidInt64FeedItemAttributeValue" errorcode="InvalidInt64FeedItemAttributeValue" code="5925" message="Invalid Int64 FeedItem Attribute Value."/>
      <error name="InvalidDoubleFeedItemAttributeValue" errorcode="InvalidDoubleFeedItemAttributeValue" code="5926" message="Invalid Double FeedItem Attribute Value."/>
      <error name="InvalidStringFeedItemAttributeValue" errorcode="InvalidStringFeedItemAttributeValue" code="5927" message="Invalid String FeedItem Attribute Value."/>
      <error name="InvalidUrlFeedItemAttributeValue" errorcode="InvalidUrlFeedItemAttributeValue" code="5928" message="Invalid Url FeedItem Attribute Value."/>
      <error name="InvalidDateTimeFeedItemAttributeValue" errorcode="InvalidDateTimeFeedItemAttributeValue" code="5929" message="Invalid DateTime FeedItem Attribute Value."/>
      <error name="InvalidInt64ListFeedItemAttributeValue" errorcode="InvalidInt64ListFeedItemAttributeValue" code="5930" message="Invalid Int64 List FeedItem Attribute Value."/>
      <error name="InvalidDoubleListFeedItemAttributeValue" errorcode="InvalidDoubleListFeedItemAttributeValue" code="5931" message="Invalid Double List FeedItem Attribute Value."/>
      <error name="InvalidStringListFeedItemAttributeValue" errorcode="InvalidStringListFeedItemAttributeValue" code="5932" message="Invalid String List FeedItem Attribute Value."/>
      <error name="InvalidBooleanListFeedItemAttributeValue" errorcode="InvalidBooleanListFeedItemAttributeValue" code="5933" message="Invalid Boolean List FeedItem Attribute Value."/>
      <error name="InvalidUrlListFeedItemAttributeValue" errorcode="InvalidUrlListFeedItemAttributeValue" code="5934" message="Invalid Url List FeedItem Attribute Value."/>
      <error name="InvalidDateTimeListFeedItemAttributeValue" errorcode="InvalidDateTimeListFeedItemAttributeValue" code="5935" message="Invalid DateTime List FeedItem Attribute Value."/>
      <error name="InvalidPriceFeedItemAttributeValue" errorcode="InvalidPriceFeedItemAttributeValue" code="5936" message="Invalid Price FeedItem Attribute Value."/>
      <error name="DuplicateFeedName" errorcode="DuplicateFeedName" code="5937" message="Duplicate Feed Name."/>
      <error name="FeedItemMaxLimitReached" errorcode="FeedItemMaxLimitReached" code="5938" message="The limitation of FeedItem count reached."/>
      <error name="DuplicateFeedItemId" errorcode="DuplicateFeedItemId" code="5939" message="Duplicate Feed Item Id."/>
      <error name="InvalidFeedItemId" errorcode="InvalidFeedItemId" code="5941" message="Invalid FeedItem Id."/>
      <error name="AccountLevelFeedLimitationReached" errorcode="AccountLevelFeedLimitationReached" code="5942" message="Account Level Feed limitation reached."/>
      <error name="AttributeLimitationPerFeedReached" errorcode="AttributeLimitationPerFeedReached" code="5943" message="Attribute Limitation per Feed reached."/>
      <error name="InvalidPageFeedLabel" errorcode="InvalidPageFeedLabel" code="5944" message="Invalid PageFeed label."/>
      <error name="TooManyPageFeedLabels" errorcode="TooManyPageFeedLabels" code="5945" message="Too many PageFeed labels."/>
      <error name="PageFeedLabelTooLong" errorcode="PageFeedLabelTooLong" code="5946" message="PageFeed label too long."/>
      <error name="PageFeedUrlTooLong" errorcode="PageFeedUrlTooLong" code="5947" message="PageFeed url too long."/>
      <error name="InvalidPageFeedUrl" errorcode="InvalidPageFeedUrl" code="5948" message="Invalid PageFeed url."/>
      <error name="InvalidCustomIdAttributeValue" errorcode="InvalidCustomIdAttributeValue" code="5949" message="Invalid CustomId attribute value."/>
      <error name="InvalidDevicePreferenceAttributeValue" errorcode="InvalidDevicePreferenceAttributeValue" code="5950" message="Invalid DevicePreference Attribute Value."/>
      <error name="InvalidTargetAdGroupAttributeValue" errorcode="InvalidTargetAdGroupAttributeValue" code="5954" message="Invalid Target AdGroup Attribute Value."/>
      <error name="InvalidTargetAudienceIdAttributeValue" errorcode="InvalidTargetAudienceIdAttributeValue" code="5955" message="Invalid Target AudienceId Attribute Value."/>
      <error name="InvalidTargetCampaignAttributeValue" errorcode="InvalidTargetCampaignAttributeValue" code="5956" message="Invalid Target Campaign Attribute Value."/>
      <error name="InvalidTargetKeywordAttributeValue" errorcode="InvalidTargetKeywordAttributeValue" code="5957" message="Invalid Target Keyword Attribute Value."/>
      <error name="InvalidTargetKeywordMatchTypeAttributeValue" errorcode="InvalidTargetKeywordMatchTypeAttributeValue" code="5958" message="Invalid Target Keyword Match Type Attribute Value."/>
      <error name="InvalidTargetKeywordTextAttributeValue" errorcode="InvalidTargetKeywordTextAttributeValue" code="5959" message="Invalid Keyword Text Attribute Value."/>
      <error name="InvalidTargetLocationAttributeValue" errorcode="InvalidTargetLocationAttributeValue" code="5960" message="Invalid Target Location Attribute Value."/>
      <error name="InvalidTargetLocationRestrictionAttributeValue" errorcode="InvalidTargetLocationRestrictionAttributeValue" code="5961" message="Invalid Target Location Restriction Attribute Value."/>
      <error name="NestedParameterInCustomAttributeNotSupported" errorcode="NestedParameterInCustomAttributeNotSupported" code="5962" message="Nested parameter in FeedItem CustomAttribute is not supported."/>
      <error name="KeyFeedItemAttributeValueConfliction" errorcode="KeyFeedItemAttributeValueConfliction" code="5963" message="Duplicate Feed Item Row With Same Key Attributes"/>
      <error name="CannotUseStandardFeedAttributeName" errorcode="CannotUseStandardFeedAttributeName" code="5964" message="Feed Attribute cannot use the name of Standard attributes"/>
      <error name="CannotUseTargetingFeedAttributeName" errorcode="CannotUseTargetingFeedAttributeName" code="5965" message="Feed Attribute cannot use the name of Targeting attributes"/>
      <error name="KeyPropertyCannotBeUpdated" errorcode="KeyPropertyCannotBeUpdated" code="5966" message="The key property of a feed item cannot be updated"/>
      <error name="CannotUpdateFeedAttributeKey" errorcode="CannotUpdateFeedAttributeKey" code="5967" message="Feed Attribute Key cannot be updated"/>
      <error name="CustomIdAttributeShouldBeOfStringType" errorcode="CustomIdAttributeShouldBeOfStringType" code="5968" message="The Custom Id property should be of string type only."/>
      <error name="AttributeValueStringTooLong" errorcode="AttributeValueStringTooLong" code="5969" message="The string value is too long, please shorten to contain 90 characters or less."/>
      <error name="CustomerNotEligibleForDynamicDataFeed" errorcode="CustomerNotEligibleForDynamicDataFeed" code="5970" message="Customer is not eligible for Dynamic Data Feed"/>
      <error name="InvalidPageFeedAdTitle" errorcode="InvalidPageFeedAdTitle" code="5971" message="Invalid PageFeed Ad Title."/>
      <error name="PageFeedAdTitleTooLong" errorcode="PageFeedAdTitleTooLong" code="5972" message="PageFeed Ad Title too long."/>
      <error name="InvalidFeedIdsForAssociation" errorcode="InvalidFeedIdsForAssociation" code="5973" message="Invalid Feed Ids for Association"/>
      <error name="FeedItemCountExceedFeedTypeLevelLimitation" errorcode="FeedItemCountExceedFeedTypeLevelLimitation" code="5974" message="The limit on the maximum number of feed items of current feed type has been reached."/>
      <error name="AttributeValueLengthExceeded" errorcode="AttributeValueLengthExceeded" code="5975" message="The feed item contains one or more attribute values exceed maximum length limit."/>
      <error name="CustomAttributeValuesEmpty" errorcode="CustomAttributeValuesEmpty" code="5976" message="Provide a value for at least one of the custom attributes"/>
      <error name="TargetAdgroupWithoutTargetCampaign" errorcode="TargetAdgroupWithoutTargetCampaign" code="5977" message="Target Campaign needed for setting target adgroup"/>
      <error name="InvalidEndDateAttributeValue" errorcode="InvalidEndDateAttributeValue" code="5978" message="Invalid end date attribute value"/>
      <error name="InvalidSchedulingAttributeValue" errorcode="InvalidSchedulingAttributeValue" code="5979" message="Invalid scheduling attribute value"/>
      <error name="InvalidStartDateAttributeValue" errorcode="InvalidStartDateAttributeValue" code="5980" message="Invalid start date attribute value"/>
      <error name="InvalidTargetLocationIdAttributeValue" errorcode="InvalidTargetLocationIdAttributeValue" code="5981" message="Invalid target location id attribute value"/>
      <error name="InvalidFeedItemLifeCycleStatus" errorcode="InvalidFeedItemLifeCycleStatus" code="5982" message="Feed item status invalid"/>
      <error name="CustomerNotEligibleForDynamicDataAutosAggregateFeed" errorcode="CustomerNotEligibleForDynamicDataAutosAggregateFeed" code="5983" message="Customer is not eligible for Dynamic Data Autos Aggregate Feed"/>
      <error name="CustomerNotEligibleForDynamicDataAutosListingFeed" errorcode="CustomerNotEligibleForDynamicDataAutosListingFeed" code="5984" message="Customer is not eligible for Dynamic Data Autos Listing Feed"/>
      <error name="CustomerNotEligibleForDynamicDataCreditCardsFeed" errorcode="CustomerNotEligibleForDynamicDataCreditCardsFeed" code="5985" message="Customer is not eligible for Dynamic Data Credit Cards Feed"/>
      <error name="CustomerNotEligibleForDynamicDataCruisesFeed" errorcode="CustomerNotEligibleForDynamicDataCruisesFeed" code="5986" message="Customer is not eligible for Dynamic Data Cruises Feed"/>
      <error name="CustomerNotEligibleForDynamicDataCustomFeed" errorcode="CustomerNotEligibleForDynamicDataCustomFeed" code="5987" message="Customer is not eligible for Dynamic Data Custom Feed"/>
      <error name="CustomerNotEligibleForDynamicDataEventsFeed" errorcode="CustomerNotEligibleForDynamicDataEventsFeed" code="5988" message="Customer is not eligible for Dynamic Data Events Feed"/>
      <error name="CustomerNotEligibleForDynamicDataHealthInsuranceFeed" errorcode="CustomerNotEligibleForDynamicDataHealthInsuranceFeed" code="5989" message="Customer is not eligible for Dynamic Data Health Insurance Feed"/>
      <error name="CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed" errorcode="CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed" code="5990" message="Customer is not eligible for Dynamic Data Hotels And Vacation Rentals Feed"/>
      <error name="CustomerNotEligibleForDynamicDataMortgageLendersFeed" errorcode="CustomerNotEligibleForDynamicDataMortgageLendersFeed" code="5991" message="Customer is not eligible for Dynamic Data Mortgage Lenders Feed"/>
      <error name="CustomerNotEligibleForDynamicDataProfessionalServiceFeed" errorcode="CustomerNotEligibleForDynamicDataProfessionalServiceFeed" code="5992" message="Customer is not eligible for Dynamic Data Professional Service Feed"/>
      <error name="CustomerNotEligibleForDynamicDataToursAndActivitiesFeed" errorcode="CustomerNotEligibleForDynamicDataToursAndActivitiesFeed" code="5993" message="Customer is not eligible for Dynamic Data Tours And Activities Feed"/>
      <error name="CustomerNotEligibleForDynamicDataDebitCardsFeed" errorcode="CustomerNotEligibleForDynamicDataCreditCardsFeed" code="5994" message="Customer is not eligible for Dynamic Data Debit Cards Feed"/>
      <error name="CustomerNotEligibleForDynamicDataJobListingsFeed" errorcode="CustomerNotEligibleForDynamicDataJobListingsFeed" code="5995" message="Customer is not eligible for Dynamic Data Job Listings Feed"/>
      <error name="FeedReferencedInCampaign" errorcode="FeedReferencedInCampaign" code="5996" message="Feed is referenced in a campaign"/>
      <error name="FeedReferencedInAd" errorcode="FeedReferencedInAd" code="5997" message="Feed is referenced in an ad"/>
      <error name="PageFeedUrlContainsManualTaggingParameters" errorcode="PageFeedUrlContainsManualTaggingParameters" code="5998" message="The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options."/>
      <error name="PageFeedUrlContainsInvalidCharacters" errorcode="PageFeedUrlContainsInvalidCharacters" code="5999" message="The URL contains invalid characters. Please remove all invalid characters and try again." />

      <!-- Experiment (range = 6101 to 6199) -->
      <error name="ExperimentsListIsNullOrEmpty" errorcode="ExperimentsListIsNullOrEmpty" code="6101" message="The experiment list cannot be null or empty."/>
      <error name="ExperimentIsNull" errorcode="ExperimentIsNull" code="6102" message="The experiment cannot be null."/>
      <error name="ExperimentsEntityLimitExceeded" errorcode="ExperimentsEntityLimitExceeded" code="6103" message="The number of experiments in the request exceeds the limit."/>
      <error name="ExperimentNameIsSameAsBaseCampaignName" errorcode="ExperimentNameIsSameAsBaseCampaignName" code="6104" message="The experiment name cannot be the same as Base Campaign Name."/>
      <error name="ExperimentStartDateGreaterThanEndDate" errorcode="ExperimentStartDateGreaterThanEndDate" code="6105" message="The experiment start date must be earlier than the end date."/>
      <error name="ExperimentStartDateLessThanToday" errorcode="ExperimentStartDateLessThanToday" code="6106" message="The experiment start date must be in the future."/>
      <error name="ExperimentEndDateLessThanToday" errorcode="ExperimentEndDateLessThanToday" code="6107" message="The experiment end date must be in the future."/>
      <error name="ExperimentTrafficSplitPercentInvalid" errorcode="ExperimentTrafficSplitPercentInvalid" code="6108" message="The experiment traffic split percent is invalid."/>
      <error name="ExperimentNameIsEmpty" errorcode="ExperimentNameIsEmpty" code="6109" message="The experiment name cannot be empty."/>
      <error name="ExperimentIdInvalid" errorcode="ExperimentIdInvalid" code="6110" message="The experiment ID is invalid."/>
      <error name="DuplicateExperimentIds" errorcode="DuplicateExperimentIds" code="6111" message="Duplicate experiment IDs are not allowed."/>
      <error name="ExperimentIdListNullOrEmpty" errorcode="ExperimentIdListNullOrEmpty" code="6112" message="The experiment IDs list cannot be null or empty."/>
      <error name="ExperimentTimeperiodOverlapping" errorcode="ExperimentTimeperiodOverlapping" code="6113" message="Multiple experiments for the same base campaign cannot have overlapping time interval."/>
      <error name="ExperimentBaseCampaignIdIsLocked" errorcode="ExperimentBaseCampaignIdIsLocked" code="6114" message="The creation of an experiment for this base campaign is already in progress. Please wait and try again in a few minutes."/>
      <error name="ExperimentCampaignIdInvalid" errorcode="ExperimentCampaignIdInvalid" code="6115" message="The experiment Campaign ID invalid."/>
      <error name="ExperimentsEntityLimitPerCampaignExceeded" errorcode="ExperimentsEntityLimitPerCampaignExceeded" code="6116" message="The maximum number of experiments for this campaign has already been reached."/>
      <error name="ExperimentPilotNotEnabledForCustomer" errorcode="ExperimentPilotNotEnabledForCustomer" code="6117" message="The customer is not in pilot for the experiments feature."/>
      <error name="ExperimentStatusInvalid" errorcode="ExperimentStatusInvalid" code="6118" message="The experiment status is invalid."/>
      <error name="ExperimentBaseCampaignIsExperimentCampaign" errorcode="ExperimentBaseCampaignIsExperimentCampaign" code="6119" message="The base campaign cannot be an experimental campaign."/>
      <error name="ExperimentStartDateCannotBeChanged" errorcode="ExperimentStartDateCannotBeChanged" code="6120" message="The experiment has already started and the start date cannot be changed."/>
      <error name="ExperimentEndDateCannotBeChanged" errorcode="ExperimentEndDateCannotBeChanged" code="6121" message="The experiment has already started and the end date cannot be changed."/>
      <error name="ExperimentStartDateInvalid" errorcode="ExperimentStartDateInvalid" code="6122" message="The experiment start date is invalid."/>
      <error name="ExperimentBaseCampaignIdInvalid" errorcode="ExperimentBaseCampaignIdInvalid" code="6123" message="The experiment base campaign ID is invalid."/>
      <error name="CampaignOrExperimentWithNameAlreadyExists" errorcode="CampaignOrExperimentWithNameAlreadyExists" code="6124" message="The experiment name cannot be the same as an existing experiment or campaign name."/>
      <error name="BaseCampaignTypeInvalid" errorcode="BaseCampaignTypeInvalid" code="6125" message="Experiments are not supported for the requested base campaign type."/>
      <error name="BaseCampaignBudgetTypeInvalid" errorcode="BaseCampaignBudgetTypeInvalid" code="6126" message="The experiment base campaign budget type is invalid."/>
      <error name="ExperimentNameTooLong" errorcode="ExperimentNameTooLong" code="6127" message="The experiment name is too long."/>
      <error name="ExperimentNameHasInvalidCharacters" errorcode="ExperimentNameHasInvalidCharacters" code="6128" message="The experiment name is invalid"/>
      <error name="ExperimentCampaignInvalid" errorcode="ExperimentCampaignInvalid" code="6129" message="The experiment campaign is invalid."/>
      <error name="ExperimentBaseCampaignIdCannotBeChanged" errorcode="ExperimentBaseCampaignIdCannotBeChanged" code="6130" message="The experiment base campaign ID cannot be changed."/>
      <error name="ExperimentTrafficSplitPercentCannotBeChanged" errorcode="ExperimentTrafficSplitPercentCannotBeChanged" code="6131" message="The experiment has already started and the traffic split percent cannot be changed."/>
      <error name="EndedExperimentCannotBeChanged" errorcode="EndedExperimentCannotBeChanged" code="6132" message="The experiment has already ended and cannot be changed."/>
      <error name="ExperimentCampaignCannotBeUpdated" errorcode="ExperimentCampaignCannotBeUpdated" code="6133" message="One or more properties of the experiment campaign cannot be updated."/>
      <error name="ExperimentBaseCampaignCannotBeChangedToSharedBudget" errorcode="ExperimentBaseCampaignCannotBeChangedToSharedBudget" code="6134" message="A shared budget is not allowed for an experiment's base campaign."/>
      <error name="ExperimentBatchLimitExceeded" errorcode="ExperimentBatchLimitExceeded" code="6135" message="The maximum number of experiments per request is exceeded."/>
      <error name="ExperimentTypeInvalid" errorcode="ExperimentTypeInvalid" code="6136" message="The experiment type is invalid."/>
      <error name="ExperimentTypeCannotBeChanged" errorcode="ExperimentTypeCannotBeChanged" code="6137" message="The experiment type cannot be changed."/>
      <error name="ExperimentDoesNotSupportMixedModeCampaign" errorcode="ExperimentDoesNotSupportMixedModeCampaign" code="6138" message="The experiment does not support mixed mode campaign."/>
	    <error name="DSASettingCannotBeAddedToExperimentRelatedCampaign" errorcode="DSASettingCannotBeAddedToExperimentRelatedCampaign" code="6139" message="DSA Setting can not be added to ExperimentRelatedCampaign."/>

      <!-- Responsive Search Ad (range = 6200 to 6299) -->
      <error name="ResponsiveSearchAdHeadlinesNullOrEmpty" errorcode="CampaignServiceResponsiveSearchAdHeadlinesNullOrEmpty" code="6200" message="The headlines cannot be null or empty."/>
      <error name="ResponsiveSearchAdDescriptionsNullOrEmpty" errorcode="CampaignServiceResponsiveSearchAdDescriptionsNullOrEmpty" code="6201" message="The descriptions cannot be null or empty."/>
      <error name="ResponsiveSearchAdDuplicateHeadlines" errorcode="CampaignServiceResponsiveSearchAdDuplicateHeadlines" code="6202" message="Duplicate headlines are not allowed."/>
      <error name="ResponsiveSearchAdDuplicateDescriptions" errorcode="CampaignServiceResponsiveSearchAdDuplicateDescriptions" code="6203" message="Duplicate descriptions are not allowed."/>
      <error name="ResponsiveSearchAdHeadlinesPinnedFieldMismatch" errorcode="CampaignServiceResponsiveSearchAdHeadlinesPinnedFieldMismatch" code="6204" message="Incorrect pinned field for headline."/>
      <error name="ResponsiveSearchAdDescriptionsPinnedFieldMismatch" errorcode="CampaignServiceResponsiveSearchAdDescriptionsPinnedFieldMismatch" code="6205" message="Incorrect pinned field for description."/>
      <error name="ResponsiveSearchAdInvalidHeadline" errorcode="CampaignServiceResponsiveSearchAdInvalidHeadline" code="6206" message="Headline is not valid."/>
      <error name="ResponsiveSearchAdInvalidDescription" errorcode="CampaignServiceResponsiveSearchAdInvalidDescription" code="6207" message="Description is not valid."/>
      <error name="ResponsiveSearchAdHeadlineTooLong" errorcode="CampaignServiceResponsiveSearchAdHeadlineTooLong" code="6208" message="Headline is too long."/>
      <error name="ResponsiveSearchAdDescriptionTooLong" errorcode="CampaignServiceResponsiveSearchAdDescriptionTooLong" code="6209" message="Description is too long."/>
      <error name="ResponsiveSearchAdHeadlinesMaxCountExceeded" errorcode="CampaignServiceResponsiveSearchAdHeadlinesMaxCountExceeded" code="6210" message="The number of headlines is more than maximum allowed."/>
      <error name="ResponsiveSearchAdDescriptionsMaxCountExceeded" errorcode="CampaignServiceResponsiveSearchAdDescriptionsMaxCountExceeded" code="6211" message="The number of descriptions is more than maximum allowed."/>
      <error name="ResponsiveSearchAdHeadlinesLessThanMinRequired" errorcode="CampaignServiceResponsiveSearchAdHeadlinesLessThanMinRequired" code="6212" message="The minimum number of eligible headlines has not been met. A headline is considered ineligible, for example if it includes a countdown function without a default value."/>
      <error name="ResponsiveSearchAdDescriptionsLessThanMinRequired" errorcode="CampaignServiceResponsiveSearchAdDescriptionsLessThanMinRequired" code="6213" message="The minimum number of eligible descriptions has not been met. A description is considered ineligible, for example if it includes a countdown function without a default value."/>
      <error name="ResponsiveSearchAdPinnedFieldInvalid" errorcode="CampaignServiceResponsiveSearchAdPinnedFieldInvalid" code="6214" message="The pinned field of asset link is invalid."/>
      <error name="ResponsiveSearchAdAssetTypeInvalidForField" errorcode="CampaignServiceResponsiveSearchAdAssetTypeInvalidForField" code="6215" message="The provided asset type is not valid for this field."/>
      <error name="ResponsiveSearchAdDefaultTextRequiredForKeyword" errorcode="CampaignServiceResponsiveSearchAdDefaultTextRequiredForKeyword" code="6216" message="When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}."/>
      <error name="ResponsiveSearchAdPath1TooLong" errorcode="CampaignServiceResponsiveSearchAdPath1TooLong" code="6217" message="Path 1 is over the character limit."/>
      <error name="ResponsiveSearchAdPath2TooLong" errorcode="CampaignServiceResponsiveSearchAdPath2TooLong" code="6218" message="Path 2 is over the character limit."/>
      <error name="ResponsiveSearchAdPath1Invalid" errorcode="CampaignServiceResponsiveSearchAdPath1Invalid" code="6219" message="Path 1 is not valid."/>
      <error name="ResponsiveSearchAdPath2Invalid" errorcode="CampaignServiceResponsiveSearchAdPath2Invalid" code="6220" message="Path 2 is not valid."/>
      <error name="ResponsiveSearchAdPath2SetWithoutPath1" errorcode="CampaignServiceResponsiveSearchAdPath2SetWithoutPath1" code="6221" message="Path2 is set without path1."/>
      <error name="ResponsiveSearchAdFinalUrlDomainTooLong" errorcode="CampaignServiceResponsiveSearchAdFinalUrlDomainTooLong" code="6222" message="Display URL (i.e., your Final URL's domain combined with Path1 or Path2, if you've specified them) is too long. Please shorten it. For more details, visit https://help.ads.microsoft.com/apex/index/3/en/53077"/>
      <!-- Responsive Search Ad - Vanity URLs-->
      <error name="ResponsiveSearchAdDisplayUrlDomainTooLong" errorcode="CampaignServiceResponsiveSearchAdDisplayUrlDomainTooLong" code="6223" message="Display Url domain is too long."/>
      <error name="ResponsiveSearchAdDisplayUrlDomainInvalid" errorcode="CampaignServiceResponsiveSearchAdDisplayUrlDomainInvalid" code="6224" message="Display Url domain is Invalid."/>

      <error name="AdCustomizerNotSupportedForAdType" errorcode="CampaignServiceAdCustomizerNotSupportedForAdType" code="6225" message="Ad customizer is not supported for this ad type."/>
      <error name="MaxActiveResponsiveSearchAdsPerAdgroupLimitReached" errorcode="CampaignServiceMaxActiveResponsiveSearchAdsPerAdGroupLimitReached" code="6226" message="The number of active Responsive Search Ads per ad group would be exceeded."/>
      <error name="ResponsiveSearchAdsBothCountDownAndGlobalCountDown" errorcode="ResponsiveSearchAdsBothCountDownAndGlobalCountDown" code="6227" message="The same ad cannot include both UTC CountDown and Global_CountDown functions."/>
      
      <!-- Multi Media Ads-->
      <error name="MaxActiveMultiMediaAdsPerAdgroupLimitReached" errorcode="MaxMMAAdsLimitReachedInAdGroup" code="6228" message="The number of active Multi Media Ads per ad group would be exceeded."/>
      <error name="ResponsiveAdDefaultTextRequiredForKeyword" errorcode="ResponsiveAdDefaultTextRequiredForKeyword" code="6229" message="When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}."/>
      <error name="ResponsiveAdHeadlinesLessThanMinRequired" errorcode="ResponsiveAdHeadlinesLessThanMinRequired" code="6230" message="The minimum number of eligible headlines has not been met. A headline is considered ineligible, for example if it includes a countdown function without a default value."/>
      <error name="ResponsiveAdLongHeadlinesLessThanMinRequired" errorcode="ResponsiveAdLongHeadlinesLessThanMinRequired" code="6231" message="The minimum number of eligible long headlines has not been met. A long headline is considered ineligible, for example if it includes a countdown function without a default value."/>
      <error name="ResponsiveAdDescriptionsLessThanMinRequired" errorcode="ResponsiveAdDescriptionsLessThanMinRequired" code="6232" message="The minimum number of eligible descriptions has not been met. A description is considered ineligible, for example if it includes a countdown function without a default value."/>
      <error name="ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed" errorcode="ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed" code="6233" message="The same ad cannot include both UTC CountDown and Global_CountDown functions."/>

	    <error name="SystemGeneratedAssetNotAllowed" errorcode="SystemGeneratedAssetNotAllowed" code="6234" message="System generated Asset cannot be used in this operation."/>
	    <error name="ImageDoesntMeetMinPixelRequirements" errorcode="ImageDoesntMeetMinPixelRequirements" code="6235" message="Image does not meet the minimum pixel requirements for this placement."/>
	    <error name="ImageDoesntFitAspectRatio" errorcode="ImageDoesntFitAspectRatio" code="6236" message="Image does not match the aspect ratio required for this association."/>
	    <error name="AssetCropSettingInvalid" errorcode="AssetCropSettingInvalid" code="6237" message="Invalid crop settings."/>

      <error name="AccountNotEnabledForRSAAutoGeneratedAssets" errorcode="AccountNotEnabledForRSAAutoGeneratedAssets" code="6238" message="Account not enabled for Responsive Search Ads auto-generated assets."/>
      <error name="CustomerNotEnabledForVanityPharmaWebsiteDescriptions" errorcode="CampaignServiceCustomerNotEnabledForVanityPharmaWebsiteDescriptions" code="6239" message="The customer is not enabled for Website descriptions" />
      <error name="InvalidDisplayUrlMode" errorcode="CampaignServiceInvalidDisplayUrlMode" code="6240" message="Invalid display URL mode." />
      <error name="InvalidWebsiteDescription" errorcode="CampaignServiceInvalidWebsiteDescription" code="6241" message="Invalid website description." />
      <error name="InvalidWebsiteDescriptionForDisplayUrlMode" errorcode="CampaignServiceInvalidWebsiteDescriptionForDisplayUrlMode" code="6242" message="Invalid website description for the selected display URL mode." />
      <error name="TextAssetLimitReachedForAccount" errorcode="CampaignServiceTextAssetLimitReachedForAccount" code="6243" message="Text Asset Limit Reached For Account."/>
      <error name="TextAssetDoesNotExist" errorcode="CampaignServiceTextAssetDoesNotExist" code="6244" message="Text Asset Does Not Exist."/>
      <error name="TextAssetsNotPassed" errorcode="CampaignServiceTextAssetsNotPassed" code="6245" message="Text Assets Not Passed."/>

      <!-- IF Function (range = 6400 to 6499) -->
      <error name="IFFunctionCustomerNotInPilot" errorcode="IFFunctionCustomerNotInPilot" code="6400" message="The customer is not in pilot for IF functions"/>
      <error name="IFFunctionIncorrectSyntaxForDevice" errorcode="IFFunctionIncorrectSyntaxForDevice" code="6401" message="Device type IF function has incorrect syntax"/>
      <error name="IFFunctionIncorrectSyntaxForAudience" errorcode="IFFunctionIncorrectSyntaxForAudience" code="6402" message="Audience type IF function has incorrect syntax"/>
      <error name="IFFunctionSomeHaveDefaultValueButNotAll" errorcode="IFFunctionSomeHaveDefaultValueButNotAll" code="6403" message="Either all IF functions should have a default or none"/>
      <error name="IFFunctionInvalidAudience" errorcode="IFFunctionInvalidAudience" code="6404" message="The audience which is references in the Ad is invalid"/>
      <error name="IFFunctionDuplicateAudiences" errorcode="IFFunctionDuplicateAudiences" code="6405" message="Duplicate audience names have been provided in the IF function"/>
      <error name="IFFunctionSpecialCharactersAreNotEscaped" errorcode="IFFunctionSpecialCharactersAreNotEscaped" code="6406" message="User needs to escape certain special charaters per guidelines"/>
      <error name="IFFunctionNestingNotAllowed" errorcode="IFFunctionNestingNotAllowed" code="6407" message="Nesting functions of any kind, within an IF function is not allowed"/>
      <error name="IFFunctionSpecialCharactersNotAllowed" errorcode="IFFunctionNestingNotAllowed" code="6408" message="Cannot include audience name containing { } in IF functions. Please rename audience without { or } before referencing in IF functions"/>
      <error name="IFFunctionInvalidSyntax" errorcode="IFFunctionInvalidSyntax" code="6409" message="Invalid syntax for IF function"/>
      <error name="IFFunctionNumAudiencesExceedsMaxForAd" errorcode="IFFunctionNumAudiencesExceedsMaxForAd" code="6410" message="Number of audiences exceeds the allowed max of 100 per ad"/>
      <error name="IFFunctionAudiencesExceedsMaxFieldLength" errorcode="IFFunctionAudiencesExceedsMaxFieldLength" code="6411" message="Audience names for ad exceed internal field length"/>
      <error name="IFFunctionErrorGettingAudiences" errorcode="IFFunctionErrorGettingAudiences" code="6412" message="There was an error fetching audience list from MDS"/>

      <!-- Disclaimer Ads (range = 6500 - 6599) -->
      <error name="BulkUploadNotSupportedForDisclaimerAds" errorcode="BulkUploadNotSupportedForDisclaimerAds" code="6500" message="Bulk upload not supported for disclaimer ads"/>
      <error name="DisclaimerSettingCannotBeUpdated" errorcode="DisclaimerSettingCannotBeUpdated" code="6501" message="Disclaimer setting cannot be updated"/>
      <error name="CustomerNotEligibleForDisclaimerAds" errorcode="CustomerNotEligibleForDisclaimerAds" code="6502" message="Customer not eligible for disclaimer ads"/>
      <error name="DisclaimerLayoutMissing" errorcode="DisclaimerLayoutMissing" code="6503" message="Disclaimer Layout is missing"/>
      <error name="InvalidDisclaimerLayout" errorcode="InvalidDisclaimerLayout" code="6504" message="Invalid Disclaimer Layout"/>
      <error name="NullOrEmptyDisclaimerPopupText" errorcode="NullOrEmptyDisclaimerPopupText" code="6505" message="Disclaimer popup text is null or empty"/>
      <error name="DisclaimerLineTextShouldbeEmpty" errorcode="DisclaimerLineTextShouldbeEmpty" code="6506" message="Disclaimer line text should be empty"/>
      <error name="NullOrEmptyDisclaimerLineText" errorcode="NullOrEmptyDisclaimerLineText" code="6507" message="Disclaimer line text is null or empty"/>
      <error name="DisclaimerPopupTextShouldbeEmpty" errorcode="DisclaimerPopupTextShouldbeEmpty" code="6508" message="Disclaimer popup text should be empty"/>
      <error name="DisclaimerFinalUrlMissing" errorcode="DisclaimerFinalUrlMissing" code="6509" message="Disclaimer final url is missing"/>
      <error name="OnlyOneDisclaimerFinalUrlIsAllowed" errorcode="OnlyOneDisclaimerFinalUrlIsAllowed" code="6510" message="Only one disclaimer final url is allowed"/>
      <error name="OnlyOneDisclaimerFinalMobileUrlIsAllowed" errorcode="OnlyOneDisclaimerFinalMobileUrlIsAllowed" code="6511" message="Only one disclaimer final mobile url is allowed"/>
      <error name="DisclaimerTitleNotAllowedForLineText" errorcode="DisclaimerTitleNotAllowedForLineText" code="6512" message="Disclaimer title is not allowed for line text disclaimer"/>
      <error name="EntityOnlyAllowedForDisclaimerCampaign" errorcode="EntityOnlyAllowedForDisclaimerCampaign" code="6513" message="Disclaimer canonly be associated to disclaimer campaign"/>
      <error name="AppInstallAdNotSupportedForDisclaimerCampaign" errorcode="AppInstallAdNotSupportedForDisclaimerCampaign" code="6514" message="App install ad is not supported to be created under disclaimer campaign"/>
      <error name="ExperimentNotSupportedForDisclaimerCampaign" errorcode="ExperimentNotSupportedForDisclaimerCampaign" code="6515" message="Experiment not supported for disclaimer campaign"/>
      <error name="AdTypeNotSupportedForDisclaimerCampaign" errorcode="AdTypeNotSupportedForDisclaimerCampaign" code="6516" message="This Ad type is not supported to be created under disclaimer campaign"/>
      <error name="DisclaimerTitleIsRequiredForPopupText" errorcode="DisclaimerTitleIsRequiredForPopupText" code="6517" message="Disclaimer title is required for popup text disclaimer"/>
      <error name="InvalidDisclaimerTitle" errorcode="InvalidDisclaimerTitle" code="6518" message="The disclaimer title is invalid."/>

      <!-- ImageAdExtensionV2 (range = 6600 - 6649) -->
      <error name="ImageAdExtensionV2PilotNotEnabledForCustomer" errorcode="ImageAdExtensionV2PilotNotEnabledForCustomer" code="6600" message="The customer is not part of the Image AdExtension V2 pilot program."/>
      <error name="ImageAdExtensionDisplayTextNullOrEmpty" errorcode="CampaignServiceImageAdExtensionDisplayTextNullOrEmpty" code="6601" message="The image ad extension's DisplayText cannot be null or empty."/>
      <error name="ImageAdExtensionDisplayTextInvalid" errorcode="CampaignServiceImageAdExtensionDisplayTextInvalid" code="6602" message="The image ad extension's DisplayText is invalid."/>
      <error name="ImageAdExtensionDisplayTextTooLong" errorcode="CampaignServiceImageAdExtensionDisplayTextTooLong" code="6603" message="The image ad extension's DisplayText is too long."/>


      <!-- ViewThroughConversion -->
      <error name="ViewThroughAccountSettingValueInvalid" errorcode="ViewThroughAccountSettingValueInvalid" code="6604" message="The view through conversions setting is invalid."/>
      <error name="CustomerNotEligibleForViewThroughConversion" errorcode="CustomerNotEligibleForViewThroughConversion" code="6605" message="The customer is not eligible for the view through conversions feature."/>
      <!-- ExpandedTargeting -->
      <error name="ProfileExpansionSettingInvalid" errorcode="ProfileExpansionSettingInvalid" code="6606" message="The profile expansion setting is invalid."/>
      <error name="CustomerNotEligibleForProfileTargeting" errorcode="CustomerNotEligibleForProfileTargeting" code="6607" message="The customer is not eligible for the profile targeting feature."/>
      <error name="InvalidViewThroughConversionWindowInMinutes" errorcode="InvalidViewThroughConversionWindowInMinutes" code="6608" message="The view through conversion window value is invalid."/>
      <error name="ViewThroughConversionNotApplicableToGoalType" errorcode="ViewThroughConversionNotApplicableToGoalType" code="6609" message="The view through conversion window is not applicable to this goal type."/>
      <!-- ConversionGoalSelection -->
      <error name="CustomerNotEligibleForConversionGoalSelection" errorcode="CustomerNotEligibleForConversionGoalSelection" code="6610" message="The customer is not eligible for conversion goal selection feature."/>
      <error name="CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid" errorcode="CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid" code="6611" message="The campaign cannot be undeleted because the associated shared budget is invalid or already deleted."/>
      <!-- PromotionAdExtension (range = 6615 - 6625) -->
      <error name="PromotionValueNotSet" errorcode="PromotionValueNotSet" code="6615" message="Promotion PercentOff and MoneyAmountOff not set."/>
      <error name="PromotionPercentAndMoneyValueSet" errorcode="PromotionPercentAndMoneyValueSet" code="6616" message="Both PercentOff and MoneyAmountOff set."/>
      <error name="PromotionOrdersOverAndPromoCodeSet" errorcode="PromotionOrdersOverAndPromoCodeSet" code="6617" message="Both PromotionCode and OrdersOverAmount set."/>
      <error name="PromotionValueNegative" errorcode="PromotionValueNegative" code="6618" message="PercentOff and MoneyAmountOff cannot be negative."/>
      <error name="PromotionOrdersOverNegative" errorcode="PromotionOrdersOverNegative" code="6619" message="OrdersOverAmount cannot be negative."/>
      <error name="PromotionDatesInvalid" errorcode="PromotionDatesInvalid" code="6620" message="The Promotion Start and End Dates are invalid."/>
      <error name="CurrencyCodeSetWithoutMonetaryValue" errorcode="CurrencyCodeSetWithoutMonetaryValue" code="6621" message="Currency Code can only be set if MoneyAmountOff or PercentOff are set."/>

      <!-- Import Api  (range = 6650 - 6699) -->
      <error name="GoogleDirectImportNotEnabledForCustomer" errorcode="GoogleDirectImportNotEnabledForCustomer" code="6650" message="The customer is not in the Google Import as a Service pilot program."/>
      <error name="ImportJobsNull" errorcode=" ImportJobsNull" code="6651" message="Import Jobs is null or empty."/>
      <error name="ScheduledImportNumberLimitExceed" errorcode="ScheduledImportNumberLimitExceed" code="6652" message="The maximum number of scheduled imports for the account would be exceeded."/>
      <error name="ImportCredentialIdInvalid" errorcode="ImportCredentialIdInvalid" code="6653" message="The import credential ID is invalid."/>
      <error name="ImportTypeInvalid" errorcode="ImportTypeInvalid" code="6654" message="The import type is invalid."/>
      <error name="ImportJobNameNull" errorcode="ImportJobNameNull" code="6655" message="The import name cannot be empty."/>
      <error name="ImportFromAccountIdNull" errorcode="ImportFromAccountIdNull" code="6656" message="The import from account ID is required."/>
      <error name="ImportNameInvalid" errorcode="ImportNameInvalid" code="6657" message="The import name is invalid."/>
      <error name="ImportSchedulingInvalid" errorcode="ImportSchedulingInvalid" code="6658" message="The import schedule is invalid."/>
      <error name="FileImportFileSourceInvalid" errorcode="FileImportFileSourceInvalid" code="6659" message="The file source for this import is invalid."/>
      <error name="FileImportFileUrlInvalid" errorcode="FileImportFileUrlInvalid" code="6660" message="The file url for this import is invalid."/>
      <error name="InvalidImportJobId" errorcode="InvalidImportJobId" code="6661" message="Import job id array cannot be null or empty."/>
      <error name="DuplicateInImportJobIds" errorcode="DuplicateInImportJobIds" code="6662" message="Duplicate IDs are contained in the array of import jobs."/>
      <error name="FileImportFrequencyInvalid" errorcode="FileImportFrequencyInvalid" code="6663" message="File Import does not support scheduling."/>
      <error name="FileImportNotEnabledForCustomer" errorcode="FileImportNotEnabledForCustomer" code="6664" message="The customer is not in the File Import as a Service pilot program."/>
      <error name="CampaignServiceImportOptionNotSupportedForJob" errorcode="CampaignServiceImportOptionNotSupportedForJob" code="6665" message="This import job type does not support the specified option type."/>
      <error name="CampaignServiceImportNameTooLong" errorcode="CampaignServiceImportNameTooLong" code="6666" message="This import job name exceeds the limit."/>
      <error name="InvalidImportEntityType" errorcode="InvalidImportEntityType" code="6667" message="This import entity type is invalid."/>
      <error name="TaskThrottlingLimitReached" errorcode="TaskThrottlingLimitReached" code="6668" message="The number of import jobs of the requested type would exceed the limit."/>
      <error name="ImportFromAccountIdInvalid" errorcode="ImportFromAccountIdInvalid" code="6669" message="The import from account ID is invalid."/>
      <error name="ImportJobCannotUpdateFromApi" errorcode="ImportJobCannotUpdateFromApi" code="6698" message="The import cannot be updated from API."/>

      <error name="CampaignServiceBatchLimitExceeded" errorcode="CampaignServiceBatchLimitExceeded" code="6999" message="The maximum number of items per request would be exceeded."/>
      <error name="AdScheduleTimeZoneSettingNotInPilot" errorcode="AdScheduleTimeZoneSettingNotInPilot" code="7000" message="The customer is not enabled for the ad scheduling by account time zone feature pilot."/>

      <error name="KeywordSubstitutionNotSupported" errorcode="KeywordSubstitutionNotSupported" code="7001" message="Substitution with the {Keyword} parameter is not supported for this property."/>

      <!-- FilterLinkAdExtension (range = 6626 - 6635) -->
      <error name="InvalidFilterLinkTextCharacter" errorcode="InvalidFilterLinkTextCharacter" code="6626" message="Filter Link text contains an invalid character."/>
      <error name="TooFewFilterLinkText" errorcode="TooFewFilterLinkText" code="6627" message="You must include between 3 to 10 Filter Link Texts."/>
      <error name="TooManyFilterLinkText" errorcode="TooManyFilterLinkText" code="6628" message="You must include between 3 to 10 Filter Link Texts."/>
      <error name="FinalUrlandTextNotMatch" errorcode="FinalUrlandTextNotMatch" code="6629" message="The count of list items in Final Urls and Texts must match."/>
      <error name="EmptyElementInListNotAllowed" errorcode="EmptyElementInListNotAllowed" code="6630" message="Empty Element In List Not Allowed."/>

      <!-- FlyerAdExtension (range = 6636 - 6650) -->
      <error name="FlyerExtensionInvalidAssetId" errorcode="FlyerExtensionInvalidAssetId" code="6636" message="AssetId provided is invalid or does not belong to Account."/>
      <error name="FlyerAdExtensionsAssetLimitExceeded" errorcode="FlyerAdExtensionsAssetLimitExceeded" code="6637" message="The list of asset IDs exceeds the limit."/>
      <error name="FlyerExtensionImageTooSmall" errorcode="FlyerExtensionImageTooSmall" code="6638" message="The Image used is too small."/>
      <error name="FlyerExtensionInvalidStoreId" errorcode="FlyerExtensionInvalidStoreId" code="6639" message="The store ID is invalid or does not belong to the customer."/>
      <error name="FlyerExtensionSchedulingStartAndEndDateRequired" errorcode="FlyerExtensionSchedulingStartAndEndDateRequired" code="6640" message="Scheduling Start and End Dates are required for Flyer Extension."/>
      <error name="FlyerExtensionEndDateRangeExceeded" errorcode="FlyerExtensionEndDateRangeExceeded" code="6643" message="The Flyer Ad Extension End Date must be within 30 days of the Start Date."/>
      <error name="FlyerExtensionStoreIdCannotBeModified" errorcode="FlyerExtensionStoreIdCannotBeModified" code="6644" message="The store ID cannot be modified."/>

      <!-- PortfolioBidStrategy (range = 6670 - 6689) -->
      <error name="DuplicatePortfolioBidStrategyName" errorcode="DuplicatePortfolioBidStrategyName" code="6670" message="The portfolio bid strategy name already exists."/>
      <error name="PortfolioBidStrategyEntityLimitExceeded" errorcode="PortfolioBidStrategyEntityLimitExceeded" code="6671" message="The number of portfolio bid strategies for the account has exceeded the limit."/>
      <error name="PortfolioBidStrategyNameTooLong" errorcode="PortfolioBidStrategyNameTooLong" code="6672" message="The portfolio bid strategy name is too long."/>
      <error name="PortfolioBidStrategyNameMissing" errorcode="PortfolioBidStrategyNameMissing" code="6673" message="The portfolio bid strategy name should not be null or empty."/>
      <error name="PortfolioBidStrategyNameHasInvalidChars" errorcode="PortfolioBidStrategyNameHasInvalidChars" code="6674" message="The portfolio bid strategy name has invalid characters."/>
      <error name="PortfolioBidStrategiesAreNullOrEmpty" errorcode="PortfolioBidStrategiesAreNullOrEmpty" code="6675" message="The portfolio bid strategy list should not be null or empty."/>
      <error name="PortfolioBidStrategyOperationsBatchLimitExceeded" errorcode="PortfolioBidStrategyOperationsBatchLimitExceeded" code="6676" message="The batch size of the portfolio bid strategy list has exceeded the limit."/>
      <error name="PortfolioBidStrategyIsNull" errorcode="PortfolioBidStrategyIsNull" code="6677" message="The portfolio bid strategy should not be null."/>
      <error name="PortfolioBidStrategyIdShouldBeNullOnAdd" errorcode="PortfolioBidStrategyIdShouldBeNullOnAdd" code="6678" message="The portfolio bid strategy id is read-only and must be null for add operation."/>
      <error name="PortfolioBidStrategyIdInvalid" errorcode="PortfolioBidStrategyIdInvalid" code="6679" message="The portfolio bid strategy id is not valid."/>
      <error name="DuplicatePortfolioBidStrategyId" errorcode="DuplicatePortfolioBidStrategyId" code="6680" message="The portfolio bid strategy list contains duplicate ids."/>
      <error name="BidStrategyTypeCannotBeNullOnAdd" errorcode="BidStrategyTypeCannotBeNullOnAdd" code="6681" message="The bid strategy type of the portfolio bid strategy can not be null for add operation."/>
      <error name="PortfolioBidStrategyIsAssociatedWithActiveCampaigns" errorcode="PortfolioBidStrategyIsAssociatedWithActiveCampaigns" code="6682" message="The portfolio bid strategy is still used by at least one campaign, so it can not be deleted."/>
      <error name="PortfolioBidStrategyTypeCannotBeChanged" errorcode="PortfolioBidStrategyTypeCannotBeChanged" code="6683" message="The bid strategy type of the portfolio bid strategy can not be changed."/>
      <error name="AccountNotInPilotForPortfolioBidStrategy" errorcode="AccountNotInPilotForPortfolioBidStrategy" code="6684" message="The account is not in pilot for portfolio bid strategy."/>
      <error name="UnsupportedBidStrategyTypeForPortfolioBidStrategy" errorcode="UnsupportedBidStrategyTypeForPortfolioBidStrategy" code="6685" message="The bid strategy type is not supported for portfolio bid strategy."/>
      <error name="CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity" errorcode="CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity" code="6686" message="Portfolio bid strategy properties cannot be updated through Campaign entity, please use BidStrategy entity to do the updating instead."/>
      <error name="UnsupportedCampaignTypeForPortfolioBidStrategy" errorcode="UnsupportedCampaignTypeForPortfolioBidStrategy" code="6687" message="The campaign type is not supported for portfolio bid strategy."/>
      <error name="CampaignTypeAndBidStrategyTypeAreMutuallyExclusive" errorcode="CampaignTypeAndBidStrategyTypeAreMutuallyExclusive" code="6688" message="The campaign type and bid strategy type are mutually exclusive."/>
      <error name="PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged" errorcode="PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged" code="6689" message="The associated campaign type of the portfolio bid strategy can not be changed."/>
      <error name="CampaignTypeNotMatchCurrentPortfolioBidStrategy" errorcode="CampaignTypeNotMatchCurrentPortfolioBidStrategy" code="6690" message="The campaign type does not match the associated campaign type for current portfolio bid strategy."/>
      <error name="CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid" errorcode="CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid" code="6691" message="The campaign cannot be undeleted because the associated portfolio bid strategy is invalid or already deleted."/>

      <!-- VideoAdExtension (range = 6700 - 6719) -->
      <error name="VideoExtensionThumbnailRequired" errorcode="VideoExtensionThumbnailRequired" code="6700" message="Either ThumbnailUrl or ThumbnailId is required for Video Extension."/>
      <error name="VideoExtensionInvalidImageFormat" errorcode="VideoExtensionInvalidImageFormat" code="6701" message="The file type provided in ThumbnailUrl field is not supported."/>
      <error name="VideoExtensionThumbnailTooSmall" errorcode="VideoExtensionThumbnailTooSmall" code="6702" message="The thumbnail image used is too small."/>
      <error name="VideoExtensionInvalidAspectRatio" errorcode="VideoExtensionInvalidAspectRatio" code="6703" message="The aspect ratio of the media used is invalid"/>
      <error name="VideoExtensionInvalidThumbnailId" errorcode="VideoExtensionInvalidThumbnailId" code="6704" message="ThumbnailId provided is invalid or does not belong to Account."/>
      <error name="VideoExtensionVideoTooSmall" errorcode="VideoExtensionVideoTooSmall" code="6705" message="The video used is too small."/>
      <error name="VideoExtensionInvalidVideoDuration" errorcode="VideoExtensionInvalidVideoDuration" code="6706" message="The duration of the video is invalid."/>
      <error name="VideoExtensionInvalidVideoId" errorcode="VideoExtensionInvalidVideoId" code="6707" message="VideoId provided is invalid or does not belong to Account."/>
      <error name="VideoExtensionThumbnailIdAndUrlSet" errorcode="VideoExtensionThumbnailIdAndUrlSet" code="6708" message="Both ThumbnailUrl and ThumbnailId cannot be set."/>
      <error name="VideoExtensionVideoProcessingFailed" errorcode="VideoExtensionVideoProcessingFailed" code="6709" message="The video could not be processed."/>

      <!-- Personalized Offers (range = 6720 - 6739) -->
      <error name="AccountNotEnabledForPersonalizedOffers" errorcode="AccountNotEnabledForPersonalizedOffers" code="6720" message="The account is not enabled for personalized offers."/>
      <error name="PersonalizedOffersCashbackPercentInvalid" errorcode="PersonalizedOffersCashbackPercentInvalid" code="6721" message="The cashback percent exceeds the supported range"/>
      <error name="PersonalizedOffersCashbackBudgetInvalid" errorcode="PersonalizedOffersCashbackBudgetInvalid" code="6722" message="The cashback budget cap is invalid."/>
      <error name="PersonalizedOffersCampaignTypeNotSupported" errorcode="PersonalizedOffersCampaignTypeNotSupported" code="6723" message="Personalized offers is not enabled for this campaign type and subtype."/>
      <error name="PersonalizedOffersCampaignBudgetRequired" errorcode="PersonalizedOffersCampaignBudgetRequired" code="6724" message="Monthly Cashback Budget Amount is required to use Personalized offers."/>
      <error name="PersonalizedOffersAdGroupTypeNotSupported" errorcode="PersonalizedOffersAdGroupTypeNotSupported" code="6725" message="Personalized offers is not enabled for this ad group type."/>
      <error name="PersonalizedOffersScopeNotSupportedForCampaignType" errorcode="PersonalizedOffersScopeNotSupportedForCampaignType" code="6726" message="Product Cashback Scope is only supported for Shopping Campaigns."/>
      <error name="PersonalizedOffersNotEnabledForCampaign" errorcode="PersonalizedOffersNotEnabledForCampaign" code="6727" message="Personalized offers is not enabled for this campaign."/>
      <error name="PersonalizedOffersCouponsNotSupportedForCampaignType" errorcode="PersonalizedOffersCouponsNotSupportedForCampaignType" code="6728" message="Personalized Coupons is only supported for Shopping Campaigns."/>
      <error name="TargetRoasNotSupportedForPersonalizedOffers" errorcode="TargetRoasNotSupportedForPersonalizedOffers" code="6729" message="Target ROAS cannot be set for campaigns with personalized offers."/>
      <error name="PersonalizedOffersSponsoredPromotionsInvalid" errorcode="PersonalizedOffersSponsoredPromotionsInvalid" code="6730" message="The Sponsored Promotions for Brands cashback setting is invalid."/>
      <error name="PersonalizedOffersUnsupportedOperation" errorcode="PersonalizedOffersUnsupportedOperation" code="6731" message="A SPB campaign tried to update/delete cashback properties, which is unsupported for SPB campaign type."/>
      <error name="PersonalizedOffersSponsoredPromotionsOnly" errorcode="PersonalizedOffersSponsoredPromotionsOnly" code="6732" message="SPB campaigns only support IsPromotionsForBrands and none of the other cashback properties."/>
       
   	  <!-- PerformanceMax (range = 6740 - 6759) -->
      <error name="PerformanceMaxCampaignsNotEnabledForAccount" errorcode="PerformanceMaxCampaignsNotEnabledForAccount" code="6740" message="The account is not enabled for performance max campaigns."/>
      <error name="MaxCpcNotSupportedForCampaignType" errorcode="MaxCpcNotSupportedForCampaignType" code="6741" message="MaxCpc is not supported for this campaign type."/>
      <error name="PerformanceMaxCampaignFinalUrlExpansionOptedOut" errorcode="PerformanceMaxCampaignFinalUrlExpansionOptedOut" code="6742" message="Website exclusion is not allowed when final url expansion opt out is true."/>
      <error name="DuplicateAudienceGroupName" errorcode="DuplicateAudienceGroupName" code="6743" message="The audience group name already exists."/>
      <error name="AudienceGroupEntityLimitExceeded" errorcode="AudienceGroupEntityLimitExceeded" code="6744" message="The number of audience groups for the account has exceeded the limit."/>
      <error name="AudienceGroupIdInvalid" errorcode="AudienceGroupIdInvalid" code="6745" message="The audience group id is not valid."/>
      <error name="UnsupportedCampaignTypeForAssetGroup" errorcode="UnsupportedCampaignTypeForAssetGroup" code="6746" message="The campaign type is not supported for asset groups."/>
      <error name="AssetGroupsAreNullOrEmpty" errorcode="AssetGroupsAreNullOrEmpty" code="6747" message="The asset groups are null or empty."/>
      <error name="AssetGroupIsNull" errorcode="AssetGroupIsNull" code="6748" message="The asset group is null."/>
      <error name="AssetGroupOperationsBatchLimitExceeded" errorcode="AssetGroupOperationsBatchLimitExceeded" code="6749" message="Maximum number of asset groups per request is exceeded"/>
      <error name="Path2SetWithoutPath1" errorcode="Path2SetWithoutPath1" code="6750" message="Path2 is set without Path1."/>
      <error name="FinalUrlRequired" errorcode="FinalUrlRequired" code="6751" message="Final url is required."/>
      <error name="DomainInvalid" errorcode="DomainInvalid" code="6752" message="The domain is not valid."/>
      <error name="DomainTooLong" errorcode="DomainTooLong" code="6753" message="The domain is too long."/>
      <error name="DomainExtractionFailed" errorcode="DomainExtractionFailed" code="6754" message="Extracting display url domain from final urls failed."/>
      <error name="AssetFieldWithRequiredLengthMinimumNotMet" errorcode="AssetFieldWithRequiredLengthMinimumNotMet" code="6755" message="Asset field with a required length is not present."/>
      <error name="AssetFieldMinimumPerSubTypeNotMet" errorcode="AssetFieldMinimumPerSubTypeNotMet" code="6756" message="Minimum number of assets for asset sub type is not present."/>
      <error name="AssetFieldLimitPerSubTypeExceeded" errorcode="AssetFieldLimitPerSubTypeExceeded" code="6757" message="Maximum number of assets for asset sub type is exceeded."/>
      <error name="AssetGroupAudienceGroupAssociationDuplicate" errorcode="AssetGroupAudienceGroupAssociationDuplicate" code="6758" message="Audience Group Asset Group Association already exists."/>
      <error name="AssetGroupAudienceGroupAssociationDoesNotExist" errorcode="AssetGroupAudienceGroupAssociationDoesNotExist" code="6759" message="Asset Group is not associated to given Audience Group."/>
	
      <!-- LeadFormAdExtension (range = 6760 - 6779) -->
      <error name="LeadFormExtensionActionNameRequired" errorcode="LeadFormExtensionActionNameRequired" code="6760" message="A custom call to action must be specified if the 'Custom' call to action choice is chosen."/>
      <error name="LeadFormExtensionConfirmationUrlRequired" errorcode="LeadFormExtensionConfirmationUrlRequired" code="6761" message="A confirmation URL must be provided if the 'Download' or 'VisitWebsite' confirmation action is chosen."/>
      <error name="LeadFormExtensionInvalidLeadDelivery" errorcode="LeadFormExtensionInvalidLeadDelivery" code="6762" message="Webhook URL and key must be provided if the 'Webhook' lead delivery method is chosen, or email addresses must be provided if the 'Email' delivery method is chosen."/>
      <error name="LeadFormExtensionInvalidEmails" errorcode="LeadFormExtensionInvalidEmails" code="6763" message="The emails provided must be a comma-separated list of valid email addresses."/>
      <error name="LeadFormExtensionQuestionUpdatesNotSupported" errorcode="LeadFormExtensionQuestionUpdatesNotSupported" code="6764" message="Form questions can't be updated."/>
      <error name="LeadFormExtensionDuplicateQuestionId" errorcode="LeadFormExtensionDuplicateQuestionId" code="6765" message="The same question can't be included more than once."/>
      <error name="LeadFormExtensionInvalidQuestionId" errorcode="LeadFormExtensionInvalidQuestionId" code="6766" message="An invalid question id was specified."/>
      <error name="EntityOnlyAllowedForSearchOrPMaxCampaigns" errorcode="EntityOnlyAllowedForSearchOrPMaxCampaigns" code="6767" message="This can only be associated with a Search or Performance Max campaign."/>
      <error name="LeadFormExtensionNotFound" errorcode="LeadFormExtensionNotFound" code="6768" message="The requested Lead Form extension was not found."/>
      <error name="OnlyMaxClicksBiddingSchemeForLeadFormExtension" errorcode="OnlyMaxClicksBiddingSchemeForLeadFormExtension" code="6769" message="Lead Form extensions can only be associated with Max Clicks campaigns."/>
      <error name="LeadFormExtensionMultipleChoiceAnswersNotAllowed" errorcode="LeadFormExtensionMultipleChoiceAnswersNotAllowed" code="6770" message="Multiple choice answers are not supported for Lead Form extensions."/>

      <!-- PerformanceMax Part2 (range = 6780 - 6900) -->
      <error name="InvalidHeadlinesText" errorcode="InvalidHeadlinesText" code="6780" message="The text format of Headlines is invalid."/>
      <error name="InvalidLongHeadlinesText" errorcode="InvalidLongHeadlinesText" code="6781" message="The text format of LongHeadlines is invalid."/>
      <error name="InvalidDescriptionsText" errorcode="InvalidDescriptionsText" code="6782" message="The text format of Descriptions is invalid."/>
      <error name="InvalidImagesText" errorcode="InvalidImagesText" code="6783" message="The text format of Images is invalid."/>
      <error name="InvalidVideosText" errorcode="InvalidVideosText" code="6784" message="The text format of Videos is invalid."/>
      <error name="StartDateLessThanCurrentDate" errorcode="StartDateLessThanCurrentDate" code="6785" message="Start date cannot be less than the current date."/>
      <error name="EndDateLessThanStartDate" errorcode="EndDateLessThanStartDate" code="6786" message="End date cannot be less than the start date."/>
      <error name="StartDateCannotBeChanged" errorcode="StartDateCannotBeChanged" code="6787" message="Serving has already started and start date cannot be changed."/>

      <error name="AudienceGroupNameTooLong" errorcode="AudienceGroupNameTooLong" code="6788" message="The audience group name is too long."/>
      <error name="AudienceGroupNameMissing" errorcode="AudienceGroupNameMissing" code="6789" message="The audience group name should not be null or empty."/>
      <error name="AudienceGroupsAreNullOrEmpty" errorcode="AudienceGroupsAreNullOrEmpty" code="6790" message="The audience group list should not be null or empty."/>
      <error name="AudienceGroupOperationsBatchLimitExceeded" errorcode="AudienceGroupOperationsBatchLimitExceeded" code="6791" message="The batch size of the audience group list has exceeded the limit."/>
      <error name="AudienceGroupIsNull" errorcode="AudienceGroupIsNull" code="6792" message="The audience group should not be null."/>
      <error name="AudienceGroupIdShouldBeNullOnAdd" errorcode="AudienceGroupIdShouldBeNullOnAdd" code="6793" message="The audience group id is read-only and must be null for add operation."/>
      <error name="DimensionsCannotBeNull" errorcode="DimensionsCannotBeNull" code="6794" message="Audience dimensions cannot be null."/>
      <error name="UnsupportedAgeRangeForAudienceGroup" errorcode="UnsupportedAgeRangeForAudienceGroup" code="6795" message="One of the audience Age Range is not supported."/>
      <error name="UnsupportedGenderTypeForAudienceGroup" errorcode="UnsupportedGenderTypeForAudienceGroup" code="6796" message="One of the audience Gender Type is not supported."/>
      <error name="DuplicateAudienceGroupId" errorcode="DuplicateAudienceGroupId" code="6797" message="The audience group list contains duplicate ids."/>
      <error name="AudienceGroupNameHasInvalidChars" errorcode="AudienceGroupNameHasInvalidChars" code="6798" message="The audience group name has invalid characters."/>
      <error name="AudienceNotFound" errorcode="AudienceNotFound" code="6799" message="One of the audience added is not found."/>
      <error name="AudienceTypeNotSupported" errorcode="AudienceTypeNotSupported" code="6800" message="One of the audience type is not supported."/>
      <error name="DuplicateAudience" errorcode="DuplicateAudience" code="6801" message="The same audience already exists in this audience group."/>
      <error name="TooManyAudiences" errorcode="TooManyAudiences" code="6802" message="Audience group can't have more than allowed number audiences."/>
      <error name="TooManyDimensionsOfSameType" errorcode="TooManyDimensionsOfSameType" code="6803" message="Audience group can't have multiple audiences of same type."/>
      <error name="AudienceGroupInUse" errorcode="AudienceGroupInUse" code="6804" message="The audience group cannot be removed, because it is currently used in an (enabled or paused) asset group."/>
      
      <error name="OperationsForTooManyAssetGroups" errorcode="OperationsForTooManyAssetGroups" code="6805" message="The operations in this call involved too many asset groups."/>
      <error name="AssetGroupListingGroupsEntityLimitExceeded" errorcode="AssetGroupListingGroupsEntityLimitExceeded" code="6806" message="AssetGroupListingGroups entity count limit exceeded."/>
      <error name="AssetGroupListingGroupIsNull" errorcode="AssetGroupListingGroupIsNull" code="6807" message="AssetGroupListingGroup can not be null"/>
      <error name="DuplicateAssetGroupListingGroupIds" errorcode="DuplicateAssetGroupListingGroupIds" code="6808" message="Duplicate AssetGroupListingGroupId."/>
      <error name="AssetGroupListingGroupActionIsNull" errorcode="AssetGroupListingGroupActionIsNull" code="6809" message="AssetGroupListingGroupAction can not be null."/>
      <error name="AssetGroupListingGroupActionsNullOrEmpty" errorcode="AssetGroupListingGroupActionsNullOrEmpty" code="6810" message="AssetGroupListingGroupActions in request can not be null or empty."/>
      <error name="AnotherOperationForSameAssetGroupHasError" errorcode="AnotherOperationForSameAssetGroupHasError" code="6811" message="Another operation for same assetGroup has error."/>
      <error name="AssetGroupIdInvalid" errorcode="AssetGroupIdInvalid" code="6812" message="AssetGroupId is invalid"/>
      <error name="CampaignTypeIsNotPerformanceMax" errorcode="CampaignTypeIsNotPerformanceMax" code="6813" message="The campaign type is not Performance Max."/>
      <error name="ListingGoupLimitExceededForAssetGroup" errorcode="ListingGoupLimitExceededForAssetGroup" code="6814" message="ListingGoup count limit exceeded for assetGroup."/>
      <error name="AssetGroupListingGroupIdInvalid" errorcode="AssetGroupListingGroupIdInvalid" code="6815" message="AssetGroupListingGroupId is invalid."/>
      <error name="AssetGroupListingTypeInvalid" errorcode="AssetGroupListingTypeInvalid" code="6816" message="AssetGroupListingType is invalid."/>
      <error name="DuplicateRootNodeForListingGroupTree" errorcode="DuplicateRootNodeForListingGroupTree" code="6817" message="Duplicate root node for listingGroup tree."/>
      <error name="ParentListingGroupNodeDoesNotExist" errorcode="ParentListingGroupNodeDoesNotExist" code="6818" message="Parent listingGroup node does not exist"/>
      <error name="HeightOfListingGroupTreeLimitExceeded" errorcode="HeightOfListingGroupTreeLimitExceeded" code="6819" message="Height of listingGroup tree limit exceeded."/>
      <error name="ProductConditionHierarchyInvalid" errorcode="ProductConditionHierarchyInvalid" code="6820" message="ProductCondition hierarchy is invalid."/>
      <error name="EverythingElseNodeMissing" errorcode="EverythingElseNodeMissing" code="6821" message="EverythingElse node missing."/>
      <error name="UpdateIsNotSupportedForListingGroupNode" errorcode="UpdateIsNotSupportedForListingGroupNode" code="6822" message="Update is not supported for listingGroup node"/>
      <error name="FailedToGetSalesCountryFromCampaignSettings" errorcode="FailedToGetSalesCountryFromCampaignSettings" code="6823" message="Failed to get sales country from campaign settings"/>
      <error name="InvalidSalesCountry" errorcode="InvalidSalesCountry" code="6824" message="Invalid sales country, please check salescountry in your campaign settings"/>
      <error name="SharedBudgetNotSupportedForCampaignType" errorcode="SharedBudgetNotSupportedForCampaignType" code="6825" message="Shared budget is not supported for this campaign type"/>
      <error name="AccountNotEnabledForSearchCampaignPredictiveTargeting" errorcode="AccountNotEnabledForSearchCampaignPredictiveTargeting" code="6826" message="Account not enabled for keyword predictive targeting"/>
      <error name="PerformanceMaxCampaignLimitExceeded" errorcode="PerformanceMaxCampaignLimitExceeded" code="6827" message="Maximum number of PerformanceMax campaigns has been reached for this account"/>
      <error name="AssetGroupAlreadyExists" errorcode="AssetGroupAlreadyExists" code="6828" message="AssetGroup with the same name already exists."/>
	    <error name="AudienceGroupWithActiveAssociationsCannotBeDeleted" errorcode="AudienceGroupWithActiveAssociationsCannotBeDeleted" code="6829" message="Audience group with active asset group associations cannot be deleted."/>
	    <error name="AssetGroupLimitExceededForCampaign" errorcode="AssetGroupLimitExceededForCampaign" code="6830" message="Maximum number of AssetGroups has been reached for this campaign"/>
      <error name="AssetGroupListingGroupIdListExceedsLimit" errorcode="AssetGroupListingGroupIdListExceedsLimit" code="6831" message="AssetGroupListingGroup id list exceeds limit"/>
      <error name="EntityDoesNotBelongToCampaign" errorcode="EntityDoesNotBelongToCampaign" code="6832" message="Requested entity does not belong to the campaign."/>
      <error name="UnsupportedCampaignTypeForAdGroup" errorcode="UnsupportedCampaignTypeForAdGroup" code="6833" message="The campaign type is not supported for ad groups."/>
      <error name="AssetGroupInvalidStatus" errorcode="AssetGroupInvalidStatus" code="6834" message="The status of the asset group is invalid."/>

      <error name="CustomerNotEnabledForFeedLabel" errorcode="CustomerNotEnabledForFeedLabel" code="6835" message="Customer not enabled for FeedLabel."/>
      <error name="ShoppingSettingsFeedLabelInvalidLength" errorcode="ShoppingSettingsFeedLabelInvalidLength" code="6836" message="FeedLabel length is outside the allowed range."/>
      <error name="ShoppingSettingsFeedLabelInvalidCharacters" errorcode="ShoppingSettingsFeedLabelInvalidCharacters" code="6837" message="FeedLabel contains invalid characters."/>
      <error name="ShoppingSettingsFeedLabelAndSalesCountryIncompatible" errorcode="ShoppingSettingsFeedLabelAndSalesCountryIncompatible" code="6838" message="FeedLabel and SalesCountry are mutually exclusive."/>
      
      <error name="DomainDoesNotMatchCampaignStoreDomain" errorcode="DomainDoesNotMatchCampaignStoreDomain" code="6839" message="The top level domain of the url does not match the url of the store associated with the campaign."/>
      <error name="CampaignStoreDoesNotHaveDomain " errorcode="CampaignStoreDoesNotHaveDomain " code="6840" message="The store associated with the campaign does not have a domain."/>

      <error name="LogoExtensionLogoTooSmall" errorcode="LogoExtensionLogoTooSmall" code="6841" message="The logo is too small."/>
      <error name="LogoExtensionLogoTooLarge" errorcode="LogoExtensionLogoTooLarge" code="6842" message="The logo is too large."/>
      <error name="LogoExtensionLogoNotSquare" errorcode="LogoExtensionLogoNotSquare" code="6843" message="The logo is not square."/>

      <error name ="EntityNotAllowedForPmaxCampaign" errorcode="EntityNotAllowedForPmaxCampaign" code="6844" message="This entity type is not supported for performance max campaigns."/>
      <error name="BidAdjustmentNotSupportedForPerformanceMaxCampaign" errorcode="BidAdjustmentNotSupportedForPerformanceMaxCampaign" code="6845" message="Bid adjustment is not supported for performance max campaigns."/>

      <error name="InvalidPredictiveMatchingSetting" errorcode="CampaignServiceInvalidPredictiveMatchingSetting" code="6846" message="Invalid predictive matching setting." />

      <error name="ManualTaggingDetectedInQueryParameters" errorcode="ManualTaggingDetectedInQueryParameters" code="6847" message="The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options."/>
      <error name="InvalidBrandId" errorcode="InvalidBrandId" code="6848" message="Brand doesn't exist." />
       
      <error name="InvalidVideoAsset" errorcode="InvalidVideoAsset" code="6849" message="Not a video asset." />
      <error name="DuplicateVideoAsset" errorcode="DuplicateVideoAsset" code="6850" message="Duplicate video asset" />
      <error name="VideoInvalidStatus" errorcode="VideoInvalidStatus" code="6851" message="The status of the video is invalid." />
      <error name="VideoWidthTooSmall" errorcode="VideoWidthTooSmall" code="6852" message="The width of the Video is too small." />
      <error name="VideoHeightTooSmall" errorcode="VideoHeightTooSmall" code="6853" message="The height of the Video is too small." />
      <error name="VideoInvalidAspectRatio" errorcode="VideoInvalidAspectRatio" code="6854" message="The aspect ratio of the video is invalid." />
      <error name="VideoInvalidDuration" errorcode="VideoInvalidDuration" code="6855" message="The duration of the video is invalid." />
      <error name="VideoBitRateTooSmall" errorcode="VideoBitRateTooSmall" code="6856" message="The bit rate of the video is too small." />
      <error name="VideoSourceLengthTooLarge" errorcode="VideoSourceLengthTooLarge" code="6857" message="The source length of the video is too large." />
      <error name="VideoUnsupportedFileFormat" errorcode="VideoUnsupportedFileFormat" code="6858" message="The file format of the video is unsupported." />
      <error name="VideoAsAssetNotEnabledForAccount" errorcode="VideoAsAssetNotEnabledForAccount" code="6859" message="This account is not enabled for Video as an asset." />
      
      <!-- AppCampaign -->
      <error name="AppCampaignsNotEnabledForAccount" errorcode="AppCampaignsNotEnabledForAccount" code="8233" message="The account is not enabled for App campaigns."/>
      <!--(range = 8300 - 8320)-->
      <error name="EntityNotAllowedForAppCampaign" errorcode="CampaignServiceEntityNotAllowedForAppCampaign" code="8300" message="You cannot add this entity to a campaign of type App."/>
      <error name="TrackingTemplateIsRequiredForMobileAppCampaign" errorcode="TrackingTemplateIsRequiredForMobileAppCampaign" code="8301" message="Tracking template is required for Mobile App Campaigns."/>

      <error name="ConversionValueRuleLimitExceeded" errorcode="ConversionValueRuleLimitExceeded" code="8601" message="Maximum 25 Conversion Value Rules." />
      <error name="NullOrEmptyConversionValueRule" errorcode="NullOrEmptyConversionValueRule" code="8602" message="Conversion Value Rule is null or empty."/>
      <error name="NullRequiredProperty" errorcode="NullRequiredProperty" code="8603" message="Some property is not set up."/>
      <error name="DuplicateRuleName" errorcode="DuplicateRuleName" code="8604" message="The value rule name is already in use by another rule."/>
      <error name="ExceedsMaximumNumberOfRules" errorcode="ExceedsMaximumNumberOfRules" code="8605" message="Maximum for Rules is 25."/>
      <error name="RuleIdNotFound" errorcode="RuleIdNotFound" code="8606" message="The rule with this Id is not found."/>
      <error name="LocationTypeMismatch" errorcode="LocationTypeMismatch" code="8607" message="Location type is wrong for this location Id."/>
      <error name="AudienceTypeMismatch" errorcode="AudienceTypeMismatch" code="8608" message="Audience type is wrong for this audience Id."/>
      <error name="ConditionOverlap" errorcode="ConditionOverlap" code="8609" message="Condition overlaps with others."/>
      <error name="LocationHierarchyIssue" errorcode="LocationHierarchyIssue" code="8610" message="Location hierarchy is this rule."/>
      <error name="EntityIsEmptyOrNull" errorcode="EntityIsEmptyOrNull" code="8611" message="Entity is empty or null."/>
      <error name="EmptyPropertyNotAllowed" errorcode="EmptyPropertyNotAllowed" code="8612" message="Some required property is not set up."/>
      <error name="CurrencyCodeShouldNotBeNullForAdd" errorcode="CurrencyCodeShouldNotBeNullForAdd" code="8613" message="Currency code should be set up."/>
      <error name="PrimaryConditionShouldNotBeNull" errorcode="PrimaryConditionShouldNotBeNull" code="8614" message="Primary condition should not be null."/>
      <error name="DuplicateRuleId" errorcode="DuplicateRuleId" code="8615" message="Rule Ids are duplicated."/>
      <error name="ConditionTypeNotAllowed" errorcode="ConditionTypeNotAllowed" code="8616" message="Condition type is not allowed."/>
      <error name="ConversionValueRuleNotEnabled" errorcode="ConversionValueRuleNotEnabled" code="8617" message="Conversion Value Rule is not enabled for this account."/>
      <error name="PrimaryConditionCountShouldBeOne" errorcode="PrimaryConditionCountShouldBeOne" code="8618" message="Conversion Value Rule requires only 1 primary condition."/>
      <error name="ConditionCountShouldBeWithinTwo" errorcode="ConditionCountShouldBeWithinTwo" code="8619" message="There should be no more than two conditions."/>

    </category>

    <category name="Reporting">
      <error name="NullReportRequest" errorcode="ReportingServiceNullReportRequest" code="2001" message="The specified report request is null. Please submit a valid report request."/>
      <error name="UnknownReportType" errorcode="ReportingServiceUnknownReportType" code="2002" message="The specified report request type is invalid. Please submit a report request with a valid type."/>
      <error name="AccountNotAuthorized" errorcode="ReportingServiceAccountNotAuthorized" code="2003" message="The specified report request contains at least one account which you have insufficient privileges to access. Please submit a report request for accounts that you are authorized to access."/>
      <error name="NoCompleteDataAvaliable" errorcode="ReportingServiceNoCompleteDataAvaliable" code="2004" message="The specified report request indicates that complete data is required, but only partial data is currently available. Please try again later or submit a report request for partial data."/>
      <error name="InvalidDataAvailabilityAndTimePeriodCombination" errorcode="ReportingServiceInvalidDataAvailabilityAndTimePeriodCombination" code="2005" message="The specified report request contains a report time period for which complete data cannot be returned."/>
      <error name="InvalidReportName" errorcode="ReportingServiceInvalidReportName" code="2006" message="The specified report request contains an invalid report name. Please submit a report request with a valid friendly name for identifying the report."/>
      <error name="InvalidReportAggregation" errorcode="ReportingServiceInvalidReportAggregation" code="2007" message="The specified report aggregation is invalid. Please look up in the documentation the valid report aggregation values for the specified time period and report type. The Details field contains the specified report aggregation and time period"/>
      <error name="InvalidReportTimeSelection" errorcode="ReportingServiceInvalidReportTimeSelection" code="2008" message="The specified report time is invalid. Please submit a report request with a time type that contains exactly one of PredefinedTime, CustomDates, CustomDateRangeStart/End, and null for the rest."/>
      <error name="InvalidCustomDateRangeStart" errorcode="ReportingServiceInvalidCustomDateRangeStart" code="2009" message="The specified report time contains an invalid custom date range start. Please submit a report request with valid start and end dates for the custom date range."/>
      <error name="InvalidCustomDateRangeEnd" errorcode="ReportingServiceInvalidCustomDateRangeEnd" code="2010" message="The specified report time contains an invalid custom date range end. Please submit a report request with valid start and end dates for the custom date range."/>
      <error name="EndDateBeforeStartDate" errorcode="ReportingServiceEndDateBeforeStartDate" code="2011" message="The specified report time contains an invalid custom date range, where the end date is before the start date. Please submit a report request with valid start and end dates for the custom date range."/>
      <error name="EmptyCustomDates" errorcode="ReportingServiceEmptyCustomDates" code="2012" message="The specified report time contains an empty array of custom dates. Please submit a report request with valid custom dates"/>
      <error name="CustomDatesOverlimit" errorcode="ReportingServiceCustomDatesOverlimit" code="2013" message="The specified report time contains too many custom dates. The Details field contains the limit for number of custom dates"/>
      <error name="NullColumns" errorcode="ReportingServiceNullColumns" code="2014" message="The specified report request does not specify which columns to return. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report."/>
      <error name="RequiredColumnsNotSelected" errorcode="ReportingServiceRequiredColumnsNotSelected" code="2015" message="The specified report request does not specify all the required columns for this report type. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report."/>
      <error name="DuplicateColumns" errorcode="ReportingServiceDuplicateColumns" code="2016" message="The specified report request specifies duplicate columns, listed in the Details field. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report."/>
      <error name="NoMeasureSelected" errorcode="ReportingServiceNoMeasureSelected" code="2017" message="The specified report request does not specify measurement columns (at least one is required). Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report."/>
      <error name="InvalidAccountIdInCampaignReportScope" errorcode="ReportingServiceInvalidAccountIdInCampaignReportScope" code="2018" message="The specified campaign report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids."/>
      <error name="InvalidCampaignIdInCampaignReportScope" errorcode="ReportingServiceInvalidCampaignIdInCampaignReportScope" code="2019" message="The specified campaign report scope contains an invalid campaign id, listed in the Details field. Please submit a report request with valid scope and ids."/>

      <error name="InvalidAccountIdInAdGroupReportScope" errorcode="ReportingServiceInvalidAccountIdInAdGroupReportScope" code="2020" message="The specified ad group report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids."/>
      <error name="InvalidCampaignIdInAdGroupReportScope" errorcode="ReportingServiceInvalidCampaignIdInAdGroupReportScope" code="2021" message="The specified ad group report scope contains an invalid campaign id, listed in the Details field. Please submit a report request with valid scope and ids."/>
      <error name="InvalidAdGroupIdInAdGroupReportScope" errorcode="ReportingServiceInvalidAdGroupIdInAdGroupReportScope" code="2022" message="The specified ad group report scope contains an invalid ad group id, listed in the Details field. Please submit a report request with valid scope and ids."/>

      <error name="InvalidAccountIdInAccountReportScope" errorcode="ReportingServiceInvalidAccountIdInAccountReportScope" code="2023" message="The specified account report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids." />

      <error name="NullCampaignReportScope" errorcode="ReportingServiceNullCampaignReportScope" code ="2024" message="The specified campaign report scope is null. Please submit a report request with valid scope and ids." />
      <error name="NullAdGroupReportScope" errorcode="ReportingServiceNullAdGroupReportScope" code ="2025" message="The specified ad group report scope is null. Please submit a report request with valid scope and ids." />
      <error name="InvalidAccountReportScope" errorcode="ReportingServiceInvalidAccountReportScope" code="2026" message="The specified account report scope does not contain account ids. Please submit a report request with valid scope and ids."/>
      <error name="InvalidAccountThruCampaignReportScope" errorcode="ReportingServiceInvalidAccountThruCampaignReportScope" code="2027" message="The specified account through campaign report scope does not contain valid values. Please submit a report request with valid scope and ids."/>
      <error name="InvalidAccountThruAdGroupReportScope" errorcode="ReportingServiceInvalidAccountThruAdGroupReportScope" code="2028" message="The specified account through ad group report scope does not contain valid values. Please submit a report request with valid scope and ids."/>

      <error name="AccountsOverLimit" errorcode="ReportingServiceAccountsOverLimit" code="2029" message="The specified report scope contains too many accounts. The Details field contains the limit for number of accounts.	"/>

      <error name="CampaignsOverLimit" errorcode="ReportingServiceMaximumCampaignsLimitReached"  code ="2030"  message="The specified report scope contains too many campaigns. The Details field contains the limit for number of campaigns.	"/>
      <error name="AdGroupsOverLimit" errorcode="ReportingServiceAdGroupsOverLimit" code="2031" message="The specified report scope contains too many ad groups. The Details field contains the limit for number of ad groups.	"/>
      <error name="CrossSiteScriptNotAllowed" errorcode="ReportingServiceCrossSiteScriptNotAllowed" code="2032" message="The specified report request contains at least one string with embedded script. The Details field contains such string(s). Please remove the script inside the string(s) and submit the report request again."/>
      <error name="InvalidKeywordFilterValue" errorcode="ReportingServiceInvalidKeywordFilterValue" code="2033" message="The specified report request contains a Keyword filter where at least one of the values is null, empty or contains an invalid keyword. Please submit a report request with valid Keyword filter values."/>
      <error name="InvalidTimePeriodColumnForSummaryReport" errorcode="ReportingServiceInvalidTimePeriodColumnForSummaryReport" code="2034" message="The specified report request contains a TimePeriod column which is incompatible with the requested Summary report aggregation. Please submit a report request with valid report columns for a summary report."/>
      <error name="InvalidAccountIds " errorcode="ReportingServiceInvalidAccountIds " code="2035" message="The specified report request contains at least one account id which is invalid. The Details field contains a comma-separated list of the invalid account ids. Please submit a report request with valid account ids."/>
      <error name="BehavioralIdOverLimit" code="2036" errorcode="ReportingServiceBehavioralIdMaxArraySizeReached" message="The specified report scope contains too many Behavioral Ids. The Details field contains the limit for number of Behavioral Ids.  "/>
      <error name="InvalidBehavioralIdValue" code="2037" errorcode="ReportingServiceInvalidBehavioralIdValue" message="The specified report request contains a BehavioralId filter where at least one of the values is empty or contains an invalid BehavioralId. Please submit a report request with valid BehavioralId filter values."/>
      <error name="SiteIdOverLimit" code="2038" errorcode="ReportingServiceSiteIdMaxArraySizeReached" message="The specified report scope contains too many Site Ids. The Details field contains the limit for number of sites.       "/>
      <error name="InvalidSiteIdValue" code="2039" errorcode="ReportingServiceInvalidSiteIdValue" message="The specified report request contains a SiteId filter where at least one of the values is empty or contains an invalid SiteId. Please submit a report request with valid SiteId filter values."/>
      <error name="InvalidCustomDateRange" errorcode="ReportingServiceInvalidCustomDateRange" code="2040" message="The specified report time contains an invalid date in custom date range. Please submit a report request with valid start and end dates for the custom date range."/>
      <error name="StartDateCannotBeInFuture" errorcode="ReportingServiceInvalidFutureStartDate" code="2041" message="The specified report time contains an invalid custom date range, where start date is in future. Please submit a report request with valid start dates for the custom date range."/>

      <error name="InvalidReportId" errorcode="ReportingServiceInvalidReportId" code="2100" message="The specified report id is invalid. Please use a valid report id."/>
      <error name="ReportNotFound" errorcode="ReportingServiceReportNotFound" code="2101" message="The specified report was not found. It may have been expired. Please use a different report id, or submit a new report request and download it in a timely manner when it is complete."/>

      <error name="NullStreamData" errorcode="UploadFileNullStreamData" code="2200" message="The provided Stream data in request is null."/>
      <error name="ReadStreamError" errorcode="UploadFileReadStreamError" code="2201" message="Error occurred while reading from the provided stream."/>
    </category>
    <category name="Research">
    </category>
    <category name="Feed">
      <!-- Feed Item Schedule Errors 6300 - 6399 -->
      <error name="FeedItemScheduleInvalidStartTime" errorcode="CampaignServiceFeedItemScheduleInvalidStartTime" code="6300" message="Start date cannot be earlier than today."/>
      <error name="FeedItemScheduleInvalidEndTime" errorcode="CampaignServiceFeedItemScheduleInvalidEndTime" code="6301" message="The End time cannot be earlier than today"/>
    </category>
    <category name="CustomerListItem">
      <error name="InvalidCustomerListId" errorcode="InvalidCustomerListId" code="8000" message="Invalid CustomerList Id"/>
      <error name="InvalidCustomerListActionType" errorcode="InvalidCustomerListActionType" code="8001" message="The Customer List Action Type can only be Add, Replace, or Delete."/>
      <error name="CustomerListItemSubTypeNotSupported" errorcode="CustomerListItemSubTypeNotSupported" code="8002" message="The Customer List Item sub type is not supported."/>
      <error name="TermsAndConditionsNotAccepted" errorcode="CustomerListTermsAndConditionsNotAccepted" code="8003" message="The Customer Match 'Terms And Conditions' are not yet Accepted in the Microsoft Advertising web UI. Please visit https://go.microsoft.com/fwlink/?linkid=2126222 for details."/>
      <error name="EmailMustBeHashed" errorcode="EmailMustBeHashed" code="8004" message="Email address can't be in plain text and must be hashed."/>
      <error name="CRMIdLengthExceedLimitation" errorcode="CRMIdLengthExceedLimitation" code="8005" message="CRM Id exceeds the limitation of 100 characters."/>
      <error name="EitherEmailOrCRMIDShouldBeProvided" errorcode="EitherEmailOrCRMIDShouldBeProvided" code="8006" message="Either email address or CRM Id should be provided."/>
	    <error name="CustomerListItemsArrayExceedsLimit" errorcode="CustomerListItemsArrayExceedsLimit" code="8007" message="Customer List Items Array Exceeds Limit."/>
	  </category>
    <category name="CombinedList">
      <error name="CombinationRulesNullOrEmpty" errorcode="CombinationRulesNullOrEmpty" code="8100" message="Combination rules are required."/>
      <error name="CombinationRulesExceedsLimit" errorcode="CombinationRulesExceedsLimit" code="8101" message="The number of combination rules exceeds the maximum per combined list or per combination relationship."/>
      <error name="NoAudienceSelected" errorcode="NoAudienceSelected" code="8102" message="No audience is selected for the CombinedListAudience."/>
      <error name="InvalidCombinationRuleOperator" errorcode="InvalidCombinationRuleOperator" code="8103" message="The operator of Combination rule is invalid."/>
      <error name="OnlyNotCombinationUnsupported" errorcode="OnlyNotCombinationUnsupported" code="8104" message="If you combine audiences using the NOT relationship, you must also combine audiences using the AND relationship or the OR relationship."/>
      <error name="AndCombinationUnsupportedForAudienceType" errorcode="AndCombinationUnsupportedForAudienceType" code="8105" message="The audience type cannot be combined using the AND relationship."/>
      <error name="OrCombinationUnsupportedForAudienceType" errorcode="OrCombinationUnsupportedForAudienceType" code="8106" message="The audience type cannot be combined using the OR relationship."/>
      <error name="NotCombinationUnsupportedForAudienceType" errorcode="NotCombinationUnsupportedForAudienceType" code="8107" message="The audience type cannot be combined using the NOT relationship."/>
      <error name="SimilarAudienceCanOnlyBeInSingleORSet" errorcode="SimilarAudienceCanOnlyBeInSingleORSet" code="8108" message="You can only include one Similar Audience per combined list, and only by using the OR relationship without any other audiences."/>
      <error name="CustomerListsCanOnlyBeCombinedWithOtherCustomerLists" errorcode="CustomerListsCanOnlyBeCombinedWithOtherCustomerLists" code="8109" message="Customer Lists cannot be combined with other audience types."/>
      <error name="AudienceUsedByCombinedListCannotBeDeleted" errorcode="AudienceUsedByCombinedListCannotBeDeleted" code="8110" message="The audience cannot be deleted because it is used by a Combined List."/>
      <error name="AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList" errorcode="AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList" code="8111" message="The audience cannot be deleted because its paired Similar Audience is used by a Combined List."/>
      <error name="CombinedListCanOnlyBeEditedByOwner" errorcode="CombinedListCanOnlyBeEditedByOwner" code="8113" message="A Combined List can only be edited by its owner."/>
      <error name="CombinedListCanOnlyBeDeletedByOwner" errorcode="CombinedListCanOnlyBeDeletedByOwner" code="8114" message="A Combined List can only be deleted by its owner."/>
      <error name="CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience" errorcode="CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience" code="8115" message="Custom Audiences cannot be combined with other audience types."/>
      <error name="InvalidCombinationRule" errorcode="InvalidCombinationRule" code="8116" message="The Combination Rule is invalid."/>
      <error name="ImpressionTrackingUrlInvalid" errorcode="ImpressionTrackingUrlInvalid" code="8117" message="The Impression Tracking Url is invalid."/>
      <error name="ImpressionTrackingUrlsExceedMaxCount" errorcode="ImpressionTrackingUrlsExceedMaxCount" code="8118" message="The Impression Tracking Url count exceed the max count."/>
      <error name="CombinedAudienceForLocationNotAllowed" errorcode="CombinedAudienceForLocationNotAllowed" code="8119" message="You cannot associate any combination of customer list or custom audience with campaigns or ad groups that target the European Union or locations with similar restrictions."/>
      <error name="ImpressionTrackingUrlInaccessible" errorcode="ImpressionTrackingUrlInaccessible" code="8120" message="The Impression Tracking Url is inaccessible."/>
      <!-- Verified Impression Tracking (range = 8121 - 8121)-->>
      <error name="VerifiedTrackingDataInvalid" errorcode="VerifiedTrackingDataInvalid" code="8121" message="The verified tracking data is invalid."/>
      <error name="AccountNotEnabledForImageAutoRetrieve" errorcode="AccountNotEnabledForImageAutoRetrieve" code="8122" message="The account is not enabled for image auto-retrieval from a website domain."/>
      <error name="CustomerNotEligibleForRemarketingListBasedParameters" errorcode="CustomerNotEligibleForRemarketingListBasedParameters" code="8123" message="the customer is not eligible for remarketing list based on new parameters"/>
    </category>
    <category name="CampaignConversionGoal">
      <error name="AccountNotEligibleForCampaignConversionGoal" errorcode="AccountNotEligibleForCampaignConversionGoal" code="8131" message="The account is not eligible for CampaignConversionGoal."/>
      <error name="StoreVisitNotSupportForCampaignConversionGoal" errorcode="StoreVisitNotSupportForCampaignConversionGoal" code="8132" message="InStoreVisit Goal is not supported for CampaignConversionGoal."/>
      <error name="DuplicateCampaignConversionGoal" errorcode="DuplicateCampaignConversionGoal" code="8133" message="CampaignConversionGoal is dupclicate."/>
      <error name="CampaignConversionGoalNotExist" errorcode="CampaignConversionGoalNotExist" code="8134" message="CampaignConversionGoal is not exist."/>
      <error name="InvalidCampaignConversionGoalSubType" errorcode="InvalidCampaignConversionGoalSubType" code="8135" message="The CampaignConversionGoal SubType is invalid."/>
      <error name="EnableMMAUnderDSAAdgroupsValueInvalid" errorcode="EnableMMAUnderDSAAdgroupsValueInvalid" code="8136" message="Value for setting Enable MMA Under DSA AdGroup is invalid."/>
      <error name="InvalidSmartListingStatus" errorcode="InvalidSmartListingStatus" code="8137" message="The smart listing status is invalid for the current operation."/>
	    <error name="CampaignNotEligibleForCampaignConversionGoal" errorcode="CampaignNotEligibleForCampaignConversionGoal" code="8138" message="The campaign is not eligible for campaign level goal" />
    </category>
    <category name="Boost">
      <error name="CallToActionNotSupported" errorcode="CallToActionNotSupported" code="8139" message="The CallToAction is not supported." />
      <error name="CallToActionCallToActionLanguagePairNotSupported" errorcode="CallToActionCallToActionLanguagePairNotSupported" code="8140" message="The CallToAction and CallToActionLanguage pair is not supported." />
      <error name="CustomerIsNotAllowedForHotSpots" errorcode="CustomerIsNotAllowedForHotSpots" code="8141" message="Customer is not allowed for hot spots." />
      <error name="InvalidHotSpot" errorcode="InvalidHotSpot" code="8142" message="Invalid HotSpot." />
      <error name="HotSpotMissingRequiredField" errorcode="HotSpotMissingRequiredField" code="8143" message="HotSpot is missing required field." />
      <error name="InvalidGlyph" errorcode="InvalidGlyph" code="8144" message="Invalid Glyph." />
      <error name="HotSpotsHasDuplication" errorcode="HotSpotsHasDuplication" code="8145" message="HotSpots has duplication." />
      <error name="InvalidPlacement" errorcode="InvalidPlacement" code="8146" message="Invalid Placement." />
      <error name="DuplicatePlacement" errorcode="DuplicatePlacement" code="8147" message="Duplicate Placement." />
      <error name="InvalidBoostAnchors" errorcode="InvalidBoostAnchors" code="8148" message="Invalid InvalidBoostAnchors." />
      <error name="UnsupportedCampaignTypeForBoostAccount" errorcode="UnsupportedCampaignTypeForBoostAccount" code="8149" message="Unsupported campaign type for Boost account." />
      <error name="AccountNotEnabledForCampaignAutomatedCallToActionOptOut" errorcode="AccountNotEnabledForCampaignAutomatedCallToActionOptOut" code="8150" message="Account not enabled for automated CallToAction." />
      <error name="BidAdjustmentNotSupportedForBoostAccount" errorcode="BidAdjustmentNotSupportedForBoostAccount" code="8151" message="Bid Adjustment not supported for Boost account." />
      <error name="NetflixTCAcceptedValueInvalid" errorcode="NetflixTCAcceptedValueInvalid" code="8152" message="Invalid NetflixTCAccepted value."/>
      <error name="AccountNotEnabledForBroadMatchOnlyCampaign" errorcode="AccountNotEnabledForBroadMatchOnlyCampaign" code="8153" message="Account not enabled for broad match only campaign."/>
      <error name="BidStrategyNotSupportedForBroadMatchOnlyCampaign" errorcode="BidStrategyNotSupportedForBroadMatchOnlyCampaign" code="8154" message="Bid Strategy not supported for broad match only campaign."/>
      <error name="UnsupportMatchTypeForBroadMatchOnlyCampaign" errorcode="UnsupportMatchTypeForBroadMatchOnlyCampaign" code="8155" message="Keyword Match Type not supported for broad match only campaign."/>
      <error name="CampaignAssociationsLimitExceeded" errorcode="CampaignAssociationsLimitExceeded" code="8200" message="Campaign Associations limit exceeded."/>
      <error name="InvalidAssociation" errorcode="InvalidAssociation" code="8201" message="Invalid Association."/>
      <error name="SeasonalityAdjustmentTimestampMismatch" errorcode="SeasonalityAdjustmentTimestampMismatch" code="8202" message="Seasonality Adjustment timestamp mismatch."/>
      <error name="EntitiesAreNullOrEmpty" errorcode="EntitiesAreNullOrEmpty" code="8203" message="Entities are null or empty."/>
      <error name="OperationsBatchLimitExceeded" errorcode="OperationsBatchLimitExceeded" code="8204" message="Operations batch limit exceed."/>
      <error name="NameIsEmpty" errorcode="NameIsEmpty" code="8205" message="SeasonalityAdjustment/DataExclusion name is empty."/>
      <error name="NameExceededMaxLen" errorcode="NameExceededMaxLen" code="8206" message="SeasonalityAdjustment/DataExclusion name exceeded max length."/>
      <error name="DescriptionIsNull" errorcode="DescriptionIsNull" code="8207" message="Description is null."/>
      <error name="DescriptionExceededMaxLen" errorcode="DescriptionExceededMaxLen" code="8208" message="Description exceeded max length."/>
      <error name="InvalidAdjustmentPercentage" errorcode="InvalidAdjustmentPercentage" code="8209" message="Invalid adjustment percentage."/>
      <error name="DateShouldNotBeNull" errorcode="DateShouldNotBeNull" code="8210" message="Date should not be null."/>
      <error name="DateGranularityCanOnlyBeToHours" errorcode="DateGranularityCanOnlyBeToHours" code="8211" message="Date granularity can only be to hours."/>
      <error name="InvalidDateRange" errorcode="InvalidDateRange" code="8212" message="Invalid date range."/>
      <error name="DeviceTypeFilterCannotBeNone" errorcode="DeviceTypeFilterCannotBeNone" code="8213" message="Device type filter cannot be none."/>
      <error name="InvalidCampaignAssociationsAndCampaignTypeFilterCombination" errorcode="InvalidCampaignAssociationsAndCampaignTypeFilterCombination" code="8214" message="Invalid CampaignAssociations and CampaignTypeFilter combination."/>
      <error name="InvalidCampaignAssociationsLength" errorcode="InvalidCampaignAssociationsLength" code="8215" message="Invalid CampaignAssociations length."/>
      <error name="InvalidCampaignType" errorcode="InvalidCampaignType" code="8216" message="Invalid Campaign type."/>
      <error name="SeasonalityAdjustmentExceedLimit" errorcode="SeasonalityAdjustmentExceedLimit" code="8217" message="Seasonality adjustment exceed limit."/>
      <error name="InvalidDataExclusionId" errorcode="InvalidDataExclusionId" code="8218" message="Invalid DataExclusion id."/>
      <error name="DataExclusionTimestampMismatch" errorcode="DataExclusionTimestampMismatch" code="8219" message="DataExclusion timestamp mismatch."/>
      <error name="DataExclusionAdjustmentPercentageShouldBeZero" errorcode="DataExclusionAdjustmentPercentageShouldBeZero" code="8220" message="DataExclusion adjustment percentage should be zero."/>
      <error name="DataExclusionExceedLimit" errorcode="DataExclusionExceedLimit" code="8221" message="DataExclusion exceed limit."/>
      <error name="StartDateComesAfterEndDate" errorcode="StartDateComesAfterEndDate" code="8222" message="StartDate comes after EndDate."/>
      <error name="DuplicateItemsInBatch" errorcode="DuplicateItemsInBatch" code="8223" message="Duplicate items in batch."/>
      <error name="InvalidSeasonalityAdjustmentId" errorcode="InvalidSeasonalityAdjustmentId" code="8224" message="Invalid SeasonalityAdjustment id."/>
    </category>
    <category name="ConversionGoal">
      <error name="CustomerNotEligibleForProductConversionGoal" errorcode="CustomerNotEligibleForProductConversionGoal" code="8225" message="The customer is not eligible for Product Conversion Goal."/>
      <error name="CustomerNotEligibleForInStoreVisitGoal" errorcode="CustomerNotEligibleForInStoreVisitGoal" code="8226" message="The customer is not eligible for InStoreVisit Goal."/>
      <error name="OnlyOneInStoreVisitGoalBeAllowedPerCustomer" errorcode="OnlyOneInStoreVisitGoalBeAllowedPerCustomer" code="8227" message="Each customer can have only one InStoreVisit Goal."/>
      <error name="OnlyOneSmartGoalBeAllowedPerAccount" errorcode="OnlyOneSmartGoalBeAllowedPerAccount" code="8228" message="Each account can have only one Smart Goal."/>
      <error name="GoalIsReadOnly" errorcode="GoalIsReadOnly" code="8229" message="This goal cannot be modified."/>
      <error name="SmartGoalCouldNotBeEditInSomeParameters" errorcode="SmartGoalCouldNotBeEditInSomeParameters" code="8230" message="Editing the Smart Goal is restricted."/>
      <error name="AttributionModelTypeNotApplicableToGoalType" errorcode="AttributionModelTypeNotApplicableToGoalType" code="8231" message="The selected attribution model type is incompatible with this Goal type."/>
      <error name="DuplicateGoalName" errorcode="DuplicateGoalName" code="8232" message="The goal name is already in use by another goal."/>
      <error name="UnsupportedAudienceTargetForDeal" errorcode="UnsupportedAudienceTargetForDeal" code="8234" message="Unsupported audience target for deal"/>
    </category>
      <category name="CustomSegment">
          <!-- (range = 8235 - 8245)-->>
          <error name="ExceedMaxCustomSegmentCriterionCountPerAdGroup" errorcode="ExceedMaxCustomSegmentCriterionCountPerAdGroup" code="8235" message="Exceed Max Custom Segment Criterion Count Per AdGroup."/>
          <error name="CustomSegmentOnlySupportAudienceCampaign" errorcode="CustomSegmentOnlySupportAudienceCampaign" code="8236" message="Custom Segment Only Support Audience Campaign."/>
          <error name="CustomerIsNotEligibleForKeywordTargeting" errorcode="CustomerIsNotEligibleForKeywordTargeting" code="8237" message="Customer Is Not Eligible For Keyword Targeting."/>
          <error name="NegativeAdGroupCriterionIsNotSupportedByCustomSegment" errorcode="NegativeAdGroupCriterionIsNotSupportedByCustomSegment" code="8238" message="NegativeAdGroupCriterion Is Not Supported By CustomSegment."/>
          <error name="CustomSegmentOnlySupportAccountLevel" errorcode="CustomSegmentOnlySupportAccountLevel" code="8239" message="CustomSegment only support account level."/>
          <error name="CustomSegmentNotFound" errorcode="CustomSegmentNotFound" code="8240" message="CustomSegment Not Found"/>
          <error name="InValidCustomSegmentId" errorcode="InValidCustomSegmentId" code="8241" message="InValid Custom Segment Id"/>
          <error name="DeletedCustomSegment" errorcode="DeletedCustomSegment" code="8242" message="Deleted Custom Segment"/>
		  <error name="CustomSegmentCatalogIsEmpty" errorcode="CustomSegmentCatalogIsEmpty" code="8243" message="Custom Segment Catalog Is Empty."/>
      </category>
    <category name="BrandSafety">
      <!-- (range = 8246 - 8250)-->>
      <error name="BlockedSegmentIdsInvalid" errorcode="BlockedSegmentIdsInvalid" code="8246" message="Block segment Ids are invalid" />
      <error name="AccountNotEligibleForBrandsafetyFeature" errorcode="AccountNotEligibleForBrandsafetyFeature" code="8247" message="Account is not enabled for BrandSafety" />
      <error name="DuplicatedBlockedSegmentIds" errorcode="DuplicatedBlockedSegmentIds" code="8248" message="Duplicated Block Segment Ids" />
    </category>
    <category name="AssetGroupSearchTheme">
          <!-- (range = 8250 - 8254)-->>
          <error name="SearchThemeEntityLimitExceeded" errorcode="SearchThemeEntityLimitExceeded" code="8250" message="The number of asset group search theme has exceeded the limit." />
          <error name="TooLongSearchTheme" errorcode="TooLongSearchTheme" code="8251" message="Too long search theme Name." />
          <error name="SearchThemeNameMissing" errorcode="SearchThemeNameMissing" code="8252" message="Search theme name missing." />
          <error name="SearchThemeNameHasInvalidChars" errorcode="SearchThemeNameHasInvalidChars" code="8253" message="Search theme name has invalid characters." />
          <error name="DuplicateSearchThemeName" errorcode="DuplicateSearchThemeName" code="8254" message="Duplicate search theme name" />
    </category>
    <category name="LinkedInCampaign">
      <!-- (range = 8255 - 8274)-->>
      <error name="LinkedInCampaignsNotEnabledForAccount" errorcode="LinkedInCampaignsNotEnabledForAccount" code="8255" message="The account is not enabled for LinkedIn campaigns."/>
      <error name="TrackingTemplateNotSupportedForLinkedInCampaign" errorcode="TrackingTemplateNotSupportedForLinkedInCampaign" code="8256" message="Tracking template is not supported for LinkedIn Campaigns."/>
      <error name="FinalUrlSuffixNotSupportedForLinkedInCampaign" errorcode="FinalUrlSuffixNotSupportedForLinkedInCampaign" code="8257" message="Final url suffix is not supported for LinkedIn Campaigns."/>
      <error name="UrlCustomParametersSupportedForLinkedInCampaign" errorcode="UrlCustomParametersSupportedForLinkedInCampaign" code="8258" message="Url custom paramters is not supported for LinkedIn Campaigns."/>
      <error name="AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign" errorcode="AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign" code="8259" message="Ad schedule timezone setting is not supported for LinkedIn Campaigns."/>
      <error name="AdGroupDeletionIsNotSupportedForLinkedInCampaign" errorcode="AdGroupDeletionIsNotSupportedForLinkedInCampaign" code="8260" message="Ad group is not supported for LinkedIn Campaigns."/>
      <error name="MultiLanguagesNotSupportedForLinkedInCampaign" errorcode="MultiLanguagesNotSupportedForLinkedInCampaign" code="8261" message="MultiLanguages is not supported for LinkedIn Campaign."/>
      <error name="CannotEditLinkedInCampaignLanguage" errorcode="CannotEditLinkedInCampaignLanguage" code="8262" message="Cannot edit LinkedIn Campaign's language."/>
      <error name="EntityNotAllowedForLinkedInCampaign" errorcode="EntityNotAllowedForLinkedInCampaign" code="8263" message="You cannot add this entity to a campaign of type LinkedIn."/>
      <error name="BidAdjustmentNotSupportedForLinkedInCampaign" errorcode="BidAdjustmentNotSupportedForLinkedInCampaign" code="8264" message="Bid adjustment is not supported for LinkedIn Campaigns."/>
      <error name="NotSupportedConversionGoalTypeForLinkedInCampaign" errorcode="NotSupportedConversionGoalTypeForLinkedInCampaign" code="8265" message="Specified conversion goal type is not supported for LinkedIn Campaigns."/>
      <error name="NotSupportedConversionGoalScopeForLinkedInCampaign" errorcode="NotSupportedConversionGoalScopeForLinkedInCampaign" code="8266" message="Specified conversion goal scope is not supported for LinkedIn Campaigns."/>
      <error name="NotSupportedGoalCountTypeForLinkedInCampaign" errorcode="NotSupportedGoalCountTypeForLinkedInCampaign" code="8267" message="Specified conversion goal count type is not supported for LinkedIn Campaigns."/>
      <error name="NotSupportedExcludeFromBiddingValueForLinkedInCampaign" errorcode="NotSupportedExcludeFromBiddingValueForLinkedInCampaign" code="8268" message="Specified ExcludeFromBidding value is not supported for LinkedIn Campaigns."/>
      <error name="NotSupportedGoalAttributionModelForLinkedInCampaign" errorcode="NotSupportedGoalAttributionModelForLinkedInCampaign" code="8269" message="Specified conversion goal attribution model is not supported for LinkedIn Campaigns."/>
      <error name="AdGroupStartDateCannotBeChangedForLinkedInCampaign" errorcode="AdGroupStartDateCannotBeChangedForLinkedInCampaign" code="8270" message="Change start date of adgroup is not supported for LinkedIn Campaigns that are synced to external channel"/>
      <error name="LinkedInCampaignUndeleteNotAllowed" errorcode="LinkedInCampaignUndeleteNotAllowed" code="8271" message="Cannot undelete LinkedIn Campaign"/>
      <error name="NotSupportedGoalClickLookbackWindowForLinkedInCampaign" errorcode="NotSupportedGoalClickLookbackWindowForLinkedInCampaign" code="8272" message="Specified conversion goal click lookback window is not supported for LinkedIn Campaigns."/>
      <error name="LinkedInCampaignAudienceEstimationBelowThreshold" errorcode="LinkedInCampaignAudienceEstimationBelowThreshold" code="8273" message="The audience estimation of a active LinkedIn campaign cannot less than 300."/>
      <error name="LinkedInCampaignAdInReviewCannotPause" errorcode="LinkedInCampaignAdInReviewCannotPause" code="8274" message="Can not pause ad when ad of LinkedIn Campaign is in review."/>
    </category>
    <category name="PMaxNewCustomerAcquisition">
      <error name="PmaxNewCustomerAcquisitionNotEnabled" errorcode="PmaxNewCustomerAcquisitionNotEnabled" code="8275" message="The account is not enabled for PMax new customer acquisition goal."/>
      <error name="InvalidAdditionalValue" errorcode="InvalidAdditionalValue" code="8276" message="Invalid additional value."/>
      <error name="NewCustomerAcquisitionGoalDoesNotExist" errorcode="NewCustomerAcquisitionGoalDoesNotExist" code="8277" message="The new customer acquisition goal does not exist."/>
      <error name="NewCustomerAcquisitionAudienceCountExceedsLimit" errorcode="NewCustomerAcquisitionAudienceCountExceedsLimit" code="8278" message="The count of the audience associated with the new customer acquisition goal exceeds the limit."/>
      <error name="DuplicateNewCustomerAcquisitionGoal" errorcode="DuplicateNewCustomerAcquisitionGoal" code="8279" message="One account can only have up to one new customer acquisition goal."/>
      <error name="NewCustomerAcquisitionNoPurchaseGoal" errorcode="NewCustomerAcquisitionNoPurchaseGoal" code="8280" message="You need at least one purchase conversion goal in your account before you can use new customer acquisition goal."/>
      <error name="InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode" errorcode="InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode" code="8281" message="Campaigns with new customer acquisition goal in bid higher mode must use Max Conversion Value or Target ROAS bid strategy."/>
      <error name="InvalidNewCustomerAcquisitionGoalId" errorcode="InvalidNewCustomerAcquisitionGoalId" code="8282" message="The ID for new customer acquisition goal is invalid."/>
      <error name="PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign" errorcode="PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign" code="8283" message="Only purchase campaign conversion goals are allowed for campaigns with new customer acquisition enabled."/>
      <error name="InvalidAdditionalConversionValue" errorcode="InvalidAdditionalConversionValue" code="8284" message="The new customer acquisition goal additional conversion value is invalid."/>
      <error name="AudienceAssociationRequiredForNewCustomerAcquisitionGoal" errorcode="AudienceAssociationRequiredForNewCustomerAcquisitionGoal" code="8285" message="New customer acquisition goal needs to be associated with audiences before it can be associated with campaigns"/>
      <error name="CampaignLevelAdditionalValueNotSupportedForBidOnlyMode" errorcode="CampaignLevelAdditionalValueNotSupportedForBidOnlyMode" code="8286" message="Campaign level additional value is not allowed for bid only mode."/>
    </category>

    <!--(range = 8400 - 8499)-->
    <category name="BrandKit">
      <error name="BrandKitNotEnabledForAccount" errorcode="BrandKitNotEnabledForAccount" code="8400" message="The account is not enabled for Brand Kit."/>
      <error name="InvalidBrandKitId" errorcode="InvalidBrandKitId" code="8401" message="The Brand Kit id is invalid."/>
      <error name="DuplicateInBrandKitIds" errorcode="DuplicateInBrandKitIds" code="8402" message="Duplicate in Brand Kit ids."/>
      <error name="InvalidBrandKitColorCode" errorcode="InvalidBrandKitColorCode" code="8403" message="The color code is invalid make sure to use Hex format #000000."/>
      <error name="InvalidBrandKitFontTypeface" errorcode="InvalidBrandKitFontTypeface" code="8404" message="The font typeface is invalid."/>
      <error name="InvalidBrandKitFontWeight" errorcode="InvalidBrandKitFontWeight" code="8405" message="The font weight is invalid."/>
      <error name="InvalidBrandKitFontTextAssetType" errorcode="InvalidBrandKitFontTextAssetType" code="8406" message="The font text asset type is invalid."/>
      <error name="BrandKitNameTooLong" errorcode="BrandKitNameTooLong" code="8407" message="Brand Kit name is too long."/>
      <error name="BrandKitNameMissing" errorcode="BrandKitNameMissing" code="8408" message="Brand Kit name is missing."/>
      <error name="BrandKitPaletteColorCountExceedsLimit" errorcode="BrandKitPaletteColorCountExceedsLimit" code="8409" message="Brand Kit palette has too many colors."/>
      <error name="BrandKitColorNameTooLong" errorcode="BrandKitColorNameTooLong" code="8410" message="Brand Kit color name is too long."/>
      <error name="BrandKitImagesCountExceedsLimit" errorcode="BrandKitImagesCountExceedsLimit" code="8411" message="Brand Kit Images list exceeds the limit."/>
      <error name="BrandKitSquareLogosCountExceedsLimit" errorcode="BrandKitSquareLogosCountExceedsLimit" code="8412" message="Brand Kit Square Logos list exceeds the limit."/>
      <error name="BrandKitLandscapeLogosCountExceedsLimit" errorcode="BrandKitLandscapeLogosCountExceedsLimit" code="8413" message="Brand Kit Landscape Logos list exceeds the limit."/>
      <error name="BrandKitIdsArrayShouldNotBeNullOrEmpty" errorcode="BrandKitIdsArrayShouldNotBeNullOrEmpty" code="8414" message="Invalid BrandKit Request Parameteres." />
      <error name="BrandKitColorsArrayShouldNotBeNullorEmpty" errorcode="BrandKitColorsArrayShouldNotBeNullorEmpty" code="8415" message="BrandKitColors Array is Empty or Null in Request Parameter." />
      <error name="BrandKitBusinessNameTooLong" errorcode="BrandKitBusinessNameTooLong" code="8416" message="Brand Kit Business Name is too long." />
      <error name="BrandKitPhase2NotEnabledForAccount" errorcode="BrandKitPhase2NotEnabledForAccount" code="8417" message="The account is not enabled for Brand Kit phase 2." />
      <error name="BrandVoicePersonalityTooLong" errorcode="BrandVoicePersonalityTooLong" code="8418" message="Brand Voice Personality is too long." />
      <error name="BrandVoiceTonesCountExceedsLimit" errorcode="BrandVoiceTonesCountExceedsLimit" code="8419" message="Brand Voice Tones list exceeds the limit." />
      <error name="BrandVoiceTonesTooLong" errorcode="BrandVoiceTonesTooLong" code="8420" message="One or more Brand Voice Tones are too long." />
      <error name="BrandKitPaletteNameTooLong" errorCode="BrandKitPaletteNameTooLong" code="8421" message="Brand Kit Palette Name is too long." />
    </category>

    <!--(range = 8550 - 8575)-->
    <category name="LifetimeBudget">
      <error name="AccountNotEnabledForCampaignLevelDates" errorcode="AccountNotEnabledForCampaignLevelDates" code="8550" message="The account is not enabled to use campaign level dates."/>
      <error name="CampaignLevelDatesNotEnabled" errorcode="CampaignLevelDatesNotEnabled" code="8551" message="Campaign level lifetime date selection should be made."/>
      <error name="CampaignStartDateNotSet" errorcode="CampaignStartDateNotSet" code="8552" message="Campaign start date should be set for campaigns with campaign level dates."/>
      <error name="CampaignEndDateNotSet" errorcode="CampaignEndDateNotSet" code="8553" message="Campaign end date should be set for lifetime budget campaigns."/>
      <error name="CampaignEndDateExceedsOneYear" errorcode="CampaignEndDateExceedsOneYear" code="8554" message="Campaign end date cannot be a over a year greater than the start date for lifetime budget campaigns."/>
      <error name="CannotUpdateStartDateAfterCampaignStart" errorcode="CannotUpdateStartDateAfterCampaignStart" code="8555" message="Cannot update start date after campaign has started."/>
      <error name="CannotUpdateBudgetTypeAfterCampaignStart" errorcode="CannotUpdateBudgetTypeAfterCampaignStart" code="8556" message="Cannot update budget type after campaign has started."/>
      <error name="CannotUpdateUseCampaignLevelAfterCampaignStart" errorcode="CannotUpdateUseCampaignLevelAfterCampaignStart" code="8557" message="Cannot update campaign level lifetime dates selection after campaign start."/>
      <error name="AdGroupLevelDatesBudgetTypeCannotBeUpdated" errorcode="AdGroupLevelDatesBudgetTypeCannotBeUpdated" code="8558" message="Cannot update budget type for daily budget campaigns."/>
      <error name="AdGroupLevelDatesCannotBeUpdated" errorcode="AdGroupLevelDatesCannotBeUpdated" code="8559" message="Cannot update to campaign level dates for daily budget campaigns."/>
      <error name="CampaignSubTypeNotSupportedForCampaignLevelDates" errorcode="CampaignSubTypeNotSupportedForCampaignLevelDates" code="8560" message="Cannot set campaign level dates with chosen campaign subtype."/>
      <error name="CampaignLifetimeBudgetAmountIsAboveLimit" errorcode="CampaignLifetimeBudgetAmountIsAboveLimit" code="8561" message="Lifetime budget amount limit exceeded."/>
    </category>

    <!--(range = 8576 - 8600)-->
    <category name="Clipchamp">
      <error name="AccountNotEnabledForVideoAdsGeneration" errorcode="AccountNotEnabledForVideoAdsGeneration" code="8576" message="The account is not enabled for Video Ads Generation feature."/>
      <error name="NoAudioMatchesFilter" errorcode="NoAudioMatchesFilter" code="8577" message="No Audio matches the filter."/>
    </category>

      <category name="AssetGroupUrlTarget">
          <!-- (range = 8661 - 8666)-->>
          <error name="AssetGroupUrlTargetDuplicated" errorcode="AssetGroupUrlTargetDuplicated" code="8661" message="Duplicate Asset Group Url Target." />
          <error name="AssetGroupUrlTargetValueDuplicated" errorcode="AssetGroupUrlTargetValueDuplicated" code="8662" message="The same value exists in the Asset Group Url Target." />
          <error name="AssetGroupUrlTargetConditionInvalid" errorcode="AssetGroupUrlTargetConditionInvalid" code="8663" message="There is an invalid condition in the Asset Group Url Target." />
          <error name="AssetGroupUrlTargetOperatorInvalid" errorcode="AssetGroupUrlTargetOperatorInvalid" code="8664" message="There is an invalid operator in the Asset Group Url Target." />
          <error name="AssetGroupUrlTargetValueInvalid" errorcode="AssetGroupUrlTargetValueInvalid" code="8665" message="There is an invalid value in the Asset Group Url Target." />
          <error name="AssetGroupUrlTargetInvalid" errorcode="AssetGroupUrlTargetInvalid" code="8666" message="Asset Group Url Target is invalid." />
      </category>
	  
	 <category name="Annotations">
          <!-- (range = 8667 - 8677)-->>
          <error name="InvalidExclusionTypeIdOrSubTypeId" errorcode="InvalidExclusionTypeIdOrSubTypeId" code="8667" message="The AnnotationGroupId isn’t valid." />
          <error name="AnnotationOptOutJustificationTextTooLong" errorcode="AnnotationOptOutJustificationTextTooLong" code="8668" message="You’ve exceeded the maximum JustificationText length of 100 characters." />
          <error name="AnnotationOptOutBatchLimitExceeded" errorcode="AnnotationOptOutBatchLimitExceeded" code="8669" message="The request contains more than the limit of 1,000 AnnotationOptOuts." />
          <error name="AnnotationOptOutCollectionNullOrEmpty" errorcode="AnnotationOptOutCollectionNullOrEmpty" code="8670" message="The request doesn’t contain any AnnotationOptOuts." />
          <error name="AnnotationOptOutJustificationTextNullOrEmpty" errorcode="AnnotationOptOutJustificationTextNullOrEmpty" code="8671" message="There is no JustificationText." />
          <error name="AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut" errorcode="AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut" code="8672" message="You are opted out of an annotation that the account is attempting to opt in to." />
     </category>

    <category name="LinkedInCampaignExtend">
      <!-- (range = 8678 - 8777)-->>
      <error name="ApplyLinkedInOfflineConversionFailed" errorcode="ApplyLinkedInOfflineConversionFailed" code="8678" message="The apply offline conversions operation for LinekdIn is failed."/>
      <error name="ApplyLinkedInOfflineConversionInternalError" errorcode="ApplyLinkedInOfflineConversionInternalError" code="8679" message="Failed to apply offline conversions for LinekdIn due to internal error."/>
    </category>
  </ErrorCodesByCategory>

  <!-- Section which mappes error codes to specific APIs. APIs should not return any error which is -->
  <!-- not part of this list.                                                                       -->
  <!-- Note: ErrorCodes under Category Common is automatically included to all the  APIs            -->
  <ErrorCodesByAPI>
    <category name="Reporting" description="Error codes grouped by APIs">
      <Api name="SubmitGenerateReport" description="Error codes used by SubmitGenerateReport" >
        <Apierror code="2001" />
        <Apierror code="2002" />
        <Apierror code="2003" />
        <Apierror code="2004" />
        <Apierror code="2005" />
        <Apierror code="2006" />
        <Apierror code="2007" />
        <Apierror code="2008" />
        <Apierror code="2009" />
        <Apierror code="2010" />
        <Apierror code="2011" />
        <Apierror code="2012" />
        <Apierror code="2013" />
        <Apierror code="2014" />
        <Apierror code="2015" />
        <Apierror code="2016" />
        <Apierror code="2017" />
        <Apierror code="2018" />
        <Apierror code="2019" />
        <Apierror code="2020" />
        <Apierror code="2021" />
        <Apierror code="2022" />
        <Apierror code="2023" />
        <Apierror code="2024" />
        <Apierror code="2025" />
        <Apierror code="2026" />
        <Apierror code="2027" />
        <Apierror code="2028" />
        <Apierror code="2029" />
        <Apierror code="2030" />
        <Apierror code="2031" />
        <Apierror code="2032" />
        <Apierror code="2033" />
        <Apierror code="2034" />
        <Apierror code="2035" />
        <Apierror code="2036" />
        <Apierror code="2037" />
        <Apierror code="2038" />
        <Apierror code="2039" />
        <Apierror code="2040" />
        <Apierror code="2041" />
      </Api>
      <Api name="PollGenerateReport" description="Error codes used by PollGenerateReport"  >
        <Apierror code="2100" />
        <Apierror code="2101" />
      </Api>
    </category>

    <category name="CampaignManagement" description="">
      <!-- Campaign APIs -->
      <Api name="AddCampaigns" description="" >
        <Apierror code="1003" />
        <Apierror code="1004" />
        <Apierror code="1005" />
        <Apierror code="1006" />
        <Apierror code="1032" />
        <Apierror code="1101" />
        <Apierror code="1102" />
        <Apierror code="1103" />
        <Apierror code="1104" />
        <Apierror code="1105" />
        <Apierror code="1106" />
        <Apierror code="1108" />
        <Apierror code="1109" />
        <Apierror code="1110" />
        <Apierror code="1111" />
        <Apierror code="1113" />
        <Apierror code="1114" />
        <Apierror code="1115" />
        <Apierror code="1121" />
        <Apierror code="1122" />
        <Apierror code="1175"/>
      </Api>
      <Api name="UpdateCampaigns" description="" >
        <Apierror code="1001" />
        <Apierror code="1005" />
        <Apierror code="1006" />
        <Apierror code="1008" />
        <Apierror code="1032" />
        <Apierror code="1100" />
        <Apierror code="1101" />
        <Apierror code="1102" />
        <Apierror code="1105" />
        <Apierror code="1106" />
        <Apierror code="1111" />
        <Apierror code="1113" />
        <Apierror code="1115" />
        <Apierror code="1120" />
        <Apierror code="1122" />
        <Apierror code="1501" />
      </Api>
      <Api name="DeleteCampaigns" description="" >
        <Apierror code="1100" />
        <Apierror code="1102" />
        <Apierror code="1107" />
        <Apierror code="1118" />
        <Apierror code="1119" />
        <Apierror code="1120"/>
      </Api>
      <Api name="GetCampaignsByAccountId" description="" >
        <Apierror code="1102" />
      </Api>
      <Api name="GetCampaignsByIds" description="" >
        <Apierror code="1100" />
        <Apierror code="1118" />
      </Api>
      <Api name="SetNegativeKeywordsToCampaigns" description="" >
        <Apierror code="1032" />
        <Apierror code="1033" />
        <Apierror code="1034" />
        <Apierror code="1130" />
      </Api>

      <!-- AdGroup APIs -->
      <Api name="AddAdGroups" description="" >
        <Apierror code="1003" />
        <Apierror code="1005" />
        <Apierror code="1006" />
        <Apierror code="1008" />
        <Apierror code="1032" />
        <Apierror code="1100" />
        <Apierror code="1120" />
        <Apierror code="1202"/>
        <Apierror code="1204"/>
        <Apierror code="1209"/>
        <Apierror code="1210"/>
        <Apierror code="1212"/>
        <Apierror code="1213"/>
        <Apierror code="1214" />
        <Apierror code="1220"/>
        <Apierror code="1223"/>
        <Apierror code="1224"/>
        <Apierror code="1232"/>
        <Apierror code="1233"/>
      </Api>
      <Api name="UpdateAdGroups" description="" >
        <Apierror code="1001"/>
        <Apierror code="1032" />
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1202" />
        <Apierror code="1204" />
        <Apierror code="1205" />
        <Apierror code="1209" />
        <Apierror code="1212" />
        <Apierror code="1214" />
        <Apierror code="1215" />
        <Apierror code="1217" />
        <Apierror code="1223" />
        <Apierror code="1232"/>
        <Apierror code="1233"/>
      </Api>
      <Api name="DeleteAdGroups" description="" >
        <Apierror code="1100" />
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1209" />
        <Apierror code="1217" />
        <Apierror code="1218" />
        <Apierror code="1219" />
      </Api>

      <Api name="GetAdGroupsByIds" description="">
        <Apierror code="1100"/>
        <Apierror code="1120"/>
        <Apierror code="1201"/>
        <Apierror code="1218"/>
        <Apierror code="1219"/>
      </Api>
      <Api name="GetAdGroupsByCampaignId" description="" >
        <Apierror code="1100" />
      </Api>

      <!-- Submit AdGroup -->
      <Api name="SubmitAdGroupForApproval" description="" >
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1226" />
        <Apierror code="1227" />
      </Api>
      <Api name="SetNegativeKeywordsToAdGroups" description="" >
        <Apierror code="1032" />
        <Apierror code="1033" />
        <Apierror code="1034" />
        <Apierror code="1244" />
      </Api>

      <!-- Keywords APIs -->
      <Api name="AddKeywords" description="" >
        <Apierror code="1003" />
        <Apierror code="1006"/>
        <Apierror code="1007" />
        <Apierror code="1008" />
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1215" />
        <Apierror code="1505" />
        <Apierror code="1506" />
        <Apierror code="1508" />
        <Apierror code="1509" />
        <Apierror code="1510" />
        <Apierror code="1511" />
        <Apierror code="1512"/>
        <Apierror code="1513" />
        <Apierror code="1514" />
        <Apierror code="1515" />
        <Apierror code="1516" />
        <Apierror code="1517" />
        <Apierror code="1519" />
        <Apierror code="1532" />
        <Apierror code="1533" />
      </Api>
      <Api name="UpdateKeywords" description="" >
        <Apierror code="1001" />
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1215" />
        <Apierror code="1500" />
        <Apierror code="1501" />
        <Apierror code="1502" />
        <Apierror code="1504" />
        <Apierror code="1505" />
        <Apierror code="1506" />
        <Apierror code="1508" />
        <Apierror code="1509" />
        <Apierror code="1510" />
        <Apierror code="1511" />
        <Apierror code="1512"/>
        <Apierror code="1513" />
        <Apierror code="1514" />
        <Apierror code="1515" />
        <Apierror code="1516" />
        <Apierror code="1528" />
        <Apierror code="1531" />
        <Apierror code="1532" />
        <Apierror code="1533" />
        <Apierror code="1534"/>
      </Api>
      <Api name="KeywordQualityScore" description="" >
        <Apierror code="1535" />
        <Apierror code="1536"/>
      </Api>
      <Api name="DeleteKeywords" description="" >
        <Apierror code="1008" />
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1215" />
        <Apierror code="1501" />
        <Apierror code="1502" />
        <Apierror code="1528" />
        <Apierror code="1529" />
        <Apierror code="1530" />
      </Api>
      <Api name="GetKeywordsByIds" description="" >
        <Apierror code="1201" />
        <Apierror code="1502" />
        <Apierror code="1529" />
        <Apierror code="1530" />
      </Api>
      <Api name="GetKeywordsByAdGroupId" description="" >
        <Apierror code="1120" />
        <Apierror code="1201" />
        <Apierror code="1217" />
      </Api>
      <Api name="GetKeywordEstimatesByBids" description="">
        <Apierror code="1701"/>
        <Apierror code="1702"/>
      </Api>

      <!-- Target APIs -->
      <Api name="AddTarget" description="" >
        <Apierror code="1201" />
        <Apierror code="1402" />
        <Apierror code="1403" />
        <Apierror code="1404" />
        <Apierror code="1405" />
        <Apierror code="1406" />
        <Apierror code="1407" />
        <Apierror code="1414" />
        <Apierror code="1417" />
        <Apierror code="2909" />
        <Apierror code="2910" />
      </Api>
      <Api name="UpdateTarget" description="" >
        <Apierror code="1201" />
        <Apierror code="1400" />
        <Apierror code="1401" />
        <Apierror code="1404" />
        <Apierror code="1405" />
        <Apierror code="1406" />
        <Apierror code="1407" />
        <Apierror code="1408" />
        <Apierror code="1409" />
        <Apierror code="1410" />
        <Apierror code="1411" />
        <Apierror code="1412" />
        <Apierror code="1413" />
        <Apierror code="1414" />
        <Apierror code="1417" />
        <Apierror code="2909" />
        <Apierror code="2910" />
      </Api>
      <Api name="DeleteTarget" description="" >
        <Apierror code="1201" />
      </Api>
      <Api name="GetTargetByAdGroupId" description="" >
        <Apierror code="1201" />
      </Api>
      <Api name="AddTargets" description="" >
        <Apierror code="1418" />
        <Apierror code="2909" />
        <Apierror code="2910" />
      </Api>
      <Api name="UpdateTargets" description="" >
        <Apierror code="2909" />
        <Apierror code="2910" />
      </Api>
      <Api name="SetTargetToAdGroup" description="" >
        <Apierror code="2918" />
        <Apierror code="2919" />
      </Api>
      <Api name="SetTargetToCampaign" description="" >
        <Apierror code="2919" />
      </Api>
      <!-- Ad APIs -->
      <Api name="AddAds" description="" >
        <Apierror code="1201"/>
        <Apierror code="1301"/>
        <Apierror code="1302"/>
        <Apierror code="1304" />
        <Apierror code="1306" />
        <Apierror code="1310" />
        <Apierror code="1313" />
        <Apierror code="1383" />
        <Apierror code="1384" />
        <Apierror code="1387"/>
        <Apierror code="1389"/>
        <Apierror code="1391"/>
        <Apierror code="2805"/>
      </Api>
      <Api name="DeleteAds" description="" >
        <Apierror code="1120"/>
        <Apierror code="1201"/>
        <Apierror code="1308"/>
        <Apierror code="1309"/>
        <Apierror code="1351"/>
      </Api>
      <Api name="UpdateAds" description="">
        <Apierror code="1008"/>
        <Apierror code="1120"/>
        <Apierror code="1201"/>
        <Apierror code="1217"/>
        <Apierror code="1301"/>
        <Apierror code="1302"/>
        <Apierror code="1303"/>
        <Apierror code="1306"/>
        <Apierror code="1307"/>
        <Apierror code="1309"/>
        <Apierror code="1310"/>
        <Apierror code="1313"/>
        <Apierror code="1356"/>
        <Apierror code="1360"/>
        <Apierror code="1363"/>
        <Apierror code="1383" />
        <Apierror code="1384" />
        <Apierror code="1387"/>
        <Apierror code="1389"/>
        <Apierror code="1391"/>
        <Apierror code="1403"/>
        <Apierror code="1404"/>
        <Apierror code="1405"/>
        <Apierror code="1406"/>
        <Apierror code="1407"/>
        <Apierror code="2805"/>
      </Api>
      <Api name="GetAdsByAdGroupId" description="">
        <Apierror code="1201"/>
      </Api>
      <Api name="GetAdsByIds" description="">
        <Apierror code="1201" />
        <Apierror code="1217"/>
        <Apierror code="1308"/>
      </Api>
      <!--Import API -->

      <Api name="AddImporJobs" description="">
        <Apierror code="6650" />
        <Apierror code="6651"/>
        <Apierror code="6652"/>
      </Api>

	<Api name="CampaignConversionGoal" description="">
        <Apierror code="8131" />
        <Apierror code="8132"/>
        <Apierror code="8133"/>
        <Apierror code="8134"/>
        <Apierror code="8135"/>
		<Apierror code="8138"/>
      </Api>

    <Api name="ConversionGoal" description="">
        <Apierror code="8225" />
        <Apierror code="8226"/>
        <Apierror code="8227"/>
        <Apierror code="8228"/>
        <Apierror code="8229"/>
        <Apierror code="8230"/>
        <Apierror code="8231"/>
        <Apierror code="8232"/>
    </Api>        
    </category>
  </ErrorCodesByAPI>
</ApiErrorCodes>
