{"idReplacementPattern": "(?<!LC)ID", "specialSymbols": ["<", ">", "=", "'"], "reportsNotInMetaTable": ["prc_EstimateRows_Dim", "rpt_AccountActivity", "rpt_CampaignActivity", "rpt_OrderSummary", "prc_OrderQualityScore_ui", "prc_CampaignExperimentSummary_ui", "prc_CampaignQualityScore_ui", "prc_AgeGenderSummary_ui", "rpt_GetCampaignInfo", "prc_GetAdLandingPageUrlDimensions_ui", "prc_KeywordQualityScore_ui", "rpt_KeywordAuctionSummary", "prc_AdExtensionsOrderAssociation_ui", "prc_AdExtensionsByOrderV2_ui", "prc_AssetSummary_ui", "prc_AssetCombinationSummary_ui", "prc_GetAssetTextByIds_ui", "prc_GetMeteredCallDetails", "prc_AssetAdGroupSnapshotSummary_ui", "prc_AudienceSummary_ui", "prc_AssetSnapShotSummary_ui", "prc_FeedItemAdExtensionSummary_ui", "prc_TargetSummaryV2_ui", "rpt_NegativeKeywordConflictsList", "rpt_TANegativeKeywordConflictsList", "rpt_NegativeKeywordConflictsListV2", "prc_PerformanceTargetSummary_ui", "rpt_GetAccountInfo", "prc_ProfileSummary_ui", "prc_LeadFormExtensionDetails_ui", "prc_GetProductTargetsCountsAccountCampaign_ui", "prc_GetProductOfferCountsAccountCampaign_ui", "prc_ProductGroupSummary_V1_ui", "prc_GetNegativeKeywordConflictDetails", "prc_GetNegativeKeywordConflictStatus", "prc_ProductGroupSummaryForAccountCampaignOrder_ui", "prc_GetAccountQualityScore", "prc_GetLastLoadCompleteTime", "prc_GetProcedureDetails", "prc_GetTaskStatus_ui", "prc_GetSearchPhraseSummary", "prc_GetSearchQuerySummary", "prc_GetPGCriteriaSelectionSummary_Perf_ui", "rpt_GetChangeHistorySummary", "rpt_GetEntityValuesByEntityIds", "rpt_UserRoleChangeHistory", "prc_GetChangeHistorySummaryByUser", "prc_GetChangeHistorySummary_ui", "prc_GetChangeHistorySummaryCount_ui", "prc_GetEntityValuesByEntityIds", "prc_GetUsersOfChanges_V3", "prc_UpsertUndoneChange", "prc_GetCampaignConvData", "prc_GetBidSuggestion", "prc_GetAutoTargetWebsiteCoverage", "prc_GetAudienceRecommendation", "prc_GetAssetTextByAccountIdAssetId_v2", "prc_CheckAccountFactDataExists", "rpt_SearchInsightSummary", "rpt_SearchVerticalCategoryClickShareReport", "rpt_SearchVerticalCategoryInsightsReport", "prc_PublicLatestCompleteDTimLoad", "prc_UpliftSummary_ui"], "nonColumns": ["case", "when", "then", "null", "else", "end", "as", "is", "not"], "deprecatedColumns": ["MaxSearchCPC", "MaxContentCPC", "UserDim1Name", "UserDim2Name", "UserDim3Name", "UserDim4Name", "UserDim5Name"], "dimTablePrefixNotCreateView": ["v", "#", "<", "AllDaily", "UserDefined", "DailyBidSuggestion"], "dimTableNameNotEndWithDim": ["vAdExtension"]}