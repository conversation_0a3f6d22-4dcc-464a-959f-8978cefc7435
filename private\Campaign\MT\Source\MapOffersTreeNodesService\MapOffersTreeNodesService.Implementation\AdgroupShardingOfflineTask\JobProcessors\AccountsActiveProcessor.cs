﻿namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using GenericFramework.QueueProcessorRuntime;
    using Microsoft.AdCenter.Shared.MT;
    using System.Data.Common;
    using System.Data;
    using Microsoft.AdCenter.Shared.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;
    using ConnectionResolvers;

    class AccountsActiveProcessor : ShardEntityJobProcessorBase
    {
        private const string AccountsActiveTableTypeName = "AccountsActiveTableType";

        public override async Task<JobResult> ProcessAsync(
            ILogShared logger, 
            JobBatch batch, 
            IJobBatchRequestContext requestCtxt)
        {
            // Starting main DB tasks
            IEnumerable<Task<QueryTaskResult<IList<AccountsActiveItem>>>> mainQueryTasks = 
                requestCtxt.MainConnectionResolver.ExecuteTasks<QueryTaskResult<IList<AccountsActiveItem>>>(
                (conn, jobs) => StartQueryDBTask(logger, conn, jobs));

            QueryTaskResult<IList<AccountsActiveItem>>[] mainQueryResults =
                await Task.WhenAll(mainQueryTasks).ConfigureAwait(false);

            IList<AccountsActiveItem> mergedItems = MergeAccountsActiveItems(mainQueryResults);

            var shardUpdatingTasks = new List<Task<NonQueryTaskResult>>();

            // Starting shard DB tasks
            shardUpdatingTasks.AddRange(requestCtxt.CampaignTaskShardConnectionResolver.ExecuteCampaignTasks<NonQueryTaskResult>((conn, modValue, jobs) =>
            {
                return StartUpdatingDBTask(logger, conn, mergedItems, jobs);
            }));

            NonQueryTaskResult[] shardUpdatingResults = await Task.WhenAll(shardUpdatingTasks).ConfigureAwait(false);

            var failedMainQueryJob = new HashSet<Job>(mainQueryResults.Where(result => !result.Success).SelectMany(result => result.Jobs));

            if ( mainQueryResults.All(result => result.Success) && shardUpdatingResults.All(result => result.Success) )
            {
                return JobResult.Successful;
            }
            else if (mainQueryResults.All(result => !result.Success) 
                || shardUpdatingResults.All(result => !result.Success || result.Jobs.All(job => failedMainQueryJob.Contains(job))))
            {
                return JobResult.Failed;
            }
            else
            {
                var mainFailedJobIds = mainQueryResults.Where(result => !result.Success)
                    .SelectMany(result => result.Jobs)
                    .Select(job => job.Id);
                var shardFailedJobIds = shardUpdatingResults.Where(result => !result.Success)
                    .SelectMany(result => result.Jobs)
                    .Select(job => job.Id);
                var failedJobIds = new HashSet<int>(mainFailedJobIds.Union(shardFailedJobIds));
                return new OfflineTaskJobResult(failedJobIds);
            }
        }

        private IList<AccountsActiveItem> MergeAccountsActiveItems(QueryTaskResult<IList<AccountsActiveItem>>[] accountActiveResults)
        {
            List<AccountsActiveItem> list = new List<AccountsActiveItem>(); ;
            foreach (var result in accountActiveResults)
            {
                if (!result.Success) continue;
                list.AddRange(result.QueryResult);
            }
            return list;
        }

        private async Task<NonQueryTaskResult> StartUpdatingDBTask(
            ILogShared logger, IConnectionProvider connection, IList<AccountsActiveItem> items,
            IList<Job> jobs)
        {
            var accountIdSetInThisShard = new HashSet<int>(jobs.Select(job => job.AccountId));
            var itemsInThisShard = new List<AccountsActiveItem>();
            foreach (var item in items)
            {
                if (accountIdSetInThisShard.Contains(item.AccountId))
                {
                    itemsInThisShard.Add(item);
                }
            }

            if (itemsInThisShard.Count == 0)
            {
                return new NonQueryTaskResult{ Success = true };
            }

            DataTable accountsActiveTable = CreateAccountsActiveTypeDataTable(itemsInThisShard);
            var procName = "prc_PublicSetStatusOnAccountsActive";
            var command = SPHelper.MakeSqlCommandWithTracking(
                procName,
                logger.CallTrackingData, CampaignDbQueueStore.SourceId);
            command.Parameters.Add(SpWrappers.TableValuedParam("@AccountsActive", accountsActiveTable));

            bool success = await CampaignDbQueueStore.ExecuteNonQuerySqlCommandAsync(
                logger, command, connection).ConfigureAwait(false);

            return new NonQueryTaskResult
            {
                Success = success,
                Jobs = jobs
            };
        }

        public static DataTable CreateAccountIdsTableTypeDataTable()
        {
            var dataTable = new DataTable("AccountIDListTableType");
            dataTable.Columns.Add("LineItemID", typeof(int));
            dataTable.Columns.Add("AccountID", typeof(int));
            return dataTable;
        }

        public static DataTable CreateAccountIdsTableTypeDataTable(HashSet<int> accountIds)
        {
            var dataTable = CreateAccountIdsTableTypeDataTable();
            int i = 0;
            foreach (var accountId in accountIds)
            {
                var row = dataTable.NewRow();

                row["LineItemID"] = i;
                row["AccountID"] = (int)accountId;

                dataTable.Rows.Add(row);
                i++;
            }

            return dataTable;
        }

        private async Task<QueryTaskResult<IList<AccountsActiveItem>>> StartQueryDBTask(
            ILogShared logger, IConnectionProvider connection, IEnumerable<Job> jobs)
        {
            var accountIds = new HashSet<int> (jobs.Select(job => job.AccountId));

            DataTable accountIdsTable = CreateAccountIdsTableTypeDataTable(accountIds);

            var sqlCommand = SPHelper.MakeSqlCommandWithTracking(
                "prc_PublicGetStatusFromAccountsActive",
                logger.CallTrackingData, CampaignDbQueueStore.SourceId);
            sqlCommand.Parameters.Add(SpWrappers.TableValuedParam("@AccountIDs", accountIdsTable));

            DbDataReader dbReader = await CampaignDbQueueStore.ExecuteSqlCommandAsync(
                logger, sqlCommand, connection).ConfigureAwait(false);
            if (dbReader == null)
            {
                return new QueryTaskResult<IList<AccountsActiveItem>>() { Success = false };
            }

            var taskResult = new QueryTaskResult<IList<AccountsActiveItem>>()
            {
                Success = true,
                QueryResult = new List<AccountsActiveItem>()
            };

            using (dbReader)
            {
                var isActiveOrdinal = DBColumnName.IsActiveOrdinal.GetOrdinal(dbReader);
                var lastProcessIdOrdinal = DBColumnName.LastUpdatedProcessIdOrdinal.GetOrdinal(dbReader);
                var accountIdOrdinal = DBColumnName.AccountIdOrdinal.GetOrdinal(dbReader);
                while (await dbReader.ReadAsync().ConfigureAwait(false))
                {
                    var item = new AccountsActiveItem();

                    item.AccountId = dbReader.GetInt32(accountIdOrdinal);
                    item.IsActive = dbReader.GetInt32(isActiveOrdinal) == 0  ? false : true;

                    item.LastUpdatedProcessId = dbReader.IsDBNull(lastProcessIdOrdinal)
                        ? (long?)null : (long?)dbReader.GetInt64(lastProcessIdOrdinal);

                    taskResult.QueryResult.Add(item);
                }
            }
            return taskResult;
        }

        private static DataTable CreateAccountsActiveTypeDataTable(IEnumerable<AccountsActiveItem> accountsActiveItems)
        {
            var table = new DataTable(AccountsActiveTableTypeName);
            table.Columns.Add(DBColumnName.AccountId, typeof(int));
            table.Columns.Add(DBColumnName.IsActive, typeof(byte));
            table.Columns.Add(DBColumnName.LastUpdatedProcessId, typeof(long));

            foreach (AccountsActiveItem item in accountsActiveItems)
            {
                DataRow row = table.NewRow();
                row[DBColumnName.AccountId] = item.AccountId;
                row[DBColumnName.IsActive] = (byte)(item.IsActive ? 1 : 0);

                 if (item.LastUpdatedProcessId.HasValue)
                    row[DBColumnName.LastUpdatedProcessId] = item.LastUpdatedProcessId.Value;
                else
                    row[DBColumnName.LastUpdatedProcessId] = DBNull.Value;

                table.Rows.Add(row);
            }

            return table;
        }

        private class AccountsActiveItem
        {
            public int AccountId;

            public bool IsActive;

            public long? LastUpdatedProcessId;

        }

    }
}
