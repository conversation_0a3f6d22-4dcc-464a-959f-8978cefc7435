﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.EventTracking
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using CampaignServiceData.EO.EventTracking.Clarity;
    using Common;
    using Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.BingAds.TaskEngine.ObjectModel;
    using Microsoft.BingAds.TaskEngine.Management.Services;
    using EventTracking = Entities.EventTracking;
    using CustomerFeatureFlag = ClientCenter.CustomerFeatureFlag;

    public partial class EventTrackingEO
    {
        public BatchResult<long> AddGoals(CustomerCallContext context, EventTracking.Goal[] goals, bool limitCheck = false, bool ignoreLock = false)
        {
            var batchResult = new BatchResult<long>();
            var existedGoalsNameCollection = new HashSet<String>(StringComparer.OrdinalIgnoreCase);

            ValidateCustomerId(context.Request, batchResult);
            ValidateAccountId(context.Request, batchResult);

            if (batchResult.HasOperationErrors)
            {
                return batchResult;
            }

            if (DynamicConfigValues.EnableMultiAgency &&
                !ValidateAgencyLinkForGoal(context.Request, goals, batchResult, context.Logger))
            {
                return batchResult;
            }

            GetPilotingFeatures(context, out var PilotOfCustomerDict);

            if (DynamicConfigValues.EnableGoalNullableProperties)
            {
                // If goal type is null, we fill it using class name.
                foreach (var goal in goals)
                {
                    FillGoalEntityTypeUsingODataType(goal);
                }
            }

            GoalValidator.VerifyGoalType(goals, batchResult);
            GoalValidator.VerifyGoalCategoryForGoalType(goals, batchResult);

            var existedGoalsCollection = GetGoalsByCustomerIdAccountId(context, EventRequestClientType.BingAds, goalTypeFilter: null);
            foreach (var existedGoal in existedGoalsCollection.Entities)
            {
                existedGoalsNameCollection.Add(existedGoal.Name);
            }

            AddDefaultValueForAddGoals(context, goals, batchResult, PilotOfCustomerDict);
            goalValidator.ValidateGoals(context, goals, batchResult, PilotOfCustomerDict, existedGoalsNameCollection: existedGoalsNameCollection);
            GoalValidator.VerifyIncomingGoalNamesDuplicate(goals, batchResult);
            if (DynamicConfigValues.EnableAutoGoalApiAndValidation)
            {
                GoalValidator.VerifiedSameCategoryAndTagForAutoGoal(context, existedGoalsCollection.Entities, goals, batchResult);
            }

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            //AppInstall, OfflineConversion and InStoreTransaction Goals require system managed tag generation
            this.CreateSystemManagedTagsForGoals(context, goals, null, batchResult);

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            var daoResult = new BatchResult<long>();

            var getTagResult = GetTagsByCustomerId(context, false, true, fetchTagUsedBy: false);
            if (getTagResult.Failed)
            {
                batchResult.AddErrors(getTagResult.Errors);
                return batchResult;
            }
            var tags = getTagResult.Entities ?? new List<EventTracking.Tag>();
            var usedTags = goals.Where(g => g.TagId.HasValue).Select(g => g.TagId.Value).ToHashSet();
            var sharedUsedTagIds = tags.Where(e => e.Id.HasValue && HasSharingScope(e) && usedTags.Contains(e.Id.Value))
                .Select(e => e.Id.Value).ToList();
            var goalLineItemContainer = new LineItemContainer<EventTracking.Goal>(goals.ToList(), batchResult.BatchErrors);

            if (!ignoreLock && DynamicConfigValues.EnableLockForAddGoals)
            {
                var redisLockToken = Guid.NewGuid().ToString();
                var distributedLockKey = goals.SafeCount() > 0 ? GetLockKey(context, string.Format("AddGoals:{0}", goals.First().Name)) : GetLockKey(context, string.Format("AddGoals"));
                if (DynamicConfigValues.MockLockForAddGoals && goals.SafeCount() > 0)
                {
                    context.Logger.LogInfo($"distributedLockKey: {distributedLockKey}; Goals count in AddGoals: {goals.SafeCount()}; goal names for add goals: {string.Join(",", goals.Select(g => g.Name).ToArray())}");

                }

                TimeSpan ttl = TimeSpan.FromSeconds(DynamicConfigValues.LockForAddGoalsDuration);
                if (LockHelper.GetLock(context, distributedLockKey, ttl, redisLockToken))
                {
                    try
                    {
                        daoResult = dao.AddUetGoals(context, goalLineItemContainer, limitCheck, PilotOfCustomerDict, sharedUsedTagIds);
                        batchResult.Merge(daoResult);
                    }
                    finally
                    {
                        LockHelper.ReleaseLock(context, distributedLockKey, redisLockToken);
                    }
                }
                else
                {
                    context.Logger.LogWarning($"AddGoals cannot get lock for CID: {context.AdvertiserCustomerId}");
                    batchResult.AddError(CampaignManagementErrorCode.ThrottlingLimitReached);
                }
            }
            else
            {
                daoResult = dao.AddUetGoals(context, goalLineItemContainer, limitCheck, PilotOfCustomerDict, sharedUsedTagIds);
                batchResult.Merge(daoResult);
            }

            //enable properties after create goal successfully
            AddAccountProperty(context, goals, batchResult, tags);

            if (batchResult.HasOperationErrors || batchResult.BatchErrors.Count >= goals.Length)
            {
                return batchResult;
            }

            var tagIdsHash = sharedUsedTagIds.ToHashSet();
            var goalSucceed = goalLineItemContainer.Where(e => daoResult.Entities.ContainsKey(e.LineItemId))
                .Select(e => e.LineItem).ToList();
            InlineUpdateUsedBy(BasicCallContext.Create(context),
                new List<Tuple<List<EventTracking.Goal>, bool>> { Tuple.Create(goalSucceed, false) },
                tagIdsHash);


            if (!batchResult.HasOperationErrors && DynamicConfigValues.EnableSmartGoal)
            {
                SendSmartGoalCreationNotification(context, goals, batchResult);
            }

            return batchResult;
        }

        private string GetLockKey(CustomerCallContext context, string keyvalue = "")
        {
            return string.Format("CID:{0}:Key:{1}", context.AdvertiserCustomerId, keyvalue);
        }

        private void AddAccountProperty(CustomerCallContext context, EventTracking.Goal[] goals, BatchResult<long> result, List<EventTracking.Tag> tags)
        {
            string taggingEnabled = "true";
            string MsClickIdTaggingEnabled = "MsClickIdTaggingEnabled";
            string AutoGoalEnabled = "AutoGoalEnabled";

            bool isAutoConversionEnabled = false;
            GetPilotingFeatures(context, out var PilotOfCustomerDict);
            PilotOfCustomerDict?.TryGetValue((int)CustomerFeatureFlag.AutoConversion, out isAutoConversionEnabled);

            Dictionary<string, string> accountLevelResultProperties = new Dictionary<string, string>();
            Dictionary<string, string> customerLevelResultProperties = new Dictionary<string, string>();

            bool enableTaggingForAccount = false;
            bool enableTaggingForCustomer = false;
            bool enableAutoGoalForAccount = false;

            for (int index = 0; index < goals.Length; index++)
            {
                if ((goals[index].Type == EventTracking.GoalEntityType.OfflineConversionGoal
                     || goals[index].Type == EventTracking.GoalEntityType.DestinationGoal
                     || goals[index].Type == EventTracking.GoalEntityType.DurationGoal
                     || goals[index].Type == EventTracking.GoalEntityType.PageViewsPerVisitGoal
                     || goals[index].Type == EventTracking.GoalEntityType.EventGoal
                     || goals[index].Type == EventTracking.GoalEntityType.ProductConversionGoal)
                    && !result.HasBatchErrorForLineItem(index))
                {
                    if (goals[index].IsAccountLevel)
                    {
                        enableTaggingForAccount = true;
                    }
                    else
                    {
                        enableTaggingForCustomer = true;
                    }

                    if (goals[index].IsAutoGoal.HasValue && goals[index].IsAutoGoal.Value)
                    {
                        enableAutoGoalForAccount = true;
                    }
                }
            }

            if (enableTaggingForCustomer)
            {
                customerLevelResultProperties.Add(MsClickIdTaggingEnabled, taggingEnabled);
            }
            else if (enableTaggingForAccount)
            {
                accountLevelResultProperties.Add(MsClickIdTaggingEnabled, taggingEnabled);
            }

            if (enableAutoGoalForAccount && isAutoConversionEnabled)
            {
                accountLevelResultProperties.Add(AutoGoalEnabled, "true");
                ProvisionClarityForTags(context, tags);
            }

            var baseResult = new BaseResult();
            if (customerLevelResultProperties.Count > 0)
            {
                baseResult = this.accountPropertiesEO.UpdateAccountPropertiesByCustomerId(context,
                    context.AdvertiserCustomerId,
                    customerLevelResultProperties);
                if (baseResult.Failed)
                {
                    result.AddError(CampaignManagementErrorCode.InternalError);
                }
            }
            if (accountLevelResultProperties.Count > 0)
            {
                if (!context.Request.CustomerAccountId.HasValue)
                {
                    throw new ArgumentException("Request.CustomerAccountId");
                }

                AccountCallContext accountCallContext = new AccountCallContext(context.Logger, context.Request, context.Request.CustomerAccountId.Value);
                baseResult = this.accountPropertiesEO.UpdateAccountProperties(accountCallContext,
                    context.Request.CustomerAccountId.Value,
                    accountLevelResultProperties);
                if (baseResult.Failed)
                {
                    result.AddError(CampaignManagementErrorCode.InternalError);
                }
            }
        }

        private async void ProvisionClarityForTags(
          CustomerCallContext context,
          List<EventTracking.Tag> tags)
        {
            if (tags == null || tags.Count == 0) { return; }

            for (var i = 0; i < tags?.Count; i++)
            {
                try
                {
                    HttpResponseMessage result = null;
                    var p = new AutoConvEnablingParameters
                    {
                        projectId = tags[i].Id.Value.ToString(),
                        conversions = true,
                        siteName = tags[i].Name,
                        url = ""
                    };

                    result = await ClarityAPIClient.EnableAutoConv(p, context.Logger);

                    if (result == null || !result.IsSuccessStatusCode)
                    {
                        context.Logger.LogError($"Failed to call Clarity for AutoConv for tagId {tags[i].Id}.");
                    }
                }
                catch
                {
                    context.Logger.LogError($"Failed to call Clarity for AutoConv for tagId {tags[i].Id}.");
                }
            }
        }
    
        private void CreateSystemManagedTagsForGoals(
            CustomerCallContext context,
            EventTracking.Goal[] goals,
            Dictionary<long, EventTracking.Goal> existingGoals,
            BatchResult<long> result)
        {
            var newTagListWithNulls = goals.OrEmpty()
                    .Select(
                        g =>
                        (((g is EventTracking.ApplicationInstallGoal)
                            && (existingGoals == null || existingGoals.ContainsKey(g.Id.Value) && existingGoals[g.Id.Value].Type != EventTracking.GoalEntityType.ApplicationInstallGoal))
                            || ((g is EventTracking.OfflineConversionGoal)
                            && (existingGoals == null || existingGoals.ContainsKey(g.Id.Value) && existingGoals[g.Id.Value].Type != EventTracking.GoalEntityType.OfflineConversionGoal))
                            || (g is EventTracking.InStoreTransactionGoal && existingGoals == null)
                            || (g is EventTracking.InStoreVisitGoal && existingGoals == null)
                            )

                            ? new EventTracking.Tag()
                            {
                                Name = "System managed tag " + Guid.NewGuid(),
                                Description = "System managed tag created automatically for goal Name=" + g.Name,
                                Status = EventTracking.TagStatus.Active,
                                Type = EventTracking.TagType.SystemManaged
                            }
                            : null);

            var newTagsContainer = new LineItemContainer<EventTracking.Tag>(
                newTagListWithNulls.ToList(),
                result.BatchErrors,
                childBatchErrors: null,
                predicateFunc: t => t != null);

            if (!newTagsContainer.Any())
            {
                return;
            }

            var addTagsResult = this.dao.AddTagsV2(context, newTagsContainer, false);

            result.MergeErrors(addTagsResult);

            if (result.Errors.Any())
            {
                return;
            }

            foreach (var tagRecord in addTagsResult.Entities.OrEmpty())
            {
                //if either tagRecord value or id is null, it'd be a bug in AddTags operation
                if (tagRecord.Value == null || !tagRecord.Value.Id.HasValue)
                {
                    if (addTagsResult.BatchErrors.ContainsKey(tagRecord.Key))
                    {
                        //can ignore if there is batch error for this item;
                        continue;
                    }

                    SafeSharedLog.LogApplicationError(context.Logger, "Add Auto Tags Error", "new tag entity or tag.id is null for line item Id = {0}", tagRecord.Key);
                    result.AddError(CampaignManagementErrorCode.InternalError);
                    return;
                }

                goals[tagRecord.Key].TagId = tagRecord.Value.Id.Value;
            }
        }


        private void AddDefaultValueForAddGoals(CustomerCallContext context, EventTracking.Goal[] goals, BatchResult<long> batchResult, IDictionary<int, bool> PilotOfCustomerDict)
        {
            for (int index = 0; index < goals.Length; index++)
            {
                if (batchResult.BatchErrors.ContainsKey(index))
                    continue;

                var isCustomerPilotForInStoreVisitConversionGoal = IsFeatureEnabled(PilotOfCustomerDict, (int)CustomerFeatureFlag.InStoreVisitConversion);

                var goal = goals[index];
                if (goal.ConversionCountType == null)
                {                
                    if (isCustomerPilotForInStoreVisitConversionGoal && goal.Type == EventTracking.GoalEntityType.InStoreVisitGoal)
                    {
                        goal.ConversionCountType =  EventTracking.ConversionCountType.Unique;
                    }
                    else
                    {
                        goal.ConversionCountType = EventTracking.ConversionCountType.All;
                    }
                }                

                if (goal.Revenue == null)
                {
                    goal.Revenue = new EventTracking.GoalRevenue
                    {
                        Value = null,
                        Type = EventTracking.GoalValueType.NoValue
                    };
                }
                else
                {
                    goal.Revenue.Type = goal.Revenue.Type ?? EventTracking.GoalValueType.NoValue;

                    if (goal.Revenue.Value == null && goal.Revenue.Type != EventTracking.GoalValueType.NoValue)
                    {
                        goal.Revenue.Value = 1;
                    }

                    // These types have Concurrency Code.
                    // Duration / PageView / AppInstall don't have currency code.
                    // InStoreVisit and InStoreTransaction also have CurrencyCode but they **only support customer level**.
                    if (goal.Revenue.Value != null && goal.Revenue.CurrencyCode == null
                        && goal.IsAccountLevel
                        && (goal.Type == EventTracking.GoalEntityType.DestinationGoal ||
                            goal.Type == EventTracking.GoalEntityType.EventGoal ||
                            goal.Type == EventTracking.GoalEntityType.OfflineConversionGoal ||
                            goal.Type == EventTracking.GoalEntityType.ProductConversionGoal))
                    {
                        AddDefaultValueToCurrencyCode(context, goal);
                    }
                }

                goal.LookbackWindowDays = goal.LookbackWindowDays ?? 30;
                goal.LookbackWindowHours = goal.LookbackWindowHours ?? 0;
                goal.LookbackWindowMinutes = goal.LookbackWindowMinutes ?? 0;

                goal.Status = goal.Status ?? EventTracking.GoalStatus.Active;

                if (goal.ExcludeFromBidding == null)
                {
                    if (isCustomerPilotForInStoreVisitConversionGoal && goal.Type == EventTracking.GoalEntityType.InStoreVisitGoal)
                    {
                        goal.ExcludeFromBidding = true;
                    }
                    else
                    {
                        goal.ExcludeFromBidding = false;
                    }
                }

                SetDefaultOperatorEnum(goal);
                
                AddDefaultValueForSpecificGoalProperty(goal);
            }
        }

        private void AddDefaultValueForSpecificGoalProperty(EventTracking.Goal goal)
        {
            if (goal is EventTracking.DestinationGoal)
            {
                var destinationGoal = goal as EventTracking.DestinationGoal;
                if (!string.IsNullOrEmpty(destinationGoal.UrlString) && destinationGoal.Operator == EventTracking.ExpressionOperator.NoExpression)
                {
                    destinationGoal.Operator = EventTracking.ExpressionOperator.EqualsTo;
                }
            }
            else if (goal is EventTracking.DurationGoal)
            {
                var durationGoal = goal as EventTracking.DurationGoal;
                if (durationGoal.Operator == EventTracking.ValueOperator.NoValue)
                {
                    durationGoal.Operator = EventTracking.ValueOperator.GreaterThan;
                }
                durationGoal.Hours = durationGoal.Hours ?? 0;
                durationGoal.Minutes = durationGoal.Minutes ?? 0;
                durationGoal.Seconds = durationGoal.Seconds ?? 0;
            }
            else if (goal is EventTracking.PageViewsPerVisitGoal)
            {
                var pageViewGoal = goal as EventTracking.PageViewsPerVisitGoal;
                if (pageViewGoal.Operator == EventTracking.ValueOperator.NoValue)
                {
                    pageViewGoal.Operator = EventTracking.ValueOperator.GreaterThan;
                }
                pageViewGoal.PageViews = pageViewGoal.PageViews ?? 0;
            }
            else if (goal is EventTracking.EventGoal)
            {
                var eventGoal = goal as EventTracking.EventGoal;
                if (!string.IsNullOrEmpty(eventGoal.Category) && eventGoal.CategoryOperator == EventTracking.ExpressionOperator.NoExpression)
                {
                    eventGoal.CategoryOperator = EventTracking.ExpressionOperator.EqualsTo;
                }
                if (!string.IsNullOrEmpty(eventGoal.Action) && eventGoal.ActionOperator == EventTracking.ExpressionOperator.NoExpression)
                {
                    eventGoal.ActionOperator = EventTracking.ExpressionOperator.EqualsTo;
                }
                if (!string.IsNullOrEmpty(eventGoal.Label) && eventGoal.LabelOperator == EventTracking.ExpressionOperator.NoExpression)
                {
                    eventGoal.LabelOperator = EventTracking.ExpressionOperator.EqualsTo;
                }
                if (eventGoal.Value != null && eventGoal.ValueOperator == EventTracking.ValueOperator.NoValue)
                {
                    eventGoal.ValueOperator = EventTracking.ValueOperator.EqualTo;
                }
            }
            else if (goal is EventTracking.ApplicationInstallGoal)
            {
                var appGoal = goal as EventTracking.ApplicationInstallGoal;
                if (string.IsNullOrWhiteSpace(appGoal.ApplicationPlatform))
                {
                    appGoal.ApplicationPlatform = "Windows Phone";
                }
            }
            else if (goal is EventTracking.SmartGoal)
            {
                var smartGoal = goal as EventTracking.SmartGoal;
                smartGoal.ConversionCountType = EventTracking.ConversionCountType.All;
                smartGoal.LookbackWindowDays = 0;
                smartGoal.LookbackWindowHours = 0;
                smartGoal.LookbackWindowMinutes = 0;
            }
        }

        private void AddDefaultValueToCurrencyCode(CustomerCallContext context, EventTracking.Goal goal)
        {
            Account account = AdvertiserAccount.GetAccountByAccountId(context.Request.CustomerAccountId.Value, context.Request, context.Logger);
            goal.Revenue.CurrencyCode = account.CurrencyCode;
        }

        private void SendSmartGoalCreationNotification(CustomerCallContext context, EventTracking.Goal[] goals, BatchResult<long> batchResult)
        {
            for (int i = 0; i < goals.Length; i++)
            {
                if (goals[i] is EventTracking.SmartGoal && !batchResult.BatchErrors.ContainsKey(i) && !batchResult.BatchWarnings.ContainsKey(i) && batchResult.Entities.ContainsKey(i))
                {
                    var notification = Microsoft.BingAds.FrameworkExtensions.ObjectExtensions.ToXml(new ConversionTrackingSmartGoalCreation
                    {
                        CustomerID = context.AdvertiserCustomerId,
                        AccountID = (long)context.Request.CustomerAccountId,
                        CreatedDate = DateTime.UtcNow,
                        EnqueueDate = DateTime.UtcNow
                    });
                    var notificationData = new TaskNotifcationData
                    {
                        // The notification type 356 is ConversionTrackingSmartGoalCreationEvent in AnB
                        NotificationId = 356,
                        Payload = notification,
                        UserToken = context.Request.SecurityTicket.SecurityTicketId
                    };
                    try
                    {
                        MessageManagerClient messageManagerClient = new MessageManagerClient(DynamicConfigValues.MessageManagerServiceUrl);
                        messageManagerClient.SendNotification(notificationData, context.Request.Tracking.TrackingId.ToString()).Wait();
                    }
                    catch (Exception e)
                    {
                        context.Logger.LogError("Send Smart Goal Creation Notification response throws exception: {0} CustomerId: {1} AccountId: {2}", e, context.AdvertiserCustomerId, context.Request.CustomerAccountId);
                    }
                }
            }
        }

        // API can assign some default values when we use nullable properties but OData can't. OData will keep null values and then fail validation.
        // This function aims to keep them in the same logic. API and OData both should have default value for Add Goal operation.
        // Separate it out from AddDefaultValueForSpecificGoalProperty to make it clean.
        private void SetDefaultOperatorEnum(EventTracking.Goal goal)
        {
            if (goal is EventTracking.DestinationGoal)
            {
                var destinationGoal = goal as EventTracking.DestinationGoal;
                if (!destinationGoal.Operator.HasValue)
                {
                    destinationGoal.Operator = EventTracking.ExpressionOperator.NoExpression;
                }
            }
            else if (goal is EventTracking.DurationGoal)
            {
                var durationGoal = goal as EventTracking.DurationGoal;
                if (!durationGoal.Operator.HasValue)
                {
                    durationGoal.Operator = EventTracking.ValueOperator.NoValue;
                }
            }
            else if (goal is EventTracking.PageViewsPerVisitGoal)
            {
                var pageViewGoal = goal as EventTracking.PageViewsPerVisitGoal;
                if (!pageViewGoal.Operator.HasValue)
                {
                    pageViewGoal.Operator = EventTracking.ValueOperator.NoValue;
                }
            }
            else if (goal is EventTracking.EventGoal)
            {
                var eventGoal = goal as EventTracking.EventGoal;
                if (!eventGoal.CategoryOperator.HasValue)
                {
                    eventGoal.CategoryOperator = EventTracking.ExpressionOperator.NoExpression;
                }
                if (!eventGoal.ActionOperator.HasValue)
                {
                    eventGoal.ActionOperator = EventTracking.ExpressionOperator.NoExpression;
                }
                if (!eventGoal.LabelOperator.HasValue)
                {
                    eventGoal.LabelOperator = EventTracking.ExpressionOperator.NoExpression;
                }
                if (!eventGoal.ValueOperator.HasValue)
                {
                    eventGoal.ValueOperator = EventTracking.ValueOperator.NoValue;
                }
            }

        }
    }
}
