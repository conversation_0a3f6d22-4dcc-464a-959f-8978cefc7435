namespace Microsoft.BingAds.Api.Service.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using System.Web;
    using AspNet.OData;
    using AspNet.OData.Query;
    using AspNetCore.Mvc.ModelBinding;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.AIGC;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Reporting;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.DataContract;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.BingAds.Api.Model;
    using Microsoft.BingAds.Api.Repositories;
    using Microsoft.BingAds.Api.Repositories.CustomQuery;
    using Microsoft.BingAds.Api.Repositories.Validators;
    using Microsoft.BingAds.Api.Repository.CustomQuery;
    using Microsoft.BingAds.Api.Repository.Helpers;
    using Microsoft.BingAds.Api.Repository.Repositories;
    using Microsoft.BingAds.Api.Repository.Repositories.Dimension;
    using Microsoft.BingAds.Api.Repository.Validators;
    using Microsoft.BingAds.Api.Service.Attribute;
    using Microsoft.OData;
    using Microsoft.OData.UriParser;
    using Repository;
    using AdType = Microsoft.Ads.Mca.BusinessMT.Model.Ads.AdType;
    using BusinessRules = Microsoft.Advertiser.CampaignManagement.BusinessRules;
    using CampaignOverviewTile = Microsoft.BingAds.Api.Model.CampaignOverviewTile;
    using Currency = Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities.Currency;
    using DimensionReportType = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Reporting.DimensionReportType;
    using Entities = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using EntityField = Microsoft.Ads.Mca.Common.Interface.EntityField;
    using McaAd = Microsoft.Ads.Mca.BusinessMT.Model.Ads.McaAd;
    using McaAdStatus = Microsoft.Ads.Mca.BusinessMT.Model.Ads.McaAdStatus;
    using McaKeyword = Microsoft.Ads.Mca.BusinessMT.Model.Ads.McaKeyword;
    using SearchAd = Microsoft.Ads.Mca.BusinessMT.Model.Ads.SearchAd;
    using Target = Microsoft.Ads.Mca.BusinessMT.Model.Ads.Target;
    using Area = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Area;
    using ConversionDiagnosticsResult = Model.ConversionDiagnostics;

    [VersionRange(2)]
    public class CampaignsController : BaseController
    {
        private IValidator<string, DateRange> dateRangeValidator;
        private IValidator<PilotingContext<ODataQueryOptions<Campaign>>, ODataActionParameters, string, GridDataRequestValidationResult> gridDataRequestValidator;
        private ICampaignsRepository campaignsRepository;
        private IValidator<PilotingContext<ODataQueryOptions<Campaign>>, QueryOptionsValidationResult> queryOptionsValidator;
        private IValidator<POPDateRange, string, PerformanceTimeSeriesValidationResult> performanceTimeSeriesOptionsValidator;
        private IPerformanceTimeSeriesRepository perfTimeSeriesRepository;
        private IBudgetRepository budgetRepository;
        private IPerformanceTargetInstancesRepository performanceTargetInstancesRepository;
        private IDimensionRepositoryFactory dimensionRepositoryFactory;
        private IDimensionReportHelperFactory dimensionReportHelperFactory;
        private IAlertInformationRepository alertInformationRepository;
        private IMtSelectConverter selectConverter;
        private Func<CallContext> callContextGetter;
        private readonly ISmartRecommendationsRepository smartRecommendationsRepository;
        private readonly IBusinessInfoExtractionRepository _businessInfoExtractionRepository;
        private IValidator<ModelStateDictionary, bool> modelStateValidator;
        private readonly IValidator<string, string, long> _stringIdValidator;
        private IValidator<PilotingContext<ODataQueryOptions<CampaignOverview>>, ODataActionParameters, CampaignOverviewParametersValidationResult> campaignOverviewParametersValidator;
        private IValidator<Func<CallContext>, DateTime?> callContextLastWriteTimeValidator;
        private IMtQueryConverter mtQueryConverter;

        public CampaignsController(
            ICampaignsRepository campaignsRepository,
            IValidator<string, DateRange> dateRangeValidator,
            IValidator<PilotingContext<ODataQueryOptions<Campaign>>, ODataActionParameters, string, GridDataRequestValidationResult> gridDataRequestValidator,
            IValidator<PilotingContext<ODataQueryOptions<Campaign>>, QueryOptionsValidationResult> queryOptionsValidator,
            IPerformanceTimeSeriesRepository perfTimeSeriesRepository,
            IBudgetRepository budgetRepository,
            IPerformanceTargetInstancesRepository performanceTargetInstancesRepository,
            IValidator<POPDateRange, string, PerformanceTimeSeriesValidationResult> performanceTimeSeriesOptionsValidator,
            IDimensionRepositoryFactory dimensionRepositoryFactory,
            IDimensionReportHelperFactory dimensionReportHelperFactory,
            IAlertInformationRepository alertInformationRepository,
            IMtSelectConverter selectConverter,
            Func<CallContext> callContextGetter,
            ISmartRecommendationsRepository smartRecommendationsRepository,
            IBusinessInfoExtractionRepository businessInfoExtractionRepository,
            IValidator<ModelStateDictionary, bool> modelStateValidator,
            IValidator<string, string, long> stringIdValidator,
            IValidator<PilotingContext<ODataQueryOptions<CampaignOverview>>, ODataActionParameters, CampaignOverviewParametersValidationResult> campaignOverviewParametersValidator,
            IValidator<Func<CallContext>, DateTime?> callContextLastWriteTimeValidator,
            IMtQueryConverter mtQueryConverter)
        {
            this.campaignsRepository = campaignsRepository;
            this.dateRangeValidator = dateRangeValidator;
            this.gridDataRequestValidator = gridDataRequestValidator;
            this.queryOptionsValidator = queryOptionsValidator;
            this.perfTimeSeriesRepository = perfTimeSeriesRepository;
            this.budgetRepository = budgetRepository;
            this.performanceTargetInstancesRepository = performanceTargetInstancesRepository;
            this.performanceTimeSeriesOptionsValidator = performanceTimeSeriesOptionsValidator;
            this.dimensionRepositoryFactory = dimensionRepositoryFactory;
            this.dimensionReportHelperFactory = dimensionReportHelperFactory;
            this.alertInformationRepository = alertInformationRepository;
            this.selectConverter = selectConverter;
            this.callContextGetter = callContextGetter;
            this.smartRecommendationsRepository = smartRecommendationsRepository;
            this._businessInfoExtractionRepository = businessInfoExtractionRepository;
            this.modelStateValidator = modelStateValidator;
            this._stringIdValidator = stringIdValidator;
            this.campaignOverviewParametersValidator = campaignOverviewParametersValidator;
            this.callContextLastWriteTimeValidator = callContextLastWriteTimeValidator;
            this.mtQueryConverter = mtQueryConverter;
        }

        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [EnableQuery]
        [LatencyPerfCounter("GetCampaignById")]
        public async Task<IActionResult>
            GetCampaign(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId)
        {
            var result = await campaignsRepository.GetCampaignsByIds(customerId, accountId, campaignId, true);
            return result.SingleEntityResponse(Request, logger);
        }

        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetCampaignByAccountId")]
        public async Task<IActionResult>
            GetCampaigns(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataQueryOptions<Campaign> options)
        {
            return await GetCampaignsFromRepository(customerId, accountId, options);
        }

        public async Task<IActionResult>
            GetCampaignsFromRepository(
            long customerId,
            long accountId,
            ODataQueryOptions<Campaign> options)
        {
            this.SetSelectExpandClauseByOptions(options);

            Validation<DateRange> dateRangeValidation = dateRangeValidator.Validate(Request.RequestQueryString());
            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(Request, logger);
            }

            PilotingContext<ODataQueryOptions<Campaign>> pilotingContext = new PilotingContext<ODataQueryOptions<Campaign>>(options, this.callContextGetter().AuthorizationContext);
            Validation<QueryOptionsValidationResult> queryOptionsValidation = this.queryOptionsValidator.Validate(pilotingContext);

            if (queryOptionsValidation.Errors.OrEmpty().Any())
            {
                return queryOptionsValidation.Errors.CreateErrorResponse(Request, logger);
            }

            var includeCampaignOptOutFromMCM = checkIfPropertyExistsInSelectClause(options, "OptOutFromMCM");

            var lcid = options.GetLcidFromHeader();
            var result = await campaignsRepository.Get(
                customerId,
                accountId,
                new GridDataRequestValidationResult
                {
                    DateRange = dateRangeValidation.Result,
                    GridDataSelection = queryOptionsValidation.Result.GridDataSelection,
                    AggregateDataFilter = queryOptionsValidation.Result.AggregateDataFilter,
                    IncludeSOVBIData = queryOptionsValidation.Result.IncludeSOVBIData,
                    SelectedColumns = queryOptionsValidation.Result.SelectedColumns,
                },
                lcid,
                includeCampaignOptOutFromMCM: includeCampaignOptOutFromMCM);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        private bool checkIfPropertyExistsInSelectClause(ODataQueryOptions<Campaign> options, string properyName)
        {
            return (options.SelectExpand?.SelectExpandClause?.SelectedItems).OrEmpty()
                          .OfType<PathSelectItem>()
                          .Select(item => item.SelectedPath.OrEmpty().First())
                          .Any(segment => segment?.Identifier == properyName);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetNCampaignsByAccountId")]
        public async Task<IActionResult> GetNCampaigns(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            int count)
        {
            if (count <= 0)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid count");
            }

            QueryResult<Campaign> result = await campaignsRepository.GetNCampaignByAccountId(
                                                    customerId,
                                                    accountId,
                                                    count).ConfigureAwait(false);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("CampaignDimensionReport")]
        public async Task<IActionResult>
            DimensionReport(long customerId, long accountId, long campaignId, DimensionReportType reportType, int locale, ODataQueryOptions<DimensionRow> options)
        {
            var reportHelper = this.dimensionReportHelperFactory.GetDimensionReportHelper(reportType);
            if (!reportHelper.IsReportTypeSupported(reportType))
            {
                var error = new AdsApiError()
                {
                    // todo create a DimensionReportTypeNotSupported error code
                    Code = ApiErrorCodes.NotSupported,
                    Property = "ReportType",
                    Message = $"{reportType} not supported"
                };

                return error.CreateErrorResponse(this.Request, this.logger, HttpStatusCode.BadRequest);
            }

            var dateRangeValidation = reportHelper.ValidateDateRange(options);
            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var pilotingContext = new PilotingContext<ODataQueryOptions<DimensionRow>>(options, this.callContextGetter().AuthorizationContext);
            var validationResult = reportHelper.ValidateGridDataSelection(pilotingContext);
            if (validationResult.Errors.OrEmpty().Any())
            {
                return validationResult.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var selectValidation = reportHelper.ValidateSelectedColumns(options);
            if (selectValidation.Errors.OrEmpty().Any())
            {
                return selectValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var validationErrors = DimensionValidationHelper.ValidateGet(this.logger, reportType, dateRangeValidation.Result, validationResult.Result.GridDataSelection, selectValidation.Result.Values.ToList(), campaignId, null, false);
            if (validationErrors.Any())
            {
                return validationErrors.CreateErrorResponse(this.Request, this.logger);
            }

            var result = await this.dimensionRepositoryFactory.GetReportRepository(DimensionReportScope.Campaign, reportType).Get(
                customerId,
                accountId,
                reportType,
                dateRangeValidation.Result,
                validationResult.Result.GridDataSelection,
                selectValidation.Result,
                locale,
                campaignId,
                null);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpGet]
        [LatencyPerfCounter("CampaignPerformanceTimeSeriesByAccountId")]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            int? currentCustomerId,
            ODataQueryOptions<Campaign> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<Campaign>>(options, this.callContextGetter().AuthorizationContext);
            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string returnPhoneCallsString = queryParams.Get(CustomQueryParameters.ReturnPhoneCalls);
            bool returnPhoneCalls = false;
            bool.TryParse(returnPhoneCallsString, out returnPhoneCalls);

            return await PerformanceTimeSeriesHelper.HandleRequest<Campaign>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.queryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.Campaign, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, null, null, returnPhoneCalls, currentCustomerId: currentCustomerId);
        }

        [HttpGet]
        [LatencyPerfCounter("CampaignPerformanceTimeSeriesByAccountId")]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            ODataQueryOptions<Campaign> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<Campaign>>(options, this.callContextGetter().AuthorizationContext);
            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string returnPhoneCallsString = queryParams.Get(CustomQueryParameters.ReturnPhoneCalls);
            bool returnPhoneCalls = false;
            bool.TryParse(returnPhoneCallsString, out returnPhoneCalls);

            return await PerformanceTimeSeriesHelper.HandleRequest<Campaign>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.queryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.Campaign, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, null, null, returnPhoneCalls, currentCustomerId: null);
        }

        [NotImplementedExceptionFilter]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetCampaignsByBudgetId")]
        public async Task<IActionResult>
            GetCampaigns(
           [FromODataUri] long customerId, [FromODataUri] long accountId, [FromODataUri] long budgetId, ODataQueryOptions<Campaign> queryOptions)
        {
            List<AdsApiError> errors = new List<AdsApiError>();
            int top = 0;
            int skip = 0;
            FilterClause filter = null;
            OrderByClause orderBy = null;

            if (null != queryOptions)
            {
                try
                {
                    var settings = new ODataValidationSettings()
                    {
                        MaxExpansionDepth = 1,
                        AllowedLogicalOperators = AllowedLogicalOperators.Equal | AllowedLogicalOperators.NotEqual | AllowedLogicalOperators.And | AllowedLogicalOperators.LessThan | AllowedLogicalOperators.LessThanOrEqual | AllowedLogicalOperators.GreaterThan | AllowedLogicalOperators.GreaterThanOrEqual,
                        AllowedArithmeticOperators = AllowedArithmeticOperators.None,
                        AllowedFunctions =
                            AllowedFunctions.Contains
                                | AllowedFunctions.StartsWith | AllowedFunctions.EndsWith,
                        AllowedQueryOptions = AllowedQueryOptions.Top | AllowedQueryOptions.Skip | AllowedQueryOptions.Filter | AllowedQueryOptions.Count | AllowedQueryOptions.OrderBy | AllowedQueryOptions.Select,
                    };

                    queryOptions.Validate(settings);

                    filter = queryOptions.Filter?.FilterClause;
                    orderBy = queryOptions.OrderBy?.OrderByClause;
                    top = queryOptions.Top?.Value ?? 0;
                    skip = queryOptions.Skip?.Value ?? 0;
                }
                catch (ODataException ex)
                {
                    errors.Add(new AdsApiError()
                    {
                        Code = ApiErrorCodes.ODataException,
                        Property = "QueryString",
                        Message = ex.Message
                    });
                }
            }

            var dateRangeValidationResult = dateRangeValidator.Validate(Request.RequestQueryString());

            if (null != dateRangeValidationResult.Errors && dateRangeValidationResult.Errors.Any())
            {
                errors.Add(new AdsApiError()
                {
                    Code = ApiErrorCodes.InvalidDateRange,
                    Property = "DateRange",
                    Message = "Invalid date range"
                });
            }

            // fail call if there is an error.
            if (errors.Count > 0)
            {
                return errors.CreateErrorResponse(Request, logger);
            }
            var result = await this.budgetRepository.GetCampaignByBudgetId(customerId, accountId, budgetId, dateRangeValidationResult.Result, filter, orderBy, top, skip);
            return result.MultipleEntitiesResponse(Request, logger, queryOptions);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("CampaignOverview")]
        public async Task<IActionResult>
            CampaignOverview(
                [FromODataUri] long customerId,
                [FromODataUri] long accountId,
                ODataQueryOptions<CampaignOverview> options,
                ODataActionParameters parameters)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<CampaignOverview>>(
                options,
                this.callContextGetter().AuthorizationContext);

            Validation<CampaignOverviewParametersValidationResult> requestValidation =
                this.campaignOverviewParametersValidator.Validate(
                    pilotingContext,
                    parameters);

            if (requestValidation.Errors.OrEmpty().Any())
            {
                return requestValidation.Errors.CreateErrorResponse(Request, logger);
            }

            var callContextLastWriteTimeValidation = this.callContextLastWriteTimeValidator.Validate(this.callContextGetter);
            if (callContextLastWriteTimeValidation.Errors.OrEmpty().Any())
            {
                return callContextLastWriteTimeValidation.Errors.CreateErrorResponse(Request, logger);
            }

            GridDataSelection gridDataSelection = requestValidation.Result.GridDataSelection;
            List<GridColumn> selectedBiColumns = requestValidation.Result.SelectedBIColumns;
            IEnumerable<CampaignOverviewTile> tiles = requestValidation.Result.Tiles;
            DateRange dateRange = requestValidation.Result.DateRange;
            DateRange comparisonDateRange = requestValidation.Result.ComparisonDateRange;
            bool hasGlobalComparisonPeriod = requestValidation.Result.HasGlobalComparisonPeriod;
            Granularity granularity = requestValidation.Result.PerformanceChartGranularity;
            List<string> metrics = requestValidation.Result.Metrics;
            GridSort allCampaignsSort = requestValidation.Result.AllCampaignsSort;
            OverviewTopChangedSort topChangeSort = requestValidation.Result.TopChangedSort;
            bool isPreload = requestValidation.Result.IsPreload;
            DateTime? lastWriteTime = callContextLastWriteTimeValidation.Result;
            bool fetchFluctuationInsights = requestValidation.Result.FetchFluctuationInsights;

            OverviewConversionsOptions overviewConversionsOptions = requestValidation.Result.OverviewConversionsOptions;

            QueryResult<CampaignOverview> result = await this.campaignsRepository.GetCampaignOverview(
                customerId,
                accountId,
                isPreload,
                lastWriteTime,
                gridDataSelection,
                selectedBiColumns,
                tiles,
                dateRange,
                comparisonDateRange,
                granularity,
                metrics,
                hasGlobalComparisonPeriod,
                allCampaignsSort,
                topChangeSort,
                fetchFluctuationInsights,
                overviewConversionsOptions);

            return result.SingleEntityResponse(Request, logger, options);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("CampaignGridData")]
        public async Task<IActionResult>
            GridData(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataQueryOptions<Campaign> options,
            ODataActionParameters parameters)
        {
            SetSelectExpandClauseByOptions(options);

            PilotingContext<ODataQueryOptions<Campaign>> pilotingContext = new PilotingContext<ODataQueryOptions<Campaign>>(options, this.callContextGetter().AuthorizationContext);
            Validation<GridDataRequestValidationResult> gridDataRequestValidation = gridDataRequestValidator.Validate(pilotingContext, parameters, Entities.EntityType.Campaign.ToString());

            if (gridDataRequestValidation.Errors.OrEmpty().Any())
            {
                return gridDataRequestValidation.Errors.CreateErrorResponse(Request, logger);
            }

            var lcid = options.GetLcidFromHeader();
            QueryResult<Campaign> result = await campaignsRepository.Get(
                customerId,
                accountId,
                gridDataRequestValidation.Result,
                lcid,
                ODataQueryOptionsHelper.GetPreloadOption(parameters));

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AlertInformation(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataQueryOptions<Campaign> options)
        {
            return await AlertInformationHelper.HandleRequest(
                this.logger,
                this.Request,
                this.alertInformationRepository,
                this.selectConverter,
                customerId,
                accountId,
                campaignId,
                null,
                options);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            GridData(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long performanceTargetId,
            [FromODataUri] long performanceTargetInstanceId,
            ODataQueryOptions<Campaign> options,
            ODataActionParameters parameters)
        {
            SetSelectExpandClauseByOptions(options);

            PilotingContext<ODataQueryOptions<Campaign>> pilotingContext = new PilotingContext<ODataQueryOptions<Campaign>>(options, this.callContextGetter().AuthorizationContext);
            Validation<GridDataRequestValidationResult> gridDataRequestValidation = gridDataRequestValidator.Validate(pilotingContext, parameters, Entities.EntityType.Campaign.ToString());

            if (gridDataRequestValidation.Errors.OrEmpty().Any())
            {
                return gridDataRequestValidation.Errors.CreateErrorResponse(Request, logger);
            }

            QueryResult<Campaign> result = await this.performanceTargetInstancesRepository.GetCampaignsByPerformanceTargetInstanceId(
                customerId,
                accountId,
                performanceTargetId,
                performanceTargetInstanceId,
                gridDataRequestValidation.Result);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            GetCampaignsCount(
                [FromODataUri] long customerId,
                [FromODataUri] long accountId,
                string campaignType,
                string campaignSubType)
        {
            Entities.CampaignType? campaignEnumType = null;
            if (Enum.TryParse<Entities.CampaignType>(campaignType, out Entities.CampaignType campaignEnumTempType))
            {
                campaignEnumType = campaignEnumTempType;
            }

            Entities.CampaignSubType? campaignEnumSubType = null;
            if (Enum.TryParse<Entities.CampaignSubType>(campaignSubType,
                out Entities.CampaignSubType campaignEnumTempSubType))
            {
                campaignEnumSubType = campaignEnumTempSubType;
            }

            QueryResult<int> result = await this.campaignsRepository.GetCampaignsCount(
                customerId,
                accountId,
                campaignEnumType,
                campaignEnumSubType);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        PrepareRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractPrepareRecommendationProperties(
                parameters,
                out string website,
                out IEnumerable<string> entities);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.PrepareRecommendation(
                customerId,
                accountId,
                campaignId,
                website,
                entities);

            return result.SingleEntityResponse(Request, logger);
        }

        /// <summary>
        /// Prepare Ads: WebUI calls it to warm up the generation service
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="accountId"></param>
        /// <param name="campaignId"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        PrepareAdRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractPrepareAdRecommendationProperties(
                parameters,
                out Model.Business business);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.PrepareAdRecommendation(
                customerId,
                accountId,
                campaignId,
                business);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        CreateAdRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            // TODO: AdType and count must be provided as paramter
            var errors = ValidateAndExtractCreateAdRecommendationProperties(
                parameters,
                out IEnumerable<string> categories,
                out Model.Business business,
                out IEnumerable<Model.Criterion> criterions,
                out int count,
                out Entities.Language language);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.CreateAdRecommendation(
                customerId,
                accountId,
                campaignId,
                categories,
                business,
                criterions,
                Entities.AdType.ExpandedText,
                count,
                language);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [EnableQuery]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        CreateResponsiveSearchAdRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataQueryOptions<ResponsiveSearchAd> options,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractCreateAdAssetRecommendationProperties(
                parameters,
                out IEnumerable<string> categories,
                out Model.Business business,
                out IEnumerable<Model.Criterion> criterions,
                out int count,
                out int headlineCount,
                out int descriptionCount,
                out Entities.AggregationServiceCaller caller);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.CreateResponsiveSearchAdRecommendation(
                customerId,
                accountId,
                campaignId,
                categories,
                business,
                criterions,
                Entities.AdType.ResponsiveSearch,
                count,
                headlineCount,
                descriptionCount,
                caller);

            this.AddExpandForCreateResponsiveSearchAdRecommendation(options);
            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        MediaGeneration(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractMediaGenerationProperties(
                parameters,
                out string jobId,
                out Entities.ImageGeneration image,
                out Entities.MediaOutput output,
                out Entities.AggregationServiceCaller caller);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            QueryResult<Entities.MediaGenerationResult> result = await this.smartRecommendationsRepository.CreateMediaGeneration(customerId, accountId, campaignId, jobId, image, output, caller);

            var queryResult = new QueryResult<Entities.MediaGenerationResult>(result, null, null)
            {
            };

            return await Task.FromResult(queryResult.SingleEntityResponse(Request, logger));
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        CreateImageRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractImageRecommendationProperties(
                parameters,
                out string url,
                out string source,
                out IEnumerable<string> sources,
                out Entities.ImageRankingCriteria rankingCriteria,
                out IEnumerable<Entities.SelectedImage> selectedImages,
                out Entities.ImageRecommendationOptions options,
                out Entities.ImageRecommendationPageDefination paging,
                out Entities.AggregationServiceCaller caller);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            QueryResult<Entities.RecommendedImages> result;

            result = await this.smartRecommendationsRepository.CreateImageRecommendation(customerId, accountId, campaignId, url, source, sources, rankingCriteria, selectedImages, options, paging, caller);

            return await Task.FromResult(result.SingleEntityResponse(Request, logger));
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        CompleteAdRecommendation(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractCompleteAdRecommendationProperties(
                parameters,
                out Model.Business business,
                out int count,
                out Model.Ad userAd,
                out Entities.AdAutoCompleteType adAutoCompleteType);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.CompleteAdRecommendation(
                customerId,
                accountId,
                campaignId,
                business,
                Entities.AdType.ExpandedText,
                userAd,
                adAutoCompleteType,
                count);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GenerateImages(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            GenerateImagesRequest generateImagesRequest;
            if (parameters != null && parameters.ContainsKey("GenerateImagesRequest") && parameters["GenerateImagesRequest"] != null)
            {
                generateImagesRequest = parameters["GenerateImagesRequest"] as GenerateImagesRequest;
            }
            else
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "GenerateImagesRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.GenerateImagesTask(
                customerId,
                accountId,
                campaignId,
                generateImagesRequest);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GenerateImagesResult")]
        public async Task<IActionResult>
            GenerateImagesResult(long customerId, long accountId, long campaignId, string taskId)
        {
            if (string.IsNullOrEmpty(taskId))
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidValue, Property = "TaskId" });
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.GenerateImagesTaskResult(
                customerId,
                accountId,
                campaignId,
                taskId);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GetThemeAssetsRecommendation(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            GetThemeAssetsRecommendationRequest request;
            if (parameters != null && parameters.ContainsKey("GetThemeAssetsRecommendationRequest") && parameters["GetThemeAssetsRecommendationRequest"] != null)
            {
                request = parameters["GetThemeAssetsRecommendationRequest"] as GetThemeAssetsRecommendationRequest;
            }
            else
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "GetThemeAssetsRecommendationRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string enableLogoString = queryParams.Get("enableLogo");
            bool enableLogo = false;
            bool.TryParse(enableLogoString, out enableLogo);
            string enableCTAString = queryParams.Get("enableCTA");
            bool enableCTA = false;
            bool.TryParse(enableCTAString, out enableCTA);

            var result = await this.smartRecommendationsRepository.GetThemeAssetsRecommendationV2(
                customerId,
                accountId,
                campaignId,
                request,
                enableLogo: enableLogo,
                enableCTA: enableCTA);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GetSocialSitesRecommendation(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var errors = new List<AdsApiError>();

            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            GetSocialSitesRecommendationRequest request;
            if (parameters != null && parameters.ContainsKey("GetSocialSitesRecommendationRequest") && parameters["GetSocialSitesRecommendationRequest"] != null)
            {
                request = parameters["GetSocialSitesRecommendationRequest"] as GetSocialSitesRecommendationRequest;
            }
            else
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "GetSocialSitesRecommendationRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            if (string.IsNullOrEmpty(request.FinalURL))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "FinalUrl" });
                return errors.CreateErrorResponse(Request, logger);
            }

            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string mockStr = queryParams.Get("Mock");
            bool mock = false;
            bool.TryParse(mockStr, out mock);

            QueryResult<AIGCResponse<GetSocialSitesRecommendationResponse>> result = await this.smartRecommendationsRepository.GetSocialSitesRecommendation(
                customerId,
                accountId,
                campaignId,
                mock,
                request);

            return result.SingleEntityResponse(Request, logger);
        }


        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GetAssetsRecommendation(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            GetAssetsRecommendationRequest request;
            if (parameters != null && parameters.ContainsKey("GetAssetsRecommendationRequest") && parameters["GetAssetsRecommendationRequest"] != null)
            {
                request = parameters["GetAssetsRecommendationRequest"] as GetAssetsRecommendationRequest;
            }
            else
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "GetAssetsRecommendationRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string enableLogoString = queryParams.Get("enableLogo");
            bool enableLogo = false;
            bool.TryParse(enableLogoString, out enableLogo);
            string enableCTAString = queryParams.Get("enableCTA");
            bool enableCTA = false;
            bool.TryParse(enableCTAString, out enableCTA);

            var result = await this.smartRecommendationsRepository.GetAssetsRecommendationV2(
                customerId,
                accountId,
                campaignId,
                request,
                enableLogo: enableLogo,
                enableCTA: enableCTA);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        RefineTextAsset(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            RefineTextAssetRequest request;
            if (parameters != null && parameters.ContainsKey("RefineTextAssetRequest") && parameters["RefineTextAssetRequest"] != null)
            {
                request = parameters["RefineTextAssetRequest"] as RefineTextAssetRequest;
            }
            else
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "RefineTextAssetRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.RefineTextAssetV2(
            customerId,
            accountId,
            campaignId,
            request);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        RefineImage(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            RefineImageRequest request;
            if (parameters != null && parameters.ContainsKey("RefineImageRequest") && parameters["RefineImageRequest"] != null)
            {
                request = parameters["RefineImageRequest"] as RefineImageRequest;
            }
            else
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "RefineImageRequest" });
                return errors.CreateErrorResponse(Request, logger);
            }

            bool? useSync = null;
            if (parameters != null && parameters.ContainsKey("UseSync") && parameters["UseSync"] != null)
            {
                useSync = (bool?)parameters["UseSync"];
            }

            if (useSync == true)
            {
                var result = await this.smartRecommendationsRepository.RefineImageV2(
                customerId,
                accountId,
                campaignId,
                request);

                return result.SingleEntityResponse(Request, logger);
            }
            else
            {
                var result = await this.smartRecommendationsRepository.RefineImage(
                customerId,
                accountId,
                campaignId,
                request);

                return result.SingleEntityResponse(Request, logger);
            }
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("RefineImageResult")]
        public async Task<IActionResult>
            RefineImageResult(long customerId, long accountId, long campaignId, string taskId)
        {
            if (string.IsNullOrEmpty(taskId))
            {
                var errors = new List<AdsApiError>();
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidValue, Property = "TaskId" });
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.RefineImageResult(
                customerId,
                accountId,
                campaignId,
                taskId);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AdPreviewRecommendations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = ValidateAndExtractAdPreviewRecommendationProperties(
                parameters,
                out string category,
                out Model.Business business,
                out IEnumerable<Model.Criterion> criterions,
                out bool isTitle3OrDescription2Requested);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.GetAdPreviewRecommendations(
                customerId,
                accountId,
                campaignId,
                category,
                business,
                criterions,
                isTitle3OrDescription2Requested);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AdPreviewSuggestions(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var result = await this.smartRecommendationsRepository.GetAdPreviewSuggestions(
                customerId,
                accountId,
                campaignId);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            BusinessCategoryRecommendations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);
            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            object val;

            parameters.TryGetValue("Language", out val);
            var language = val as Entities.Language?;
            if (language == null)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Must have Language in parameters");
            }

            parameters.TryGetValue("Url", out val);
            var url = val as string;

            parameters.TryGetValue("Category", out val);
            var category = val as string;

            if (url == null && category == null)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Must have url or category in parameters");
            }

            if (url != null && category != null)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Cannot have both Url and Category in parameters in the same time.");
            }

            var results = url != null
                ? await this.smartRecommendationsRepository.GetBusinessCategoryRecommendationsFromUrl(customerId, accountId, campaignId, language.Value, url)
                : await this.smartRecommendationsRepository.GetBusinessCategoryRecommendationsFromCategory(customerId, accountId, campaignId, language.Value, category);

            return results.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            ProductOrServiceRecommendations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);
            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            object val;

            parameters.TryGetValue("Language", out val);
            var language = val as Entities.Language?;
            if (language == null)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Must have Language in parameters");
            }

            parameters.TryGetValue("Category", out val);
            var category = val as string;

            if (category == null)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Must have Category in parameters");
            }

            var result = await this.smartRecommendationsRepository.GetProductOrServiceRecommendations(
                customerId,
                accountId,
                campaignId,
                language.Value,
                category);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            ProductServiceRecommendations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);
            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            NameValueCollection queryParams = HttpUtility.ParseQueryString(Request.RequestQueryString());
            string useKeywordSuggestionServiceString = queryParams.Get(CustomQueryParameters.UseKeywordSuggestionService);
            bool useKeywordSuggestionService = false;
            bool.TryParse(useKeywordSuggestionServiceString, out useKeywordSuggestionService);

            parameters.TryGetValue("SmartListings", out var val);
            var smartListings = val as IEnumerable<Model.SmartListing>;

            parameters.TryGetValue("Language", out val);
            var language = val as Entities.Language?;

            var result = await this.smartRecommendationsRepository.GetProductServiceRecommendations(
                customerId,
                accountId,
                campaignId,
                smartListings,
                useKeywordSuggestionService,
                language ?? Entities.Language.English);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AudienceReachEstimations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);

            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var errors = ValidateAndExtractSmartEstimationProperties(
                parameters,
                out Entities.Language? language,
                out Entities.CampaignGoal? campaignGoal,
                out Model.Business business,
                out IEnumerable<Model.Criterion> criterions,
                out IEnumerable<Model.SmartListing> smartListings,
                out IEnumerable<Model.Ad> ads,
                out BusinessRules.Entities.Currency currency,
                allowSmartListingsNull: true,
                allowAdsNull: true);


            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.GetAudienceReachEstimations(
                customerId,
                accountId,
                campaignId,
                language.Value,
                campaignGoal.Value,
                business,
                criterions,
                smartListings);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AudienceEstimations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);

            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            parameters.TryGetValue("Criterions", out var val);
            var criterions = val as IEnumerable<Model.Criterion>;
            if (criterions == null)
            {
                var error = new AdsApiError()
                {
                    Code = ApiErrorCodes.EntityIsNull,
                    Property = "Criterions",
                    Message = "Location info is null or type is not correct"
                };

                return error.CreateErrorResponse(this.Request, this.logger, HttpStatusCode.BadRequest);
            }

            var result = await this.smartRecommendationsRepository.GetAudienceEstimations(
                customerId,
                accountId,
                campaignId,
                criterions);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            BudgetKpiEstimations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);

            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var errors = ValidateAndExtractSmartEstimationProperties(
                parameters,
                out Entities.Language? language,
                out Entities.CampaignGoal? campaignGoal,
                out Model.Business business,
                out IEnumerable<Model.Criterion> criterions,
                out IEnumerable<Model.SmartListing> smartListings,
                out IEnumerable<Model.Ad> ads,
                out BusinessRules.Entities.Currency currency);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.smartRecommendationsRepository.GetBudgetKpiEstimations(
                customerId,
                accountId,
                campaignId,
                language.Value,
                campaignGoal.Value,
                business,
                criterions,
                smartListings,
                ads,
                currency);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            KpiEstimations(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var modelStateValidation = this.modelStateValidator.Validate(this.ModelState);

            if (modelStateValidation.Errors.OrEmpty().Any())
            {
                return modelStateValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            parameters.TryGetValue("Currency", out object val);
            var currency = BusinessRules.Entities.Currency.UsDollar;
            if (val != null)
            {
                currency = (BusinessRules.Entities.Currency)val;
            }

            var result = await this.smartRecommendationsRepository.GetKpiEstimations(
                customerId,
                accountId,
                campaignId,
                currency);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            SmartCampaignConversionMetrics(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataActionParameters parameters)
        {
            var dateRangeValidation = dateRangeValidator.Validate(Request.RequestQueryString());

            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(Request, logger);
            }

            if (dateRangeValidation.Result == null)
            {
                dateRangeValidation.Errors = new List<AdsApiError>
                {
                    new AdsApiError {Code = ApiErrorCodes.InvalidDateRange, Property = "QueryString"}
                };

                return dateRangeValidation.Errors.CreateErrorResponse(Request, logger);
            }

            var result = await this.campaignsRepository.GetSmartCampaignConversionMetrics(
                customerId,
                accountId,
                campaignId,
                dateRangeValidation.Result);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GetExtractedBusinessInfo(
           [FromODataUri] long customerId,
           [FromODataUri] long accountId,
           [FromODataUri] long campaignId,
           ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = new List<AdsApiError>();
            if (!parameters.ContainsKey("url"))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "url", Message = "URL is required." });
                return errors.CreateErrorResponse(Request, logger);
            }
            var url = parameters?["url"] as string;
            if (string.IsNullOrEmpty(url))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "url" });
            }

            if (url != null && !(url.ToLower().StartsWith("http://") || url.ToLower().StartsWith("https://")))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidValue, Property = "url", Message = "Url must have http or https scheme." });
            }

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var refreshCache = false;
            parameters.TryGetValue("refreshCache", out var val);
            var cache = val as bool?;
            if (cache.HasValue)
            {
                refreshCache = cache.Value;
            }

            parameters.TryGetValue("language", out val);
            if (!(val is Entities.Language language))
            {
                language = Entities.Language.English;
            }

            bool? overrideUseGetExtractedBusinessInfoAsyncNew = null;
            if (parameters.TryGetValue("overrideUseGetExtractedBusinessInfoAsyncNew", out val))
            {
                overrideUseGetExtractedBusinessInfoAsyncNew = Convert.ToBoolean(val);
            }

            var result = await _businessInfoExtractionRepository.GetExtractedBusinessInfoAsync(customerId,
                accountId,
                url,
                language,
                refreshCache,
                overrideUseGetExtractedBusinessInfoAsyncNew);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            PrepareExtractedBusinessInfo(
                [FromODataUri] long customerId,
                [FromODataUri] long accountId,
                [FromODataUri] long campaignId,
                ODataActionParameters parameters)
        {
            if (!ModelState.IsValid)
            {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "Invalid parameters.");
            }

            var errors = new List<AdsApiError>();
            var url = parameters?["url"] as string;
            if (string.IsNullOrEmpty(url))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "url" });
            }

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await _businessInfoExtractionRepository.PrepareExtractedBusinessInfoAsync(customerId,
                accountId,
                campaignId,
                url);

            return result.SingleEntityResponse(Request, logger);
        }


        [HttpPost]
        [EnableQuery]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
           GetOriginalBudgetSuggestions(
           int customerId,
           int accountId,
           [FromODataUri] string campaignId,
           [FromBody] ODataActionParameters parameters)
        {
            var stringIdValidationResult = _stringIdValidator.Validate(campaignId, EntityField.McaCampaignId.ToString());
            var errors = stringIdValidationResult.Errors.OrEmpty().ToList();
            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var validation = modelStateValidator.Validate(ModelState);
            if (!validation.Result)
            {
                return validation.Errors.CreateErrorResponse(Request, logger);
            }

            errors = ValidateAndExtractGetBudgetSuggestionsProperties(
               parameters,
               out string url,
               out Entities.Language? language,
               out Entities.CampaignGoal? goal,
               out IEnumerable<McaKeyword> keywords,
               out IEnumerable<Target> targets,
               out List<SearchAd> searchAds,
               out Currency? currency,
               out double? budget);

            if (errors.Any())
            {
                return errors.CreateErrorResponse(Request, logger);
            }

            var result = await campaignsRepository.GetBudgetRecommendation(
                customerId,
                accountId,
                language.Value,
                goal.Value,
                url,
                currency.Value,
                targets,
                keywords.ToList(),
                searchAds,
                budget);

            return result.SingleEntityResponse(Request, logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("LoadCampaignChangeHistoryDetails")]
        public async Task<IActionResult> LoadChangeHistoryDetails(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataQueryOptions<DimensionRow> options,
            ODataActionParameters parameters)
        {
            var reportType = DimensionReportType.ChangeHistoryNewDetailReport;
            IDimensionReportHelper reportHelper = this.dimensionReportHelperFactory.GetDimensionReportHelper(reportType);
            Validation<DateRange> dateRangeValidation = reportHelper.ValidateDateRange(options);

            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(Request, this.logger);
            }

            var pilotingContext = new PilotingContext<ODataQueryOptions<DimensionRow>>(options, this.callContextGetter().AuthorizationContext);
            Validation<QueryOptionsValidationResult> queryOptionsValidation = reportHelper.ValidateGridDataSelection(pilotingContext);

            if (queryOptionsValidation.Errors.OrEmpty().Any())
            {
                return queryOptionsValidation.Errors.CreateErrorResponse(Request, this.logger);
            }

            var selectValidation = reportHelper.ValidateSelectedColumns(options);

            if (selectValidation.Errors.OrEmpty().Any())
            {
                return selectValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            List<AdsApiError> validationErrors = DimensionValidationHelper.ValidateGet(
                this.logger,
                reportType,
                dateRangeValidation.Result,
                queryOptionsValidation.Result.GridDataSelection,
                selectValidation.Result.Values.ToList(),
                campaignId,
                null,
                false);

            if (validationErrors.Any())
            {
                return validationErrors.CreateErrorResponse(Request, this.logger);
            }

            if (parameters == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (!parameters.TryGetValue("ChangeHistorySummaryRows", out object parameterValue))
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityDoesNotExist, Property = "ChangeHistorySummaryRows", Message = "Missing param ChangeHistorySummaryRows" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (parameterValue is not IEnumerable<ChangeHistorySummaryRow> changeHistorySummaryRows)
            {
                validationErrors.Add(new AdsApiError
                {
                    Code = ApiErrorCodes.EntityIsUnsupported,
                    Property = "ChangeHistorySummaryRows",
                    Message = "The type of ChangeHistorySummaryRows should be IEnumerable<ChangeHistorySummaryRow>"
                });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            List<ChangeHistorySummaryRow> changeHistoryRows = changeHistorySummaryRows.ToList();

            if (!changeHistoryRows.Any())
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityDoesNotExist, Property = "ChangeHistorySummaryRows", Message = "ChangeHistorySummaryRows are empty" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            var reportRepository = (IChangeHistoryNewDimensionReportRepository)this.dimensionRepositoryFactory.GetReportRepository(
                DimensionReportScope.Account,
                reportType);

            var lcid = options.GetLcidFromHeader();
            var result = await reportRepository.GetChangeHistoryDetails(
                customerId,
                accountId,
                reportType,
                dateRangeValidation.Result,
                queryOptionsValidation.Result.GridDataSelection,
                selectValidation.Result,
                lcid,
                changeHistoryRows,
                campaignId,
                null).ConfigureAwait(false);

            return result.MultipleEntitiesResponse(base.Request, base.logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementFullControl)]
        [LatencyPerfCounter("UpgradeCampaignsToPMax")]
        public async Task<IActionResult> UpgradeCampaignsToPMax(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataActionParameters parameters)
        {
            var validationErrors = new List<AdsApiError>();
            if (parameters == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            parameters.TryGetValue("Campaigns", out object campaignsObject);

            if (campaignsObject == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (campaignsObject is not IEnumerable<EntityIdParentId> campaigns)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }
            var result = await campaignsRepository.ConvertSmartShoppingToPerformanceMax(accountId, campaigns);
            return result.MultipleEntitiesResponse(this.Request, this.logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementFullControl)]
        [LatencyPerfCounter("UpgradeDSACampaignsToPMax")]
        public async Task<IActionResult> UpgradeDSACampaignsToPMax(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataActionParameters parameters)
        {
            var validationErrors = new List<AdsApiError>();
            if (parameters == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            parameters.TryGetValue("Campaigns", out object campaignsObject);

            if (campaignsObject == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (campaignsObject is not IEnumerable<EntityIdParentId> campaigns)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            var result = await campaignsRepository.ConvertDSAToPMaxCampaign(accountId, campaigns);
            return result.MultipleEntitiesResponse(this.Request, this.logger);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementFullControl)]
        [LatencyPerfCounter("GetDSACampaignsToPMaxStatus")]
        public async Task<IActionResult> GetDSACampaignsToPMaxStatus(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataActionParameters parameters)
        {
            var validationErrors = new List<AdsApiError>();
            if (parameters == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            parameters.TryGetValue("Campaigns", out object campaignsObject);

            if (campaignsObject == null)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (campaignsObject is not IEnumerable<EntityIdParentId> campaigns)
            {
                validationErrors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null" });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            var result = await campaignsRepository.GetDSAToPMAXCampaignMigrationStatus(accountId, campaigns);
            return result.MultipleEntitiesResponse(this.Request, this.logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("CampaignConversionDiagnostics")]
        public async Task<IActionResult> CampaignConversionDiagnostics(
        [FromODataUri] long customerId,
        [FromODataUri] long accountId,
        [FromODataUri] long campaignId,
        ODataQueryOptions<Campaign> options)
        {
            var campaignDiagnostics = await campaignsRepository.GetConversionDiagnostics(customerId, accountId, campaignId);
            return campaignDiagnostics.MultipleEntitiesResponse(this.Request, logger);
        }

        private List<AdsApiError> ValidateAndExtractGetBudgetSuggestionsProperties(
            ODataActionParameters parameters,
            out string url,
            out Entities.Language? language,
            out Entities.CampaignGoal? goal,
            out IEnumerable<McaKeyword> keywords,
            out IEnumerable<Target> targets,
            out List<SearchAd> searchAds,
            out Currency? currency,
            out double? budget)
        {
            var errors = new List<AdsApiError>();
            object val = null;

            parameters.TryGetValue("Url", out val);
            url = val as string;
            if (string.IsNullOrEmpty(url))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "url" });
            }

            parameters.TryGetValue("Language", out val);
            language = val as Entities.Language?;
            if (language == null)
            {
                language = Entities.Language.English;
            }

            goal = parameters["Goal"] as Entities.CampaignGoal?;

            parameters.TryGetValue("Keywords", out val);
            keywords = val as IEnumerable<McaKeyword>;
            if (keywords.EnumerableIsNullOrEmpty())
            {
                keywords = Array.Empty<McaKeyword>();
            }

            parameters.TryGetValue("Targets", out val);
            targets = val as IEnumerable<Target>;
            if (targets.EnumerableIsNullOrEmpty())
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "targets" });
            }

            parameters.TryGetValue("SearchAds", out val);
            var mcaAds = val as IEnumerable<McaAd>;
            searchAds = new List<SearchAd>();
            foreach (McaAd mcaAd in mcaAds.OrEmpty())
            {
                if (mcaAd.AdType == AdType.SearchAd && mcaAd.Status == McaAdStatus.Active)
                {
                    searchAds.Add((SearchAd)mcaAd);
                }
            }
            parameters.TryGetValue("Currency", out val);
            currency = val as Currency?;
            if (currency == null)
            {
                currency = Currency.UsDollar;
            }

            parameters.TryGetValue("Budget", out val);
            budget = val as double?;

            return errors;
        }


        private void SetSelectExpandClauseByOptions(ODataQueryOptions<Campaign> options)
        {
            string rawSelect = null;
            string rawExpand = null;

            if (options.SelectExpand != null)
            {
                rawSelect = options.SelectExpand.RawSelect;

                if (string.IsNullOrEmpty(options.SelectExpand.RawExpand))
                {
                    rawExpand = "Budget";
                }
                else if (!options.SelectExpand.RawExpand.Contains("Budget"))
                {
                    rawExpand = "Budget" + "," + options.SelectExpand.RawExpand;
                }
                else
                {
                    rawExpand = options.SelectExpand.RawExpand;
                }
            }
            else
            {
                rawExpand = "Budget";
            }

            if (rawSelect != null || rawExpand != null)
            {
                var parser = new ODataQueryOptionParser(
                    options.Context.Model,
                    options.Context.ElementType,
                    null,
                    new Dictionary<string, string> { { "$select", rawSelect }, { "$expand", rawExpand } });

                Request.ODataProperties().SelectExpandClause = parser.ParseSelectAndExpand().CompactPropertyPaths();
            }
        }

        private List<AdsApiError> ValidateAndExtractPrepareAdRecommendationProperties(
            ODataActionParameters parameters,
            out Model.Business business)
        {
            var errors = new List<AdsApiError>();

            business = parameters?["business"] as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business" });
            }
            else if (string.IsNullOrWhiteSpace(business.Website))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business.Website" });
            }

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractPrepareRecommendationProperties(
            ODataActionParameters parameters,
            out string website,
            out IEnumerable<string> entities)
        {
            var errors = new List<AdsApiError>();

            website = parameters?["Website"] as string;
            if (website == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Website", Message = "Website is null" });
            }

            entities = parameters?["Entities"] as IEnumerable<string>;
            if (entities.EnumerableIsNullOrEmpty())
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Entities", Message = "Entities is null or empty" });
            }
            else
            {
                var supportEntities = new HashSet<string>(new List<string> { "ad", "keyword" });
                foreach (var entity in entities)
                {
                    if (!supportEntities.Contains(entity))
                    {
                        errors.Add(new AdsApiError { Code = ApiErrorCodes.UnknownEntity, Property = "Entities", Message = "Invalid Entity - " + entity });
                    }
                }
            }

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractCreateAdRecommendationProperties(
            ODataActionParameters parameters,
            out IEnumerable<string> categories,
            out Model.Business business,
            out IEnumerable<Model.Criterion> criterions,
            out int count,
            out Entities.Language language)
        {
            var errors = new List<AdsApiError>();

            categories = parameters?["categories"] as IEnumerable<string>;
            if (categories == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "categories" });
            }

            business = parameters?["business"] as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business" });
            }
            else if (string.IsNullOrWhiteSpace(business.Website))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business.Website" });
            }

            criterions = parameters?["criterions"] as IEnumerable<Model.Criterion>;
            if (criterions == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "criterions" });
            }
            else if (criterions.Where(c => !(c is Model.LocationCriterion || c is Model.RadiusCriterion)).Any())
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "criterions" });
            }

            count = 0;
            if (parameters?["count"] == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "count" });
            }
            else
            {
                count = (int)parameters["count"];
            }

            parameters.TryGetValue("Language", out var val);
            var languageValue = val as Entities.Language?;
            language = languageValue ?? Entities.Language.English;

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractCompleteAdRecommendationProperties(
            ODataActionParameters parameters,
            out Model.Business business,
            out int count,
            out Model.Ad userAd,
            out Entities.AdAutoCompleteType adAutoCompleteType)
        {
            var errors = new List<AdsApiError>();

            business = parameters?["business"] as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business" });
            }
            else if (string.IsNullOrWhiteSpace(business.Website))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business.Website" });
            }

            count = 0;
            if (parameters?["count"] == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "count" });
            }
            else
            {
                count = (int)parameters["count"];
            }

            userAd = parameters?["userad"] as Model.Ad;
            if (userAd == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "userad" });
            }

            parameters.TryGetValue("completetype", out object val);
            if (val == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "completetype" });
            }
            adAutoCompleteType = val != null ? (Entities.AdAutoCompleteType)val : Entities.AdAutoCompleteType.AdTitle1;

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractAdPreviewRecommendationProperties(
            ODataActionParameters parameters,
            out string category,
            out Model.Business business,
            out IEnumerable<Model.Criterion> criterions,
            out bool isTitle3OrDescription2Requested)
        {
            var errors = new List<AdsApiError>();

            category = parameters?["category"] as string;
            if (string.IsNullOrEmpty(category))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "category" });
            }

            business = parameters?["business"] as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business" });
            }

            criterions = parameters?["criterions"] as IEnumerable<Model.Criterion>;
            if (criterions == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "criterions" });
            }
            else if (criterions.Where(c => !(c is Model.LocationCriterion || c is Model.RadiusCriterion)).Any())
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "criterions" });
            }

            object title3OrDescription2ReturnedValue = null;
            parameters?.TryGetValue("isTitle3OrDescription2Requested", out title3OrDescription2ReturnedValue);
            isTitle3OrDescription2Requested = (title3OrDescription2ReturnedValue as bool?) == true;

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractCreateAdAssetRecommendationProperties(
            ODataActionParameters parameters,
            out IEnumerable<string> categories,
            out Model.Business business,
            out IEnumerable<Model.Criterion> criterions,
            out int count,
            out int headlineCount,
            out int descriptionCount,
            out Entities.AggregationServiceCaller caller)
        {
            var errors = new List<AdsApiError>();

            if (parameters != null && parameters.ContainsKey("categories") && parameters["categories"] != null)
            {
                categories = parameters["categories"] as IEnumerable<string>;
            }
            else
            {
                categories = null;
            }

            business = parameters?["business"] as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business" });
            }
            else if (string.IsNullOrWhiteSpace(business.Website))
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "business.Website" });
            }

            if (parameters != null && parameters.ContainsKey("criterions") && parameters["criterions"] != null)
            {
                criterions = parameters["criterions"] as IEnumerable<Model.Criterion>;
                if (criterions != null && criterions.Where(c => !(c is Model.LocationCriterion || c is Model.RadiusCriterion)).Any())
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "criterions" });
                }
            }
            else
            {
                criterions = null;
            }

            count = 0;
            if (parameters?["count"] == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "count" });
            }
            else
            {
                count = (int)parameters["count"];
            }

            headlineCount = 0;
            if (parameters != null && parameters.ContainsKey("headlineCount") && parameters["headlineCount"] != null)
            {
                headlineCount = (int)parameters["headlineCount"];
            }


            descriptionCount = 0;
            if (parameters != null && parameters.ContainsKey("descriptionCount") && parameters["descriptionCount"] != null)
            {
                descriptionCount = (int)parameters["descriptionCount"];
            }

            caller = Entities.AggregationServiceCaller.expert;
            if (parameters != null && parameters.ContainsKey("caller") && parameters["caller"] != null)
            {
                var callername = parameters["caller"] as string;
                if (Enum.TryParse(callername, out Entities.AggregationServiceCaller callerEnum))
                {
                    caller = callerEnum;
                }
            }

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractSmartEstimationProperties(
            ODataActionParameters parameters,
            out Entities.Language? language,
            out Entities.CampaignGoal? campaignGoal,
            out Model.Business business,
            out IEnumerable<Model.Criterion> criterions,
            out IEnumerable<Model.SmartListing> smartListings,
            out IEnumerable<Model.Ad> ads,
            out BusinessRules.Entities.Currency currency,
            bool allowSmartListingsNull = false,
            bool allowAdsNull = false)
        {
            var errors = new List<AdsApiError>();

            object val;

            parameters.TryGetValue("Language", out val);
            language = val as Entities.Language?;
            if (language == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Language" });
            }

            parameters.TryGetValue("CampaignGoal", out val);
            campaignGoal = val as Entities.CampaignGoal?;
            if (campaignGoal == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "CampaignGoal" });
            }

            parameters.TryGetValue("Business", out val);
            business = val as Model.Business;
            if (business == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Business" });
            }

            parameters.TryGetValue("Criterions", out val);
            criterions = val as IEnumerable<Model.Criterion>;
            if (criterions == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Criterions" });
            }

            parameters.TryGetValue("SmartListings", out val);
            smartListings = val as IEnumerable<Model.SmartListing>;
            if (!allowSmartListingsNull && smartListings == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "SmartListings" });
            }

            parameters.TryGetValue("Ads", out val);
            ads = val as IEnumerable<Model.Ad>;
            if (!allowAdsNull && ads == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.EntityIsNull, Property = "Ads" });
            }

            parameters.TryGetValue("Currency", out val);
            currency = val != null ? (BusinessRules.Entities.Currency)val : BusinessRules.Entities.Currency.UsDollar;

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractImageRecommendationProperties(
            ODataActionParameters parameters,
            out string url,
            out string source,
            out IEnumerable<string> sources,
            out Entities.ImageRankingCriteria rankingCriteria,
            out IEnumerable<Entities.SelectedImage> selectedImages,
            out Entities.ImageRecommendationOptions options,
            out Entities.ImageRecommendationPageDefination paging,
            out Entities.AggregationServiceCaller caller)
        {
            var errors = new List<AdsApiError>();

            object val;

            if (parameters.TryGetValue("url", out val))
            {
                url = val as string;
            }
            else
            {
                url = null;
            }

            if (parameters.TryGetValue("source", out val))
            {
                source = val as string;
            }
            else
            {
                source = null;
            }

            if (parameters.TryGetValue("sources", out val))
            {
                sources = val as IEnumerable<string>;
            }
            else
            {
                sources = null;
            }

            if (parameters.TryGetValue("rankingCriteria", out val))
            {
                rankingCriteria = val as Entities.ImageRankingCriteria;
            }
            else
            {
                rankingCriteria = null;
            }

            if (parameters.TryGetValue("selectedImages", out val))
            {
                selectedImages = val as IEnumerable<Entities.SelectedImage>;
            }
            else
            {
                selectedImages = null;
            }

            if (parameters.TryGetValue("options", out val))
            {
                options = val as Entities.ImageRecommendationOptions;
            }
            else
            {
                options = null;
            }

            if (parameters.TryGetValue("paging", out val))
            {
                paging = val as Entities.ImageRecommendationPageDefination;
                if (paging.Page < 0)
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidValue, Property = "paging.page" });
                }
                if (paging.PageSize <= 0)
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidValue, Property = "paging.pageSize" });
                }
            }
            else
            {
                paging = null;
            }

            caller = Entities.AggregationServiceCaller.searchcampaign_mma;
            if (parameters.TryGetValue("caller", out val))
            {
                if (Enum.TryParse(val as string, out Entities.AggregationServiceCaller callerEnum))
                {
                    caller = callerEnum;
                }
            }

            return errors;
        }

        private List<AdsApiError> ValidateAndExtractMediaGenerationProperties(
            ODataActionParameters parameters,
            out string jobId,
            out Entities.ImageGeneration inputImage,
            out Entities.MediaOutput output,
            out Entities.AggregationServiceCaller caller)
        {
            var errors = new List<AdsApiError>();
            object val = null;

            parameters.TryGetValue("JobId", out val);
            jobId = val as string;

            parameters.TryGetValue("Image", out val);
            inputImage = val as Entities.ImageGeneration;
            if (inputImage == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "Image" });
            }
            else
            {
                if (inputImage.BaseImageUrl == null)
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "Image.BaseImageUrl" });
                }

                if (inputImage.Crop == null && inputImage.Filters == null)
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "Image.Filter" });
                }
            }

            parameters.TryGetValue("Output", out val);
            output = val as Entities.MediaOutput;
            if (output == null)
            {
                errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "Output" });
            }
            else
            {
                if (output.MediaType == null)
                {
                    errors.Add(new AdsApiError { Code = ApiErrorCodes.InvalidEntity, Property = "Output.MediaType" });
                }
            }

            caller = Entities.AggregationServiceCaller.searchcampaign_mma;
            if (parameters.TryGetValue("caller", out val))
            {
                if (Enum.TryParse(val as string, out Entities.AggregationServiceCaller callerEnum))
                {
                    caller = callerEnum;
                }
            }

            return errors;
        }


        // Auto expand does not work for child types currently in Asp.NET Core
        private void AddExpandForCreateResponsiveSearchAdRecommendation(ODataQueryOptions<ResponsiveSearchAd> options)
        {
            string expand = $"{ResponsiveSearchAd.HeadlinesPropertyPath},{ResponsiveSearchAd.DescriptionsPropertyPath}";
            var parser = new ODataQueryOptionParser(
                options.Context.Model,
                options.Context.ElementType,
                null,
                new Dictionary<string, string> { { "$expand", expand } });

            Request.ODataProperties().SelectExpandClause = parser.ParseSelectAndExpand();
        }
    }
}
