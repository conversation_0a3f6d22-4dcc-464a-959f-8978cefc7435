package com.microsoft.bingads.internal.restful;

import java.util.Map;
import java.util.concurrent.Future;
import java.util.function.BiFunction;
import java.util.function.Supplier;

import jakarta.xml.ws.AsyncHandler;
import jakarta.xml.ws.Response;

import com.microsoft.bingads.ApiEnvironment;
import com.microsoft.bingads.InternalException;
import com.microsoft.bingads.v13.${entity.Context}.*;

public class AdInsightService extends RestfulServiceClient implements IAdInsightService {

	public AdInsightService(Map<String, String> headers, ApiEnvironment env) {
        super(headers, env, IAdInsightService.class);

    }
	
	Exception processFaultDetail(ApplicationFault faultDetail, boolean shouldThrow) 
			throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception {
		String message = getFaultMessage(faultDetail.getTrackingId());

		if (faultDetail instanceof AdApiFaultDetail) {
			AdApiFaultDetail_Exception ex = new AdApiFaultDetail_Exception(message, (AdApiFaultDetail)faultDetail);
			
			if (shouldThrow) {				
				throw ex;
			} 
			
			return ex;
		}

		if (faultDetail instanceof ApiFaultDetail) {
			ApiFaultDetail_Exception ex = new ApiFaultDetail_Exception(message, (ApiFaultDetail)faultDetail);
			
			if (shouldThrow) {				
				throw ex;
			} 
			
			return ex;
		}

		if (shouldThrow) {
			throw new RuntimeException("Unknown fault type: " + faultDetail.getClass());
		}

		return null;
	}

	protected <Req, Resp> Resp sendRequest(Req request, String entityEndpoint, String verb, Class<Resp> respClass)
			throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception {
		ResponseInfo<Resp, ApplicationFault> responseInfo = getResponseInfo(request, entityEndpoint, verb, respClass, ApplicationFault.class);

		if (responseInfo == null) {
			return null;
		}

		ApplicationFault faultDetail = responseInfo.getFaultDetail();

		if (faultDetail != null) {
			processFaultDetail(faultDetail, true);
		}

		return responseInfo.getResponse();
	}
	
	private Exception getFaultException(ApplicationFault faultDetail) {
		try {
			Exception ex = processFaultDetail(faultDetail, false);

			return ex;
		} catch (Exception ex) {
			throw new InternalException(ex);
		}
	}

	protected <Req, Resp> Response<Resp> sendRequestAsync(Req request, String entityEndpoint, String verb, Class<Resp> respClass, AsyncHandler<Resp> handler) {
		return processRequestAsync(request, entityEndpoint, verb, respClass, ApplicationFault.class, x -> getFaultException(x), handler);
	}
	#foreach( $field in $entity.Attributes )
	
	public ${field.name}Response ${field.InitialValue}(${field.name}Request request)
		  throws AdApiFaultDetail_Exception, ApiFaultDetail_Exception {
		${field.name}Response response = sendRequest(request, "${field.Label}", ${field.DefaultValue}, ${field.name}Response.class);
		
		return response;
	}

    public Response<${field.name}Response> ${field.InitialValue}Async(${field.name}Request request) {
		return sendRequestAsync(request, "${field.Label}", ${field.DefaultValue}, ${field.name}Response.class, null);
	}

	public Future<?> ${field.InitialValue}Async(${field.name}Request request, AsyncHandler<${field.name}Response> asyncHandler) {
		return sendRequestAsync(request, "${field.Label}", ${field.DefaultValue}, ${field.name}Response.class, asyncHandler);
	}
	
    #end
}