@Context(campaignmanagement)
ComplexTypesWithEnum {
  GetCampaignsByAccountIdRequest : string { #SpecialProperty };
  GetCampaignsByIdsRequest : string { #SpecialProperty };
  GetAdGroupsByIdsRequest : string { #SpecialProperty };
  GetAdGroupsByCampaignIdRequest : string { #SpecialProperty };
  GetAdsByEditorialStatusRequest : string { #SpecialProperty };
  GetAdsByIdsRequest : string { #SpecialProperty };
  GetAdsByAdGroupIdRequest : string { #SpecialProperty };
  GetKeywordsByEditorialStatusRequest : string { #SpecialProperty };
  GetKeywordsByIdsRequest : string { #SpecialProperty };
  GetKeywordsByAdGroupIdRequest : string { #SpecialProperty };
  GetAdExtensionsByIdsRequest : string { #SpecialProperty };
  GetAdExtensionsAssociationsRequest : string { #SpecialProperty };
  GetAdExtensionIdsByAccountIdRequest : string { #SpecialProperty };
  GetMediaMetaDataByAccountIdRequest : string { #SpecialProperty };
  GetMediaMetaDataByIdsRequest : string { #SpecialProperty };
  GetMediaAssociationsRequest : string { #SpecialProperty };
  GetAdGroupCriterionsByIdsRequest : string { #SpecialProperty };
  AddAdGroupCriterionsRequest : string { #SpecialProperty };
  UpdateAdGroupCriterionsRequest : string { #SpecialProperty };
  DeleteAdGroupCriterionsRequest : string { #SpecialProperty };
  GetAssetGroupListingGroupsByIdsRequest : string { #SpecialProperty };
  GetBMCStoresByCustomerIdRequest : string { #SpecialProperty };
  AddCampaignCriterionsRequest : string { #SpecialProperty };
  UpdateCampaignCriterionsRequest : string { #SpecialProperty };
  DeleteCampaignCriterionsRequest : string { #SpecialProperty };
  GetCampaignCriterionsByIdsRequest : string { #SpecialProperty };
  GetBidStrategiesByIdsRequest : string { #SpecialProperty };
  GetAudienceGroupsByIdsRequest : string { #SpecialProperty };
  GetAssetGroupsByIdsRequest : string { #SpecialProperty };
  GetAssetGroupsByCampaignIdRequest : string { #SpecialProperty };
  GetAudiencesByIdsRequest : string { #SpecialProperty };
  GetConversionGoalsByIdsRequest : string { #SpecialProperty };
  GetConversionGoalsByTagIdsRequest : string { #SpecialProperty };
  GetProfileDataFileUrlRequest : string { #SpecialProperty };
  GetImportResultsRequest : string { #SpecialProperty };
  GetImportJobsByIdsRequest : string { #SpecialProperty };
  CreateResponsiveAdRecommendationRequest : string { #SpecialProperty };
  GetSupportedFontsRequest : string { #SpecialProperty };
  AppInstallAd : string { #SpecialProperty };
  ResponsiveAd : string { #SpecialProperty };
  HotelSetting : string { #SpecialProperty };
  AppSetting : string { #SpecialProperty };
  Campaign : string { #SpecialProperty };
  AppAdExtension : string { #SpecialProperty };
  MediaAssociation : string { #SpecialProperty };
  AudienceCriterion : string { #SpecialProperty };
  ProfileCriterion : string { #SpecialProperty };
  BidStrategy : string { #SpecialProperty };
  AudienceInfo : string { #SpecialProperty };
  ProfileInfo : string { #SpecialProperty };
  Audience : string { #SpecialProperty };
  ProductAudience : string { #SpecialProperty };
  ConversionGoal : string { #SpecialProperty };
  AppInstallGoal : string { #SpecialProperty };
  DataExclusion : string { #SpecialProperty };
  SeasonalityAdjustment : string { #SpecialProperty };
  AudienceConditionItem : string { #SpecialProperty };
  DeviceCondition : string { #SpecialProperty };
}
