@Context(reporting)
ArrayOfTypes {
  AccountPerformanceReportColumn : string { @DefaultValue(AccountPerformanceReportColumn) @Label( "AccountPerformanceReportColumns") } ;
  CampaignPerformanceReportColumn : string { @DefaultValue(CampaignPerformanceReportColumn) @Label( "CampaignPerformanceReportColumns") } ;
  CampaignReportScope : string { @DefaultValue(CampaignReportScope) @Label( "CampaignReportScopes") } ;
  AdDynamicTextPerformanceReportColumn : string { @DefaultValue(AdDynamicTextPerformanceReportColumn) @Label( "AdDynamicTextPerformanceReportColumns") } ;
  AdGroupReportScope : string { @DefaultValue(AdGroupReportScope) @Label( "AdGroupReportScopes") } ;
  AdGroupPerformanceReportColumn : string { @DefaultValue(AdGroupPerformanceReportColumn) @Label( "AdGroupPerformanceReportColumns") } ;
  AdPerformanceReportColumn : string { @DefaultValue(AdPerformanceReportColumn) @Label( "AdPerformanceReportColumns") } ;
  KeywordPerformanceReportColumn : string { @DefaultValue(KeywordPerformanceReportColumn) @Label( "KeywordPerformanceReportColumns") } ;
  KeywordPerformanceReportSort : string { @DefaultValue(KeywordPerformanceReportSort) @Label( "KeywordPerformanceReportSorts") } ;
  DestinationUrlPerformanceReportColumn : string { @DefaultValue(DestinationUrlPerformanceReportColumn) @Label( "DestinationUrlPerformanceReportColumns") } ;
  BudgetSummaryReportColumn : string { @DefaultValue(BudgetSummaryReportColumn) @Label( "BudgetSummaryReportColumns") } ;
  AgeGenderAudienceReportColumn : string { @DefaultValue(AgeGenderAudienceReportColumn) @Label( "AgeGenderAudienceReportColumns") } ;
  ProfessionalDemographicsAudienceReportColumn : string { @DefaultValue(ProfessionalDemographicsAudienceReportColumn) @Label( "ProfessionalDemographicsAudienceReportColumns") } ;
  UserLocationPerformanceReportColumn : string { @DefaultValue(UserLocationPerformanceReportColumn) @Label( "UserLocationPerformanceReportColumns") } ;
  PublisherUsagePerformanceReportColumn : string { @DefaultValue(PublisherUsagePerformanceReportColumn) @Label( "PublisherUsagePerformanceReportColumns") } ;
  SearchQueryPerformanceReportColumn : string { @DefaultValue(SearchQueryPerformanceReportColumn) @Label( "SearchQueryPerformanceReportColumns") } ;
  ConversionPerformanceReportColumn : string { @DefaultValue(ConversionPerformanceReportColumn) @Label( "ConversionPerformanceReportColumns") } ;
  GoalsAndFunnelsReportColumn : string { @DefaultValue(GoalsAndFunnelsReportColumn) @Label( "GoalsAndFunnelsReportColumns") } ;
  NegativeKeywordConflictReportColumn : string { @DefaultValue(NegativeKeywordConflictReportColumn) @Label( "NegativeKeywordConflictReportColumns") } ;
  SearchCampaignChangeHistoryReportColumn : string { @DefaultValue(SearchCampaignChangeHistoryReportColumn) @Label( "SearchCampaignChangeHistoryReportColumns") } ;
  AdExtensionByAdReportColumn : string { @DefaultValue(AdExtensionByAdReportColumn) @Label( "AdExtensionByAdReportColumns") } ;
  AdExtensionByKeywordReportColumn : string { @DefaultValue(AdExtensionByKeywordReportColumn) @Label( "AdExtensionByKeywordReportColumns") } ;
  AudiencePerformanceReportColumn : string { @DefaultValue(AudiencePerformanceReportColumn) @Label( "AudiencePerformanceReportColumns") } ;
  AdExtensionDetailReportColumn : string { @DefaultValue(AdExtensionDetailReportColumn) @Label( "AdExtensionDetailReportColumns") } ;
  ShareOfVoiceReportColumn : string { @DefaultValue(ShareOfVoiceReportColumn) @Label( "ShareOfVoiceReportColumns") } ;
  ProductDimensionPerformanceReportColumn : string { @DefaultValue(ProductDimensionPerformanceReportColumn) @Label( "ProductDimensionPerformanceReportColumns") } ;
  ProductPartitionPerformanceReportColumn : string { @DefaultValue(ProductPartitionPerformanceReportColumn) @Label( "ProductPartitionPerformanceReportColumns") } ;
  ProductPartitionUnitPerformanceReportColumn : string { @DefaultValue(ProductPartitionUnitPerformanceReportColumn) @Label( "ProductPartitionUnitPerformanceReportColumns") } ;
  ProductSearchQueryPerformanceReportColumn : string { @DefaultValue(ProductSearchQueryPerformanceReportColumn) @Label( "ProductSearchQueryPerformanceReportColumns") } ;
  ProductMatchCountReportColumn : string { @DefaultValue(ProductMatchCountReportColumn) @Label( "ProductMatchCountReportColumns") } ;
  ProductNegativeKeywordConflictReportColumn : string { @DefaultValue(ProductNegativeKeywordConflictReportColumn) @Label( "ProductNegativeKeywordConflictReportColumns") } ;
  CallDetailReportColumn : string { @DefaultValue(CallDetailReportColumn) @Label( "CallDetailReportColumns") } ;
  GeographicPerformanceReportColumn : string { @DefaultValue(GeographicPerformanceReportColumn) @Label( "GeographicPerformanceReportColumns") } ;
  DSASearchQueryPerformanceReportColumn : string { @DefaultValue(DSASearchQueryPerformanceReportColumn) @Label( "DSASearchQueryPerformanceReportColumns") } ;
  DSAAutoTargetPerformanceReportColumn : string { @DefaultValue(DSAAutoTargetPerformanceReportColumn) @Label( "DSAAutoTargetPerformanceReportColumns") } ;
  DSACategoryPerformanceReportColumn : string { @DefaultValue(DSACategoryPerformanceReportColumn) @Label( "DSACategoryPerformanceReportColumns") } ;
  HotelDimensionPerformanceReportColumn : string { @DefaultValue(HotelDimensionPerformanceReportColumn) @Label( "HotelDimensionPerformanceReportColumns") } ;
  HotelGroupPerformanceReportColumn : string { @DefaultValue(HotelGroupPerformanceReportColumn) @Label( "HotelGroupPerformanceReportColumns") } ;
  AssetGroupPerformanceReportColumn : string { @DefaultValue(AssetGroupPerformanceReportColumn) @Label( "AssetGroupPerformanceReportColumns") } ;
  AssetGroupReportScope : string { @DefaultValue(AssetGroupReportScope) @Label( "AssetGroupReportScopes") } ;
  SearchInsightPerformanceReportColumn : string { @DefaultValue(SearchInsightPerformanceReportColumn) @Label( "SearchInsightPerformanceReportColumns") } ;
  AssetPerformanceReportColumn : string { @DefaultValue(AssetPerformanceReportColumn) @Label( "AssetPerformanceReportColumns") } ;
  CategoryInsightsReportColumn : string { @DefaultValue(CategoryInsightsReportColumn) @Label( "CategoryInsightsReportColumns") } ;
  CategoryClickCoverageReportColumn : string { @DefaultValue(CategoryClickCoverageReportColumn) @Label( "CategoryClickCoverageReportColumns") } ;
  CombinationPerformanceReportColumn : string { @DefaultValue(CombinationPerformanceReportColumn) @Label( "CombinationPerformanceReportColumns") } ;
  AppsPerformanceReportColumn : string { @DefaultValue(AppsPerformanceReportColumn) @Label( "AppsPerformanceReportColumns") } ;
  FeedItemPerformanceReportColumn : string { @DefaultValue(FeedItemPerformanceReportColumn) @Label( "FeedItemPerformanceReportColumns") } ;
  TravelQueryInsightReportColumn : string { @DefaultValue(TravelQueryInsightReportColumn) @Label( "TravelQueryInsightReportColumns") } ;
  BatchError : string { @DefaultValue(BatchError) @Label( "BatchErrors") } ;
  OperationError : string { @DefaultValue(OperationError) @Label( "OperationErrors") } ;
  long : string { @DefaultValue(Long) @Label( "Longs") } ;
  int : string { @DefaultValue(Integer) @Label( "Ints") } ;
  string : string { @DefaultValue(String) @Label( "Strings") } ;
  AdApiError : string { @DefaultValue(AdApiError) @Label( "AdApiErrors") } ;
}
