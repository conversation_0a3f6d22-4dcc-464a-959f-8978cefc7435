﻿/*
* THIS FILE CONTAINS SOME EXTRA LOGIC THAN WHAT WAS GENERATED FROM THE SCRIPT. Please port
* the extra logic if this file needs to be updated to a newer version from the script.
* 
* Auto-Generated by the MIScripts tool, see:
* $(EnlistmentRoot)\private\Campaign\MT\Source\Import\ImportData\MIScripts\MIScripts\readme.md
*/

namespace Microsoft.Advertising.Client.GoogleSync.GoogleAdsProxy.GoogleAds.GoogleServiceResources
{
    using Microsoft.Advertising.Client.GoogleSync.GoogleAdsProxy.GoogleAds.GoogleAdsService;
    using System.Collections.Generic;
    using System.Globalization;

    public interface ICampaignResourceField : IGoogleAdsResourceField {}
    public class CampaignResourceField : GoogleAdsResourceField, ICampaignResourceField,
    // Add Resource types that can be `SelectWith` Campaign.
        IAdGroupAdResourceField, IAdGroupAdLabelResourceField, IAdGroupBidModifierResourceField, IAdGroupCriterionResourceField,
        IAdGroupCriterionLabelResourceField, IAdGroupLabelResourceField, IAdGroupResourceField, ICampaignCriterionResourceField,
        ICampaignLabelResourceField, ICampaignSharedSetResourceField, IChangeStatusResourceField, ICampaignAssetResourceField,
        ISmartCampaignSettingResourceField, ICampaignCustomizerResourceField, IAdGroupCustomizerResourceField,
        IAdGroupCriterionCustomizerResourceField, IAssetGroupResourceField, IAssetGroupListingGroupFilterResourceField,
        IAssetGroupAssetResourceField, IAssetGroupSignalResourceField, ICampaignAssetSetResourceField
    { }

    public class CampaignSelectQuery : GoogleAdsSelectQuery<ICampaignResourceField>
    {
        public override string ResourceName => "campaign";
        public CampaignSelectQuery(
            ICampaignResourceField[] selectedFields,
            IEnumerable<Condition> conditions = null,
            Ordering[] orderings = null,
            long limit = 0) : base(selectedFields, conditions, orderings, limit) {}
    }

    /// <summary>
    /// BingAds mirror to: https://developers.google.com/google-ads/api/fields/v20/campaign
    /// </summary>
    public static class CampaignResource
    {
        public static readonly CampaignResourceField AccessibleBiddingStrategy = new CampaignResourceField
        {
            Name = "campaign.accessible_bidding_strategy",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AccessibleBiddingStrategy?.ToString(),
        };

        public static readonly CampaignResourceField AdServingOptimizationStatus = new CampaignResourceField
        {
            Name = "campaign.ad_serving_optimization_status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AdServingOptimizationStatus.ToString(),
        };

        public static readonly CampaignResourceField AdvertisingChannelSubType = new CampaignResourceField
        {
            Name = "campaign.advertising_channel_sub_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AdvertisingChannelSubType.ToString(),
        };

        public static readonly CampaignResourceField AdvertisingChannelType = new CampaignResourceField
        {
            Name = "campaign.advertising_channel_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AdvertisingChannelType.ToString(),
        };

        public static readonly CampaignResourceField AppCampaignSettingAppId = new CampaignResourceField
        {
            Name = "campaign.app_campaign_setting.app_id",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AppCampaignSetting?.AppId?.ToString(),
        };

        public static readonly CampaignResourceField AppCampaignSettingAppStore = new CampaignResourceField
        {
            Name = "campaign.app_campaign_setting.app_store",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AppCampaignSetting?.AppStore.ToString(),
        };

        public static readonly CampaignResourceField AppCampaignSettingBiddingStrategyGoalType = new CampaignResourceField
        {
            Name = "campaign.app_campaign_setting.bidding_strategy_goal_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AppCampaignSetting?.BiddingStrategyGoalType.ToString(),
        };

        public static readonly CampaignResourceField AssetAutomationSettings = new CampaignResourceField
        {
            Name = "campaign.asset_automation_settings",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // simple handling for asset automation settings.
                var assetAutomationSettings = googleAdsRow?.Campaign?.AssetAutomationSettings;
                if (assetAutomationSettings == null) { return null; }

                foreach (var assetAutomationSetting in assetAutomationSettings)
                {
                    if (assetAutomationSetting.HasAssetAutomationType && assetAutomationSetting.HasAssetAutomationStatus)
                    {
                        // In V18, only real type is Text: https://developers.google.com/google-ads/api/reference/rpc/v18/AssetAutomationTypeEnum.AssetAutomationType
                        // It's a repeated field... probably shouldn't be IMO, but we'll take the first valid one
                        if (assetAutomationSetting.AssetAutomationType == GoogleAdsApi.Enums.AssetAutomationTypeEnum.Types.AssetAutomationType.TextAssetAutomation)
                        {
                            return (assetAutomationSetting.AssetAutomationStatus == GoogleAdsApi.Enums.AssetAutomationStatusEnum.Types.AssetAutomationStatus.OptedOut).ToString();
                        }
                    }
                }
                return null;
            },
        };

        public static readonly CampaignResourceField AudienceSettingUseAudienceGrouped = new CampaignResourceField
        {
            Name = "campaign.audience_setting.use_audience_grouped",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.AudienceSetting?.UseAudienceGrouped.ToString(),
        };

        public static readonly CampaignResourceField BaseCampaign = new CampaignResourceField
        {
            Name = "campaign.base_campaign",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BaseCampaign?.ToString(),
        };

        public static readonly CampaignResourceField BiddingStrategy = new CampaignResourceField
        {
            Name = "campaign.bidding_strategy",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BiddingStrategy?.ToString(),
        };

        public static readonly CampaignResourceField BiddingStrategySystemStatus = new CampaignResourceField
        {
            Name = "campaign.bidding_strategy_system_status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BiddingStrategySystemStatus.ToString(),
        };

        public static readonly CampaignResourceField BiddingStrategyType = new CampaignResourceField
        {
            Name = "campaign.bidding_strategy_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BiddingStrategyType.ToString(),
        };

        public static readonly CampaignResourceField BrandGuidelinesAccentColor = new CampaignResourceField
        {
            Name = "campaign.brand_guidelines.accent_color",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BrandGuidelines?.AccentColor?.ToString(),
        };

        public static readonly CampaignResourceField BrandGuidelinesMainColor = new CampaignResourceField
        {
            Name = "campaign.brand_guidelines.main_color",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BrandGuidelines?.MainColor?.ToString(),
        };

        public static readonly CampaignResourceField BrandGuidelinesPredefinedFontFamily = new CampaignResourceField
        {
            Name = "campaign.brand_guidelines.predefined_font_family",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.BrandGuidelines?.PredefinedFontFamily?.ToString(),
        };

        public static readonly CampaignResourceField BrandGuidelinesEnabled = new CampaignResourceField
        {
            Name = "campaign.brand_guidelines_enabled",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var campaign = googleAdsRow?.Campaign;
                return campaign?.HasBrandGuidelinesEnabled == true ? campaign.BrandGuidelinesEnabled.ToString() : null;
            },
        };

        public static readonly CampaignResourceField CampaignBudget = new CampaignResourceField
        {
            Name = "campaign.campaign_budget",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.CampaignBudget?.ToString(),
        };

        public static readonly CampaignResourceField CampaignGroup = new CampaignResourceField
        {
            Name = "campaign.campaign_group",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.CampaignGroup?.ToString(),
        };

        public static readonly CampaignResourceField CommissionCommissionRateMicros = new CampaignResourceField
        {
            Name = "campaign.commission.commission_rate_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.Commission?.CommissionRateMicros.ToString(),
        };

        public static readonly CampaignResourceField DemandGenCampaignSettingsUpgradedTargeting = new CampaignResourceField
        {
            Name = "campaign.demand_gen_campaign_settings.upgraded_targeting",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.DemandGenCampaignSettings?.UpgradedTargeting.ToString(),
        };

        public static readonly CampaignResourceField DynamicSearchAdsSettingDomainName = new CampaignResourceField
        {
            Name = "campaign.dynamic_search_ads_setting.domain_name",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.DynamicSearchAdsSetting?.DomainName?.ToString(),
        };

        public static readonly CampaignResourceField DynamicSearchAdsSettingLanguageCode = new CampaignResourceField
        {
            Name = "campaign.dynamic_search_ads_setting.language_code",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.DynamicSearchAdsSetting?.LanguageCode?.ToString(),
        };

        public static readonly CampaignResourceField DynamicSearchAdsSettingUseSuppliedUrlsOnly = new CampaignResourceField
        {
            Name = "campaign.dynamic_search_ads_setting.use_supplied_urls_only",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.DynamicSearchAdsSetting?.UseSuppliedUrlsOnly.ToString(),
        };

        public static readonly CampaignResourceField EndDate = new CampaignResourceField
        {
            Name = "campaign.end_date",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.EndDate?.ToString(),
        };

        public static readonly CampaignResourceField ExcludedParentAssetFieldTypes = new CampaignResourceField
        {
            Name = "campaign.excluded_parent_asset_field_types",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ExcludedParentAssetFieldTypes.ToString(),
        };

        public static readonly CampaignResourceField ExcludedParentAssetSetTypes = new CampaignResourceField
        {
            Name = "campaign.excluded_parent_asset_set_types",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ExcludedParentAssetSetTypes.ToString(),
        };

        public static readonly CampaignResourceField ExperimentType = new CampaignResourceField
        {
            Name = "campaign.experiment_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ExperimentType.ToString(),
        };

        public static readonly CampaignResourceField FinalUrlSuffix = new CampaignResourceField
        {
            Name = "campaign.final_url_suffix",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.FinalUrlSuffix?.ToString(),
        };

        public static readonly CampaignResourceField FixedCpmGoal = new CampaignResourceField
        {
            Name = "campaign.fixed_cpm.goal",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.FixedCpm?.Goal.ToString(),
        };

        public static readonly CampaignResourceField FixedCpmTargetFrequencyInfoTargetCount = new CampaignResourceField
        {
            Name = "campaign.fixed_cpm.target_frequency_info.target_count",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.FixedCpm?.TargetFrequencyInfo?.TargetCount.ToString(),
        };

        public static readonly CampaignResourceField FixedCpmTargetFrequencyInfoTimeUnit = new CampaignResourceField
        {
            Name = "campaign.fixed_cpm.target_frequency_info.time_unit",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.FixedCpm?.TargetFrequencyInfo?.TimeUnit.ToString(),
        };

        public static readonly CampaignResourceField FrequencyCaps = new CampaignResourceField
        {
            Name = "campaign.frequency_caps",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.FrequencyCaps?.ToString(),
        };

        public static readonly CampaignResourceField GeoTargetTypeSettingNegativeGeoTargetType = new CampaignResourceField
        {
            Name = "campaign.geo_target_type_setting.negative_geo_target_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.GeoTargetTypeSetting?.NegativeGeoTargetType.ToString(),
        };

        public static readonly CampaignResourceField GeoTargetTypeSettingPositiveGeoTargetType = new CampaignResourceField
        {
            Name = "campaign.geo_target_type_setting.positive_geo_target_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.GeoTargetTypeSetting?.PositiveGeoTargetType.ToString(),
        };

        public static readonly CampaignResourceField HotelPropertyAssetSet = new CampaignResourceField
        {
            Name = "campaign.hotel_property_asset_set",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.HotelPropertyAssetSet?.ToString(),
        };

        public static readonly CampaignResourceField HotelSettingHotelCenterId = new CampaignResourceField
        {
            Name = "campaign.hotel_setting.hotel_center_id",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.HotelSetting?.HotelCenterId.ToString(),
        };

        public static readonly CampaignResourceField Id = new CampaignResourceField
        {
            Name = "campaign.id",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.Id.ToString(),
        };

        public static readonly CampaignResourceField KeywordMatchType = new CampaignResourceField
        {
            Name = "campaign.keyword_match_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.KeywordMatchType.ToString(),
        };

        public static readonly CampaignResourceField Labels = new CampaignResourceField
        {
            Name = "campaign.labels",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.Labels?.ToString(),
        };

        public static readonly CampaignResourceField ListingType = new CampaignResourceField
        {
            Name = "campaign.listing_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ListingType.ToString(),
        };

        public static readonly CampaignResourceField LocalCampaignSettingLocationSourceType = new CampaignResourceField
        {
            Name = "campaign.local_campaign_setting.location_source_type",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.LocalCampaignSetting?.LocationSourceType.ToString(),
        };

        public static readonly CampaignResourceField LocalServicesCampaignSettingsCategoryBids = new CampaignResourceField
        {
            Name = "campaign.local_services_campaign_settings.category_bids",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.LocalServicesCampaignSettings?.CategoryBids?.ToString(),
        };

        public static readonly CampaignResourceField ManualCpa = new CampaignResourceField
        {
            Name = "campaign.manual_cpa",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ManualCpa?.ToString(),
        };

        public static readonly CampaignResourceField ManualCpcEnhancedCpcEnabled = new CampaignResourceField
        {
            Name = "campaign.manual_cpc.enhanced_cpc_enabled",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ManualCpc?.EnhancedCpcEnabled.ToString(),
        };

        public static readonly CampaignResourceField ManualCpm = new CampaignResourceField
        {
            Name = "campaign.manual_cpm",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ManualCpm?.ToString(),
        };

        public static readonly CampaignResourceField ManualCpv = new CampaignResourceField
        {
            Name = "campaign.manual_cpv",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ManualCpv?.ToString(),
        };

        public static readonly CampaignResourceField MaximizeConversionValueTargetRoas = new CampaignResourceField
        {
            Name = "campaign.maximize_conversion_value.target_roas",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.MaximizeConversionValue?.TargetRoas.ToString(CultureInfo.InvariantCulture),
        };

        // The generated name for this field is MaximizeConversionsTargetCpaMicros,
        // Update the name to be consistent with previous versions.
        public static readonly CampaignResourceField MaximizeConversionsTargetCpa = new CampaignResourceField
        {
            Name = "campaign.maximize_conversions.target_cpa_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.MaximizeConversions?.TargetCpaMicros.ToString(),
        };

        public static readonly CampaignResourceField Name = new CampaignResourceField
        {
            Name = "campaign.name",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.Name?.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetContentNetwork = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_content_network",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetContentNetwork.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetGoogleSearch = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_google_search",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetGoogleSearch.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetGoogleTvNetwork = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_google_tv_network",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetGoogleTvNetwork.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetPartnerSearchNetwork = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_partner_search_network",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetPartnerSearchNetwork.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetSearchNetwork = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_search_network",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetSearchNetwork.ToString(),
        };

        public static readonly CampaignResourceField NetworkSettingsTargetYoutube = new CampaignResourceField
        {
            Name = "campaign.network_settings.target_youtube",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.NetworkSettings?.TargetYoutube.ToString(),
        };

        public static readonly CampaignResourceField OptimizationGoalSettingOptimizationGoalTypes = new CampaignResourceField
        {
            Name = "campaign.optimization_goal_setting.optimization_goal_types",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.OptimizationGoalSetting?.OptimizationGoalTypes.ToString(),
        };

        public static readonly CampaignResourceField OptimizationScore = new CampaignResourceField
        {
            Name = "campaign.optimization_score",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.OptimizationScore.ToString(CultureInfo.InvariantCulture),
        };

        public static readonly CampaignResourceField PaymentMode = new CampaignResourceField
        {
            Name = "campaign.payment_mode",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PaymentMode.ToString(),
        };

        public static readonly CampaignResourceField PercentCpcCpcBidCeilingMicros = new CampaignResourceField
        {
            Name = "campaign.percent_cpc.cpc_bid_ceiling_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PercentCpc?.CpcBidCeilingMicros.ToString(),
        };

        public static readonly CampaignResourceField PercentCpcEnhancedCpcEnabled = new CampaignResourceField
        {
            Name = "campaign.percent_cpc.enhanced_cpc_enabled",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PercentCpc?.EnhancedCpcEnabled.ToString(),
        };

        public static readonly CampaignResourceField PerformanceMaxUpgradePerformanceMaxCampaign = new CampaignResourceField
        {
            Name = "campaign.performance_max_upgrade.performance_max_campaign",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PerformanceMaxUpgrade?.PerformanceMaxCampaign?.ToString(),
        };

        public static readonly CampaignResourceField PerformanceMaxUpgradePreUpgradeCampaign = new CampaignResourceField
        {
            Name = "campaign.performance_max_upgrade.pre_upgrade_campaign",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PerformanceMaxUpgrade?.PreUpgradeCampaign?.ToString(),
        };

        public static readonly CampaignResourceField PerformanceMaxUpgradeStatus = new CampaignResourceField
        {
            Name = "campaign.performance_max_upgrade.status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PerformanceMaxUpgrade?.Status.ToString(),
        };

        public static readonly CampaignResourceField PmaxCampaignSettingsBrandTargetingOverridesIgnoreExclusionsForShoppingAds = new CampaignResourceField
        {
            Name = "campaign.pmax_campaign_settings.brand_targeting_overrides.ignore_exclusions_for_shopping_ads",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PmaxCampaignSettings?.BrandTargetingOverrides?.IgnoreExclusionsForShoppingAds.ToString(),
        };

        public static readonly CampaignResourceField PrimaryStatus = new CampaignResourceField
        {
            Name = "campaign.primary_status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PrimaryStatus.ToString(),
        };

        public static readonly CampaignResourceField PrimaryStatusReasons = new CampaignResourceField
        {
            Name = "campaign.primary_status_reasons",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.PrimaryStatusReasons.ToString(),
        };

        public static readonly CampaignResourceField RealTimeBiddingSettingOptIn = new CampaignResourceField
        {
            Name = "campaign.real_time_bidding_setting.opt_in",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.RealTimeBiddingSetting?.OptIn.ToString(),
        };

        public static readonly CampaignResourceField ResourceName = new CampaignResourceField
        {
            Name = "campaign.resource_name",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ResourceName?.ToString(),
        };

        public static readonly CampaignResourceField SelectiveOptimizationConversionActions = new CampaignResourceField
        {
            Name = "campaign.selective_optimization.conversion_actions",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.SelectiveOptimization?.ConversionActions?.ToString(),
        };

        public static readonly CampaignResourceField ServingStatus = new CampaignResourceField
        {
            Name = "campaign.serving_status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ServingStatus.ToString(),
        };

        public static readonly CampaignResourceField ShoppingSettingAdvertisingPartnerIds = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.advertising_partner_ids",
            Filterable = true,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ShoppingSetting?.AdvertisingPartnerIds.ToString(),
        };

        public static readonly CampaignResourceField ShoppingSettingCampaignPriority = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.campaign_priority",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var shoppingSetting = googleAdsRow?.Campaign?.ShoppingSetting;
                return shoppingSetting?.HasCampaignPriority == true ? shoppingSetting.CampaignPriority.ToString() : null;
            },
        };

        public static readonly CampaignResourceField ShoppingSettingDisableProductFeed = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.disable_product_feed",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ShoppingSetting?.DisableProductFeed.ToString(),
        };

        public static readonly CampaignResourceField ShoppingSettingEnableLocal = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.enable_local",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var shoppingSetting = googleAdsRow?.Campaign?.ShoppingSetting;
                return shoppingSetting?.HasEnableLocal == true ? shoppingSetting.EnableLocal.ToString() : null;
            },
        };

        public static readonly CampaignResourceField ShoppingSettingFeedLabel = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.feed_label",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ShoppingSetting?.FeedLabel?.ToString(),
        };

        public static readonly CampaignResourceField ShoppingSettingMerchantId = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.merchant_id",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var shoppingSetting = googleAdsRow?.Campaign?.ShoppingSetting;
                return shoppingSetting?.HasMerchantId == true ? shoppingSetting.MerchantId.ToString() : null;
            },
        };

        public static readonly CampaignResourceField ShoppingSettingUseVehicleInventory = new CampaignResourceField
        {
            Name = "campaign.shopping_setting.use_vehicle_inventory",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.ShoppingSetting?.UseVehicleInventory.ToString(),
        };

        public static readonly CampaignResourceField StartDate = new CampaignResourceField
        {
            Name = "campaign.start_date",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.StartDate?.ToString(),
        };

        public static readonly CampaignResourceField Status = new CampaignResourceField
        {
            Name = "campaign.status",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.Status.ToString(),
        };

        public static readonly CampaignResourceField TargetCpaCpcBidCeilingMicros = new CampaignResourceField
        {
            Name = "campaign.target_cpa.cpc_bid_ceiling_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetCpa = googleAdsRow?.Campaign?.TargetCpa;
                return targetCpa?.HasCpcBidCeilingMicros == true ? targetCpa.CpcBidCeilingMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetCpaCpcBidFloorMicros = new CampaignResourceField
        {
            Name = "campaign.target_cpa.cpc_bid_floor_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetCpa = googleAdsRow?.Campaign?.TargetCpa;
                return targetCpa?.HasCpcBidFloorMicros == true ? targetCpa.CpcBidFloorMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetCpaTargetCpaMicros = new CampaignResourceField
        {
            Name = "campaign.target_cpa.target_cpa_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetCpa = googleAdsRow?.Campaign?.TargetCpa;
                return targetCpa?.HasTargetCpaMicros == true ? targetCpa.TargetCpaMicros.ToString() : null;
            },
        };

        //TargetCpm is deprecated in v18 & v20
        public static readonly CampaignResourceField TargetCpm = new CampaignResourceField
        {
            Name = "campaign.target_cpm",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetCpm?.ToString(),
        };

        public static readonly CampaignResourceField TargetCpmTargetFrequencyGoalTargetCount = new CampaignResourceField
        {
            Name = "campaign.target_cpm.target_frequency_goal.target_count",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetCpm?.TargetFrequencyGoal?.TargetCount.ToString(),
        };

        public static readonly CampaignResourceField TargetCpmTargetFrequencyGoalTimeUnit = new CampaignResourceField
        {
            Name = "campaign.target_cpm.target_frequency_goal.time_unit",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetCpm?.TargetFrequencyGoal?.TimeUnit.ToString(),
        };

        public static readonly CampaignResourceField TargetCpv = new CampaignResourceField
        {
            Name = "campaign.target_cpv",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetCpv?.ToString(),
        };

        public static readonly CampaignResourceField TargetImpressionShareCpcBidCeilingMicros = new CampaignResourceField
        {
            Name = "campaign.target_impression_share.cpc_bid_ceiling_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetImpressionShare = googleAdsRow?.Campaign?.TargetImpressionShare;
                return targetImpressionShare?.HasCpcBidCeilingMicros == true ? targetImpressionShare.CpcBidCeilingMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetImpressionShareLocation = new CampaignResourceField
        {
            Name = "campaign.target_impression_share.location",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetImpressionShare?.Location.ToString(),
        };

        public static readonly CampaignResourceField TargetImpressionShareLocationFractionMicros = new CampaignResourceField
        {
            Name = "campaign.target_impression_share.location_fraction_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetImpressionShare = googleAdsRow?.Campaign?.TargetImpressionShare;
                return targetImpressionShare?.HasLocationFractionMicros == true ? targetImpressionShare.LocationFractionMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetRoasCpcBidCeilingMicros = new CampaignResourceField
        {
            Name = "campaign.target_roas.cpc_bid_ceiling_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetRoas = googleAdsRow?.Campaign?.TargetRoas;
                return targetRoas?.HasCpcBidCeilingMicros == true ? targetRoas.CpcBidCeilingMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetRoasCpcBidFloorMicros = new CampaignResourceField
        {
            Name = "campaign.target_roas.cpc_bid_floor_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetRoas = googleAdsRow?.Campaign?.TargetRoas;
                return targetRoas?.HasCpcBidFloorMicros == true ? targetRoas.CpcBidFloorMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetRoasTargetRoas = new CampaignResourceField
        {
            Name = "campaign.target_roas.target_roas",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetRoas = googleAdsRow?.Campaign?.TargetRoas;
                return targetRoas?.HasTargetRoas_ == true ? targetRoas.TargetRoas_.ToString(CultureInfo.InvariantCulture) : null;
            },
        };

        public static readonly CampaignResourceField TargetSpendCpcBidCeilingMicros = new CampaignResourceField
        {
            Name = "campaign.target_spend.cpc_bid_ceiling_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetSpend = googleAdsRow?.Campaign?.TargetSpend;
                return targetSpend?.HasCpcBidCeilingMicros == true ? targetSpend.CpcBidCeilingMicros.ToString() : null;
            },
        };

        [System.Obsolete]  // TargetSpendMicros is deprecated.
        public static readonly CampaignResourceField TargetSpendTargetSpendMicros = new CampaignResourceField
        {
            Name = "campaign.target_spend.target_spend_micros",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            // CONVENTION BREAK
            Serialize = googleAdsRow =>
            {
                // Nullable Handling
                var targetSpend = googleAdsRow?.Campaign?.TargetSpend;
                return targetSpend?.HasTargetSpendMicros == true ? targetSpend.TargetSpendMicros.ToString() : null;
            },
        };

        public static readonly CampaignResourceField TargetingSettingTargetRestrictions = new CampaignResourceField
        {
            Name = "campaign.targeting_setting.target_restrictions",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TargetingSetting?.TargetRestrictions?.ToString(),
        };

        public static readonly CampaignResourceField TrackingSettingTrackingUrl = new CampaignResourceField
        {
            Name = "campaign.tracking_setting.tracking_url",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TrackingSetting?.TrackingUrl?.ToString(),
        };

        public static readonly CampaignResourceField TrackingUrlTemplate = new CampaignResourceField
        {
            Name = "campaign.tracking_url_template",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TrackingUrlTemplate?.ToString(),
        };

        public static readonly CampaignResourceField TravelCampaignSettingsTravelAccountId = new CampaignResourceField
        {
            Name = "campaign.travel_campaign_settings.travel_account_id",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.TravelCampaignSettings?.TravelAccountId.ToString(),
        };

        public static readonly CampaignResourceField UrlCustomParameters = new CampaignResourceField
        {
            Name = "campaign.url_custom_parameters",
            Filterable = false,
            Selectable = true,
            Sortable = false,
            Repeated = true,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.UrlCustomParameters?.ToString(),
        };

        public static readonly CampaignResourceField UrlExpansionOptOut = new CampaignResourceField
        {
            Name = "campaign.url_expansion_opt_out",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.UrlExpansionOptOut.ToString(),
        };

        public static readonly CampaignResourceField VanityPharmaVanityPharmaDisplayUrlMode = new CampaignResourceField
        {
            Name = "campaign.vanity_pharma.vanity_pharma_display_url_mode",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VanityPharma?.VanityPharmaDisplayUrlMode.ToString(),
        };

        public static readonly CampaignResourceField VanityPharmaVanityPharmaText = new CampaignResourceField
        {
            Name = "campaign.vanity_pharma.vanity_pharma_text",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VanityPharma?.VanityPharmaText.ToString(),
        };

        public static readonly CampaignResourceField VideoBrandSafetySuitability = new CampaignResourceField
        {
            Name = "campaign.video_brand_safety_suitability",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoBrandSafetySuitability.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdFormatControlFormatRestriction = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_format_control.format_restriction",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdFormatControl?.FormatRestriction.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdFormatControlNonSkippableInStreamRestrictionsMaxDuration = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_format_control.non_skippable_in_stream_restrictions.max_duration",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdFormatControl?.NonSkippableInStreamRestrictions?.MaxDuration.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdFormatControlNonSkippableInStreamRestrictionsMinDuration = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_format_control.non_skippable_in_stream_restrictions.min_duration",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdFormatControl?.NonSkippableInStreamRestrictions?.MinDuration.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdInventoryControlAllowInFeed = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_inventory_control.allow_in_feed",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdInventoryControl?.AllowInFeed.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdInventoryControlAllowInStream = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_inventory_control.allow_in_stream",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdInventoryControl?.AllowInStream.ToString(),
        };

        public static readonly CampaignResourceField VideoCampaignSettingsVideoAdInventoryControlAllowShorts = new CampaignResourceField
        {
            Name = "campaign.video_campaign_settings.video_ad_inventory_control.allow_shorts",
            Filterable = true,
            Selectable = true,
            Sortable = true,
            Repeated = false,
            Serialize = googleAdsRow => googleAdsRow?.Campaign?.VideoCampaignSettings?.VideoAdInventoryControl?.AllowShorts.ToString(),
        };
    }
}
