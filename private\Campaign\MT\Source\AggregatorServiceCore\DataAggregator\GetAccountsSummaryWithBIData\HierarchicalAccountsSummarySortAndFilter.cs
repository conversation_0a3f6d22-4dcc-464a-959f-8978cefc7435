﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.AggregatorService
{
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Message.Aggregator2.CustomColumn;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities;
    using Microsoft.Advertising.Client.Import;
    using Microsoft.BingAds.Utils.SortUtils;
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;

    public class HierarchicalAccountsSummarySortAndFilter
    {
        private ILogShared logger;
        private GridDataSelection gridDataSelection;
        private HierarchicalAccountsSummaryCacheItem cacheItem;
        private CultureInfo culture;
        private bool usePeriodComparisonBiData;
        private bool loadDirectlyLinkedAccounts;

        private readonly BiData emptyBiData;

        private static HashSet<string> TaxInformationKeysForSorting = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase)
        {
            "CPF",
            "CNPJ",
            "PANNumber",
            "VatNumber",
            "NZGSTNumber",
            "AUGSTNumber",
        };

        public HierarchicalAccountsSummarySortAndFilter(
            ILogShared logger,
            GridDataSelection gridDataSelection,
            HierarchicalAccountsSummaryCacheItem cacheItem,
            bool usePeriodComparisonBiData,
            bool loadDirectlyLinkedAccounts,
            IEnumerable<CustomColumnAggregatorDefinition> customColumnDefinitions)
        {
            if (logger == null)
            {
                throw new ArgumentNullException(nameof(logger));
            }

            if (cacheItem == null)
            {
                throw new ArgumentNullException(nameof(cacheItem));
            }

            this.gridDataSelection = gridDataSelection;
            this.logger = logger;
            this.cacheItem = cacheItem;
            this.usePeriodComparisonBiData = usePeriodComparisonBiData;
            this.loadDirectlyLinkedAccounts = loadDirectlyLinkedAccounts;

            this.culture = CultureInfo.InvariantCulture;
            int? cultureInfoLCID = this.gridDataSelection?.LocalizationLookup?.CultureInfoLCID;

            if (cultureInfoLCID.HasValue && cultureInfoLCID != 0)
            {
                this.culture = new CultureInfo(cultureInfoLCID.Value);
            }

            this.emptyBiData = BiData.InitializeBiData();
            this.emptyBiData = this.emptyBiData.GetSummary(allowNulls: true, customColumnDefinitions: customColumnDefinitions);
        }

        public IList<HierarchicalAccountId> Execute()
        {
            List<HierarchicalAccountId> keys = this.loadDirectlyLinkedAccounts
                ? this.cacheItem.DirectedLinkedHierarchicalAccountIds
                : this.cacheItem.HierarchicalAccountIds;

            if (keys == null)
            {
                return new List<HierarchicalAccountId>();
            }

            IEnumerable<HierarchicalAccountId> filteredKeys = Filter(keys.AsParallel());

            IList<HierarchicalAccountId> filteredAndSorted;
            if (this.gridDataSelection?.GridSort != null)
            {
                // If UI pass sort column, first deleted Accounts will
                // be put into the bottom of page, then sort by selected column.
                filteredAndSorted = Sort(filteredKeys.AsParallel());
            }
            else
            {
                // Default sort should return rows in the same order as returned from client center for accounts under each customer.
                // With the additional desire to have the deleted accounts under each customer at the end under each customer.
                filteredAndSorted = SortByAccountStatus(filteredKeys.ToList());
            }

            return filteredAndSorted;
        }

        private IList<HierarchicalAccountId> SortByAccountStatus(List<HierarchicalAccountId> input)
        {
            List<HierarchicalAccountId> resultKeys = new List<HierarchicalAccountId>();

            using (this.logger.AcquireNamedPerfLogger("HierarchicalAccountsSummarySortAndFilter", "SortByAccountStatus"))
            {
                int i = 0;
                var pageInfo = new PageInfo();
                while (i < input.Count)
                {
                    AccountInfo2 accountInfo = this.cacheItem.Accounts[input.ElementAt(i)];

                    // Keep CCMT order if it is customer.
                    if (accountInfo.AccountType == AccountType.ManagerAccount)
                    {
                        resultKeys.Add(input.ElementAt(i++));
                    }
                    // Make minor change to re-order sub accounts under same customer.
                    else
                    {
                        int startIndex = i;
                        int endIndex = i + 1;
                        int hierarchyParentId = this.cacheItem.Accounts[input[startIndex]].HierarchyParentId;

                        while (endIndex < input.Count &&
                            this.cacheItem.Accounts[input[endIndex]].AccountType == AccountType.AdAccount &&
                            hierarchyParentId == this.cacheItem.Accounts[input[endIndex]].HierarchyParentId)
                        {
                            ++endIndex;
                        }

                        // All sub accounts those belong same costomer with the additional desire to have the deleted accounts at the bottom     
                        IOrderedEnumerable<HierarchicalAccountId> adAccountsResult = AggregatorEntityOperations.SortNonStringHelper(
                            input.GetRange(startIndex, endIndex - startIndex),
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.IsAccountDeleted),
                            pageInfo,
                            true);

                        // Then second tier sort follow by account ID.
                        resultKeys.AddRange(adAccountsResult.ThenBy(accountId => accountId.Id));

                        i = endIndex;
                    }
                }
            }

            return resultKeys;
        }

        private IList<HierarchicalAccountId> Sort(IEnumerable<HierarchicalAccountId> input)
        {
            IEnumerable<HierarchicalAccountId> resultKeys;

            using (this.logger.AcquireNamedPerfLogger("HierarchicalAccountsSummarySortAndFilter", "SortAccounts"))
            {
                // If user select specific sort column, we assume that it will break CCMT Hierarchical accounts order.
                // Always sort by account deleted status first.
                // All deleted accounts will be put into the bottom of the page.

                IOrderedPagedEnumerable<HierarchicalAccountId> firstSort = AggregatorEntityOperations.SortNonStringHelper(
                    input,
                    (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.IsAccountDeleted),
                    new PageInfo(),
                    isAsc: true);

                GridSort gridSort = this.gridDataSelection.GridSort;
                bool isAsc = gridSort.SortDirection == SortDirection.ASCENDING;

                PeriodComparisonOptions options = PeriodComparisonOptions.None;
                if (this.usePeriodComparisonBiData)
                {
                    options = gridSort.PeriodComparsionSortOptions;

                    switch (options)
                    {
                        case PeriodComparisonOptions.Change:
                        case PeriodComparisonOptions.ChangePercentage:
                        case PeriodComparisonOptions.Period:
                        case PeriodComparisonOptions.None:
                            break;

                        default:
                            throw new InvalidOperationException($"Sort - AccountsSummary - Unknown PeriodComparisonOptions value: {options}");
                    }
                }

                var cultureStringComparer = StringComparer.Create(this.culture, true);

                switch (gridSort.SortByGridColumn)
                {
                    case GridColumn.AccountStatus:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Status),
                            isAsc);
                        break;

                    case GridColumn.AccountType:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.AccountType),
                            isAsc);
                        break;

                    case GridColumn.SimplifiedAccountMode:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.AccountMode),
                            isAsc);
                        break;

                    case GridColumn.AccountName:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Name),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountId:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.Id),
                            isAsc);
                        break;

                    case GridColumn.Currency:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.CurrencyCode),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.Owner:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.CustomerName),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.BillToCustomerName:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.BillToCustomerName),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountInactiveReasonsCount:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountData) => accountData.AccountInactiveReasonsCount),
                            isAsc);
                        break;

                    case GridColumn.QualityScore:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountQualityScoreMemberOrDefault(accountId, (qualityScoreData) => qualityScoreData.OverallQualityScore),
                            isAsc);
                        break;

                    // Additional Info
                    case GridColumn.AccountPaymentType:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.PaymentType),
                            isAsc);
                        break;

                    case GridColumn.AccountBalanceAmountBalance:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.Balance),
                            isAsc);
                        break;

                    case GridColumn.AccountLanguageType:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.LanguageType),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountBusinessAddressCountry:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.Country),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.TimeZone:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.TimeZone),
                            isAsc);
                        break;

                    case GridColumn.AccountTaxId:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.TaxId),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountPaymentInstrumentType:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.PaymentInstrumentType),
                            isAsc);
                        break;

                    case GridColumn.AccountAutoTagType:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.AutoTagType),
                            isAsc);
                        break;

                    case GridColumn.AccountBusinessLegalName:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.BusinessLegalName),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountVerificationStatus:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.AccountVerificationStatus),
                            isAsc);
                        break;

                    case GridColumn.AccountPreferredUserName:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (info) => info.PreferredUserName),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.AccountMode:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountMode2 ?? AccountMode.Legacy),
                            isAsc);
                        break;

                    case GridColumn.AccountIsValidPrimaryContact:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            this.GetIsValidPrimaryContact,
                            isAsc);
                        break;

                    case GridColumn.AccountTaxInformation:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            this.GetTaxInformationSortValue,
                            cultureStringComparer,
                            isAsc);
                        break;

                    // IO
                    case GridColumn.IOPurchaseOrder:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.PurchaseOrder),
                            cultureStringComparer,
                            isAsc);
                        break;

                    case GridColumn.IOBudget:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.Budget.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.RemainingIOBudget:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.RemainingBudget.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.TotalRemainingIOBudget:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.TotalRemainingBudget.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.StartDate:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDate.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.EndDate:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EndDate.GetValueOrDefault()),
                            isAsc);
                        break;

                    // budget
                    case GridColumn.DailyBudget:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.DailyBudget.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.MonthlyBudget:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.MonthlyBudget.GetValueOrDefault()),
                            isAsc);
                        break;

                    case GridColumn.LifetimeBudgetAmount:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.LifetimeBudget.GetValueOrDefault()),
                            isAsc);
                        break;

                    // BI Columns
                    case GridColumn.CTR:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ClickThruRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.Clicks:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.Clicks.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.Impressions:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.Impressions.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.TotalEffectiveCost: // "Spend"
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.Spent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.Conversions:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.Conversions.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AverageCPC:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AverageCPC.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AverageCPM:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AverageCPM.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AverageCPV:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AverageCPV.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AveragePosition:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AveragePosition.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.CPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.CPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.RevenueOnAdSpend:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.RevenueOnAdSpend.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.ConversionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ConversionRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AdvertiserReportedRevenue:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AdvertiserReportedRevenue.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.TopImpressionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                           firstSort,
                           (biData) => biData.TopImpressionRate.GetValueOrDefault(),
                           options,
                           isAsc);
                        break;

                    case GridColumn.AbsoluteTopImpressionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.AbsoluteTopImpressionRate.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.SalesCount:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.SalesCount.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.AverageCPS:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.AverageCPS.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.Installs:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.Installs.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.CostPerInstall:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.CostPerInstall.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.RevenuePerInstall:
                        resultKeys = this.SortThenByBIDataColumn(
                          firstSort,
                          (biData) => biData.RevenuePerInstall.GetValueOrDefault(),
                          options,
                          isAsc);
                        break;

                    case GridColumn.VideoViews:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.VideoViews.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.TotalWatchTimeInMS:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.TotalWatchTimeInMS.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AverageWatchTimePerImpression:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AverageWatchTimePerImpression.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AverageWatchTimePerVideoView:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AverageWatchTimePerVideoView.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.Reach:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.Reach.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.VideoViewsAt25Percent:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.VideoViewsAt25Percent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.VideoViewsAt50Percent:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.VideoViewsAt50Percent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.VideoViewsAt75Percent:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.VideoViewsAt75Percent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.CompletedVideoViews:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.CompletedVideoViews.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.VideoCompletionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.VideoCompletionRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break; 

                    // IS Column - The selector must return the nullable value for the correct SoV participation filter logic to work.
                    case GridColumn.AuctionWonPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionWonPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AuctionLostToBudgetPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionLostToBudgetPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AuctionLostToRankPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionLostToRankPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AuctionLostToLandingPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionLostToLandingPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AuctionLostToAdQualityPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionLostToAdQualityPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AuctionLostToBidPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AuctionLostToBidPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AbsoluteTopImpressionSharePercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AbsoluteTopImpressionSharePercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.ClickSharePercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.ClickSharePercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.ExactMatchImpressionSharePercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.ExactMatchImpressionSharePercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.TopImpressionSharePercent:
                        resultKeys = this.SortThenBySovColumn(
                              firstSort,
                              (biData) => biData.TopImpressionSharePercent.GetValueOrDefault(),
                              options,
                              isAsc);
                        break;

                    case GridColumn.TopISLostToBudgetPercent:
                        resultKeys = this.SortThenBySovColumn(
                              firstSort,
                              (biData) => biData.TopISLostToBudgetPercent.GetValueOrDefault(),
                              options,
                              isAsc);
                        break;

                    case GridColumn.TopISLostToRankPercent:
                        resultKeys = this.SortThenBySovColumn(
                             firstSort,
                             (biData) => biData.TopISLostToRankPercent.GetValueOrDefault(),
                             options,
                             isAsc);
                        break;

                    case GridColumn.AbsoluteTopISLostToBudgetPercent:
                        resultKeys = this.SortThenBySovColumn(
                              firstSort,
                              (biData) => biData.AbsoluteTopISLostToBudgetPercent.GetValueOrDefault(),
                              options,
                              isAsc);
                        break;

                    case GridColumn.AbsoluteTopISLostToRankPercent:
                        resultKeys = this.SortThenBySovColumn(
                            firstSort,
                            (biData) => biData.AbsoluteTopISLostToRankPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    // Audience SOV 
                    case GridColumn.AudienceAuctionWonPercent:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.AudienceAuctionWonPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AudienceTopISLostToBudgetPercent:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.AudienceTopISLostToBudgetPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AudienceTopISLostToRankPercent:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.AudienceTopISLostToRankPercent.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.CustomColumn:
                        resultKeys = AggregatorEntityOperations.SortThenByCustomColumn(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.cacheItem.BiResultById, gridSort.CustomColumnId),
                            (HierarchicalAccountId accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, gridSort.CustomColumnId),
                            options,
                            isAsc);
                        break;

                    case GridColumn.AllConversions:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversions.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversions:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversions.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsRevenue:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsRevenue.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsCPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsCPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsReturnOnAdSpend:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsReturnOnAdSpend.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllConversionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversionRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllConversionAdvertiserReportedRevenue:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllConversionRevenueOnAdSpend:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversionRevenueOnAdSpend.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllConversionCPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversionCPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ConversionsCredit:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ConversionsCredit.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.PartialConversionCPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.PartialConversionCPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.PartialConversionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.PartialConversionRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllConversionsCredit:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllConversionsCredit.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllPartialConversionCPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllPartialConversionCPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.AllPartialConversionRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.AllPartialConversionRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsCredit:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsCredit.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughPartialConversionsCPA:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughPartialConversionsCPA.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ViewThroughPartialConversionsRate:
                        resultKeys = this.SortThenByBIDataColumn(
                            firstSort,
                            (biData) => biData.ViewThroughPartialConversionsRate.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    case GridColumn.ImportName:
                        resultKeys = AggregatorEntityOperations.SortThenByStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.ImportName),
                            cultureStringComparer,
                            isAsc);
                        break;
                    case GridColumn.ImportEntityIdsSpecified:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EntityIdsSpecified),
                            isAsc);
                        break;
                    case GridColumn.ImportStartTime:
                        resultKeys = AggregatorEntityOperations.SortThenByNonStringHelper(
                            firstSort,
                            (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDateTime),
                            isAsc);
                        break;
                    case GridColumn.AdvertiserReportedRevenueAdjustment:
                        resultKeys = this.SortThenByBIDataColumn(
                             firstSort,
                             (biData) => biData.AdvertiserReportedRevenueAdjustment.GetValueOrDefault(),
                             options,
                             isAsc);
                        break;
                    case GridColumn.AllConversionAdvertiserReportedRevenueAdjustment:
                        resultKeys = this.SortThenByBIDataColumn(
                             firstSort,
                             (biData) => biData.AllConversionAdvertiserReportedRevenueAdjustment.GetValueOrDefault(),
                             options,
                             isAsc);
                        break;
                    case GridColumn.ViewThroughConversionsRevenueAdjustment:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.ViewThroughConversionsRevenueAdjustment.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ConversionDelayZeroDay:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.ConversionDelayZeroDay.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ConversionDelayNinety:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.ConversionDelayNinety.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;
                    case GridColumn.ConversionDelayNinetyNine:
                        resultKeys = this.SortThenByAudienceSovColumn(
                            firstSort,
                            (biData) => biData.ConversionDelayNinetyNine.GetValueOrDefault(),
                            options,
                            isAsc);
                        break;

                    default:
                        string message = $"Unexpected sort column: {gridSort.SortByGridColumn}";
                        this.logger.LogUserError(message);
                        throw new InvalidOperationException(message);
                }
            }

            return resultKeys.ToList();
        }

        public IOrderedPagedEnumerable<HierarchicalAccountId> SortThenByBIDataColumn(
            IOrderedPagedEnumerable<HierarchicalAccountId> keys,
            Func<BiData, double> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByDoubleBIDataColumn(
                keys,
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<HierarchicalAccountId> SortThenByBIDataColumn(
            IOrderedPagedEnumerable<HierarchicalAccountId> keys,
            Func<BiData, long> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByLongBIDataColumn(
                keys,
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<HierarchicalAccountId> SortThenBySovColumn(
            IOrderedPagedEnumerable<HierarchicalAccountId> keys,
            Func<IBiData, double?> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenBySoVBIDataColumn(
                keys,
                (HierarchicalAccountId accountId) => this.GetSovMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetSovMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector),
                options,
                isAsc);
        }

        public IOrderedPagedEnumerable<HierarchicalAccountId> SortThenByAudienceSovColumn(
            IOrderedPagedEnumerable<HierarchicalAccountId> keys,
            Func<IBiData, double?> biDataItemSelector,
            PeriodComparisonOptions options,
            bool isAsc)
        {
            return AggregatorEntityOperations.SortThenByAudienceSoVBIDataColumn(
                keys,
                (HierarchicalAccountId accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetAudienceSovMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector),
                options,
                isAsc);
        }

        private IEnumerable<HierarchicalAccountId> Filter(IEnumerable<HierarchicalAccountId> input)
        {
            IEnumerable<HierarchicalAccountId> results = input;

            using (logger.AcquireNamedPerfLogger("HierarchicalAccountsSummarySortAndFilter", "Filter"))
            {
                if (this.gridDataSelection != null && this.gridDataSelection.GridFilter != null && this.gridDataSelection.GridFilter.Any())
                {
                    foreach (GridFilterExpression expression in this.gridDataSelection.GridFilter)
                    {
                        results = results.Where(CreateWherePredicate(expression));
                    }
                }
            }

            return results;
        }

        private Func<HierarchicalAccountId, bool> CreateWherePredicate(GridFilterExpression expression)
        {
            Func<HierarchicalAccountId, bool> result;

            PeriodComparisonOptions options = PeriodComparisonOptions.None;

            if (this.usePeriodComparisonBiData)
            {
                options = expression.PeriodComparisonFilterOptions;

                switch (options)
                {
                    case PeriodComparisonOptions.Change:
                    case PeriodComparisonOptions.ChangePercentage:
                    case PeriodComparisonOptions.Period:
                    case PeriodComparisonOptions.None:
                        break;

                    default:
                        throw new InvalidOperationException($"CreateWherePredicate - HierarchicalAccountsSummary - Unknown PeriodComparisonOptions value: {options}");
                }
            }

            switch (expression.ColumnToFilter)
            {
                // Account properties.
                case GridColumn.AccountId:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, int>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Id));
                    break;

                case GridColumn.AccountName:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Name),
                        this.culture);
                    break;

                case GridColumn.AccountNumber:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountNumber),
                        this.culture);
                    break;

                case GridColumn.AccountStatus:
                    result = PredicateHelper.PredicateByEnum<HierarchicalAccountId, AccountStatus>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.Status));
                    break;

                case GridColumn.AccountType:
                    result = PredicateHelper.PredicateByEnum<HierarchicalAccountId, AccountType>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountType));
                    break;

                case GridColumn.SimplifiedAccountMode:
                    result = PredicateHelper.PredicateByEnum<HierarchicalAccountId, SimplifiedAccountMode>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountMode));
                    break;

                case GridColumn.AccountVerificationStatus:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, int?, int>(
                         expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountVerificationStatus),
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountVerificationStatus.Value));
                    break;

                case GridColumn.AccountBusinessLegalName:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.BusinessLegalName),
                        this.culture);
                    break;

                case GridColumn.Currency:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.CurrencyCode),
                        this.culture);
                    break;

                case GridColumn.BillToCustomerName:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.BillToCustomerName),
                        this.culture);
                    break;

                case GridColumn.Owner:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.CustomerName),
                        this.culture);
                    break;

                case GridColumn.AccountInactiveReasons:
                    result = PredicateHelper.PredicateByEnum<HierarchicalAccountId, AccountInactiveReasonCode>(
                        this.GetAccountInactiveReasonCode,
                        expression);
                    break;

                case GridColumn.AvailableInsightTypeIds:
                    result = PredicateHelper.PredicateByEnum<HierarchicalAccountId, FluctuationType>(
                        this.GetPerformanceInsightData,
                        expression);
                    break;

                // Additional Info
                case GridColumn.AccountPaymentType:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, byte?, byte>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.PaymentType),
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.PaymentType.Value));
                    break;

                case GridColumn.AccountBalanceAmountBalance:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, decimal>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.Balance ?? 0));
                    break;

                case GridColumn.AccountLanguageType:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.LanguageType),
                        this.culture);
                    break;

                case GridColumn.AccountBusinessAddressCountry:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.Country),
                        this.culture);
                    break;

                case GridColumn.TimeZone:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, byte?, byte>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.TimeZone),
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.TimeZone.Value));
                    break;

                case GridColumn.AccountTaxId:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.TaxId),
                        this.culture);
                    break;

                case GridColumn.AccountPaymentInstrumentType:
                    // UI treats lack of payment instrument type as zero
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, byte>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.PaymentInstrumentType ?? 0));
                    break;

                case GridColumn.AccountAutoTagType:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, byte?, byte>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.AutoTagType),
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.AutoTagType.Value));
                    break;

                case GridColumn.AccountPreferredUserName:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAdditionalInfoMemberOrDefault(accountId, (accountInfo) => accountInfo.PreferredUserName),
                        this.culture);
                    break;

                case GridColumn.AccountMode:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, byte>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetAccountSummaryRowMemberOrDefault(accountId, (accountInfo) => accountInfo.AccountMode2?.GetByte() ?? (byte)0));
                    break;

                case GridColumn.AccountIsValidPrimaryContact:
                    result = PredicateHelper.PredicateByBool<HierarchicalAccountId>(
                        expression,
                        this.GetIsValidPrimaryContact);
                    break;

                // Insertion Order
                case GridColumn.IOPurchaseOrder:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.PurchaseOrder),
                        this.culture);
                    break;

                case GridColumn.IOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.Budget.GetValueOrDefault()));
                    break;

                case GridColumn.RemainingIOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.RemainingBudget.GetValueOrDefault()));
                    break;

                case GridColumn.TotalRemainingIOBudget:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.TotalRemainingBudget.GetValueOrDefault()));
                    break;

                case GridColumn.StartDate:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, DateTime>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.StartDate.GetValueOrDefault()));
                    break;

                case GridColumn.EndDate:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, DateTime>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetIOMemberOrDefault(accountId, (insertionOrder) => insertionOrder.EndDate.GetValueOrDefault()));
                    break;

                // Budget
                case GridColumn.DailyBudget:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.DailyBudget.GetValueOrDefault()));
                    break;

                case GridColumn.MonthlyBudget:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.MonthlyBudget.GetValueOrDefault()));
                    break;

                case GridColumn.LifetimeBudgetAmount:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, double>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetBudgetMemberOrDefault(accountId, (budget) => budget.LifetimeBudget.GetValueOrDefault()));
                    break;

                // BI Columns
                case GridColumn.CTR:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ClickThruRate.GetValueOrDefault());
                    break;

                case GridColumn.Clicks:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Clicks.GetValueOrDefault());
                    break;

                case GridColumn.Impressions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Impressions.GetValueOrDefault());
                    break;

                case GridColumn.TotalEffectiveCost: // "Spend"
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Spent.GetValueOrDefault());
                    break;

                case GridColumn.Conversions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Conversions.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPC:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPC.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPM:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPM.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPV:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPV.GetValueOrDefault());
                    break;

                case GridColumn.AveragePosition:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AveragePosition.GetValueOrDefault());
                    break;

                case GridColumn.CPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.CPA.GetValueOrDefault());
                    break;

                case GridColumn.RevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.RevenueOnAdSpend.GetValueOrDefault());
                    break;

                case GridColumn.ConversionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionRate.GetValueOrDefault());
                    break;

                case GridColumn.AdvertiserReportedRevenue:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AdvertiserReportedRevenue.GetValueOrDefault());
                    break;

                case GridColumn.TopImpressionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.TopImpressionRate.GetValueOrDefault());
                    break;

                case GridColumn.AbsoluteTopImpressionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AbsoluteTopImpressionRate.GetValueOrDefault());
                    break;

                case GridColumn.SalesCount:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.SalesCount.GetValueOrDefault());
                    break;

                case GridColumn.AverageCPS:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageCPS.GetValueOrDefault());
                    break;

                case GridColumn.Installs:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Installs.GetValueOrDefault());
                    break;

                case GridColumn.CostPerInstall:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.CostPerInstall.GetValueOrDefault());
                    break;

                case GridColumn.RevenuePerInstall:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.RevenuePerInstall.GetValueOrDefault());
                    break;

                case GridColumn.VideoViews:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViews.GetValueOrDefault());
                    break;

                case GridColumn.ViewThroughRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughRate.GetValueOrDefault());
                    break;

                case GridColumn.TotalWatchTimeInMS:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.TotalWatchTimeInMS.GetValueOrDefault());
                    break;

                case GridColumn.AverageWatchTimePerImpression:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageWatchTimePerImpression.GetValueOrDefault());
                    break;

                case GridColumn.AverageWatchTimePerVideoView:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AverageWatchTimePerVideoView.GetValueOrDefault());
                    break;

                case GridColumn.Reach:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.Reach.GetValueOrDefault());
                    break;

                case GridColumn.VideoViewsAt25Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt25Percent.GetValueOrDefault());
                    break;

                case GridColumn.VideoViewsAt50Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt50Percent.GetValueOrDefault());
                    break;

                case GridColumn.VideoViewsAt75Percent:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoViewsAt75Percent.GetValueOrDefault());
                    break;

                case GridColumn.CompletedVideoViews:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.CompletedVideoViews.GetValueOrDefault());
                    break;

                case GridColumn.VideoCompletionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.VideoCompletionRate.GetValueOrDefault());
                    break;

                // IS Columns - The selector must return the nullable value for the correct SoV participation filter logic to work.
                case GridColumn.AuctionWonPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionWonPercent);
                    break;

                case GridColumn.AuctionLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToBudgetPercent);
                    break;

                case GridColumn.AuctionLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToRankPercent);
                    break;

                case GridColumn.AuctionLostToLandingPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToLandingPercent);
                    break;

                case GridColumn.AuctionLostToAdQualityPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToAdQualityPercent);
                    break;

                case GridColumn.ExactMatchImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.ExactMatchImpressionSharePercent);
                    break;

                case GridColumn.AbsoluteTopImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopImpressionSharePercent);
                    break;

                case GridColumn.ClickSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.ClickSharePercent);
                    break;

                case GridColumn.AuctionLostToBidPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AuctionLostToBidPercent);
                    break;

                case GridColumn.TopImpressionSharePercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopImpressionSharePercent);
                    break;

                case GridColumn.TopISLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopISLostToBudgetPercent);
                    break;

                case GridColumn.TopISLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.TopISLostToRankPercent);
                    break;

                case GridColumn.AbsoluteTopISLostToRankPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopISLostToRankPercent);
                    break;

                case GridColumn.AbsoluteTopISLostToBudgetPercent:
                    result = this.PredicateBySoVColumn(expression, (biData) => biData.AbsoluteTopISLostToBudgetPercent);
                    break;

                // Audience SOV Columns
                case GridColumn.AudienceAuctionWonPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceAuctionWonPercent);
                    break;

                case GridColumn.AudienceTopISLostToBudgetPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceTopISLostToBudgetPercent);
                    break;

                case GridColumn.AudienceTopISLostToRankPercent:
                    result = this.PredicateByAudienceSoVColumn(expression, (biData) => biData.AudienceTopISLostToRankPercent);
                    break;

                case GridColumn.QualityScore:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, short>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetAccountQualityScoreMemberOrDefault(accountId, (qualityScore) => qualityScore.OverallQualityScore.GetValueOrDefault()));
                    break;

                case GridColumn.CustomColumn:
                    result = this.PredicateByCustomColumn(expression);
                    break;
                case GridColumn.AllConversions:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversions.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversions:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversions.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRevenue:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsRevenue.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsCPA.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsReturnOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsReturnOnAdSpend.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.ViewThroughConversionsRate.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionRevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionRevenueOnAdSpend.GetValueOrDefault());
                    break;

                case GridColumn.AllConversionRate:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionRate.GetValueOrDefault());
                    break;

                case GridColumn.AllConversionAdvertiserReportedRevenue: // "Revenue"
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionCPA:
                    result = this.PredicateByBIDataColumn(expression,
                        (biData) => biData.AllConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.ConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.PartialConversionCPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.PartialConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.PartialConversionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.PartialConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AllConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.AllPartialConversionCPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AllPartialConversionCPA.GetValueOrDefault());
                    break;
                case GridColumn.AllPartialConversionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AllPartialConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsCredit:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughConversionsCredit.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughPartialConversionsCPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughPartialConversionsCPA.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughPartialConversionsRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughPartialConversionsRate.GetValueOrDefault());
                    break;

                case GridColumn.ImportName:
                    result = PredicateHelper.PredicateByString<HierarchicalAccountId>(
                        expression.FilterOperation,
                        expression.FilterOnValues,
                        (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault(accountId, (lastImportInfo) => lastImportInfo.ImportName),
                        this.culture);
                    break;
                case GridColumn.ImportEntityIdsSpecified:
                    result = PredicateHelper.PredicateByNullableBool<HierarchicalAccountId>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault<bool?>(accountId, (lastImportInfo) => lastImportInfo.EntityIdsSpecified));
                    break;
                case GridColumn.ImportStartTime:
                    result = PredicateHelper.PredicateByNumericTypes<HierarchicalAccountId, DateTime>(
                        expression,
                        (HierarchicalAccountId accountId) => this.GetLastImportInfoMemberOrDefault<DateTime>(accountId, (lastImportInfo) => lastImportInfo.StartDateTime));
                    break;
                case GridColumn.Labels:
                    var filterOnLabels = expression.FilterOnValues.Select(PredicateHelper.ChangeType<Entities.Label>).ToList();
                    if (filterOnLabels.Any(l => l.Scope == Entities.EntityScope.Customer))
                    {
                        logger.LogInfo("Filtering by Customer level label: {0}", expression.FilterOperation);
                    }

                    result = PredicateHelper.PredicateByObjectCollection<HierarchicalAccountId, Entities.Label>(
                        expression.FilterOperation,
                        filterOnLabels,
                        this.GetAssociatedLabels,
                        new Entities.Label.LabelEqualityComparer());
                    break;
                case GridColumn.AdvertiserReportedRevenueAdjustment:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AdvertiserReportedRevenueAdjustment.GetValueOrDefault());
                    break;
                case GridColumn.ViewThroughConversionsRevenueAdjustment:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ViewThroughConversionsRevenueAdjustment.GetValueOrDefault());
                    break;
                case GridColumn.AllConversionAdvertiserReportedRevenueAdjustment: 
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.AllConversionAdvertiserReportedRevenueAdjustment.GetValueOrDefault());
                    break;

                case GridColumn.ConversionDelayZeroDay:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionDelayZeroDay.GetValueOrDefault());
                    break;
                case GridColumn.ConversionDelayNinety:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionDelayNinety.GetValueOrDefault());
                    break;
                case GridColumn.ConversionDelayNinetyNine:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.ConversionDelayNinetyNine.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerConversions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerConversions.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerRevenue:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerRevenue.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerConversionRate:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerConversionRate.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerCPA:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerCPA.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerRevenueOnAdSpend:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerRevenueOnAdSpend.GetValueOrDefault());
                    break;
                case GridColumn.NewCustomerCount:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.NewCustomerCount.GetValueOrDefault());
                    break;
                case GridColumn.UnknownCustomerConversions:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.UnknownCustomerConversions.GetValueOrDefault());
                    break;
                case GridColumn.UnknownCustomerRevenue:
                    result = this.PredicateByBIDataColumn(expression, (biData) => biData.UnknownCustomerRevenue.GetValueOrDefault());
                    break;
                default:
                    string message = $"Unexpected filter column: {expression.ColumnToFilter}";
                    this.logger.LogUserError(message);
                    throw new InvalidOperationException(message);
            }

            return result;
        }

        private IReadOnlyCollection<Entities.Label> GetAssociatedLabels(
            HierarchicalAccountId accountId)
        {
            if (this.cacheItem.AssociatedLabelIdsByAccountIds != null &&
                this.cacheItem.AssociatedLabelIdsByAccountIds.TryGetValue(accountId, out List<long> associatedLabelIds))
            {
                return associatedLabelIds.Select(x =>
                    new Entities.Label
                    {
                        Id = x,
                        Scope = Entities.EntityScope.Customer
                    }).ToList();
            }

            return null;
        }

        private MemberType GetAccountSummaryRowMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Func<AccountInfo2, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.Accounts, memberFetcher);

            return result;
        }

        private TMemberType GetMemberFromDictionaryItemOrDefault<TMemberType, TDictionaryType>(
            HierarchicalAccountId key,
            IDictionary<HierarchicalAccountId, TDictionaryType> dictionary,
            Func<TDictionaryType, TMemberType> memberFetcher)
        {
            TMemberType result = default(TMemberType);

            TDictionaryType item;
            if (dictionary != null && dictionary.TryGetValue(key, out item))
            {
                result = memberFetcher(item);
            }

            return result;
        }

        private IEnumerable<AccountInactiveReasonCode> GetAccountInactiveReasonCode(HierarchicalAccountId accountId)
        {
            IList<AccountInactiveReasonCode> inactiveReasonCodes = new List<AccountInactiveReasonCode>();
            IEnumerable<AccountInactiveReason> inactiveReasons = GetAccountSummaryRowMemberOrDefault(
                accountId,
                (accountInfo) => accountInfo.AccountInactiveReasons);

            foreach (var inactiveReason in inactiveReasons.OrEmpty())
            {
                inactiveReasonCodes.Add(inactiveReason.Reason);
            }

            return inactiveReasonCodes;
        }

        private IEnumerable<FluctuationType> GetPerformanceInsightData(HierarchicalAccountId accountId)
        {
            List<FluctuationType> result;

            if (this.cacheItem.PerformanceInsightDataByAccountId == null ||
                !this.cacheItem.PerformanceInsightDataByAccountId.TryGetValue(accountId, out result))
            {
                result = AdInsightExtensions.EmptyPerformanceInsightData;
            }

            return result;
        }

        private T GetIOMemberOrDefault<T>(
            HierarchicalAccountId accountId,
            Func<InsertionOrderDetails, T> memberFetcher)
        {
            T result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.InsertionOrderDetailsById, memberFetcher);

            return result;
        }

        private T GetAdditionalInfoMemberOrDefault<T>(
            HierarchicalAccountId accountId,
            Func<AccountAdditionalInfo, T> memberFetcher)
        {
            T result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.AdditionalInfoById, memberFetcher);

            return result;
        }

        private T GetLastImportInfoMemberOrDefault<T>(
            HierarchicalAccountId accountId,
            Func<LastImportInfo, T> memberFetcher)
        {
            T result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.LastImportInfoById, memberFetcher);
            return result;
        }

        // This logic should be consistent with CCMT's SearchAccount SortAccountsByTaxInformation function.
        private string GetTaxInformationSortValue(HierarchicalAccountId accountId)
        {
            List<KeyValuePair<string, string>> taxInformation = GetMemberFromDictionaryItemOrDefault(
                accountId, 
                this.cacheItem.AdditionalInfoById, 
                (a) => a.TaxInformation);

            if (taxInformation.IsNullOrEmpty())
            {
                return string.Empty;
            }
            else
            {
                foreach (KeyValuePair<string, string> kvp in taxInformation)
                {
                    if (TaxInformationKeysForSorting.Contains(kvp.Key))
                    {
                        return kvp.Value;
                    }
                }
            }

            return string.Empty;
        }

        private bool GetIsValidPrimaryContact(HierarchicalAccountId accountId)
        {
            return this.cacheItem.IsValidPrimaryContactSet != null && this.cacheItem.IsValidPrimaryContactSet.Contains(accountId);
        }

        private MemberType GetBudgetMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Func<AggregatedBudget, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.AggregatedBudgetById, memberFetcher);

            return result;
        }

        private Func<HierarchicalAccountId, bool> PredicateByBIDataColumn(
            GridFilterExpression gfe,
            Func<BiData, double> biDataItemSelector)
        {
            return PredicateHelper.PredicateByBIDataColumn<HierarchicalAccountId>(
                gfe,
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector));
        }

        private Func<HierarchicalAccountId, bool> PredicateByBIDataColumn(
            GridFilterExpression gfe,
            Func<BiData, long> biDataItemSelector)
        {
            return PredicateHelper.PredicateByBIDataColumn<HierarchicalAccountId>(
                gfe,
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => this.GetBIMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector));
        }

        private MemberType GetBIMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Dictionary<HierarchicalAccountId, BiData> biDataDictionary,
            Func<BiData, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, biDataDictionary, memberFetcher);
            return result;
        }

        private Func<HierarchicalAccountId, bool> PredicateBySoVColumn(
            GridFilterExpression gfe,
            Func<IBiData, double?> biDataItemSelector)
        {
            return PredicateHelper.PredicateBySoVColumn<HierarchicalAccountId>(
                gfe,
                (HierarchicalAccountId accountId) => GetSovMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => GetSovMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector));
        }

        private Func<HierarchicalAccountId, bool> PredicateByAudienceSoVColumn(
            GridFilterExpression gfe,
            Func<IBiData, double?> biDataItemSelector)
        {
            return PredicateHelper.PredicateByAudienceSoVColumn<HierarchicalAccountId>(
                gfe,
                (HierarchicalAccountId accountId) => GetAudienceSovMemberOrDefault(accountId, this.cacheItem.BiResultById, biDataItemSelector),
                (HierarchicalAccountId accountId) => GetAudienceSovMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, biDataItemSelector));
        }

        private MemberType GetSovMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Dictionary<HierarchicalAccountId, BiData> biDataDictionary,
            Func<IBiData, MemberType> memberFetcher)
        {
            MemberType result = default(MemberType);

            if (biDataDictionary != null && biDataDictionary.TryGetValue(accountId, out BiData biData))
            {
                result = BiData.GetSoVMemberOrDefault<MemberType>(biData, memberFetcher);
            }

            return result;
        }

        private MemberType GetAudienceSovMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Dictionary<HierarchicalAccountId, BiData> biDataDictionary,
            Func<IBiData, MemberType> memberFetcher)
        {
            MemberType result = default(MemberType);

            if (biDataDictionary != null && biDataDictionary.TryGetValue(accountId, out BiData biData))
            {
                result = BiData.GetAudienceSoVMemberOrDefault<MemberType>(biData, memberFetcher);
            }

            return result;
        }

        private MemberType GetAccountQualityScoreMemberOrDefault<MemberType>(
            HierarchicalAccountId accountId,
            Func<QualityScoreData, MemberType> memberFetcher)
        {
            MemberType result = GetMemberFromDictionaryItemOrDefault(accountId, this.cacheItem.QualityScoreDataById, memberFetcher);

            return result;
        }

        private Func<HierarchicalAccountId, bool> PredicateByCustomColumn(GridFilterExpression gfe)
        {
            return PredicateHelper.PredicateByBIDataColumn<HierarchicalAccountId>(
                gfe,
                (HierarchicalAccountId accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.cacheItem.BiResultById, gfe.CustomColumnId),
                (HierarchicalAccountId accountId) => this.GetCustomColumnMemberOrDefault(accountId, this.cacheItem.PeriodBiResultById, gfe.CustomColumnId)
            );
        }

        private CustomColumnValue GetCustomColumnMemberOrDefault(HierarchicalAccountId accountId, Dictionary<HierarchicalAccountId, BiData> biDataDictionary, string customColumnId)
        {
            BiData biData = biDataDictionary.GetOrDefault(accountId);

            return BiData.GetCustomColumnValueSafe(biData, this.emptyBiData, customColumnId);
        }
    }
}
