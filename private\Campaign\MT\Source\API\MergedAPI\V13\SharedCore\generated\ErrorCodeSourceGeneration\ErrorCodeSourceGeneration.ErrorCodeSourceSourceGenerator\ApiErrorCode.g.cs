﻿/// Add new errorCodes to the MasterApiErrorCodeList.xml and build the project to generate it in this file
/// For implementation, see private/Campaign/MT/Source/ErrorCodeSourceGeneration/ErrorCodeSourceGeneration.cs
namespace Microsoft.AdCenter.Shared.Api.V13
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// ErrorCodes defines all the api error code as const integers.   
    /// </summary>
    
    public static class ErrorCodes
    {
        /// <summary>
        /// An internal error has occurred.
        /// </summary>
        public const int InternalError = 0;

        /// <summary>
        /// The request message is null.
        /// </summary>
        public const int NullRequest = 100;

        /// <summary>
        /// Authentication failed. Either supplied credentials are invalid or the account is inactive.
        /// </summary>
        public const int LoginFailed = 105;

        /// <summary>
        /// The user does not represent a authorized developer.
        /// </summary>
        public const int UserIsNotAuthorized = 106;

        /// <summary>
        /// There is not enough quota available for the authorized developer.
        /// </summary>
        public const int QuotaNotAvailable = 107;

        /// <summary>
        /// The Date object specified in the request is not valid.
        /// </summary>
        public const int InvalidDateObject = 113;

        /// <summary>
        /// Required headers are missing in the request.
        /// </summary>
        public const int RequestMissingHeaders = 116;

        /// <summary>
        /// One or more input elements failed validation.
        /// </summary>
        public const int ApiInputValidationError = 201;

        /// <summary>
        /// The operation cannot be completed by backend.
        /// </summary>
        public const int MTBusinessOperationError = 202;

        /// <summary>
        /// The parameter or field must not be null.
        /// </summary>
        public const int NullParameter = 203;

        /// <summary>
        /// This operation is not supported.
        /// </summary>
        public const int OperationNotSupported = 204;

        /// <summary>
        /// This is not a valid version.
        /// </summary>
        public const int InvalidVersion = 205;

        /// <summary>
        /// Null array argument is not allowed.
        /// </summary>
        public const int NullArrayArgument = 206;

        /// <summary>
        /// The maximum number of allowed concurrent requests has been exceeded.
        /// </summary>
        public const int ConcurrentRequestOverLimit = 207;

        /// <summary>
        /// The account is invalid.
        /// </summary>
        public const int InvalidAccount = 208;

        /// <summary>
        /// The timestamp does not match.
        /// </summary>
        public const int TimestampNotMatch = 209;

        /// <summary>
        /// The entity does not exist.
        /// </summary>
        public const int EntityNotExistent = 210;

        /// <summary>
        /// The specified name is too long.
        /// </summary>
        public const int NameTooLong = 211;

        /// <summary>
        /// The specified entity type is not supported for this operation.
        /// </summary>
        public const int EntityTypeNotSupported = 212;

        /// <summary>
        /// The input contains invalid parameters.
        /// </summary>
        public const int InvalidParameters = 213;

        /// <summary>
        /// This version of the Bing Ads API is no longer supported. Please migrate to the latest version of the API. For more details, visit https://go.microsoft.com/fwlink/?linkid=862106.
        /// </summary>
        public const int ApiVersionNoLongerSupported = 303;

        /// <summary>
        /// The associated filter list is over size limit.
        /// </summary>
        public const int FilterListOverLimit = 512;

        /// <summary>
        /// The list size is over limit.
        /// </summary>
        public const int EntityIdsArrayExceedsLimit = 513;

        /// <summary>
        /// Ids passed are null or empty.
        /// </summary>
        public const int IdsNotPassed = 514;

        /// <summary>
        /// Duplicate IDs are contained in the request.
        /// </summary>
        public const int DuplicateId = 515;

        /// <summary>
        /// The type of the entity that was specified by ID does not match provided entity filter type.
        /// </summary>
        public const int EntityIdFilterMismatch = 516;

        /// <summary>
        /// The entity identifier is invalid.
        /// </summary>
        public const int EntityIdInvalid = 517;

        /// <summary>
        /// Search string is not sufficient.
        /// </summary>
        public const int SearchStringNotSufficient = 518;

        /// <summary>
        /// Status cannot be changed on Update; instead, use Pause or Resume APIs.
        /// </summary>
        public const int CannotChangeStatusOnUpdate = 1001;

        /// <summary>
        /// Status should be null on Add operations.
        /// </summary>
        public const int CannotSpecifyStatusOnAdd = 1003;

        /// <summary>
        /// ID should be null on Add operations.
        /// </summary>
        public const int IdShouldBeNullOnAdd = 1004;

        /// <summary>
        /// The negative keyword is invalid.
        /// </summary>
        public const int InvalidNegativeKeyword = 1005;

        /// <summary>
        /// The total length of the negative keywords has been exceeded.
        /// </summary>
        public const int NegativeKeywordsTotalLengthExceeded = 1006;

        /// <summary>
        /// The negative keyword matches a keyword.
        /// </summary>
        public const int NegativeKeywordMatchesKeyword = 1007;

        /// <summary>
        /// The account status is invalid for the current operation.
        /// </summary>
        public const int InvalidAccountStatus = 1008;

        /// <summary>
        /// For aggregator users account id of end customer should be supplied in request header.
        /// </summary>
        public const int AccountIdMissingInRequestHeader = 1009;

        /// <summary>
        /// The system is currently in read-only mode (only Get operations are supported).
        /// </summary>
        public const int SystemInReadOnlyMode = 1010;

        /// <summary>
        /// An internal error has occurred.
        /// </summary>
        public const int FutureFeatureCode = 1011;

        /// <summary>
        /// The negative site url is invalid.
        /// </summary>
        public const int InvalidNegativeSiteURL = 1012;

        /// <summary>
        /// Negative site urls exceeded maximum allowed limit.
        /// </summary>
        public const int NegativeSiteURLExceededMaxCount = 1013;

        /// <summary>
        /// Passed TimeZone value is invalid. Please refer to documentation for list of valid values.
        /// </summary>
        public const int TimeZoneValueInvalid = 1014;

        /// <summary>
        /// Passed Currency value is invalid. Please refer to documentation for list of valid values.
        /// </summary>
        public const int CurrencyValueInvalid = 1015;

        /// <summary>
        /// Passed entity state is invalid. Please refer to documentation for list of valid values for given entity.
        /// </summary>
        public const int InvalidEntityState = 1016;

        /// <summary>
        /// Negative keywords exceeded maximum allowed limit.
        /// </summary>
        public const int NegativeKeywordsLimitExceeded = 1032;

        /// <summary>
        /// Number of entities with maximum negative keywords has exceeded limit.
        /// </summary>
        public const int NegativeKeywordsEntityLimitExceeded = 1033;

        /// <summary>
        /// Negative keywords passed is null.
        /// </summary>
        public const int NegativeKeywordsNotPassed = 1034;

        /// <summary>
        /// Negative site specified cannot be an owned or operated site.
        /// </summary>
        public const int NegativeSiteCannotBeOwnedOrOperatedSite = 1035;

        /// <summary>
        /// Negative site specified exceeds maximum number of subdirectories.
        /// </summary>
        public const int NegativeSiteURLExceedMaxSubDirectories = 1036;

        /// <summary>
        /// Negative site specified exceeds maximum number of subdomains.
        /// </summary>
        public const int NegativeSiteURLExceedMaxSubDomains = 1037;

        /// <summary>
        /// The customer status is invalid for the current operation.
        /// </summary>
        public const int InvalidCustomerStatus = 1038;

        /// <summary>
        /// Negative keyword has invalid match type format.
        /// </summary>
        public const int NegativeKeywordHasInvalidMatchTypeFormat = 1039;

        /// <summary>
        /// The size of the response exceeds the maximum allowed. Please decrease the size of your request and try again.
        /// </summary>
        public const int NegativeSiteEntityLimitExceeded = 1040;

        /// <summary>
        /// The Device Preference property is not supported.
        /// </summary>
        public const int DevicePreferenceNotSupported = 1041;

        /// <summary>
        /// The specified entity did not pass editorial validation. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int EditorialValidationError = 1042;

        /// <summary>
        /// The specified entity already exists. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int EntityAlreadyExists = 1043;

        /// <summary>
        /// The specified entity doesnot exist. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int EntityDoesNotExist = 1044;

        /// <summary>
        /// The system limit for the submitted entity type has already been reached. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int MaxLimitReached = 1045;

        /// <summary>
        /// The entity ID field cannot be null or empty.. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int EntityIdNotPassed = 1046;

        /// <summary>
        /// The CPC bids associated with the entity are not valid.
        /// </summary>
        public const int InvalidCpcBids = 1047;

        /// <summary>
        /// The customer is not in pilot for this language.
        /// </summary>
        public const int CustomerNotInLanguagePilot = 526;

        /// <summary>
        /// You cannot share with the customer or account.
        /// </summary>
        public const int InvalidCustomerAccountShare = 1048;

        /// <summary>
        /// You don't have permission for this operation.
        /// </summary>
        public const int CustomerAccountSharePermissionDenied = 1049;

        /// <summary>
        /// Invalid Medium for Cashback.
        /// </summary>
        public const int CashbackAllowedOnlyForSearchMedium = 1019;

        /// <summary>
        /// Invalid Distribution channel for Cashback.
        /// </summary>
        public const int CashbackNotAllowedForAdgroupsDistributionChannel = 1020;

        /// <summary>
        /// Account not eligble for Keyword level Cashback.
        /// </summary>
        public const int AccountNotEligbleForKeywordLevelCashback = 1021;

        /// <summary>
        /// Parent entity needs to enable Cashback before enabling at the child level.
        /// </summary>
        public const int CampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup = 1022;

        /// <summary>
        /// Account not eligible for CashBack.
        /// </summary>
        public const int AccountNotEligibleToModifyCashBack = 1023;

        /// <summary>
        /// Account not eligible for CashBack Amount.
        /// </summary>
        public const int AccountNotEligibleToSetCashbackAmount = 1024;

        /// <summary>
        /// Invalid Cashback amount.
        /// </summary>
        public const int InvalidCashbackAmount = 1025;

        /// <summary>
        /// Invalid Cashback text.
        /// </summary>
        public const int CashbackTextTooLong = 1026;

        /// <summary>
        /// Invalid Cashback status.
        /// </summary>
        public const int CashBackStatusRequired = 1027;

        /// <summary>
        /// Cashback information should be null for backwards compatibility.
        /// </summary>
        public const int CashbackInfoShouldBeNullForBackwardCompatability = 1028;

        /// <summary>
        /// Customer Id has to be specified for this operation.
        /// </summary>
        public const int CustomerIdHasToBeSpecified = 1029;

        /// <summary>
        /// Account Id has to be specified for this operation.
        /// </summary>
        public const int AccountIdHasToBeSpecified = 1030;

        /// <summary>
        /// The current operation cannot be completed due to some invalid customer or user information provided.
        /// </summary>
        public const int CannotPerformCurrentOperation = 1031;

        /// <summary>
        /// The campaign ID is invalid.
        /// </summary>
        public const int InvalidCampaignId = 1100;

        /// <summary>
        /// The campaign name is invalid.
        /// </summary>
        public const int InvalidCampaignName = 1101;

        /// <summary>
        /// The account ID is invalid.
        /// </summary>
        public const int InvalidAccountId = 1102;

        /// <summary>
        /// Campaign is null.
        /// </summary>
        public const int NullCampaign = 1103;

        /// <summary>
        /// The campaign monthly budget is invalid.
        /// </summary>
        public const int InvalidMonthlyBudget = 1105;

        /// <summary>
        /// The campaign daily budget is invalid.
        /// </summary>
        public const int InvalidDailyBudget = 1106;

        /// <summary>
        /// Duplicate IDs are contained in the array of campaigns.
        /// </summary>
        public const int DuplicateInCampaignIds = 1107;

        /// <summary>
        /// TimeZoneType Enabled should be set for the campaign.
        /// </summary>
        public const int InvalidTimeZone = 1109;

        /// <summary>
        /// Daylight saving time should be set for campaign.
        /// </summary>
        public const int InvalidDaylightSaving = 1110;

        /// <summary>
        /// Conversion Tracking Script should not be set for the campaign.
        /// </summary>
        public const int ConversionTrackingScriptNonNull = 1111;

        /// <summary>
        /// Campaigns array should not be null or empty.
        /// </summary>
        public const int CampaignsArrayShouldNotBeNullOrEmpty = 1113;

        /// <summary>
        /// The list of campaigns exceeds the limit.
        /// </summary>
        public const int CampaignsArrayExceedsLimit = 1114;

        /// <summary>
        /// Trying to create a duplicate campaign.
        /// </summary>
        public const int DuplicateCampaign = 1115;

        /// <summary>
        /// Cannot add any more campaigns to this account.
        /// </summary>
        public const int MaxCampaignsReached = 1117;

        /// <summary>
        /// Campaign Ids array should not be null or empty.
        /// </summary>
        public const int CampaignIdsArrayShouldNotBeNullOrEmpty = 1118;

        /// <summary>
        /// The list of campaign Ids exceeds the limit.
        /// </summary>
        public const int CampaignIdsArrayExceedsLimit = 1119;

        /// <summary>
        /// The campaign status is invalid for the current operation.
        /// </summary>
        public const int InvalidCampaignStatus = 1120;

        /// <summary>
        /// The campaign budget type is invalid or not specified
        /// </summary>
        public const int InvalidBudgetType = 1121;

        /// <summary>
        /// The campaign is not eligible for cashback
        /// </summary>
        public const int CampaignNotEligibleForCashBack = 1122;

        /// <summary>
        /// The monthly campaign budget is less than the amount already spent for the current month.
        /// </summary>
        public const int CampaignBudgetAmountIsLessThanSpendAmount = 1123;

        /// <summary>
        /// The campaign already exists.
        /// </summary>
        public const int CampaignAlreadyExists = 1129;

        /// <summary>
        /// CampaignNegativeKeywords is null.
        /// </summary>
        public const int NullCampaignNegativeKeywords = 1130;

        /// <summary>
        /// Campaign Timezone/Start Date are not updatable as not all AdGroups are in draft state.
        /// </summary>
        public const int CannotChangeTimezoneOrStartDateWithActiveAdGroups = 1131;

        /// <summary>
        /// CampaignSiteExclusions is null.
        /// </summary>
        public const int NullCampaignNegativeSites = 1132;

        /// <summary>
        /// AdExtensions property is null.
        /// </summary>
        public const int AdExtensionsArrayShouldNotBeNullOrEmpty = 1133;

        /// <summary>
        /// One of AdExtensions is null.
        /// </summary>
        public const int AdExtensionIsNull = 1134;

        /// <summary>
        /// AdExtensions array exceeds the limit.
        /// </summary>
        public const int AdExtensionsEntityLimitExceeded = 1135;

        /// <summary>
        /// PhoneExtension data should be empty (null) when disabling phone extension.
        /// </summary>
        public const int PhoneExtensionDataNotNull = 1136;

        /// <summary>
        /// Customer account is not enabled to set PhoneExtension.EnableClickToCallOnly.
        /// </summary>
        public const int CustomerNotEligibleToSetClickToCallOnly = 1137;

        /// <summary>
        /// PhoneExtension.Country should not be null.
        /// </summary>
        public const int PhoneExtensionInvalidCountry = 1138;

        /// <summary>
        /// PhoneExtension.Phone is invalid.
        /// </summary>
        public const int PhoneExtensionPhoneNumberHasInvalidChars = 1139;

        /// <summary>
        /// PhoneExtension.Phone should not be null.
        /// </summary>
        public const int PhoneExtensionPhoneNumberMissing = 1140;

        /// <summary>
        /// PhoneExtension.Phone length exceeds the limit.
        /// </summary>
        public const int PhoneExtensionPhoneNumberTooLong = 1141;

        /// <summary>
        /// Customer account is not enabled to set AdExtension.
        /// </summary>
        public const int CustomerNotEligibleToSetAdExtension = 1142;

        /// <summary>
        /// Phone number provided in the PhonExtension is invalid.
        /// </summary>
        public const int PhoneExtensionPhoneNumberInvalid = 1143;

        /// <summary>
        /// Business locations are read-only and cannot be added, updated, or deleted.
        /// </summary>
        public const int BusinessLocationReadOnlyForLocationAdExtensionV2Pilot = 1144;

        /// <summary>
        /// Location extensions v1 are read-only and cannot be enabled or disabled.
        /// </summary>
        public const int LocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot = 1145;

        /// <summary>
        /// Phone extensions v1 are read-only and cannot be enabled, disabled or updated.
        /// </summary>
        public const int PhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot = 1146;

        /// <summary>
        /// The value specified for Keyword Variant Match Type is Invalid.
        /// </summary>
        public const int CampaignServiceInvalidKeywordVariantMatchEnabledValue = 1147;

        /// <summary>
        /// You may not specify duplicate settings for an entity.
        /// </summary>
        public const int DuplicateSettingsInEntity = 1149;

        /// <summary>
        /// The required campaign settings were not specified.
        /// </summary>
        public const int CampaignSettingsRequired = 1150;

        /// <summary>
        /// One or more settings are not supported for the campaign type.
        /// </summary>
        public const int InvalidSettingForCampaignType = 1151;

        /// <summary>
        /// The priority of the shopping campaign is invalid.
        /// </summary>
        public const int ShoppingCampaignPriorityInvalid = 1152;

        /// <summary>
        /// The sales country/region code of the shopping campaign is invalid.
        /// </summary>
        public const int ShoppingCampaignSalesCountryCodeInvalid = 1153;

        /// <summary>
        /// The store ID of the shopping campaign is invalid.
        /// </summary>
        public const int ShoppingCampaignStoreIdInvalid = 1154;

        /// <summary>
        /// The campaign type field of a campaign cannot be updated.
        /// </summary>
        public const int CampaignTypeImmutable = 1155;

        /// <summary>
        /// The sales country/region code of a shopping campaign cannot be updated.
        /// </summary>
        public const int SalesCountryCodeImmutable = 1156;

        /// <summary>
        /// The store ID of a shopping campaign cannot be updated.
        /// </summary>
        public const int StoreIdImmutable = 1157;

        /// <summary>
        /// You can specify a maximum of one campaign type for a campaign.
        /// </summary>
        public const int CampaignTypeLimitExceeded = 1158;

        /// <summary>
        /// You cannot update shared budget through Campaign Entity, please use Budget entity to update shared budget.
        /// </summary>
        public const int CannotUpdateSharedBudget = 1159;

        /// <summary>
        /// Monthly budget is deprecated and cannot be retrieved through this API version.
        /// </summary>
        public const int MonthlyBudgetNotSupported = 1160;

        /// <summary>
        /// The subtype of the shopping campaign is invalid.
        /// </summary>
        public const int ShoppingCampaignSubTypeInvalid = 1161;

        /// <summary>
        /// Users need to ask for Co-Op campaigns explicitly.
        /// </summary>
        public const int CoOpCampaignShouldNotBeReturned = 1162;

        /// <summary>
        /// The campaign target setting is invalid.
        /// </summary>
        public const int CampaignInvalidTargetSetting = 1163;

        /// <summary>
        /// Customer is not enabled for Sponsored Product Ads V2 pilot.
        /// </summary>
        public const int CustomerNotEnabledForSponsoredProductAdsV2 = 1164;

        /// <summary>
        /// Customer is not enabled for Smart Shopping Campaign V2 pilot.
        /// </summary>
        public const int CustomerNotEnabledForSmartShoppignCampaign = 1165;

        /// <summary>
        /// Customer do not setup UET tag for Max Conversion Value Bidding Scheme.
        /// </summary>
        public const int CustomerHasNoUETForMaxConversionValueBiddingScheme = 1166;

        /// <summary>
        /// Customer do not setup Conversion goal with revenue for Max Conversion Value Bidding Scheme.
        /// </summary>
        public const int AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme = 1167;

        /// <summary>
        /// At most support 100 smart shopping campaigns under singel account.
        /// </summary>
        public const int SmartShoppingCampaignLimitExceeded = 1168;

        /// <summary>
        /// Bid Adjustments are not supported for this criterion type in a Smart Shopping campaign.
        /// </summary>
        public const int ShoppingSmartAdsBidAdjustmentNotSupported = 1169;

        /// <summary>
        /// This entity type is not supported for Smart Shopping campaigns.
        /// </summary>
        public const int ShoppingSmartAdsEntityNotSupported = 1170;

        /// <summary>
        /// Update campaign priority to highest(3) failed. If it is for smart shopping campaign google Import, you can delete this campaign in bing ads first, then  import this campaign as new one.
        /// </summary>
        public const int SmartShoppingUpdateInvalidDeleteItIfWantImportSuccess = 1171;

        /// <summary>
        /// Store not specified in Import Options. Suggested store selected for imported shopping campaigns.
        /// </summary>
        public const int ShoppingCampaignStoreIdInferredFromMapping = 1172;

        /// <summary>
        /// This campaign has a draft store. Complete store setup to run this campaign.
        /// </summary>
        public const int CampaignHasDraftStore = 1173;

        /// <summary>
        /// Customer not in Pilot to use Keyword Variant Match Type Feature
        /// </summary>
        public const int CampaignServiceKeywordVariantMatchNotEnabledForPilot = 3510;

        /// <summary>
        /// The campaign subtype is not allowed in current campaign type.
        /// </summary>
        public const int CampaignSubtypeNotAllowedInCampaignType = 3511;

        /// <summary>
        /// The account is not enabled to create video campaign.
        /// </summary>
        public const int AccountNotEnabledForVideoCampaign = 3512;

        /// <summary>
        /// The account is not in the pilot to customize thumbnail in video assets.
        /// </summary>
        public const int AccountNotEnabledForCustomVideoAssetThumbnail = 3513;

        /// <summary>
        /// The customer is not in the pilot to enable multi channel campaign.
        /// </summary>
        public const int CustomerNotEnabledForMultiChannelCampaign = 67312;

        /// <summary>
        /// The creation of new Smart Shopping Campaigns is currently not supported.
        /// </summary>
        public const int SmartShoppingCampaignCreationNotSupported = 1174;

        /// <summary>
        /// The creation of Legacy Winstore Ads Campaigns is deprecated.
        /// </summary>
        public const int LegacyWinstoreAdsCampaignCreationNotSupported = 1175;

        /// <summary>
        /// Placement targeting not supported without pilot
        /// </summary>
        public const int PlacementTargetingNotSupported = 1176;

        /// <summary>
        /// AdGroup is null.
        /// </summary>
        public const int NullAdGroup = 1200;

        /// <summary>
        /// The AdGroup ID is invalid.
        /// </summary>
        public const int InvalidAdGroupId = 1201;

        /// <summary>
        /// The AdGroup name is invalid.
        /// </summary>
        public const int InvalidAdGroupName = 1202;

        /// <summary>
        /// Duplicate IDs are contained in the array of ad groups.
        /// </summary>
        public const int DuplicateInAdGroupIds = 1203;

        /// <summary>
        /// The AdGroup End date should be after the Start date.
        /// </summary>
        public const int AdGroupEndDateShouldBeAfterStartDate = 1204;

        /// <summary>
        /// The AdGroup LanguageAndRegion cannot be updated.
        /// </summary>
        public const int CannotUpdateLanguageAndRegion = 1205;

        /// <summary>
        /// The AdGroup's budget is more than the campaign's budget.
        /// </summary>
        public const int CampaignBudgetLessThanAdGroupBudget = 1208;

        /// <summary>
        /// AdGroups array should not be null or empty.
        /// </summary>
        public const int AdGroupsArrayShouldNotBeNullOrEmpty = 1209;

        /// <summary>
        /// The list of adGroups exceeds the limit.
        /// </summary>
        public const int AdGroupsArrayExceedsLimit = 1210;

        /// <summary>
        /// The AdGroup user cannot use Content Medium.
        /// </summary>
        public const int AdGroupUserNotAllowedContentMedium = 1211;

        /// <summary>
        /// The AdGroup Start Date should be after current date.
        /// </summary>
        public const int AdGroupStartDateLessThanCurrentDate = 1212;

        /// <summary>
        /// Cannot add any more adgroups to this campaign.
        /// </summary>
        public const int MaxAdGroupsReached = 1213;

        /// <summary>
        /// Trying to create a duplicate adgroup.
        /// </summary>
        public const int DuplicateAdGroup = 1214;

        /// <summary>
        /// Cannot update adgroup which is in expired state.
        /// </summary>
        public const int CannotUpdateAdGroupInExpiredState = 1215;

        /// <summary>
        /// Cannot update adgroup which is in submitted state.
        /// </summary>
        public const int CannotUpdateAdGroupInSubmittedState = 1216;

        /// <summary>
        /// Cannot operate on adgroup in current state.
        /// </summary>
        public const int CannotOperateOnAdGroupInCurrentState = 1217;

        /// <summary>
        /// AdGroup Ids array should not be null or empty.
        /// </summary>
        public const int AdGroupIdsArrayShouldNotBeNullOrEmpty = 1218;

        /// <summary>
        /// The list of adGroup Ids exceeds the limit.
        /// </summary>
        public const int AdGroupIdsArrayExceedsLimit = 1219;

        /// <summary>
        /// The LanguageAndRegion property, representing the distribution channel, is missing.
        /// </summary>
        public const int MissingDistributionChannel = 1220;

        /// <summary>
        /// The AdGroup.LanguageAndRegion value is invalid.
        /// </summary>
        public const int AdGroupInvalidDistributionChannel = 1221;

        /// <summary>
        /// The AdGroup.Medium value is invalid.
        /// </summary>
        public const int AdGroupInvalidMedium = 1222;

        /// <summary>
        /// The Content AdDistribution is not enabled for your market.
        /// </summary>
        public const int AdGroupMediumNotAllowedForDistributionChannel = 1223;

        /// <summary>
        /// The medium is required value while creating a new ad group.
        /// </summary>
        public const int AdGroupMissingAdMedium = 1224;

        /// <summary>
        /// The user is not authorized to use the specified LanguageAndRegion for the ad group.
        /// </summary>
        public const int UserNotAuthorizedForDistributionChannel = 1225;

        /// <summary>
        /// An ad group can be submitted only if there is an ad and keyword associated with it.
        /// </summary>
        public const int NeedAtleastOneAdAndOneKeywordToSubmit = 1226;

        /// <summary>
        /// An ad group's start date cannot be earlier than the submitted date.
        /// </summary>
        public const int AdGroupStartDateCannotBeEarlierThanSubmitDate = 1227;

        /// <summary>
        /// Pricing Model is not currently supported on the AdGroup.
        /// </summary>
        public const int CannotSetPricingModelOnAdGroup = 1228;

        /// <summary>
        /// Operation not valid for an expired ad group
        /// </summary>
        public const int AdGroupExpired = 1231;

        /// <summary>
        /// The start date specified in the ad group is not valid.
        /// </summary>
        public const int AdGroupInvalidStartDate = 1232;

        /// <summary>
        /// The end date specified in the ad group is not valid.
        /// </summary>
        public const int AdGroupInvalidEndDate = 1233;

        /// <summary>
        /// Pricing Model CPM is allowed only if the medium is Content for the Ad Group.
        /// </summary>
        public const int AdGroupPricingModelCpmRequiresContentMedium = 1234;

        /// <summary>
        /// Customer account is not enabled to create adgroups in this medium.
        /// </summary>
        public const int AdGroupInvalidMediumForCustomer = 1235;

        /// <summary>
        /// Customer account is not enabled to create adgroups with Pricing Model CPM.
        /// </summary>
        public const int AdGroupPricingModelCpmIsNotEnabledForCustomer = 1236;

        /// <summary>
        /// PricingModel field is empty. Please set appropriate value when creating an AdGroup entity.
        /// </summary>
        public const int AdGroupPricingModelIsNull = 1237;

        /// <summary>
        /// Type can be set to BehavioralBid for only content adgroups.
        /// </summary>
        public const int TypeCanBeBehavioralBidOnlyForContentAdGroups = 1239;

        /// <summary>
        /// Cannot update bidding model of an adgroup.
        /// </summary>
        public const int CannotUpdateBiddingModel = 1241;

        /// <summary>
        /// Cannot update the AdDistribution for this type of adgroup.
        /// </summary>
        public const int CannotUpdateAdDistributionForThisType = 1242;

        /// <summary>
        /// Adgroups limit per account is exceeded.
        /// </summary>
        public const int TooManyAdGroupsInAccount = 1243;

        /// <summary>
        /// AdGroupNegativeKeywords is null.
        /// </summary>
        public const int NullAdGroupNegativeKeywords = 1244;

        /// <summary>
        /// AdGroupNegative site Urls is null.
        /// </summary>
        public const int NegativeSiteUrlsNotPassed = 1245;

        /// <summary>
        /// AdGroupNetworks array should not be null or empty.
        /// </summary>
        public const int AdGroupNetworksArrayShouldNotBeNullOrEmpty = 1246;

        /// <summary>
        /// The list of AdGroupNetworks exceeds the limit.
        /// </summary>
        public const int AdGroupNetworksArrayExceedsLimit = 1247;

        /// <summary>
        /// Network should be null for content adgroups.
        /// </summary>
        public const int NetworkShouldBeNullForContentAdGroup = 1248;

        /// <summary>
        /// Network is set to null.
        /// </summary>
        public const int NullNetwork = 1249;

        /// <summary>
        /// AdGroup Negative Sites cannot be null or empty.
        /// </summary>
        public const int NegativeSitesArrayShouldNotBeNullOrEmpty = 1250;

        /// <summary>
        /// AdGroup Negative Sites array size exceeded limit.
        /// </summary>
        public const int NegativeSitesEntityLimitExceeded = 1251;

        /// <summary>
        /// AdGroupNegativeSites is null.
        /// </summary>
        public const int NullAdGroupNegativeSites = 1252;

        /// <summary>
        /// The network provided is not allowed for specified publisher countries or ad group publisher language.
        /// </summary>
        public const int AdGroupNetworkValueNotAllowedForPublisherCountries = 1253;

        /// <summary>
        /// The publisher countries are not provided.
        /// </summary>
        public const int MissingPublisherCountries = 1254;

        /// <summary>
        /// The medium provided is not allowed for specified publisher countries.
        /// </summary>
        public const int AdGroupMediumNotAllowedForPublisherCountries = 1256;

        /// <summary>
        /// The language is not provided.
        /// </summary>
        public const int MissingLanguage = 1257;

        /// <summary>
        /// Only one publisher country/region can be specified.
        /// </summary>
        public const int MultiplePublisherCountriesNotAllowed = 1258;

        /// <summary>
        /// The publisher countries cannot be updated.
        /// </summary>
        public const int PublisherCountriesUpdateNotAllowed = 1259;

        /// <summary>
        /// The language cannot be updated.
        /// </summary>
        public const int LanguageUpdateNotAllowed = 1260;

        /// <summary>
        /// AdGroupAdRotation array should not be null or empty.
        /// </summary>
        public const int AdGroupAdRotationArrayShouldNotBeNullOrEmpty = 1261;

        /// <summary>
        /// AdGroupAdRotations array exceeds the limit.
        /// </summary>
        public const int AdGroupAdRotationsArrayExceedsLimit = 1262;

        /// <summary>
        /// Cannot set the Start or End Date of Ad Rotation feature for the ad group.
        /// </summary>
        public const int CannotSetStartOrEndDateForAdRotation = 1263;

        /// <summary>
        /// The customer is not a member of the Ad Rotation pilot program.
        /// </summary>
        public const int AdRotationPilotNotEnabledForCustomer = 1264;

        /// <summary>
        /// The ad group status is not valid for the requested operation.
        /// </summary>
        public const int InvalidAdGroupStatus = 1266;

        /// <summary>
        /// The bidding model value of the ad group is invalid.
        /// </summary>
        public const int InvalidBiddingModel = 1267;

        /// <summary>
        /// The remarketing targeting setting is invalid.
        /// </summary>
        public const int InvalidRemarketingTargetingSetting = 1268;

        /// <summary>
        /// Ad Rotation type is invalid.
        /// </summary>
        public const int InvalidAdRotationType = 1269;

        /// <summary>
        /// One or more settings are not supported for the entity type.
        /// </summary>
        public const int InvalidSettingsInEntity = 1270;

        /// <summary>
        /// The bid option in co-op setting is invalid.
        /// </summary>
        public const int CoOpSettingBidOptionInvalid = 1271;

        /// <summary>
        /// The bid boost value in co-op setting is invalid.
        /// </summary>
        public const int CoOpSettingBidBoostValueInvalid = 1272;

        /// <summary>
        /// The bid max value in co-op setting is invalid.
        /// </summary>
        public const int CoOpSettingBidMaxValueInvalid = 1273;

        /// <summary>
        /// The customer is not a member of the In-House promotion pilot program.
        /// </summary>
        public const int CustomerNotEnableForInHousePromotion = 1274;

        /// <summary>
        /// The network is invalid.
        /// </summary>
        public const int InvalidNetwork = 1275;

        /// <summary>
        /// The bidding scheme of the ad group is not supported.
        /// </summary>
        public const int AdGroupBiddingSchemeNotSupported = 1276;

        /// <summary>
        /// Search Bid is invalid for this enity.
        /// </summary>
        public const int InvalidSearchBids = 1277;

        /// <summary>
        /// Content Bid is invalid for this enity.
        /// </summary>
        public const int InvalidContentBid = 1278;

        /// <summary>
        /// Bid is invalid for this enity.
        /// </summary>
        public const int InvalidBid = 1279;

        /// <summary>
        /// The pricing model is invalid for this enity.
        /// </summary>
        public const int InvalidPricingModel = 1280;

        /// <summary>
        /// The account is not enabled for MMA V2.
        /// </summary>
        public const int AccountNotInPilotForMMAV2 = 1281;

        /// <summary>
        /// The ad group language cannot be removed until campaign language updates have been processed. Please try again in 12 hours.
        /// </summary>
        public const int AdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed = 5813;

        /// <summary>
        /// Ad is null.
        /// </summary>
        public const int NullAd = 1300;

        /// <summary>
        /// The Ad's title is invalid.
        /// </summary>
        public const int InvalidAdTitle = 1301;

        /// <summary>
        /// Invalid URL has been specified.
        /// </summary>
        public const int InvalidAdDestinationUrl = 1302;

        /// <summary>
        /// The Ad's Id field should be set.
        /// </summary>
        public const int AdIdIsNull = 1303;

        /// <summary>
        /// The Ad's Id field should not be set.
        /// </summary>
        public const int AdIdIsNonNull = 1304;

        /// <summary>
        /// The Ad's Type field should not be set.
        /// </summary>
        public const int AdTypeIsNonNull = 1305;

        /// <summary>
        /// The Ad's Text field contains an invalid value.
        /// </summary>
        public const int InvalidAdText = 1306;

        /// <summary>
        /// The Ad's DisplayUrl field contains an invalid value.
        /// </summary>
        public const int InvalidAdDisplayUrl = 1307;

        /// <summary>
        /// The Ad Id is invalid.
        /// </summary>
        public const int InvalidAdId = 1308;

        /// <summary>
        /// Duplicate IDs are contained in the array of AdIds.
        /// </summary>
        public const int DuplicateInAdIds = 1309;

        /// <summary>
        /// Ads array should not be null or empty.
        /// </summary>
        public const int AdsArrayShouldNotBeNullOrEmpty = 1310;

        /// <summary>
        /// The list of ads exceeds the limit.
        /// </summary>
        public const int AdsArrayExceedsLimit = 1311;

        /// <summary>
        /// Cannot add any more ads to this AdGroup.
        /// </summary>
        public const int MaxAdsReached = 1312;

        /// <summary>
        /// Trying to create a duplicate ad.
        /// </summary>
        public const int DuplicateAd = 1313;

        /// <summary>
        /// A default Ad already exists.
        /// </summary>
        public const int DefaultAdExists = 1314;

        /// <summary>
        /// There is a syntax error in the Ad title.
        /// </summary>
        public const int SyntaxErrorInAdTitle = 1315;

        /// <summary>
        /// There is a syntax error in the Ad Text.
        /// </summary>
        public const int SyntaxErrorInAdText = 1316;

        /// <summary>
        /// There is a syntax error in the Ad Display URL.
        /// </summary>
        public const int SyntaxErrorInAdDisplayUrl = 1317;

        /// <summary>
        /// There is some forbidden text in the Ad title.
        /// </summary>
        public const int ForbiddenTextInAdTitle = 1318;

        /// <summary>
        /// There is some forbidden text in the Ad Text.
        /// </summary>
        public const int ForbiddenTextInAdText = 1319;

        /// <summary>
        /// There is some forbidden text in the Ad Display URL.
        /// </summary>
        public const int ForbiddenTextInAdDisplayUrl = 1320;

        /// <summary>
        /// The ad format in the title is incorrect.
        /// </summary>
        public const int IncorrectAdFormatInTitle = 1321;

        /// <summary>
        /// The ad format in the text is incorrect.
        /// </summary>
        public const int IncorrectAdFormatInText = 1322;

        /// <summary>
        /// The ad format in the display URL is incorrect.
        /// </summary>
        public const int IncorrectAdFormatInDisplayUrl = 1323;

        /// <summary>
        /// The Ad title has too much text.
        /// </summary>
        public const int TooMuchAdTextInTitle = 1324;

        /// <summary>
        /// The Ad text is too long.
        /// </summary>
        public const int TooMuchAdTextInText = 1325;

        /// <summary>
        /// The Ad Display URL has too much text.
        /// </summary>
        public const int TooMuchAdTextInDisplayUrl = 1326;

        /// <summary>
        /// The Ad Destination URL has too much text.
        /// </summary>
        public const int TooMuchAdTextInDestinationUrl = 1327;

        /// <summary>
        /// The combination of the ad title and the ad text does not meet the requirement for the minimum number of words.
        /// </summary>
        public const int NotEnoughAdText = 1328;

        /// <summary>
        /// The Ad title contains a reserved word.
        /// </summary>
        public const int ExclusiveWordInAdTitle = 1329;

        /// <summary>
        /// The Ad text contains a reserved word.
        /// </summary>
        public const int ExclusiveWordInAdText = 1330;

        /// <summary>
        /// The Ad display Url contains a reserved word.
        /// </summary>
        public const int ExclusiveWordInAdDisplayUrl = 1331;

        /// <summary>
        /// The Ad display Url format is invalid.
        /// </summary>
        public const int InvalidAdDisplayUrlFormat = 1332;

        /// <summary>
        /// There is a syntax error in the title of the default Ad.
        /// </summary>
        public const int DefaultAdSyntaxErrorInTitle = 1333;

        /// <summary>
        /// There is a syntax error in the text of the default Ad.
        /// </summary>
        public const int DefaultAdSyntaxErrorInText = 1334;

        /// <summary>
        /// There is a syntax error in the display URL of the default Ad.
        /// </summary>
        public const int DefaultAdSyntaxErrorInDisplayUrl = 1335;

        /// <summary>
        /// There is some forbidden text in the title of the default Ad.
        /// </summary>
        public const int DefaultAdForbiddenWordInTitle = 1336;

        /// <summary>
        /// There is some forbidden text in the default Ad.
        /// </summary>
        public const int DefaultAdForbiddenWordInText = 1337;

        /// <summary>
        /// There is some forbidden text in the Display URL of the default Ad.
        /// </summary>
        public const int DefaultAdForbiddenWordInDisplayUrl = 1338;

        /// <summary>
        /// The ad format of the title is incorrect for the default Ad.
        /// </summary>
        public const int DefaultAdIncorrectAdFormatInTitle = 1339;

        /// <summary>
        /// The ad format of the text is incorrect for the default Ad.
        /// </summary>
        public const int DefaultAdIncorrectAdFormatInText = 1340;

        /// <summary>
        /// The ad format of the display URL is incorrect for the default Ad.
        /// </summary>
        public const int DefaultAdIncorrectAdFormatInDisplayUrl = 1341;

        /// <summary>
        /// There is too much text in the title for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInTitle = 1342;

        /// <summary>
        /// There is too much text for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInText = 1343;

        /// <summary>
        /// There is too much text in the display URL for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInDisplayUrl = 1344;

        /// <summary>
        /// There is too much text in the destination URL for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInDestinationUrl = 1345;

        /// <summary>
        /// There is not enough text in the default Ad.
        /// </summary>
        public const int DefaultAdNotEnoughAdText = 1346;

        /// <summary>
        /// A reserved word has been used in the title of the default Ad.
        /// </summary>
        public const int DefaultAdExclusiveWordInTitle = 1347;

        /// <summary>
        /// A reserved word has been used in the text of the default Ad.
        /// </summary>
        public const int DefaultAdExclusiveWordInText = 1348;

        /// <summary>
        /// A reserved word has been used in the display URL of the default Ad.
        /// </summary>
        public const int DefaultAdExclusiveWordInDisplayUrl = 1349;

        /// <summary>
        /// The format for the display URL in the default Ad is invalid.
        /// </summary>
        public const int DefaultAdInvalidDisplayUrlFormat = 1350;

        /// <summary>
        /// Ad Ids array should not be null or empty.
        /// </summary>
        public const int AdIdsArrayShouldNotBeNullOrEmpty = 1351;

        /// <summary>
        /// The list of ad Ids exceeds the limit.
        /// </summary>
        public const int AdIdsArrayExceedsLimit = 1352;

        /// <summary>
        /// There is too much text in the title across all keyword-ad associations.
        /// </summary>
        public const int TooMuchTextInTitleAcrossAllAssociations = 1353;

        /// <summary>
        /// There is too much text across all keyword-ad associations.
        /// </summary>
        public const int TooMuchTextInTextAcrossAllAssociations = 1354;

        /// <summary>
        /// There is too much text in the display URL across all keyword-ad associations.
        /// </summary>
        public const int TooMuchTextInDisplayUrlAcrossAllAssociations = 1355;

        /// <summary>
        /// There is no information to update in the given ad request.
        /// </summary>
        public const int NothingToUpdateInAdRequest = 1356;

        /// <summary>
        /// Cannot operate on ad in current state.
        /// </summary>
        public const int CannotOperateOnAdInCurrentState = 1357;

        /// <summary>
        /// The format for the destination URL in the default Ad is invalid.
        /// </summary>
        public const int DefaultAdInvalidDestinationUrlFormat = 1358;

        /// <summary>
        /// The Ad destination Url format is invalid.
        /// </summary>
        public const int InvalidAdDestinationUrlFormat = 1359;

        /// <summary>
        /// The Ad's Type field should not be set or should match the type of the Ad.
        /// </summary>
        public const int AdTypeDoesNotMatch = 1360;

        /// <summary>
        /// The Ad's business name is not valid.
        /// </summary>
        public const int InvalidBusinessName = 1361;

        /// <summary>
        /// The Ad's phone number is not valid.
        /// </summary>
        public const int InvalidPhoneNumber = 1362;

        /// <summary>
        /// For a mobile ad, either phone number and business name or destination url and display url need to be supplied.
        /// </summary>
        public const int MobileAdRequiredDataMissing = 1363;

        /// <summary>
        /// A mobile ad may be added only to ad groups with AdDistribution = Search
        /// </summary>
        public const int MobileAdSupportedForSearchOnlyAdGroups = 1364;

        /// <summary>
        /// Existing Ad has different type than one passed for the same ID.
        /// </summary>
        public const int AdTypeMismatch = 1365;

        /// <summary>
        /// Customer account is not enabled to create ads of this type
        /// </summary>
        public const int AdTypeInvalidForCustomer = 1366;

        /// <summary>
        /// The business name has too much text
        /// </summary>
        public const int TooMuchAdTextInBusinessName = 1367;

        /// <summary>
        /// The phone number has too much text
        /// </summary>
        public const int TooMuchAdTextInPhoneNumber = 1368;

        /// <summary>
        /// Phone number is not valid for this country/region.
        /// </summary>
        public const int PhoneNumberNotAllowedForCountry = 1370;

        /// <summary>
        /// Phone number is blocked.
        /// </summary>
        public const int BlockedPhoneNumber = 1371;

        /// <summary>
        /// Phone number is not allowed in ad title.
        /// </summary>
        public const int PhoneNumberNotAllowedInAdTitle = 1372;

        /// <summary>
        /// Phone number is not allowed in ad text.
        /// </summary>
        public const int PhoneNumberNotAllowedInAdText = 1373;

        /// <summary>
        /// Phone number is not allowed in ad display url.
        /// </summary>
        public const int PhoneNumberNotAllowedInAdDisplayUrl = 1374;

        /// <summary>
        /// Phone number is not allowed in ad business name.
        /// </summary>
        public const int PhoneNumberNotAllowedInAdBusinessName = 1375;

        /// <summary>
        /// Ad title failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdTitle = 1376;

        /// <summary>
        /// Ad text failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdText = 1377;

        /// <summary>
        /// Ad display url failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdDisplayUrl = 1378;

        /// <summary>
        /// Ad destination url failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdDestinationUrl = 1379;

        /// <summary>
        /// Ad business name failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdBusinessName = 1380;

        /// <summary>
        /// Ad phone number failed editorial validation.
        /// </summary>
        public const int EditorialErrorInAdPhoneNumber = 1381;

        /// <summary>
        /// Setting Status not allowed.
        /// </summary>
        public const int InvalidAdStatus = 1382;

        /// <summary>
        /// Setting Editorial Status is not allowed.
        /// </summary>
        public const int InvalidAdEditorialStatus = 1383;

        /// <summary>
        /// Exemption Request for keyword is not currently supported and hence cannot be set.
        /// </summary>
        public const int CannotSetExemptionRequestOnAd = 1384;

        /// <summary>
        /// Update ad entity has no data
        /// </summary>
        public const int UpdateAdEmpty = 1385;

        /// <summary>
        /// The requested ad type does not match the existing ad type. For example the App Install ad type was specified, but the ad identifier refers to an Expanded Text ad.
        /// </summary>
        public const int AdTypeDoesNotMatchExistingValue = 1386;

        /// <summary>
        /// Ad title is blank for all keywords
        /// </summary>
        public const int EditorialAdTitleBlankAcrossAllAssociations = 1387;

        /// <summary>
        /// Keyword would make title blank for at least one Ad
        /// </summary>
        public const int EditorialAdTitleBlank = 1388;

        /// <summary>
        /// Ad text is blank for all keywords
        /// </summary>
        public const int EditorialAdTextBlankAcrossAllAssociations = 1389;

        /// <summary>
        /// Keyword would make text blank for at least one Ad
        /// </summary>
        public const int EditorialAdTextBlank = 1390;

        /// <summary>
        /// Ad display URL is blank for all keywords
        /// </summary>
        public const int EditorialAdDisplayUrlBlankAcrossAllAssociations = 1391;

        /// <summary>
        /// Keyword would make display URL blank for at least one Ad
        /// </summary>
        public const int EditorialAdDisplayUrlBlank = 1392;

        /// <summary>
        /// Keyword would make destination URL blank for at least one Ad
        /// </summary>
        public const int EditorialAdDestinationUrlBlank = 1393;

        /// <summary>
        /// The ad is already deleted or does not exist.
        /// </summary>
        public const int AdDeleted = 1394;

        /// <summary>
        /// The Ad Status is Invalid for the current operation.
        /// </summary>
        public const int AdInInvalidStatus = 1395;

        /// <summary>
        /// There is too much text in the business name for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInBusniessName = 1396;

        /// <summary>
        /// The given data failed some editorial checks.
        /// </summary>
        public const int EditorialGenericError = 1397;

        /// <summary>
        /// The promotional text of Product Ad is too long.
        /// </summary>
        public const int ProductAdPromotionalTextTooLong = 1398;

        /// <summary>
        /// Campaign type is not enabled to create ads of this type
        /// </summary>
        public const int AdTypeInvalidForCampaign = 1399;

        /// <summary>
        /// Image URL is read-only field and should not be specified.
        /// </summary>
        public const int NonNullImageUrl = 2802;

        /// <summary>
        /// There is too much text in the alternate text for the default Ad.
        /// </summary>
        public const int DefaultAdTooMuchTextInAltText = 2803;

        /// <summary>
        /// The Ad alternate text has too much text.
        /// </summary>
        public const int TooMuchAdTextInAltText = 2804;

        /// <summary>
        /// Creating or updating ads of this type is not allowed.
        /// </summary>
        public const int AdTypeInvalid = 2805;

        /// <summary>
        /// AdGroup with Rich Search Ads cannot have any other type of Ads in the AdGroup.
        /// </summary>
        public const int RichSearchAdsExclusiveInAdGroup = 2806;

        /// <summary>
        /// Changing medium of Adgroup having Rich Search Ad is not allowed.
        /// </summary>
        public const int MediumChangeNotAllowedForAdgroupWithRichSearchAds = 2807;

        /// <summary>
        /// Resume failed because the entity had migration errors.
        /// </summary>
        public const int ResumeFailedDueToMigrationErrors = 2809;

        /// <summary>
        /// Device Preference cannot be set for Ad type of this type.
        /// </summary>
        public const int DevicePreferenceIncompatibleWithAdType = 5000;

        /// <summary>
        /// Device Preference passed in was invalid.
        /// </summary>
        public const int InvalidAdDevicePreference = 5001;

        /// <summary>
        /// The promotional text of Product Ad is not valid.
        /// </summary>
        public const int ProductAdPromotionalTextInvalid = 5002;

        /// <summary>
        /// The app platform cannot be null or empty.
        /// </summary>
        public const int AppInstallAdAppPlatformNullOrEmpty = 5005;

        /// <summary>
        /// The app platform is invalid.
        /// </summary>
        public const int AppInstallAdAppPlatformInvalid = 5006;

        /// <summary>
        /// The app store ID cannot be null or empty.
        /// </summary>
        public const int AppInstallAdAppStoreIdIsNullOrEmpty = 5007;

        /// <summary>
        /// The app store ID is too long.
        /// </summary>
        public const int AppInstallAdAppStoreIdTooMuchText = 5008;

        /// <summary>
        /// The app store ID is invalid.
        /// </summary>
        public const int AppInstallAdAppStoreIdInvalid = 5009;

        /// <summary>
        /// The app platform cannot be modified.
        /// </summary>
        public const int AppInstallAdUpdateAppPlatformChanged = 5010;

        /// <summary>
        /// The app store ID cannot be modified.
        /// </summary>
        public const int AppInstallAdUpdateAppStoreIdChanged = 5011;

        /// <summary>
        /// AdFormatPreference passed in was invalid.
        /// </summary>
        public const int InvalidAdFormatPreference = 5012;

        /// <summary>
        /// Customer is Not Enabled For LocalInventoryAds
        /// </summary>
        public const int CustomerNotEnabledForLocalInventoryAds = 4576;

        /// <summary>
        /// Account is Not Enabled For Shoppable Ads
        /// </summary>
        public const int AccountNotEnabledForShoppableAds = 4577;

        /// <summary>
        /// App is not found for this app campaign
        /// </summary>
        public const int AppNotFound = 4578;

        /// <summary>
        /// Ad title part 1 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdTitlePart1TooLong = 5013;

        /// <summary>
        /// Ad title part 2 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdTitlePart2TooLong = 5014;

        /// <summary>
        /// Ad title part 1 is not valid.
        /// </summary>
        public const int ExpandedTextAdTitlePart1Invalid = 5015;

        /// <summary>
        /// Ad title part 2 is not valid.
        /// </summary>
        public const int ExpandedTextAdTitlePart2Invalid = 5016;

        /// <summary>
        /// Path 1 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdPath1TooLong = 5017;

        /// <summary>
        /// Path 2 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdPath2TooLong = 5018;

        /// <summary>
        /// Path 1 is not valid.
        /// </summary>
        public const int ExpandedTextAdPath1Invalid = 5019;

        /// <summary>
        /// Path 2 is not valid.
        /// </summary>
        public const int ExpandedTextAdPath2Invalid = 5020;

        /// <summary>
        /// Path2 is set without path1.
        /// </summary>
        public const int ExpandedTextAdPath2SetWithoutPath1 = 5021;

        /// <summary>
        /// The Expanded Text Ad pilot is not enabled for customer.
        /// </summary>
        public const int CustomerNotEnabledForExpandedTextAds = 5022;

        /// <summary>
        /// Combined Title 1 and Title 2 exceeds the system limit.
        /// </summary>
        public const int ExpandedTextAdCombinedTitleTooLong = 5023;

        /// <summary>
        /// Final Url domain is too long to be used as Ad Display URL.
        /// </summary>
        public const int ExpandedTextAdFinalUrlDomainTooLong = 5024;

        /// <summary>
        /// Display Url domain is too long.
        /// </summary>
        public const int ExpandedTextAdDisplayUrlDomainTooLong = 5025;

        /// <summary>
        /// Display Url domain is Invalid.
        /// </summary>
        public const int ExpandedTextAdDisplayUrlDomainInvalid = 5026;

        /// <summary>
        /// The syntax of your function contains invalid formatting, likely caused by a missing }.
        /// </summary>
        public const int ExpandedTextAdInvalidFunctionFormat = 5028;

        /// <summary>
        /// One or more functions are invalid or not supported.
        /// </summary>
        public const int ExpandedTextAdUnknownFunction = 5029;

        /// <summary>
        /// You need to have at least one character between any two functions.
        /// </summary>
        public const int ExpandedTextAdMissingDelimiterBetweenFunctions = 5030;

        /// <summary>
        /// Your countdown function contains an invalid date and/or time.
        /// </summary>
        public const int ExpandedTextAdCountdownInvalidDateTime = 5031;

        /// <summary>
        /// Your countdown function contains an invalid language code.
        /// </summary>
        public const int ExpandedTextAdCountdownInvalidLanguageCode = 5032;

        /// <summary>
        /// Your countdown function contains an invalid days-before value.
        /// </summary>
        public const int ExpandedTextAdCountdownInvalidDaysBefore = 5033;

        /// <summary>
        /// The days-before value in your countdown function is out of range.
        /// </summary>
        public const int ExpandedTextAdCountdownDaysBeforeOutOfRange = 5034;

        /// <summary>
        /// A countdown function must have at least one parameter and no more than three.
        /// </summary>
        public const int ExpandedTextAdCountdownInvalidParameters = 5035;

        /// <summary>
        /// Your countdown function contains a date and/or time in the past.
        /// </summary>
        public const int ExpandedTextAdCountdownPastDateTime = 5036;

        /// <summary>
        /// Default value is not allowed in countdown function.
        /// </summary>
        public const int ExpandedTextAdCountdownInvalidDefaultText = 5037;

        /// <summary>
        /// When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}
        /// </summary>
        public const int ExpandedTextDefaultTextRequiredForKeyword = 5038;

        /// <summary>
        /// Cannot add any more Ads to this customer.
        /// </summary>
        public const int MaxAdsReachedForCustomer = 5039;

        /// <summary>
        /// Cannot add any more Ads to this account.
        /// </summary>
        public const int MaxAdsReachedForAccount = 5040;

        /// <summary>
        /// The expanded text ad domain is too long.
        /// </summary>
        public const int ExpandedTextAdDomainTooLong = 5041;

        /// <summary>
        /// The expanded text ad domain is invalid.
        /// </summary>
        public const int ExpandedTextAdDomainInvalid = 5042;

        /// <summary>
        /// The feed name in the ad customizer function is missing.
        /// </summary>
        public const int AdCustomizerFeedNameMissing = 5043;

        /// <summary>
        /// The attribute name in the ad customizer function is missing.
        /// </summary>
        public const int AdCustomizerFeedAttributeMissing = 5044;

        /// <summary>
        /// The default value in the ad customizer function is missing.
        /// </summary>
        public const int AdCustomizerDefaultValueMissing = 5045;

        /// <summary>
        /// Only one feed name can be referenced in the ad.
        /// </summary>
        public const int FeedPerAdLimitExceeded = 5046;

        /// <summary>
        /// The feed name does not exist.
        /// </summary>
        public const int FeedNameDoesNotExist = 5047;

        /// <summary>
        /// The feed is not supported for the ad type.
        /// </summary>
        public const int InvalidFeedForAdType = 5048;

        /// <summary>
        /// The attribute does not exist in the feed.
        /// </summary>
        public const int FeedAttributeDoesNotExist = 5049;

        /// <summary>
        /// The feed attribute is not supported for the ad type.
        /// </summary>
        public const int InvalidFeedAttributeForAdType = 5050;

        /// <summary>
        /// The feed attribute in the COUNTDOWN or GLOBAL_COUNTDOWN function should be of DateTime type only.
        /// </summary>
        public const int InvalidFeedAttributeTypeInCountdown = 5051;

        /// <summary>
        /// The attribute data type can't be changed.
        /// </summary>
        public const int RSAAdCustomizerAttributeTypeChangedInUpdate = 65302;

        /// <summary>
        /// The AdCustomizer Attribute Count exceeds the system limit.
        /// </summary>
        public const int RSAAdCustomizerAttributeCountMoreThanLimit = 65303;

        /// <summary>
        /// attribute data type is invalid.
        /// </summary>
        public const int RSAAdCustomizerInvalidAttributeType = 65304;

        /// <summary>
        /// The attribute name does not exist in the account.
        /// </summary>
        public const int AttributeNameDoesNotExist = 65305;

        /// <summary>
        /// Failed to load existing attributes.
        /// </summary>
        public const int FetchAttributesFailed = 65306;

        /// <summary>
        /// attribute data type is invalid.
        /// </summary>
        public const int AttributeNameLengthExceeded = 65307;

        /// <summary>
        /// The attributeId is invalid.
        /// </summary>
        public const int InvalidAdcustomizerAttributeId = 65308;

        /// <summary>
        /// Attribute must have a name.
        /// </summary>
        public const int AttributeNameMissing = 65309;

        /// <summary>
        /// There exists ad associated with this attribute.
        /// </summary>
        public const int AttributeReferencedInAd = 65310;

        /// <summary>
        /// Ad title part 3 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdTitlePart3TooLong = 5052;

        /// <summary>
        /// Ad title part 3 is not valid.
        /// </summary>
        public const int ExpandedTextAdTitlePart3Invalid = 5053;

        /// <summary>
        /// Ad text part 1 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdTextPart1TooLong = 5054;

        /// <summary>
        /// Ad text part 1 is not valid.
        /// </summary>
        public const int ExpandedTextAdTextPart1Invalid = 5055;

        /// <summary>
        /// Ad text part 2 is over the character limit.
        /// </summary>
        public const int ExpandedTextAdTextPart2TooLong = 5056;

        /// <summary>
        /// Ad text part 2 is not valid.
        /// </summary>
        public const int ExpandedTextAdTextPart2Invalid = 5057;

        /// <summary>
        /// Target is null.
        /// </summary>
        public const int NullTarget = 1400;

        /// <summary>
        /// The Target ID is invalid.
        /// </summary>
        public const int InvalidTargetId = 1401;

        /// <summary>
        /// There are no bids in target.
        /// </summary>
        public const int NoBidsInTarget = 1402;

        /// <summary>
        /// The day target specified is invalid.
        /// </summary>
        public const int InvalidDayTarget = 1403;

        /// <summary>
        /// The hour target specified is invalid.
        /// </summary>
        public const int InvalidHourTarget = 1404;

        /// <summary>
        /// The location target specified is invalid.
        /// </summary>
        public const int InvalidLocationTarget = 1405;

        /// <summary>
        /// The gender target specified is invalid.
        /// </summary>
        public const int InvalidGenderTarget = 1406;

        /// <summary>
        /// The age target specified is invalid.
        /// </summary>
        public const int InvalidAgeTarget = 1407;

        /// <summary>
        /// The day target specified is a duplicate.
        /// </summary>
        public const int DuplicateDayTarget = 1408;

        /// <summary>
        /// The hour target specified is a duplicate.
        /// </summary>
        public const int DuplicateHourTarget = 1409;

        /// <summary>
        /// The metro area target specified is a duplicate.
        /// </summary>
        public const int DuplicateMetroAreaLocationTarget = 1410;

        /// <summary>
        /// The country/region target specified is a duplicate.
        /// </summary>
        public const int DuplicateCountryLocationTarget = 1411;

        /// <summary>
        /// The gender target specified is a duplicate.
        /// </summary>
        public const int DuplicateGenderTarget = 1412;

        /// <summary>
        /// The age target specified is a duplicate.
        /// </summary>
        public const int DuplicateAgeTarget = 1413;

        /// <summary>
        /// The country/region and metro targets should be mutually exclusive.
        /// </summary>
        public const int CountryAndMetroAreaTargetsExclusive = 1414;

        /// <summary>
        /// The metro targets specified should be from the same country/region.
        /// </summary>
        public const int MetroTargetsFromMultipleCountries = 1415;

        /// <summary>
        /// When day target is specified, incremental budget amount should be not null and greater than zero
        /// </summary>
        public const int IncrementalBudgetAmountRequiredForDayTarget = 1416;

        /// <summary>
        /// The number of location targets in the adgroup exceeds the limit.
        /// </summary>
        public const int GeoTargetsInAdGroupExceedsLimit = 1417;

        /// <summary>
        /// Duplicate Ids are contained in the array of targets.
        /// </summary>
        public const int DuplicateInTargetIds = 1418;

        /// <summary>
        /// User does not have permission to all entities that this TargetGroup is assigned.
        /// </summary>
        public const int TargetGroupAssignedEntitiesPermissionMismatch = 1419;

        /// <summary>
        /// Targets passed is null.
        /// </summary>
        public const int TargetsNotPassed = 1420;

        /// <summary>
        /// Target already exists.
        /// </summary>
        public const int TargetAlreadyExists = 1421;

        /// <summary>
        /// Targets limit has been reached.
        /// </summary>
        public const int TargetsLimitReached = 1422;

        /// <summary>
        /// Invalid Geo Location level.
        /// </summary>
        public const int InvalidGeoLocationLevel = 1423;

        /// <summary>
        /// The adGroup medium is invalid for Business Targets.
        /// </summary>
        public const int AdGroupMediumInvalidWithBusinessTargets = 1424;

        /// <summary>
        /// The list of targets exceeds the limit.
        /// </summary>
        public const int TargetsArrayExceedsLimit = 1425;

        /// <summary>
        /// Target is not associated with the entity.
        /// </summary>
        public const int TargetNotAssociatedWithEntity = 1426;

        /// <summary>
        /// A target is already associated with the entity.
        /// </summary>
        public const int TargetAlreadyAssociatedWithEntity = 1427;

        /// <summary>
        /// This target is associated with entities.
        /// </summary>
        public const int TargetHasActiveAssociations = 1428;

        /// <summary>
        /// The target is invalid.
        /// </summary>
        public const int InvalidTarget = 1429;

        /// <summary>
        /// The Behavioral Targeting pilot is not enabled for the customer.
        /// </summary>
        public const int BTTargettingNotEnabledForPilot = 1430;

        /// <summary>
        /// Behavior target is invalid.
        /// </summary>
        public const int InvalidBehaviorTarget = 1431;

        /// <summary>
        /// There is a duplicate in the behavior target .
        /// </summary>
        public const int DuplicateBehaviorTarget = 1432;

        /// <summary>
        /// Invalid segment target.
        /// </summary>
        public const int InvalidSegmentTarget = 1433;

        /// <summary>
        /// There is a duplicate in the segment target.
        /// </summary>
        public const int DuplicateSegmentTarget = 1434;

        /// <summary>
        /// Negative Bidding is not allowed for this type of target.
        /// </summary>
        public const int NegativeBiddingNotAllowedForThisTargetType = 1435;

        /// <summary>
        /// Invalid cashback text in segment target.
        /// </summary>
        public const int InvalidCashbackTextinSegmentTarget = 1436;

        /// <summary>
        /// Invalid param1 in segment target.
        /// </summary>
        public const int InvalidParam1inSegmentTarget = 1437;

        /// <summary>
        /// Invalid param2 in segment target.
        /// </summary>
        public const int InvalidParam2inSegmentTarget = 1438;

        /// <summary>
        /// Invalid param3 in segment target.
        /// </summary>
        public const int InvalidParam3inSegmentTarget = 1439;

        /// <summary>
        /// Invalid segment param1 in segment target.
        /// </summary>
        public const int InvalidSegmentParam1inSegmentTarget = 1440;

        /// <summary>
        /// Invalid segment param2 in segment target.
        /// </summary>
        public const int InvalidSegmentParam2inSegmentTarget = 1441;

        /// <summary>
        /// Cannot specify segment target with any other target.
        /// </summary>
        public const int CannotSpecifySegmentTargetsWithAnyOtherTarget = 1442;

        /// <summary>
        /// Business locations not passed.
        /// </summary>
        public const int BusinessLocationsNotPassed = 1443;

        /// <summary>
        /// Invalid Business Location Hours.
        /// </summary>
        public const int InvalidBusinessLocationHours = 1444;

        /// <summary>
        /// Invalid address specified.
        /// </summary>
        public const int InvalidAddress = 1445;

        /// <summary>
        /// Business location targets limit has been reached for customer.
        /// </summary>
        public const int BusinessLocationTargetsLimitReachedForCustomer = 1446;

        /// <summary>
        /// Business location targets limit has been reached for adgroup.
        /// </summary>
        public const int BusinessLocationTargetsLimitReachedForAdGroup = 1447;

        /// <summary>
        /// Business location targets limit has been reached for campaign.
        /// </summary>
        public const int BusinessLocationTargetsLimitReachedForCampaign = 1448;

        /// <summary>
        /// Maximum number of bids for Age is exceeded.
        /// </summary>
        public const int TargetsAgeBidsBatchLimitExceeded = 1449;

        /// <summary>
        /// Maximum number of bids for Day is exceeded.
        /// </summary>
        public const int TargetsDayBidsBatchLimitExceeded = 1450;

        /// <summary>
        /// Maximum number of bids for Hour is exceeded.
        /// </summary>
        public const int TargetsHourBidsBatchLimitExceeded = 1451;

        /// <summary>
        /// Maximum number of bids for Gender is exceeded.
        /// </summary>
        public const int TargetsGenderBidsBatchLimitExceeded = 1452;

        /// <summary>
        /// Maximum number of bids for Location is exceeded.
        /// </summary>
        public const int TargetsLocationBidsBatchLimitExceeded = 1453;

        /// <summary>
        /// Maximum number of bids for Segment is exceeded.
        /// </summary>
        public const int TargetsSegmentBidsBatchLimitExceeded = 1454;

        /// <summary>
        /// Maximum number of bids for Behavior is exceeded.
        /// </summary>
        public const int TargetsBehaviorBidsBatchLimitExceeded = 1455;

        /// <summary>
        /// Business location target ID is invalid.
        /// </summary>
        public const int InvalidBusinessLocationId = 1456;

        /// <summary>
        /// Target radius is invalid. Valid values are 5, 10, 20, .. 100.
        /// </summary>
        public const int InvalidTargetRadius = 1457;

        /// <summary>
        /// Passed value for latitude is invalid.
        /// </summary>
        public const int InvalidLatitude = 1458;

        /// <summary>
        /// Passed value for longitude is invalid.
        /// </summary>
        public const int InvalidLongitude = 1459;

        /// <summary>
        /// The subgeography target is duplicated.
        /// </summary>
        public const int DuplicateSubGeographyTarget = 1460;

        /// <summary>
        /// The city target is duplicated.
        /// </summary>
        public const int DuplicateCityTarget = 1461;

        /// <summary>
        /// The business location target is duplicated.
        /// </summary>
        public const int DuplicateBusinessLocationTarget = 1462;

        /// <summary>
        /// The custom location target is duplicated.
        /// </summary>
        public const int DuplicateCustomLocationTarget = 1463;

        /// <summary>
        /// Location ID is invalid.
        /// </summary>
        public const int InvalidLocationId = 1464;

        /// <summary>
        /// Geo location options required.
        /// </summary>
        public const int GeoLocationOptionsRequired = 1465;

        /// <summary>
        /// Unsupported combination of location Id and options.
        /// </summary>
        public const int UnsupportedCombinationOfLocationIdAndOptions = 1466;

        /// <summary>
        /// Invalid geographical location search string.
        /// </summary>
        public const int InvalidGeographicalLocationSearchString = 1467;

        /// <summary>
        /// Geo targets (Country/Region/State/MetroArea/City) and Business/Radius targets are mutually exclusive.
        /// </summary>
        public const int GeoTargetsAndBusinessTargetsMutuallyExclusive = 1468;

        /// <summary>
        /// Business location is not set.
        /// </summary>
        public const int BusinessLocationNotSet = 1469;

        /// <summary>
        /// Business name is required.
        /// </summary>
        public const int BusinessNameRequired = 1470;

        /// <summary>
        /// Latitude and longitude are required.
        /// </summary>
        public const int LatitudeLongitudeRequired = 1471;

        /// <summary>
        /// Business name is too long.
        /// </summary>
        public const int BusinessNameTooLong = 1472;

        /// <summary>
        /// Domain name is already taken.
        /// </summary>
        public const int DomainNameAlreadyTaken = 1473;

        /// <summary>
        /// Business description is too long.
        /// </summary>
        public const int BusinessDescriptionTooLong = 1474;

        /// <summary>
        /// Business type ID is invalid.
        /// </summary>
        public const int InvalidBusinessTypeId = 1475;

        /// <summary>
        /// Payment type ID is invalid.
        /// </summary>
        public const int InvalidPaymentTypeId = 1476;

        /// <summary>
        /// Business hours entry is invalid.
        /// </summary>
        public const int InvalidBusinessHoursEntry = 1477;

        /// <summary>
        /// Address is required.
        /// </summary>
        public const int AddressRequired = 1478;

        /// <summary>
        /// Address is too long.
        /// </summary>
        public const int AddressTooLong = 1479;

        /// <summary>
        /// City name is required.
        /// </summary>
        public const int CityNameRequired = 1480;

        /// <summary>
        /// City name is too long.
        /// </summary>
        public const int CityNameTooLong = 1481;

        /// <summary>
        /// Country/region code is required.
        /// </summary>
        public const int CountryCodeRequired = 1482;

        /// <summary>
        /// Country/region code is too long.
        /// </summary>
        public const int CountryCodeTooLong = 1483;

        /// <summary>
        /// StateOrProvince field is required.
        /// </summary>
        public const int StateOrProvinceRequired = 1484;

        /// <summary>
        /// StateOrProvince field is too long.
        /// </summary>
        public const int StateOrProvinceTooLong = 1485;

        /// <summary>
        /// Location is not specified.
        /// </summary>
        public const int LocationIsNotSpecified = 1486;

        /// <summary>
        /// Open24Hours and business hours are mutually exclusive.
        /// </summary>
        public const int Open24HoursAndBusinessHoursMutuallyExclusive = 1487;

        /// <summary>
        /// Business name and address already exists.
        /// </summary>
        public const int BusinessNameAndAddressAlreadyExists = 1488;

        /// <summary>
        /// Business location list is too long.
        /// </summary>
        public const int BusinessLocationListTooLong = 1489;

        /// <summary>
        /// Customer ID is invalid.
        /// </summary>
        public const int InvalidCustomerId = 1490;

        /// <summary>
        /// Domain name invalid.
        /// </summary>
        public const int InvalidDomainName = 1491;

        /// <summary>
        /// Business domain name is not set.
        /// </summary>
        public const int BusinessDomainNameNotSet = 1492;

        /// <summary>
        /// Business domain timestamp mismatch.
        /// </summary>
        public const int BusinessDomainTimestampMismatch = 1493;

        /// <summary>
        /// Timestamp is required for business domain name modification.
        /// </summary>
        public const int TimestampRequiredForBusinessDomainNameModification = 1494;

        /// <summary>
        /// Business domain is already set.
        /// </summary>
        public const int BusinessDomainAlreadySet = 1495;

        /// <summary>
        /// Domain name is unknown.
        /// </summary>
        public const int DomainNameUnknown = 1496;

        /// <summary>
        /// Only one business location per customer is allowed.
        /// </summary>
        public const int OnlyOneBusinessLocationPerCustomerIsAllowed = 1497;

        /// <summary>
        /// Only zero incremental bid is allowed for this target type.
        /// </summary>
        public const int BiddingOtherThanZeroNotAllowedForThisTargetType = 1498;

        /// <summary>
        /// You can't target all locations for this target type.
        /// </summary>
        public const int TargetingShouldBeExclusiveForThisTargetType = 1499;

        /// <summary>
        /// Invalid cashback amount for the Segment target.
        /// </summary>
        public const int InvalidCashbackAmountInSegmentTarget = 2900;

        /// <summary>
        /// Business hours are mismatched. Please make sure begin hour is earlier than end hour.
        /// </summary>
        public const int BusinessLocationBeginAndEndHoursMismatch = 2901;

        /// <summary>
        /// The Target name is invalid.
        /// </summary>
        public const int InvalidTargetName = 2902;

        /// <summary>
        /// Payment types specified has duplciate value(s).
        /// </summary>
        public const int DuplicatePaymentTypes = 2903;

        /// <summary>
        /// Customer account is not enabled to use one or more of target types in the target.
        /// </summary>
        public const int TargetInvalidForCustomer = 2904;

        /// <summary>
        /// Entered email address is invalid.
        /// </summary>
        public const int InvalidEmail = 2905;

        /// <summary>
        /// Entered email address is too long.
        /// </summary>
        public const int EmailTooLong = 2906;

        /// <summary>
        /// Entered phone number is invalid.
        /// </summary>
        public const int BusinessPhoneNumberInvalid = 2907;

        /// <summary>
        /// Entered phone number is too long.
        /// </summary>
        public const int PhoneNumberTooLong = 2908;

        /// <summary>
        /// Setting IsLibraryTarget flag is not allowed.
        /// </summary>
        public const int IsLibraryTargetNotNull = 2909;

        /// <summary>
        /// Latitude/Longitude of business location is invalid. Please make sure that business location has valid latitude/longitude.
        /// </summary>
        public const int InvalidLatitudeLongitudeForBusinessLocation = 2910;

        /// <summary>
        /// Data in ZipOrPostalCode field is too long.
        /// </summary>
        public const int ZipOrPostalCodeTooLong = 2914;

        /// <summary>
        /// Business description is required.
        /// </summary>
        public const int BusinessDescriptionRequired = 2915;

        /// <summary>
        /// Duplicates are present in the array of business hours.
        /// </summary>
        public const int DuplicateBusinessHours = 2916;

        /// <summary>
        /// Address is invalid.
        /// </summary>
        public const int AddressInvalid = 2917;

        /// <summary>
        /// Invalid Medium for Business/Radius targets.
        /// </summary>
        public const int BusinessAndRadiusTargetsAllowedOnlyForSearchMedium = 2918;

        /// <summary>
        /// Only targets in target library can be associated with an AdGroup or Campaign.
        /// </summary>
        public const int AssociatingNonLibraryTargetNotAllowed = 2919;

        /// <summary>
        /// Business address cannot be updated with invalid value.
        /// </summary>
        public const int BusinessAddressShouldBeValidForUpdate = 2920;

        /// <summary>
        /// This business is associated with entities.
        /// </summary>
        public const int BusinessHasActiveAssociations = 2921;

        /// <summary>
        /// The device target specified is invalid.
        /// </summary>
        public const int InvalidDeviceTarget = 2922;

        /// <summary>
        /// The device target specified is a duplicate.
        /// </summary>
        public const int DuplicateDeviceTarget = 2923;

        /// <summary>
        /// The radius target id is not used currently.
        /// </summary>
        public const int RadiusTargetIdCannotBeSet = 2925;

        /// <summary>
        /// The radius target name is not used currently.
        /// </summary>
        public const int RadiusTargetNameCannotBeSet = 2926;

        /// <summary>
        /// OS Targeting is not enabled for the current customer.
        /// </summary>
        public const int OSTargetingNotEnabledForPilot = 2927;

        /// <summary>
        /// You cannot modify data for this customer at the moment, as it is under migration.
        /// </summary>
        public const int CustomerDataBeingMigrated = 2928;

        /// <summary>
        /// Setting PhysicalIntent is not enabled for the current customer.
        /// </summary>
        public const int PhysicalIntentNotEnabledForPilot = 2929;

        /// <summary>
        /// The business location is duplicated.
        /// </summary>
        public const int DuplicateBusinessLocation = 2930;

        /// <summary>
        /// Multiple duplicate business location exist in the system.
        /// </summary>
        public const int MultipleDuplicateBusinessLocation = 2931;

        /// <summary>
        /// The version number of the location target codes is not supported.
        /// </summary>
        public const int LocationTargetVersionIsNotSupported = 2932;

        /// <summary>
        /// Business location targeting is no longer supported.
        /// </summary>
        public const int BusinessLocationTargetingIsNotSupported = 2933;

        /// <summary>
        /// The value of device target bid adjustment is not valid.
        /// </summary>
        public const int InvalidDeviceTargetBidAdjustment = 2934;

        /// <summary>
        /// The value of location target bid adjustment is not valid.
        /// </summary>
        public const int InvalidLocationTargetBidAdjustment = 2935;

        /// <summary>
        /// The value of age target bid adjustment is not valid.
        /// </summary>
        public const int InvalidAgeTargetBidAdjustment = 2936;

        /// <summary>
        /// The value of gender target bid adjustment is not valid.
        /// </summary>
        public const int InvalidGenderTargetBidAdjustment = 2937;

        /// <summary>
        /// The value of day target bid adjustment is not valid.
        /// </summary>
        public const int InvalidDayTargetBidAdjustment = 2938;

        /// <summary>
        /// The value of hour target bid adjustment is not valid.
        /// </summary>
        public const int InvalidHourTargetBidAdjustment = 2939;

        /// <summary>
        /// The value of segmented target bid adjustment is not valid.
        /// </summary>
        public const int InvalidSegmentedTargetBidAdjustment = 2940;

        /// <summary>
        /// The null value for bid adjustment is not valid.
        /// </summary>
        public const int BidAdjustmentNullPassedForAddTarget = 2941;

        /// <summary>
        /// The IsExcluded element of the specified target bid is reserved for future use and may not be set to true.
        /// </summary>
        public const int InvalidTargetBidExclusion = 2942;

        /// <summary>
        /// You must set either Miles or Kilometers as the radius unit of a radius target bid.
        /// </summary>
        public const int InvalidTargetRadiusUnit = 2943;

        /// <summary>
        /// The DayTimeTarget is not Valid.
        /// </summary>
        public const int InvalidDayTimeTarget = 2944;

        /// <summary>
        /// The number of bids in the DayTimeTarget would be exceeded.
        /// </summary>
        public const int TargetsDayTimeBidBatchLimitExceeded = 2945;

        /// <summary>
        /// The From or To interval of the DayTimeTargetBid is not valid.
        /// </summary>
        public const int TargetDayTimeIntervalInvalid = 2946;

        /// <summary>
        /// The customer is not in Pilot to use DayTime Targets.
        /// </summary>
        public const int CustomerNotEnabledForDayTimePilot = 2947;

        /// <summary>
        /// The PostalCode Target Bids contain Duplicate PostalCodes.
        /// </summary>
        public const int DuplicatePostalCodeTarget = 2948;

        /// <summary>
        /// The customer is not in Pilot to use PostalCode Targets.
        /// </summary>
        public const int PostalCodeNotSupportedForCustomer = 2949;

        /// <summary>
        /// DayHour and DayTime targets are Exclusive
        /// </summary>
        public const int TargetDayTimeAndDayHourAreExclusive = 2950;

        /// <summary>
        /// The DayTime Target intervals overlap.
        /// </summary>
        public const int TargetDayTimeOverlapping = 2951;

        /// <summary>
        /// The DayTime Target BidAdjustment is invalid.
        /// </summary>
        public const int InvalidDayTimeTargetBidAdjustment = 2952;

        /// <summary>
        /// OSName is not supported
        /// </summary>
        public const int OSNameIsNotSupported = 2953;

        /// <summary>
        /// The target cannot be migrated to criterion because of an error with another target subtype.
        /// </summary>
        public const int RelatedCriterionActionError = 2954;

        /// <summary>
        /// The Criterion ID is invalid.
        /// </summary>
        public const int InvalidCriterionId = 2955;

        /// <summary>
        /// The Existing Criterion is different from the one passed for the same ID
        /// </summary>
        public const int CriterionDoesNotMatch = 2956;

        /// <summary>
        /// The entity must either be explicitly associated with all device criterion types (Computers, Tablets, Smartphones) or should not have any device criterion.
        /// </summary>
        public const int IncompleteDeviceCriterionSet = 2957;

        /// <summary>
        /// This is not a valid Geolocations.csv file version.
        /// </summary>
        public const int InvalidGeolocationsFileVersion = 2958;

        /// <summary>
        /// This is not a valid LanguageLocale.
        /// </summary>
        public const int InvalidLanguageLocale = 2959;

        /// <summary>
        /// This is not a valid Geolocations file format.
        /// </summary>
        public const int InvalidGeolocationsFileFormat = 2969;

        /// <summary>
        /// The target combination is invalid.
        /// </summary>
        public const int InvalidTargetCombination = 2970;

        /// <summary>
        /// AdExtensions DeviceTarget Entities array exceeds the limit.
        /// </summary>
        public const int AdExtensionDeviceTargetEntityLimitExceeded = 2960;

        /// <summary>
        /// AdExtensions DeviceTarget EntityIds cannot be null.
        /// </summary>
        public const int AdExtensionDeviceTargeEntityIdsNullOrEmpty = 2961;

        /// <summary>
        /// AdExtensions DeviceTarget EntityIds invalid.
        /// </summary>
        public const int AdExtensionDeviceTargeEntityIdsInvalid = 2962;

        /// <summary>
        /// AdExtensions DeviceTarget value invalid.
        /// </summary>
        public const int AdExtensionDeviceTargeValueInvalid = 2963;

        /// <summary>
        /// Customer not enabled for AdExtensionDeviceTarge pilot
        /// </summary>
        public const int CustomerNotEnabledForAdExtensionDeviceTargetPilot = 2964;

        /// <summary>
        /// Location intent criterion cannot be deleted.
        /// </summary>
        public const int LocationIntentCriterionCannotBeDeleted = 2965;

        /// <summary>
        /// The location intent option is invalid.
        /// </summary>
        public const int LocationIntentCriterionInvalid = 2966;

        /// <summary>
        /// The bids must be equal for the same device type.
        /// </summary>
        public const int BidsMustBeEqualForDeviceType = 2967;

        /// <summary>
        /// The customer is not in pilot to use County Targets.
        /// </summary>
        public const int CustomerNotEnabledForCountyTargets = 2968;

        /// <summary>
        /// Keyword is null.
        /// </summary>
        public const int NullKeyword = 1500;

        /// <summary>
        /// The Keyword ID is invalid.
        /// </summary>
        public const int InvalidKeywordId = 1501;

        /// <summary>
        /// Duplicate IDs are contained in the array of keywords.
        /// </summary>
        public const int DuplicateInKeywordIds = 1502;

        /// <summary>
        /// The Keyword text is invalid.
        /// </summary>
        public const int InvalidKeywordText = 1503;

        /// <summary>
        /// The Keyword text cannot be changed on Update.
        /// </summary>
        public const int CannotChangeTextOnUpdate = 1504;

        /// <summary>
        /// Keywords array should not be null or empty.
        /// </summary>
        public const int KeywordsArrayShouldNotBeNullOrEmpty = 1505;

        /// <summary>
        /// The list of keywords exceeds the limit.
        /// </summary>
        public const int KeywordsArrayExceedsLimit = 1506;

        /// <summary>
        /// The bid amounts are invalid.
        /// </summary>
        public const int InvalidBidAmounts = 1507;

        /// <summary>
        /// The bid amounts are invalid for Search adgroup.
        /// </summary>
        public const int InvalidBidAmountForSearchAdGroup = 1508;

        /// <summary>
        /// The bid amounts are invalid for Content adgroup.
        /// </summary>
        public const int InvalidBidAmountForContentAdGroup = 1509;

        /// <summary>
        /// The bid amounts are invalid for Hybrid adgroup.
        /// </summary>
        public const int InvalidBidAmountForHybridAdGroup = 1510;

        /// <summary>
        /// The Param 1 is invalid.
        /// </summary>
        public const int InvalidParam1 = 1511;

        /// <summary>
        /// The Param 2 is invalid.
        /// </summary>
        public const int InvalidParam2 = 1512;

        /// <summary>
        /// The Param 3 is invalid.
        /// </summary>
        public const int InvalidParam3 = 1513;

        /// <summary>
        /// The Negative keywords requires partial match bid.
        /// </summary>
        public const int NegativeKeywordRequiresPartialMatchBid = 1514;

        /// <summary>
        /// Bid amounts are less than floor price.
        /// </summary>
        public const int BidAmountsLessThanFloorPrice = 1515;

        /// <summary>
        /// Bid amounts are greater than ceiling price.
        /// </summary>
        public const int BidAmountsGreaterThanCeilingPrice = 1516;

        /// <summary>
        /// Trying to create a duplicate keyword.
        /// </summary>
        public const int DuplicateKeyword = 1517;

        /// <summary>
        /// Cannot add any more keywords to this account.
        /// </summary>
        public const int MaxKeywordsReachedForAccount = 1518;

        /// <summary>
        /// Cannot add any more keywords to this adgroup.
        /// </summary>
        public const int MaxKeywordsReachedForAdGroup = 1519;

        /// <summary>
        /// There is some forbidden text in the Keyword.
        /// </summary>
        public const int ForbiddenWordInKeywordText = 1520;

        /// <summary>
        /// There is some forbidden text in the parameter 1.
        /// </summary>
        public const int ForbiddenWordInParam1 = 1521;

        /// <summary>
        /// There is some forbidden text in the parameter 2.
        /// </summary>
        public const int ForbiddenWordInParam2 = 1522;

        /// <summary>
        /// There is some forbidden text in the parameter 3.
        /// </summary>
        public const int ForbiddenWordInParam3 = 1523;

        /// <summary>
        /// A reserved word has been used in the Keyword.
        /// </summary>
        public const int ExclusiveWordInKeywordText = 1524;

        /// <summary>
        /// A reserved word has been used in the parameter 1.
        /// </summary>
        public const int ExclusiveWordInParam1 = 1525;

        /// <summary>
        /// A reserved word has been used in the parameter 2.
        /// </summary>
        public const int ExclusiveWordInParam2 = 1526;

        /// <summary>
        /// A reserved word has been used in the parameter 3.
        /// </summary>
        public const int ExclusiveWordInParam3 = 1527;

        /// <summary>
        /// Keyword specified does not belong to the AdGroup.
        /// </summary>
        public const int KeywordDoesNotBelongToAdGroupId = 1528;

        /// <summary>
        /// Keyword Ids array should not be null or empty.
        /// </summary>
        public const int KeywordIdsArrayShouldNotBeNullOrEmpty = 1529;

        /// <summary>
        /// The list of keyword Ids exceeds the limit.
        /// </summary>
        public const int KeywordIdsArrayExceedsLimit = 1530;

        /// <summary>
        /// The keyword status is invalid for the current operation.
        /// </summary>
        public const int InvalidKeywordStatus = 1531;

        /// <summary>
        /// Setting Editorial Status is not allowed.
        /// </summary>
        public const int InvalidKeywordEditorialStatus = 1532;

        /// <summary>
        /// Exemption Request for ad is not currently supported and hence cannot be set.
        /// </summary>
        public const int CannotSetExemptionRequestOnKeyword = 1533;

        /// <summary>
        /// Update keyword entity has no data
        /// </summary>
        public const int UpdateKeywordEmpty = 1534;

        /// <summary>
        /// Duplicate Keyword Ids in the request.
        /// </summary>
        public const int KeywordIdDuplicateInRequest = 1536;

        /// <summary>
        /// Cannot add keyword to an adgroup whose bidding model type is not Keyword.
        /// </summary>
        public const int CannotAddKeywordToSpecifiedAdGroup = 1537;

        /// <summary>
        /// The bid amount is invalid.
        /// </summary>
        public const int InvalidBidAmount = 1538;

        /// <summary>
        /// Cannot add any more keywords to this customer.
        /// </summary>
        public const int MaxKeywordsReachedForCustomer = 1539;

        /// <summary>
        /// You must set only one of the match type bids (for example, ExactMatchBid) in the Keyword object to a non-null value. To specify multiple match type bids for a keyword, you must create a Keyword object for each keyword and match type combination.
        /// </summary>
        public const int MultipleKeywordBidTypesNotAllowed = 1541;

        /// <summary>
        /// A keyword with the specified match type already exists.
        /// </summary>
        public const int KeywordAndMatchTypeCombinationAlreadyExists  = 1542;

        /// <summary>
        /// The Keyword object does not contain a valid and non-null bid for the specified match type.
        /// </summary>
        public const int KeywordBidRequired = 1543;

        /// <summary>
        /// You cannot set the amount of a match type bid to zero (0).
        /// </summary>
        public const int KeywordZeroBidAmountNotAllowed = 1544;

        /// <summary>
        /// You cannot change the match type on which you bid when you update a Keyword object.
        /// </summary>
        public const int KeywordMatchTypeChangeNotAllowedInUpdate = 1545;

        /// <summary>
        /// The KeywordByMatchType pilot is not enabled for the given customer.
        /// </summary>
        public const int KeywordByMatchTypePilotNotEnabledForCustomer = 1546;

        /// <summary>
        /// You cannot set UpdateMatchType to true unless you specify a new match type bid.
        /// </summary>
        public const int MatchTypeNotUpdated = 1548;

        /// <summary>
        /// You need to pass an array for KeywordDestinationUrls.
        /// </summary>
        public const int KeywordDestinationUrlsArrayShouldNotBeNullOrEmpty = 1549;

        /// <summary>
        /// Too much text is provided in keyword destination URL.
        /// </summary>
        public const int TooMuchTextInKeywordDestinationUrl = 1550;

        /// <summary>
        /// This operation is transaction dependent on another failed operation
        /// </summary>
        public const int KeywordTransactionalDependencyFailed = 1551;

        /// <summary>
        /// The match type is required.
        /// </summary>
        public const int MatchTypeRequired = 1552;

        /// <summary>
        /// The Keyword ad association status is invalid.
        /// </summary>
        public const int InvalidStatus = 1600;

        /// <summary>
        /// The Keyword ad association status is not passed.
        /// </summary>
        public const int StatusIsNull = 1601;

        /// <summary>
        /// The Modified-After date is invalid.
        /// </summary>
        public const int InvalidModifiedAfterDate = 1602;

        /// <summary>
        /// The Modified-After date is not in UTC.
        /// </summary>
        public const int ModifiedAfterDateNotInUtc = 1603;

        /// <summary>
        /// You cannot add this entity to a campaign of type Shopping.
        /// </summary>
        public const int EntityNotAllowedForShoppingCampaign = 1604;

        /// <summary>
        /// You cannot add this entity to a campaign of type DynamicSearchAds.
        /// </summary>
        public const int EntityNotAllowedForDynamicSearchAdsCampaign = 1605;

        /// <summary>
        /// You cannot add Dynamic Search Ad to a non DynamicSearchAdscampaign.
        /// </summary>
        public const int DynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign = 1606;

        /// <summary>
        /// You cannot add this entity to a campaign of type Audience.
        /// </summary>
        public const int EntityNotAllowedForAudienceCampaign = 1607;

        /// <summary>
        /// KeywordBid is null.
        /// </summary>
        public const int NullKeywordBid = 1700;

        /// <summary>
        /// KeywordBids array should not be null or empty.
        /// </summary>
        public const int KeywordBidsArrayShouldNotBeNullOrEmpty = 1701;

        /// <summary>
        /// The list of keywordBids exceeds the limit.
        /// </summary>
        public const int KeywordBidsArrayExceedsLimit = 1702;

        /// <summary>
        /// Invalid Keyword in the bid.
        /// </summary>
        public const int KeywordBidsInvalidKeyword = 1703;

        /// <summary>
        /// Please specify at least one of the keyword bids.
        /// </summary>
        public const int AtleastOneKeywordBidShouldBeSpecified = 1704;

        /// <summary>
        /// Invalid value passed for LanguageAndRegion. Please refer to documentation for valid values or leave empty.
        /// </summary>
        public const int InvalidLanguageAndRegionValue = 1705;

        /// <summary>
        /// Invalid EntityId passed in the array of EntityIds.
        /// </summary>
        public const int InvalidEntity = 1706;

        /// <summary>
        /// Duplicate EntityID passed in the array of EntityIds.
        /// </summary>
        public const int DuplicateEntity = 1707;

        /// <summary>
        /// Appeal creation failed because JustificationText is missing.
        /// </summary>
        public const int JustificationTextMissingForInlineAppeal = 1708;

        /// <summary>
        /// Appeal creation failed because Justification text was too long (should be less than 2000 characters).
        /// </summary>
        public const int JustificationTextTooLongForInlineAppeal = 1709;

        /// <summary>
        /// Appeal creation quota Exceeded.
        /// </summary>
        public const int EditorialAppealCreationQuotaExceeded = 1710;

        /// <summary>
        /// Appeal creation quota Exceeded for last 24 hours.
        /// </summary>
        public const int EditorialAppealCreationQuotaExceededForLast24Hours = 1711;

        /// <summary>
        /// Appeal already created for the entity.
        /// </summary>
        public const int EditorialAppealEntityAlreadyAppealed = 1712;

        /// <summary>
        /// Segment is null.
        /// </summary>
        public const int NullSegment = 1900;

        /// <summary>
        /// The Segment Id is invalid.
        /// </summary>
        public const int InvalidSegmentId = 1901;

        /// <summary>
        /// Duplicate Ids are contained in the array of segments.
        /// </summary>
        public const int DuplicateInSegmentIds = 1902;

        /// <summary>
        /// The Segment name is invalid.
        /// </summary>
        public const int InvalidSegmentName = 1903;

        /// <summary>
        /// The user hash specified is invalid.
        /// </summary>
        public const int InvalidUserHash = 1904;

        /// <summary>
        /// Segments array should not be null or empty.
        /// </summary>
        public const int SegmentsArrayShouldNotBeNullOrEmpty = 1905;

        /// <summary>
        /// The list of segments exceeds the limit.
        /// </summary>
        public const int SegmentsArrayExceedsLimit = 1906;

        /// <summary>
        /// There is a duplicate segment name in the array.
        /// </summary>
        public const int DuplicateSegmentName = 1907;

        /// <summary>
        /// Customer Account is not enabled to perform operations on segments.
        /// </summary>
        public const int SegmentOperationNotAllowedForPilot = 1908;

        /// <summary>
        /// User Hash array should not be null or empty.
        /// </summary>
        public const int UserHashArrayShouldNotBeNullOrEmpty = 1909;

        /// <summary>
        /// The list of User Hashes exceeds the limit.
        /// </summary>
        public const int UserHashArrayExceedsLimit = 1910;

        /// <summary>
        /// Segment Ids array should not be null or empty.
        /// </summary>
        public const int SegmentIdsArrayShouldNotBeNullOrEmpty = 1911;

        /// <summary>
        /// The list of segment Ids exceeds the limit.
        /// </summary>
        public const int SegmentIdsArrayExceedsLimit = 1912;

        /// <summary>
        /// The limit of segments for the customer has been reached.
        /// </summary>
        public const int MaxSegmentsForCustomerHasBeenReached = 1913;

        /// <summary>
        /// Segment targeting not available for this ad group's DistributionChannel.
        /// </summary>
        public const int SegmentNotAllowedForDistributionChannel = 1914;

        /// <summary>
        /// Invalid URL has been specified.
        /// </summary>
        public const int InvalidUrl = 2611;

        /// <summary>
        /// Found a duplicate Url in the input.
        /// </summary>
        public const int DuplicateUrl = 2619;

        /// <summary>
        /// The provided URL was not crawlable.
        /// </summary>
        public const int UncrawlableUrl = 2629;

        /// <summary>
        /// BehavioralBid is null.
        /// </summary>
        public const int NullBehavioralBid = 2700;

        /// <summary>
        /// The BehavioralBidId is invalid.
        /// </summary>
        public const int InvalidBehavioralBidId = 2701;

        /// <summary>
        /// Duplicate Ids are contained in the array of BehavioralBids.
        /// </summary>
        public const int DuplicateInBehavioralBidIds = 2702;

        /// <summary>
        /// BehavioralBids array should not be null or empty.
        /// </summary>
        public const int BehavioralBidsArrayShouldNotBeNullOrEmpty = 2703;

        /// <summary>
        /// The list of BehavioralBids exceeds the limit.
        /// </summary>
        public const int BehavioralBidsArrayExceedsLimit = 2704;

        /// <summary>
        /// Customer Account is not enabled to perform operations on BehavioralBids.
        /// </summary>
        public const int BehavioralBidOperationNotAllowedForPilot = 2705;

        /// <summary>
        /// BehavioralBid Ids array should not be null or empty.
        /// </summary>
        public const int BehavioralBidIdsArrayShouldNotBeNullOrEmpty = 2706;

        /// <summary>
        /// The list of BehavioralBid Ids exceeds the limit.
        /// </summary>
        public const int BehavioralBidIdsArrayExceedsLimit = 2707;

        /// <summary>
        /// The behavioral bid name is invalid.
        /// </summary>
        public const int InvalidBehavioralBidName = 2708;

        /// <summary>
        /// The behavioral name cannot be changed.
        /// </summary>
        public const int CannotChangeBehavioralName = 2709;

        /// <summary>
        /// There is no information to update in the given behavioral bid request.
        /// </summary>
        public const int NothingToUpdateInBehavioralBidRequest = 2710;

        /// <summary>
        /// Cannot add behavioral bids to an adgroup whose bidding model type is not BehavioralBid.
        /// </summary>
        public const int CannotAddBehavioralBidToSpecifiedAdGroup = 2711;

        /// <summary>
        /// The BT Bidding pilot is not enabled for the customer.
        /// </summary>
        public const int BTBiddingNotEnabledForPilot = 2712;

        /// <summary>
        /// Trying to add a duplicate behavioral bid.
        /// </summary>
        public const int DuplicateBehavioralBid = 2713;

        /// <summary>
        /// The behavioral Bid status is invalid for the current operation.
        /// </summary>
        public const int InvalidBehavioralBidStatus = 2714;

        /// <summary>
        /// Invalid ad group medium and bidding strategy combination.
        /// </summary>
        public const int InvalidMediumAndBiddingStrategyCombination = 2715;

        /// <summary>
        /// The segment Id cannot be changed.
        /// </summary>
        public const int CannotChangeSegmentId = 2716;

        /// <summary>
        /// Either segment Id or name must be passed.
        /// </summary>
        public const int NameAndSegmentIdMutuallyExclusive = 2717;

        /// <summary>
        /// Invalid Ids are passed.
        /// </summary>
        public const int InvalidIds = 2718;

        /// <summary>
        /// The Geo Entity Type is invalid.
        /// </summary>
        public const int InvalidGeoEntityType = 2810;

        /// <summary>
        /// The Excluded Location name is null or empty.
        /// </summary>
        public const int ExcludedLocationNameIsNullOrEmpty = 2811;

        /// <summary>
        /// The Location Exclusion parameter is null.
        /// </summary>
        public const int LocationExclusionIsNull = 2812;

        /// <summary>
        /// The exclusion type is mapped to an incorrect associated entity.
        /// </summary>
        public const int ExclusionTypeMappedToIncorrectAssociatedEntity = 2813;

        /// <summary>
        /// One or more of the specified exclusion types are not valid.
        /// </summary>
        public const int ExclusionTypeIsInvalid = 2814;

        /// <summary>
        /// The batch limit for setting Location Exclusions against targets has been exceeded. Valid batch limit is 1.
        /// </summary>
        public const int LocationExclusionBatchLimitExceeded = 2815;

        /// <summary>
        /// The entity to associate the exclusion with cannot be null.
        /// </summary>
        public const int AssociatedEntityIsNull = 2816;

        /// <summary>
        /// The exclusion to associate the entity with cannot be null.
        /// </summary>
        public const int ExclusionIsNull = 2817;

        /// <summary>
        /// The exclusion to entity association cannot be null.
        /// </summary>
        public const int ExclusionToEntityAssociationIsNull = 2818;

        /// <summary>
        /// The collection of exclusion to entity associations cannot be null.
        /// </summary>
        public const int ExclusionToEntityAssociationCollectionIsNull = 2819;

        /// <summary>
        /// The customer is not a member of the Location Exclusion pilot program.
        /// </summary>
        public const int LocationExclusionPilotNotEnabledForCustomer = 2820;

        /// <summary>
        /// The batch limit for Excluded Geo Targets in a Location Exclusion has been exceeded. Valid batch limit is 255.
        /// </summary>
        public const int ExcludedGeoTargetsBatchLimitExceeded = 2821;

        /// <summary>
        /// The location target conflicts with location exclusion.
        /// </summary>
        public const int ConflictWithLocationExclusion = 2822;

        /// <summary>
        /// The excluded geo targets cannot have duplicate entries.
        /// </summary>
        public const int DuplicateExcludedGeoTargets = 2823;

        /// <summary>
        /// The Location exclusion parameter is invalid.
        /// </summary>
        public const int LocationExclusionIsInvalid = 2824;

        /// <summary>
        /// The excluded geo target parameter is null.
        /// </summary>
        public const int ExcludedGeoTargetIsNull = 2825;

        /// <summary>
        /// The entity type parameter is invalid.
        /// </summary>
        public const int EntityTypeIsInvalid = 2826;

        /// <summary>
        /// The list of entities exceeds the limit.
        /// </summary>
        public const int EntitiesArrayExceedsLimit = 2827;

        /// <summary>
        /// The set operation on excluded radius target is not enabled.
        /// </summary>
        public const int ExcludedRadiusTargetSetNotEnabled = 2828;

        /// <summary>
        /// The list of entities cannot be null or empty.
        /// </summary>
        public const int EntitiesArrayIsNullOrEmpty = 2829;

        /// <summary>
        /// The target conflicts with exclusion target.
        /// </summary>
        public const int ConflictWithExclusionTarget = 2830;

        /// <summary>
        /// Paging info is invalid.
        /// </summary>
        public const int PagingInfoInvalid = 3032;

        /// <summary>
        /// The text is too long.
        /// </summary>
        public const int TextTooLong = 3101;

        /// <summary>
        /// Strings array should not be null or empty.
        /// </summary>
        public const int StringsArrayShouldNotBeNullOrEmpty = 3102;

        /// <summary>
        /// The list of strings exceeds the limit.
        /// </summary>
        public const int StringsArrayExceedsLimit = 3103;

        /// <summary>
        /// The text is null.
        /// </summary>
        public const int NullText = 3104;

        /// <summary>
        /// The list of account IDs exceeds the maximum number allowed.
        /// </summary>
        public const int AccountIdsLengthExceeded = 3201;

        /// <summary>
        /// AccountIds and Campaigns arrays are empty. At least one of them should be populated.
        /// </summary>
        public const int AccountIdsCampaignsEmpty = 3202;

        /// <summary>
        /// The list of campaigns must belong to the same account.
        /// </summary>
        public const int CampaignsContainsMultipleAccounts = 3203;

        /// <summary>
        /// The list of campaigns cannot contain null items.
        /// </summary>
        public const int CampaignsContainsNullScope = 3204;

        /// <summary>
        /// The DownloadRequestId is invalid.
        /// </summary>
        public const int InvalidDownloadRequestId = 3205;

        /// <summary>
        /// The lengths of campaigns and accounts put together is exceeded.
        /// </summary>
        public const int AccountsAndCampaignsLengthExceeded = 3206;

        /// <summary>
        /// The account has more keywords than allowed per request. Please call DownloadCampaignsByCampaignIds to download the account's campaigns in multiple requests. The Details field contains the campaign identifiers under the account.
        /// </summary>
        public const int AccountTooBigToDownload = 3207;

        /// <summary>
        /// The last sync time cannot be later than now.
        /// </summary>
        public const int LastSyncTimeCannotBeInTheFuture = 3208;

        /// <summary>
        /// The last sync time cannot be earlier than 30 days
        /// </summary>
        public const int LastSyncTimeTooOld = 3209;

        /// <summary>
        /// The minimum set of required entities should be specified for bulk download.
        /// </summary>
        public const int MinimumRequiredEntitiesNotSpecified = 3210;

        /// <summary>
        /// One or more specified entities are not supported for bulk download.
        /// </summary>
        public const int BulkDownloadUnsupportedEntities = 3211;

        /// <summary>
        /// The LastSyncTimeInUTC element should be null when including performance statistics, bid suggestions, or quality score in the requested data scope.
        /// </summary>
        public const int InvalidSyncTimeForDataScopeSelected = 3213;

        /// <summary>
        /// The start and end date for performance statistics should be valid and within the supported range.
        /// </summary>
        public const int InvalidBulkCustomDateRange = 3215;

        /// <summary>
        /// The campaigns included in the download have more keywords than allowed per request. Please call DownloadCampaignsByCampaignIds with fewer campaigns.
        /// </summary>
        public const int CampaignsTooBigToDownload = 3216;

        /// <summary>
        /// The specified format version is not supported.
        /// </summary>
        public const int FormatVersionNotSupported = 3217;

        /// <summary>
        /// One or more bulk download entities are not supported with the specified format version.
        /// </summary>
        public const int EntityNotSupportedForFormatVersion = 3218;

        /// <summary>
        /// The format version is required.
        /// </summary>
        public const int FormatVersionRequired = 3219;

        /// <summary>
        /// Invalid bulk upload URL.
        /// </summary>
        public const int InvalidBulkUploadUrl = 3220;

        /// <summary>
        /// No file uploaded.
        /// </summary>
        public const int NoFileFound = 3221;

        /// <summary>
        /// More than one file uploaded.
        /// </summary>
        public const int MultipleFilesFound = 3222;

        /// <summary>
        /// Unrecognised file extension.
        /// </summary>
        public const int InvalidFileExtension = 3223;

        /// <summary>
        /// The URL has already been used for file upload.
        /// </summary>
        public const int UrlAlreadyUsedForUpload = 3224;

        /// <summary>
        /// The number of rows in the file has exceeded the maximum allowed value.
        /// </summary>
        public const int FileRowCountExceeded = 3225;

        /// <summary>
        /// The URL has expired and cannot be used for uploads.
        /// </summary>
        public const int UrlExpired = 3226;

        /// <summary>
        /// Too many requests.
        /// </summary>
        public const int TooManyRequests = 3227;

        /// <summary>
        /// The specified DataScope value is either invalid or no longer supported.
        /// </summary>
        public const int DataScopeInvalid = 3228;

        /// <summary>
        /// The entity records cannot be null or empty.
        /// </summary>
        public const int EntityRecordsArrayIsNullOrEmpty = 3229;

        /// <summary>
        /// The list of entity records exceeds the limit.
        /// </summary>
        public const int EntityRecordsArrayLimitExceeded = 3230;

        /// <summary>
        /// The Goal Id is invalid.
        /// </summary>
        public const int InvalidGoalId = 3300;

        /// <summary>
        /// Duplicate IDs are contained in the array of goals.
        /// </summary>
        public const int DuplicateInGoalIds = 3301;

        /// <summary>
        /// Goals array should not be null or empty.
        /// </summary>
        public const int GoalsArrayShouldNotBeNullOrEmpty = 3302;

        /// <summary>
        /// The list of goals exceeds the limit.
        /// </summary>
        public const int GoalsArrayExceedsLimit = 3303;

        /// <summary>
        /// Goal Ids array should not be null or empty.
        /// </summary>
        public const int GoalIdsArrayShouldNotBeNullOrEmpty = 3304;

        /// <summary>
        /// The list of account Ids exceeds the limit.
        /// </summary>
        public const int AccountIdsArrayExceedsLimit = 3305;

        /// <summary>
        /// Account Ids array should not be null or empty.
        /// </summary>
        public const int AccountIdsArrayShouldNotBeNullOrEmpty = 3306;

        /// <summary>
        /// The list of AccountAnalyticsTypes exceeds the limit.
        /// </summary>
        public const int AccountAnalyticsTypesArrayExceedsLimit = 3307;

        /// <summary>
        /// AccountAnalyticsTypes array should not be null or empty.
        /// </summary>
        public const int AccountAnalyticsTypesArrayShouldNotBeNullOrEmpty = 3308;

        /// <summary>
        /// Goal passed is null.
        /// </summary>
        public const int GoalNotPassed = 3309;

        /// <summary>
        /// Goal name is required.
        /// </summary>
        public const int GoalNameMissing = 3310;

        /// <summary>
        /// Goal name too long.
        /// </summary>
        public const int GoalNameTooLong = 3311;

        /// <summary>
        /// Duplicate goal name.
        /// </summary>
        public const int GoalNameAlreadyExists = 3312;

        /// <summary>
        /// Goal name has invalid characters.
        /// </summary>
        public const int GoalNameHasInvalidChars = 3313;

        /// <summary>
        /// DaysApplicableForConversion is required.
        /// </summary>
        public const int DaysApplicableForConversionIsRequired = 3314;

        /// <summary>
        /// Goal Id is required.
        /// </summary>
        public const int GoalIdIsRequired = 3315;

        /// <summary>
        /// Goal Id must be null.
        /// </summary>
        public const int GoalIdMustBeNull = 3316;

        /// <summary>
        /// Duplicate goal Id.
        /// </summary>
        public const int GoalIdAlreadyExists = 3317;

        /// <summary>
        /// Cost model cannot be combined with none.
        /// </summary>
        public const int GoalCostModelCannotbeCombinedWithNone = 3318;

        /// <summary>
        /// Amount is required for constant revenue model.
        /// </summary>
        public const int GoalRevenueAmountRequiredForConstantModel = 3319;

        /// <summary>
        /// Amount must be null for variable revenue model.
        /// </summary>
        public const int GoalRevenueAmountMustBeNullForVariableModel = 3320;

        /// <summary>
        /// Amount must be null for null revenue model.
        /// </summary>
        public const int GoalRevenueAmountMustBeNullForNullModel = 3321;

        /// <summary>
        /// Amount is valid only for constant revenue model.
        /// </summary>
        public const int GoalRevenueAmountValidOnlyForConstantModel = 3322;

        /// <summary>
        /// Revenue amount is less than minimum allowed.
        /// </summary>
        public const int GoalRevenueAmountLessThanMinimum = 3323;

        /// <summary>
        /// Revenue amount is more than maximum allowed.
        /// </summary>
        public const int GoalRevenueAmountMoreThanMax = 3324;

        /// <summary>
        /// Revenue model is required.
        /// </summary>
        public const int GoalRevenueModelIsRequired = 3325;

        /// <summary>
        /// Cost model is required.
        /// </summary>
        public const int GoalCostModelIsRequired = 3326;

        /// <summary>
        /// Limit of goals for account has been exceeded.
        /// </summary>
        public const int MaxGoalsLimitExceededForAccount = 3327;

        /// <summary>
        /// Step passed is null.
        /// </summary>
        public const int StepNotPassed = 3328;

        /// <summary>
        /// Step name is required.
        /// </summary>
        public const int StepNameMissing = 3329;

        /// <summary>
        /// Goal name too long.
        /// </summary>
        public const int StepNameTooLong = 3330;

        /// <summary>
        /// Duplicate goal name.
        /// </summary>
        public const int StepNameAlreadyExists = 3331;

        /// <summary>
        /// Goal name has invalid characters.
        /// </summary>
        public const int StepNameHasInvalidChars = 3332;

        /// <summary>
        /// Goal Id is required.
        /// </summary>
        public const int StepIdIsRequired = 3333;

        /// <summary>
        /// Goal Id must be null.
        /// </summary>
        public const int StepIdMustBeNull = 3334;

        /// <summary>
        /// Duplicate goal Id.
        /// </summary>
        public const int StepIdAlreadyExists = 3335;

        /// <summary>
        /// Step type is required.
        /// </summary>
        public const int StepTypeIsRequired = 3336;

        /// <summary>
        /// Step type must be null.
        /// </summary>
        public const int StepTypeMustBeNull = 3337;

        /// <summary>
        /// Position number is required.
        /// </summary>
        public const int PositionNumberIsRequired = 3338;

        /// <summary>
        /// Position number must be null.
        /// </summary>
        public const int PositionNumberMustBeNull = 3339;

        /// <summary>
        /// Position number must be unique.
        /// </summary>
        public const int PositionNumberAlreadyExists = 3340;

        /// <summary>
        /// Limit of steps per goal has been exceeded.
        /// </summary>
        public const int MaxStepsLimitExceededForGoal = 3341;

        /// <summary>
        /// Atleast one step is required for a goal.
        /// </summary>
        public const int AtleaseOneStepRequiredForGoal = 3342;

        /// <summary>
        /// Atleast one conversion step is required for a goal.
        /// </summary>
        public const int AtleaseOneConversionStepRequiredForGoal = 3343;

        /// <summary>
        /// Only one lead step is allowed for a goal.
        /// </summary>
        public const int OnlyOneLeadStepAllowedForGoal = 3344;

        /// <summary>
        /// Only one lead step is allowed for a goal.
        /// </summary>
        public const int OnlyOneConversionStepAllowedForGoal = 3345;

        /// <summary>
        /// Analytics settings deprecated at campaign level.
        /// </summary>
        public const int AnalyticsSettingCampaignLevelDeprecated = 3346;

        /// <summary>
        /// The Goal Category is invalid.
        /// </summary>
        public const int InvalidGoalCategory = 3347;

        /// <summary>
        /// The regular expression is invalid.
        /// </summary>
        public const int InvalidRegularExpression = 3350;

        /// <summary>
        /// Customer not eligible for Goal Category.
        /// </summary>
        public const int CustomerNotEligibleForGoalCategory = 3348;

        /// <summary>
        /// The requested category is not valid for this goal type.
        /// </summary>
        public const int InvalidCategoryForGoalType  = 3349;

        /// <summary>
        /// This language is not supported.
        /// </summary>
        public const int LanguageNotSupported = 3400;

        /// <summary>
        /// The maximum keywords requested is more than the supported maximum.
        /// </summary>
        public const int MaxKeywordsRequestedMoreThanSupportNumber = 3401;

        /// <summary>
        /// The maximum keywords requested is invalid.
        /// </summary>
        public const int InvalidMaxKeywords = 3402;

        /// <summary>
        /// The minimum confidence score is invalid.
        /// </summary>
        public const int InvalidMinConfidenceScore = 3403;

        /// <summary>
        /// The start date is invalid.
        /// </summary>
        public const int InvalidStartDate = 3404;

        /// <summary>
        /// The end date is invalid.
        /// </summary>
        public const int InvalidEndDate = 3405;

        /// <summary>
        /// The start date is later than the end date.
        /// </summary>
        public const int StartDateGreaterThanEndDate = 3406;

        /// <summary>
        /// The maximum suggestions per keyword is invalid.
        /// </summary>
        public const int InvalidMaxSuggestionsPerKeyword = 3407;

        /// <summary>
        /// The langauge and publisher country/region combination is invalid.
        /// </summary>
        public const int LanguageAndCountryNotSupported = 3408;

        /// <summary>
        /// The matchTypes is invalid.
        /// </summary>
        public const int InvalidMatchTypes = 3409;

        /// <summary>
        /// The publisher country/region array specified exceeds limit.
        /// </summary>
        public const int PublisherCountryArrayExceedsLimit = 3410;

        /// <summary>
        /// The date provided is invalid.
        /// </summary>
        public const int InvalidDate = 3411;

        /// <summary>
        /// The language provided is invalid.
        /// </summary>
        public const int InvalidLanguage = 3412;

        /// <summary>
        /// The publisher country/region provided is invalid.
        /// </summary>
        public const int InvalidPublisherCountry = 3413;

        /// <summary>
        /// The customer is not a member of the bulk download pilot program.
        /// </summary>
        public const int BulkApiNotEnabledForPilot = 3500;

        /// <summary>
        /// The customer is not a member of the bulk upload program.
        /// </summary>
        public const int BulkUploadFeatureNotEnabledForCustomer = 3501;

        /// <summary>
        /// The list of account IDs exceeds the maximum number of IDs allowed. The Details field contains the maximum number of IDs.
        /// </summary>
        public const int AccountIdsBatchLimitExceeded = 3600;

        /// <summary>
        /// The list of account IDs contains duplicates. Please ensure that the list contains only unique account IDs.
        /// </summary>
        public const int DuplicateInAccountIds = 3601;

        /// <summary>
        /// The migration filter value is not valid.
        /// </summary>
        public const int InvalidMigrationTypeFilter = 3602;

        /// <summary>
        /// A full sync on the account is required.
        /// </summary>
        public const int FullSyncRequired = 3603;

        /// <summary>
        /// AccountPropertyName is invalid.
        /// </summary>
        public const int AccountPropertyNameInvalid = 3604;

        /// <summary>
        /// The value of MSCLKIDAutoTaggingEnabled is invalid.
        /// </summary>
        public const int MSCLKIDAutoTaggingEnabledValueInvalid = 3605;

        /// <summary>
        /// AccountProperties cannot be null or empty.
        /// </summary>
        public const int AccountPropertiesNullOrEmpty = 3606;

        /// <summary>
        /// AccountProperty is null.
        /// </summary>
        public const int AccountPropertyIsNull = 3607;

        /// <summary>
        /// AccountPropertyNames cannot be null or empty.
        /// </summary>
        public const int AccountPropertyNamesNullOrEmpty = 3608;

        /// <summary>
        /// The list of AccountProperty contains duplicate AccountPropertyName.
        /// </summary>
        public const int DuplicateAccountPropertyName = 3609;

        /// <summary>
        /// The account property cannot be changed.
        /// </summary>
        public const int CannotUpdateAccountProperty = 3610;

        /// <summary>
        /// The specified status value is not allowed for the operation being performed.
        /// </summary>
        public const int InvalidAdExtensionStatus = 3800;

        /// <summary>
        /// The type of ad extension is not valid.
        /// </summary>
        public const int InvalidAdExtensionType = 3801;

        /// <summary>
        /// The list of site links cannot be null or empty.
        /// </summary>
        public const int AdExtensionSiteLinkArrayIsNullOrEmpty = 3802;

        /// <summary>
        /// The list of site links cannot contain a null site link. The Details element contains the index of the site link.
        /// </summary>
        public const int AdExtensionSiteLinkIsNull = 3803;

        /// <summary>
        /// The site link's destination URL cannot be null or empty. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDestinationUrlNullOrEmpty = 3804;

        /// <summary>
        /// The site link's display text cannot be null or empty. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDisplayTextNullOrEmpty = 3805;

        /// <summary>
        /// The site link's destination URL is too long. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDestinationUrlTooLong = 3806;

        /// <summary>
        /// The site link's display text is too long. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDisplayTextTooLong = 3807;

        /// <summary>
        /// Adding the ad extensions to the account will exceed the maximum number of ad extensions that you can add to the account.
        /// </summary>
        public const int TooManyAdExtensionsPerAccount = 3808;

        /// <summary>
        /// The ad extension contains too many site links.
        /// </summary>
        public const int TooManySiteLinks = 3809;

        /// <summary>
        /// The list of ad extension to campaign associations cannot be null or empty.
        /// </summary>
        public const int AdExtensionIdToCampaignIdAssociationArrayShouldNotBeNullOrEmpty = 3810;

        /// <summary>
        /// The list of ad extension to campaign associations is too long.
        /// </summary>
        public const int AdExtensionIdToCampaignIdAssociationArrayLimitExceeded = 3811;

        /// <summary>
        /// The specified ad extension ID is not valid.
        /// </summary>
        public const int InvalidAdExtensionId = 3812;

        /// <summary>
        /// The ad extension Id to campaign Id association provided is not valid.
        /// </summary>
        public const int InvalidAdExtensionIdToCampaignIdAssociation = 3813;

        /// <summary>
        /// The specified type filter is not valid.
        /// </summary>
        public const int InvalidAdExtensionTypeFilter = 3814;

        /// <summary>
        /// The list of ad extension IDs cannot be null or empty.
        /// </summary>
        public const int AdExtensionIdsArrayShouldNotBeNullOrEmpty = 3815;

        /// <summary>
        /// The list of ad extension IDs is too long.
        /// </summary>
        public const int AdExtensionIdsArrayExceedsLimit = 3816;

        /// <summary>
        /// The list of ad extension IDs contains duplicates.
        /// </summary>
        public const int DuplicateInAdExtensionIds = 3817;

        /// <summary>
        /// The entity to which the ad extension is assigned to already has an adextension of the same type. Please select a different ad extension type.
        /// </summary>
        public const int CannotAssignMoreThanOneAdExtensionTypeToAnEntity = 3818;

        /// <summary>
        /// The Ad extension Id array is null or has no elements.
        /// </summary>
        public const int AdExtensionIdsArrayIsNullOrEmpty = 3819;

        /// <summary>
        /// The campaign Id to Ad extension Id association is null.
        /// </summary>
        public const int AdExtensionIdToCampaignIdAssociationNotPassed = 3820;

        /// <summary>
        /// The array of campaign Id to Ad extension Id association is null or has no elements.
        /// </summary>
        public const int AdExtensionIdToCampaignIdAssociationsNotPassed = 3821;

        /// <summary>
        /// The customer is not part of the SiteLink AdExtension pilot program.
        /// </summary>
        public const int SiteLinkAdExtensionPilotNotEnabledForCustomer = 3822;

        /// <summary>
        /// The site link's destination URL has invalid characters. The Details field contains the index of the site link element which has the error.
        /// </summary>
        public const int SiteLinkDestinationUrlInvalid = 3823;

        /// <summary>
        /// The Campaign Id array has too many elements.
        /// </summary>
        public const int TooManyCampaignIds = 3824;

        /// <summary>
        /// The list of ad extension to campaign associations contains duplicates.
        /// </summary>
        public const int DuplicateInAdExtensionIdToCampaignIdAssociations = 3825;

        /// <summary>
        /// Version is read-only and must be null.
        /// </summary>
        public const int AdExtensionVersionCannotBeSet = 3826;

        /// <summary>
        /// The list of site links contains duplicates.
        /// </summary>
        public const int DuplicateSiteLinksInAdExtension = 3827;

        /// <summary>
        /// The address cannot be null.
        /// </summary>
        public const int AdExtensionAddressIsNull = 3828;

        /// <summary>
        /// The street address cannot be null or empty.
        /// </summary>
        public const int AdExtensionStreetAddressNullOrEmpty = 3829;

        /// <summary>
        /// The street address is too long.
        /// </summary>
        public const int AdExtensionStreetAddressTooLong = 3830;

        /// <summary>
        /// The street address contains invalid characters.
        /// </summary>
        public const int AdExtensionStreetAddressInvalid = 3831;

        /// <summary>
        /// The street address2 is too long.
        /// </summary>
        public const int AdExtensionStreetAddress2TooLong = 3832;

        /// <summary>
        /// The street address2 contains invalid characters.
        /// </summary>
        public const int AdExtensionStreetAddress2Invalid = 3833;

        /// <summary>
        /// The city name cannot be null or empty.
        /// </summary>
        public const int AdExtensionCityNameNullOrEmpty = 3834;

        /// <summary>
        /// The city name is too long.
        /// </summary>
        public const int AdExtensionCityNameTooLong = 3835;

        /// <summary>
        /// The city name contains invalid characters.
        /// </summary>
        public const int AdExtensionCityNameInvalid = 3836;

        /// <summary>
        /// The province name is too long.
        /// </summary>
        public const int AdExtensionProvinceNameTooLong = 3837;

        /// <summary>
        /// The province name contains invalid characters.
        /// </summary>
        public const int AdExtensionProvinceNameInvalid = 3838;

        /// <summary>
        /// The postal code cannot be null or empty.
        /// </summary>
        public const int AdExtensionPostalCodeNullOrEmpty = 3839;

        /// <summary>
        /// The postal code is too long.
        /// </summary>
        public const int AdExtensionPostalCodeTooLong = 3840;

        /// <summary>
        /// The postal code contains invalid characters.
        /// </summary>
        public const int AdExtensionPostalCodeInvalid = 3841;

        /// <summary>
        /// The country/region code cannot be null or empty.
        /// </summary>
        public const int AdExtensionCountryCodeNullOrEmpty = 3842;

        /// <summary>
        /// The country/region code has wrong length.
        /// </summary>
        public const int AdExtensionCountryCodeWrongLength = 3843;

        /// <summary>
        /// The country/region code is not valid.
        /// </summary>
        public const int AdExtensionCountryCodeInvalid = 3844;

        /// <summary>
        /// The GeoPoint element must be null.
        /// </summary>
        public const int AdExtensionGeoPointIsNotNull = 3845;

        /// <summary>
        /// The GeoCodeStatus element must be null.
        /// </summary>
        public const int AdExtensionGeoCodeStatusIsNotNull = 3846;

        /// <summary>
        /// The company name cannot be null or empty.
        /// </summary>
        public const int AdExtensionCompanyNameNullOrEmpty = 3847;

        /// <summary>
        /// The company name is too long.
        /// </summary>
        public const int AdExtensionCompanyNameTooLong = 3848;

        /// <summary>
        /// The company name contains invalid characters.
        /// </summary>
        public const int AdExtensionCompanyNameInvalid = 3849;

        /// <summary>
        /// The phone number is too long.
        /// </summary>
        public const int AdExtensionPhoneNumberTooLong = 3850;

        /// <summary>
        /// The phone number is invalid.
        /// </summary>
        public const int AdExtensionPhoneNumberInvalid = 3851;

        /// <summary>
        /// The ad extension icon media ID is invalid.
        /// </summary>
        public const int AdExtensionIconMediaIdInvalid = 3852;

        /// <summary>
        /// The ad extension icon is too large.
        /// </summary>
        public const int AdExtensionIconTooLarge = 3853;

        /// <summary>
        /// The ad extension image media ID is invalid.
        /// </summary>
        public const int AdExtensionImageMediaIdInvalid = 3854;

        /// <summary>
        /// The ad extension image is too large.
        /// </summary>
        public const int AdExtensionImageTooLarge = 3855;

        /// <summary>
        /// The customer is not a member of the Location Ad Extension v2 pilot program.
        /// </summary>
        public const int LocationAdExtensionPilotNotEnabledForCustomer = 3856;

        /// <summary>
        /// The customer is not a member of the Call Ad Extension v2 pilot program.
        /// </summary>
        public const int CallAdExtensionPilotNotEnabledForCustomer = 3857;

        /// <summary>
        /// The province code is too long.
        /// </summary>
        public const int AdExtensionProvinceCodeTooLong = 3858;

        /// <summary>
        /// The province code contains invalid characters.
        /// </summary>
        public const int AdExtensionProvinceCodeInvalid = 3859;

        /// <summary>
        /// The province code is required if the province name is empty (for the specified country/region).
        /// </summary>
        public const int AdExtensionProvinceCodeRequiredIfProvinceNameEmpty = 3860;

        /// <summary>
        /// The phone number cannot be null or empty.
        /// </summary>
        public const int AdExtensionPhoneNumberNullOrEmpty = 3861;

        /// <summary>
        /// The number of location extensions in the ad extensions list, exceeds the maximum allowed.
        /// </summary>
        public const int LocationAdExtensionsEntityLimitExceeded = 3862;

        /// <summary>
        /// The customer is not part of the Product Listing Ad program.
        /// </summary>
        public const int ProductListingAdPilotNotEnabledForCustomer = 3863;

        /// <summary>
        /// The list of product condition collections is too long.
        /// </summary>
        public const int ProductAdExtensionTooManyProductConditionCollections = 3864;

        /// <summary>
        /// The list of conditions in the product condition collection is too long.
        /// </summary>
        public const int ProductAdExtensionTooManyConditions = 3865;

        /// <summary>
        /// The store ID is not valid.
        /// </summary>
        public const int ProductAdExtensionInvalidStoreId = 3866;

        /// <summary>
        /// The list of conditions in the production condition collection cannot be null or empty.
        /// </summary>
        public const int ProductAdExtensionProductConditionsArrayIsNullOrEmpty = 3867;

        /// <summary>
        /// The product condition cannot be null.
        /// </summary>
        public const int ProductAdExtensionProductConditionIsNull = 3868;

        /// <summary>
        /// The product condition's operand is not valid.
        /// </summary>
        public const int ProductAdExtensionOperandIsInvalid = 3869;

        /// <summary>
        /// The product condition's attribute is not valid.
        /// </summary>
        public const int ProductAdExtensionAttributeIsInvalid = 3870;

        /// <summary>
        /// The product condition's attribute is too long.
        /// </summary>
        public const int ProductConditionAttributeTooLong = 3871;

        /// <summary>
        /// The product condition's operand is duplicate.
        /// </summary>
        public const int ProductConditionDuplicateOperand = 3872;

        /// <summary>
        /// Store Name is read-only and cannot be set.
        /// </summary>
        public const int ProductAdExtensionStoreNameCannotBeSet = 3873;

        /// <summary>
        /// Existing ad extension has different type that one passed for the same ID.
        /// </summary>
        public const int AdExtensionTypeMismatch = 3874;

        /// <summary>
        /// RequireTollFreeTrackingNumber must be null when call tracking is not enabled
        /// </summary>
        public const int CallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled = 3875;

        /// <summary>
        /// The customer is not a member of the Call Tracking pilot program.
        /// </summary>
        public const int CallTrackingNotEnabledForCustomer  = 3876;

        /// <summary>
        /// Call tracking is not supported for selected country/region.
        /// </summary>
        public const int CallAdExtensionCallTrackingNotSupportedForCountry = 3877;

        /// <summary>
        /// The geo point coordinates are invalid.
        /// </summary>
        public const int AdExtensionGeoPointInvalid = 3878;

        /// <summary>
        /// The name of Product Extension is too long.
        /// </summary>
        public const int ProductAdExtensionNameTooLong = 3879;

        /// <summary>
        /// The name of Product Extension is invalid.
        /// </summary>
        public const int ProductAdExtensionNameInvalid = 3880;

        /// <summary>
        /// The list of product targets contains duplicates.
        /// </summary>
        public const int DuplicateProductTarget = 3881;

        /// <summary>
        /// The product condition collection cannot be null.
        /// </summary>
        public const int ProductAdExtensionProductConditionCollectionIsNull = 3882;

        /// <summary>
        /// The specified association type is not valid.
        /// </summary>
        public const int InvalidAssociationType = 3883;

        /// <summary>
        /// The list of ad extension to ad group associations cannot be null or empty.
        /// </summary>
        public const int AdExtensionIdToAdGroupIdAssociationArrayShouldNotBeNullOrEmpty = 3884;

        /// <summary>
        /// The list of ad extension to ad group associations is too long.
        /// </summary>
        public const int AdExtensionIdToAdGroupIdAssociationArrayLimitExceeded = 3885;

        /// <summary>
        /// The ad extension Id to ad group Id association provided is not valid.
        /// </summary>
        public const int InvalidAdExtensionIdToAdGroupIdAssociation = 3886;

        /// <summary>
        /// The list of ad extension to ad group associations contains duplicates.
        /// </summary>
        public const int DuplicateInAdExtensionIdToAdGroupIdAssociations = 3887;

        /// <summary>
        /// Ad extension type not allowed for ad group association.
        /// </summary>
        public const int AdExtensionTypeNotAllowedForAdGroupAssociation = 3888;

        /// <summary>
        /// Duplicate product filter provided.
        /// </summary>
        public const int ProductAdExtensionDuplicateProductFilter = 3889;

        /// <summary>
        /// Duplicate product condition provided.
        /// </summary>
        public const int ProductAdExtensionDuplicateProductCondition = 3890;

        /// <summary>
        /// Product ad extension cannot have all and specific products specified.
        /// </summary>
        public const int ProductAdExtensionCannotHaveBothAllAndSpecificProducts = 3891;

        /// <summary>
        /// Multiple product ad extensions cannot be assigned to a single entity.
        /// </summary>
        public const int MultipleProductAdExtensionCannotBeAssignedToSingleEntity = 3892;

        /// <summary>
        /// Product Selection is null.
        /// </summary>
        public const int ExtensionProductSelectionIsNull = 3893;

        /// <summary>
        /// Product Target destination URL is too long.
        /// </summary>
        public const int ProductTargetDestinationUrlTooLong = 3894;

        /// <summary>
        /// Product Value is too long.
        /// </summary>
        public const int ProductValueTooLong = 3895;

        /// <summary>
        /// Param1 in product target is invalid.
        /// </summary>
        public const int InvalidProductTargetParam1 = 3896;

        /// <summary>
        /// Param2 in product target is invalid.
        /// </summary>
        public const int InvalidProductTargetParam2 = 3897;

        /// <summary>
        /// Param3 in product target is invalid.
        /// </summary>
        public const int InvalidProductTargetParam3 = 3898;

        /// <summary>
        /// The list of ad extension to account associations cannot be null or empty.
        /// </summary>
        public const int AdExtensionIdToAccountIdAssociationArrayShouldNotBeNullOrEmpty = 3899;

        /// <summary>
        /// The list of ad extension to account associations is too long.
        /// </summary>
        public const int AdExtensionIdToAccountIdAssociationArrayLimitExceeded = 3900;

        /// <summary>
        /// The ad extension Id to account Id association provided is not valid.
        /// </summary>
        public const int InvalidAdExtensionIdToAccountIdAssociation = 3901;

        /// <summary>
        /// The list of ad extension to account associations contains duplicates.
        /// </summary>
        public const int DuplicateInAdExtensionIdToAccountIdAssociations = 3902;

        /// <summary>
        /// Ad extension type not allowed for account association.
        /// </summary>
        public const int AdExtensionTypeNotAllowedForAccountAssociation = 3903;

        /// <summary>
        /// DevicePreference is incompatible with this ad extension type.
        /// </summary>
        public const int DevicePreferenceIncompatibleWithAdExtensionType = 3904;

        /// <summary>
        /// The account Id to Ad extension Id association is null
        /// </summary>
        public const int AdExtensionIdToAccountIdAssociationNotPassed = 3905;

        /// <summary>
        /// Entity id shoud be same with account id in Ad extension account association
        /// </summary>
        public const int AdExtensionAccountAssociationEntityIdNotEqualToAccountId = 3906;

        /// <summary>
        /// There should be only one entity id in Ad extension account association
        /// </summary>
        public const int AdExtensionAccountAssociationShouldHaveOneEntityId = 3907;

        /// <summary>
        /// The customer is not part of the AdExtension Scheduling pilot program.
        /// </summary>
        public const int AdExtensionSchedulingPilotNotEnabledForCustomer = 3930;

        /// <summary>
        /// Start date cannot be earlier than today.
        /// </summary>
        public const int AdExtensionScheduleInvalidStartTime = 3931;

        /// <summary>
        /// The End time cannot be earlier than today
        /// </summary>
        public const int AdExtensionScheduleInvalidEndTime = 3932;

        /// <summary>
        /// The schedule day and time is invalid. For example, the end date cannot be earlier than the start date.
        /// </summary>
        public const int InvalidScheduleDayTimeRange = 3933;

        /// <summary>
        /// Max of 6 time intervals per day.
        /// </summary>
        public const int ScheduleDayTimeRangesDayBatchLimitExceeded = 3934;

        /// <summary>
        /// Ad schedules should be non-overlapping
        /// </summary>
        public const int ScheduleDayTimeRangesOverlapping = 3935;

        /// <summary>
        /// interval should be greater than zero
        /// </summary>
        public const int InvalidScheduleDayTimeRangeInterval = 3936;

        /// <summary>
        /// UseSearcherTimeZone should be null when startDate and endDate and DayTimeRanges are null
        /// </summary>
        public const int AdExtensionScheduleInvalidUseSearcherTimeZone = 3937;

        /// <summary>
        /// This AdExtension type don't support AdExtension Level Schedule
        /// </summary>
        public const int AdExtensionTypeNotSupportAdExtensionLevelSchedule = 3938;

        /// <summary>
        /// This AdExtension type don't support AdExtension Schedule
        /// </summary>
        public const int AdExtensionTypeNotSupportSchedule = 3939;

        /// <summary>
        /// Schedule days not in date range
        /// </summary>
        public const int ScheduleDaysNotInDateRange = 3940;

        /// <summary>
        /// You must specify both sitelink description elements or do not specify either. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDescriptionAllOrNoneRequired = 3948;

        /// <summary>
        /// The customer is not part of the Enhanced SiteLink AdExtension pilot program.
        /// </summary>
        public const int EnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer = 3949;

        /// <summary>
        /// The site link's Description1 is too long. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDescription1TooLong = 3950;

        /// <summary>
        /// The site link's Description2 is too long. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDescription2TooLong = 3951;

        /// <summary>
        /// The site link's Description1 contains invalid characters. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDescription1Invalid = 3952;

        /// <summary>
        /// The site link's Description2 contains invalid characters. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDescription2Invalid = 3953;

        /// <summary>
        /// The site link's DevicePreference value is invalid. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDevicePreferenceInvalid = 3954;

        /// <summary>
        /// The site link's display text contains invalid characters. The Details element contains the index of the site link.
        /// </summary>
        public const int SiteLinkDisplayTextInvalid = 3955;

        /// <summary>
        /// The customer is not part of the Image AdExtension pilot program.
        /// </summary>
        public const int ImageAdExtensionPilotNotEnabledForCustomer = 3956;

        /// <summary>
        /// The customer is not part of the Campaign level Image AdExtension pilot program.
        /// </summary>
        public const int CampaignImageAdExtensionPilotNotEnabledForCustomer = 3957;

        /// <summary>
        /// The image ad extension's alternative text cannot be null or empty.
        /// </summary>
        public const int ImageAdExtensionAlternativeTextNullOrEmpty = 3960;

        /// <summary>
        /// The image ad extension's alternative text contains invalid characters.
        /// </summary>
        public const int ImageAdExtensionAlternativeTextInvalid = 3961;

        /// <summary>
        /// The image ad extension's alternative text is too long.
        /// </summary>
        public const int ImageAdExtensionAlternativeTextTooLong = 3962;

        /// <summary>
        /// The image ad extension's destination URL contains invalid characters.
        /// </summary>
        public const int ImageAdExtensionDestinationUrlInvalid = 3963;

        /// <summary>
        /// The image ad extension's destination URL is too long.
        /// </summary>
        public const int ImageAdExtensionDestinationUrlTooLong = 3964;

        /// <summary>
        /// The image ad extension's media id is invalid.
        /// </summary>
        public const int ImageAdExtensionImageMediaIdInvalid = 3966;

        /// <summary>
        /// The maximum number of campaigns or ad groups are already associated with ad extensions of this type. Please refer to documentation for entity association limits.
        /// </summary>
        public const int AdExtensionAssociationsLimitExceededPerEntityType = 3967;

        /// <summary>
        /// The Account or campaign or ad group cannot be associated with an additional ad extension of this type. Please refer to documentation for entity and ad extension association limits.
        /// </summary>
        public const int AssociationsLimitExceededPerAdExtensionType = 3968;

        /// <summary>
        /// The customer is not part of the App AdExtension pilot program.
        /// </summary>
        public const int AppAdExtensionPilotNotEnabledForCustomer = 3969;

        /// <summary>
        /// The app platform cannot be null or empty. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionAppPlatformNullOrEmpty = 3970;

        /// <summary>
        /// The app platform is invalid. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionInvalidAppPlatform = 3971;

        /// <summary>
        /// The app network ad extension's app store ID cannot be null or empty. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionAppStoreIdNullOrEmpty = 3972;

        /// <summary>
        /// The app ad extension's app store ID is too long. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionAppStoreIdTooLong = 3973;

        /// <summary>
        /// The app ad extension's app store ID is invalid. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionAppStoreIdInvalid = 3974;

        /// <summary>
        /// The app network ad extension's display text cannot be null or empty. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDisplayTextNullOrEmpty = 3975;

        /// <summary>
        /// The app ad extension's display text is too long. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDisplayTextTooLong = 3976;

        /// <summary>
        /// The app ad extension's display text is invalid. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDisplayTextInvalid = 3977;

        /// <summary>
        /// The app network ad extension's destination URL cannot be null or empty. The Details element contains the index of the ad extension.
        /// </summary>
        public const int AppAdExtensionDestinationUrlNullOrEmpty = 3978;

        /// <summary>
        /// The app ad extension's URL is too long. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDestinationUrlTooLong = 3979;

        /// <summary>
        /// The app or measurement URL is incorrect, or doesn't correspond to the specified app ID or package name. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDestinationUrlInvalid = 3980;

        /// <summary>
        /// The app ad extension device preference is invalid. The Details element contains the index of the app ad extension.
        /// </summary>
        public const int AppAdExtensionDevicePreferenceInvalid = 3981;

        /// <summary>
        /// The customer is not part of the Application Installation Tracking pilot program.
        /// </summary>
        public const int AppInstallTrackingPilotNotEnabledForCustomer = 3982;

        /// <summary>
        /// The image ad extension's description is invalid.
        /// </summary>
        public const int ImageAdExtensionDescriptionInvalid = 3990;

        /// <summary>
        /// The image ad extension's description is too long.
        /// </summary>
        public const int ImageAdExtensionDescriptionTooLong = 3991;

        /// <summary>
        /// The image ad extension's ImageMediaIds contain too many images.
        /// </summary>
        public const int ImageAdExtensionTooManyImages = 3992;

        /// <summary>
        /// The image ad extension's ImageMediaIds is NULL or empty.
        /// </summary>
        public const int ImageAdExtensionImageMediaIdsNullOrEmpty = 3993;

        /// <summary>
        /// The image ad extension's ImageAltText is invalid.
        /// </summary>
        public const int ImageAdExtensionImageAltTextInvalid = 3994;

        /// <summary>
        /// The image ad extension's ImageAltText is too long.
        /// </summary>
        public const int ImageAdExtensionImageAltTextTooLong = 3995;

        /// <summary>
        /// An image or icon with the specified ID was not found in the account's media library.
        /// </summary>
        public const int MediaIdInvalid = 4000;

        /// <summary>
        /// The image data is not valid. The string must be base64 and cannot be empty or null.
        /// </summary>
        public const int ImageDataInvalid = 4001;

        /// <summary>
        /// The image type is not valid.
        /// </summary>
        public const int ImageTypeEnumInvalid = 4002;

        /// <summary>
        /// The image's MIME type is not supported.
        /// </summary>
        public const int ImageMimeTypeInvalid = 4003;

        /// <summary>
        /// The width and height of the image is greater than the maximum allowed.
        /// </summary>
        public const int ImageTooLarge = 4004;

        /// <summary>
        /// The image's binary size (in megabytes) is greater than the maximum allowed.
        /// </summary>
        public const int ImageOverweight = 4005;

        /// <summary>
        /// Animated images are not allowed.
        /// </summary>
        public const int AnimatedImageNotAllowed = 4006;

        /// <summary>
        /// The type of media is not valid.
        /// </summary>
        public const int MediaTypeInvalid = 4007;

        /// <summary>
        /// The list of media IDs cannot be null or empty.
        /// </summary>
        public const int MediaIdsArrayIsNullOrEmpty = 4008;

        /// <summary>
        /// The list of media IDs exceeds the limit.
        /// </summary>
        public const int MediaIdsLimitExceeded = 4009;

        /// <summary>
        /// The list of media IDs contains duplicates.
        /// </summary>
        public const int DuplicateInMediaIds = 4010;

        /// <summary>
        /// The list of media cannot be null or empty.
        /// </summary>
        public const int MediaEntityArrayIsNullOrEmpty = 4011;

        /// <summary>
        /// The list of media exceeds the limit.
        /// </summary>
        public const int MediaEntityLimitExceeded = 4012;

        /// <summary>
        /// One of the media entities is null.
        /// </summary>
        public const int MediaEntityIsNull = 4013;

        /// <summary>
        /// Data in one of the media entities is null.
        /// </summary>
        public const int MediaEntityDataIsNull = 4014;

        /// <summary>
        /// Data in one or more the media is not a supported format.
        /// </summary>
        public const int MediaEntityDataInvalid = 4015;

        /// <summary>
        /// The specified media enabled entities filter is invalid.
        /// </summary>
        public const int MediaEnabledEntitiesInvalid = 4016;

        /// <summary>
        /// The specified media is not supported by this operation.
        /// </summary>
        public const int MediaTypeNotSupportedByOperation = 4017;

        /// <summary>
        /// Data in one of the media entities is not valid format.
        /// </summary>
        public const int MediaEntityDataEncodingInvalid = 4018;

        /// <summary>
        /// Data in one or more media has invalid Width.
        /// </summary>
        public const int MediaEntityDataWidthInvalid = 4019;

        /// <summary>
        /// Data in one or more media has invalid Height.
        /// </summary>
        public const int MediaEntityDataHeightInvalid = 4020;

        /// <summary>
        /// Data in one or more media has invalid aspect ratio.
        /// </summary>
        public const int MediaEntityDataAspectRatioInvalid = 4021;

        /// <summary>
        /// The media you attempted to add is not represented by a supported data format. Please refer to documentation for the supported media formats.
        /// </summary>
        public const int MediaFormatNotSupported = 4022;

        /// <summary>
        /// The media is not in the media library for the specified account.
        /// </summary>
        public const int MediaNotFoundInAccount = 4023;

        /// <summary>
        /// The media you attempted to delete is still associated with one or more entities.
        /// </summary>
        public const int MediaIsAssociated = 4024;

        /// <summary>
        /// Image is invalid.
        /// </summary>
        public const int InvalidImage = 4025;

        /// <summary>
        /// Image is duplicate.
        /// </summary>
        public const int DuplicateImage = 4026;

        /// <summary>
        /// Required image is missing.
        /// </summary>
        public const int RequiredImageMissing = 4027;

        /// <summary>
        /// Invalid ImageAdExtension Layout.
        /// </summary>
        public const int InvalidImageExtensionLayout = 4036;

        /// <summary>
        /// Limit of Images Saved per Account reached
        /// </summary>
        public const int ImagesLimitExceededPerAccount = 4028;

        /// <summary>
        /// One of the video source entities is null
        /// </summary>
        public const int VideoSourceIsNull = 4029;

        /// <summary>
        /// One of the video urls is null
        /// </summary>
        public const int VideoUrlDataIsNull = 4030;

        /// <summary>
        /// One of the video descriptions is null
        /// </summary>
        public const int VideoDescriptionDataIsNull = 4031;

        /// <summary>
        /// One of the video urls is null
        /// </summary>
        public const int VideoUrlTextTooLong = 4032;

        /// <summary>
        /// One of the video descriptions is null
        /// </summary>
        public const int VideoDescriptionTextTooLong = 4034;

        /// <summary>
        /// The list of VideoSource exceeds the limit
        /// </summary>
        public const int VideoSourceLimitExceeded = 4035;

        /// <summary>
        /// Max videos per account limit is reached.
        /// </summary>
        public const int VideoLimitExceededPerAccount = 4037;

        /// <summary>
        /// The video scan type is not supported
        /// </summary>
        public const int VideoScanTypeNotSupported = 4038;

        /// <summary>
        /// The frame rate of the video is not supported
        /// </summary>
        public const int InvalidFrameRate = 4039;

        /// <summary>
        /// Only single track is supported in the video
        /// </summary>
        public const int OnlySingleTrackSupported = 4040;

        /// <summary>
        /// The duration of the video and audio must match
        /// </summary>
        public const int VideoAudioDurationMustMatch = 4041;

        /// <summary>
        /// The chroma subsampling in the video is not supported
        /// </summary>
        public const int ChromaSubsamplingNotSupported = 4042;

        /// <summary>
        /// The number of audio channels in the video is not supported
        /// </summary>
        public const int UnsupportedNumberOfAudioChannels = 4043;

        /// <summary>
        /// The video profile is not supported
        /// </summary>
        public const int VideoProfileNotSupported = 4044;

        /// <summary>
        /// The audio bit rate in the video is invalid
        /// </summary>
        public const int InvalidAudioBitRate = 4045;

        /// <summary>
        /// The audio bit depth in the video is not supported
        /// </summary>
        public const int UnsupportedAudioBitDepth = 4046;

        /// <summary>
        /// The audio sample rate in the video is not supported
        /// </summary>
        public const int UnsupportedAudioSampleRate = 4047;

        /// <summary>
        /// The frame rate mode in the video is not supported
        /// </summary>
        public const int UnsupportedFrameRateMode = 4048;

        /// <summary>
        /// The ad group criterion ID is not valid.
        /// </summary>
        public const int AdGroupCriterionIdInvalid = 4100;

        /// <summary>
        /// The list of ad group criterion IDs cannot be null or empty.
        /// </summary>
        public const int AdGroupCriterionIdArrayNullOrEmpty = 4101;

        /// <summary>
        /// The list of ad group criterion IDs cannot contain duplicates.
        /// </summary>
        public const int DuplicateAdGroupCriterionId = 4102;

        /// <summary>
        /// The list of ad group criterion IDs is too long.
        /// </summary>
        public const int AdGroupCriterionIdListExceedsLimit = 4103;

        /// <summary>
        /// The list of ad group criterions cannot be null or empty.
        /// </summary>
        public const int AdGroupCriterionsNullOrEmpty = 4104;

        /// <summary>
        /// The list of ad group criterions is too long.
        /// </summary>
        public const int AdGroupCriterionsEntityLimitExceeded = 4105;

        /// <summary>
        /// The ad group criterion cannot be null.
        /// </summary>
        public const int AdGroupCriterionIsNull = 4106;

        /// <summary>
        /// The condition of Ad Group Criterion is invalid.
        /// </summary>
        public const int AdGroupCriterionInvalidConditionType = 4107;

        /// <summary>
        /// The ad group criterion's bid type is not valid.
        /// </summary>
        public const int AdGroupCriterionInvalidBidType = 4108;

        /// <summary>
        /// The ad group criterion's bid value is not valid.
        /// </summary>
        public const int AdGroupCriterionInvalidBidValue = 4109;

        /// <summary>
        /// The ad group criterion's ID is read-only and must be null.
        /// </summary>
        public const int AdGroupCriterionIdShouldBeNullOnAdd = 4110;

        /// <summary>
        /// The ad group criterion's status is not valid.
        /// </summary>
        public const int InvalidAdGroupCriterionStatus = 4111;

        /// <summary>
        /// The ad group criterion's type is not valid.
        /// </summary>
        public const int InvalidAdGroupCriterionType = 4112;

        /// <summary>
        /// The list of product conditions is too long.
        /// </summary>
        public const int ProductAdGroupCriterionTooManyConditions = 4113;

        /// <summary>
        /// The product condition cannot be null.
        /// </summary>
        public const int AdGroupCriterionProductConditionIsNull = 4114;

        /// <summary>
        /// The criterion's type is not valid for this operation.
        /// </summary>
        public const int AdGroupCriterionTypeInvalid = 4115;

        /// <summary>
        /// The product condition's attribute cannot be null or empty.
        /// </summary>
        public const int ProductConditionAttributeNullOrEmpty = 4116;

        /// <summary>
        /// The ad group criterion is deleted.
        /// </summary>
        public const int AdGroupCriterionIsDeleted = 4117;

        /// <summary>
        /// Setting Editorial Status is not allowed.
        /// </summary>
        public const int InvalidAdGroupCriterionEditorialStatus = 4118;

        /// <summary>
        /// The ad group criterion does not exist.
        /// </summary>
        public const int AdGroupCriterionDoesNotExist = 4151;

        /// <summary>
        /// Duplicate ad group criterions are not allowed.
        /// </summary>
        public const int DuplicateAdGroupCriterion = 4152;

        /// <summary>
        /// The ad group criterion's Id must be null.
        /// </summary>
        public const int AdgroupCriterionIdIsNotNull = 4153;

        /// <summary>
        /// The ad group criterion's Id cannot be null.
        /// </summary>
        public const int AdGroupCriterionIdIsNull = 4154;

        /// <summary>
        /// The ad group criterion's Id don't match criterion type.
        /// </summary>
        public const int AdGroupCriterionIdNotMatchCriterionType = 4155;

        /// <summary>
        /// The BidMultiplier is not set for AdGroup Criterion.
        /// </summary>
        public const int AdGroupCriterionBidMultiplierValueNotSet = 4156;

        /// <summary>
        /// The criterion of ad group criterion cannot be null.
        /// </summary>
        public const int AdGroupCriterionCriterionIsNullOrEmpty = 4157;

        /// <summary>
        /// Transaction failed because one or more criterion actions failed in the same transaction.
        /// </summary>
        public const int RelatedCriterionTransactionError = 4158;

        /// <summary>
        /// Criterion actions in the same transaction should be in the same AdGroup.
        /// </summary>
        public const int AdGroupCriterionTransactionAcrossAdGroups = 4159;

        /// <summary>
        /// The audience's delivery channel didn't match campaign type.
        /// </summary>
        public const int AudienceDeliveryChannelNotMatchCampaignType = 4160;

        /// <summary>
        /// The product audience's type is invalid.
        /// </summary>
        public const int InvalidProductAudienceType = 4161;

        /// <summary>
        /// Duplicate product audience.
        /// </summary>
        public const int DuplicateProductAudience = 4162;

        /// <summary>
        /// The ad group already has a campaign audience criterion. Please remove first.
        /// </summary>
        public const int AdGroupAlreadyHasCampaignAudienceCriterion = 4163;

        /// <summary>
        /// The BidMultiplier and CashbackAdjustment are not set for AdGroup Criterion.
        /// </summary>
        public const int AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet = 4164;

        /// <summary>
        /// Content Targeting Only Support Audience Campaign.
        /// </summary>
        public const int ContentTargetingOnlySupportAudienceCampaign = 4165;

        /// <summary>
        /// Account Is Not Eligible For Placement Targeting.
        /// </summary>
        public const int AccountIsNotEligibleForPlacementTargeting = 4166;

        /// <summary>
        /// Invalid PlacementId Target.
        /// </summary>
        public const int InvalidPlacementIdTarget = 4167;

        /// <summary>
        /// Invalid Placement Target Bid Adjustment.
        /// </summary>
        public const int InvalidPlacementTargetBidAdjustment = 4168;

        /// <summary>
        /// Account Is Not Eligible For Sub Placement Targeting.
        /// </summary>
        public const int AccountIsNotEligibleForSubPlacementTargeting = 4169;

        /// <summary>
        /// The Topic ID target is invalid.
        /// </summary>
        public const int InvalidTopicIdTarget = 4173;

        /// <summary>
        /// The Topic target bid adjustment is invalid.
        /// </summary>
        public const int InvalidTopicTargetBidAdjustment = 4174;

        /// <summary>
        /// The account is not eligible for Topic targeting.
        /// </summary>
        public const int AccountIsNotEligibleForTopicTargeting = 4175;

        /// <summary>
        /// The list of ad group criterion actions cannot be null or empty.
        /// </summary>
        public const int AdGroupCriterionActionsNullOrEmpty = 4119;

        /// <summary>
        /// The ad group criterion action cannot be null.
        /// </summary>
        public const int AdGroupCriterionActionNull = 4120;

        /// <summary>
        /// The Campaign must be of type Shopping for this operation.
        /// </summary>
        public const int CampaignIsNotOfTypeShopping = 4121;

        /// <summary>
        /// The product partition type is invalid.
        /// </summary>
        public const int ProductPartitionTypeInvalid = 4123;

        /// <summary>
        /// The ad group criterion type cannot be updated.
        /// </summary>
        public const int AdGroupCriterionTypeImmutable = 4124;

        /// <summary>
        /// The negative ad group criterion cannot be updated.
        /// </summary>
        public const int NegativeAdGroupCriterionImmutable = 4125;

        /// <summary>
        /// The specified ad group cannot contain additional product partitions.
        /// </summary>
        public const int AdGroupProductPartitionLimitExceeded = 4126;

        /// <summary>
        /// Adding the product partition would result in an invalid product partition hierarchy.
        /// </summary>
        public const int InvalidProductPartitionHierarchy = 4127;

        /// <summary>
        /// The product partition is missing a product condition that specifies the remaining products.
        /// </summary>
        public const int RemainingProductsNodeMissingInProductPartition = 4128;

        /// <summary>
        /// Children of a product partition node cannot contain duplicate product conditions.
        /// </summary>
        public const int DuplicateProductConditions = 4129;

        /// <summary>
        /// All children of a product partition node must have the same operand.
        /// </summary>
        public const int ProductPartitionSiblingsMustHaveSameOperand  = 4130;

        /// <summary>
        /// The height of the product partition tree would have exceeded the limit.
        /// </summary>
        public const int ProductPartitionTreeHeightLimitExceeeded = 4131;

        /// <summary>
        /// The ad group already has a product partition tree root node.
        /// </summary>
        public const int ProductPartitionTreeRootAlreadyExists = 4132;

        /// <summary>
        /// You can only add child nodes to a parent of type subdivision.
        /// </summary>
        public const int CannotAddChildrenToProductPartitionUnit = 4133;

        /// <summary>
        /// The requested parent ad group criterion does not exist in the product partition tree.
        /// </summary>
        public const int ParentAdGroupCriterionIdInvalid = 4134;

        /// <summary>
        /// The criterion field of a product partition cannot be updated.
        /// </summary>
        public const int ProductPartitionCriterionImmutable = 4135;

        /// <summary>
        /// You must add a replacement product partition node before it can be deleted from the subdivision.
        /// </summary>
        public const int RemainingProductsNodeRequired = 4136;

        /// <summary>
        /// Too many ad groups were specified in the list of ad group criterion actions.
        /// </summary>
        public const int CriterionActionsAdGroupLimitExceeded = 4137;

        /// <summary>
        /// The product condition's operand is not valid.
        /// </summary>
        public const int ProductConditionOperandInvalid = 4139;

        /// <summary>
        /// The product condition's attribute is not valid.
        /// </summary>
        public const int ProductConditionAttributeInvalid = 4140;

        /// <summary>
        /// The criterion object passed is not allowed for the operation.
        /// </summary>
        public const int CriterionTypeNotAllowed = 4141;

        /// <summary>
        /// The criterion type specified does not match the criterion object passed.
        /// </summary>
        public const int CriterionTypeMismatch = 4142;

        /// <summary>
        /// The destination URL protocol is invalid.
        /// </summary>
        public const int DestinationUrlProtocolInvalid = 4143;

        /// <summary>
        /// The destination URL is invalid.
        /// </summary>
        public const int DestinationUrlInvalid = 4144;

        /// <summary>
        /// The destination URL is too long.
        /// </summary>
        public const int DestinationUrlTooLong = 4145;

        /// <summary>
        /// Param1 is not supported for this criterion type.
        /// </summary>
        public const int Param1NotSupportedForCriterionType = 4146;

        /// <summary>
        /// Param2 is not supported for this criterion type.
        /// </summary>
        public const int Param2NotSupportedForCriterionType = 4147;

        /// <summary>
        /// Param3 is not supported for this criterion type.
        /// </summary>
        public const int Param3NotSupportedForCriterionType = 4148;

        /// <summary>
        /// The data that you tried to modify was instead modified by another operation during the attempted processing of this service operation.
        /// </summary>
        public const int ConcurrentStoreModification = 4149;

        /// <summary>
        /// Product partition action for the same ad group has an error.
        /// </summary>
        public const int RelatedProductPartitionActionError = 4150;

        /// <summary>
        /// The product condition's operator is not valid.
        /// </summary>
        public const int ProductConditionOperatorInvalid = 4170;

        /// <summary>
        /// Customer not eligible for enhanced product ads filter.
        /// </summary>
        public const int CustomerNotEligibleForEnhancedProductAdsFilter = 4171;

        /// <summary>
        /// The product condition operator is not supported for this campaign type.
        /// </summary>
        public const int ProductConditionOperatorNotSupportedForThisCampaignType = 4172;

        /// <summary>
        /// Failed due to other items in the batch.
        /// </summary>
        public const int BatchOperationFailedForItems = 4200;

        /// <summary>
        /// The specified RequestId is not valid.
        /// </summary>
        public const int InvalidRequestId = 4201;

        /// <summary>
        /// The entity specified by the row could not be found.
        /// </summary>
        public const int EntityNotFound = 4202;

        /// <summary>
        /// The Type column of the row was not recognized and could not be inferred from the contents.
        /// </summary>
        public const int UnknownTypeForRow = 4203;

        /// <summary>
        /// No more bulk upload or download calls will be permitted for this account for the current time period. If you have reached your bulk upload limit, the bulk download operations may still be available, or vice versa.
        /// </summary>
        public const int NoMoreCallsPermittedForTheTimePeriod = 4204;

        /// <summary>
        /// Cannot remove the last valid item in an adextension.
        /// </summary>
        public const int CannotRemoveLastGoodAdExtensionItem = 4205;

        /// <summary>
        /// The text format of Custom Parameters is invalid.
        /// </summary>
        public const int InvalidCustomParametersText = 4206;

        /// <summary>
        /// The text format of Final Url is invalid.
        /// </summary>
        public const int InvalidFinalUrlsText = 4207;

        /// <summary>
        /// The text format of Mobile Final Url is invalid.
        /// </summary>
        public const int InvalidMobileFinalUrlsText = 4208;

        /// <summary>
        /// Shared entity Name field is a required.
        /// </summary>
        public const int SharedEntityNameNullOrEmpty = 4301;

        /// <summary>
        /// Your account already has the maximum number of shared entity lists.
        /// </summary>
        public const int SharedEntityLimitExceeded = 4302;

        /// <summary>
        /// The specified shared entity type is invalid or not currently supported.
        /// </summary>
        public const int SharedEntityInvalidType = 4303;

        /// <summary>
        /// Shared entity associations are required.
        /// </summary>
        public const int SharedEntityAssociationsNullOrEmpty = 4304;

        /// <summary>
        /// Multiple types of shared entity are not allowed in one call.
        /// </summary>
        public const int MultipleSharedEntityTypesNotAllowed = 4306;

        /// <summary>
        /// Too many shared entity associations in one batch.
        /// </summary>
        public const int SharedEntityAssociationsBatchLimitExceeded = 4307;

        /// <summary>
        /// Shared entity associations collection cannot be null or empty
        /// </summary>
        public const int SharedEntityAssociationsListItemNullOrEmpty = 4308;

        /// <summary>
        /// Shared entity is a duplicate of a existing association.
        /// </summary>
        public const int SharedEntityAssociationDuplicate = 4309;

        /// <summary>
        /// Shared entity Id field cannot be null or empty.
        /// </summary>
        public const int SharedEntityIdsNullOrEmpty = 4310;

        /// <summary>
        /// Shared Entity cannot be null or empty
        /// </summary>
        public const int SharedEntityNullOrEmpty = 4311;

        /// <summary>
        /// Duplicate shared entity Ids are not allowed.
        /// </summary>
        public const int DuplicateSharedEntityId = 4312;

        /// <summary>
        /// Shared entity type is a required field.
        /// </summary>
        public const int SharedEntityTypeNullOrEmpty = 4313;

        /// <summary>
        /// Deleted shared entity lists cannot be retrieved or modified.
        /// </summary>
        public const int SharedListDeleted = 4314;

        /// <summary>
        /// Shared entity lists are required.
        /// </summary>
        public const int SharedListsNullOrEmpty = 4315;

        /// <summary>
        /// Shared entity list is a required field.
        /// </summary>
        public const int SharedListNullOrEmpty = 4316;

        /// <summary>
        /// Shared entity list Id is not valid
        /// </summary>
        public const int SharedListIdInvalid = 4317;

        /// <summary>
        /// Duplicate shared list identifiers are not allowed.
        /// </summary>
        public const int SharedListDuplicate = 4318;

        /// <summary>
        /// Shared entity list items are a required field.
        /// </summary>
        public const int SharedListItemsNullOrEmpty = 4319;

        /// <summary>
        /// Shared entity list Id is not a valid Id
        /// </summary>
        public const int SharedListItemIdInvalid = 4320;

        /// <summary>
        /// The maximum number of shared entity list identifiers per call has been exceeded.
        /// </summary>
        public const int SharedListItemIdsLimitExceeded = 4321;

        /// <summary>
        /// Shared entities is a required field.
        /// </summary>
        public const int SharedEntitiesNullOrEmpty = 4322;

        /// <summary>
        /// Shared entity name is too long.
        /// </summary>
        public const int SharedEntityNameTooLong = 4323;

        /// <summary>
        /// Shared entity name is invalid
        /// </summary>
        public const int SharedEntityNameInvalid = 4324;

        /// <summary>
        /// Shared entity list Id is not allowed
        /// </summary>
        public const int SharedListIdNotAllowed = 4325;

        /// <summary>
        /// Shared entity id is invalid
        /// </summary>
        public const int SharedEntityIdInvalid = 4326;

        /// <summary>
        /// Adding the negative keyword to the list will exceed the maximum number of negative keywords that you can add to the list.
        /// </summary>
        public const int MaxNegativeKeywordLimitExceededForList = 4328;

        /// <summary>
        /// Cannot modify a deleted negative keyword
        /// </summary>
        public const int NegativeKeywordDeleted = 4329;

        /// <summary>
        /// Negative keyword Id is invalid
        /// </summary>
        public const int InvalidNegativeKeywordId = 4330;

        /// <summary>
        /// A list with an active association cannot be deleted.
        /// </summary>
        public const int NegativeKeywordListWithActiveAssociationsCannotBeDeleted = 4331;

        /// <summary>
        /// Negative Keyword Type field is invalid
        /// </summary>
        public const int NegativeKeywordTypeInvalid = 4332;

        /// <summary>
        /// Negative Keyword text is required
        /// </summary>
        public const int NegativeKeywordTextRequired = 4333;

        /// <summary>
        /// Duplicate negative keyword list names are not allowed
        /// </summary>
        public const int DuplicateNegativeKeywordListName = 4334;

        /// <summary>
        /// The Structured Negative Keyword Pilot is not enabled for this customer.
        /// </summary>
        public const int StructuredNegativeKeywordPilotNotEnabledForCustomer = 4336;

        /// <summary>
        /// You cannot mix the parent types for the Negative Keywords in this calls.
        /// </summary>
        public const int NegativeKeywordTooManyParentTypes = 4337;

        /// <summary>
        /// Duplicate negative keyword already exists in this list.
        /// </summary>
        public const int NegativeKeywordDuplicateFound = 4338;

        /// <summary>
        /// No negative keywords were found.
        /// </summary>
        public const int NegativeKeywordNotFound = 4339;

        /// <summary>
        /// The match type for this keyword was invalid.
        /// </summary>
        public const int NegativeKeywordInvalidMatchType = 4340;

        /// <summary>
        /// The parent type for this negative keyword is invalid.
        /// </summary>
        public const int NegativeKeywordInvalidParentType = 4341;

        /// <summary>
        /// Negative Keywords entities are required for this call
        /// </summary>
        public const int NegativeKeywordEntitiesNotPassed = 4342;

        /// <summary>
        /// Negative Keyword cannot be null
        /// </summary>
        public const int NegativeKeywordEntityNull = 4343;

        /// <summary>
        /// Negative Keyword types cannot be added to multiple entity types in one call.
        /// </summary>
        public const int NegativeKeywordEntityTypesMismatch = 4344;

        /// <summary>
        /// Duplicate negative keyword are not allowed.
        /// </summary>
        public const int NegativeKeywordDuplicate = 4345;

        /// <summary>
        /// Shared entity association does not exisit
        /// </summary>
        public const int SharedEntityAssociationDoesNotExist = 4346;

        /// <summary>
        /// Campaign Ids passed are null or empty.
        /// </summary>
        public const int CampaignIdsNotPassed = 4347;

        /// <summary>
        /// AdGroup Ids passed are null or empty.
        /// </summary>
        public const int AdGroupIdsNotPassed = 4348;

        /// <summary>
        /// Too many shared entity ids in one batch.
        /// </summary>
        public const int SharedEntityBatchLimitExceeded = 4349;

        /// <summary>
        /// Too many entity ids in one batch.
        /// </summary>
        public const int EntityBatchLimitExceeded = 4350;

        /// <summary>
        /// Negative keyword limit at account level exceeded.
        /// </summary>
        public const int NegativeKeywordsAccountLimitExceeded = 4351;

        /// <summary>
        /// Shared entity association limit at account level exceeded.
        /// </summary>
        public const int SharedEntityAssociationsAccountLimitExceeded = 4352;

        /// <summary>
        /// The customer is not in pilot for Manager account website exclusion lists.
        /// </summary>
        public const int NotInPilotForManagerAccountSharedWebsiteExclusions = 4353;

        /// <summary>
        /// Shared list with the same name already exists.
        /// </summary>
        public const int DuplicateSharedListName = 4354;

        /// <summary>
        /// Limit for shared list items in list has been reached.
        /// </summary>
        public const int MaxListItemLimitExceededForList = 4355;

        /// <summary>
        /// A matching list item already exists.
        /// </summary>
        public const int DuplicateListItemInList = 4356;

        /// <summary>
        /// The list item type is not valid for the list type.
        /// </summary>
        public const int InvalidListItemTypeForList = 4357;

        /// <summary>
        /// All associations must be removed before the shared entity can be deleted.
        /// </summary>
        public const int SharedEntitiesWithActiveAssociationsCannotBeDeleted = 4358;

        /// <summary>
        /// The list item was not found in the list.
        /// </summary>
        public const int SharedListItemNotInList = 4359;

        /// <summary>
        /// The list item batch limit is exceeded.
        /// </summary>
        public const int SharedListItemBatchLimitExceeded = 4360;

        /// <summary>
        /// Invalid Campaign SubType for this operation.
        /// </summary>
        public const int InvalidCampaignSubType = 4361;

        /// <summary>
        /// The customer is not part of the shopping campaign pilot program.
        /// </summary>
        public const int ShoppingCampaignPilotNotEnabledForCustomer = 4500;

        /// <summary>
        /// The list of campaign criterion cannot be null or empty.
        /// </summary>
        public const int CampaignCriterionsNullOrEmpty = 4501;

        /// <summary>
        /// The list of campaign criterion is too long.
        /// </summary>
        public const int CampaignCriterionsLimitExceeded = 4502;

        /// <summary>
        /// The campaign criterion's type is not valid.
        /// </summary>
        public const int CampaignCriterionTypeInvalid = 4503;

        /// <summary>
        /// The campaign criterion cannot be null.
        /// </summary>
        public const int CampaignCriterionIsNull = 4504;

        /// <summary>
        /// The campaign criterion's ID is read-only and must be null.
        /// </summary>
        public const int CampaignCriterionIdShouldBeNullOnAdd = 4505;

        /// <summary>
        ///  The list of campaign criterion IDs cannot contain duplicates.
        /// </summary>
        public const int DuplicateCampaignCriterionId = 4506;

        /// <summary>
        /// The campaign criterion ID is not valid.
        /// </summary>
        public const int CampaignCriterionIdInvalid  = 4507;

        /// <summary>
        /// The product condition of the campaign criterion is invalid.
        /// </summary>
        public const int CampaignCriterionProductConditionInvalid = 4508;

        /// <summary>
        /// Campaign has to be of type Shopping for this operation.
        /// </summary>
        public const int CampaignTypeIsNotShoppingCampaign = 4509;

        /// <summary>
        /// The Criterion of campaign criterion cannot be null or empty.
        /// </summary>
        public const int CampaignCriterionCriterionIsNullOrEmpty = 4510;

        /// <summary>
        /// The list of product conditions for the campaign criterion is too long.
        /// </summary>
        public const int CampaignCriterionTooManyConditions = 4511;

        /// <summary>
        /// Duplicate campaign IDs were specified in the list of campaign criterion.
        /// </summary>
        public const int CampaignCriterionDuplicateCampaignId = 4512;

        /// <summary>
        /// The list of campaign criterion IDs cannot be null or empty.
        /// </summary>
        public const int CampaignCriterionIdArrayNullOrEmpty = 4513;

        /// <summary>
        /// The list of campaign criterion IDs is too long.
        /// </summary>
        public const int CampaignCriterionIdsLimitExceeded = 4514;

        /// <summary>
        /// The product condition cannot be null.
        /// </summary>
        public const int ProductConditionIsNull = 4515;

        /// <summary>
        /// The product condition's operand is already in use for the campaign criterion.
        /// </summary>
        public const int CampaignCriterionDuplicateProductConditionOperand = 4516;

        /// <summary>
        /// The campaign already has a campaign criterion. Only one campaign criterion is allowed per campaign.
        /// </summary>
        public const int CampaignCriterionAlreadyExists = 4517;

        /// <summary>
        /// The list of CampaignCriterionActions is NullOrEmpty. Please see the ReasonCode element of this error object for details.
        /// </summary>
        public const int CampaignCriterionActionsNullOrEmpty = 4518;

        /// <summary>
        /// The BidMultiplier is not set for Campaign Criterion.
        /// </summary>
        public const int CampaignCriterionBidMultiplierValueNotSet = 4519;

        /// <summary>
        /// The campaign criterion's status is not valid.
        /// </summary>
        public const int CampaignCriterionStatusInvalid = 4520;

        /// <summary>
        /// Duplicate campaign criterions are not allowed.
        /// </summary>
        public const int DuplicateCampaignCriterion = 4521;

        /// <summary>
        /// The campaign already has an ad group audience criterion. Please remove first
        /// </summary>
        public const int CampaignAlreadyHasAdGroupAudienceCriterion = 4522;

        /// <summary>
        /// The campaign criterion's type cannot be all null or empty on creation
        /// </summary>
        public const int AllNullCampaignCriterionTypesNotAllowedOnCreate = 4523;

        /// <summary>
        /// The campaign criterion does not exist.
        /// </summary>
        public const int CampaignCriterionDoesNotExist = 4526;

        /// <summary>
        /// The BidMultiplier and CashbackAdjustment are not set for Campaign Criterion.
        /// </summary>
        public const int CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet = 4527;

        /// <summary>
        /// Customer is not in pilot for this adextension type.
        /// </summary>
        public const int AdExtensionPilotNotEnabledForCustomer = 4400;

        /// <summary>
        /// Customer is not in pilot for this adextension type campaign association.
        /// </summary>
        public const int AdExtensionCampaignAssociationPilotNotEnabledForCustomer = 4401;

        /// <summary>
        /// Customer is not in pilot for this adextension type adgroup association.
        /// </summary>
        public const int AdExtensionAdGroupAssociationPilotNotEnabledForCustomer = 4402;

        /// <summary>
        /// Value is too short.
        /// </summary>
        public const int ValueTooShort = 4403;

        /// <summary>
        /// Value is too long.
        /// </summary>
        public const int ValueTooLong = 4404;

        /// <summary>
        /// Value is out of valid range.
        /// </summary>
        public const int ValueOutOfRange = 4405;

        /// <summary>
        /// Required value is missing.
        /// </summary>
        public const int ValueIsMissing = 4406;

        /// <summary>
        /// Value is invalid.
        /// </summary>
        public const int InvalidValue = 4407;

        /// <summary>
        /// Customer Not Enable For NativeAds Pilot.
        /// </summary>
        public const int CustomerNotEnableForNativeAdsPilot = 4408;

        /// <summary>
        /// AudienceAdsBidAdjustment value or flag is invalid.
        /// </summary>
        public const int InvalidAudienceAdsBidAdjustmentValue = 4409;

        /// <summary>
        /// Header is invalid.
        /// </summary>
        public const int InvalidHeader = 4410;

        /// <summary>
        /// Structured Snippet values do not support commas or semicolons.
        /// </summary>
        public const int InvalidStructuredSnippetCharacter = 4411;

        /// <summary>
        /// Too many structured snippet texts.
        /// </summary>
        public const int TooManyStructuredSnippetText = 4412;

        /// <summary>
        /// Too few structured snippet text.
        /// </summary>
        public const int TooFewStructuredSnippetText = 4413;

        /// <summary>
        /// Customer is not in pilot for this adextension type account association.
        /// </summary>
        public const int AdExtensionAccountAssociationPilotNotEnabledForCustomer = 4414;

        /// <summary>
        /// Value cannot be updated.
        /// </summary>
        public const int ValueImmutable = 4415;

        /// <summary>
        /// Minimum number of assets for field not met.
        /// </summary>
        public const int AssetFieldMinimumNotMet = 4416;

        /// <summary>
        /// Excceded maximum number of assets allowed in field.
        /// </summary>
        public const int AssetFieldLimitExceeded = 4417;

        /// <summary>
        /// Resulting state would be invalid.
        /// </summary>
        public const int InvalidState = 4418;

        /// <summary>
        /// Duplicate values in MMA text fields are not allowed.
        /// </summary>
        public const int DuplicatedTextField = 4419;

        /// <summary>
        /// Url scheme is invalid.
        /// </summary>
        public const int InvalidUrlScheme = 4600;

        /// <summary>
        /// Tracking url template is too long.
        /// </summary>
        public const int TrackingTemplateTooLong = 4601;

        /// <summary>
        /// Landing page url tag is missing.
        /// </summary>
        public const int MissingLandingPageUrlTag = 4602;

        /// <summary>
        /// The list exceeds maximun allowed limit.
        /// </summary>
        public const int CountExceedsLimit = 4603;

        /// <summary>
        /// Key contains invalid characters.
        /// </summary>
        public const int InvalidCharactersInKey = 4604;

        /// <summary>
        /// Value contains invalid characters.
        /// </summary>
        public const int InvalidCharactersInValue = 4605;

        /// <summary>
        /// Value contains invalid tag.
        /// </summary>
        public const int InvalidTag = 4606;

        /// <summary>
        /// Os type is invalid.
        /// </summary>
        public const int InvalidOsType = 4607;

        /// <summary>
        /// Key is too long.
        /// </summary>
        public const int KeyTooLong = 4608;

        /// <summary>
        /// Url is too long.
        /// </summary>
        public const int UrlTooLong = 4609;

        /// <summary>
        /// Duplicate os type for app url.
        /// </summary>
        public const int DuplicateOsTypeForAppUrl = 4610;

        /// <summary>
        /// Key is null or empty.
        /// </summary>
        public const int KeyNullOrEmpty = 4611;

        /// <summary>
        /// Value is null or empty.
        /// </summary>
        public const int ValueNullOrEmpty = 4612;

        /// <summary>
        /// Parameter is null.
        /// </summary>
        public const int ParameterIsNull = 4613;

        /// <summary>
        /// UpgradedUrls pilot is not enabled for customer.
        /// </summary>
        public const int UpgradedUrlsPilotNotEnabledForCustomer = 4614;

        /// <summary>
        /// Final Url is required when using tracking url template or custom parameter.
        /// </summary>
        public const int FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter = 4615;

        /// <summary>
        /// Final Url is required when using mobile final url.
        /// </summary>
        public const int FinalUrlRequiredWhenUsingMobileFinalUrl = 4616;

        /// <summary>
        /// Mobile final url is not allowed with mobile device preference.
        /// </summary>
        public const int MobileFinalUrlNotAllowedWithMobileDevicePreference = 4617;

        /// <summary>
        /// Having both destination url and final url is not allowed.
        /// </summary>
        public const int BothDestinationUrlAndFinalUrlNotAllowed = 4618;

        /// <summary>
        /// Having both destination url and url custom parameter is not allowed.
        /// </summary>
        public const int BothDestinationUrlAndUrlCustomParameterNotAllowed = 4619;

        /// <summary>
        /// Duplicate values in field are not allowed.
        /// </summary>
        public const int DuplicatesInField = 4620;

        /// <summary>
        /// Having both destination url and tracking template is not allowed.
        /// </summary>
        public const int BothDestinationUrlAndTrackingTemplateNotAllowed = 4621;

        /// <summary>
        /// Final Urls and Mobile Final Urls are not allowed in a product partition.
        /// </summary>
        public const int CampaignServiceFinalUrlAndMobileUrlNotAllowedForProductPartition = 4622;

        /// <summary>
        /// Tracking template and custom parameters cannot be added to a product partition subdivision.
        /// </summary>
        public const int CampaignServiceTemplateAndParametersNotAllowedForProductPartitionSubdivision = 4623;

        /// <summary>
        /// The customer is not in the Upgraded URLs pilot. The tracking template from this entity is imported as a destination url instead of being imported as a tracking template. If you tried to import any custom parameters, they were not imported.
        /// </summary>
        public const int UpgradedUrlsPilotNotEnabledCustomParametersNotImported = 4624;

        /// <summary>
        /// Your tracking template doesn't match the expected format, missing required parameters or string.
        /// </summary>
        public const int MissingRequiredParameterOrString = 4632;

        /// <summary>
        /// Your tracking template doesn't match the expected format.
        /// </summary>
        public const int InvalidQueryDelimiterPlacement = 4633;

        /// <summary>
        /// Your tracking template contains an invalid App ID.
        /// </summary>
        public const int InvalidAppId  = 4634;

        /// <summary>
        /// Final Url is required when using final url suffix.
        /// </summary>
        public const int FinalUrlRequiredWhenUsingFinalUrlSuffix = 4625;

        /// <summary>
        /// Final Url Suffix contains invalid characters.
        /// </summary>
        public const int FinalUrlSuffixInvalidCharacters = 4626;

        /// <summary>
        /// Final Url Suffix contains invalid parameters.
        /// </summary>
        public const int FinalUrlSuffixInvalidParameters = 4627;

        /// <summary>
        /// Having both destination url and final url suffix is not allowed.
        /// </summary>
        public const int BothDestinationUrlAndFinalUrlSuffixNotAllowed = 4628;

        /// <summary>
        /// The Final Url Suffix is too long
        /// </summary>
        public const int FinalUrlSuffixTooLong = 4629;

        /// <summary>
        /// Final Url Suffix phase 1 pilot is not enabled for customer
        /// </summary>
        public const int CustomerNotEnabledForFinalUrlSuffixPhase1Pilot = 4630;

        /// <summary>
        /// Final Url Suffix phase 2 pilot is not enabled for customer
        /// </summary>
        public const int CustomerNotEnabledForFinalUrlSuffixPhase2Pilot = 4631;

        /// <summary>
        /// The customer is not enabled for this bidding scheme.
        /// </summary>
        public const int BiddingSchemeNotEnabledForCustomer = 4700;

        /// <summary>
        /// The bidding scheme is not supported.
        /// </summary>
        public const int UnsupportedBiddingScheme = 4701;

        /// <summary>
        /// TargetCpa is required for this bidding scheme.
        /// </summary>
        public const int TargetCpaIsRequired = 4702;

        /// <summary>
        /// MaxCpc exceeds campaign monthly budget.
        /// </summary>
        public const int MaxCpcExceedsMonthlyBudget = 4703;

        /// <summary>
        /// InheritFromParentBiddingScheme is not allowed to set to campaign and can only be applied in ad group and keyword entities.
        /// </summary>
        public const int InheritFromParentBiddingSchemeNotAllowedForCampaign = 4704;

        /// <summary>
        /// Only Manual and InheritFromParent bid strategy is allowed.
        /// </summary>
        public const int SupportOnlyManualAndInheritFromParentBiddingStrategy = 4705;

        /// <summary>
        /// Enhanced CPC is not allowed for shopping campaign.
        /// </summary>
        public const int EnhancedCpcNotAllowedForShoppingCampaign = 4706;

        /// <summary>
        /// The Bid Strategy Type from Google is unsupported.
        /// </summary>
        public const int UnsupportedGoogleBidStrategyType = 4707;

        /// <summary>
        /// MaxCpc Less Than Or Equal To Zero.
        /// </summary>
        public const int MaxCpcLessThanOrEqualToZero = 4708;

        /// <summary>
        /// TargetCpa Less Than Or Equal To Zero.
        /// </summary>
        public const int TargetCpaLessThanOrEqualToZero = 4709;

        /// <summary>
        /// TargetCpa Exceeds Monthly Budget.
        /// </summary>
        public const int TargetCpaExceedsMonthlyBudget = 4710;

        /// <summary>
        /// Unsupported BiddingScheme For Campaign with Monthly Budget Type.
        /// </summary>
        public const int UnsupportedBiddingSchemeForMonthlyBudgetType = 4711;

        /// <summary>
        /// MaxCpc Bid Amounts Less Than Floor Price.
        /// </summary>
        public const int MaxCpcBidAmountsLessThanFloorPrice = 4712;

        /// <summary>
        /// MaxCpc Bids Amounts Greater Than Ceiling Price.
        /// </summary>
        public const int MaxCpcBidsAmountsGreaterThanCeilingPrice = 4713;

        /// <summary>
        /// TargetCpa Bid Amounts Less Than Floor Price.
        /// </summary>
        public const int TargetCpaBidAmountsLessThanFloorPrice = 4714;

        /// <summary>
        /// TargetCpa Bids Amounts Greater Than Ceiling Price.
        /// </summary>
        public const int TargetCpaBidsAmountsGreaterThanCeilingPrice = 4715;

        /// <summary>
        /// No enough conversion for MaxConversions bidding scheme.
        /// </summary>
        public const int NoEnoughConversionForMaxConversionsBiddingScheme = 4716;

        /// <summary>
        /// No enough conversion for TargetCPA bidding scheme.
        /// </summary>
        public const int NoEnoughConversionForTargetCpaBiddingScheme = 4717;

        /// <summary>
        /// MaxConversions bidding scheme and Shared Budget feature are mutually exclusive.
        /// </summary>
        public const int MaxConversionAndSharedBudgetAreMutuallyExclusive = 4718;

        /// <summary>
        /// TargetCPA bidding scheme and Shared Budget feature are mutually exclusive.
        /// </summary>
        public const int TargetCpaAndSharedBudgetAreMutuallyExclusive = 4719;

        /// <summary>
        /// MaxConversions bidding scheme is not enabled for dynamic search ads.
        /// </summary>
        public const int MaxConversionsNotEnabledForDynamicSearchAds = 4720;

        /// <summary>
        /// TargetCPA bidding scheme is not enabled for dynamic search ads.
        /// </summary>
        public const int TargetCpaNotEnabledForDynamicSearchAds = 4721;

        /// <summary>
        /// MaxConversions bidding scheme is not enabled for the markets(locations).
        /// </summary>
        public const int MaxConversionsNotEnabledForTheMarkets = 4722;

        /// <summary>
        /// TargetCPA bidding scheme is not enabled for the markets(locations).
        /// </summary>
        public const int TargetCpaNotEnabledForTheMarkets = 4723;

        /// <summary>
        /// This bidding scheme and Shared Budget are mutually exclusive.
        /// </summary>
        public const int BiddingSchemeAndSharedBudgetAreMutuallyExclusive = 4724;

        /// <summary>
        /// Shared Budget and existed bidding scheme are mutually exclusive.
        /// </summary>
        public const int SharedBudgetAndBiddingSchemeAreMutuallyExclusive = 4725;

        /// <summary>
        /// The bidding scheme is not enabled for the markets(locations).
        /// </summary>
        public const int BiddingSchemeNotEnabledForTheLocations = 4726;

        /// <summary>
        /// The location is not enabled for the bidding scheme.
        /// </summary>
        public const int LocationNotEnabledForTheBiddingScheme = 4727;

        /// <summary>
        /// There must be at least 15 conversions in the last 30 days to use this bid strategy.
        /// </summary>
        public const int NotEnoughConversionsForTargetRoasBiddingScheme = 4728;

        /// <summary>
        /// There must be revenue tracking set up and revenue greater than zero in the last 30 days to use this bid strategy.
        /// </summary>
        public const int NotEnoughRevenueForTargetRoasBiddingScheme = 4729;

        /// <summary>
        /// Target ROAS(Revenue on Ad Spend) needs to be a positive integer.
        /// </summary>
        public const int InvalidTargetRoasValue = 4730;

        /// <summary>
        /// Customer not in pilot for Target ROAS(Revenue on Ad Spend) bid strategy.
        /// </summary>
        public const int CustomerNotInPilotForTargetRoas = 4731;

        /// <summary>
        /// Import will not change bid strategy because there is no revenue
        /// </summary>
        public const int BidStrategyUnchangedBecauseNotEnoughRevenue = 4732;

        /// <summary>
        /// There must be a conversion goal set up to use this bid strategy.
        /// </summary>
        public const int ConversionGoalCriteriaNotMetForBiddingScheme = 4733;

        /// <summary>
        /// The customer is not in pilot for Max Conversion Value.
        /// </summary>
        public const int CustomerNotInPilotForMaxConversionValue = 4734;

        /// <summary>
        /// The customer is not in pilot for the Target Impression Share bid strategy type.
        /// </summary>
        public const int CustomerNotInPilotForTargetImpressionShare = 4735;

        /// <summary>
        /// Target Ad Position is invalid.
        /// </summary>
        public const int InvalidTargetAdPositionValue = 4736;

        /// <summary>
        /// Target Impression Share is required for this bidding scheme.
        /// </summary>
        public const int TargetImpressionShareIsRequired = 4737;

        /// <summary>
        /// Target Impression Share needs to be a positive percentage value.
        /// </summary>
        public const int InvalidTargetImpressionShareValue = 4738;

        /// <summary>
        /// Target cost per sale value is invalid.
        /// </summary>
        public const int TargetCostPerSaleInvalid = 4739;

        /// <summary>
        /// Cost per sale bidding scheme cannot be updated.
        /// </summary>
        public const int CostPerSaleBiddingSchemeCannotBeUpdated = 4740;

        /// <summary>
        /// Campaign priority must be set to highest for cost per sale campaign.
        /// </summary>
        public const int InvalidShoppingCampaignPriorityForCostPerSale = 4741;

        /// <summary>
        /// Bid cannot be managed for selected bidding scheme.
        /// </summary>
        public const int BidCannotBeManagedForBiddingScheme = 4742;

        /// <summary>
        /// MaxCpc value is invalid.
        /// </summary>
        public const int InvalidMaxCpcValue = 4743;

        /// <summary>
        /// TargetCpa value is invalid.
        /// </summary>
        public const int InvalidTargetCpaValue = 4744;

        /// <summary>
        /// Bidding scheme cannot be updated.
        /// </summary>
        public const int ImmutableBiddingScheme = 4745;

        /// <summary>
        /// Daily budget is less than CPS bid value.
        /// </summary>
        public const int DailyBudgetAmountLessThanTargetCostPerSale = 4746;

        /// <summary>
        /// CPS bid strategy does not work with collection campaigns. Please change the bid strategy or de-select collection campaigns.
        /// </summary>
        public const int CostPerSaleNotWorkWithShoppableAds = 4747;

        /// <summary>
        /// ManualCPC value is invalid.
        /// </summary>
        public const int InvalidManualCpcValue = 4748;

        /// <summary>
        /// ManualCPC bid value is required for this bidding scheme..
        /// </summary>
        public const int ManualCpcIsRequired = 4749;

        /// <summary>
        /// ManualCPC less than or equal to zero
        /// </summary>
        public const int ManualCpcLessThanOrEqualToZero = 4750;

        /// <summary>
        /// MaxCpm Less Than Or Equal To Zero.
        /// </summary>
        public const int MaxCpmLessThanOrEqualToZero = 4751;

        /// <summary>
        /// MaxCpm value is invalid.
        /// </summary>
        public const int InvalidMaxCpmValue = 4752;

        /// <summary>
        /// MaxCpc Greater Than Ceiling.
        /// </summary>
        public const int MaxCpcGreaterThanCeiling = 4753;

        /// <summary>
        /// MaxCpm Greater Than Ceiling.
        /// </summary>
        public const int MaxCpmGreaterThanCeiling = 4754;

        /// <summary>
        /// The remarketing list rule item is invalid.
        /// </summary>
        public const int InvalidRemarketingListRuleItem = 4814;

        /// <summary>
        /// Too many rule items were specified in the remarketing list for the operation.
        /// </summary>
        public const int TooManyRuleItemsError = 4815;

        /// <summary>
        /// Too many rule item groups were specified in the remarketing list for the operation.
        /// </summary>
        public const int TooManyRuleItemGroupsError = 4816;

        /// <summary>
        /// Remarketing list tag id is invalid or not specified.
        /// </summary>
        public const int InvalidRemarketingListTagId = 4826;

        /// <summary>
        /// Remarketing list rule is invalid or not specified.
        /// </summary>
        public const int InvalidRemarketingListRule = 4827;

        /// <summary>
        /// The audience ID is invalid.
        /// </summary>
        public const int InvalidAudienceId = 4835;

        /// <summary>
        /// Audience IDs are required.
        /// </summary>
        public const int AudienceIdsNotPassed = 4836;

        /// <summary>
        /// Duplicate audience IDs are not allowed.
        /// </summary>
        public const int DuplicateAudienceId = 4837;

        /// <summary>
        /// The audience name is invalid.
        /// </summary>
        public const int InvalidAudienceName = 4838;

        /// <summary>
        /// The ad group audience criterion's bid adjustment is invalid.
        /// </summary>
        public const int InvalidAudienceCriterionBidAdjustment = 4839;

        /// <summary>
        /// Audiences cannot be associated with content only ad groups.
        /// </summary>
        public const int CannotAssociateAudienceWithContentOnlyAdGroup = 4840;

        /// <summary>
        /// Audience description is invalid.
        /// </summary>
        public const int InvalidAudienceDescription = 4841;

        /// <summary>
        /// Audience membership duration is invalid.
        /// </summary>
        public const int InvalidAudienceMembershipDuration = 4842;

        /// <summary>
        /// Audience parent id does not match the scope.
        /// </summary>
        public const int AudienceParentIdDoesNotMatchScope = 4843;

        /// <summary>
        /// Audiences are required.
        /// </summary>
        public const int AudiencesArrayShouldNotBeNullOrEmpty = 4844;

        /// <summary>
        /// Audience cannot be null.
        /// </summary>
        public const int AudienceIsNull = 4845;

        /// <summary>
        /// Audience id cannot be null.
        /// </summary>
        public const int AudienceIdIsNull = 4846;

        /// <summary>
        /// Audience id must be null.
        /// </summary>
        public const int AudienceIdIsNotNull = 4847;

        /// <summary>
        /// Audience parent id is invalid or not specified.
        /// </summary>
        public const int InvalidAudienceParentId = 4848;

        /// <summary>
        /// Audience scope is invalid or not specified.
        /// </summary>
        public const int InvalidAudienceScope = 4849;

        /// <summary>
        /// Audiences array exceeds the limit size.
        /// </summary>
        public const int AudiencesArrayExceedsLimit = 4850;

        /// <summary>
        /// Audience ids array should not be null or empty.
        /// </summary>
        public const int AudienceIdsArrayShouldNotBeNullOrEmpty = 4851;

        /// <summary>
        /// Audience ids array exceeds the limit size.
        /// </summary>
        public const int AudienceIdsArrayExceedsLimit = 4852;

        /// <summary>
        /// Audience name is duplicate.
        /// </summary>
        public const int DuplicateAudienceName = 4853;

        /// <summary>
        /// Audience cannot be deleted due to existing association.
        /// </summary>
        public const int AudienceCannotBeDeletedDueToExistingAdGroupCriterion = 4854;

        /// <summary>
        /// Audience scope cannot be changed on update.
        /// </summary>
        public const int AudienceCanNotChangeScopeOnUpdate = 4855;

        /// <summary>
        /// In-market audience is read-only.
        /// </summary>
        public const int InMarketAudienceIsReadOnly = 4856;

        /// <summary>
        /// Could not add custom audience.
        /// </summary>
        public const int CustomAudienceCouldNotBeAdded = 4857;

        /// <summary>
        /// Max custom audiences per customer limit is reached.
        /// </summary>
        public const int MaxCustomAudiencesPerCustomerLimitReached = 4858;

        /// <summary>
        /// Audience Type is invalid.
        /// </summary>
        public const int InvalidAudienceType = 4859;

        /// <summary>
        /// Could not delete custom audience and in-market audience.
        /// </summary>
        public const int CustomAudienceAndInMarketAudienceCouldNotBeDeleted = 4860;

        /// <summary>
        /// Audience Id do not match audience type.
        /// </summary>
        public const int AudienceIdDoNotMatchAudienceType = 4861;

        /// <summary>
        /// Max audience criterions per account limit is reached.
        /// </summary>
        public const int MaxAudienceCriterionsPerAccountLimitReached = 4862;

        /// <summary>
        /// Max remarketing list associations per account limit is reached.
        /// </summary>
        public const int MaxRemarketingListAssociationsPerAccountLimitReached = 4863;

        /// <summary>
        /// Could not delete in-market audience.
        /// </summary>
        public const int InMarketAudienceCouldNotBeDeleted = 4864;

        /// <summary>
        /// Max audiences per account limit is reached.
        /// </summary>
        public const int MaxAudiencesPerAccountLimitReached = 4865;

        /// <summary>
        /// Max audiences per customer limit is reached.
        /// </summary>
        public const int MaxAudiencesPerCustomerLimitReached = 4866;

        /// <summary>
        /// Max in-market audience exclusion per account limit is reached.
        /// </summary>
        public const int MaxInMarketAudienceExclusionPerAccountLimitReached = 4867;

        /// <summary>
        /// Similar remarketing list is read-only.
        /// </summary>
        public const int SimilarRemarketingListIsReadOnly = 4868;

        /// <summary>
        /// Could not delete similar remarketing list.
        /// </summary>
        public const int SimilarRemarketingListCouldNotBeDeleted = 4869;

        /// <summary>
        /// Customer is not eligible for similar remarketing list.
        /// </summary>
        public const int CustomerNotEligibleForSimilarRemarketingList = 4870;

        /// <summary>
        /// Audience cannot be deleted due to its paired similar audience has associations.
        /// </summary>
        public const int AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociations = 4871;

        /// <summary>
        /// Audience exclusions can't be changed to enabled or paused.
        /// </summary>
        public const int IllegalAudienceAssociationConversionFromExclusion = 4872;

        /// <summary>
        /// Customer share is not supported for this entity scope.
        /// </summary>
        public const int CustomerShareEntityScopeDoesNotMatch = 4873;

        /// <summary>
        /// Max criterions per customer limit is reached.
        /// </summary>
        public const int MaxCriterionLimitExceededForCustomer = 4874;

        /// <summary>
        /// Customer Is Not Eligible For ImpressionBasedRemarketingList.
        /// </summary>
        public const int CustomerIsNotEligibleForImpressionBasedRemarketingList = 4875;

        /// <summary>
        /// Customer Cannot Operate ImpressionBasedRemarketingList.
        /// </summary>
        public const int CustomerCannotOperateImpressionBasedRemarketingList = 4876;

        /// <summary>
        /// Invalid EntityId For ImpressionBasedRemarketingList.
        /// </summary>
        public const int InvalidEntityIdForImpressionBasedRemarketingList = 4877;

        /// <summary>
        /// Invalid Entity Type For ImpressionBasedRemarketingList.
        /// </summary>
        public const int InvalidEntityTypeForImpressionBasedRemarketingList = 4878;

        /// <summary>
        /// ImpressionBasedRemarketingList Can Only Be Edited By Creator.
        /// </summary>
        public const int ImpressionBasedRemarketingListCanOnlyBeEditedByCreator = 4879;

        /// <summary>
        /// ImpressionBasedRemarketingList Can Only Be Deleted By Creator.
        /// </summary>
        public const int ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator = 4880;

        /// <summary>
        /// Duplicated EntityId And Type For ImpressionBasedRemarketingList.
        /// </summary>
        public const int DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList = 4881;

        /// <summary>
        /// Customer not in Shared Budget pilot.
        /// </summary>
        public const int BudgetPilotNotEnabledForCustomer = 4900;

        /// <summary>
        /// The budget list should not be null or empty.
        /// </summary>
        public const int BudgetsNullOrEmpty = 4901;

        /// <summary>
        /// The budget should not be null.
        /// </summary>
        public const int BudgetIsNull = 4902;

        /// <summary>
        /// The budget id is read-only and must be null.
        /// </summary>
        public const int BudgetIdShouldBeNullOnAdd = 4903;

        /// <summary>
        /// The budget name should not be null or empty.
        /// </summary>
        public const int BudgetNameMissing = 4904;

        /// <summary>
        /// The budget name is too long.
        /// </summary>
        public const int BudgetNameTooLong = 4905;

        /// <summary>
        /// The budget name has invalid characters.
        /// </summary>
        public const int BudgetNameInvalid = 4906;

        /// <summary>
        /// The budget name already exists.
        /// </summary>
        public const int DuplicateBudgetName = 4907;

        /// <summary>
        /// The budget type must be specified.
        /// </summary>
        public const int BudgetTypeCannotBeNullOnAdd = 4908;

        /// <summary>
        /// Monthly budget type is not a valid Shared Budget type.
        /// </summary>
        public const int MonthlyBudgetNotAllowed = 4909;

        /// <summary>
        /// The budget amount is not provided.
        /// </summary>
        public const int BudgetAmountMissing = 4910;

        /// <summary>
        /// The budget amount is above the limit.
        /// </summary>
        public const int BudgetAmountIsAboveLimit = 4911;

        /// <summary>
        /// The budget amount is below the limit.
        /// </summary>
        public const int BudgetAmountIsBelowLimit = 4912;

        /// <summary>
        /// The budget list exceeds the maximum batch size.
        /// </summary>
        public const int BudgetBatchLimitExceeded = 4913;

        /// <summary>
        /// The number of budgets for the account has been exceeded.
        /// </summary>
        public const int BudgetEntityLimitExceeded = 4914;

        /// <summary>
        /// The budget list contains duplicate budget ids.
        /// </summary>
        public const int DuplicateBudgetId = 4915;

        /// <summary>
        /// The budget id is invalid.
        /// </summary>
        public const int BudgetIdInvalid = 4916;

        /// <summary>
        /// The campaign is using a shared daily budget, therefore it can't be updated to use an unshared monthly budget. You can use an unshared daily budget instead.
        /// </summary>
        public const int CannotUpdateSharedDailyBudgetToUnsharedMonthlyBudget = 4917;

        /// <summary>
        /// The budget is shared with at least one campaign, therefore it can't be deleted.
        /// </summary>
        public const int BudgetIsSharedWithCampaigns = 4918;

        /// <summary>
        /// The campaign associated with a Performance Target cannot use a shared budget.
        /// </summary>
        public const int SharedBudgetNotAllowedWithPerformanceTargetCampaign = 4919;

        /// <summary>
        /// Customer Not Eligible For Dynamic Search Ads
        /// </summary>
        public const int CustomerNotEligibleForDynamicSearchAds = 5100;

        /// <summary>
        /// The domain name and language settings of a Dynamic Search Ads campaign cannot be updated.
        /// </summary>
        public const int DynamicSearchAdsCampaignSettingNotAllowedForUpdate = 5101;

        /// <summary>
        /// Domain name of Dynamic Search Ads campaign is invalid
        /// </summary>
        public const int InvalidDynamicSearchAdsCampaignDomainName = 5102;

        /// <summary>
        /// Language of Dynamic Search Ads campaign is invalid
        /// </summary>
        public const int InvalidDynamicSearchAdsCampaignLanguage = 5103;

        /// <summary>
        /// Max campaign webpage criterion limit exceeded for campaign
        /// </summary>
        public const int MaxCampaignWebpageCriterionsLimitExceededForCampaign = 5104;

        /// <summary>
        /// Cannot update biddable criterion to negative and vice versa
        /// </summary>
        public const int BiddableOrNegativeStatusCannotBeUpdated = 5105;

        /// <summary>
        /// Path 1 is over the character limit.
        /// </summary>
        public const int DynamicSearchAdPath1TooLong = 5106;

        /// <summary>
        /// Path 2 is over the character limit.
        /// </summary>
        public const int DynamicSearchAdPath2TooLong = 5107;

        /// <summary>
        /// Path 1 is not valid.
        /// </summary>
        public const int DynamicSearchAdPath1Invalid = 5108;

        /// <summary>
        /// Path 2 is not valid.
        /// </summary>
        public const int DynamicSearchAdPath2Invalid = 5109;

        /// <summary>
        /// The path2 is set without setting path1.
        /// </summary>
        public const int DynamicSearchAdPath2SetWithoutPath1 = 5110;

        /// <summary>
        /// The customer is not in pilot for the Dynamic Search Ads Text Part 2 feature.
        /// </summary>
        public const int DynamicSearchAdTextPart2PilotNotEnabledForCustomer = 5111;

        /// <summary>
        /// Ad text part 2 is over the character limit.
        /// </summary>
        public const int DynamicSearchAdTextPart2TooLong = 5112;

        /// <summary>
        /// Ad text part 2 is not valid.
        /// </summary>
        public const int DynamicSearchAdTextPart2Invalid = 5113;

        /// <summary>
        /// The customer is not enabled for the DSA domain languages Phase 2 pilot program.
        /// </summary>
        public const int DSADomainLanguagesPhase2PilotNotEnabledForCustomer = 5114;

        /// <summary>
        /// The ad group type field of the ad group is invalid.
        /// </summary>
        public const int AdGroupTypeInvalid = 5115;

        /// <summary>
        /// The ad group type field of an ad group cannot be updated.
        /// </summary>
        public const int AdGroupTypeImmutable = 5116;

        /// <summary>
        /// The account is not in the mixed mode campaign pilot.
        /// </summary>
        public const int AccountNotInPilotForMixedModeCampaign = 5117;

        /// <summary>
        /// The ad group type is not valid for the campaign type.
        /// </summary>
        public const int InvalidAdGroupTypeForCampaignType = 5118;

        /// <summary>
        /// Dynamic search ad groups cannot be added to campaigns without dynamic search settings.
        /// </summary>
        public const int CannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings = 5119;

        /// <summary>
        /// Dynamic search ads cannot be added to non dynamic search ad groups.
        /// </summary>
        public const int DynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup = 5120;

        /// <summary>
        /// The ad type is not valid for the ad group type.
        /// </summary>
        public const int AdTypeInvalidForAdGroup = 5121;

        /// <summary>
        /// The criterion is not valid for the campaign type.
        /// </summary>
        public const int InvalidCriterionForCampaignType = 5122;

        /// <summary>
        /// The criterion is not valid for the ad group type.
        /// </summary>
        public const int InvalidCriterionForAdGroupType = 5123;

        /// <summary>
        /// Dynamic search ads are not supported for disclaimer campaigns.
        /// </summary>
        public const int DynamicSearchAdsNotSupportedForDisclaimerCampaign = 5124;

        /// <summary>
        /// The entity is not allowed for dynamic search ad groups.
        /// </summary>
        public const int EntityNotAllowedForDynamicSearchAdsAdGroup = 5125;

        /// <summary>
        /// The customer is not enabled for the DSA domain languages Phase 3 pilot program.
        /// </summary>
        public const int DSADomainLanguagesPhase3PilotNotEnabledForCustomer = 5126;

        /// <summary>
        /// The customer is not enabled for the DSA domain languages Phase 4 pilot program.
        /// </summary>
        public const int DSADomainLanguagesPhase4PilotNotEnabledForCustomer = 5127;

        /// <summary>
        /// Criterion cannot be added to campaigns without dynamic search settings.
        /// </summary>
        public const int CannotAddCriterionToCampaignWithoutDynamicSearchSettings = 5128;

        /// <summary>
        /// The maximum number of auto targets per account is exceeded.
        /// </summary>
        public const int MaxDSAAutoTargetPerAccountLimitReached = 5129;

        /// <summary>
        /// Dynamic search ad campaign creation is no longer allowed. Please create a search campaign with DSA settings instead.
        /// </summary>
        public const int DynamicSearchAdCampaignCreationNotAllowed = 5130;

        /// <summary>
        /// Account is not eligible for Dynamic Description in Dynamic Search Ads.
        /// </summary>
        public const int AccountNotEligibleForDynamicDescription = 5131;

        /// <summary>
        /// Corresponding CampaignType is not Dynamic Search Ads campaign
        /// </summary>
        public const int CampaignTypeIsNotDynamicSearchAdsCampaign = 5200;

        /// <summary>
        /// Parameter of webpage criterion is null
        /// </summary>
        public const int WebpageCriterionParameterIsNull = 5201;

        /// <summary>
        /// Criterion name of webpage criterion is invalid
        /// </summary>
        public const int WebpageCriterionNameInvalid = 5202;

        /// <summary>
        /// Conditions of webpage criterion contain duplicate values
        /// </summary>
        public const int WebpageCriterionConditionsContainDuplicateValues = 5203;

        /// <summary>
        /// Number of criterion conditions exceed limiation
        /// </summary>
        public const int TooManyWebpageCriterionConditions = 5204;

        /// <summary>
        /// Webpage criterion condition is invalid
        /// </summary>
        public const int WebpageCriterionConditionInvalid = 5205;

        /// <summary>
        /// Operand of webpage criterion condition is invalid
        /// </summary>
        public const int WebpageCriterionWebpageConditionOperandInvalid = 5206;

        /// <summary>
        /// Argument of webpage criterion condition is invalid
        /// </summary>
        public const int WebpageCriterionWebpageConditionArgumentInvalid = 5207;

        /// <summary>
        /// Negative webpage criterion condition is null or empty
        /// </summary>
        public const int NegativeWebpageCriterionConditionIsNullOrEmpty = 5208;

        /// <summary>
        /// Webpage criterion condition cannot be updated
        /// </summary>
        public const int CannotUpdateCriterionForWebpageCriterion = 5209;

        /// <summary>
        /// The bidding scheme of webpage criterion must inherit from parent entity
        /// </summary>
        public const int BiddingSchemeMustInheritFromParentEntity = 5210;

        /// <summary>
        /// Dynamic Search Ads do not support keyword substitution
        /// </summary>
        public const int SubstitutionNotSupportedForDynamicSearchAd = 5211;

        /// <summary>
        /// Webpage criterion already exists
        /// </summary>
        public const int WebpageCriterionAlreadyExists = 5212;

        /// <summary>
        /// You cannot set Final URLs for a webpage criterion. Webpage criterion only support tracking template and custom parameters for Upgraded URLs.
        /// </summary>
        public const int InvalidUpgradedUrlsForWebpageCriterion = 5213;

        /// <summary>
        /// Operator of webpage criterion condition is invalid
        /// </summary>
        public const int WebpageCriterionWebpageConditionOperatorInvalid = 5214;

        /// <summary>
        /// Url Equals value should use the same domain you used in your campaign settings.
        /// </summary>
        public const int WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain = 5215;

        /// <summary>
        /// Account is not in Pilot for webpage criterion webpage condition operator or URL Equals
        /// </summary>
        public const int AccountIsNotInPilotForWebpageCriterionWebpageConditionOperatorOrUrlEquals = 5216;

        /// <summary>
        /// The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options.
        /// </summary>
        public const int WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter = 5217;

        /// <summary>
        /// The UET Tag ID is invalid.
        /// </summary>
        public const int InvalidUetTagId = 5300;

        /// <summary>
        /// Duplicate UET Tag IDs are not allowed.
        /// </summary>
        public const int DuplicateUetTagId = 5301;

        /// <summary>
        /// Duplicate UET Tag names are not allowed.
        /// </summary>
        public const int DuplicateUetTagName = 5302;

        /// <summary>
        /// The UET Tag with the same name already exists.
        /// </summary>
        public const int UetTagNameAlreadyExists = 5303;

        /// <summary>
        /// The list of UET Tag IDs exceeds the limit.
        /// </summary>
        public const int UetTagIdsExceedsLimit = 5304;

        /// <summary>
        /// The UET Tag name is required.
        /// </summary>
        public const int UetTagNameNotPassed = 5305;

        /// <summary>
        /// The UET Tag name is empty or exceeds the length limit.
        /// </summary>
        public const int InvalidUetTagName = 5306;

        /// <summary>
        /// The UET Tag description exceeds the length limit.
        /// </summary>
        public const int InvalidUetTagDescription = 5307;

        /// <summary>
        /// The UET Tags are required.
        /// </summary>
        public const int UetTagsNotPassed = 5308;

        /// <summary>
        /// The list of UET Tags exceeds the limit.
        /// </summary>
        public const int UetTagArrayExceedsLimit = 5309;

        /// <summary>
        /// The UET Tag is required.
        /// </summary>
        public const int UetTagIsNull = 5310;

        /// <summary>
        /// The UET Tag ID should be null.
        /// </summary>
        public const int UetTagIdIsNotNull = 5311;

        /// <summary>
        /// The UET Tag ID is required.
        /// </summary>
        public const int UetTagIdIsNull = 5312;

        /// <summary>
        /// The total UET Tag count would exceed the limit.
        /// </summary>
        public const int TotalUetTagsExceedLimit = 5313;

        /// <summary>
        /// The Conversion Goal ID is invalid.
        /// </summary>
        public const int InvalidConversionGoalId = 5314;

        /// <summary>
        /// Duplicate Conversion Goal IDs are not allowed.
        /// </summary>
        public const int DuplicateConversionGoalId = 5315;

        /// <summary>
        /// Duplicate Conversion Goal names are not allowed.
        /// </summary>
        public const int DuplicateConversionGoalName = 5316;

        /// <summary>
        /// The Conversion Goal with the same name already exists for the customer.
        /// </summary>
        public const int ConversionGoalNameAlreadyExists = 5317;

        /// <summary>
        /// The list of Conversion Goal IDs exceeds the limit.
        /// </summary>
        public const int ConversionGoalIdArrayExceedsLimit = 5318;

        /// <summary>
        /// The Conversion Goal name is required.
        /// </summary>
        public const int ConversionGoalNameNotPassed = 5319;

        /// <summary>
        /// The Conversion Goal name is invalid or exceeds the length limit.
        /// </summary>
        public const int InvalidConversionGoalName = 5320;

        /// <summary>
        /// The Conversion Goals are required.
        /// </summary>
        public const int ConversionGoalsNotPassed = 5321;

        /// <summary>
        /// The list of Conversion Goals exceeds the limit.
        /// </summary>
        public const int ConversionGoalArrayExceedsLimit = 5322;

        /// <summary>
        /// The Conversion Goal is required.
        /// </summary>
        public const int ConversionGoalIsNull = 5323;

        /// <summary>
        /// The Conversion Goal ID must be null.
        /// </summary>
        public const int ConversionGoalIdIsNotNull = 5324;

        /// <summary>
        /// The Conversion Goal ID is required.
        /// </summary>
        public const int ConversionGoalIdIsNull = 5325;

        /// <summary>
        /// The total Conversion Goal count would exceed the account level limit.
        /// </summary>
        public const int TotalConversionGoalsExceedAccountLimit = 5326;

        /// <summary>
        /// The total Conversion Goal count would exceed the customer level limit.
        /// </summary>
        public const int TotalConversionGoalsExceedCustomerLimit = 5327;

        /// <summary>
        /// The Conversion Goal status is invalid.
        /// </summary>
        public const int InvalidConversionGoalStatus = 5328;

        /// <summary>
        /// The Conversion Goal types are required.
        /// </summary>
        public const int ConversionGoalTypesNotPassed = 5329;

        /// <summary>
        /// The UET Tag IDs are required.
        /// </summary>
        public const int UetTagIdsNotPassed = 5330;

        /// <summary>
        /// The Conversion Goal type should be matched to the goal entity.
        /// </summary>
        public const int ConversionGoalTypeNotMatched = 5331;

        /// <summary>
        /// The Conversion Goal revenue type is invalid.
        /// </summary>
        public const int InvalidConversionGoalRevenueType = 5332;

        /// <summary>
        /// The Conversion Goal revenue value is invalid.
        /// </summary>
        public const int InvalidConversionGoalRevenueValue = 5333;

        /// <summary>
        /// The Url Expression for Url Goal is required.
        /// </summary>
        public const int UrlGoalUrlExpressionNotPassed = 5334;

        /// <summary>
        /// The Store ID for App Install Goal is required.
        /// </summary>
        public const int AppInstallGoalStoreIdNotPassed = 5335;

        /// <summary>
        /// At least one expression and operator pair is required for an Event Goal.
        /// </summary>
        public const int EventGoalExpressionWithOperatorNotPassed = 5336;

        /// <summary>
        /// The Conversion Window for Conversion Goal is invalid.
        /// </summary>
        public const int InvalidConversionGoalConversionWindow = 5337;

        /// <summary>
        /// The Duration Time for Duration Goal is invalid.
        /// </summary>
        public const int InvalidDurationGoalDurationTime = 5338;

        /// <summary>
        /// The Minimum Pages Viewed is invalid for the goal.
        /// </summary>
        public const int InvalidMinimumPagesViewedForGoal = 5339;

        /// <summary>
        /// The App Platform for App Install Goal is invalid.
        /// </summary>
        public const int InvalidAppInstallGoalAppPlatform = 5340;

        /// <summary>
        /// The Url Expression for Url Goal is empty or exceeds the length limit.
        /// </summary>
        public const int InvalidUrlGoalUrlExpression = 5341;

        /// <summary>
        /// The Category Expression for Event Goal is empty or exceeds the length limit.
        /// </summary>
        public const int InvalidEventGoalCategoryExpression = 5342;

        /// <summary>
        /// The Action Expression for Event Goal is empty or exceeds the length limit.
        /// </summary>
        public const int InvalidEventGoalActionExpression = 5343;

        /// <summary>
        /// The Label Expression for Event Goal is empty or exceeds the length limit.
        /// </summary>
        public const int InvalidEventGoalLabelExpression = 5344;

        /// <summary>
        /// The Value for Event Goal is invalid.
        /// </summary>
        public const int InvalidEventGoalValue = 5345;

        /// <summary>
        /// The Store ID for App Install Goal is invalid.
        /// </summary>
        public const int InvalidAppInstallGoalStoreId = 5346;

        /// <summary>
        /// The requested conversion goal types do not match the existing conversion goal type.
        /// </summary>
        public const int ConversionGoalTypesDoNotMatchExistingValue = 5347;

        /// <summary>
        /// App Install Goal should scope to the customer.
        /// </summary>
        public const int InvalidAppInstallGoalScope = 5348;

        /// <summary>
        /// The count type for App Install Goal should be All.
        /// </summary>
        public const int InvalidAppInstallGoalCountType = 5349;

        /// <summary>
        /// The currency code of the goal is invalid.
        /// </summary>
        public const int InvalidGoalCurrencyCode = 5351;

        /// <summary>
        /// The currency code is not supported for this goal type.
        /// </summary>
        public const int GoalCurrencyCodeIsNotNull = 5352;

        /// <summary>
        /// The currency code is required for this goal type.
        /// </summary>
        public const int GoalCurrencyCodeIsNull = 5355;

        /// <summary>
        /// The goal type can't be changed for In-Store Transaction Goal or Offline Conversion Goal.
        /// </summary>
        public const int GoalTypeCannotBeChanged = 5356;

        /// <summary>
        /// In-Store Transaction goals must be set to Customer level scope.
        /// </summary>
        public const int InStoreTransactionGoalScopeInvalid = 5357;

        /// <summary>
        /// Only one In-Store Transaction Goal can be created per customer.
        /// </summary>
        public const int OnlyOneInStoreTransactionGoalAllowedPerCustomer = 5358;

        /// <summary>
        /// In-Store Transaction pilot not enabled for current customer.
        /// </summary>
        public const int InStoreTransactionPilotNotEnabledForCustomer = 5359;

        /// <summary>
        /// The customer scope is not supported for this conversion goal type.
        /// </summary>
        public const int CustomerScopeNotSupportedForConversionGoalType = 5360;

        /// <summary>
        /// Cannot update criterion status due to the UET Tag of the associated audience is not available.
        /// </summary>
        public const int CannotUpdateCriterionStatusDueToTagNotAvailable = 5361;

        /// <summary>
        /// Customer Not Eligible For External Attribution.
        /// </summary>
        public const int CustomerNotEligibleForExternalAttribution = 5362;

        /// <summary>
        /// The property IsExternallyAttributed can not be changed.
        /// </summary>
        public const int IsExternallyAttributedCannotBeChanged = 5363;

        /// <summary>
        /// The conversion goal level cannot be changed. Please create a new goal.
        /// </summary>
        public const int GoalLevelCannotBeChanged = 5364;

        /// <summary>
        /// The conversion goal level cannot be downgraded once it's been created. Please create a new goal.
        /// </summary>
        public const int GoalLevelCannotBeDowngraded = 5365;

        /// <summary>
        /// Tag tracking code length doesn't match requirements.
        /// </summary>
        public const int InvalidTagTrackingCode = 8500;

        /// <summary>
        /// Invalid tag status.
        /// </summary>
        public const int InvalidTagStatus = 8501;

        /// <summary>
        /// Too many tags passed in.
        /// </summary>
        public const int TagsBatchSizeExceesdLimit = 8502;

        /// <summary>
        /// Invalid destination goal expression operator.
        /// </summary>
        public const int InvalidDestinationGoalExpressionOperator = 8503;

        /// <summary>
        /// Invalid duration goal value operator.
        /// </summary>
        public const int InvalidDurationGoalValueOperator = 8504;

        /// <summary>
        /// Invalid event goal category operator.
        /// </summary>
        public const int InvalidEventGoalCategoryOperator = 8505;

        /// <summary>
        /// Invalid event goal action operator.
        /// </summary>
        public const int InvalidEventGoalActionOperator = 8506;

        /// <summary>
        /// Invalid event goal label operator.
        /// </summary>
        public const int InvalidEventGoalLabelOperator = 8507;

        /// <summary>
        /// Invalid event goal value operator.
        /// </summary>
        public const int InvalidEventGoalValueOperator = 8508;

        /// <summary>
        /// Invalid page views per visit value operator.
        /// </summary>
        public const int InvalidPageViewsPerVisitValueOperator = 8509;

        /// <summary>
        /// Invalid product conversion goal.
        /// </summary>
        public const int InvalidProductConversionGoal = 8511;

        /// <summary>
        /// Attribution Model Type cannot be updated.
        /// </summary>
        public const int AttributionModelTypeCannotBeUpdated = 8514;

        /// <summary>
        /// Smart Goal Should Be Account Level.
        /// </summary>
        public const int SmartGoalShouldBeAccountLevel = 8517;

        /// <summary>
        /// Smart Goal should be only one for one account.
        /// </summary>
        public const int SmartGoalShouldBeOnlyOne = 8518;

        /// <summary>
        /// Smart Goal could not be created by customer.
        /// </summary>
        public const int SmartGoalCouldNotBeCreatedByCustomer = 8520;

        /// <summary>
        /// Only one in-store transaction goal be allowed per customer.
        /// </summary>
        public const int OnlyOneInStoreTransactionGoalBeAllowedPerCustomer = 8521;

        /// <summary>
        /// Smart Goal is not available for the account.
        /// </summary>
        public const int AccountNotEligibleForSmartGoal = 8522;

        /// <summary>
        /// The customer is not enabled for campaign languages.
        /// </summary>
        public const int CampaignLanguagesNotEnabledForCustomer = 5350;

        /// <summary>
        /// All campaign languages cannot be removed.
        /// </summary>
        public const int AllCampaignLanguagesCannotBeRemoved = 5353;

        /// <summary>
        /// Campaign language specified more than once.
        /// </summary>
        public const int CampaignLanguageSpecifiedMoreThanOnce = 5354;

        /// <summary>
        /// You must include one or more labels.
        /// </summary>
        public const int LabelsListIsNullOrEmpty = 5400;

        /// <summary>
        /// You cannot add any more labels to the account.
        /// </summary>
        public const int LabelsEntityLimitExceeded = 5401;

        /// <summary>
        /// The label is required.
        /// </summary>
        public const int LabelIsNull = 5402;

        /// <summary>
        /// Duplicate label identifiers cannot be included in the same request.
        /// </summary>
        public const int DuplicateLabelId = 5404;

        /// <summary>
        /// The label identifier is invalid.
        /// </summary>
        public const int LabelIdInvalid = 5405;

        /// <summary>
        /// The label name is invalid.
        /// </summary>
        public const int LabelNameInvalid = 5406;

        /// <summary>
        /// The length of the label name is too long.
        /// </summary>
        public const int LabelNameLengthExceeded = 5407;

        /// <summary>
        /// Duplicate label names are not allowed in the same account.
        /// </summary>
        public const int LabelNameDuplicate = 5408;

        /// <summary>
        /// The length of the label description is too long.
        /// </summary>
        public const int LabelDescriptionLengthExceeded = 5409;

        /// <summary>
        /// The label color code is invalid.
        /// </summary>
        public const int LabelColorCodeInvalid = 5410;

        /// <summary>
        /// The label description is invalid.
        /// </summary>
        public const int LabelDescriptionInvalid = 5411;

        /// <summary>
        /// The customer is not enabled for the labels feature.
        /// </summary>
        public const int LabelPilotNotEnabledForCustomer = 5412;

        /// <summary>
        /// You must include one or more label associations.
        /// </summary>
        public const int LabelAssociationListIsNullOrEmpty = 5413;

        /// <summary>
        /// The maximum number of label associations per request is exceeded.
        /// </summary>
        public const int LabelAssociationsBatchLimitExceeded = 5414;

        /// <summary>
        /// The label association is required.
        /// </summary>
        public const int LabelAssociationIsNull = 5415;

        /// <summary>
        /// Duplicate label associations are not allowed.
        /// </summary>
        public const int DuplicateLabelAssociation = 5416;

        /// <summary>
        /// You cannot associate any more labels with the entity.
        /// </summary>
        public const int LabelAssociationsPerEntityLimitExceeded = 5417;

        /// <summary>
        /// The label association does not exist.
        /// </summary>
        public const int LabelAssociationDoesNotExist = 5418;

        /// <summary>
        /// The limit of label and campaign associations for the account would be exceeded.
        /// </summary>
        public const int LabelCampaignAssociationsAccountLimitExceeded = 5419;

        /// <summary>
        /// The limit of label and ad group associations for the account would be exceeded.
        /// </summary>
        public const int LabelAdGroupAssociationsAccountLimitExceeded = 5420;

        /// <summary>
        /// The limit of label and ad associations for the account would be exceeded.
        /// </summary>
        public const int LabelAdAssociationsAccountLimitExceeded = 5421;

        /// <summary>
        /// The limit of label and keyword associations for the account would be exceeded.
        /// </summary>
        public const int LabelKeywordAssociationsAccountLimitExceeded = 5422;

        /// <summary>
        /// The maximum number of labels per request is exceeded.
        /// </summary>
        public const int LabelBatchLimitExceeded = 5423;

        /// <summary>
        /// Labels cannot be associated with this entity type.
        /// </summary>
        public const int LabelAssociationEntityTypeInvalid = 5424;

        /// <summary>
        /// The label association contains an invalid entity ID.
        /// </summary>
        public const int LabelAssociationEntityIdInvalid = 5425;

        /// <summary>
        /// The maximum number of label ids per request is exceeded.
        /// </summary>
        public const int LabelIdsBatchLimitExceeded = 5426;

        /// <summary>
        /// Price tables rows of a price ad extension cannot contain duplicate header values.
        /// </summary>
        public const int DuplicateHeaders = 5500;

        /// <summary>
        /// Price Ad Extension must have at least 3 price table rows.
        /// </summary>
        public const int TooFewPriceTableRows = 5501;

        /// <summary>
        /// Cannot set negative price amount in price table row.
        /// </summary>
        public const int NegativePrice = 5502;

        /// <summary>
        /// The currency is not supported.
        /// </summary>
        public const int CurrencyCodeNotSupported = 5503;

        /// <summary>
        /// The list of offline conversions cannot be null or empty.
        /// </summary>
        public const int OfflineConversionsNullOrEmpty = 5600;

        /// <summary>
        /// The list of offline conversions exceeds the limit.
        /// </summary>
        public const int OfflineConversionsLimitExceeded = 5601;

        /// <summary>
        /// The offline conversion cannot be null.
        /// </summary>
        public const int OfflineConversionIsNull = 5602;

        /// <summary>
        /// The Microsoft Click Id of offline conversion cannot be null or empty.
        /// </summary>
        public const int OfflineConversionMicrosoftClickIdNullOrEmpty = 5603;

        /// <summary>
        /// The  Microsoft Click Id of offline conversion is invalid.
        /// </summary>
        public const int OfflineConversionMicrosoftClickIdInvalid = 5604;

        /// <summary>
        /// The conversion name of offline conversion cannot be null or empty.
        /// </summary>
        public const int OfflineConversionNameNullOrEmpty = 5605;

        /// <summary>
        /// The conversion name do not match an existing offline conversion goal.
        /// </summary>
        public const int OfflineConversionNameInvalid = 5606;

        /// <summary>
        /// The conversion time of offline conversion is invalid.
        /// </summary>
        public const int OfflineConversionTimeInvalid = 5607;

        /// <summary>
        /// The conversion time of offline conversion cannot be null or empty .
        /// </summary>
        public const int OfflineConversionTimeNullOrEmpty = 5608;

        /// <summary>
        /// A conversion time must have occurred within the past 90 days.
        /// </summary>
        public const int OfflineConversionTimeOutOfWindow = 5609;

        /// <summary>
        /// The conversion value of offline conversion is invalid.
        /// </summary>
        public const int OfflineConversionValueInvalid = 5610;

        /// <summary>
        /// The currency code of offline conversion is invalid.
        /// </summary>
        public const int OfflineConversionCurrencyCodeInvalid = 5611;

        /// <summary>
        /// Failed to send offline conversions to internal server.
        /// </summary>
        public const int OfflineConversionsApplyFailed = 5612;

        /// <summary>
        /// A future conversion time cannot be set.
        /// </summary>
        public const int FutureConversionTimeCannotBeSet = 5614;

        /// <summary>
        /// Offline conversions cannot be accepted for the conversion goal at this time. You must wait 2 hours after the goal was created before uploading offline conversions.
        /// </summary>
        public const int OfflineConversionNotAcceptedForGoal = 5615;

        /// <summary>
        /// The conversion time must be later than the click time.
        /// </summary>
        public const int ConversionTimeEarlierThanClickTime = 5616;

        /// <summary>
        /// The date and time for the click ID is outside the conversion window for this goal.
        /// </summary>
        public const int ClickIdDateTimeOutsideGoalConversionWindow = 5617;

        /// <summary>
        /// The offline conversion adjustment type is invalid
        /// </summary>
        public const int OfflineConversionInvalidAdjustmentType = 5618;

        /// <summary>
        /// The offline conversion adjustment time is empty
        /// </summary>
        public const int OfflineConversionAdjustmentTimeNullOrEmpty = 5619;

        /// <summary>
        /// The offline conversion adjustment time is earlier than conversion time
        /// </summary>
        public const int OfflineConversionAdjustmentTimeEarlierThanConversionTime = 5620;

        /// <summary>
        /// The offline conversion adjustment time is invalid
        /// </summary>
        public const int OfflineConversionAdjustmentTimeInvalid = 5621;

        /// <summary>
        /// The offline conversion adjustment time cannot be in the future
        /// </summary>
        public const int OfflineConversionFutureAdjustmentTimeCannotBeSet = 5622;

        /// <summary>
        /// The offline conversion adjustment time is out of the window
        /// </summary>
        public const int OfflineConversionAdjustmentTimeOutOfWindow = 5623;

        /// <summary>
        /// The offline conversion adjustment value is not expected
        /// </summary>
        public const int OfflineConversionAdjustmentValueNotExpected = 5624;

        /// <summary>
        /// The offline conversion adjustment value is required
        /// </summary>
        public const int OfflineConversionAdjustmentValueRequired = 5625;

        /// <summary>
        /// The offline conversion currency value is required
        /// </summary>
        public const int OfflineConversionCurrencyValueRequired = 5626;

        /// <summary>
        /// The offline conversion restate or retract operation is not supported
        /// </summary>
        public const int OfflineConversionRestateRetractNotSupported = 5627;

        /// <summary>
        /// The Scheduled Offline Conversion pilot must be enabled when creating a Scheduled task
        /// </summary>
        public const int ScheduledOfflineConversionUploadUnableToFetchFile = 5628;

        /// <summary>
        /// The Offline Conversion addition columns are not expected in an Adjustment row
        /// </summary>
        public const int OfflineConversionAdditionColumnsNotExpectedInHeader = 5629;

        /// <summary>
        /// The Offline Conversion adjustment columns are not expected in an Adjustment row
        /// </summary>
        public const int OfflineConversionAdjustmentColumnsNotExpectedInHeader = 5630;

        /// <summary>
        /// The Offline Conversion external attribution fields are invalid.
        /// </summary>
        public const int ExternalAttributionRequiredFieldEmpty  = 65426;

        /// <summary>
        /// The Offline Conversion ExternalAttributionModel value cannot be longer than 100 characters.
        /// </summary>
        public const int ExternalAttributionModelTooLong = 65427;

        /// <summary>
        /// The Offline Conversion ExternalAttributionCredit should be between 0 and 1.
        /// </summary>
        public const int ExternalAttributionCreditValueInvalid = 65428;

        /// <summary>
        /// This Offline Conversion goal is not eligible for external attribution
        /// </summary>
        public const int GoalNotEligibleForExternalAttribution = 65429;

        /// <summary>
        /// Please accept the terms of Enhanced Conversions on the UI first.
        /// </summary>
        public const int ShouldAcceptTermsBeforeUsingEnhancedConversions = 44944;

        /// <summary>
        /// Enhanced conversion is not eligible for this goal.
        /// </summary>
        public const int NotEligibleForEnhancedConversions = 44945;

        /// <summary>
        /// The conversion email address value is not hashed.
        /// </summary>
        public const int ConversionEmailAddressIsNotHashed = 44946;

        /// <summary>
        /// The conversion phone number value is not hashed.
        /// </summary>
        public const int ConversionPhoneNumberIsNotHashed = 44947;

        /// <summary>
        /// The online conversion adjustment cannot be null.
        /// </summary>
        public const int OnlineConversionIsNull = 5650;

        /// <summary>
        /// The online conversion adjustment value is invalid.
        /// </summary>
        public const int OnlineConversionAdjustmentValueInvalid = 5651;

        /// <summary>
        /// The online conversion adjustment currency value is invalid.
        /// </summary>
        public const int OnlineConversionCurrencyCodeInvalid = 5652;

        /// <summary>
        /// The online conversion adjustment time is invalid.
        /// </summary>
        public const int OnlineConversionAdjustmentTimeInvalid = 5653;

        /// <summary>
        /// The online conversion adjustment time cannot be in the future.
        /// </summary>
        public const int OnlineConversionFutureAdjustmentTimeCannotBeSet = 5654;

        /// <summary>
        /// The list of online conversion adjustments cannot be null or empty.
        /// </summary>
        public const int OnlineConversionsNullOrEmpty = 5655;

        /// <summary>
        /// The list of online conversion adjustments exceeds the limit.
        /// </summary>
        public const int OnlineConversionBatchSizeExceedsLimit = 5656;

        /// <summary>
        /// The online conversion adjustment value is not expected.
        /// </summary>
        public const int OnlineConversionAdjustmentValueNotExpected = 5657;

        /// <summary>
        /// The online conversion adjustment value is required.
        /// </summary>
        public const int OnlineConversionAdjustmentValueRequired = 5658;

        /// <summary>
        /// The online conversion adjustment type cannot be empty.
        /// </summary>
        public const int OnlineConversionAdjustmentTypeIsNull = 5659;

        /// <summary>
        /// Invalid column HashedEmailAddress in online conversion file
        /// </summary>
        public const int OnlineConversionHashedEmailAddressNotExpected = 45026;

        /// <summary>
        /// Invalid column HashedPhoneNumber in online conversion file
        /// </summary>
        public const int OnlineConversionHashedPhoneNumberNotExpected = 45027;

        /// <summary>
        /// The online conversion adjustment type is invalid.
        /// </summary>
        public const int OnlineConversionInvalidAdjustmentType = 5660;

        /// <summary>
        /// The conversion name is empty.
        /// </summary>
        public const int OnlineConversionNameNullOrEmpty = 5661;

        /// <summary>
        /// The transaction ID is empty.
        /// </summary>
        public const int TransactionIdIsNull = 5662;

        /// <summary>
        /// The transaction ID is invalid.
        /// </summary>
        public const int TransactionIdIsInvalid = 5663;

        /// <summary>
        /// The time zone is invalid.
        /// </summary>
        public const int OnlineConversionInvalidTimeZone = 5664;

        /// <summary>
        /// The feature Online Conversion Restate/Retract is not enabled.
        /// </summary>
        public const int OnlineConversionNotEnabled = 5665;

        /// <summary>
        /// The conversion name do not match any existing online conversion goal.
        /// </summary>
        public const int OnlineConversionNameNotFound = 5666;

        /// <summary>
        /// Can not have other goals that use same tag and category with an auto goal.
        /// </summary>
        public const int SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal = 5667;

        /// <summary>
        /// Event goal parameter can not be assigned for auto goal.
        /// </summary>
        public const int InvalidEventParameterForAutoGoal = 5668;

        /// <summary>
        /// Only support event type for auto goal.
        /// </summary>
        public const int InvalidGoalTypeForAutoGoal = 5669;

        /// <summary>
        /// Can not change isAutoGoal field.
        /// </summary>
        public const int IsAutoGoalFieldCannotBeChanged = 5670;

        /// <summary>
        /// Can not change event parameter for auto goal.
        /// </summary>
        public const int EventParameterNotAllowToChangeForAutoGoal = 5671;

        /// <summary>
        /// Can not change goal category for auto goal.
        /// </summary>
        public const int GoalCategoryCannotBeChangedForAutoGoal = 5672;

        /// <summary>
        /// Auto goal must have goal category.
        /// </summary>
        public const int MustHaveCategoryForAutoGoal = 5673;

        /// <summary>
        /// Cannot change tag for auto goal.
        /// </summary>
        public const int TagNotAllowToChangeForAutoGoal = 5674;

        /// <summary>
        /// Customer Not Eligible For Audience Campaign.
        /// </summary>
        public const int CustomerNotEligibleForAudienceCampaign = 5700;

        /// <summary>
        /// Campaign Languages should include All.
        /// </summary>
        public const int CampaignLanguageShouldIncludeAll = 5701;

        /// <summary>
        /// The ad group target setting is invalid.
        /// </summary>
        public const int AdGroupInvalidTargetSetting = 5702;

        /// <summary>
        /// Not Supported For This Campaign Type.
        /// </summary>
        public const int NotSupportedForThisCampaignType = 5703;

        /// <summary>
        /// Language for each ad group is not supported. You can set campaign languages.
        /// </summary>
        public const int AdGroupLanguageNotSupported = 5704;

        /// <summary>
        /// The syntax of your function contains invalid formatting, likely caused by a missing }.
        /// </summary>
        public const int InvalidFunctionFormat = 5705;

        /// <summary>
        /// One or more functions are invalid or not supported.
        /// </summary>
        public const int UnknownFunction = 5706;

        /// <summary>
        /// You need to have at least one character between any two functions.
        /// </summary>
        public const int MissingDelimiterBetweenFunctions = 5707;

        /// <summary>
        /// Your countdown function contains an invalid date and/or time.
        /// </summary>
        public const int CountDownInvalidDateTime = 5708;

        /// <summary>
        /// Your countdown function contains an invalid days-before value.
        /// </summary>
        public const int CountDownInvalidDaysBefore = 5709;

        /// <summary>
        /// The days-before value in your countdown function is out of range.
        /// </summary>
        public const int CountDownDaysBeforeOutOfRange = 5710;

        /// <summary>
        /// Your countdown function contains a date and/or time in the past.
        /// </summary>
        public const int CountDownPastDateTime = 5711;

        /// <summary>
        /// Default value is not allowed in countdown function.
        /// </summary>
        public const int CountDownInvalidDefaultText = 5712;

        /// <summary>
        /// Your countdown function contains an invalid language code.
        /// </summary>
        public const int CountDownInvalidLanguageCode = 5713;

        /// <summary>
        /// A countdown function must have at least one parameter and no more than three.
        /// </summary>
        public const int CountDownInvalidParameters = 5714;

        /// <summary>
        /// The call to action is invalid.
        /// </summary>
        public const int InvalidCallToAction = 5715;

        /// <summary>
        /// Extracting display url domain from final urls failed.
        /// </summary>
        public const int AdDisplayUrlDomainExtractionFailed = 5716;

        /// <summary>
        /// Ad modification is not allowed on this campaign.
        /// </summary>
        public const int AdModificationNotAllowedOnThisCampaign = 5717;

        /// <summary>
        /// This is not a valid ProfileType.
        /// </summary>
        public const int InvalidProfileType = 5718;

        /// <summary>
        /// Customer Not Eligible For Product Audience.
        /// </summary>
        public const int CustomerNotEligibleForProductAudience = 5719;

        /// <summary>
        /// Customer Not Eligible For Enhanced Responsive Ad.
        /// </summary>
        public const int CustomerNotEligibleForEnhancedResponsiveAd = 5720;

        /// <summary>
        /// Image is invalid for Responsive Ad
        /// </summary>
        public const int ResponsiveAdInvalidImage = 5721;

        /// <summary>
        /// Image is duplicate for Responsive Ad
        /// </summary>
        public const int ResponsiveAdDuplicateImage = 5722;

        /// <summary>
        /// Image required by Responsive Ad is missing.
        /// </summary>
        public const int ResponsiveAdRequiredImageMissing = 5723;

        /// <summary>
        /// Audience campaign is not eligible to create campaign-level audience association.
        /// </summary>
        public const int AIMCampaignLevelAudienceTargetingNotEnabled = 5724;

        /// <summary>
        /// Account not in Pilot to use this sub type of AIM campaign
        /// </summary>
        public const int NotEligibleForAudienceCampaignSubType = 5725;

        /// <summary>
        /// A location function must have exactly one location level parameter.
        /// </summary>
        public const int LocationFunctionInvalidParameters = 5726;

        /// <summary>
        /// Video is invalid for Responsive Ad
        /// </summary>
        public const int ResponsiveAdInvalidVideo = 5727;

        /// <summary>
        /// Video is duplicate for Responsive Ad
        /// </summary>
        public const int ResponsiveAdDuplicateVideo = 5728;

        /// <summary>
        /// Video required by Responsive Ad is missing.
        /// </summary>
        public const int ResponsiveAdRequiredVideoMissing = 5729;

        /// <summary>
        /// Account is not in pilot for auto bidding for audience network.
        /// </summary>
        public const int AccountNotEligibleForAutoBiddingForAudienceNetwork = 5730;

        /// <summary>
        /// Value for setting include auto bidding for view through conversions is invalid.
        /// </summary>
        public const int IncludeAutoBiddingViewThroughConversionsValueInvalid = 5731;

        /// <summary>
        /// The business attribute value is invalid.
        /// </summary>
        public const int BusinessAttributesValueInvalid = 5732;

        /// <summary>
        /// Account is not in pilot for business attributes.
        /// </summary>
        public const int AccountNotEnabledForBusinessAttributes = 5733;

        /// <summary>
        /// Lead Gen setting is invalid.
        /// </summary>
        public const int InvalidLeadGenSetting = 5734;

        /// <summary>
        /// Campaign Lead Gen setting is immutable.
        /// </summary>
        public const int CampaignLeadGenSettingIsImmutable = 5740;

        /// <summary>
        /// Lead Gen Campaign can only have CPM.
        /// </summary>
        public const int LeadGenCampaignOnlyAllowCPM = 5741;

        /// <summary>
        /// Account is not in pilot for out out from MCM.
        /// </summary>
        public const int AccountNotEligibleForOptOutFromMCM = 5743;

        /// <summary>
        /// Invalid OptOutFromMCM value.
        /// </summary>
        public const int OptOutFromMCMValueInvalid = 5744;

        /// <summary>
        /// Account is not in pilot for out out from Boost Targeting.
        /// </summary>
        public const int AccountNotEligibleForBoostTargeting = 5745;

        /// <summary>
        /// Invalid Boost Placement setting.
        /// </summary>
        public const int InvalidBoostPlacementSetting = 5746;

        /// <summary>
        /// Not allowed to turn off OptimizedTargeting when adgroup has no targets.
        /// </summary>
        public const int EligibleTargetNeededForOptimizedTargetingOptout = 5747;

        /// <summary>
        /// Account is not in pilot for setting FrequencyCap.
        /// </summary>
        public const int AccountNotEligibleForFrequencyCap = 5748;

        /// <summary>
        /// Invalid campaign subtype for FrequencyCap.
        /// </summary>
        public const int InvalidCampaignSubtypeForFrequencyCap = 5749;

        /// <summary>
        /// Invalid FrequencyCap settings
        /// </summary>
        public const int InvalidFrequencyCapSettings = 5750;

        /// <summary>
        /// Unsupported setting in brand awareness video ads.
        /// </summary>
        public const int UnsupportedSettingInBrandAwarenessVideoAds = 5751;

        /// <summary>
        /// Unsupported BitRate in video ads.
        /// </summary>
        public const int InvalidBitRate = 5752;

        /// <summary>
        /// The status of the video is invalid.
        /// </summary>
        public const int ResponsiveAdVideoInvalidStatus = 5753;

        /// <summary>
        /// The width of the video is too small.
        /// </summary>
        public const int ResponsiveAdVideoWidthTooSmall = 5754;

        /// <summary>
        /// The height of the video is too small.
        /// </summary>
        public const int ResponsiveAdVideoHeightTooSmall = 5755;

        /// <summary>
        /// The aspect ratio of the video is invalid.
        /// </summary>
        public const int ResponsiveAdVideoInvalidAspectRatio = 5756;

        /// <summary>
        /// The duration of the video is invalid.
        /// </summary>
        public const int ResponsiveAdVideoInvalidDuration = 5757;

        /// <summary>
        /// The bit rate of the video is too small.
        /// </summary>
        public const int ResponsiveAdVideoBitRateTooSmall = 5758;

        /// <summary>
        /// The source length of the video is too large.
        /// </summary>
        public const int ResponsiveAdVideoSourceLengthTooLarge = 5759;

        /// <summary>
        /// The file format of the video is unsupported.
        /// </summary>
        public const int ResponsiveAdVideoUnsupportedFileFormat = 5760;

        /// <summary>
        /// The value of target bid adjustment is not valid.
        /// </summary>
        public const int InvalidCriterionBidAdjustmentValue = 5761;

        /// <summary>
        /// The optimized targeting is not eligible for BrandAwarenessVideoAds campaign subtype.
        /// </summary>
        public const int OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds = 5762;

        /// <summary>
        /// The DealId is invalid
        /// </summary>
        public const int InvalidDealIdTarget = 5763;

        /// <summary>
        /// Unsupported setting in display ads.
        /// </summary>
        public const int UnsupportedSettingInDisplayAds = 5764;

        /// <summary>
        /// Account not enabled for Display campaign.
        /// </summary>
        public const int AccountNotEnabledForDisplayCampaign = 5765;

        /// <summary>
        /// only CPM bidding Schema is supported for Deal now.
        /// </summary>
        public const int UnsupportedBiddingSchemeForDeal = 5766;

        /// <summary>
        /// Unsupported Age Target For Deal Requriement.
        /// </summary>
        public const int UnsupportedAgeTargetForDeal = 5767;

        /// <summary>
        /// Unsupported Gender Target For Deal Requriement.
        /// </summary>
        public const int UnsupportedGenderTargetForDeal = 5768;

        /// <summary>
        /// Unsupported Location Target For Deal Requriement.
        /// </summary>
        public const int UnsupportedLocationTargetForDeal = 5769;

        /// <summary>
        /// Unsupported Device Target For Deal Requriement.
        /// </summary>
        public const int UnsupportedDeviceTargetForDeal = 5770;

        /// <summary>
        /// Adgroup BidAmount is  less than Deal AskPrice.
        /// </summary>
        public const int AdgroupBidAmountLessThanDealAskPrice = 5771;

        /// <summary>
        /// Invalid AdQuality For Deal Video Requriement.
        /// </summary>
        public const int InvalidAdQualityForDeal = 5772;

        /// <summary>
        /// VerifiedTrackingSettings is not allowed.
        /// </summary>
        public const int VerifiedTrackingSettingsIsNotAllowed = 5773;

        /// <summary>
        /// Empty VerifiedTrackingSettings.
        /// </summary>
        public const int EmptyVerifiedTrackingSettings = 5774;

        /// <summary>
        /// Too many items in VerifiedTrackingSettings.
        /// </summary>
        public const int TooManyItemsInVerifiedTrackingSettings = 5775;

        /// <summary>
        /// VerifiedTrackingSetting Invalid Format.
        /// </summary>
        public const int VerifiedTrackingSettingInvalidFormat = 5776;

        /// <summary>
        /// Video Duration Is Invalid For Deal.
        /// </summary>
        public const int VideoDurationIsInvalidForDeal = 5777;

        /// <summary>
        /// The GenreId is invalid
        /// </summary>
        public const int InvalidGenreIdTarget = 5778;

        /// <summary>
        /// Age Criterion Cannot Be Empty For BrandAwarenessCampaign
        /// </summary>
        public const int AgeCriterionCannotBeEmptyForBrandAwarenessCampaign = 5779;

        /// <summary>
        /// Gender Criterion Cannot Be Empty For BrandAwarenessCampaign
        /// </summary>
        public const int GenderCriterionCannotBeEmptyForBrandAwarenessCampaign = 5780;

        /// <summary>
        /// Gender Criterion Cannot Be Empty For BrandAwarenessCampaign
        /// </summary>
        public const int DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign = 5781;

        /// <summary>
        /// Job And CompanyIMA Cannot Be Used By VideoAds And Display
        /// </summary>
        public const int JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay = 5782;

        /// <summary>
        /// Impression Audience Cannot Be Used By Display
        /// </summary>
        public const int ImpressionAudienceCannotBeUsedByDisplay = 5783;

        /// <summary>
        /// Impression Audience Cannot Be Used By OLV
        /// </summary>
        public const int ImpressionAudienceCannotBeUsedByOLV = 5784;

        /// <summary>
        /// Impression Audience Cannot Be Used By CTV
        /// </summary>
        public const int ImpressionAudienceCannotBeUsedByCTV = 5785;

        /// <summary>
        /// Account not enabled for Multi-Format Ads cannot create Display or Video ads
        /// </summary>
        public const int AccountNotEnabledForMultiFormatAds = 5786;

        /// <summary>
        /// AdSubType cannot be changed after created
        /// </summary>
        public const int AdSubTypeCannotBeChanged = 5787;

        /// <summary>
        /// Deal Campaign Cannot Be changed
        /// </summary>
        public const int DealCampaignIsImmutable = 5788;

        /// <summary>
        /// IsDealCampaign Bit Mask Only Support For Deal Campaign
        /// </summary>
        public const int EntityOnlySupportForDealCampaign = 5789;

        /// <summary>
        /// Deal Campaign must associate deals before attach other sub entity
        /// </summary>
        public const int ShouldAssociateDealBeforeAddAdGroup = 5790;

        /// <summary>
        /// Non-Audience Campaign Impression-Based Remarketing List Cannot Be Used by Non-Audience Campaign
        /// </summary>
        public const int NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign = 5791;

        /// <summary>
        /// Unsupported setting in multi format ads.
        /// </summary>
        public const int UnsupportedSettingInMultiFormatAds = 5792;

        /// <summary>
        /// Not Enabled For HTML5 Asset.
        /// </summary>
        public const int NotEnabledForHTML5Asset = 5793;

        /// <summary>
        /// Customer is not eligible for hotel ads.
        /// </summary>
        public const int CustomerNotEligibleForHotelAds = 5800;

        /// <summary>
        /// There are too many values in request.
        /// </summary>
        public const int TooManyValues = 5801;

        /// <summary>
        /// Duplicate values in request.
        /// </summary>
        public const int DuplicateValues = 5802;

        /// <summary>
        /// The bid multiplier is invalid.
        /// </summary>
        public const int InvalidBidMultiplier = 5803;

        /// <summary>
        /// The property is immutable.
        /// </summary>
        public const int ImmutableProperty = 5804;

        /// <summary>
        /// The sub account is invalid.
        /// </summary>
        public const int InvalidSubAccount = 5805;

        /// <summary>
        /// The max active sub account count has reached limit.
        /// </summary>
        public const int MaxActiveSubAccountsLimitReached = 5806;

        /// <summary>
        /// No default hotel group in sub account.
        /// </summary>
        public const int NoDefaultHotelGroupExists = 5807;

        /// <summary>
        /// Default hotel group not allow to update.
        /// </summary>
        public const int DefaultHotelGroupUpdateNotAllowed = 5808;

        /// <summary>
        /// The association type does not be supported.
        /// </summary>
        public const int UnsupportedAssociationType = 5809;

        /// <summary>
        /// There are duplicate hotel id.
        /// </summary>
        public const int DuplicateHotelId = 5810;

        /// <summary>
        /// Cannot delete hotel group with active associations.
        /// </summary>
        public const int HotelGroupHasActiveAssociations = 5811;

        /// <summary>
        /// The hotel group is invalid.
        /// </summary>
        public const int InvalidHotelGroup = 5812;

        /// <summary>
        /// Customer is not enabled for hotel campaigns.
        /// </summary>
        public const int HotelCampaignNotEnabledForAccount = 5814;

        /// <summary>
        /// MaxPercentCpc must be greater than 0.
        /// </summary>
        public const int MaxPercentCpcLessThanOrEqualToZero = 5815;

        /// <summary>
        /// MaxPercentCpc must not be greater than 1000.
        /// </summary>
        public const int MaxPercentCpcGreaterThanOneThousand = 5816;

        /// <summary>
        /// CommissionRate is required for Commission Bidding Scheme.
        /// </summary>
        public const int CommissionRateIsRequired = 5817;

        /// <summary>
        /// CommissionRate must be greater than 0.
        /// </summary>
        public const int CommissionRateLessThanOrEqualToZero = 5818;

        /// <summary>
        /// CommissionRate must not be greater than 100.
        /// </summary>
        public const int CommissionRateGreaterThanOneHundred = 5819;

        /// <summary>
        /// Switching of Bid Type from PPS to Non PPS is not allowed.
        /// </summary>
        public const int SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed = 5820;

        /// <summary>
        /// For PPS only Supported values for BM are increase by 0% or decrease by 100%
        /// </summary>
        public const int OnlySupportedBMValueIsDecreaseby100OrIncreaseby0 = 5821;

        /// <summary>
        /// PercentCpcBid must be set for applible AdGroups when adding.
        /// </summary>
        public const int MissingPercentCpcBiddingScheme = 5822;

        /// <summary>
        /// HotelAdGroupType must be set to HPA, PPA, or both
        /// </summary>
        public const int InvalidHotelSetting = 5823;

        /// <summary>
        /// Invalid Advance Booking Window Target
        /// </summary>
        public const int InvalidAdvanceBookingWindowTarget = 5824;

        /// <summary>
        /// Invalid Check In Day Target
        /// </summary>
        public const int InvalidCheckInDayTarget = 5825;

        /// <summary>
        /// Invalid Length Of Stay Target
        /// </summary>
        public const int InvalidLengthOfStayTarget = 5826;

        /// <summary>
        /// Invalid Date Selection Type Target
        /// </summary>
        public const int InvalidDateSelectionTypeTarget = 5827;

        /// <summary>
        /// Invalid Check In Date Target
        /// </summary>
        public const int InvalidCheckInDateTarget = 5828;

        /// <summary>
        /// Invalid Advance Booking Window Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out.
        /// </summary>
        public const int InvalidAdvanceBookingWindowTargetBidAdjustment = 5829;

        /// <summary>
        /// Invalid Check In Day Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out.
        /// </summary>
        public const int InvalidCheckInDayTargetBidAdjustment = 5830;

        /// <summary>
        /// Invalid Length Of Stay Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out.
        /// </summary>
        public const int InvalidLengthOfStayTargetBidAdjustment = 5831;

        /// <summary>
        /// Invalid Date Selection Type Target Bid Adjustment. Value must be between -50 and 100.
        /// </summary>
        public const int InvalidDateSelectionTypeTargetBidAdjustment = 5832;

        /// <summary>
        /// Invalid Check In Date Target Bid Adjustment. Value must be between -90 and 900 or -100 to opt out
        /// </summary>
        public const int InvalidCheckInDateTargetBidAdjustment = 5833;

        /// <summary>
        /// Conflict with Advance Booking Window Targets. Targets must not overlap with each other
        /// </summary>
        public const int AdvanceBookingWindowTargetConflict = 5834;

        /// <summary>
        /// Conflict with Length of Stay Targets. Targets must not overlap with each other
        /// </summary>
        public const int LengthOfStayTargetConflict = 5835;

        /// <summary>
        /// Conflict with Check In Date Targets. Targets must not overlap with each other
        /// </summary>
        public const int CheckInDateTargetConflict = 5836;

        /// <summary>
        /// You cannot add this entity to a campaign of type Hotel.
        /// </summary>
        public const int EntityNotAllowedForHotelCampaign = 5837;

        /// <summary>
        /// You cannot change the bidding scheme for hotel adgroups.
        /// </summary>
        public const int CannotSetBiddingSchemeForHotelAdGroup = 5838;

        /// <summary>
        /// Hotel setting can not be changed after hotel adgroup creation
        /// </summary>
        public const int HotelSettingCanNotBeChanged = 5839;

        /// <summary>
        /// Invalid bid type for campaign bidding schema.
        /// </summary>
        public const int InvalidBidTypeForCampaignBiddingScheme = 5840;

        /// <summary>
        /// Campaign is not of hotel campaign type.
        /// </summary>
        public const int CampaignIsNotOfTypeHotel = 5841;

        /// <summary>
        /// Invalid hotelListing type.
        /// </summary>
        public const int InvalidHotelListingType = 5842;

        /// <summary>
        /// HotelListing in adGroupCriterion is null.
        /// </summary>
        public const int AdGroupCriterionHotelListingIsNull = 5843;

        /// <summary>
        /// Invalid hotelListing operand.
        /// </summary>
        public const int InvalidHotelListingOperand = 5844;

        /// <summary>
        /// Invalid hotelListing attribute.
        /// </summary>
        public const int InvalidHotelListingAttribute = 5845;

        /// <summary>
        /// FinalUrl and mobileUrl not allowed for hotel group.
        /// </summary>
        public const int FinalUrlAndMobileUrlNotAllowedForHotelGroup = 5846;

        /// <summary>
        /// Duplicate root node for hotel group tree.
        /// </summary>
        public const int DuplicateRootNodeForHotelGroupTree = 5847;

        /// <summary>
        /// Parent hotel group node does not exist.
        /// </summary>
        public const int ParentHotelGroupNodeDoesNotExist = 5848;

        /// <summary>
        /// Height of hotel group tree exceeeded limit.
        /// </summary>
        public const int HeightOfHotelGroupTreeExceeededLimit = 5849;

        /// <summary>
        /// HotelListing operand under same sub division must be same.
        /// </summary>
        public const int HotelListingOperandUnderSubDivisionMustBeSame = 5850;

        /// <summary>
        /// Duplicate hotelListing.
        /// </summary>
        public const int DuplicateHotelListing = 5851;

        /// <summary>
        /// Invalid hotel group hierarchy.
        /// </summary>
        public const int InvalidHotelGroupHierarchy = 5852;

        /// <summary>
        /// Hotel groups limit exceeded for adGroup.
        /// </summary>
        public const int HotelGroupLimitExceededForAdGroup = 5853;

        /// <summary>
        /// Invalid adGroup criterion rate bid value
        /// </summary>
        public const int InvalidAdGroupCriterionRateBidValue = 5854;

        /// <summary>
        /// Hotel group tree everything else node missing.
        /// </summary>
        public const int HotelGroupEverythingElseMissing = 5855;

        /// <summary>
        /// Hotel group tree location node parent location invalid.
        /// </summary>
        public const int InvalidLocationNodeInvalidParentLocation = 5856;

        /// <summary>
        /// Invalid target type for commission bidding scheme
        /// </summary>
        public const int InvalidTargetTypeForCommisionBiddingScheme = 5857;

        /// <summary>
        /// Customer is not eligible for DSA page feed.
        /// </summary>
        public const int CustomerNotEligibleForDsaPageFeed = 5900;

        /// <summary>
        /// Customer is not eligible for feed service.
        /// </summary>
        public const int CustomerNotEligibleForFeedService = 5901;

        /// <summary>
        /// Duplicate feed associations are not allowed.
        /// </summary>
        public const int DuplicateFeedAssociation = 5902;

        /// <summary>
        /// The feed association does not exist.
        /// </summary>
        public const int FeedAssociationDoesNotExist = 5903;

        /// <summary>
        /// The feed identifier is invalid.
        /// </summary>
        public const int InvalidFeedId = 5904;

        /// <summary>
        /// The feed association count has reached limit.
        /// </summary>
        public const int FeedAssociationLimitationReached = 5905;

        /// <summary>
        /// The definition text of feed attributes is invalid.
        /// </summary>
        public const int InvalidFeedCustomAttributesDefinitionText = 5906;

        /// <summary>
        /// The feed type is not supported for bulk upload.
        /// </summary>
        public const int FeedTypeNotSupportedForBulkUpload = 5907;

        /// <summary>
        /// Dual feed type is currently not supported.
        /// </summary>
        public const int DualFeedNotSupported = 5908;

        /// <summary>
        /// Duplicate Feed Id.
        /// </summary>
        public const int DuplicateFeedId = 5909;

        /// <summary>
        /// Invalid Feed Attribute.
        /// </summary>
        public const int InvalidFeedAttribute = 5911;

        /// <summary>
        /// Duplicate Feed Attribute Name.
        /// </summary>
        public const int DuplicateFeedAttributeName = 5912;

        /// <summary>
        /// Invalid Feed Attribute Type.
        /// </summary>
        public const int InvalidFeedAttributeType = 5913;

        /// <summary>
        /// Schedule Not Allowed For Feed Type.
        /// </summary>
        public const int ScheduleNotAllowedForFeedType = 5914;

        /// <summary>
        /// Url Not Allowed For Feed Type.
        /// </summary>
        public const int UrlNotAllowedForFeedType = 5915;

        /// <summary>
        /// Invalid Feed Attribute Mapping.
        /// </summary>
        public const int InvalidFeedAttributeMapping = 5916;

        /// <summary>
        /// Duplicate Feed Property Id.
        /// </summary>
        public const int DuplicateFeedPropertyId = 5918;

        /// <summary>
        /// Invalid Feed Type.
        /// </summary>
        public const int InvalidFeedType = 5919;

        /// <summary>
        /// Customer is not eligible for Ad Customizer Feed.
        /// </summary>
        public const int CustomerNotEligibleForAdCustomizersFeed = 5920;

        /// <summary>
        /// Target Feed Status Invalid.
        /// </summary>
        public const int TargetFeedStatusInvalid = 5921;

        /// <summary>
        /// Target Feed Invalid.
        /// </summary>
        public const int TargetFeedInvalid = 5922;

        /// <summary>
        /// Invalid FeedItem Attribute Value.
        /// </summary>
        public const int InvalidFeedItemAttributeValue = 5923;

        /// <summary>
        /// Invalid Boolean Feed Item Attribute Value.
        /// </summary>
        public const int InvalidBooleanFeedItemAttributeValue = 5924;

        /// <summary>
        /// Invalid Int64 FeedItem Attribute Value.
        /// </summary>
        public const int InvalidInt64FeedItemAttributeValue = 5925;

        /// <summary>
        /// Invalid Double FeedItem Attribute Value.
        /// </summary>
        public const int InvalidDoubleFeedItemAttributeValue = 5926;

        /// <summary>
        /// Invalid String FeedItem Attribute Value.
        /// </summary>
        public const int InvalidStringFeedItemAttributeValue = 5927;

        /// <summary>
        /// Invalid Url FeedItem Attribute Value.
        /// </summary>
        public const int InvalidUrlFeedItemAttributeValue = 5928;

        /// <summary>
        /// Invalid DateTime FeedItem Attribute Value.
        /// </summary>
        public const int InvalidDateTimeFeedItemAttributeValue = 5929;

        /// <summary>
        /// Invalid Int64 List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidInt64ListFeedItemAttributeValue = 5930;

        /// <summary>
        /// Invalid Double List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidDoubleListFeedItemAttributeValue = 5931;

        /// <summary>
        /// Invalid String List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidStringListFeedItemAttributeValue = 5932;

        /// <summary>
        /// Invalid Boolean List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidBooleanListFeedItemAttributeValue = 5933;

        /// <summary>
        /// Invalid Url List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidUrlListFeedItemAttributeValue = 5934;

        /// <summary>
        /// Invalid DateTime List FeedItem Attribute Value.
        /// </summary>
        public const int InvalidDateTimeListFeedItemAttributeValue = 5935;

        /// <summary>
        /// Invalid Price FeedItem Attribute Value.
        /// </summary>
        public const int InvalidPriceFeedItemAttributeValue = 5936;

        /// <summary>
        /// Duplicate Feed Name.
        /// </summary>
        public const int DuplicateFeedName = 5937;

        /// <summary>
        /// The limitation of FeedItem count reached.
        /// </summary>
        public const int FeedItemMaxLimitReached = 5938;

        /// <summary>
        /// Duplicate Feed Item Id.
        /// </summary>
        public const int DuplicateFeedItemId = 5939;

        /// <summary>
        /// Invalid FeedItem Id.
        /// </summary>
        public const int InvalidFeedItemId = 5941;

        /// <summary>
        /// Account Level Feed limitation reached.
        /// </summary>
        public const int AccountLevelFeedLimitationReached = 5942;

        /// <summary>
        /// Attribute Limitation per Feed reached.
        /// </summary>
        public const int AttributeLimitationPerFeedReached = 5943;

        /// <summary>
        /// Invalid PageFeed label.
        /// </summary>
        public const int InvalidPageFeedLabel = 5944;

        /// <summary>
        /// Too many PageFeed labels.
        /// </summary>
        public const int TooManyPageFeedLabels = 5945;

        /// <summary>
        /// PageFeed label too long.
        /// </summary>
        public const int PageFeedLabelTooLong = 5946;

        /// <summary>
        /// PageFeed url too long.
        /// </summary>
        public const int PageFeedUrlTooLong = 5947;

        /// <summary>
        /// Invalid PageFeed url.
        /// </summary>
        public const int InvalidPageFeedUrl = 5948;

        /// <summary>
        /// Invalid CustomId attribute value.
        /// </summary>
        public const int InvalidCustomIdAttributeValue = 5949;

        /// <summary>
        /// Invalid DevicePreference Attribute Value.
        /// </summary>
        public const int InvalidDevicePreferenceAttributeValue = 5950;

        /// <summary>
        /// Invalid Target AdGroup Attribute Value.
        /// </summary>
        public const int InvalidTargetAdGroupAttributeValue = 5954;

        /// <summary>
        /// Invalid Target AudienceId Attribute Value.
        /// </summary>
        public const int InvalidTargetAudienceIdAttributeValue = 5955;

        /// <summary>
        /// Invalid Target Campaign Attribute Value.
        /// </summary>
        public const int InvalidTargetCampaignAttributeValue = 5956;

        /// <summary>
        /// Invalid Target Keyword Attribute Value.
        /// </summary>
        public const int InvalidTargetKeywordAttributeValue = 5957;

        /// <summary>
        /// Invalid Target Keyword Match Type Attribute Value.
        /// </summary>
        public const int InvalidTargetKeywordMatchTypeAttributeValue = 5958;

        /// <summary>
        /// Invalid Keyword Text Attribute Value.
        /// </summary>
        public const int InvalidTargetKeywordTextAttributeValue = 5959;

        /// <summary>
        /// Invalid Target Location Attribute Value.
        /// </summary>
        public const int InvalidTargetLocationAttributeValue = 5960;

        /// <summary>
        /// Invalid Target Location Restriction Attribute Value.
        /// </summary>
        public const int InvalidTargetLocationRestrictionAttributeValue = 5961;

        /// <summary>
        /// Nested parameter in FeedItem CustomAttribute is not supported.
        /// </summary>
        public const int NestedParameterInCustomAttributeNotSupported = 5962;

        /// <summary>
        /// Duplicate Feed Item Row With Same Key Attributes
        /// </summary>
        public const int KeyFeedItemAttributeValueConfliction = 5963;

        /// <summary>
        /// Feed Attribute cannot use the name of Standard attributes
        /// </summary>
        public const int CannotUseStandardFeedAttributeName = 5964;

        /// <summary>
        /// Feed Attribute cannot use the name of Targeting attributes
        /// </summary>
        public const int CannotUseTargetingFeedAttributeName = 5965;

        /// <summary>
        /// The key property of a feed item cannot be updated
        /// </summary>
        public const int KeyPropertyCannotBeUpdated = 5966;

        /// <summary>
        /// Feed Attribute Key cannot be updated
        /// </summary>
        public const int CannotUpdateFeedAttributeKey = 5967;

        /// <summary>
        /// The Custom Id property should be of string type only.
        /// </summary>
        public const int CustomIdAttributeShouldBeOfStringType = 5968;

        /// <summary>
        /// The string value is too long, please shorten to contain 90 characters or less.
        /// </summary>
        public const int AttributeValueStringTooLong = 5969;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataFeed = 5970;

        /// <summary>
        /// Invalid PageFeed Ad Title.
        /// </summary>
        public const int InvalidPageFeedAdTitle = 5971;

        /// <summary>
        /// PageFeed Ad Title too long.
        /// </summary>
        public const int PageFeedAdTitleTooLong = 5972;

        /// <summary>
        /// Invalid Feed Ids for Association
        /// </summary>
        public const int InvalidFeedIdsForAssociation = 5973;

        /// <summary>
        /// The limit on the maximum number of feed items of current feed type has been reached.
        /// </summary>
        public const int FeedItemCountExceedFeedTypeLevelLimitation = 5974;

        /// <summary>
        /// The feed item contains one or more attribute values exceed maximum length limit.
        /// </summary>
        public const int AttributeValueLengthExceeded = 5975;

        /// <summary>
        /// Provide a value for at least one of the custom attributes
        /// </summary>
        public const int CustomAttributeValuesEmpty = 5976;

        /// <summary>
        /// Target Campaign needed for setting target adgroup
        /// </summary>
        public const int TargetAdgroupWithoutTargetCampaign = 5977;

        /// <summary>
        /// Invalid end date attribute value
        /// </summary>
        public const int InvalidEndDateAttributeValue = 5978;

        /// <summary>
        /// Invalid scheduling attribute value
        /// </summary>
        public const int InvalidSchedulingAttributeValue = 5979;

        /// <summary>
        /// Invalid start date attribute value
        /// </summary>
        public const int InvalidStartDateAttributeValue = 5980;

        /// <summary>
        /// Invalid target location id attribute value
        /// </summary>
        public const int InvalidTargetLocationIdAttributeValue = 5981;

        /// <summary>
        /// Feed item status invalid
        /// </summary>
        public const int InvalidFeedItemLifeCycleStatus = 5982;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Autos Aggregate Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataAutosAggregateFeed = 5983;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Autos Listing Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataAutosListingFeed = 5984;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Credit Cards Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataCreditCardsFeed = 5985;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Cruises Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataCruisesFeed = 5986;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Custom Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataCustomFeed = 5987;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Events Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataEventsFeed = 5988;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Health Insurance Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataHealthInsuranceFeed = 5989;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Hotels And Vacation Rentals Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed = 5990;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Mortgage Lenders Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataMortgageLendersFeed = 5991;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Professional Service Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataProfessionalServiceFeed = 5992;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Tours And Activities Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataToursAndActivitiesFeed = 5993;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Debit Cards Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataDebitCardsFeed = 5994;

        /// <summary>
        /// Customer is not eligible for Dynamic Data Job Listings Feed
        /// </summary>
        public const int CustomerNotEligibleForDynamicDataJobListingsFeed = 5995;

        /// <summary>
        /// Feed is referenced in a campaign
        /// </summary>
        public const int FeedReferencedInCampaign = 5996;

        /// <summary>
        /// Feed is referenced in an ad
        /// </summary>
        public const int FeedReferencedInAd = 5997;

        /// <summary>
        /// The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options.
        /// </summary>
        public const int PageFeedUrlContainsManualTaggingParameters = 5998;

        /// <summary>
        /// The URL contains invalid characters. Please remove all invalid characters and try again.
        /// </summary>
        public const int PageFeedUrlContainsInvalidCharacters = 5999;

        /// <summary>
        /// The experiment list cannot be null or empty.
        /// </summary>
        public const int ExperimentsListIsNullOrEmpty = 6101;

        /// <summary>
        /// The experiment cannot be null.
        /// </summary>
        public const int ExperimentIsNull = 6102;

        /// <summary>
        /// The number of experiments in the request exceeds the limit.
        /// </summary>
        public const int ExperimentsEntityLimitExceeded = 6103;

        /// <summary>
        /// The experiment name cannot be the same as Base Campaign Name.
        /// </summary>
        public const int ExperimentNameIsSameAsBaseCampaignName = 6104;

        /// <summary>
        /// The experiment start date must be earlier than the end date.
        /// </summary>
        public const int ExperimentStartDateGreaterThanEndDate = 6105;

        /// <summary>
        /// The experiment start date must be in the future.
        /// </summary>
        public const int ExperimentStartDateLessThanToday = 6106;

        /// <summary>
        /// The experiment end date must be in the future.
        /// </summary>
        public const int ExperimentEndDateLessThanToday = 6107;

        /// <summary>
        /// The experiment traffic split percent is invalid.
        /// </summary>
        public const int ExperimentTrafficSplitPercentInvalid = 6108;

        /// <summary>
        /// The experiment name cannot be empty.
        /// </summary>
        public const int ExperimentNameIsEmpty = 6109;

        /// <summary>
        /// The experiment ID is invalid.
        /// </summary>
        public const int ExperimentIdInvalid = 6110;

        /// <summary>
        /// Duplicate experiment IDs are not allowed.
        /// </summary>
        public const int DuplicateExperimentIds = 6111;

        /// <summary>
        /// The experiment IDs list cannot be null or empty.
        /// </summary>
        public const int ExperimentIdListNullOrEmpty = 6112;

        /// <summary>
        /// Multiple experiments for the same base campaign cannot have overlapping time interval.
        /// </summary>
        public const int ExperimentTimeperiodOverlapping = 6113;

        /// <summary>
        /// The creation of an experiment for this base campaign is already in progress. Please wait and try again in a few minutes.
        /// </summary>
        public const int ExperimentBaseCampaignIdIsLocked = 6114;

        /// <summary>
        /// The experiment Campaign ID invalid.
        /// </summary>
        public const int ExperimentCampaignIdInvalid = 6115;

        /// <summary>
        /// The maximum number of experiments for this campaign has already been reached.
        /// </summary>
        public const int ExperimentsEntityLimitPerCampaignExceeded = 6116;

        /// <summary>
        /// The customer is not in pilot for the experiments feature.
        /// </summary>
        public const int ExperimentPilotNotEnabledForCustomer = 6117;

        /// <summary>
        /// The experiment status is invalid.
        /// </summary>
        public const int ExperimentStatusInvalid = 6118;

        /// <summary>
        /// The base campaign cannot be an experimental campaign.
        /// </summary>
        public const int ExperimentBaseCampaignIsExperimentCampaign = 6119;

        /// <summary>
        /// The experiment has already started and the start date cannot be changed.
        /// </summary>
        public const int ExperimentStartDateCannotBeChanged = 6120;

        /// <summary>
        /// The experiment has already started and the end date cannot be changed.
        /// </summary>
        public const int ExperimentEndDateCannotBeChanged = 6121;

        /// <summary>
        /// The experiment start date is invalid.
        /// </summary>
        public const int ExperimentStartDateInvalid = 6122;

        /// <summary>
        /// The experiment base campaign ID is invalid.
        /// </summary>
        public const int ExperimentBaseCampaignIdInvalid = 6123;

        /// <summary>
        /// The experiment name cannot be the same as an existing experiment or campaign name.
        /// </summary>
        public const int CampaignOrExperimentWithNameAlreadyExists = 6124;

        /// <summary>
        /// Experiments are not supported for the requested base campaign type.
        /// </summary>
        public const int BaseCampaignTypeInvalid = 6125;

        /// <summary>
        /// The experiment base campaign budget type is invalid.
        /// </summary>
        public const int BaseCampaignBudgetTypeInvalid = 6126;

        /// <summary>
        /// The experiment name is too long.
        /// </summary>
        public const int ExperimentNameTooLong = 6127;

        /// <summary>
        /// The experiment name is invalid
        /// </summary>
        public const int ExperimentNameHasInvalidCharacters = 6128;

        /// <summary>
        /// The experiment campaign is invalid.
        /// </summary>
        public const int ExperimentCampaignInvalid = 6129;

        /// <summary>
        /// The experiment base campaign ID cannot be changed.
        /// </summary>
        public const int ExperimentBaseCampaignIdCannotBeChanged = 6130;

        /// <summary>
        /// The experiment has already started and the traffic split percent cannot be changed.
        /// </summary>
        public const int ExperimentTrafficSplitPercentCannotBeChanged = 6131;

        /// <summary>
        /// The experiment has already ended and cannot be changed.
        /// </summary>
        public const int EndedExperimentCannotBeChanged = 6132;

        /// <summary>
        /// One or more properties of the experiment campaign cannot be updated.
        /// </summary>
        public const int ExperimentCampaignCannotBeUpdated = 6133;

        /// <summary>
        /// A shared budget is not allowed for an experiment's base campaign.
        /// </summary>
        public const int ExperimentBaseCampaignCannotBeChangedToSharedBudget = 6134;

        /// <summary>
        /// The maximum number of experiments per request is exceeded.
        /// </summary>
        public const int ExperimentBatchLimitExceeded = 6135;

        /// <summary>
        /// The experiment type is invalid.
        /// </summary>
        public const int ExperimentTypeInvalid = 6136;

        /// <summary>
        /// The experiment type cannot be changed.
        /// </summary>
        public const int ExperimentTypeCannotBeChanged = 6137;

        /// <summary>
        /// The experiment does not support mixed mode campaign.
        /// </summary>
        public const int ExperimentDoesNotSupportMixedModeCampaign = 6138;

        /// <summary>
        /// DSA Setting can not be added to ExperimentRelatedCampaign.
        /// </summary>
        public const int DSASettingCannotBeAddedToExperimentRelatedCampaign = 6139;

        /// <summary>
        /// The headlines cannot be null or empty.
        /// </summary>
        public const int ResponsiveSearchAdHeadlinesNullOrEmpty = 6200;

        /// <summary>
        /// The descriptions cannot be null or empty.
        /// </summary>
        public const int ResponsiveSearchAdDescriptionsNullOrEmpty = 6201;

        /// <summary>
        /// Duplicate headlines are not allowed.
        /// </summary>
        public const int ResponsiveSearchAdDuplicateHeadlines = 6202;

        /// <summary>
        /// Duplicate descriptions are not allowed.
        /// </summary>
        public const int ResponsiveSearchAdDuplicateDescriptions = 6203;

        /// <summary>
        /// Incorrect pinned field for headline.
        /// </summary>
        public const int ResponsiveSearchAdHeadlinesPinnedFieldMismatch = 6204;

        /// <summary>
        /// Incorrect pinned field for description.
        /// </summary>
        public const int ResponsiveSearchAdDescriptionsPinnedFieldMismatch = 6205;

        /// <summary>
        /// Headline is not valid.
        /// </summary>
        public const int ResponsiveSearchAdInvalidHeadline = 6206;

        /// <summary>
        /// Description is not valid.
        /// </summary>
        public const int ResponsiveSearchAdInvalidDescription = 6207;

        /// <summary>
        /// Headline is too long.
        /// </summary>
        public const int ResponsiveSearchAdHeadlineTooLong = 6208;

        /// <summary>
        /// Description is too long.
        /// </summary>
        public const int ResponsiveSearchAdDescriptionTooLong = 6209;

        /// <summary>
        /// The number of headlines is more than maximum allowed.
        /// </summary>
        public const int ResponsiveSearchAdHeadlinesMaxCountExceeded = 6210;

        /// <summary>
        /// The number of descriptions is more than maximum allowed.
        /// </summary>
        public const int ResponsiveSearchAdDescriptionsMaxCountExceeded = 6211;

        /// <summary>
        /// The minimum number of eligible headlines has not been met. A headline is considered ineligible, for example if it includes a countdown function without a default value.
        /// </summary>
        public const int ResponsiveSearchAdHeadlinesLessThanMinRequired = 6212;

        /// <summary>
        /// The minimum number of eligible descriptions has not been met. A description is considered ineligible, for example if it includes a countdown function without a default value.
        /// </summary>
        public const int ResponsiveSearchAdDescriptionsLessThanMinRequired = 6213;

        /// <summary>
        /// The pinned field of asset link is invalid.
        /// </summary>
        public const int ResponsiveSearchAdPinnedFieldInvalid = 6214;

        /// <summary>
        /// The provided asset type is not valid for this field.
        /// </summary>
        public const int ResponsiveSearchAdAssetTypeInvalidForField = 6215;

        /// <summary>
        /// When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}.
        /// </summary>
        public const int ResponsiveSearchAdDefaultTextRequiredForKeyword = 6216;

        /// <summary>
        /// Path 1 is over the character limit.
        /// </summary>
        public const int ResponsiveSearchAdPath1TooLong = 6217;

        /// <summary>
        /// Path 2 is over the character limit.
        /// </summary>
        public const int ResponsiveSearchAdPath2TooLong = 6218;

        /// <summary>
        /// Path 1 is not valid.
        /// </summary>
        public const int ResponsiveSearchAdPath1Invalid = 6219;

        /// <summary>
        /// Path 2 is not valid.
        /// </summary>
        public const int ResponsiveSearchAdPath2Invalid = 6220;

        /// <summary>
        /// Path2 is set without path1.
        /// </summary>
        public const int ResponsiveSearchAdPath2SetWithoutPath1 = 6221;

        /// <summary>
        /// Display URL (i.e., your Final URL's domain combined with Path1 or Path2, if you've specified them) is too long. Please shorten it. For more details, visit https://help.ads.microsoft.com/apex/index/3/en/53077
        /// </summary>
        public const int ResponsiveSearchAdFinalUrlDomainTooLong = 6222;

        /// <summary>
        /// Display Url domain is too long.
        /// </summary>
        public const int ResponsiveSearchAdDisplayUrlDomainTooLong = 6223;

        /// <summary>
        /// Display Url domain is Invalid.
        /// </summary>
        public const int ResponsiveSearchAdDisplayUrlDomainInvalid = 6224;

        /// <summary>
        /// Ad customizer is not supported for this ad type.
        /// </summary>
        public const int AdCustomizerNotSupportedForAdType = 6225;

        /// <summary>
        /// The number of active Responsive Search Ads per ad group would be exceeded.
        /// </summary>
        public const int MaxActiveResponsiveSearchAdsPerAdgroupLimitReached = 6226;

        /// <summary>
        /// The same ad cannot include both UTC CountDown and Global_CountDown functions.
        /// </summary>
        public const int ResponsiveSearchAdsBothCountDownAndGlobalCountDown = 6227;

        /// <summary>
        /// The number of active Multi Media Ads per ad group would be exceeded.
        /// </summary>
        public const int MaxActiveMultiMediaAdsPerAdgroupLimitReached = 6228;

        /// <summary>
        /// When using the {Keyword} dynamic text parameter, default text is required. For example {Keyword:default}.
        /// </summary>
        public const int ResponsiveAdDefaultTextRequiredForKeyword = 6229;

        /// <summary>
        /// The minimum number of eligible headlines has not been met. A headline is considered ineligible, for example if it includes a countdown function without a default value.
        /// </summary>
        public const int ResponsiveAdHeadlinesLessThanMinRequired = 6230;

        /// <summary>
        /// The minimum number of eligible long headlines has not been met. A long headline is considered ineligible, for example if it includes a countdown function without a default value.
        /// </summary>
        public const int ResponsiveAdLongHeadlinesLessThanMinRequired = 6231;

        /// <summary>
        /// The minimum number of eligible descriptions has not been met. A description is considered ineligible, for example if it includes a countdown function without a default value.
        /// </summary>
        public const int ResponsiveAdDescriptionsLessThanMinRequired = 6232;

        /// <summary>
        /// The same ad cannot include both UTC CountDown and Global_CountDown functions.
        /// </summary>
        public const int ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed = 6233;

        /// <summary>
        /// System generated Asset cannot be used in this operation.
        /// </summary>
        public const int SystemGeneratedAssetNotAllowed = 6234;

        /// <summary>
        /// Image does not meet the minimum pixel requirements for this placement.
        /// </summary>
        public const int ImageDoesntMeetMinPixelRequirements = 6235;

        /// <summary>
        /// Image does not match the aspect ratio required for this association.
        /// </summary>
        public const int ImageDoesntFitAspectRatio = 6236;

        /// <summary>
        /// Invalid crop settings.
        /// </summary>
        public const int AssetCropSettingInvalid = 6237;

        /// <summary>
        /// Account not enabled for Responsive Search Ads auto-generated assets.
        /// </summary>
        public const int AccountNotEnabledForRSAAutoGeneratedAssets = 6238;

        /// <summary>
        /// The customer is not enabled for Website descriptions
        /// </summary>
        public const int CustomerNotEnabledForVanityPharmaWebsiteDescriptions = 6239;

        /// <summary>
        /// Invalid display URL mode.
        /// </summary>
        public const int InvalidDisplayUrlMode = 6240;

        /// <summary>
        /// Invalid website description.
        /// </summary>
        public const int InvalidWebsiteDescription = 6241;

        /// <summary>
        /// Invalid website description for the selected display URL mode.
        /// </summary>
        public const int InvalidWebsiteDescriptionForDisplayUrlMode = 6242;

        /// <summary>
        /// Text Asset Limit Reached For Account.
        /// </summary>
        public const int TextAssetLimitReachedForAccount = 6243;

        /// <summary>
        /// Text Asset Does Not Exist.
        /// </summary>
        public const int TextAssetDoesNotExist = 6244;

        /// <summary>
        /// Text Assets Not Passed.
        /// </summary>
        public const int TextAssetsNotPassed = 6245;

        /// <summary>
        /// The customer is not in pilot for IF functions
        /// </summary>
        public const int IFFunctionCustomerNotInPilot = 6400;

        /// <summary>
        /// Device type IF function has incorrect syntax
        /// </summary>
        public const int IFFunctionIncorrectSyntaxForDevice = 6401;

        /// <summary>
        /// Audience type IF function has incorrect syntax
        /// </summary>
        public const int IFFunctionIncorrectSyntaxForAudience = 6402;

        /// <summary>
        /// Either all IF functions should have a default or none
        /// </summary>
        public const int IFFunctionSomeHaveDefaultValueButNotAll = 6403;

        /// <summary>
        /// The audience which is references in the Ad is invalid
        /// </summary>
        public const int IFFunctionInvalidAudience = 6404;

        /// <summary>
        /// Duplicate audience names have been provided in the IF function
        /// </summary>
        public const int IFFunctionDuplicateAudiences = 6405;

        /// <summary>
        /// User needs to escape certain special charaters per guidelines
        /// </summary>
        public const int IFFunctionSpecialCharactersAreNotEscaped = 6406;

        /// <summary>
        /// Nesting functions of any kind, within an IF function is not allowed
        /// </summary>
        public const int IFFunctionNestingNotAllowed = 6407;

        /// <summary>
        /// Cannot include audience name containing { } in IF functions. Please rename audience without { or } before referencing in IF functions
        /// </summary>
        public const int IFFunctionSpecialCharactersNotAllowed = 6408;

        /// <summary>
        /// Invalid syntax for IF function
        /// </summary>
        public const int IFFunctionInvalidSyntax = 6409;

        /// <summary>
        /// Number of audiences exceeds the allowed max of 100 per ad
        /// </summary>
        public const int IFFunctionNumAudiencesExceedsMaxForAd = 6410;

        /// <summary>
        /// Audience names for ad exceed internal field length
        /// </summary>
        public const int IFFunctionAudiencesExceedsMaxFieldLength = 6411;

        /// <summary>
        /// There was an error fetching audience list from MDS
        /// </summary>
        public const int IFFunctionErrorGettingAudiences = 6412;

        /// <summary>
        /// Bulk upload not supported for disclaimer ads
        /// </summary>
        public const int BulkUploadNotSupportedForDisclaimerAds = 6500;

        /// <summary>
        /// Disclaimer setting cannot be updated
        /// </summary>
        public const int DisclaimerSettingCannotBeUpdated = 6501;

        /// <summary>
        /// Customer not eligible for disclaimer ads
        /// </summary>
        public const int CustomerNotEligibleForDisclaimerAds = 6502;

        /// <summary>
        /// Disclaimer Layout is missing
        /// </summary>
        public const int DisclaimerLayoutMissing = 6503;

        /// <summary>
        /// Invalid Disclaimer Layout
        /// </summary>
        public const int InvalidDisclaimerLayout = 6504;

        /// <summary>
        /// Disclaimer popup text is null or empty
        /// </summary>
        public const int NullOrEmptyDisclaimerPopupText = 6505;

        /// <summary>
        /// Disclaimer line text should be empty
        /// </summary>
        public const int DisclaimerLineTextShouldbeEmpty = 6506;

        /// <summary>
        /// Disclaimer line text is null or empty
        /// </summary>
        public const int NullOrEmptyDisclaimerLineText = 6507;

        /// <summary>
        /// Disclaimer popup text should be empty
        /// </summary>
        public const int DisclaimerPopupTextShouldbeEmpty = 6508;

        /// <summary>
        /// Disclaimer final url is missing
        /// </summary>
        public const int DisclaimerFinalUrlMissing = 6509;

        /// <summary>
        /// Only one disclaimer final url is allowed
        /// </summary>
        public const int OnlyOneDisclaimerFinalUrlIsAllowed = 6510;

        /// <summary>
        /// Only one disclaimer final mobile url is allowed
        /// </summary>
        public const int OnlyOneDisclaimerFinalMobileUrlIsAllowed = 6511;

        /// <summary>
        /// Disclaimer title is not allowed for line text disclaimer
        /// </summary>
        public const int DisclaimerTitleNotAllowedForLineText = 6512;

        /// <summary>
        /// Disclaimer canonly be associated to disclaimer campaign
        /// </summary>
        public const int EntityOnlyAllowedForDisclaimerCampaign = 6513;

        /// <summary>
        /// App install ad is not supported to be created under disclaimer campaign
        /// </summary>
        public const int AppInstallAdNotSupportedForDisclaimerCampaign = 6514;

        /// <summary>
        /// Experiment not supported for disclaimer campaign
        /// </summary>
        public const int ExperimentNotSupportedForDisclaimerCampaign = 6515;

        /// <summary>
        /// This Ad type is not supported to be created under disclaimer campaign
        /// </summary>
        public const int AdTypeNotSupportedForDisclaimerCampaign = 6516;

        /// <summary>
        /// Disclaimer title is required for popup text disclaimer
        /// </summary>
        public const int DisclaimerTitleIsRequiredForPopupText = 6517;

        /// <summary>
        /// The disclaimer title is invalid.
        /// </summary>
        public const int InvalidDisclaimerTitle = 6518;

        /// <summary>
        /// The customer is not part of the Image AdExtension V2 pilot program.
        /// </summary>
        public const int ImageAdExtensionV2PilotNotEnabledForCustomer = 6600;

        /// <summary>
        /// The image ad extension's DisplayText cannot be null or empty.
        /// </summary>
        public const int ImageAdExtensionDisplayTextNullOrEmpty = 6601;

        /// <summary>
        /// The image ad extension's DisplayText is invalid.
        /// </summary>
        public const int ImageAdExtensionDisplayTextInvalid = 6602;

        /// <summary>
        /// The image ad extension's DisplayText is too long.
        /// </summary>
        public const int ImageAdExtensionDisplayTextTooLong = 6603;

        /// <summary>
        /// The view through conversions setting is invalid.
        /// </summary>
        public const int ViewThroughAccountSettingValueInvalid = 6604;

        /// <summary>
        /// The customer is not eligible for the view through conversions feature.
        /// </summary>
        public const int CustomerNotEligibleForViewThroughConversion = 6605;

        /// <summary>
        /// The profile expansion setting is invalid.
        /// </summary>
        public const int ProfileExpansionSettingInvalid = 6606;

        /// <summary>
        /// The customer is not eligible for the profile targeting feature.
        /// </summary>
        public const int CustomerNotEligibleForProfileTargeting = 6607;

        /// <summary>
        /// The view through conversion window value is invalid.
        /// </summary>
        public const int InvalidViewThroughConversionWindowInMinutes = 6608;

        /// <summary>
        /// The view through conversion window is not applicable to this goal type.
        /// </summary>
        public const int ViewThroughConversionNotApplicableToGoalType = 6609;

        /// <summary>
        /// The customer is not eligible for conversion goal selection feature.
        /// </summary>
        public const int CustomerNotEligibleForConversionGoalSelection = 6610;

        /// <summary>
        /// The campaign cannot be undeleted because the associated shared budget is invalid or already deleted.
        /// </summary>
        public const int CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid = 6611;

        /// <summary>
        /// Promotion PercentOff and MoneyAmountOff not set.
        /// </summary>
        public const int PromotionValueNotSet = 6615;

        /// <summary>
        /// Both PercentOff and MoneyAmountOff set.
        /// </summary>
        public const int PromotionPercentAndMoneyValueSet = 6616;

        /// <summary>
        /// Both PromotionCode and OrdersOverAmount set.
        /// </summary>
        public const int PromotionOrdersOverAndPromoCodeSet = 6617;

        /// <summary>
        /// PercentOff and MoneyAmountOff cannot be negative.
        /// </summary>
        public const int PromotionValueNegative = 6618;

        /// <summary>
        /// OrdersOverAmount cannot be negative.
        /// </summary>
        public const int PromotionOrdersOverNegative = 6619;

        /// <summary>
        /// The Promotion Start and End Dates are invalid.
        /// </summary>
        public const int PromotionDatesInvalid = 6620;

        /// <summary>
        /// Currency Code can only be set if MoneyAmountOff or PercentOff are set.
        /// </summary>
        public const int CurrencyCodeSetWithoutMonetaryValue = 6621;

        /// <summary>
        /// The customer is not in the Google Import as a Service pilot program.
        /// </summary>
        public const int GoogleDirectImportNotEnabledForCustomer = 6650;

        /// <summary>
        /// Import Jobs is null or empty.
        /// </summary>
        public const int ImportJobsNull = 6651;

        /// <summary>
        /// The maximum number of scheduled imports for the account would be exceeded.
        /// </summary>
        public const int ScheduledImportNumberLimitExceed = 6652;

        /// <summary>
        /// The import credential ID is invalid.
        /// </summary>
        public const int ImportCredentialIdInvalid = 6653;

        /// <summary>
        /// The import type is invalid.
        /// </summary>
        public const int ImportTypeInvalid = 6654;

        /// <summary>
        /// The import name cannot be empty.
        /// </summary>
        public const int ImportJobNameNull = 6655;

        /// <summary>
        /// The import from account ID is required.
        /// </summary>
        public const int ImportFromAccountIdNull = 6656;

        /// <summary>
        /// The import name is invalid.
        /// </summary>
        public const int ImportNameInvalid = 6657;

        /// <summary>
        /// The import schedule is invalid.
        /// </summary>
        public const int ImportSchedulingInvalid = 6658;

        /// <summary>
        /// The file source for this import is invalid.
        /// </summary>
        public const int FileImportFileSourceInvalid = 6659;

        /// <summary>
        /// The file url for this import is invalid.
        /// </summary>
        public const int FileImportFileUrlInvalid = 6660;

        /// <summary>
        /// Import job id array cannot be null or empty.
        /// </summary>
        public const int InvalidImportJobId = 6661;

        /// <summary>
        /// Duplicate IDs are contained in the array of import jobs.
        /// </summary>
        public const int DuplicateInImportJobIds = 6662;

        /// <summary>
        /// File Import does not support scheduling.
        /// </summary>
        public const int FileImportFrequencyInvalid = 6663;

        /// <summary>
        /// The customer is not in the File Import as a Service pilot program.
        /// </summary>
        public const int FileImportNotEnabledForCustomer = 6664;

        /// <summary>
        /// This import job type does not support the specified option type.
        /// </summary>
        public const int CampaignServiceImportOptionNotSupportedForJob = 6665;

        /// <summary>
        /// This import job name exceeds the limit.
        /// </summary>
        public const int CampaignServiceImportNameTooLong = 6666;

        /// <summary>
        /// This import entity type is invalid.
        /// </summary>
        public const int InvalidImportEntityType = 6667;

        /// <summary>
        /// The number of import jobs of the requested type would exceed the limit.
        /// </summary>
        public const int TaskThrottlingLimitReached = 6668;

        /// <summary>
        /// The import from account ID is invalid.
        /// </summary>
        public const int ImportFromAccountIdInvalid = 6669;

        /// <summary>
        /// The import cannot be updated from API.
        /// </summary>
        public const int ImportJobCannotUpdateFromApi = 6698;

        /// <summary>
        /// The maximum number of items per request would be exceeded.
        /// </summary>
        public const int CampaignServiceBatchLimitExceeded = 6999;

        /// <summary>
        /// The customer is not enabled for the ad scheduling by account time zone feature pilot.
        /// </summary>
        public const int AdScheduleTimeZoneSettingNotInPilot = 7000;

        /// <summary>
        /// Substitution with the {Keyword} parameter is not supported for this property.
        /// </summary>
        public const int KeywordSubstitutionNotSupported = 7001;

        /// <summary>
        /// Filter Link text contains an invalid character.
        /// </summary>
        public const int InvalidFilterLinkTextCharacter = 6626;

        /// <summary>
        /// You must include between 3 to 10 Filter Link Texts.
        /// </summary>
        public const int TooFewFilterLinkText = 6627;

        /// <summary>
        /// You must include between 3 to 10 Filter Link Texts.
        /// </summary>
        public const int TooManyFilterLinkText = 6628;

        /// <summary>
        /// The count of list items in Final Urls and Texts must match.
        /// </summary>
        public const int FinalUrlandTextNotMatch = 6629;

        /// <summary>
        /// Empty Element In List Not Allowed.
        /// </summary>
        public const int EmptyElementInListNotAllowed = 6630;

        /// <summary>
        /// AssetId provided is invalid or does not belong to Account.
        /// </summary>
        public const int FlyerExtensionInvalidAssetId = 6636;

        /// <summary>
        /// The list of asset IDs exceeds the limit.
        /// </summary>
        public const int FlyerAdExtensionsAssetLimitExceeded = 6637;

        /// <summary>
        /// The Image used is too small.
        /// </summary>
        public const int FlyerExtensionImageTooSmall = 6638;

        /// <summary>
        /// The store ID is invalid or does not belong to the customer.
        /// </summary>
        public const int FlyerExtensionInvalidStoreId = 6639;

        /// <summary>
        /// Scheduling Start and End Dates are required for Flyer Extension.
        /// </summary>
        public const int FlyerExtensionSchedulingStartAndEndDateRequired = 6640;

        /// <summary>
        /// The Flyer Ad Extension End Date must be within 30 days of the Start Date.
        /// </summary>
        public const int FlyerExtensionEndDateRangeExceeded = 6643;

        /// <summary>
        /// The store ID cannot be modified.
        /// </summary>
        public const int FlyerExtensionStoreIdCannotBeModified = 6644;

        /// <summary>
        /// The portfolio bid strategy name already exists.
        /// </summary>
        public const int DuplicatePortfolioBidStrategyName = 6670;

        /// <summary>
        /// The number of portfolio bid strategies for the account has exceeded the limit.
        /// </summary>
        public const int PortfolioBidStrategyEntityLimitExceeded = 6671;

        /// <summary>
        /// The portfolio bid strategy name is too long.
        /// </summary>
        public const int PortfolioBidStrategyNameTooLong = 6672;

        /// <summary>
        /// The portfolio bid strategy name should not be null or empty.
        /// </summary>
        public const int PortfolioBidStrategyNameMissing = 6673;

        /// <summary>
        /// The portfolio bid strategy name has invalid characters.
        /// </summary>
        public const int PortfolioBidStrategyNameHasInvalidChars = 6674;

        /// <summary>
        /// The portfolio bid strategy list should not be null or empty.
        /// </summary>
        public const int PortfolioBidStrategiesAreNullOrEmpty = 6675;

        /// <summary>
        /// The batch size of the portfolio bid strategy list has exceeded the limit.
        /// </summary>
        public const int PortfolioBidStrategyOperationsBatchLimitExceeded = 6676;

        /// <summary>
        /// The portfolio bid strategy should not be null.
        /// </summary>
        public const int PortfolioBidStrategyIsNull = 6677;

        /// <summary>
        /// The portfolio bid strategy id is read-only and must be null for add operation.
        /// </summary>
        public const int PortfolioBidStrategyIdShouldBeNullOnAdd = 6678;

        /// <summary>
        /// The portfolio bid strategy id is not valid.
        /// </summary>
        public const int PortfolioBidStrategyIdInvalid = 6679;

        /// <summary>
        /// The portfolio bid strategy list contains duplicate ids.
        /// </summary>
        public const int DuplicatePortfolioBidStrategyId = 6680;

        /// <summary>
        /// The bid strategy type of the portfolio bid strategy can not be null for add operation.
        /// </summary>
        public const int BidStrategyTypeCannotBeNullOnAdd = 6681;

        /// <summary>
        /// The portfolio bid strategy is still used by at least one campaign, so it can not be deleted.
        /// </summary>
        public const int PortfolioBidStrategyIsAssociatedWithActiveCampaigns = 6682;

        /// <summary>
        /// The bid strategy type of the portfolio bid strategy can not be changed.
        /// </summary>
        public const int PortfolioBidStrategyTypeCannotBeChanged = 6683;

        /// <summary>
        /// The account is not in pilot for portfolio bid strategy.
        /// </summary>
        public const int AccountNotInPilotForPortfolioBidStrategy = 6684;

        /// <summary>
        /// The bid strategy type is not supported for portfolio bid strategy.
        /// </summary>
        public const int UnsupportedBidStrategyTypeForPortfolioBidStrategy = 6685;

        /// <summary>
        /// Portfolio bid strategy properties cannot be updated through Campaign entity, please use BidStrategy entity to do the updating instead.
        /// </summary>
        public const int CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity = 6686;

        /// <summary>
        /// The campaign type is not supported for portfolio bid strategy.
        /// </summary>
        public const int UnsupportedCampaignTypeForPortfolioBidStrategy = 6687;

        /// <summary>
        /// The campaign type and bid strategy type are mutually exclusive.
        /// </summary>
        public const int CampaignTypeAndBidStrategyTypeAreMutuallyExclusive = 6688;

        /// <summary>
        /// The associated campaign type of the portfolio bid strategy can not be changed.
        /// </summary>
        public const int PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged = 6689;

        /// <summary>
        /// The campaign type does not match the associated campaign type for current portfolio bid strategy.
        /// </summary>
        public const int CampaignTypeNotMatchCurrentPortfolioBidStrategy = 6690;

        /// <summary>
        /// The campaign cannot be undeleted because the associated portfolio bid strategy is invalid or already deleted.
        /// </summary>
        public const int CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid = 6691;

        /// <summary>
        /// Either ThumbnailUrl or ThumbnailId is required for Video Extension.
        /// </summary>
        public const int VideoExtensionThumbnailRequired = 6700;

        /// <summary>
        /// The file type provided in ThumbnailUrl field is not supported.
        /// </summary>
        public const int VideoExtensionInvalidImageFormat = 6701;

        /// <summary>
        /// The thumbnail image used is too small.
        /// </summary>
        public const int VideoExtensionThumbnailTooSmall = 6702;

        /// <summary>
        /// The aspect ratio of the media used is invalid
        /// </summary>
        public const int VideoExtensionInvalidAspectRatio = 6703;

        /// <summary>
        /// ThumbnailId provided is invalid or does not belong to Account.
        /// </summary>
        public const int VideoExtensionInvalidThumbnailId = 6704;

        /// <summary>
        /// The video used is too small.
        /// </summary>
        public const int VideoExtensionVideoTooSmall = 6705;

        /// <summary>
        /// The duration of the video is invalid.
        /// </summary>
        public const int VideoExtensionInvalidVideoDuration = 6706;

        /// <summary>
        /// VideoId provided is invalid or does not belong to Account.
        /// </summary>
        public const int VideoExtensionInvalidVideoId = 6707;

        /// <summary>
        /// Both ThumbnailUrl and ThumbnailId cannot be set.
        /// </summary>
        public const int VideoExtensionThumbnailIdAndUrlSet = 6708;

        /// <summary>
        /// The video could not be processed.
        /// </summary>
        public const int VideoExtensionVideoProcessingFailed = 6709;

        /// <summary>
        /// The account is not enabled for personalized offers.
        /// </summary>
        public const int AccountNotEnabledForPersonalizedOffers = 6720;

        /// <summary>
        /// The cashback percent exceeds the supported range
        /// </summary>
        public const int PersonalizedOffersCashbackPercentInvalid = 6721;

        /// <summary>
        /// The cashback budget cap is invalid.
        /// </summary>
        public const int PersonalizedOffersCashbackBudgetInvalid = 6722;

        /// <summary>
        /// Personalized offers is not enabled for this campaign type and subtype.
        /// </summary>
        public const int PersonalizedOffersCampaignTypeNotSupported = 6723;

        /// <summary>
        /// Monthly Cashback Budget Amount is required to use Personalized offers.
        /// </summary>
        public const int PersonalizedOffersCampaignBudgetRequired = 6724;

        /// <summary>
        /// Personalized offers is not enabled for this ad group type.
        /// </summary>
        public const int PersonalizedOffersAdGroupTypeNotSupported = 6725;

        /// <summary>
        /// Product Cashback Scope is only supported for Shopping Campaigns.
        /// </summary>
        public const int PersonalizedOffersScopeNotSupportedForCampaignType = 6726;

        /// <summary>
        /// Personalized offers is not enabled for this campaign.
        /// </summary>
        public const int PersonalizedOffersNotEnabledForCampaign = 6727;

        /// <summary>
        /// Personalized Coupons is only supported for Shopping Campaigns.
        /// </summary>
        public const int PersonalizedOffersCouponsNotSupportedForCampaignType = 6728;

        /// <summary>
        /// Target ROAS cannot be set for campaigns with personalized offers.
        /// </summary>
        public const int TargetRoasNotSupportedForPersonalizedOffers = 6729;

        /// <summary>
        /// The Sponsored Promotions for Brands cashback setting is invalid.
        /// </summary>
        public const int PersonalizedOffersSponsoredPromotionsInvalid = 6730;

        /// <summary>
        /// A SPB campaign tried to update/delete cashback properties, which is unsupported for SPB campaign type.
        /// </summary>
        public const int PersonalizedOffersUnsupportedOperation = 6731;

        /// <summary>
        /// SPB campaigns only support IsPromotionsForBrands and none of the other cashback properties.
        /// </summary>
        public const int PersonalizedOffersSponsoredPromotionsOnly = 6732;

        /// <summary>
        /// The account is not enabled for performance max campaigns.
        /// </summary>
        public const int PerformanceMaxCampaignsNotEnabledForAccount = 6740;

        /// <summary>
        /// MaxCpc is not supported for this campaign type.
        /// </summary>
        public const int MaxCpcNotSupportedForCampaignType = 6741;

        /// <summary>
        /// Website exclusion is not allowed when final url expansion opt out is true.
        /// </summary>
        public const int PerformanceMaxCampaignFinalUrlExpansionOptedOut = 6742;

        /// <summary>
        /// The audience group name already exists.
        /// </summary>
        public const int DuplicateAudienceGroupName = 6743;

        /// <summary>
        /// The number of audience groups for the account has exceeded the limit.
        /// </summary>
        public const int AudienceGroupEntityLimitExceeded = 6744;

        /// <summary>
        /// The audience group id is not valid.
        /// </summary>
        public const int AudienceGroupIdInvalid = 6745;

        /// <summary>
        /// The campaign type is not supported for asset groups.
        /// </summary>
        public const int UnsupportedCampaignTypeForAssetGroup = 6746;

        /// <summary>
        /// The asset groups are null or empty.
        /// </summary>
        public const int AssetGroupsAreNullOrEmpty = 6747;

        /// <summary>
        /// The asset group is null.
        /// </summary>
        public const int AssetGroupIsNull = 6748;

        /// <summary>
        /// Maximum number of asset groups per request is exceeded
        /// </summary>
        public const int AssetGroupOperationsBatchLimitExceeded = 6749;

        /// <summary>
        /// Path2 is set without Path1.
        /// </summary>
        public const int Path2SetWithoutPath1 = 6750;

        /// <summary>
        /// Final url is required.
        /// </summary>
        public const int FinalUrlRequired = 6751;

        /// <summary>
        /// The domain is not valid.
        /// </summary>
        public const int DomainInvalid = 6752;

        /// <summary>
        /// The domain is too long.
        /// </summary>
        public const int DomainTooLong = 6753;

        /// <summary>
        /// Extracting display url domain from final urls failed.
        /// </summary>
        public const int DomainExtractionFailed = 6754;

        /// <summary>
        /// Asset field with a required length is not present.
        /// </summary>
        public const int AssetFieldWithRequiredLengthMinimumNotMet = 6755;

        /// <summary>
        /// Minimum number of assets for asset sub type is not present.
        /// </summary>
        public const int AssetFieldMinimumPerSubTypeNotMet = 6756;

        /// <summary>
        /// Maximum number of assets for asset sub type is exceeded.
        /// </summary>
        public const int AssetFieldLimitPerSubTypeExceeded = 6757;

        /// <summary>
        /// Audience Group Asset Group Association already exists.
        /// </summary>
        public const int AssetGroupAudienceGroupAssociationDuplicate = 6758;

        /// <summary>
        /// Asset Group is not associated to given Audience Group.
        /// </summary>
        public const int AssetGroupAudienceGroupAssociationDoesNotExist = 6759;

        /// <summary>
        /// A custom call to action must be specified if the 'Custom' call to action choice is chosen.
        /// </summary>
        public const int LeadFormExtensionActionNameRequired = 6760;

        /// <summary>
        /// A confirmation URL must be provided if the 'Download' or 'VisitWebsite' confirmation action is chosen.
        /// </summary>
        public const int LeadFormExtensionConfirmationUrlRequired = 6761;

        /// <summary>
        /// Webhook URL and key must be provided if the 'Webhook' lead delivery method is chosen, or email addresses must be provided if the 'Email' delivery method is chosen.
        /// </summary>
        public const int LeadFormExtensionInvalidLeadDelivery = 6762;

        /// <summary>
        /// The emails provided must be a comma-separated list of valid email addresses.
        /// </summary>
        public const int LeadFormExtensionInvalidEmails = 6763;

        /// <summary>
        /// Form questions can't be updated.
        /// </summary>
        public const int LeadFormExtensionQuestionUpdatesNotSupported = 6764;

        /// <summary>
        /// The same question can't be included more than once.
        /// </summary>
        public const int LeadFormExtensionDuplicateQuestionId = 6765;

        /// <summary>
        /// An invalid question id was specified.
        /// </summary>
        public const int LeadFormExtensionInvalidQuestionId = 6766;

        /// <summary>
        /// This can only be associated with a Search or Performance Max campaign.
        /// </summary>
        public const int EntityOnlyAllowedForSearchOrPMaxCampaigns = 6767;

        /// <summary>
        /// The requested Lead Form extension was not found.
        /// </summary>
        public const int LeadFormExtensionNotFound = 6768;

        /// <summary>
        /// Lead Form extensions can only be associated with Max Clicks campaigns.
        /// </summary>
        public const int OnlyMaxClicksBiddingSchemeForLeadFormExtension = 6769;

        /// <summary>
        /// Multiple choice answers are not supported for Lead Form extensions.
        /// </summary>
        public const int LeadFormExtensionMultipleChoiceAnswersNotAllowed = 6770;

        /// <summary>
        /// The text format of Headlines is invalid.
        /// </summary>
        public const int InvalidHeadlinesText = 6780;

        /// <summary>
        /// The text format of LongHeadlines is invalid.
        /// </summary>
        public const int InvalidLongHeadlinesText = 6781;

        /// <summary>
        /// The text format of Descriptions is invalid.
        /// </summary>
        public const int InvalidDescriptionsText = 6782;

        /// <summary>
        /// The text format of Images is invalid.
        /// </summary>
        public const int InvalidImagesText = 6783;

        /// <summary>
        /// The text format of Videos is invalid.
        /// </summary>
        public const int InvalidVideosText = 6784;

        /// <summary>
        /// Start date cannot be less than the current date.
        /// </summary>
        public const int StartDateLessThanCurrentDate = 6785;

        /// <summary>
        /// End date cannot be less than the start date.
        /// </summary>
        public const int EndDateLessThanStartDate = 6786;

        /// <summary>
        /// Serving has already started and start date cannot be changed.
        /// </summary>
        public const int StartDateCannotBeChanged = 6787;

        /// <summary>
        /// The audience group name is too long.
        /// </summary>
        public const int AudienceGroupNameTooLong = 6788;

        /// <summary>
        /// The audience group name should not be null or empty.
        /// </summary>
        public const int AudienceGroupNameMissing = 6789;

        /// <summary>
        /// The audience group list should not be null or empty.
        /// </summary>
        public const int AudienceGroupsAreNullOrEmpty = 6790;

        /// <summary>
        /// The batch size of the audience group list has exceeded the limit.
        /// </summary>
        public const int AudienceGroupOperationsBatchLimitExceeded = 6791;

        /// <summary>
        /// The audience group should not be null.
        /// </summary>
        public const int AudienceGroupIsNull = 6792;

        /// <summary>
        /// The audience group id is read-only and must be null for add operation.
        /// </summary>
        public const int AudienceGroupIdShouldBeNullOnAdd = 6793;

        /// <summary>
        /// Audience dimensions cannot be null.
        /// </summary>
        public const int DimensionsCannotBeNull = 6794;

        /// <summary>
        /// One of the audience Age Range is not supported.
        /// </summary>
        public const int UnsupportedAgeRangeForAudienceGroup = 6795;

        /// <summary>
        /// One of the audience Gender Type is not supported.
        /// </summary>
        public const int UnsupportedGenderTypeForAudienceGroup = 6796;

        /// <summary>
        /// The audience group list contains duplicate ids.
        /// </summary>
        public const int DuplicateAudienceGroupId = 6797;

        /// <summary>
        /// The audience group name has invalid characters.
        /// </summary>
        public const int AudienceGroupNameHasInvalidChars = 6798;

        /// <summary>
        /// One of the audience added is not found.
        /// </summary>
        public const int AudienceNotFound = 6799;

        /// <summary>
        /// One of the audience type is not supported.
        /// </summary>
        public const int AudienceTypeNotSupported = 6800;

        /// <summary>
        /// The same audience already exists in this audience group.
        /// </summary>
        public const int DuplicateAudience = 6801;

        /// <summary>
        /// Audience group can't have more than allowed number audiences.
        /// </summary>
        public const int TooManyAudiences = 6802;

        /// <summary>
        /// Audience group can't have multiple audiences of same type.
        /// </summary>
        public const int TooManyDimensionsOfSameType = 6803;

        /// <summary>
        /// The audience group cannot be removed, because it is currently used in an (enabled or paused) asset group.
        /// </summary>
        public const int AudienceGroupInUse = 6804;

        /// <summary>
        /// The operations in this call involved too many asset groups.
        /// </summary>
        public const int OperationsForTooManyAssetGroups = 6805;

        /// <summary>
        /// AssetGroupListingGroups entity count limit exceeded.
        /// </summary>
        public const int AssetGroupListingGroupsEntityLimitExceeded = 6806;

        /// <summary>
        /// AssetGroupListingGroup can not be null
        /// </summary>
        public const int AssetGroupListingGroupIsNull = 6807;

        /// <summary>
        /// Duplicate AssetGroupListingGroupId.
        /// </summary>
        public const int DuplicateAssetGroupListingGroupIds = 6808;

        /// <summary>
        /// AssetGroupListingGroupAction can not be null.
        /// </summary>
        public const int AssetGroupListingGroupActionIsNull = 6809;

        /// <summary>
        /// AssetGroupListingGroupActions in request can not be null or empty.
        /// </summary>
        public const int AssetGroupListingGroupActionsNullOrEmpty = 6810;

        /// <summary>
        /// Another operation for same assetGroup has error.
        /// </summary>
        public const int AnotherOperationForSameAssetGroupHasError = 6811;

        /// <summary>
        /// AssetGroupId is invalid
        /// </summary>
        public const int AssetGroupIdInvalid = 6812;

        /// <summary>
        /// The campaign type is not Performance Max.
        /// </summary>
        public const int CampaignTypeIsNotPerformanceMax = 6813;

        /// <summary>
        /// ListingGoup count limit exceeded for assetGroup.
        /// </summary>
        public const int ListingGoupLimitExceededForAssetGroup = 6814;

        /// <summary>
        /// AssetGroupListingGroupId is invalid.
        /// </summary>
        public const int AssetGroupListingGroupIdInvalid = 6815;

        /// <summary>
        /// AssetGroupListingType is invalid.
        /// </summary>
        public const int AssetGroupListingTypeInvalid = 6816;

        /// <summary>
        /// Duplicate root node for listingGroup tree.
        /// </summary>
        public const int DuplicateRootNodeForListingGroupTree = 6817;

        /// <summary>
        /// Parent listingGroup node does not exist
        /// </summary>
        public const int ParentListingGroupNodeDoesNotExist = 6818;

        /// <summary>
        /// Height of listingGroup tree limit exceeded.
        /// </summary>
        public const int HeightOfListingGroupTreeLimitExceeded = 6819;

        /// <summary>
        /// ProductCondition hierarchy is invalid.
        /// </summary>
        public const int ProductConditionHierarchyInvalid = 6820;

        /// <summary>
        /// EverythingElse node missing.
        /// </summary>
        public const int EverythingElseNodeMissing = 6821;

        /// <summary>
        /// Update is not supported for listingGroup node
        /// </summary>
        public const int UpdateIsNotSupportedForListingGroupNode = 6822;

        /// <summary>
        /// Failed to get sales country from campaign settings
        /// </summary>
        public const int FailedToGetSalesCountryFromCampaignSettings = 6823;

        /// <summary>
        /// Invalid sales country, please check salescountry in your campaign settings
        /// </summary>
        public const int InvalidSalesCountry = 6824;

        /// <summary>
        /// Shared budget is not supported for this campaign type
        /// </summary>
        public const int SharedBudgetNotSupportedForCampaignType = 6825;

        /// <summary>
        /// Account not enabled for keyword predictive targeting
        /// </summary>
        public const int AccountNotEnabledForSearchCampaignPredictiveTargeting = 6826;

        /// <summary>
        /// Maximum number of PerformanceMax campaigns has been reached for this account
        /// </summary>
        public const int PerformanceMaxCampaignLimitExceeded = 6827;

        /// <summary>
        /// AssetGroup with the same name already exists.
        /// </summary>
        public const int AssetGroupAlreadyExists = 6828;

        /// <summary>
        /// Audience group with active asset group associations cannot be deleted.
        /// </summary>
        public const int AudienceGroupWithActiveAssociationsCannotBeDeleted = 6829;

        /// <summary>
        /// Maximum number of AssetGroups has been reached for this campaign
        /// </summary>
        public const int AssetGroupLimitExceededForCampaign = 6830;

        /// <summary>
        /// AssetGroupListingGroup id list exceeds limit
        /// </summary>
        public const int AssetGroupListingGroupIdListExceedsLimit = 6831;

        /// <summary>
        /// Requested entity does not belong to the campaign.
        /// </summary>
        public const int EntityDoesNotBelongToCampaign = 6832;

        /// <summary>
        /// The campaign type is not supported for ad groups.
        /// </summary>
        public const int UnsupportedCampaignTypeForAdGroup = 6833;

        /// <summary>
        /// The status of the asset group is invalid.
        /// </summary>
        public const int AssetGroupInvalidStatus = 6834;

        /// <summary>
        /// Customer not enabled for FeedLabel.
        /// </summary>
        public const int CustomerNotEnabledForFeedLabel = 6835;

        /// <summary>
        /// FeedLabel length is outside the allowed range.
        /// </summary>
        public const int ShoppingSettingsFeedLabelInvalidLength = 6836;

        /// <summary>
        /// FeedLabel contains invalid characters.
        /// </summary>
        public const int ShoppingSettingsFeedLabelInvalidCharacters = 6837;

        /// <summary>
        /// FeedLabel and SalesCountry are mutually exclusive.
        /// </summary>
        public const int ShoppingSettingsFeedLabelAndSalesCountryIncompatible = 6838;

        /// <summary>
        /// The top level domain of the url does not match the url of the store associated with the campaign.
        /// </summary>
        public const int DomainDoesNotMatchCampaignStoreDomain = 6839;

        /// <summary>
        /// The store associated with the campaign does not have a domain.
        /// </summary>
        public const int CampaignStoreDoesNotHaveDomain  = 6840;

        /// <summary>
        /// The logo is too small.
        /// </summary>
        public const int LogoExtensionLogoTooSmall = 6841;

        /// <summary>
        /// The logo is too large.
        /// </summary>
        public const int LogoExtensionLogoTooLarge = 6842;

        /// <summary>
        /// The logo is not square.
        /// </summary>
        public const int LogoExtensionLogoNotSquare = 6843;

        /// <summary>
        /// This entity type is not supported for performance max campaigns.
        /// </summary>
        public const int EntityNotAllowedForPmaxCampaign = 6844;

        /// <summary>
        /// Bid adjustment is not supported for performance max campaigns.
        /// </summary>
        public const int BidAdjustmentNotSupportedForPerformanceMaxCampaign = 6845;

        /// <summary>
        /// Invalid predictive matching setting.
        /// </summary>
        public const int InvalidPredictiveMatchingSetting = 6846;

        /// <summary>
        /// The URL contains UTM (Urchin Tracking Module) tags. Please remove all UTM tags and try again. Note: If you want to add UTM tags, you can enable Auto-tagging of UTM in Settings > Account level options.
        /// </summary>
        public const int ManualTaggingDetectedInQueryParameters = 6847;

        /// <summary>
        /// Brand doesn't exist.
        /// </summary>
        public const int InvalidBrandId = 6848;

        /// <summary>
        /// Not a video asset.
        /// </summary>
        public const int InvalidVideoAsset = 6849;

        /// <summary>
        /// Duplicate video asset
        /// </summary>
        public const int DuplicateVideoAsset = 6850;

        /// <summary>
        /// The status of the video is invalid.
        /// </summary>
        public const int VideoInvalidStatus = 6851;

        /// <summary>
        /// The width of the Video is too small.
        /// </summary>
        public const int VideoWidthTooSmall = 6852;

        /// <summary>
        /// The height of the Video is too small.
        /// </summary>
        public const int VideoHeightTooSmall = 6853;

        /// <summary>
        /// The aspect ratio of the video is invalid.
        /// </summary>
        public const int VideoInvalidAspectRatio = 6854;

        /// <summary>
        /// The duration of the video is invalid.
        /// </summary>
        public const int VideoInvalidDuration = 6855;

        /// <summary>
        /// The bit rate of the video is too small.
        /// </summary>
        public const int VideoBitRateTooSmall = 6856;

        /// <summary>
        /// The source length of the video is too large.
        /// </summary>
        public const int VideoSourceLengthTooLarge = 6857;

        /// <summary>
        /// The file format of the video is unsupported.
        /// </summary>
        public const int VideoUnsupportedFileFormat = 6858;

        /// <summary>
        /// This account is not enabled for Video as an asset.
        /// </summary>
        public const int VideoAsAssetNotEnabledForAccount = 6859;

        /// <summary>
        /// The account is not enabled for App campaigns.
        /// </summary>
        public const int AppCampaignsNotEnabledForAccount = 8233;

        /// <summary>
        /// You cannot add this entity to a campaign of type App.
        /// </summary>
        public const int EntityNotAllowedForAppCampaign = 8300;

        /// <summary>
        /// Tracking template is required for Mobile App Campaigns.
        /// </summary>
        public const int TrackingTemplateIsRequiredForMobileAppCampaign = 8301;

        /// <summary>
        /// Maximum 25 Conversion Value Rules.
        /// </summary>
        public const int ConversionValueRuleLimitExceeded = 8601;

        /// <summary>
        /// Conversion Value Rule is null or empty.
        /// </summary>
        public const int NullOrEmptyConversionValueRule = 8602;

        /// <summary>
        /// Some property is not set up.
        /// </summary>
        public const int NullRequiredProperty = 8603;

        /// <summary>
        /// The value rule name is already in use by another rule.
        /// </summary>
        public const int DuplicateRuleName = 8604;

        /// <summary>
        /// Maximum for Rules is 25.
        /// </summary>
        public const int ExceedsMaximumNumberOfRules = 8605;

        /// <summary>
        /// The rule with this Id is not found.
        /// </summary>
        public const int RuleIdNotFound = 8606;

        /// <summary>
        /// Location type is wrong for this location Id.
        /// </summary>
        public const int LocationTypeMismatch = 8607;

        /// <summary>
        /// Audience type is wrong for this audience Id.
        /// </summary>
        public const int AudienceTypeMismatch = 8608;

        /// <summary>
        /// Condition overlaps with others.
        /// </summary>
        public const int ConditionOverlap = 8609;

        /// <summary>
        /// Location hierarchy is this rule.
        /// </summary>
        public const int LocationHierarchyIssue = 8610;

        /// <summary>
        /// Entity is empty or null.
        /// </summary>
        public const int EntityIsEmptyOrNull = 8611;

        /// <summary>
        /// Some required property is not set up.
        /// </summary>
        public const int EmptyPropertyNotAllowed = 8612;

        /// <summary>
        /// Currency code should be set up.
        /// </summary>
        public const int CurrencyCodeShouldNotBeNullForAdd = 8613;

        /// <summary>
        /// Primary condition should not be null.
        /// </summary>
        public const int PrimaryConditionShouldNotBeNull = 8614;

        /// <summary>
        /// Rule Ids are duplicated.
        /// </summary>
        public const int DuplicateRuleId = 8615;

        /// <summary>
        /// Condition type is not allowed.
        /// </summary>
        public const int ConditionTypeNotAllowed = 8616;

        /// <summary>
        /// Conversion Value Rule is not enabled for this account.
        /// </summary>
        public const int ConversionValueRuleNotEnabled = 8617;

        /// <summary>
        /// Conversion Value Rule requires only 1 primary condition.
        /// </summary>
        public const int PrimaryConditionCountShouldBeOne = 8618;

        /// <summary>
        /// There should be no more than two conditions.
        /// </summary>
        public const int ConditionCountShouldBeWithinTwo = 8619;

        /// <summary>
        /// The specified report request is null. Please submit a valid report request.
        /// </summary>
        public const int NullReportRequest = 2001;

        /// <summary>
        /// The specified report request type is invalid. Please submit a report request with a valid type.
        /// </summary>
        public const int UnknownReportType = 2002;

        /// <summary>
        /// The specified report request contains at least one account which you have insufficient privileges to access. Please submit a report request for accounts that you are authorized to access.
        /// </summary>
        public const int AccountNotAuthorized = 2003;

        /// <summary>
        /// The specified report request indicates that complete data is required, but only partial data is currently available. Please try again later or submit a report request for partial data.
        /// </summary>
        public const int NoCompleteDataAvaliable = 2004;

        /// <summary>
        /// The specified report request contains a report time period for which complete data cannot be returned.
        /// </summary>
        public const int InvalidDataAvailabilityAndTimePeriodCombination = 2005;

        /// <summary>
        /// The specified report request contains an invalid report name. Please submit a report request with a valid friendly name for identifying the report.
        /// </summary>
        public const int InvalidReportName = 2006;

        /// <summary>
        /// The specified report aggregation is invalid. Please look up in the documentation the valid report aggregation values for the specified time period and report type. The Details field contains the specified report aggregation and time period
        /// </summary>
        public const int InvalidReportAggregation = 2007;

        /// <summary>
        /// The specified report time is invalid. Please submit a report request with a time type that contains exactly one of PredefinedTime, CustomDates, CustomDateRangeStart/End, and null for the rest.
        /// </summary>
        public const int InvalidReportTimeSelection = 2008;

        /// <summary>
        /// The specified report time contains an invalid custom date range start. Please submit a report request with valid start and end dates for the custom date range.
        /// </summary>
        public const int InvalidCustomDateRangeStart = 2009;

        /// <summary>
        /// The specified report time contains an invalid custom date range end. Please submit a report request with valid start and end dates for the custom date range.
        /// </summary>
        public const int InvalidCustomDateRangeEnd = 2010;

        /// <summary>
        /// The specified report time contains an invalid custom date range, where the end date is before the start date. Please submit a report request with valid start and end dates for the custom date range.
        /// </summary>
        public const int EndDateBeforeStartDate = 2011;

        /// <summary>
        /// The specified report time contains an empty array of custom dates. Please submit a report request with valid custom dates
        /// </summary>
        public const int EmptyCustomDates = 2012;

        /// <summary>
        /// The specified report time contains too many custom dates. The Details field contains the limit for number of custom dates
        /// </summary>
        public const int CustomDatesOverlimit = 2013;

        /// <summary>
        /// The specified report request does not specify which columns to return. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report.
        /// </summary>
        public const int NullColumns = 2014;

        /// <summary>
        /// The specified report request does not specify all the required columns for this report type. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report.
        /// </summary>
        public const int RequiredColumnsNotSelected = 2015;

        /// <summary>
        /// The specified report request specifies duplicate columns, listed in the Details field. Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report.
        /// </summary>
        public const int DuplicateColumns = 2016;

        /// <summary>
        /// The specified report request does not specify measurement columns (at least one is required). Please submit a report request with the required columns for this report type, and optionally additional columns that are to be included in the report.
        /// </summary>
        public const int NoMeasureSelected = 2017;

        /// <summary>
        /// The specified campaign report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountIdInCampaignReportScope = 2018;

        /// <summary>
        /// The specified campaign report scope contains an invalid campaign id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidCampaignIdInCampaignReportScope = 2019;

        /// <summary>
        /// The specified ad group report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountIdInAdGroupReportScope = 2020;

        /// <summary>
        /// The specified ad group report scope contains an invalid campaign id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidCampaignIdInAdGroupReportScope = 2021;

        /// <summary>
        /// The specified ad group report scope contains an invalid ad group id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAdGroupIdInAdGroupReportScope = 2022;

        /// <summary>
        /// The specified account report scope contains an invalid account id, listed in the Details field. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountIdInAccountReportScope = 2023;

        /// <summary>
        /// The specified campaign report scope is null. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int NullCampaignReportScope = 2024;

        /// <summary>
        /// The specified ad group report scope is null. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int NullAdGroupReportScope = 2025;

        /// <summary>
        /// The specified account report scope does not contain account ids. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountReportScope = 2026;

        /// <summary>
        /// The specified account through campaign report scope does not contain valid values. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountThruCampaignReportScope = 2027;

        /// <summary>
        /// The specified account through ad group report scope does not contain valid values. Please submit a report request with valid scope and ids.
        /// </summary>
        public const int InvalidAccountThruAdGroupReportScope = 2028;

        /// <summary>
        /// The specified report scope contains too many accounts. The Details field contains the limit for number of accounts. 
        /// </summary>
        public const int AccountsOverLimit = 2029;

        /// <summary>
        /// The specified report scope contains too many campaigns. The Details field contains the limit for number of campaigns. 
        /// </summary>
        public const int CampaignsOverLimit = 2030;

        /// <summary>
        /// The specified report scope contains too many ad groups. The Details field contains the limit for number of ad groups. 
        /// </summary>
        public const int AdGroupsOverLimit = 2031;

        /// <summary>
        /// The specified report request contains at least one string with embedded script. The Details field contains such string(s). Please remove the script inside the string(s) and submit the report request again.
        /// </summary>
        public const int CrossSiteScriptNotAllowed = 2032;

        /// <summary>
        /// The specified report request contains a Keyword filter where at least one of the values is null, empty or contains an invalid keyword. Please submit a report request with valid Keyword filter values.
        /// </summary>
        public const int InvalidKeywordFilterValue = 2033;

        /// <summary>
        /// The specified report request contains a TimePeriod column which is incompatible with the requested Summary report aggregation. Please submit a report request with valid report columns for a summary report.
        /// </summary>
        public const int InvalidTimePeriodColumnForSummaryReport = 2034;

        /// <summary>
        /// The specified report request contains at least one account id which is invalid. The Details field contains a comma-separated list of the invalid account ids. Please submit a report request with valid account ids.
        /// </summary>
        public const int InvalidAccountIds  = 2035;

        /// <summary>
        /// The specified report scope contains too many Behavioral Ids. The Details field contains the limit for number of Behavioral Ids.  
        /// </summary>
        public const int BehavioralIdOverLimit = 2036;

        /// <summary>
        /// The specified report request contains a BehavioralId filter where at least one of the values is empty or contains an invalid BehavioralId. Please submit a report request with valid BehavioralId filter values.
        /// </summary>
        public const int InvalidBehavioralIdValue = 2037;

        /// <summary>
        /// The specified report scope contains too many Site Ids. The Details field contains the limit for number of sites.       
        /// </summary>
        public const int SiteIdOverLimit = 2038;

        /// <summary>
        /// The specified report request contains a SiteId filter where at least one of the values is empty or contains an invalid SiteId. Please submit a report request with valid SiteId filter values.
        /// </summary>
        public const int InvalidSiteIdValue = 2039;

        /// <summary>
        /// The specified report time contains an invalid date in custom date range. Please submit a report request with valid start and end dates for the custom date range.
        /// </summary>
        public const int InvalidCustomDateRange = 2040;

        /// <summary>
        /// The specified report time contains an invalid custom date range, where start date is in future. Please submit a report request with valid start dates for the custom date range.
        /// </summary>
        public const int StartDateCannotBeInFuture = 2041;

        /// <summary>
        /// The specified report id is invalid. Please use a valid report id.
        /// </summary>
        public const int InvalidReportId = 2100;

        /// <summary>
        /// The specified report was not found. It may have been expired. Please use a different report id, or submit a new report request and download it in a timely manner when it is complete.
        /// </summary>
        public const int ReportNotFound = 2101;

        /// <summary>
        /// The provided Stream data in request is null.
        /// </summary>
        public const int NullStreamData = 2200;

        /// <summary>
        /// Error occurred while reading from the provided stream.
        /// </summary>
        public const int ReadStreamError = 2201;

        /// <summary>
        /// Start date cannot be earlier than today.
        /// </summary>
        public const int FeedItemScheduleInvalidStartTime = 6300;

        /// <summary>
        /// The End time cannot be earlier than today
        /// </summary>
        public const int FeedItemScheduleInvalidEndTime = 6301;

        /// <summary>
        /// Invalid CustomerList Id
        /// </summary>
        public const int InvalidCustomerListId = 8000;

        /// <summary>
        /// The Customer List Action Type can only be Add, Replace, or Delete.
        /// </summary>
        public const int InvalidCustomerListActionType = 8001;

        /// <summary>
        /// The Customer List Item sub type is not supported.
        /// </summary>
        public const int CustomerListItemSubTypeNotSupported = 8002;

        /// <summary>
        /// The Customer Match 'Terms And Conditions' are not yet Accepted in the Microsoft Advertising web UI. Please visit https://go.microsoft.com/fwlink/?linkid=2126222 for details.
        /// </summary>
        public const int TermsAndConditionsNotAccepted = 8003;

        /// <summary>
        /// Email address can't be in plain text and must be hashed.
        /// </summary>
        public const int EmailMustBeHashed = 8004;

        /// <summary>
        /// CRM Id exceeds the limitation of 100 characters.
        /// </summary>
        public const int CRMIdLengthExceedLimitation = 8005;

        /// <summary>
        /// Either email address or CRM Id should be provided.
        /// </summary>
        public const int EitherEmailOrCRMIDShouldBeProvided = 8006;

        /// <summary>
        /// Customer List Items Array Exceeds Limit.
        /// </summary>
        public const int CustomerListItemsArrayExceedsLimit = 8007;

        /// <summary>
        /// Combination rules are required.
        /// </summary>
        public const int CombinationRulesNullOrEmpty = 8100;

        /// <summary>
        /// The number of combination rules exceeds the maximum per combined list or per combination relationship.
        /// </summary>
        public const int CombinationRulesExceedsLimit = 8101;

        /// <summary>
        /// No audience is selected for the CombinedListAudience.
        /// </summary>
        public const int NoAudienceSelected = 8102;

        /// <summary>
        /// The operator of Combination rule is invalid.
        /// </summary>
        public const int InvalidCombinationRuleOperator = 8103;

        /// <summary>
        /// If you combine audiences using the NOT relationship, you must also combine audiences using the AND relationship or the OR relationship.
        /// </summary>
        public const int OnlyNotCombinationUnsupported = 8104;

        /// <summary>
        /// The audience type cannot be combined using the AND relationship.
        /// </summary>
        public const int AndCombinationUnsupportedForAudienceType = 8105;

        /// <summary>
        /// The audience type cannot be combined using the OR relationship.
        /// </summary>
        public const int OrCombinationUnsupportedForAudienceType = 8106;

        /// <summary>
        /// The audience type cannot be combined using the NOT relationship.
        /// </summary>
        public const int NotCombinationUnsupportedForAudienceType = 8107;

        /// <summary>
        /// You can only include one Similar Audience per combined list, and only by using the OR relationship without any other audiences.
        /// </summary>
        public const int SimilarAudienceCanOnlyBeInSingleORSet = 8108;

        /// <summary>
        /// Customer Lists cannot be combined with other audience types.
        /// </summary>
        public const int CustomerListsCanOnlyBeCombinedWithOtherCustomerLists = 8109;

        /// <summary>
        /// The audience cannot be deleted because it is used by a Combined List.
        /// </summary>
        public const int AudienceUsedByCombinedListCannotBeDeleted = 8110;

        /// <summary>
        /// The audience cannot be deleted because its paired Similar Audience is used by a Combined List.
        /// </summary>
        public const int AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList = 8111;

        /// <summary>
        /// A Combined List can only be edited by its owner.
        /// </summary>
        public const int CombinedListCanOnlyBeEditedByOwner = 8113;

        /// <summary>
        /// A Combined List can only be deleted by its owner.
        /// </summary>
        public const int CombinedListCanOnlyBeDeletedByOwner = 8114;

        /// <summary>
        /// Custom Audiences cannot be combined with other audience types.
        /// </summary>
        public const int CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience = 8115;

        /// <summary>
        /// The Combination Rule is invalid.
        /// </summary>
        public const int InvalidCombinationRule = 8116;

        /// <summary>
        /// The Impression Tracking Url is invalid.
        /// </summary>
        public const int ImpressionTrackingUrlInvalid = 8117;

        /// <summary>
        /// The Impression Tracking Url count exceed the max count.
        /// </summary>
        public const int ImpressionTrackingUrlsExceedMaxCount = 8118;

        /// <summary>
        /// You cannot associate any combination of customer list or custom audience with campaigns or ad groups that target the European Union or locations with similar restrictions.
        /// </summary>
        public const int CombinedAudienceForLocationNotAllowed = 8119;

        /// <summary>
        /// The Impression Tracking Url is inaccessible.
        /// </summary>
        public const int ImpressionTrackingUrlInaccessible = 8120;

        /// <summary>
        /// The verified tracking data is invalid.
        /// </summary>
        public const int VerifiedTrackingDataInvalid = 8121;

        /// <summary>
        /// The account is not enabled for image auto-retrieval from a website domain.
        /// </summary>
        public const int AccountNotEnabledForImageAutoRetrieve = 8122;

        /// <summary>
        /// the customer is not eligible for remarketing list based on new parameters
        /// </summary>
        public const int CustomerNotEligibleForRemarketingListBasedParameters = 8123;

        /// <summary>
        /// The account is not eligible for CampaignConversionGoal.
        /// </summary>
        public const int AccountNotEligibleForCampaignConversionGoal = 8131;

        /// <summary>
        /// InStoreVisit Goal is not supported for CampaignConversionGoal.
        /// </summary>
        public const int StoreVisitNotSupportForCampaignConversionGoal = 8132;

        /// <summary>
        /// CampaignConversionGoal is dupclicate.
        /// </summary>
        public const int DuplicateCampaignConversionGoal = 8133;

        /// <summary>
        /// CampaignConversionGoal is not exist.
        /// </summary>
        public const int CampaignConversionGoalNotExist = 8134;

        /// <summary>
        /// The CampaignConversionGoal SubType is invalid.
        /// </summary>
        public const int InvalidCampaignConversionGoalSubType = 8135;

        /// <summary>
        /// Value for setting Enable MMA Under DSA AdGroup is invalid.
        /// </summary>
        public const int EnableMMAUnderDSAAdgroupsValueInvalid = 8136;

        /// <summary>
        /// The smart listing status is invalid for the current operation.
        /// </summary>
        public const int InvalidSmartListingStatus = 8137;

        /// <summary>
        /// The campaign is not eligible for campaign level goal
        /// </summary>
        public const int CampaignNotEligibleForCampaignConversionGoal = 8138;

        /// <summary>
        /// The CallToAction is not supported.
        /// </summary>
        public const int CallToActionNotSupported = 8139;

        /// <summary>
        /// The CallToAction and CallToActionLanguage pair is not supported.
        /// </summary>
        public const int CallToActionCallToActionLanguagePairNotSupported = 8140;

        /// <summary>
        /// Customer is not allowed for hot spots.
        /// </summary>
        public const int CustomerIsNotAllowedForHotSpots = 8141;

        /// <summary>
        /// Invalid HotSpot.
        /// </summary>
        public const int InvalidHotSpot = 8142;

        /// <summary>
        /// HotSpot is missing required field.
        /// </summary>
        public const int HotSpotMissingRequiredField = 8143;

        /// <summary>
        /// Invalid Glyph.
        /// </summary>
        public const int InvalidGlyph = 8144;

        /// <summary>
        /// HotSpots has duplication.
        /// </summary>
        public const int HotSpotsHasDuplication = 8145;

        /// <summary>
        /// Invalid Placement.
        /// </summary>
        public const int InvalidPlacement = 8146;

        /// <summary>
        /// Duplicate Placement.
        /// </summary>
        public const int DuplicatePlacement = 8147;

        /// <summary>
        /// Invalid InvalidBoostAnchors.
        /// </summary>
        public const int InvalidBoostAnchors = 8148;

        /// <summary>
        /// Unsupported campaign type for Boost account.
        /// </summary>
        public const int UnsupportedCampaignTypeForBoostAccount = 8149;

        /// <summary>
        /// Account not enabled for automated CallToAction.
        /// </summary>
        public const int AccountNotEnabledForCampaignAutomatedCallToActionOptOut = 8150;

        /// <summary>
        /// Bid Adjustment not supported for Boost account.
        /// </summary>
        public const int BidAdjustmentNotSupportedForBoostAccount = 8151;

        /// <summary>
        /// Invalid NetflixTCAccepted value.
        /// </summary>
        public const int NetflixTCAcceptedValueInvalid = 8152;

        /// <summary>
        /// Account not enabled for broad match only campaign.
        /// </summary>
        public const int AccountNotEnabledForBroadMatchOnlyCampaign = 8153;

        /// <summary>
        /// Bid Strategy not supported for broad match only campaign.
        /// </summary>
        public const int BidStrategyNotSupportedForBroadMatchOnlyCampaign = 8154;

        /// <summary>
        /// Keyword Match Type not supported for broad match only campaign.
        /// </summary>
        public const int UnsupportMatchTypeForBroadMatchOnlyCampaign = 8155;

        /// <summary>
        /// Campaign Associations limit exceeded.
        /// </summary>
        public const int CampaignAssociationsLimitExceeded = 8200;

        /// <summary>
        /// Invalid Association.
        /// </summary>
        public const int InvalidAssociation = 8201;

        /// <summary>
        /// Seasonality Adjustment timestamp mismatch.
        /// </summary>
        public const int SeasonalityAdjustmentTimestampMismatch = 8202;

        /// <summary>
        /// Entities are null or empty.
        /// </summary>
        public const int EntitiesAreNullOrEmpty = 8203;

        /// <summary>
        /// Operations batch limit exceed.
        /// </summary>
        public const int OperationsBatchLimitExceeded = 8204;

        /// <summary>
        /// SeasonalityAdjustment/DataExclusion name is empty.
        /// </summary>
        public const int NameIsEmpty = 8205;

        /// <summary>
        /// SeasonalityAdjustment/DataExclusion name exceeded max length.
        /// </summary>
        public const int NameExceededMaxLen = 8206;

        /// <summary>
        /// Description is null.
        /// </summary>
        public const int DescriptionIsNull = 8207;

        /// <summary>
        /// Description exceeded max length.
        /// </summary>
        public const int DescriptionExceededMaxLen = 8208;

        /// <summary>
        /// Invalid adjustment percentage.
        /// </summary>
        public const int InvalidAdjustmentPercentage = 8209;

        /// <summary>
        /// Date should not be null.
        /// </summary>
        public const int DateShouldNotBeNull = 8210;

        /// <summary>
        /// Date granularity can only be to hours.
        /// </summary>
        public const int DateGranularityCanOnlyBeToHours = 8211;

        /// <summary>
        /// Invalid date range.
        /// </summary>
        public const int InvalidDateRange = 8212;

        /// <summary>
        /// Device type filter cannot be none.
        /// </summary>
        public const int DeviceTypeFilterCannotBeNone = 8213;

        /// <summary>
        /// Invalid CampaignAssociations and CampaignTypeFilter combination.
        /// </summary>
        public const int InvalidCampaignAssociationsAndCampaignTypeFilterCombination = 8214;

        /// <summary>
        /// Invalid CampaignAssociations length.
        /// </summary>
        public const int InvalidCampaignAssociationsLength = 8215;

        /// <summary>
        /// Invalid Campaign type.
        /// </summary>
        public const int InvalidCampaignType = 8216;

        /// <summary>
        /// Seasonality adjustment exceed limit.
        /// </summary>
        public const int SeasonalityAdjustmentExceedLimit = 8217;

        /// <summary>
        /// Invalid DataExclusion id.
        /// </summary>
        public const int InvalidDataExclusionId = 8218;

        /// <summary>
        /// DataExclusion timestamp mismatch.
        /// </summary>
        public const int DataExclusionTimestampMismatch = 8219;

        /// <summary>
        /// DataExclusion adjustment percentage should be zero.
        /// </summary>
        public const int DataExclusionAdjustmentPercentageShouldBeZero = 8220;

        /// <summary>
        /// DataExclusion exceed limit.
        /// </summary>
        public const int DataExclusionExceedLimit = 8221;

        /// <summary>
        /// StartDate comes after EndDate.
        /// </summary>
        public const int StartDateComesAfterEndDate = 8222;

        /// <summary>
        /// Duplicate items in batch.
        /// </summary>
        public const int DuplicateItemsInBatch = 8223;

        /// <summary>
        /// Invalid SeasonalityAdjustment id.
        /// </summary>
        public const int InvalidSeasonalityAdjustmentId = 8224;

        /// <summary>
        /// The customer is not eligible for Product Conversion Goal.
        /// </summary>
        public const int CustomerNotEligibleForProductConversionGoal = 8225;

        /// <summary>
        /// The customer is not eligible for InStoreVisit Goal.
        /// </summary>
        public const int CustomerNotEligibleForInStoreVisitGoal = 8226;

        /// <summary>
        /// Each customer can have only one InStoreVisit Goal.
        /// </summary>
        public const int OnlyOneInStoreVisitGoalBeAllowedPerCustomer = 8227;

        /// <summary>
        /// Each account can have only one Smart Goal.
        /// </summary>
        public const int OnlyOneSmartGoalBeAllowedPerAccount = 8228;

        /// <summary>
        /// This goal cannot be modified.
        /// </summary>
        public const int GoalIsReadOnly = 8229;

        /// <summary>
        /// Editing the Smart Goal is restricted.
        /// </summary>
        public const int SmartGoalCouldNotBeEditInSomeParameters = 8230;

        /// <summary>
        /// The selected attribution model type is incompatible with this Goal type.
        /// </summary>
        public const int AttributionModelTypeNotApplicableToGoalType = 8231;

        /// <summary>
        /// The goal name is already in use by another goal.
        /// </summary>
        public const int DuplicateGoalName = 8232;

        /// <summary>
        /// Unsupported audience target for deal
        /// </summary>
        public const int UnsupportedAudienceTargetForDeal = 8234;

        /// <summary>
        /// Exceed Max Custom Segment Criterion Count Per AdGroup.
        /// </summary>
        public const int ExceedMaxCustomSegmentCriterionCountPerAdGroup = 8235;

        /// <summary>
        /// Custom Segment Only Support Audience Campaign.
        /// </summary>
        public const int CustomSegmentOnlySupportAudienceCampaign = 8236;

        /// <summary>
        /// Customer Is Not Eligible For Keyword Targeting.
        /// </summary>
        public const int CustomerIsNotEligibleForKeywordTargeting = 8237;

        /// <summary>
        /// NegativeAdGroupCriterion Is Not Supported By CustomSegment.
        /// </summary>
        public const int NegativeAdGroupCriterionIsNotSupportedByCustomSegment = 8238;

        /// <summary>
        /// CustomSegment only support account level.
        /// </summary>
        public const int CustomSegmentOnlySupportAccountLevel = 8239;

        /// <summary>
        /// CustomSegment Not Found
        /// </summary>
        public const int CustomSegmentNotFound = 8240;

        /// <summary>
        /// InValid Custom Segment Id
        /// </summary>
        public const int InValidCustomSegmentId = 8241;

        /// <summary>
        /// Deleted Custom Segment
        /// </summary>
        public const int DeletedCustomSegment = 8242;

        /// <summary>
        /// Custom Segment Catalog Is Empty.
        /// </summary>
        public const int CustomSegmentCatalogIsEmpty = 8243;

        /// <summary>
        /// Block segment Ids are invalid
        /// </summary>
        public const int BlockedSegmentIdsInvalid = 8246;

        /// <summary>
        /// Account is not enabled for BrandSafety
        /// </summary>
        public const int AccountNotEligibleForBrandsafetyFeature = 8247;

        /// <summary>
        /// Duplicated Block Segment Ids
        /// </summary>
        public const int DuplicatedBlockedSegmentIds = 8248;

        /// <summary>
        /// The number of asset group search theme has exceeded the limit.
        /// </summary>
        public const int SearchThemeEntityLimitExceeded = 8250;

        /// <summary>
        /// Too long search theme Name.
        /// </summary>
        public const int TooLongSearchTheme = 8251;

        /// <summary>
        /// Search theme name missing.
        /// </summary>
        public const int SearchThemeNameMissing = 8252;

        /// <summary>
        /// Search theme name has invalid characters.
        /// </summary>
        public const int SearchThemeNameHasInvalidChars = 8253;

        /// <summary>
        /// Duplicate search theme name
        /// </summary>
        public const int DuplicateSearchThemeName = 8254;

        /// <summary>
        /// The account is not enabled for LinkedIn campaigns.
        /// </summary>
        public const int LinkedInCampaignsNotEnabledForAccount = 8255;

        /// <summary>
        /// Tracking template is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int TrackingTemplateNotSupportedForLinkedInCampaign = 8256;

        /// <summary>
        /// Final url suffix is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int FinalUrlSuffixNotSupportedForLinkedInCampaign = 8257;

        /// <summary>
        /// Url custom paramters is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int UrlCustomParametersSupportedForLinkedInCampaign = 8258;

        /// <summary>
        /// Ad schedule timezone setting is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign = 8259;

        /// <summary>
        /// Ad group is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int AdGroupDeletionIsNotSupportedForLinkedInCampaign = 8260;

        /// <summary>
        /// MultiLanguages is not supported for LinkedIn Campaign.
        /// </summary>
        public const int MultiLanguagesNotSupportedForLinkedInCampaign = 8261;

        /// <summary>
        /// Cannot edit LinkedIn Campaign's language.
        /// </summary>
        public const int CannotEditLinkedInCampaignLanguage = 8262;

        /// <summary>
        /// You cannot add this entity to a campaign of type LinkedIn.
        /// </summary>
        public const int EntityNotAllowedForLinkedInCampaign = 8263;

        /// <summary>
        /// Bid adjustment is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int BidAdjustmentNotSupportedForLinkedInCampaign = 8264;

        /// <summary>
        /// Specified conversion goal type is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedConversionGoalTypeForLinkedInCampaign = 8265;

        /// <summary>
        /// Specified conversion goal scope is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedConversionGoalScopeForLinkedInCampaign = 8266;

        /// <summary>
        /// Specified conversion goal count type is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedGoalCountTypeForLinkedInCampaign = 8267;

        /// <summary>
        /// Specified ExcludeFromBidding value is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedExcludeFromBiddingValueForLinkedInCampaign = 8268;

        /// <summary>
        /// Specified conversion goal attribution model is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedGoalAttributionModelForLinkedInCampaign = 8269;

        /// <summary>
        /// Change start date of adgroup is not supported for LinkedIn Campaigns that are synced to external channel
        /// </summary>
        public const int AdGroupStartDateCannotBeChangedForLinkedInCampaign = 8270;

        /// <summary>
        /// Cannot undelete LinkedIn Campaign
        /// </summary>
        public const int LinkedInCampaignUndeleteNotAllowed = 8271;

        /// <summary>
        /// Specified conversion goal click lookback window is not supported for LinkedIn Campaigns.
        /// </summary>
        public const int NotSupportedGoalClickLookbackWindowForLinkedInCampaign = 8272;

        /// <summary>
        /// The audience estimation of a active LinkedIn campaign cannot less than 300.
        /// </summary>
        public const int LinkedInCampaignAudienceEstimationBelowThreshold = 8273;

        /// <summary>
        /// Can not pause ad when ad of LinkedIn Campaign is in review.
        /// </summary>
        public const int LinkedInCampaignAdInReviewCannotPause = 8274;

        /// <summary>
        /// The account is not enabled for PMax new customer acquisition goal.
        /// </summary>
        public const int PmaxNewCustomerAcquisitionNotEnabled = 8275;

        /// <summary>
        /// Invalid additional value.
        /// </summary>
        public const int InvalidAdditionalValue = 8276;

        /// <summary>
        /// The new customer acquisition goal does not exist.
        /// </summary>
        public const int NewCustomerAcquisitionGoalDoesNotExist = 8277;

        /// <summary>
        /// The count of the audience associated with the new customer acquisition goal exceeds the limit.
        /// </summary>
        public const int NewCustomerAcquisitionAudienceCountExceedsLimit = 8278;

        /// <summary>
        /// One account can only have up to one new customer acquisition goal.
        /// </summary>
        public const int DuplicateNewCustomerAcquisitionGoal = 8279;

        /// <summary>
        /// You need at least one purchase conversion goal in your account before you can use new customer acquisition goal.
        /// </summary>
        public const int NewCustomerAcquisitionNoPurchaseGoal = 8280;

        /// <summary>
        /// Campaigns with new customer acquisition goal in bid higher mode must use Max Conversion Value or Target ROAS bid strategy.
        /// </summary>
        public const int InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode = 8281;

        /// <summary>
        /// The ID for new customer acquisition goal is invalid.
        /// </summary>
        public const int InvalidNewCustomerAcquisitionGoalId = 8282;

        /// <summary>
        /// Only purchase campaign conversion goals are allowed for campaigns with new customer acquisition enabled.
        /// </summary>
        public const int PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign = 8283;

        /// <summary>
        /// The new customer acquisition goal additional conversion value is invalid.
        /// </summary>
        public const int InvalidAdditionalConversionValue = 8284;

        /// <summary>
        /// New customer acquisition goal needs to be associated with audiences before it can be associated with campaigns
        /// </summary>
        public const int AudienceAssociationRequiredForNewCustomerAcquisitionGoal = 8285;

        /// <summary>
        /// Campaign level additional value is not allowed for bid only mode.
        /// </summary>
        public const int CampaignLevelAdditionalValueNotSupportedForBidOnlyMode = 8286;

        /// <summary>
        /// The account is not enabled for Brand Kit.
        /// </summary>
        public const int BrandKitNotEnabledForAccount = 8400;

        /// <summary>
        /// The Brand Kit id is invalid.
        /// </summary>
        public const int InvalidBrandKitId = 8401;

        /// <summary>
        /// Duplicate in Brand Kit ids.
        /// </summary>
        public const int DuplicateInBrandKitIds = 8402;

        /// <summary>
        /// The color code is invalid make sure to use Hex format #000000.
        /// </summary>
        public const int InvalidBrandKitColorCode = 8403;

        /// <summary>
        /// The font typeface is invalid.
        /// </summary>
        public const int InvalidBrandKitFontTypeface = 8404;

        /// <summary>
        /// The font weight is invalid.
        /// </summary>
        public const int InvalidBrandKitFontWeight = 8405;

        /// <summary>
        /// The font text asset type is invalid.
        /// </summary>
        public const int InvalidBrandKitFontTextAssetType = 8406;

        /// <summary>
        /// Brand Kit name is too long.
        /// </summary>
        public const int BrandKitNameTooLong = 8407;

        /// <summary>
        /// Brand Kit name is missing.
        /// </summary>
        public const int BrandKitNameMissing = 8408;

        /// <summary>
        /// Brand Kit palette has too many colors.
        /// </summary>
        public const int BrandKitPaletteColorCountExceedsLimit = 8409;

        /// <summary>
        /// Brand Kit color name is too long.
        /// </summary>
        public const int BrandKitColorNameTooLong = 8410;

        /// <summary>
        /// Brand Kit Images list exceeds the limit.
        /// </summary>
        public const int BrandKitImagesCountExceedsLimit = 8411;

        /// <summary>
        /// Brand Kit Square Logos list exceeds the limit.
        /// </summary>
        public const int BrandKitSquareLogosCountExceedsLimit = 8412;

        /// <summary>
        /// Brand Kit Landscape Logos list exceeds the limit.
        /// </summary>
        public const int BrandKitLandscapeLogosCountExceedsLimit = 8413;

        /// <summary>
        /// Invalid BrandKit Request Parameteres.
        /// </summary>
        public const int BrandKitIdsArrayShouldNotBeNullOrEmpty = 8414;

        /// <summary>
        /// BrandKitColors Array is Empty or Null in Request Parameter.
        /// </summary>
        public const int BrandKitColorsArrayShouldNotBeNullorEmpty = 8415;

        /// <summary>
        /// Brand Kit Business Name is too long.
        /// </summary>
        public const int BrandKitBusinessNameTooLong = 8416;

        /// <summary>
        /// The account is not enabled for Brand Kit phase 2.
        /// </summary>
        public const int BrandKitPhase2NotEnabledForAccount = 8417;

        /// <summary>
        /// Brand Voice Personality is too long.
        /// </summary>
        public const int BrandVoicePersonalityTooLong = 8418;

        /// <summary>
        /// Brand Voice Tones list exceeds the limit.
        /// </summary>
        public const int BrandVoiceTonesCountExceedsLimit = 8419;

        /// <summary>
        /// One or more Brand Voice Tones are too long.
        /// </summary>
        public const int BrandVoiceTonesTooLong = 8420;

        /// <summary>
        /// Brand Kit Palette Name is too long.
        /// </summary>
        public const int BrandKitPaletteNameTooLong = 8421;

        /// <summary>
        /// The account is not enabled to use campaign level dates.
        /// </summary>
        public const int AccountNotEnabledForCampaignLevelDates = 8550;

        /// <summary>
        /// Campaign level lifetime date selection should be made.
        /// </summary>
        public const int CampaignLevelDatesNotEnabled = 8551;

        /// <summary>
        /// Campaign start date should be set for campaigns with campaign level dates.
        /// </summary>
        public const int CampaignStartDateNotSet = 8552;

        /// <summary>
        /// Campaign end date should be set for lifetime budget campaigns.
        /// </summary>
        public const int CampaignEndDateNotSet = 8553;

        /// <summary>
        /// Campaign end date cannot be a over a year greater than the start date for lifetime budget campaigns.
        /// </summary>
        public const int CampaignEndDateExceedsOneYear = 8554;

        /// <summary>
        /// Cannot update start date after campaign has started.
        /// </summary>
        public const int CannotUpdateStartDateAfterCampaignStart = 8555;

        /// <summary>
        /// Cannot update budget type after campaign has started.
        /// </summary>
        public const int CannotUpdateBudgetTypeAfterCampaignStart = 8556;

        /// <summary>
        /// Cannot update campaign level lifetime dates selection after campaign start.
        /// </summary>
        public const int CannotUpdateUseCampaignLevelAfterCampaignStart = 8557;

        /// <summary>
        /// Cannot update budget type for daily budget campaigns.
        /// </summary>
        public const int AdGroupLevelDatesBudgetTypeCannotBeUpdated = 8558;

        /// <summary>
        /// Cannot update to campaign level dates for daily budget campaigns.
        /// </summary>
        public const int AdGroupLevelDatesCannotBeUpdated = 8559;

        /// <summary>
        /// Cannot set campaign level dates with chosen campaign subtype.
        /// </summary>
        public const int CampaignSubTypeNotSupportedForCampaignLevelDates = 8560;

        /// <summary>
        /// Lifetime budget amount limit exceeded.
        /// </summary>
        public const int CampaignLifetimeBudgetAmountIsAboveLimit = 8561;

        /// <summary>
        /// The account is not enabled for Video Ads Generation feature.
        /// </summary>
        public const int AccountNotEnabledForVideoAdsGeneration = 8576;

        /// <summary>
        /// No Audio matches the filter.
        /// </summary>
        public const int NoAudioMatchesFilter = 8577;

        /// <summary>
        /// Duplicate Asset Group Url Target.
        /// </summary>
        public const int AssetGroupUrlTargetDuplicated = 8661;

        /// <summary>
        /// The same value exists in the Asset Group Url Target.
        /// </summary>
        public const int AssetGroupUrlTargetValueDuplicated = 8662;

        /// <summary>
        /// There is an invalid condition in the Asset Group Url Target.
        /// </summary>
        public const int AssetGroupUrlTargetConditionInvalid = 8663;

        /// <summary>
        /// There is an invalid operator in the Asset Group Url Target.
        /// </summary>
        public const int AssetGroupUrlTargetOperatorInvalid = 8664;

        /// <summary>
        /// There is an invalid value in the Asset Group Url Target.
        /// </summary>
        public const int AssetGroupUrlTargetValueInvalid = 8665;

        /// <summary>
        /// Asset Group Url Target is invalid.
        /// </summary>
        public const int AssetGroupUrlTargetInvalid = 8666;

        /// <summary>
        /// The AnnotationGroupId isn’t valid.
        /// </summary>
        public const int InvalidExclusionTypeIdOrSubTypeId = 8667;

        /// <summary>
        /// You’ve exceeded the maximum JustificationText length of 100 characters.
        /// </summary>
        public const int AnnotationOptOutJustificationTextTooLong = 8668;

        /// <summary>
        /// The request contains more than the limit of 1,000 AnnotationOptOuts.
        /// </summary>
        public const int AnnotationOptOutBatchLimitExceeded = 8669;

        /// <summary>
        /// The request doesn’t contain any AnnotationOptOuts.
        /// </summary>
        public const int AnnotationOptOutCollectionNullOrEmpty = 8670;

        /// <summary>
        /// There is no JustificationText.
        /// </summary>
        public const int AnnotationOptOutJustificationTextNullOrEmpty = 8671;

        /// <summary>
        /// You are opted out of an annotation that the account is attempting to opt in to.
        /// </summary>
        public const int AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut = 8672;

        /// <summary>
        /// The apply offline conversions operation for LinekdIn is failed.
        /// </summary>
        public const int ApplyLinkedInOfflineConversionFailed = 8678;

        /// <summary>
        /// Failed to apply offline conversions for LinekdIn due to internal error.
        /// </summary>
        public const int ApplyLinkedInOfflineConversionInternalError = 8679;

    }
    
    /// <summary>
    /// ErrorCodes defines all the api error code as const integers.
    /// </summary>
    public static class ErrorCodesByApi
    {
        private static Dictionary <string, int>errorCodesByApiMapping;
        static ErrorCodesByApi()
        {
            errorCodesByApiMapping = new  Dictionary<string, int>();
          errorCodesByApiMapping.Add("Common" + "0", 1);

          errorCodesByApiMapping.Add("Common" + "100", 1);

          errorCodesByApiMapping.Add("Common" + "105", 1);

          errorCodesByApiMapping.Add("Common" + "106", 1);

          errorCodesByApiMapping.Add("Common" + "107", 1);

          errorCodesByApiMapping.Add("Common" + "113", 1);

          errorCodesByApiMapping.Add("Common" + "116", 1);

          errorCodesByApiMapping.Add("Common" + "201", 1);

          errorCodesByApiMapping.Add("Common" + "202", 1);

          errorCodesByApiMapping.Add("Common" + "203", 1);

          errorCodesByApiMapping.Add("Common" + "204", 1);

          errorCodesByApiMapping.Add("Common" + "205", 1);

          errorCodesByApiMapping.Add("Common" + "206", 1);

          errorCodesByApiMapping.Add("Common" + "207", 1);

          errorCodesByApiMapping.Add("Common" + "208", 1);

          errorCodesByApiMapping.Add("Common" + "209", 1);

          errorCodesByApiMapping.Add("Common" + "210", 1);

          errorCodesByApiMapping.Add("Common" + "211", 1);

          errorCodesByApiMapping.Add("Common" + "212", 1);

          errorCodesByApiMapping.Add("Common" + "213", 1);

          errorCodesByApiMapping.Add("Common" + "303", 1);

          errorCodesByApiMapping.Add("Common" + "512", 1);

          errorCodesByApiMapping.Add("Common" + "513", 1);

          errorCodesByApiMapping.Add("Common" + "514", 1);

          errorCodesByApiMapping.Add("Common" + "515", 1);

          errorCodesByApiMapping.Add("Common" + "516", 1);

          errorCodesByApiMapping.Add("Common" + "517", 1);

          errorCodesByApiMapping.Add("Common" + "518", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2001", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2002", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2003", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2004", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2005", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2006", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2007", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2008", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2009", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2010", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2011", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2012", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2013", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2014", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2015", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2016", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2017", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2018", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2019", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2020", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2021", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2022", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2023", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2024", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2025", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2026", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2027", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2028", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2029", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2030", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2031", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2032", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2033", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2034", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2035", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2036", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2037", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2038", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2039", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2040", 1);

          errorCodesByApiMapping.Add("SubmitGenerateReport" + "2041", 1);

          errorCodesByApiMapping.Add("PollGenerateReport" + "2100", 1);

          errorCodesByApiMapping.Add("PollGenerateReport" + "2101", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1003", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1004", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1005", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1006", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1032", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1101", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1102", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1103", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1104", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1105", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1106", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1108", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1109", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1110", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1111", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1113", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1114", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1115", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1121", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1122", 1);

          errorCodesByApiMapping.Add("AddCampaigns" + "1175", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1001", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1005", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1006", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1008", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1032", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1100", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1101", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1102", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1105", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1106", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1111", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1113", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1115", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1120", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1122", 1);

          errorCodesByApiMapping.Add("UpdateCampaigns" + "1501", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1100", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1102", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1107", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1118", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1119", 1);

          errorCodesByApiMapping.Add("DeleteCampaigns" + "1120", 1);

          errorCodesByApiMapping.Add("GetCampaignsByAccountId" + "1102", 1);

          errorCodesByApiMapping.Add("GetCampaignsByIds" + "1100", 1);

          errorCodesByApiMapping.Add("GetCampaignsByIds" + "1118", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToCampaigns" + "1032", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToCampaigns" + "1033", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToCampaigns" + "1034", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToCampaigns" + "1130", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1003", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1005", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1006", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1008", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1032", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1100", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1120", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1202", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1204", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1209", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1210", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1212", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1213", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1214", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1220", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1223", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1224", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1232", 1);

          errorCodesByApiMapping.Add("AddAdGroups" + "1233", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1001", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1032", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1120", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1201", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1202", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1204", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1205", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1209", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1212", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1214", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1215", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1217", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1223", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1232", 1);

          errorCodesByApiMapping.Add("UpdateAdGroups" + "1233", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1100", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1120", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1201", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1209", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1217", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1218", 1);

          errorCodesByApiMapping.Add("DeleteAdGroups" + "1219", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByIds" + "1100", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByIds" + "1120", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByIds" + "1201", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByIds" + "1218", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByIds" + "1219", 1);

          errorCodesByApiMapping.Add("GetAdGroupsByCampaignId" + "1100", 1);

          errorCodesByApiMapping.Add("SubmitAdGroupForApproval" + "1120", 1);

          errorCodesByApiMapping.Add("SubmitAdGroupForApproval" + "1201", 1);

          errorCodesByApiMapping.Add("SubmitAdGroupForApproval" + "1226", 1);

          errorCodesByApiMapping.Add("SubmitAdGroupForApproval" + "1227", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToAdGroups" + "1032", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToAdGroups" + "1033", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToAdGroups" + "1034", 1);

          errorCodesByApiMapping.Add("SetNegativeKeywordsToAdGroups" + "1244", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1003", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1006", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1007", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1008", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1120", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1201", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1215", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1505", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1506", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1508", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1509", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1510", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1511", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1512", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1513", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1514", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1515", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1516", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1517", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1519", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1532", 1);

          errorCodesByApiMapping.Add("AddKeywords" + "1533", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1001", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1120", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1201", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1215", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1500", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1501", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1502", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1504", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1505", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1506", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1508", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1509", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1510", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1511", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1512", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1513", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1514", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1515", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1516", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1528", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1531", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1532", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1533", 1);

          errorCodesByApiMapping.Add("UpdateKeywords" + "1534", 1);

          errorCodesByApiMapping.Add("KeywordQualityScore" + "1535", 1);

          errorCodesByApiMapping.Add("KeywordQualityScore" + "1536", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1008", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1120", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1201", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1215", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1501", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1502", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1528", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1529", 1);

          errorCodesByApiMapping.Add("DeleteKeywords" + "1530", 1);

          errorCodesByApiMapping.Add("GetKeywordsByIds" + "1201", 1);

          errorCodesByApiMapping.Add("GetKeywordsByIds" + "1502", 1);

          errorCodesByApiMapping.Add("GetKeywordsByIds" + "1529", 1);

          errorCodesByApiMapping.Add("GetKeywordsByIds" + "1530", 1);

          errorCodesByApiMapping.Add("GetKeywordsByAdGroupId" + "1120", 1);

          errorCodesByApiMapping.Add("GetKeywordsByAdGroupId" + "1201", 1);

          errorCodesByApiMapping.Add("GetKeywordsByAdGroupId" + "1217", 1);

          errorCodesByApiMapping.Add("GetKeywordEstimatesByBids" + "1701", 1);

          errorCodesByApiMapping.Add("GetKeywordEstimatesByBids" + "1702", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1201", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1402", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1403", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1404", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1405", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1406", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1407", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1414", 1);

          errorCodesByApiMapping.Add("AddTarget" + "1417", 1);

          errorCodesByApiMapping.Add("AddTarget" + "2909", 1);

          errorCodesByApiMapping.Add("AddTarget" + "2910", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1201", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1400", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1401", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1404", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1405", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1406", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1407", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1408", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1409", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1410", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1411", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1412", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1413", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1414", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "1417", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "2909", 1);

          errorCodesByApiMapping.Add("UpdateTarget" + "2910", 1);

          errorCodesByApiMapping.Add("DeleteTarget" + "1201", 1);

          errorCodesByApiMapping.Add("GetTargetByAdGroupId" + "1201", 1);

          errorCodesByApiMapping.Add("AddTargets" + "1418", 1);

          errorCodesByApiMapping.Add("AddTargets" + "2909", 1);

          errorCodesByApiMapping.Add("AddTargets" + "2910", 1);

          errorCodesByApiMapping.Add("UpdateTargets" + "2909", 1);

          errorCodesByApiMapping.Add("UpdateTargets" + "2910", 1);

          errorCodesByApiMapping.Add("SetTargetToAdGroup" + "2918", 1);

          errorCodesByApiMapping.Add("SetTargetToAdGroup" + "2919", 1);

          errorCodesByApiMapping.Add("SetTargetToCampaign" + "2919", 1);

          errorCodesByApiMapping.Add("AddAds" + "1201", 1);

          errorCodesByApiMapping.Add("AddAds" + "1301", 1);

          errorCodesByApiMapping.Add("AddAds" + "1302", 1);

          errorCodesByApiMapping.Add("AddAds" + "1304", 1);

          errorCodesByApiMapping.Add("AddAds" + "1306", 1);

          errorCodesByApiMapping.Add("AddAds" + "1310", 1);

          errorCodesByApiMapping.Add("AddAds" + "1313", 1);

          errorCodesByApiMapping.Add("AddAds" + "1383", 1);

          errorCodesByApiMapping.Add("AddAds" + "1384", 1);

          errorCodesByApiMapping.Add("AddAds" + "1387", 1);

          errorCodesByApiMapping.Add("AddAds" + "1389", 1);

          errorCodesByApiMapping.Add("AddAds" + "1391", 1);

          errorCodesByApiMapping.Add("AddAds" + "2805", 1);

          errorCodesByApiMapping.Add("DeleteAds" + "1120", 1);

          errorCodesByApiMapping.Add("DeleteAds" + "1201", 1);

          errorCodesByApiMapping.Add("DeleteAds" + "1308", 1);

          errorCodesByApiMapping.Add("DeleteAds" + "1309", 1);

          errorCodesByApiMapping.Add("DeleteAds" + "1351", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1008", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1120", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1201", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1217", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1301", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1302", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1303", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1306", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1307", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1309", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1310", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1313", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1356", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1360", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1363", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1383", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1384", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1387", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1389", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1391", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1403", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1404", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1405", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1406", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "1407", 1);

          errorCodesByApiMapping.Add("UpdateAds" + "2805", 1);

          errorCodesByApiMapping.Add("GetAdsByAdGroupId" + "1201", 1);

          errorCodesByApiMapping.Add("GetAdsByIds" + "1201", 1);

          errorCodesByApiMapping.Add("GetAdsByIds" + "1217", 1);

          errorCodesByApiMapping.Add("GetAdsByIds" + "1308", 1);

          errorCodesByApiMapping.Add("AddImporJobs" + "6650", 1);

          errorCodesByApiMapping.Add("AddImporJobs" + "6651", 1);

          errorCodesByApiMapping.Add("AddImporJobs" + "6652", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8131", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8132", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8133", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8134", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8135", 1);

          errorCodesByApiMapping.Add("CampaignConversionGoal" + "8138", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8225", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8226", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8227", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8228", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8229", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8230", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8231", 1);

          errorCodesByApiMapping.Add("ConversionGoal" + "8232", 1);

        }

        public static bool IsValidateApiErrorCode(String apiName, int apiErrorCode)
        {
            if (apiName == null)
            {
                return false;
            }
        
            if (errorCodesByApiMapping.ContainsKey("Common"+apiErrorCode))
            {
                return true;
            }

            if (errorCodesByApiMapping.ContainsKey(apiName+apiErrorCode))
            {
                return true;
            }

            return false;
        }
    }
}