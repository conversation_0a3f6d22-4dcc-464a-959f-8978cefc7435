﻿namespace Microsoft.Advertising.Advertiser.Api.V2.AdgroupShardingOfflineTask
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using Microsoft.Data.SqlClient;
    using System.Globalization;
    using System.Linq;
    using CampaignMiddleTierTest.Framework;
    using Microsoft.AdCenter.Shared.MT;
    using MT.Database;
    using VisualStudio.TestTools.UnitTesting;
    using System.Threading.Tasks;

    internal class DbHelper
    {
        private int customerId;
        private int accountId;

        internal DbHelper(int customerId, int accountId)
        {
            this.customerId = customerId;
            this.accountId = accountId;
        }

        private DataTable CreateEntityIdsTable(IEnumerable<Tuple<long?, long?, long?>> entityIds)
        {
            var dataTable = new DataTable("CampaignIdOrderIdEntityIdTableType")
            {
                Locale = CultureInfo.InvariantCulture
            };

            dataTable.Columns.Add("LineItemId", typeof(int));
            dataTable.Columns.Add("CampaignID", typeof(long));
            dataTable.Columns.Add("OrderID", typeof(long));
            dataTable.Columns.Add("EntityId", typeof(long));

            int i = 0;
            foreach (var tuple in entityIds)
            {
                var row = dataTable.NewRow();

                row["LineItemId"] = i++;

                if (tuple.Item1.HasValue)
                {
                    row["CampaignID"] = tuple.Item1.Value;
                }

                if (tuple.Item2.HasValue)
                {
                    row["OrderID"] = tuple.Item2.Value;
                }

                if (tuple.Item3.HasValue)
                {
                    row["EntityId"] = tuple.Item3.Value;
                }

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        public void RemoveExistingTaskEntries(long accountId, TaskDescription task)
        {
            var basicCommandStr = string.Format(
                "delete from dbo.OfflineTask where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                task.OperationType,
                this.customerId, accountId);

            if (task.IsAccountLevelTask)
            {
                var command = new SqlCommand(basicCommandStr);
                this.ExecuteTaskCommand(task, command);

                return;
            }

            foreach (var tuple in task.EntityIds)
            {
                var commandStrWithEntityIds =
                    basicCommandStr + GenerateWhereConditionFromEntityIds(tuple.Item1, tuple.Item2, tuple.Item3);

                var command = new SqlCommand(commandStrWithEntityIds);

                this.ExecuteTaskCommand(task, command);
            }
        }

        public void ModifyVideoLifeCycleStatus(int customerId, int accountId, long videoId, int lifeCycleStatusId)
        {
            if (DatabaseHelper.CheckAccountPilotFeature(customerId,accountId,1550))
            {
                ModifyVideoLifeCycleStatusInAdGroupShard(customerId, accountId, videoId, lifeCycleStatusId);
            }
            else
            {
                ModifyVideoLifeCycleStatusInMainShard(customerId, accountId, videoId, lifeCycleStatusId);
            }
        }

        public void ModifyVideoLifeCycleStatusInMainShard(int customerId, int accountId, long videoId, int lifeCycleStatusId)
        {
            var commandStr =
                $"Update dbo.Video SET LifeCycleStatusId = {lifeCycleStatusId} WHERE AccountId = {accountId} and VideoId = {videoId}";

            var command = new SqlCommand(commandStr);
            DataSet data;

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand
                    (customerId, command, out data);
        }

        public void ModifyVideoLifeCycleStatusInAdGroupShard(int customerId, int accountId, long videoId, int lifeCycleStatusId)
        {
            var commandStr =
                $"Update catalog.Video SET LifeCycleStatusId = {lifeCycleStatusId} WHERE AccountId = {accountId} and VideoId = {videoId}";

            var command = new SqlCommand(commandStr);

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(customerId, videoId, command, accountId);
        }

        public bool CheckIfDealCampaign(long? customerId, long accountId, long campaignId, bool isDealCampaign)
        {
            return CheckIfFeatureBitmaskIsSet(customerId, campaignId, isDealCampaign, 256);
        }

        private bool CheckIfFeatureBitmaskIsSet(long? customerId, long campaignId, bool isFeatureBitmaskExpected, int featueBitmaskValue)
        {
            var customerID = customerId ?? this.customerId;
            var commandStr = string.Format(
                "select num=count(*) from dbo.Campaign where CampaignID = {0} and CampaignFeatureBitMask & " + featueBitmaskValue + " = " + featueBitmaskValue,
                campaignId);

            var command = new SqlCommand(commandStr);
            DataSet data;

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand
                    ((int)customerID, command, out data);

            if (data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                var firstRow = data.Tables[0].Rows[0];
                var count = firstRow.ExtractIntRequired("num");
                if (count == 1)
                {
                    return isFeatureBitmaskExpected ? true : false;
                }
            }

            return isFeatureBitmaskExpected ? false : true;
        }

        public bool CheckOfflineTaskExist(long? customerId, long accountId, byte operationTypeId, long? entityId, bool isSharedLibraryShardDB = false, long? orderId = null, int? status = null, bool shouldExist = true)
        {
            var customerID = customerId ?? this.customerId;
            var commandStr = string.Format(
                "select num=count(*) from dbo.OfflineTask where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                operationTypeId,
                customerID,
                accountId);

            if (orderId.HasValue)
            {
                commandStr = string.Format("{0} and OrderId = {1}", commandStr, orderId.Value);
            }

            if (entityId.HasValue)
            {
                commandStr = string.Format("{0} and EntityId = {1}", commandStr, entityId.Value);
            }

            if (status.HasValue)
            {
                commandStr = string.Format("{0} and Status = {1}", commandStr, status.Value);
            }

            var command = new SqlCommand(commandStr);
            DataSet data;
            if (isSharedLibraryShardDB)
            {
                var list = TestSetting.Environment.CampaignService.CampaignSharedLibraryShardDB.ExecuteCommand
                    ((int)customerID, command);
                data = list.First();
            }
            else if (orderId.HasValue)
            {
                data = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand((int)customerId, orderId.Value, command, (int)accountId);
            }
            else
            {
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand
                    ((int)customerID, command, out data);
            }

            if (data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                var firstRow = data.Tables[0].Rows[0];
                var count = firstRow.ExtractIntRequired("num");
                if (count > 0)
                {
                    return shouldExist ? true : false;
                }
            }
            return shouldExist ? false : true;
        }

        public bool CheckOfflineTaskExist(int customerId, int accountId, byte operationTypeId, long? entityId, TaskDescription task, int? status = null)
        {
            var commandStr = string.Format(
                "select num=count(*) from dbo.OfflineTask where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                operationTypeId,
                customerId,
                accountId);

            var entityIdList = new List<long>();
            if (entityId.HasValue)
            {
                commandStr = string.Format("{0} and EntityId = {1}", commandStr, entityId.Value);
                entityIdList.Add(entityId.Value);
            }

            if (status.HasValue)
            {
                commandStr = string.Format("{0} and Status = {1}", commandStr, status.Value);
            }

            var command = new SqlCommand(commandStr);
            DataSet data;
            if (task.IsTaskInCampaignEntityLibraryShard)
            {
                var list = TestSetting.Environment.CampaignService.CampaignEntityShardDB.ExecuteCommand(customerId, accountId, entityIdList, command);
                data = list.First();
            }
            else
            {
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out data);
            }

            if (data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                var firstRow = data.Tables[0].Rows[0];
                var count = firstRow.ExtractIntRequired("num");
                if (count > 0)
                {
                    return true;
                }
            }

            return false;
        }

        internal void RemoveCampaignSizeTableEntry(byte operationTypeId, IEnumerable<long> campaignIds)
        {
            string campaignIdSet = string.Join(",", campaignIds);
            var commandStr = string.Format(
                "delete from dbo.CampaignSize where CampaignId in ({0})", campaignIdSet);

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void AddCampaignAudienceTestData(long accountId, long campaignId)
        {
            var commandStr = string.Format(
                @"
                Declare @CampaignTargetsInfo dbo.CampaignTargetsInfo
                INSERT INTO @CampaignTargetsInfo (CampaignId, IntentionBitMask, IsExclusiveBitMask, CurrentTimestamp)
                VALUES ({0}, 1, 1573384, NULL)

                DECLARE @TargetGroupDetails dbo.TargetGroupDetailEntityIdTableTypeV2
                INSERT INTO @TargetGroupDetails (EntityId, LineItemId, TargetGroupDetailId, TargetTypeId, TargetValueId, IncrementPct, Attribute, IsExclusive, ExclusivePct, ExclusionFlag, Attribute2, LifeCycleStatusId, Action)
                VALUES ({0}, 1, NULL, 22, 1, 5, NULL, 1, NULL, 0, NULL, 90, 0)

                EXEC [dbo].[prc_publicCampaignTargetBatchInsertAndUpdate_V50]
                    @CampaignTargetsInfo            = @CampaignTargetsInfo
                    ,@TargetGroupDetailEntities        = @TargetGroupDetails
                    ,@AccountId                        = {1}
                    ,@SourceId                        = 1
                    ,@ModifiedByUserId                = 1
                    ,@SessionId                     = NULL
                    ,@RequestId                     = NULL
                    ,@TrackingId                    = NULL",
                campaignId, accountId);
            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void AddOrderAudienceTestData(long accountId, long campaignId, long orderId)
        {
            var commandStr = string.Format(
                @"
                DECLARE @EntityIds dbo.OrderTargetsInfo,
                        @TargetGroupDetail dbo.TargetGroupDetailEntityIdTableTypeV3
                INSERT INTO @EntityIds (OrderId, CampaignId, IntentionBitMask, IsAIMCampaignTarget, IsExclusiveBitMask, CurrentTimestamp)
                    VALUES ({1}, {0}, 1, 0, ************, NULL)
                INSERT INTO @TargetGroupDetail (EntityId, LineItemId, TargetGroupDetailId, TargetTypeId, TargetValueId, IncrementPct, ExclusionFlag, LifeCycleStatusId, Action)
                    SELECT {1}, 1, NULL, 22, 2, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 2, NULL, 25, 3, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 3, NULL, 26, 4, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 4, NULL, 31, 5, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 5, NULL, 33, 6, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 6, NULL, 36, 7, 5, 0, 90, 0 UNION ALL
                    SELECT {1}, 7, NULL, 37, 8, 5, 0, 90, 0
                EXEC dbo.prc_publicOrderAudienceTargetBatchInsert_V50
                    @EntityIds = @EntityIds,
                    @TargetGroupDetail = @TargetGroupDetail,
                    @ModifiedByUserId = 1,
                    @AccountId = {3},
                    @SourceID = 1,
                    @SessionId = NULL,
                    @RequestId = NULL,
                    @TrackingId = NULL",
                campaignId, orderId, this.customerId, accountId);
            var command = new SqlCommand(commandStr);

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId,
                new List<long>() { orderId }, command, this.accountId);
        }

        internal List<DataSet> AddTasksToTheQueue(long accountId, TaskDescription taskDesc, bool isRemoveAccountExistingTask = true)
        {
            if (isRemoveAccountExistingTask)
            {
                RemoveExistingTaskEntries(accountId, taskDesc);
            }

            var command = new SqlCommand
            {
                CommandText = "dbo.prc_PublicOfflineTaskAddAccountLevelTask",
                CommandType = CommandType.StoredProcedure
            };

            if (!taskDesc.IsAccountLevelTask)
            {
                command.CommandText = "dbo.prc_PublicOfflineTaskAdd_V2";

                command.Parameters.Add(new SqlParameter("@EntityIds", CreateEntityIdsTable(taskDesc.EntityIds))
                {
                    SqlDbType = SqlDbType.Structured
                });
            }

            command.Parameters.Add(new SqlParameter("@CustomerID", this.customerId)
            {
                SqlDbType = SqlDbType.Int
            });
            command.Parameters.Add(new SqlParameter("@AccountID", accountId)
            {
                SqlDbType = SqlDbType.Int
            });
            command.Parameters.Add(new SqlParameter("@OperationTypeid", taskDesc.OperationType)
            {
                SqlDbType = SqlDbType.TinyInt
            });
            command.Parameters.Add(new SqlParameter("@DelayInSec", 1)
            {
                SqlDbType = SqlDbType.Int
            });
            command.Parameters.Add(new SqlParameter("@TrackingId", Guid.NewGuid())
            {
                SqlDbType = SqlDbType.UniqueIdentifier
            });

            this.ExecuteTaskCommand(taskDesc, command);

            // Below code is similar to RemoveExistingTaskEntries and selects offline tasks after test tasks were created above
            var basicCommandStr = $"select * from dbo.OfflineTask where OperationTypeID = {taskDesc.OperationType} and CustomerID = {this.customerId} and AccountID = {accountId}";

            if (taskDesc.IsAccountLevelTask)
            {
                var selectCommand = new SqlCommand(basicCommandStr);

                return this.ExecuteTaskCommand(taskDesc, selectCommand).ToList();
            }

            var dataSets = new List<DataSet>();

            foreach (var tuple in taskDesc.EntityIds)
            {
                var commandStrWithEntityIds =
                    basicCommandStr + GenerateWhereConditionFromEntityIds(tuple.Item1, tuple.Item2, tuple.Item3);

                var selectCommand = new SqlCommand(commandStrWithEntityIds);

                dataSets.AddRange(this.ExecuteTaskCommand(taskDesc, selectCommand));
            }

            return dataSets;
        }

        internal DataSet GetCampaignSizeEntries(long accountId, long? campaignId)
        {
            var command =
                new SqlCommand(
                    string.Format(
@"select
    CampaignId,
    sum(OrderCount) AS ActiveOrderCount,
    sum(DraftOrderCount) AS DraftOrderCount,
    sum(OrderItemCount) AS OrderItemCount,
    sum(AdCount) AS AdCount,
    sum(OrderAudienceCount) AS OrderAudienceCount,
    sum(NegativeKeywordCount) As NegativeKeywordCount,
    sum(InMarketAudienceExclusionCount) As InMarketAudienceExclusionCount,
    sum(DSAAutoTargetCount) as DSAAutoTargetCount,
    max(ModifiedDTim) AS ModifiedDTim
from dbo.CampaignSize
where AccountID = {0}"
+ (campaignId.HasValue ? " and CampaignId = " + campaignId : "")
+ "group by CampaignId",
accountId));

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command,
                out data);
            return data;
        }

        internal DataSet GetCampaignTargetSizeEntries(long accountId, long? campaignId)
        {
            var command =
                new SqlCommand(
                    string.Format(
@"select
    CampaignId,
    sum(CampaignTargetDetailCount) AS CampaignTargetDetailCount,
    sum(OrderTargetDetailCount) AS OrderTargetDetailCount
from dbo.CampaignTargetSize
where AccountID = {0}"
+ (campaignId.HasValue ? " and CampaignId = " + campaignId : "")
+ "group by CampaignId",
accountId));

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command,
                out data);
            return data;
        }

        internal int GetNegativeKeywordWithCatalogIdCount(int accountId, IEnumerable<long> orderIds)
        {
            var command =
                new SqlCommand(
                    string.Format(@"select count(*) as NegativeKeywordCount from NegativeKeyword
WHERE AccountId = {0}
AND LifecycleStatusId = 108
", accountId));

            IEnumerable<DataSet> data;
            data = TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(this.customerId, orderIds, command, this.accountId);
            int rowCount = 0;
            foreach (DataSet ds in data)
            {
                rowCount += Convert.ToInt32(ds.Tables[0].Rows[0]["NegativeKeywordCount"]);
            }
            return rowCount;

        }

        internal DataSet GetAudienceSizeEntries(long accountId, List<long> audienceIds)
        {
            var audienceIdSet = string.Join(",", audienceIds);
            var command =
                new SqlCommand(
                    string.Format(@"SELECT  AUS.AudienceId,
ISNULL(SUM(AUS.OrderAssociationCount),0) AS OrderAssociationCount
FROM dbo.AudienceSize AUS
WHERE AUS.AccountId = {0}
AND AUS.AudienceId in ({1})
AND LifecycleStatusId = 108
GROUP BY AUS.AudienceId", accountId, audienceIdSet));

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command,
                out data);
            return data;
        }

        internal DataSet GetCampaignAudienceSizeEntries(long accountId, List<long> audienceIds)
        {
            var audienceIdString = string.Join(",", audienceIds);
            var command =
                new SqlCommand(
                    string.Format(@"SELECT AUS.AudienceId,
                        SUM(AUS.CampaignAssociationCount) AS CampaignAssociationCount
                        FROM dbo.AudienceSize AUS
                        WHERE AUS.AccountId = {0}
                        AND AUS.AudienceId in ({1})
                        AND LifecycleStatusId = 108
                        GROUP BY AUS.AudienceId", accountId, audienceIdString));

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command,
                out data);
            return data;
        }

        internal void RemoveAudienceSizeTableEntry(long accountId, IEnumerable<long> audienceIds)
        {
            string audienceIdString = string.Join(",", audienceIds);
            var commandStr = $"delete from dbo.AudienceSize where AccountId = {accountId} AND AudienceId in ({audienceIdString})";

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void SetUsedbyTableEntry(long sharedToCustomerId, IEnumerable<long> entityIds, int sharedLibraryEntityType, int value = 0)
        {
            string entityIdString = string.Join(",", entityIds);
            var commandStr = $@"update UsedByLibrary set UsedCount = {value} where UsedByLibraryId in (
select UsedByLibraryId
from SharedEntityLibrary sel
join SharedToLibrary stl on sel.SharedEntityLibraryId = stl.SharedEntityLibraryId
join UsedByLibrary ubl on ubl.SharedToLibraryId = stl.SharedToLibraryId
where SharedEntityTypeId = {sharedLibraryEntityType} and SharedEntityId in ({entityIdString}) and SharedToCustomerId = {sharedToCustomerId})";

            var command = new SqlCommand(commandStr);

            TestSetting.Environment.CampaignService.CampaignSharedLibraryShardDB.ExecuteCommand(this.customerId, entityIds, command);
        }

        private DataTable MergeTable(IEnumerable<DataSet> datasets)
        {
            var enumerator = datasets.GetEnumerator();
            enumerator.Reset();
            Assert.IsTrue(enumerator.MoveNext(), "The result should have at leat one dataset");

            var destTable = enumerator.Current.Tables[0];

            var srcTables = new List<DataTable>();
            while (enumerator.MoveNext())
            {
                srcTables.Add(enumerator.Current.Tables[0]);
            }

            return CopyTable(destTable, srcTables);
        }

        internal bool CheckJobCompletionStatus(long accountId, TaskDescription taskDesc, out bool anyTaskFail, bool checkFullCompletion = false)
        {
            anyTaskFail = false;
            string basicCommandStr = string.Format(
                "select Status, CreatedDtim, ModifiedDtim from dbo.OfflineTask where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                taskDesc.OperationType,
                this.customerId, accountId);

            if (taskDesc.IsAccountLevelTask)
            {
                var accountLevelTaskCommand = new SqlCommand(basicCommandStr);
                IEnumerable<DataSet> accountLevelTaskDataSets = this.ExecuteTaskCommand(taskDesc, accountLevelTaskCommand);

                return checkFullCompletion ? IsStatusMarkedAsCompleted(accountLevelTaskDataSets, out anyTaskFail) : IsStatusMarkedAsCompletedOrReQueuedAsCreated(accountLevelTaskDataSets, out anyTaskFail);
            }

            foreach (var tuple in taskDesc.EntityIds)
            {
                string commandStrWithEntityIds =
                    basicCommandStr + GenerateWhereConditionFromEntityIds(tuple.Item1, tuple.Item2, tuple.Item3);

                var command = new SqlCommand(commandStrWithEntityIds);
                IEnumerable<DataSet> datasets = this.ExecuteTaskCommand(taskDesc, command);

                var finished = checkFullCompletion ? IsStatusMarkedAsCompleted(datasets, out anyTaskFail) : IsStatusMarkedAsCompletedOrReQueuedAsCreated(datasets, out anyTaskFail);

                if (!finished)
                {
                    return false;
                }
            }

            return true;
        }

        private bool IsStatusMarkedAsCompletedOrReQueuedAsCreated(IEnumerable<DataSet> datasets, out bool taskFail)
        {
            taskFail = false;
            if (!datasets.Any())
            {
                return false;
            }

            bool notCompletedAndNotReQueuedAsCreated = false;
            foreach (DataSet data in datasets)
            {
                Assert.AreEqual(1, data.Tables.Count);
                Assert.IsTrue(data.Tables[0].Rows.Count > 0);
                int status = Convert.ToInt32(data.Tables[0].Rows[0][0]);
                var createdDtim = Convert.ToDateTime(data.Tables[0].Rows[0][1]);
                var modifiedDtim = Convert.ToDateTime(data.Tables[0].Rows[0][2]);

                if (status == 4)
                {
                    taskFail = true;
                }

                if (status != 3 && !(status == 1 && modifiedDtim > createdDtim))
                {
                    notCompletedAndNotReQueuedAsCreated = true;
                }
            }

            return !notCompletedAndNotReQueuedAsCreated && !taskFail;
        }

        private bool IsStatusMarkedAsCompleted(IEnumerable<DataSet> datasets, out bool taskFail)
        {
            taskFail = false;
            if (!datasets.Any())
            {
                return false;
            }

            bool notCompletedAndNotReQueuedAsCreated = false;
            foreach (DataSet data in datasets)
            {
                Assert.AreEqual(1, data.Tables.Count);
                Assert.IsTrue(data.Tables[0].Rows.Count > 0);
                int status = Convert.ToInt32(data.Tables[0].Rows[0][0]);

                if (status == 4)
                {
                    taskFail = true;
                }

                if (status != 3)
                {
                    notCompletedAndNotReQueuedAsCreated = true;
                }
            }

            return !notCompletedAndNotReQueuedAsCreated && !taskFail;
        }

        private DataTable CopyTable(DataTable dest, IEnumerable<DataTable> src)
        {
            foreach (DataTable table in src)
            {
                foreach (DataRow row in table.Rows)
                {
                    dest.ImportRow(row);
                }
            }
            return dest;
        }

        internal DataRowCollection QueryTasksFromQueueInMain(
            long accountId,
            byte operationTypeId,
            long? campaignId = null,
            long? orderId = null,
            long? entityId = null,
            bool queryCreatedOnly = false,
            bool queryCompletedOnly = false)
        {
            var command = CreateTaskQueryCommand(accountId, operationTypeId, campaignId, orderId, entityId, queryCreatedOnly, queryCompletedOnly);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal DataRowCollection QueryTasksFromQueueInShard(
            long accountId,
            byte operationTypeId,
            long? campaignId = null,
            long? orderId = null,
            long? entityId = null,
            bool queryCreatedOnly = false,
            bool isAdgroupShardTask = true,
            bool queryCompletedOnly = false,
            string additionalWhereClause = null)
        {
            var command = CreateTaskQueryCommand(accountId, operationTypeId, campaignId, orderId, entityId, queryCreatedOnly, queryCompletedOnly, additionalWhereClause: additionalWhereClause);

            IEnumerable<DataSet> datasets = null;
            if (isAdgroupShardTask)
            {
                datasets = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(
                    this.customerId, command, this.accountId);
            }
            else
            {
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out var dataSet);
                datasets = new List<DataSet> { dataSet };
            }

            Assert.IsTrue(datasets.Count() >= 1, "invalid datasets");
            int tableCount = datasets.Select(ds => ds.Tables.Count).Sum();
            Assert.IsTrue(tableCount >= 1, "Invalid tables returned");

            var mergedTable = new DataTable();
            var tables = datasets.Select(ds => ds.Tables[0]);
            foreach (DataTable table in tables)
            {
                mergedTable.Merge(table, true, MissingSchemaAction.AddWithKey);
            }
            return mergedTable.Rows;
        }

        internal void UpdateTaskToCompleteFromQueueInShard(
            long accountId,
            byte operationTypeId,
            long? campaignId = null,
            long? orderId = null,
            long? entityId = null,
            bool isAdgroupShardTask = true)
        {
            var command = CreateTaskUpdateToCompleteCommand(accountId, operationTypeId, campaignId, orderId, entityId);

            if (isAdgroupShardTask)
            {
                TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, command, (int)accountId);
            }
            else
            {
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out var dataSet);
            }
        }

        private SqlCommand CreateTaskQueryCommand(
            long accountId,
            byte operationTypeId,
            long? campaignId,
            long? orderId,
            long? entityId,
            bool queryCreatedOnly,
            bool queryCompletedOnly,
            string additionalWhereClause = null)
        {
            var commandStr = string.Format(
                "select * from dbo.OfflineTask where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                operationTypeId,
                this.customerId,
                accountId);
            commandStr += GenerateWhereConditionFromEntityIds(campaignId, orderId, entityId);

            if (!string.IsNullOrEmpty(additionalWhereClause))
            {
                commandStr += " and " + additionalWhereClause;
            }

            if (queryCreatedOnly)
            {
                commandStr += " and [status] = 1";
            }

            if (queryCompletedOnly)
            {
                commandStr += " and [status] = 3";
            }

            return new SqlCommand(commandStr);
        }

        private SqlCommand CreateTaskUpdateToCompleteCommand(
            long accountId,
            byte operationTypeId,
            long? campaignId,
            long? orderId,
            long? entityId)
        {
            var commandStr = string.Format(
                "update dbo.OfflineTask set [status] = 3 where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                operationTypeId,
                this.customerId,
                accountId);
            commandStr += GenerateWhereConditionFromEntityIds(campaignId, orderId, entityId);

            return new SqlCommand(commandStr);
        }

        internal void UpdateTaskStartInShard(
            long accountId,
            TaskDescription taskDesc)
        {
            string basicCommandStr = string.Format(
                "update dbo.OfflineTask SET StartTime = DATEADD(HOUR, -1, GETUTCDATE()) where OperationTypeID = {0} and CustomerID = {1} and AccountID = {2}",
                taskDesc.OperationType,
                this.customerId,
                accountId);

            if (taskDesc.IsAccountLevelTask)
            {
                var accountLevelTaskCommand = new SqlCommand(basicCommandStr);
                IEnumerable<DataSet> accountLevelTaskDataSets = this.ExecuteTaskCommand(taskDesc, accountLevelTaskCommand);
            }
            else
            {
                foreach (var tuple in taskDesc.EntityIds)
                {
                    string commandStrWithEntityIds =
                        basicCommandStr + GenerateWhereConditionFromEntityIds(tuple.Item1, tuple.Item2, tuple.Item3);

                    var command = new SqlCommand(commandStrWithEntityIds);
                    IEnumerable<DataSet> datasets = this.ExecuteTaskCommand(taskDesc, command);
                }
            }                
        }

        internal bool GetSqlJobStatus(string name)
        {
            if (IsCampaignDBOnAzure())
            {
                return false;
            }

            var command =
                new SqlCommand(
                    string.Format(
                                  "declare @job_name sysname; select @job_name = '{0}_'+ DB_NAME();" +
                                  "IF EXISTS (SELECT job_id FROM msdb.dbo.sysjobs_view WHERE name = @job_name) begin EXEC msdb.dbo.sp_help_job @Job_name = @job_name; end " +
                                  "else select 0;",
                        name));

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            if (data.Tables[0].Columns.Contains("enabled"))
            {
                return Convert.ToInt32(data.Tables[0].Rows[0]["enabled"]) == 1;
            }
            else
            {
                return false;
            }
        }
        internal void MarkCampaignStatus(long campaignId, bool isDeleted)
        {
            string isDeletedStr = isDeleted ? "1" : "0";
            var command = new SqlCommand(
                string.Format(
                    "Update CampaignStatus Set IsDeleted = {0} where CampaignId = {1} ", isDeletedStr, campaignId));

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(
                this.customerId, command, this.accountId);
        }

        internal void MarkCampaignStatusInLibraryShard(long campaignId, bool isDeleted)
        {
            string cmdStr = isDeleted ?
                $@"Insert into CampaignsDeleted (AccountId, CampaignId, CreatedDTim, ModifiedDTim)
                    values ({accountId}, {campaignId}, getutcdate(), getutcdate())" :
                $"Delete from CampaignsDeleted where CampaignId = {campaignId}";
            var command = new SqlCommand(cmdStr);

            TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(
                this.customerId, command, this.accountId);
        }

        internal bool FindDeletedCampaignStatusInShard(long campaignId)
        {
            var command = new SqlCommand(
                string.Format(
                    "select 1 from CampaignStatus where CampaignId = {0} and IsDeleted = 1", campaignId));
            IEnumerable<DataSet> datasets = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(
                this.customerId, command, this.accountId);

            foreach (var ds in datasets)
            {
                if (ds.Tables[0].Rows.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        internal bool FindDeletedCampaignStatusInLibraryShard(long campaignId)
        {
            var command = new SqlCommand(
                string.Format(
                    "select 1 from CampaignsDeleted where CampaignId = {0}", campaignId));
            IEnumerable<DataSet> datasets = TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(
                this.customerId, command, this.accountId);

            foreach (var ds in datasets)
            {
                if (ds.Tables[0].Rows.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        internal void UpdateSqlJob(string name, bool enable)
        {
            var command = new SqlCommand(
                string.Format(
                    "declare @job_name sysname; select @job_name = '{0}_'+ DB_NAME(); exec msdb.dbo.sp_update_job @job_name = @job_name, @enabled = {1}",
                    name, (enable ? 1 : 0)));
            Console.WriteLine("command text:" + command.CommandText);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        private IEnumerable<DataSet> ExecuteTaskCommand(TaskDescription task, SqlCommand command)
        {
            if (task.IsTaskInShardDB)
            {
                if (task.OrderIdsForSharding == null)
                {
                    throw new ArgumentException("Please set OrderIdsForSharding to enqueue task in shard DB");
                }
                return TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand
                    (this.customerId, task.OrderIdsForSharding, command, this.accountId);
            }
            else if (task.IsTaskInNegativeKeywordDB)
            {
                return TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(this.customerId, command, this.accountId);
            }
            else if (task.IsTaskInSharedLibraryShard)
            {
                return TestSetting.Environment.CampaignService.CampaignSharedLibraryShardDB.ExecuteCommand
                    (this.customerId, command);
            }
            else if (task.IsTaskInCampaignEntityLibraryShard)
            {
                if (task.CreateTaskOnAllShards)
                {
                    return TestSetting.Environment.CampaignService.CampaignEntityShardDB.ExecuteCommand(this.customerId, this.accountId, command);
                }

                var entityIds = task.EntityIds.Where(t => t.Item3.HasValue).Select(t => t.Item3.Value);
                return TestSetting.Environment.CampaignService.CampaignEntityShardDB.ExecuteCommand(this.customerId, this.accountId, entityIds, command);
            }
            else
            {
                DataSet data;
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand
                    (this.customerId, command, out data);
                return new List<DataSet> { data };
            }
        }

        private bool IsCampaignDBOnAzure()
        {
            int partitionId = TestSetting.Environment.CampaignService.CampaignDB
                .GetPartitionId(this.customerId);
            var dbComponent = TestSetting.Environment.CampaignService.CampaignDB.GetDatabaseComponent(partitionId);
            return dbComponent.IsSqlAzure;
        }

        private string GenerateWhereConditionFromEntityIds(long? campaignId, long? orderId, long? entityId)
        {
            string commandStr = string.Empty;

            if (campaignId.HasValue)
            {
                commandStr += " and CampaignId = " + campaignId.Value;
            }
            if (orderId.HasValue)
            {
                commandStr += " and OrderID = " + orderId.Value;
            }
            if (entityId.HasValue)
            {
                commandStr += " and EntityId = " + entityId.Value;
            }

            return commandStr;
        }

        internal void RemoveExistingAdExtensionEditorialStatus(long accountId, long adExtensionId)
        {
            var command = new SqlCommand()
            {
                CommandText = @"
DELETE FROM AdExtensionItemEditorialStatus
WHERE AdExtensionId = @AdExtensionId
    and AccountId = @AccountId",
                CommandType = CommandType.Text
            };
            command.Parameters.AddWithValue("@AdExtensionId", adExtensionId);
            command.Parameters.AddWithValue("@AccountId", accountId);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void AddAdExtensionEditorialStatus(long accountId, long adExtensionId, short countryId, short languageId, short editorilStatus)
        {
            var commandStr = string.Format(
               "INSERT INTO AdExtensionItemEditorialStatus ( " +
               " AdExtensionId,AdExtensionItemId,LanguageId,CountryId,EditorialStatusId,LifeCycleStatusId,PolicyMask," +
               "EditorialStatusModifiedDTim,CreatedDTim,ModifiedDTim,AccountID,Appealability) " +
               "SELECT {0},AdExtensionItemId,{1},{2},211,108,0,getutcdate(),getutcdate(),getutcdate(),{3},0 " +
               "FROM AdExtensionItem WHERE AdExtensionId = {0}"
               , adExtensionId
               , languageId
               , countryId
               , accountId
           );

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);

        }

        internal void SetAdExtensionAppealPendingStatus(long accountId, long adExtensionId, bool isAppealPending)
        {
            var commandStr = $"UPDATE AdExtensionItem SET IsAppealPending={(isAppealPending ? "1" : "NULL")} WHERE AdExtensionId = {adExtensionId}";
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void AddAdExtensionEditorialStatusJson(long accountId, long adExtensionId, short editorilStatus)
        {
            var commandStr = "INSERT INTO AdExtensionItemEditorialStatus (AdExtensionId,AdExtensionItemId,LanguageId,CountryId,EditorialStatusId,LifeCycleStatusId,PolicyMask,EditorialStatusModifiedDTim,CreatedDTim,ModifiedDTim,AccountID,Appealability, EditorialStatusJSON) " +
               $"SELECT {adExtensionId},AdExtensionItemId,1000,-1,0,108,0,getutcdate(),getutcdate(),getutcdate(),{accountId},0,'{{\"es\":{{\"{editorilStatus}\":[0]}}}}' FROM AdExtensionItem WHERE AdExtensionId = {adExtensionId}";

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void AddAdExtensionCampaignAssociationEditorialStatus(long accountId, long adExtensionId, long campaignId, short editorialStatus)
        {
            var commandStr = $"UPDATE AdExtensionCampaign SET EffectiveEditorialStatusId={editorialStatus},ExternalEffectiveEditorialStatusId={editorialStatus}" +
               $"WHERE AdExtensionId = {adExtensionId} AND CampaignId={campaignId}";

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal DataTable[] GetAccountStatus(int accountId)
        {

            var command = new SqlCommand()
            {
                CommandText = @"select AccountId from DataPurge.DeletedAccountsForPurge where AccountId = " + accountId,
                CommandType = CommandType.Text
            };

            IEnumerable<DataSet> datasets = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(customerId, command, accountId);
            return datasets.Select(ds => ds.Tables[0]).ToArray();
        }


        internal DataTable[] GetAccountStatusInCampaignEntityShard(int accountId)
        {

            var command = new SqlCommand()
            {
                CommandText = @"select AccountId from DataPurge.DeletedAccountsForPurge where AccountId = " + accountId,
                CommandType = CommandType.Text
            };

            IEnumerable<DataSet> datasets = TestSetting.Environment.CampaignService.CampaignEntityShardDB.ExecuteCommand(customerId, accountId, command);
            return datasets.Select(ds => ds.Tables[0]).ToArray();
        }

        internal DataTable[] GetAccountStatusInCampaignNegativeKeywordShard(int accountId)
        {
            var command = new SqlCommand()
            {
                CommandText = @"select AccountId from DataPurge.DeletedAccountsForPurge where AccountId = " + accountId,
                CommandType = CommandType.Text
            };

            IEnumerable<DataSet> datasets = TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(customerId, command, accountId);
            return datasets.Select(ds => ds.Tables[0]).ToArray();
        }

        internal DataRowCollection GetAdExtensionOrderEffectiveEditorialStatus(long orderId, long adExtensionId)
        {
            var commandStr = string.Format("SELECT EffectiveEditorialStatusId, ExternalEffectiveEditorialStatusId, PolicyMask FROM AdExtensionOrder WITH (NOLOCK) WHERE OrderId={0} AND AdExtensionId = {1}", orderId, adExtensionId);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal DataRowCollection GetAdExtensionCampaignEffectiveEditorialStatus(long campaignId, long adExtensionId)
        {
            var commandStr = string.Format("SELECT EffectiveEditorialStatusId, ExternalEffectiveEditorialStatusId, PolicyMask FROM AdExtensionCampaign WITH (NOLOCK) WHERE CampaignId={0} AND AdExtensionId = {1}", campaignId, adExtensionId);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal DataRowCollection GetAdExtensionAccountEffectiveEditorialStatus(int accountId, long adExtensionId)
        {
            var commandStr = string.Format("SELECT EffectiveEditorialStatusId, ExternalEffectiveEditorialStatusId, PolicyMask FROM AdExtensionAccount WITH (NOLOCK) WHERE AccountId={0} AND AdExtensionId = {1}", accountId, adExtensionId);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal DataRowCollection GetAdExtensionVerticalCampaignEffectiveEditorialStatus(int accountId, long verticalCampaignId, long adExtensionId)
        {
            var commandStr = string.Format("SELECT EffectiveEditorialStatusId, ExternalEffectiveEditorialStatusId, PolicyMask FROM AdExtensionVerticalCampaign " +
                $"WHERE AccountId={accountId} AND VerticalCampaignId = {verticalCampaignId} AND AdExtensionId = {adExtensionId}");
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal DataRowCollection GetAdExtensionVerticalItemGroupEffectiveEditorialStatus(int accountId, long verticalCampaignId, long verticalItemGroup, long adExtensionId)
        {
            var commandStr = string.Format("SELECT EffectiveEditorialStatusId, ExternalEffectiveEditorialStatusId, PolicyMask FROM AdExtensionVerticalItemGroup " +
                $"WHERE AccountId={accountId} AND VerticalCampaignId = {verticalCampaignId} AND VerticalItemGroupId = {verticalItemGroup} AND AdExtensionId = {adExtensionId}");
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count);
            return data.Tables[0].Rows;
        }

        internal int GetDeletedOrdersCount(int customerId, long campaignId)
        {
            string query = "select count(*) from OrdersDeleted where campaignId = " + campaignId;
            return (int)TestSetting.Environment.CampaignService.CampaignDB.ExecuteScalerQuery(customerId, query);
        }

        internal int GetDeletedOrdersCountFromNegativeKeywordShard(int customerId, long campaignId, long orderId, int accountId)
        {
            var isCustomerSharded = TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.IsCustomerSharded(customerId);
            SqlCommand sqlCommand = new SqlCommand($"select count(*) from OrdersDeleted where campaignId = {campaignId} and orderId = {orderId}");

            if (isCustomerSharded)
            {
                return (int)TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteScalarQuery(customerId, orderId, sqlCommand, accountId);
            }
            else
            {
                return (int)TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteQueryInNonShard(customerId, sqlCommand);
            }
        }

        internal void UpdateCampaignToActiveStatus(IEnumerable<long> campaignIds)
        {
            string campaingIdSet = string.Join(",", campaignIds);
            var command = new SqlCommand()
            {
                CommandText = string.Format(@"
UPDATE Customer
SET LifeCycleStatusId = 11,
    FinancialStatusId = 16,
    FraudStatusId = 6,
    LockStatusId = 21
WHERE CustomerId = @CustomerId

UPDATE Account
SET LifeCycleStatusId = 31,
    FinancialStatusId = 36,
    LockStatusId = 41,
    PauseStatus = 0
WHERE AccountId = @AccountId

INSERT INTO AccountEx (AccountId,MarketSensitiveCategoryId,CreatedDTim,ModifiedDTim,ModifiedByUserId,AdCenterExpress)
SELECT A.AccountId,0,GETUTCDATE(),GETUTCDATE(),1,0
FROM Account A
LEFT JOIN AccountEx AE ON A.AccountId = AE.AccountId
WHERE A.AccountId = @AccountId
    AND AE.AccountId is NULL

UPDATE AccountEx
SET BilltoLifeCycleStatusId = 11,
    BillToFinStatusId = 16,
    BillToFraudStatusId = 6
WHERE AccountId = @AccountId

UPDATE Campaign
SET LifeCycleStatusId = 121,
    PauseStatusId = 125
WHERE CampaignId IN ({0})

INSERT INTO AccountInPartition (AccountId, CustomerId, IsInPartition, CreatedDTim, ModifiedDTim)
SELECT DISTINCT A.AccountId, A.AdvertiserCustomerId AS CustomerId, 1, GETUTCDATE(), GETUTCDATE()
FROM Campaign C
INNER JOIN Account A On C.AccountId = A.AccountId
LEFT JOIN AccountInPartition AIP On AIP.AccountId = A.AccountId
WHERE A.AccountId = @AccountId
    AND AIP.AccountId IS NULL

UPDATE AccountInPartition
SET IsInPartition = 1
WHERE AccountId = @AccountId
    AND IsInPartition <> 1

INSERT INTO CustomerInPartition (CustomerId,[Status])
SELECT DISTINCT A.AdvertiserCustomerId AS CustomerId,0
FROM Campaign C
INNER JOIN Account A On C.AccountId = A.AccountId
LEFT JOIN CustomerInPartition CIP On CIP.CustomerId = A.AdvertiserCustomerId
WHERE A.AccountId = @AccountId
    AND CIP.CustomerId IS NULL

UPDATE CustomerInPartition
SET Status = 0
WHERE CustomerId = @CustomerId
    AND Status <> 0", campaingIdSet),
                CommandType = CommandType.Text
            };

            command.Parameters.Clear();
            command.Parameters.AddWithValue("@CustomerId", this.customerId);
            command.Parameters.AddWithValue("@AccountId", accountId);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(this.customerId, command);
        }

        internal void UpdateAccountLifeCycleStatus(byte AccountLifeCycleStatus)
        {
            var command = new SqlCommand()
            {
                CommandText = string.Format(@"
UPDATE Customer
SET LifeCycleStatusId = 11,
    FinancialStatusId = 16,
    FraudStatusId = 6,
    LockStatusId = 21
WHERE CustomerId = @CustomerId

UPDATE Account
SET LifeCycleStatusId = {0},
    FinancialStatusId = 36,
    LockStatusId = 41,
    PauseStatus = 0
WHERE AccountId = @AccountId

INSERT INTO AccountEx (AccountId,MarketSensitiveCategoryId,CreatedDTim,ModifiedDTim,ModifiedByUserId,AdCenterExpress)
SELECT A.AccountId,0,GETUTCDATE(),GETUTCDATE(),1,0
FROM Account A
LEFT JOIN AccountEx AE ON A.AccountId = AE.AccountId
WHERE A.AccountId = @AccountId
    AND AE.AccountId is NULL

UPDATE AccountEx
SET BilltoLifeCycleStatusId = 11,
    BillToFinStatusId = 16,
    BillToFraudStatusId = 6
WHERE AccountId = @AccountId

INSERT INTO AccountInPartition (AccountId, CustomerId, IsInPartition, CreatedDTim, ModifiedDTim)
SELECT DISTINCT A.AccountId, A.AdvertiserCustomerId AS CustomerId, 1, GETUTCDATE(), GETUTCDATE()
FROM Campaign C
INNER JOIN Account A On C.AccountId = A.AccountId
LEFT JOIN AccountInPartition AIP On AIP.AccountId = A.AccountId
WHERE A.AccountId = @AccountId
    AND AIP.AccountId IS NULL

UPDATE AccountInPartition
SET IsInPartition = 1
WHERE AccountId = @AccountId
    AND IsInPartition <> 1

INSERT INTO CustomerInPartition (CustomerId,[Status])
SELECT DISTINCT A.AdvertiserCustomerId AS CustomerId,0
FROM Campaign C
INNER JOIN Account A On C.AccountId = A.AccountId
LEFT JOIN CustomerInPartition CIP On CIP.CustomerId = A.AdvertiserCustomerId
WHERE A.AccountId = @AccountId
    AND CIP.CustomerId IS NULL

UPDATE CustomerInPartition
SET Status = 0
WHERE CustomerId = @CustomerId
    AND Status <> 0", AccountLifeCycleStatus),
                CommandType = CommandType.Text
            };

            command.Parameters.Clear();
            command.Parameters.AddWithValue("@CustomerId", this.customerId);
            command.Parameters.AddWithValue("@AccountId", accountId);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(this.customerId, command);
        }

        internal void InsertToCampaignsActive(IEnumerable<long> campaignIds)
        {
            var command = new SqlCommand()
            {
                CommandText = @"
INSERT INTO CampaignsActive(CampaignId, LastUpdatedProcessId, AccountId)
SELECT @CampaignId, @LastUpdatedProcessId, @AccountId
WHERE NOT EXISTS (
    SELECT * FROM CampaignsActive
    WHERE CampaignId = @CampaignId
)",
                CommandType = CommandType.Text
            };

            foreach (long campaignId in campaignIds)
            {
                long lastUpdatedProcessId = campaignId / 10; // This is a mock value
                command.Parameters.Clear();
                command.Parameters.AddWithValue("@CampaignId", campaignId);
                command.Parameters.AddWithValue("@LastUpdatedProcessId", lastUpdatedProcessId);
                command.Parameters.AddWithValue("@AccountId", this.accountId);
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(this.customerId, command);
            }
        }

        internal void DeleteCampaignsActiveTable(IEnumerable<long> campaignIds)
        {
            string campaignIdsStr = string.Join(",", campaignIds);
            var command = new SqlCommand()
            {
                CommandText = @"
DELETE FROM CampaignsActive
WHERE CampaignId IN (" + campaignIdsStr + @")
",
                CommandType = CommandType.Text
            };

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(customerId, command);
        }

        internal void DeleteAccountsActiveTable(int customerId, int accountId)
        {
            string accountIdsStr = string.Join(",", accountId);
            var command = new SqlCommand()
            {
                CommandText = @"
DELETE FROM AccountsActive
WHERE AccountId IN (" + accountIdsStr + @")
",
                CommandType = CommandType.Text
            };

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(customerId, command);
        }

        internal void InsertAccountsActiveTable(int customerId, int accountId)
        {
            var command = new SqlCommand()
            {
                CommandText = @"
INSERT INTO AccountsActive (AccountId, LastUpdatedProcessId)
SELECT " + accountId + @", " + 1 + @"
WHERE NOT EXISTS (
    SELECT * FROM AccountsActive
    WHERE AccountId = " + accountId + @"
)",
                CommandType = CommandType.Text
            };

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteNonQuery(customerId, command);
        }

        internal DataTable[] GetAccountsActiveTable(int customerId, DatabaseTypes dbType, int accountId)
        {
            string accountIdsStr = string.Join(",", accountId);
            var command = new SqlCommand()
            {
                CommandText = @"
SELECT AccountId, LastUpdatedProcessId
FROM AccountsActive
WHERE AccountId IN (" + accountIdsStr + @")
ORDER BY AccountId ASC
",
                CommandType = CommandType.Text
            };

            if (dbType == DatabaseTypes.CampaignDB)
            {
                DataSet ds = null;
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out ds);
                return new[] { ds.Tables[0] };
            }
            else if (dbType == DatabaseTypes.CampaignAdGroupShard)
            {
                IEnumerable<DataSet> datasets =
                    TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(customerId, command, accountId);
                return datasets.Select(ds => ds.Tables[0]).ToArray();
            }
            else if (dbType == DatabaseTypes.CampaignNegativeKeywordShard)
            {
                IEnumerable<DataSet> datasets =
                    TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(customerId, command, accountId);
                return datasets.Select(ds => ds.Tables[0]).ToArray();
            }
            else
            {
                throw new NotImplementedException();
            }
        }

        internal DataTable[] GetCampaignsActiveTable(int customerId, IEnumerable<long> campaignIds, DatabaseTypes dbType, int accountId)
        {
            string campaignIdsStr = string.Join(",", campaignIds);
            var command = new SqlCommand()
            {
                CommandText = @"
SELECT CampaignId, LastUpdatedProcessId, AccountId
FROM CampaignsActive
WHERE CampaignId IN (" + campaignIdsStr + @")
ORDER BY CampaignId ASC
",
                CommandType = CommandType.Text
            };

            if (dbType == DatabaseTypes.CampaignDB)
            {
                DataSet ds = null;
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out ds);
                return new[] { ds.Tables[0] };
            }
            else if (dbType == DatabaseTypes.CampaignAdGroupShard)
            {
                IEnumerable<DataSet> datasets =
                    TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(customerId, command, accountId);
                return datasets.Select(ds => ds.Tables[0]).ToArray();
            }
            else if (dbType == DatabaseTypes.CampaignNegativeKeywordShard)
            {
                IEnumerable<DataSet> datasets =
                    TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(customerId, command, accountId);
                return datasets.Select(ds => ds.Tables[0]).ToArray();
            }
            else
            {
                throw new NotImplementedException();
            }
        }

        internal DataRowCollection GetNonLibraryTargetAssociationFromMain(IEnumerable<long> orderIds)
        {
            var orderIdsStr = string.Join(",", orderIds);
            var commandStr = string.Format("SELECT OrderId,TargetGroupId FROM OrderNonLibraryTargetGroup WITH (NOLOCK) WHERE  OrderId in ({0})", orderIdsStr);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count, "No results returned for OrderNonLibraryTargetGroup.");
            return data.Tables[0].Rows;
        }

        internal long GetMaxAuditRowIdFromMain(string tableName)
        {
            string commandStr = string.Format("SELECT MAX(ROWID) FROM {0}_Audit WITH (NOLOCK)", tableName);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count, "No results returned from Audit table.");
            return Convert.ToInt64(data.Tables[0].Rows[0][0].ToString());
        }

        internal DataRowCollection GetAuditRowsFromMain(string tableName, long maxRowId, IEnumerable<long> orderIds)
        {
            string orderIdsStr = string.Join(",", orderIds);
            var commandStr = string.Format("SELECT DISTINCT OrderId FROM {0}_Audit WITH (NOLOCK) WHERE RowId > {1} AND ActionFlag = 3 AND OrderId IN ({2})", tableName, maxRowId, orderIdsStr);
            var command = new SqlCommand(commandStr);
            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out data);
            Assert.AreEqual(1, data.Tables.Count, "No results returned from Audit table.");
            return data.Tables[0].Rows;
        }

        internal void ClearLabelAssociationInShard(string tableName, long labelId)
        {
            var command = new SqlCommand(
                string.Format(
                    "UPDATE {0} SET LifeCycleStatusId = 110 WHERE LabelId = {1} ",
                    tableName,
                    labelId));

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, command, this.accountId);
        }

        internal void ClearLabelMccAssociation(string tableName, long labelId)
        {
            var command = new SqlCommand(
                string.Format(
                    "UPDATE {0} SET LifeCycleStatusId = 110 WHERE LabelMccId = {1} ",
                    tableName,
                    labelId));

            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out DataSet ds);
        }

        internal void ClearLabelMccAssociationInShard(string tableName, long labelId)
        {
            var command = new SqlCommand(
                string.Format(
                    "UPDATE {0} SET LifeCycleStatusId = 110 WHERE LabelMccId = {1} ",
                    tableName,
                    labelId));

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, command, this.accountId);
        }

        internal int GetEntityLabelAssociationCount(
            string tableName,
            long accountId,
            string entityIdName,
            long entityId,
            IEnumerable<long> orderIds)
        {
            var command = new SqlCommand(
                string.Format(
                    "SELECT AssociationCount FROM {0} WHERE AccountId = {1} AND {2} = {3}",
                    tableName,
                    accountId,
                    entityIdName,
                    entityId));

            var data = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, orderIds, command, (int)accountId);

            Assert.AreEqual(1, data.Count(), string.Format("Not exactly 1 dataset returned from {0} table.", tableName));
            Assert.AreEqual(1, data.First().Tables.Count, string.Format("Not exactly 1 record returned from {0} table.", tableName));

            return Convert.ToInt32(data.First().Tables[0].Rows[0][0].ToString());
        }

        internal int GetEntityLabelMccAssociationCount(
            string tableName,
            long accountId,
            string entityIdName,
            long entityId,
            IEnumerable<long> orderIds,
            int customerId)
        {
            var command = new SqlCommand(
                string.Format(
                    "SELECT AssociationCount FROM {0} WHERE AccountId = {1} AND {2} = {3} AND OwnerCustomerId = {4}",
                    tableName,
                    accountId,
                    entityIdName,
                    entityId,
                    customerId));

            var data = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, orderIds, command, (int)accountId);

            Assert.AreEqual(1, data.Count(), string.Format("Not exactly 1 dataset returned from {0} table.", tableName));
            Assert.AreEqual(1, data.First().Tables.Count, string.Format("Not exactly 1 record returned from {0} table.", tableName));

            return Convert.ToInt32(data.First().Tables[0].Rows[0][0].ToString());
        }

        internal int GetInactiveLabelAssociationsCount(string tableName, long accountId, long campaignId, long orderId)
        {
            var command = new SqlCommand(
                string.Format(
                    "SELECT COUNT(LifeCycleStatusId) FROM {0} WHERE AccountId = {1} AND CampaignId = {2} AND OrderId = {3} AND LifeCycleStatusId = 110",
                    tableName,
                    accountId,
                    campaignId,
                    orderId));

            var data = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, new long[] { orderId }, command, (int)accountId);

            Assert.AreEqual(1, data.Count(), string.Format("Not exactly 1 dataset returned from {0} table.", tableName));
            Assert.AreEqual(1, data.First().Tables.Count, string.Format("Not exactly 1 record returned from {0} table.", tableName));

            return Convert.ToInt32(data.First().Tables[0].Rows[0][0].ToString());
        }

        internal void MarkLabelStatusAsInActiveInShard(long labelId, List<long> orderIds)
        {
            var command = new SqlCommand(
                string.Format(
                    "UPDATE dbo.LabelStatus SET LifeCycleStatusId = 110 WHERE LabelId = {0} ",
                    labelId));

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, orderIds, command, this.accountId);
        }

        internal void MarkLabelMccStatusAsInActiveInShard(long labelId, List<long> orderIds)
        {
            var command = new SqlCommand(
                string.Format(
                    "UPDATE dbo.LabelMccStatus SET LifeCycleStatusId = 110 WHERE LabelMccId = {0} ",
                    labelId));

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, orderIds, command, this.accountId);
        }

        internal void SetOfflineTaskStatusToNotStartedMainShard(
            byte operationTypeId,
            long _accountId,
            IEnumerable<long> campaignIds)
        {
            string commandStr;
            if (campaignIds.EnumerableIsNullOrEmpty())
            {
                commandStr =
                $"UPDATE OfflineTask SET STATUS=1, StartTime='2000-01-01 00:00:00' "
                + $"WHERE OperationTypeId = {operationTypeId} "
                + $" AND AccountId = {_accountId} ";
            }
            else
            {
                commandStr =
                $"UPDATE OfflineTask SET STATUS=1, StartTime='2000-01-01 00:00:00' "
                + $"WHERE OperationTypeId = {operationTypeId} "
                + $" AND CustomerId = {this.customerId} "
                + $" AND AccountId = {_accountId} "
                + $" AND CampaignId IN ({string.Join(",", campaignIds)}) ";
            }

            var command = new SqlCommand(commandStr);

            DataSet data;
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out data);
        }

        internal void SetOfflineTaskStatusToNotStartedAdgroupShard(
            byte operationTypeId,
            long _accountId,
            IEnumerable<long> campaignIds)
        {
            string commandStr;

            if (campaignIds.EnumerableIsNullOrEmpty())
            {
                commandStr = $"UPDATE OfflineTask SET STATUS=1, StartTime='2000-01-01 00:00:00' "
                    + $"WHERE OperationTypeId = {operationTypeId} "
                    + $" AND CustomerId = {this.customerId} "
                    + $" AND AccountId = {_accountId}";
            }
            else
            {
                commandStr =
                    $"UPDATE OfflineTask SET STATUS=1, StartTime='2000-01-01 00:00:00' "
                    + $"WHERE OperationTypeId = {operationTypeId} "
                    + $" AND CustomerId = {this.customerId} "
                    + $" AND AccountId = {_accountId} "
                    + $" AND CampaignId IN ({string.Join(",", campaignIds)}) ";
            }

            var command = new SqlCommand(commandStr);

            TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, command, (int)_accountId);
        }
        internal void SetOfflineTaskStatusToNotStartedNegativeKeywordShard(
            byte operationTypeId,
            long _accountId,
            IEnumerable<long> campaignIds)
        {
            var commandStr =
                $"UPDATE OfflineTask SET STATUS=1, StartTime='2000-01-01 00:00:00' "
                + $"WHERE OperationTypeId = {operationTypeId} "
                + $" AND CustomerId = {this.customerId} "
                + $" AND AccountId = {_accountId} "
                + $" AND CampaignId IN ({string.Join(",", campaignIds)}) ";

            var command = new SqlCommand(commandStr);

            TestSetting.Environment.CampaignService.CampaignNegativeKeywordShardDB.ExecuteCommand(this.customerId, command, (int)_accountId);
        }

        internal Dictionary<long, int> GetUsedbyCountByEntity(int customerId, int? accountId, List<Tuple<long, short, int>> sharedLibraryEntities)
        {
            var command = new SqlCommand
            {
                CommandText = "dbo.prc_PublicGetSharedEntityLibraryByEntityIds",
                CommandType = CommandType.StoredProcedure
            };

            command.Parameters.Add(new SqlParameter("@CustomerID", customerId)
            {
                SqlDbType = SqlDbType.Int
            });

            if (accountId != null)
            {
                command.Parameters.Add(new SqlParameter("@AccountID", accountId)
                {
                    SqlDbType = SqlDbType.Int
                });
            }

            command.Parameters.Add(new SqlParameter("@SourceID", 1)
            {
                SqlDbType = SqlDbType.Int
            });
            command.Parameters.Add(new SqlParameter("@EntityListTableType", CreateShareLibraryEntityIdsTable(sharedLibraryEntities.Select(t => Tuple.Create(t.Item1, t.Item2))))
            {
                SqlDbType = SqlDbType.Structured
            });

            var result = TestSetting.Environment.CampaignService.CampaignSharedLibraryShardDB.ExecuteCommand(this.customerId, sharedLibraryEntities.Select(t => t.Item1), command);

            return CreateEntityUsedbyDictionary(result, sharedLibraryEntities.ToDictionary(t => t.Item1, t => t.Item3));
        }

        internal void DeleteAttributes(
            long _accountId,
            IEnumerable<long> attributeIds)
        {
            if (attributeIds.Any())
            {
                var attributeIdsStr = String.Join(",", attributeIds);
                var commandStr =
                     $"Delete FROM dbo.AdCustomizerAttribute "
                   + $"WHERE AccountId = {_accountId} "
                   + $" AND AttributeId in ({attributeIdsStr}) ";
                var command = new SqlCommand(commandStr);
                TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.customerId, command, out DataSet data);
            }
        }

        private Dictionary<long, int> CreateEntityUsedbyDictionary(IEnumerable<DataSet> dataSet, Dictionary<long, int> usedbyTypeIdByEntityId)
        {
            var result = new Dictionary<long, int>();
            foreach (var ds in dataSet)
            {
                if (ds.Tables != null && ds.Tables.Count == 2)
                {
                    var table = ds.Tables[1];
                    foreach (DataRow row in table.Rows)
                    {
                        var sharedEntityId = row.ExtractLongRequired("SharedEntityId");
                        var usedByTypeId = row.ExtractIntRequired("UsedByTypeId");
                        var usedCount = row.ExtractIntRequired("UsedCount");
                        if (usedbyTypeIdByEntityId.TryGetValue(sharedEntityId, out var expectUsedby)
                            && expectUsedby == usedByTypeId)
                        {
                            if (result.TryGetValue(sharedEntityId, out var existCount))
                            {
                                usedCount += existCount;
                            }
                            result[sharedEntityId] = usedCount;
                        }
                    }
                }
            }
            return result;
        }

        private DataTable CreateShareLibraryEntityIdsTable(IEnumerable<Tuple<long, short>> sharedLibraryEntities)
        {
            var dataTable = new DataTable("EntityListTableType")
            {
                Locale = CultureInfo.InvariantCulture
            };

            dataTable.Columns.Add("LineItemId", typeof(int));
            dataTable.Columns.Add("EntityId", typeof(long));
            dataTable.Columns.Add("EntitytypeId", typeof(short));

            int i = 0;
            foreach (var tuple in sharedLibraryEntities)
            {
                var row = dataTable.NewRow();

                row["LineItemId"] = i++;
                row["EntityId"] = tuple.Item1;
                row["EntitytypeId"] = tuple.Item2;

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        private int GetDummyKeywordsCount(bool isPredictiveMatchingEnabled, long accountId, List<long> campaignIds, List<long> orderIds, bool? areDeleted = null)
        {
            var keywordText = isPredictiveMatchingEnabled ? "(Predictive matching)" : "(Predictive targeting)";
            var command = new SqlCommand(
                string.Format(
                    "select * from OrderItem where AccountId={0} and CampaignId in ({1}) and MatchTypeId=9 and Keyword='{2}' {3}",
                    accountId,
                    campaignIds.ToDelimitedString(),
                    keywordText,
                    areDeleted.HasValue ? (areDeleted.Value ? "and LifecycleStatusId = 163" : "and LifecycleStatusId <> 163") : ""));

            var data = TestSetting.Environment.CampaignService.CampaignShardDB.ExecuteCommand(this.customerId, orderIds, command, this.accountId);

            int totalRowCount = 0;
            foreach (var dataset in data)
            {
                var tableCount = dataset.Tables.Count;
                Assert.AreEqual(1, tableCount);

                totalRowCount += dataset.Tables[0].Rows.Count;
            }

            return totalRowCount;
        }

        internal Task WaitForPredictiveTargetingKeywordsCount(int expectedCount, int accountId, long campaignId, long orderId, bool? areDeleted)
        {
            return WaitForPredictiveTargetingKeywordsCount(expectedCount, accountId, new List<long> { campaignId }, new List<long> { orderId }, areDeleted);
        }

        internal Task WaitForPredictiveTargetingKeywordsCount(int expectedCount, int accountId, long campaignId, List<long> orderIds, bool? areDeleted)
        {
            return WaitForPredictiveTargetingKeywordsCount(expectedCount, accountId, new List<long> { campaignId }, orderIds, areDeleted);
        }

        internal Task WaitForPredictiveTargetingKeywordsCount(int expectedCount, int accountId, List<long> campaignIds, List<long> orderIds, bool? areDeleted)
        {
            return WaitForDummyKeywordsCount(false, expectedCount, accountId, campaignIds, orderIds, areDeleted);
        }

        internal Task WaitForPredictiveMatchingKeywordsCount(int expectedCount, int accountId, long campaignId, long orderId, bool? areDeleted)
        {
            return WaitForPredictiveMatchingKeywordsCount(expectedCount, accountId, new List<long> { campaignId }, new List<long> { orderId }, areDeleted);
        }

        internal Task WaitForPredictiveMatchingKeywordsCount(int expectedCount, int accountId, long campaignId, List<long> orderIds, bool? areDeleted)
        {
            return WaitForPredictiveMatchingKeywordsCount(expectedCount, accountId, new List<long> { campaignId }, orderIds, areDeleted);
        }

        internal Task WaitForPredictiveMatchingKeywordsCount(int expectedCount, int accountId, List<long> campaignIds, List<long> orderIds, bool? areDeleted)
        {
            return WaitForDummyKeywordsCount(true, expectedCount, accountId, campaignIds, orderIds, areDeleted);
        }

        internal Task WaitForDummyKeywordsCount(bool isPredictiveMatchingEnabled, int expectedCount, int accountId, List<long> campaignIds, List<long> orderIds, bool? areDeleted)
        {
            return WaitUtils.WaitFor<int>(async () =>
            {
                var actualCount = await Task.Run(() => this.GetDummyKeywordsCount(isPredictiveMatchingEnabled, accountId, campaignIds, orderIds, areDeleted));
                return (expectedCount == actualCount, actualCount);

            }, $"Timed out waiting for the number of {(areDeleted.HasValue ? (areDeleted.Value ? "deleted" : "non-deleted") : "all")} dummy keywords to equal {expectedCount} across ad groups [{string.Join(",", orderIds)}] of campaigns [{string.Join(",", campaignIds)}] under account {accountId}");
        }
    }
}

