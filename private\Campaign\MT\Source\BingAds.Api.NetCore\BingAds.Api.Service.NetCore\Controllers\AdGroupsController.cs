namespace Microsoft.BingAds.Api.Service.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using AspNet.OData;
    using AspNet.OData.Query;
    using AspNetCore.Mvc;
    using Microsoft.OData.UriParser;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Reporting;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.DataContract;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.BingAds.Api.Model;
    using Microsoft.BingAds.Api.Repositories;
    using Microsoft.BingAds.Api.Repositories.Validators;
    using Microsoft.BingAds.Api.Repository.CustomQuery;
    using Microsoft.BingAds.Api.Repository.Helpers;
    using Microsoft.BingAds.Api.Repository.Repositories.Dimension;
    using Microsoft.BingAds.Api.Repository.Validators;
    using Microsoft.BingAds.Api.Service.Attribute;
    using Repository;
    using DimensionReportType = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Reporting.DimensionReportType;

    [VersionRange(2)]
    public class AdGroupsController : BaseController
    {
        private IAdGroupsRepository adgroupsRepository;
        private IValidator<string, DateRange> dateRangeValidator;
        private IValidator<string, NullifyNotApplyBidAmountsFlag> nullifyNotApplyBidAmountsValidator;
        private IValidator<string, IncludeSmartCampaignFlag> includeSmartCampaignFilterValidator;
        private IValidator<string, IncludeHotelCampaignFlag> includeHotelCampaignFilterValidator;
        private IValidator<PilotingContext<ODataQueryOptions<AdGroup>>, QueryOptionsValidationResult> entityQueryOptionsValidator;
        private IValidator<PilotingContext<ODataQueryOptions<AdGroup>>, ODataActionParameters, string, GridDataRequestValidationResult> gridDataRequestValidator;
        private IValidator<POPDateRange, string, PerformanceTimeSeriesValidationResult> performanceTimeSeriesOptionsValidator;
        private IPerformanceTimeSeriesRepository perfTimeSeriesRepository;
        private IDimensionRepositoryFactory dimensionRepositoryFactory;
        private IDimensionReportHelperFactory dimensionReportHelperFactory;
        private Func<CallContext> callContextGetter;
        private IAlertInformationRepository alertInformationRepository;
        private IMtSelectConverter selectConverter;

        public AdGroupsController(IAdGroupsRepository repository,
            IValidator<string, DateRange> dateRangeValidator,
            IValidator<PilotingContext<ODataQueryOptions<AdGroup>>, QueryOptionsValidationResult> entityQueryOptionsValidator,
            IValidator<PilotingContext<ODataQueryOptions<AdGroup>>, ODataActionParameters, string, GridDataRequestValidationResult> gridDataRequestValidator,
            IValidator<POPDateRange, string, PerformanceTimeSeriesValidationResult> performanceTimeSeriesOptionsValidator,
            IValidator<string, IncludeSmartCampaignFlag> includeSmartCampaignFilterValidator,
            IValidator<string, IncludeHotelCampaignFlag> includeHotelCampaignFilterValidator,
            IValidator<string, NullifyNotApplyBidAmountsFlag> nullifyNotApplyBidAmountsValidator,
            IPerformanceTimeSeriesRepository perfTimeSeriesRepository,
            IDimensionRepositoryFactory dimensionRepositoryFactory,
            IDimensionReportHelperFactory dimensionReportHelperFactory,
            Func<CallContext> callContextGetter,
            IAlertInformationRepository alertInformationRepository,
            IMtSelectConverter selectConverter)
        {
            this.dateRangeValidator = dateRangeValidator;
            this.adgroupsRepository = repository;
            this.entityQueryOptionsValidator = entityQueryOptionsValidator;
            this.gridDataRequestValidator = gridDataRequestValidator;
            this.perfTimeSeriesRepository = perfTimeSeriesRepository;
            this.performanceTimeSeriesOptionsValidator = performanceTimeSeriesOptionsValidator;
            this.includeSmartCampaignFilterValidator = includeSmartCampaignFilterValidator;
            this.includeHotelCampaignFilterValidator = includeHotelCampaignFilterValidator;
            this.nullifyNotApplyBidAmountsValidator = nullifyNotApplyBidAmountsValidator;
            this.dimensionRepositoryFactory = dimensionRepositoryFactory;
            this.dimensionReportHelperFactory = dimensionReportHelperFactory;
            this.callContextGetter = callContextGetter;
            this.alertInformationRepository = alertInformationRepository;
            this.selectConverter = selectConverter;
        }

        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [EnableQuery]
        [LatencyPerfCounter("GetAdGroupByAdGroupId")]
        public async Task<IActionResult>
            GetAdGroup(
            long customerId,
            long accountId,
            long campaignId,
            long adGroupId)
        {
            var result = await adgroupsRepository.Get(customerId, accountId, campaignId, adGroupId, true);
            return result.SingleEntityResponse(Request, logger);
        }

        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetAdGroupsByAccountId")]
        public async Task<IActionResult>
            GetAdGroups(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataQueryOptions<AdGroup> options)
        {
            return await GetAdGroupsFromRepository(customerId, accountId, null, options);
        }

        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetAdGroupsByCampaignId")]
        public async Task<IActionResult>
            GetAdGroups(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataQueryOptions<AdGroup> options)
        {
            return await GetAdGroupsFromRepository(customerId, accountId, campaignId, options);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupGridData")]
        public async Task<IActionResult>
            GridData(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            ODataQueryOptions<AdGroup> options,
            ODataActionParameters parameters)
        {
            return await GetAdGroupsFromRepository(customerId, accountId, null, options, parameters);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupGridData")]
        public async Task<IActionResult>
            GridData(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            ODataQueryOptions<AdGroup> options,
            ODataActionParameters parameters)
        {
            return await GetAdGroupsFromRepository(customerId, accountId, campaignId, options, parameters);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("GetAdGroupsCount")]
        public async Task<IActionResult>
            GetAdGroupsCount(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            string campaignIds)
        {
            var campaignStringIdList = campaignIds.Split(',');
            var campaignLongIds = new List<long>();
            foreach (string campaignStringId in campaignStringIdList)
            {
                long campaignId = -1;
                if (long.TryParse(campaignStringId, out campaignId))
                {
                    campaignLongIds.Add(campaignId);
                }
                else
                {
                    var error = new AdsApiError() { Code = ApiErrorCodes.UnknownProperty, Property = $"campaignID {campaignStringId}" };
                    return error.CreateErrorResponse(Request, logger, HttpStatusCode.BadRequest);
                }
            }

            return await GetAdGroupsCountFromRepository(customerId, accountId, campaignLongIds);
        }


        [HttpPost]
        [EnableQuery]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
        GetAdGroupCount(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromBody]ODataActionParameters parameters)
        {
            if (parameters == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "parameter can't be null");
            }

            List<long> campaigns = null;
            if (parameters["campaignIds"] != null)
            {
                if (!(parameters["campaignIds"] is IEnumerable<long> campaignIds))
                {
                    return Request.CreateResponse(HttpStatusCode.BadRequest, "Campaign Ids are required.");
                }
                else
                {
                    campaigns = campaignIds.ToList();
                }
            }

            return await GetAdGroupsCountFromRepository(customerId, accountId, campaigns);
        }

        [HttpPatch]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementFullControl)]
        [LatencyPerfCounter("AdGroupUpdateBidAndResetAutoTargetSource")]
        public async Task<IActionResult>
            UpdateBidAndResetAutoTargetSource(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            [FromODataUri] long adGroupId,
            ODataActionParameters parameters)
        {
            double? bid = parameters?["Bid"] as double?;
            if (!bid.HasValue)
            {
                var error = new AdsApiError() { Code = ApiErrorCodes.EntityIsNull, Property = "Bid" };
                return error.CreateErrorResponse(Request, logger, HttpStatusCode.BadRequest);
            }

            var result = await this.adgroupsRepository.UpdateBidAndResetAutoTargetSource(customerId, accountId, campaignId, adGroupId, bid.Value);

            if (result.Items.Single())
            {
                return Request.CreateResponse(HttpStatusCode.OK);
            }
            else
            {
                if (result.ApiErrors.OrEmpty().Any(e => e.Code.Equals(ApiErrorCodes.TooManyCustomBidsInAdGroup)))
                {
                    return result.ApiErrors.CreateErrorResponse(Request, logger, HttpStatusCode.BadRequest);
                }

                return result.ApiErrors.CreateErrorResponse(Request, logger, HttpStatusCode.InternalServerError);
            }
        }

        [HttpPatch]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementFullControl)]
        [LatencyPerfCounter("AdGroupUpdateBidAndResetKeywordSource")]
        public async Task<IActionResult>
            UpdateBidAndResetKeywordSource(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            [FromODataUri] long adGroupId,
            ODataActionParameters parameters)
        {
            double? bid = parameters?["Bid"] as double?;
            if (!bid.HasValue)
            {
                var error = new AdsApiError() { Code = ApiErrorCodes.EntityIsNull, Property = "Bid" };
                return error.CreateErrorResponse(Request, logger, HttpStatusCode.BadRequest);
            }

            var result = await this.adgroupsRepository.UpdateBidAndResetKeywordSource(customerId, accountId, campaignId, adGroupId, bid.Value);

            if (result.Items.Single())
            {
                return Request.CreateResponse(HttpStatusCode.OK);
            }
            else
            {
                if (result.ApiErrors.OrEmpty().Any(e => e.Code.Equals(ApiErrorCodes.TooManyCustomBidsInAdGroup)))
                {
                    return result.ApiErrors.CreateErrorResponse(Request, logger, HttpStatusCode.BadRequest);
                }

                return result.ApiErrors.CreateErrorResponse(Request, logger, HttpStatusCode.InternalServerError);
            }
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupDimensionReport")]
        public async Task<IActionResult>
            DimensionReport(long customerId, long accountId, long campaignId, long adGroupId, DimensionReportType reportType, int locale, ODataQueryOptions<DimensionRow> options)
        {
            var reportHelper = this.dimensionReportHelperFactory.GetDimensionReportHelper(reportType);
            if (!reportHelper.IsReportTypeSupported(reportType))
            {
                var error = new AdsApiError()
                {
                    // todo create a DimensionReportTypeNotSupported error code
                    Code = ApiErrorCodes.NotSupported,
                    Property = "ReportType",
                    Message = $"{reportType} not supported"
                };

                return error.CreateErrorResponse(this.Request, this.logger, System.Net.HttpStatusCode.BadRequest);
            }

            var dateRangeValidation = reportHelper.ValidateDateRange(options);
            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var pilotingContext = new PilotingContext<ODataQueryOptions<DimensionRow>>(options, this.callContextGetter().AuthorizationContext);
            var validationResult = reportHelper.ValidateGridDataSelection(pilotingContext);
            if (validationResult.Errors.OrEmpty().Any())
            {
                return validationResult.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var selectValidation = reportHelper.ValidateSelectedColumns(options);
            if (selectValidation.Errors.OrEmpty().Any())
            {
                return selectValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            var validationErrors = DimensionValidationHelper.ValidateGet(
                this.logger,
                reportType,
                dateRangeValidation.Result,
                validationResult.Result.GridDataSelection,
                selectValidation.Result.Values.ToList(),
                campaignId,
                adGroupId,
                false);
            if (validationErrors.Any())
            {
                return validationErrors.CreateErrorResponse(this.Request, this.logger);
            }

            var result = await this.dimensionRepositoryFactory
                .GetReportRepository(DimensionReportScope.AdGroup, reportType)
                .Get(
                    customerId,
                    accountId,
                    reportType,
                    dateRangeValidation.Result,
                    validationResult.Result.GridDataSelection,
                    selectValidation.Result,
                    locale,
                    campaignId,
                    adGroupId);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupPerformanceTimeSeriesByAccountId")]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            ODataQueryOptions<AdGroup> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(options, this.callContextGetter().AuthorizationContext);
            return await PerformanceTimeSeriesHelper.HandleRequest<AdGroup>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.entityQueryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.AdGroup, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, null, null, currentCustomerId: null);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupPerformanceTimeSeriesByAccountId")]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            int? currentCustomerId,
            ODataQueryOptions<AdGroup> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(options, this.callContextGetter().AuthorizationContext);
            return await PerformanceTimeSeriesHelper.HandleRequest<AdGroup>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.entityQueryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.AdGroup, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, null, null, currentCustomerId: currentCustomerId);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupPerformanceTimeSeriesByCampaignId")]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            int? currentCustomerId,
            ODataQueryOptions<AdGroup> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(options, this.callContextGetter().AuthorizationContext);
            return await PerformanceTimeSeriesHelper.HandleRequest<AdGroup>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.entityQueryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.AdGroup, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, campaignId, null, currentCustomerId: currentCustomerId);
        }

        [HttpGet]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("AdGroupPerformanceTimeSeriesByCampaignId")]
        public async Task<IActionResult>
            PerformanceTimeSeries(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            Granularity granularity,
            DateTimeOffset startDate,
            DateTimeOffset endDate,
            DateTimeOffset? comparisonStartDate,
            DateTimeOffset? comparisonEndDate,
            string metrics,
            bool movingAverage,
            ODataQueryOptions<AdGroup> options)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(options, this.callContextGetter().AuthorizationContext);
            return await PerformanceTimeSeriesHelper.HandleRequest<AdGroup>(this.Request, this.logger, this.performanceTimeSeriesOptionsValidator, this.entityQueryOptionsValidator, this.perfTimeSeriesRepository, InlineChartEntityType.AdGroup, granularity, startDate, endDate, comparisonStartDate, comparisonEndDate, metrics, movingAverage, pilotingContext, campaignId, null, currentCustomerId: null);
        }

        // TODO UI should stop using POST so it can be removed
        [AcceptVerbs("POST", "GET")]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        public async Task<IActionResult>
            AlertInformation(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            [FromODataUri] long adGroupId,
            ODataQueryOptions<AdGroup> options)
        {
            return await AlertInformationHelper.HandleRequest(
                this.logger,
                this.Request,
                this.alertInformationRepository,
                this.selectConverter,
                customerId,
                accountId,
                campaignId,
                adGroupId,
                options);
        }

        [HttpPost]
        [Service.Authorization.Authorize(AuthorizationActions.CampaignManagementLoad)]
        [LatencyPerfCounter("LoadAdGroupChangeHistoryDetails")]
        public async Task<IActionResult> LoadChangeHistoryDetails(
            [FromODataUri] long customerId,
            [FromODataUri] long accountId,
            [FromODataUri] long campaignId,
            [FromODataUri] long adGroupId,
            ODataQueryOptions<DimensionRow> options,
            ODataActionParameters parameters)
        {
            var reportType = DimensionReportType.ChangeHistoryNewDetailReport;
            IDimensionReportHelper reportHelper = this.dimensionReportHelperFactory.GetDimensionReportHelper(reportType);
            Validation<DateRange> dateRangeValidation = reportHelper.ValidateDateRange(options);

            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(Request, this.logger);
            }

            var pilotingContext = new PilotingContext<ODataQueryOptions<DimensionRow>>(options, this.callContextGetter().AuthorizationContext);
            Validation<QueryOptionsValidationResult> queryOptionsValidation = reportHelper.ValidateGridDataSelection(pilotingContext);

            if (queryOptionsValidation.Errors.OrEmpty().Any())
            {
                return queryOptionsValidation.Errors.CreateErrorResponse(Request, this.logger);
            }

            var selectValidation = reportHelper.ValidateSelectedColumns(options);

            if (selectValidation.Errors.OrEmpty().Any())
            {
                return selectValidation.Errors.CreateErrorResponse(this.Request, this.logger);
            }

            List<AdsApiError> validationErrors = DimensionValidationHelper.ValidateGet(
                this.logger,
                reportType,
                dateRangeValidation.Result,
                queryOptionsValidation.Result.GridDataSelection,
                selectValidation.Result.Values.ToList(),
                campaignId,
                adGroupId,
                false);

            if (validationErrors.Any())
            {
                return validationErrors.CreateErrorResponse(Request, this.logger);
            }

            if (parameters == null)
            {
                validationErrors.Add(new AdsApiError {Code = ApiErrorCodes.EntityIsNull, Property = "parameters", Message = "Action Parameters null"});
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (!parameters.TryGetValue("ChangeHistorySummaryRows", out object parameterValue))
            {
                validationErrors.Add(new AdsApiError {Code = ApiErrorCodes.EntityDoesNotExist, Property = "ChangeHistorySummaryRows", Message = "Missing param ChangeHistorySummaryRows"});
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            if (parameterValue is not IEnumerable<ChangeHistorySummaryRow> changeHistorySummaryRows)
            {
                validationErrors.Add(new AdsApiError
                {
                    Code = ApiErrorCodes.EntityIsUnsupported, Property = "ChangeHistorySummaryRows",
                    Message = "The type of ChangeHistorySummaryRows should be IEnumerable<ChangeHistorySummaryRow>"
                });
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            List<ChangeHistorySummaryRow> changeHistoryRows = changeHistorySummaryRows.ToList();

            if (!changeHistoryRows.Any())
            {
                validationErrors.Add(new AdsApiError {Code = ApiErrorCodes.EntityDoesNotExist, Property = "ChangeHistorySummaryRows", Message = "ChangeHistorySummaryRows are empty"});
                return validationErrors.CreateErrorResponse(base.Request, base.logger);
            }

            var reportRepository = (IChangeHistoryNewDimensionReportRepository) this.dimensionRepositoryFactory.GetReportRepository(
                DimensionReportScope.Account,
                reportType);

            var lcid = options.GetLcidFromHeader();
            var result = await reportRepository.GetChangeHistoryDetails(
                customerId,
                accountId,
                reportType,
                dateRangeValidation.Result,
                queryOptionsValidation.Result.GridDataSelection,
                selectValidation.Result,
                lcid,
                changeHistoryRows,
                campaignId,
                adGroupId).ConfigureAwait(false);

            return result.MultipleEntitiesResponse(base.Request, base.logger);
        }

        private async Task<IActionResult>
            GetAdGroupsFromRepository(
            long customerId,
            long accountId,
            long? campaignId,
            ODataQueryOptions<AdGroup> options)
        {
            Validation<DateRange> dateRangeValidation = dateRangeValidator.Validate(Request.RequestQueryString());

            if (dateRangeValidation.Errors.OrEmpty().Any())
            {
                return dateRangeValidation.Errors.CreateErrorResponse(Request, logger);
            }

            Validation<NullifyNotApplyBidAmountsFlag> nullifyNotApplyBidAmountsValidation = this.nullifyNotApplyBidAmountsValidator.Validate(Request.RequestQueryString());

            if (nullifyNotApplyBidAmountsValidation.Errors.OrEmpty().Any())
            {
                return nullifyNotApplyBidAmountsValidation.Errors.CreateErrorResponse(Request, logger);
            }

            Validation<IncludeSmartCampaignFlag> includeSmartCampaignValidation = this.includeSmartCampaignFilterValidator.Validate(Request.RequestQueryString());
            if (includeSmartCampaignValidation.Errors.OrEmpty().Any())
            {
                return includeSmartCampaignValidation.Errors.CreateErrorResponse(Request, logger);
            }

            // TODO - nihall - use this variable when working on adGroup
            Validation<IncludeHotelCampaignFlag> includeHotelCampaignValidation = this.includeHotelCampaignFilterValidator.Validate(Request.RequestQueryString());
            if (includeHotelCampaignValidation.Errors.OrEmpty().Any())
            {
                return includeHotelCampaignValidation.Errors.CreateErrorResponse(Request, logger);
            }

            PilotingContext<ODataQueryOptions<AdGroup>> pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(
                options,
                this.callContextGetter().AuthorizationContext);

            Validation<QueryOptionsValidationResult> queryOptionsValidation = this.entityQueryOptionsValidator.Validate(pilotingContext);

            if (queryOptionsValidation.Errors.OrEmpty().Any())
            {
                return queryOptionsValidation.Errors.CreateErrorResponse(Request, logger);
            }

            var lcid = options.GetLcidFromHeader();
            var result = await adgroupsRepository.Get(
                customerId,
                accountId,
                campaignId,
                new GridDataRequestValidationResult
                {
                    DateRange = dateRangeValidation.Result,
                    GridDataSelection = queryOptionsValidation.Result.GridDataSelection,
                    AggregateDataFilter = queryOptionsValidation.Result.AggregateDataFilter,
                    IncludeSOVBIData = queryOptionsValidation.Result.IncludeSOVBIData,
                    IncludeSmartCampaignData = includeSmartCampaignValidation.Result.IncludeSmartCampaign,
                    NullifyNotApplyBidAmounts = nullifyNotApplyBidAmountsValidation.Result.NullifyNotApplyBidAmounts,
                    SelectedColumns = queryOptionsValidation.Result.SelectedColumns,
                },
                lcid);

            this.SetSelectExpandClauseCompacted(options);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        private async Task<IActionResult>
            GetAdGroupsFromRepository(
            long customerId,
            long accountId,
            long? campaignId,
            ODataQueryOptions<AdGroup> options,
            ODataActionParameters parameters)
        {
            var pilotingContext = new PilotingContext<ODataQueryOptions<AdGroup>>(
                options,
                this.callContextGetter().AuthorizationContext);

            Validation<GridDataRequestValidationResult> requestValidation = gridDataRequestValidator.Validate(
                pilotingContext, 
                parameters,
                AdCenter.Advertiser.CampaignManagement.MT.Entities.EntityType.AdGroup.ToString());

            if (requestValidation.Errors.OrEmpty().Any())
            {
                return requestValidation.Errors.CreateErrorResponse(Request, logger);
            }

            Validation<NullifyNotApplyBidAmountsFlag> nullifyNotApplyBidAmountsValidation = this.nullifyNotApplyBidAmountsValidator.Validate(Request.RequestQueryString());
            if (nullifyNotApplyBidAmountsValidation.Errors.OrEmpty().Any())
            {
                return nullifyNotApplyBidAmountsValidation.Errors.CreateErrorResponse(Request, logger);
            }
            requestValidation.Result.NullifyNotApplyBidAmounts = nullifyNotApplyBidAmountsValidation.Result.NullifyNotApplyBidAmounts;

            var lcid = options.GetLcidFromHeader();
            var result = await adgroupsRepository.Get(
                customerId,
                accountId,
                campaignId,
                requestValidation.Result,
                lcid);

            this.SetSelectExpandClauseCompacted(options);

            return result.MultipleEntitiesResponse(Request, logger);
        }

        private async Task<IActionResult>
            GetAdGroupsCountFromRepository(
            long customerId,
            long accountId,
            IEnumerable<long> campaignIds)
        {
            var result = await adgroupsRepository.GetCounts(
                customerId,
                accountId,
                campaignIds);

            return result.MultipleEntitiesResponse(Request, logger);
        }


        private void SetSelectExpandClauseCompacted(ODataQueryOptions<AdGroup> options)
        {
            string rawSelect = options.SelectExpand?.RawSelect;
            string rawExpand = options.SelectExpand?.RawExpand;

            if (rawSelect != null || rawExpand != null)
            {
                var parser = new ODataQueryOptionParser(
                    options.Context.Model,
                    options.Context.ElementType,
                    null,
                    new Dictionary<string, string> { { "$select", rawSelect }, { "$expand", rawExpand } });

                Request.ODataProperties().SelectExpandClause = parser.ParseSelectAndExpand().CompactPropertyPaths();
            }
        }
    }
}
