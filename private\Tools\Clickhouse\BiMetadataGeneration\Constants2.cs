﻿using ClickHouse.Client.ADO;
using Microsoft.Data.SqlClient;
using Azure.Core;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public class Constants2
    {
        public static string vDistributionChannel_FlatDim_Alias = "vdc";
        public static string vLanguageLocaleDim_Alias = "ll";

        public static List<string> prc_CampaignExperimentSummary_uiCoulumns = new List<string>()
        {
            "AccountId", "CampaignId", "CustomerId",
        };

        public static List<string> prc_AdExtensionsOrderAssociation_uiCoulumns = new List<string>()
        {
            "AdExtensionGroupId", "CampaignId", "OrderId", "AdExtensionId", "MediumId", "ClickTypeId", "GoalId", "GoalTypeId", "NetworkId", "PagePositionId", "TargetValueId", "DeviceTypeId", "AdExtensionTypeId", "SOSId"
        };

        public static List<string> prc_AdExtensionsByOrderV2_uiCoulumns = new List<string>()
        {
            "AdExtensionId", "CampaignId", "OrderId", "MediumId", "ClickTypeId", "GoalId", "GoalTypeId", "NetworkId", "PagePositionId", "TargetValueId", "DeviceTypeId", "AdExtensionTypeId", "SOSId", "DeviceOSId"
        };

        public static List<string> prc_AssetSummary_uiCoulumns = new List<string>()
        {
            "OrderId", "AdId", "AssetId", "AdAssetAssociationTypeId", "TargetTypeId", "TargetValueId"
        };

        public static List<string> prc_AssetCombinationSummary_uiCoulumns = new List<string>()
        {
            "OrderId", "AdId", "Headline1AssetId", "Headline2AssetId", "Headline3AssetId", "Description1AssetId", "Description2AssetId", "HeadlineAssetId", "LongHeadlineAssetId", "DescriptionAssetId", "ImageId",
            "LogoImageId"
        };

        public static Dictionary<BiDataCategory, List<(string columnName, string defaultValue)>> ColumnsDefaultValue = new Dictionary<BiDataCategory, List<(string columnName, string defaultValue)>>()
        {
            {
                BiDataCategory.CookieCampaignExperimentUsage, new List<(string columnName, string defaultValue)>
                {
                    ("CLICKSUMOFSQUARE", "CAST(0 AS BIGINT)"),
                    ("CLICKTIMESIMPRESSION", "CAST(0 AS BIGINT)"),
                    ("CLICKTIMESCONVERSION", "CAST(0 AS BIGINT)"),
                    ("CONVERSIONSUMOFSQUARE", "CAST(0 AS BIGINT)"),
                    ("POSITIONSUMOFSQUARE", "CAST(0 AS BIGINT)"),
                    ("FullConversionCnt", "CAST(0 AS BIGINT)"),
                    ("FullCLICKTIMESCONVERSION", "CAST(0 AS BIGINT)"),
                    ("TotalConversionCnt", "CAST(0 AS BIGINT)"),
                    ("TotalCONVERSIONSUMOFSQUARE", "CAST(0 AS BIGINT)"),
                    ("ConversionCnt", "CAST(0 AS BIGINT)"),
                    ("ClickCnt", "CAST(0 AS BIGINT)"),
                    ("ImpressionCnt", "CAST(0 AS BIGINT)"),
                    ("TotalPosition", "CAST(0 AS BIGINT)"),
                    ("FullViewConversionCnt", "CAST(0 AS BIGINT)"),
                    ("ConversionEnabledClickCnt", "CAST(0 AS BIGINT)"),
                    ("CLICKTIMESREVENUE", "CAST(0 AS DECIMAL(28,12))"),
                    ("CONVERSIONTIMESREVENUE", "CAST(0 AS DECIMAL(28,12))"),
                    ("CONVERSIONVALUETIMESREVENUE", "CAST(0 AS DECIMAL(28,12))"),
                    ("CONVERSIONVALUESUMOFSQUARE", "CAST(0 AS DECIMAL(28,12))"),
                    ("REVENUESUMOFSQUARE", "CAST(0 AS DECIMAL(28,12))"),
                    ("FullCONVERSIONTIMESREVENUE", "CAST(0 AS DECIMAL(28,12))"),
                    ("FullCONVERSIONSUMOFSQUARE", "CAST(0 AS DECIMAL(28,12))"),
                    ("FullCONVERSIONVALUETIMESREVENUE", "CAST(0 AS DECIMAL(28,12))"),
                    ("FullCONVERSIONVALUESUMOFSQUARE", "CAST(0 AS DECIMAL(28,12))"),
                    ("ConversionEnabledTotalAmount", "CAST(0 AS DECIMAL(28,12))")
                }
            }
        };

        public static Dictionary<ClickhouseQueryType, Dictionary<string, RequestColumn>> ReportRequestColumns = new Dictionary<ClickhouseQueryType, Dictionary<string, RequestColumn>>()
        {
            { ClickhouseQueryType.prc_CampaignQualityScore_ui, new Dictionary<string, RequestColumn>()
                {
                        { "CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null)},
                }
            },
            {
                ClickhouseQueryType.rpt_MajorCitySummary, new Dictionary<string, RequestColumn>
                {
                    {
                        "CampaignType", new RequestColumn
                        {
                            JoinTableAlias = "cam",
                            DependentColumnName = "AdvertisingChannelTypeId",
                            ColumnExpression = "CAST(ifZeroOrNull(cam.AdvertisingChannelTypeId, 1) AS NVARCHAR(20))"
                        }
                    }
                }
            },

            { ClickhouseQueryType.prc_KeywordQualityScore_ui, new Dictionary<string, RequestColumn>()
                {
                        { "OrderItemId", new RequestColumn(null, "<factalias>.OrderItemId", null)},
                        { "BiddedMatchTypeId", new RequestColumn(null, "<factalias>.BiddedMatchTypeId", null)},
                }
            },
            { ClickhouseQueryType.prc_FeedItemAdExtensionSummary_ui, new Dictionary<string, RequestColumn>()
                {
                        { "FeedItemId", new RequestColumn(null, "<factalias>.FeedItemId", null)},
                        { "CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null)},
                        { "OrderId", new RequestColumn(null, "<factalias>.OrderId", null)},
                        { "AdId", new RequestColumn(null, "<factalias>.AdId", null)},
                        { "AdScenarioType", new RequestColumn(null, "<factalias>.AdScenarioType", null)},
                }
            },
            { ClickhouseQueryType.prc_OrderQualityScore_ui, new Dictionary<string, RequestColumn>()
                {
                        { "AccountId", new RequestColumn(null, "<factalias>.AccountId", null)},
                        { "CampaignId", new RequestColumn(null, "<factalias>.CampaignId", null)},
                        { "OrderId", new RequestColumn(null, "<factalias>.OrderId", null)},
                }
            },
            { ClickhouseQueryType.prc_HotelVerticalSummary_ui, new Dictionary<string, RequestColumn>()
                {
                        { "VerticalItemGroupId", new RequestColumn(null, "<factalias>.VerticalItemGroupId", null)},
                        { "VerticalItemId", new RequestColumn(null, "<factalias>.VerticalItemId", null)}
                }
            },
            { ClickhouseQueryType.prc_AdSummary_ui, new Dictionary<string, RequestColumn>()
                {
                        { "FeedItemId", new RequestColumn(null, "<factalias>.FeedItemId", null)},
                        { "AudienceId", new RequestColumn(null, "<factalias>.AudienceId", null)},
                }
            },
             { ClickhouseQueryType.prc_AccountSummary_ui, new Dictionary<string, RequestColumn>()
                {
                        { "AgencyId", new RequestColumn("acc", "acc.AgencyCustomerId", "acc.AgencyCustomerId")},
                        
                }
            },
        };

        public static Dictionary<ClickhouseQueryType, List<string>> DeprecateColumns = new Dictionary<ClickhouseQueryType, List<string>>()
        {
            { ClickhouseQueryType.rpt_AdExtensionItemActivity, new List<string>(){ "DistributionChannelName", "PublisherURL" , "LanguageName", "PricingModelName", "LanguageCode" , "TargetTypeName" } },
            { ClickhouseQueryType.rpt_AdParameterSummary, new List<string>(){ "LanguageLocaleId" } },
            { ClickhouseQueryType.rpt_DestinationURLActivity, new List<string>(){ "PricingModelName" } },
            { ClickhouseQueryType.rpt_ElementAdUsagePerformance, new List<string>(){ "DistributionChannelName" } }, 
            { ClickhouseQueryType.prc_AssetGroupSummary_ui, new List<string>(){ "AdScenarioType", "BiddedMatchTypeId", "DeviceOSId", "DeviceOSID2", "MatchTypeId","PagePositionId", "RelationshipId", "SOSId",} },
            { ClickhouseQueryType.rpt_AssetGroupSummary, new List<string>(){ "AdScenarioType", "BiddedMatchTypeId", "DeviceOSId", "DeviceOSID2",  "MatchTypeId","PagePositionId", "RelationshipId", "SOSId"} },
            { ClickhouseQueryType.rpt_AppSummary, new List<string>(){ "AdScenarioType", "BiddedMatchTypeId", "DeviceOSId", "DeviceOSID2", "MatchTypeId", "PagePositionId", "RelationshipId", "SOSId", "TargetValueId", "TargetTypeId", "DistributionChannelId" } },
            {ClickhouseQueryType.rpt_ElementKeywordUsagePerformance, new List<string>() { "AdExtensionItemId", "DistributionChannelName", "PublisherURL", "PricingModelName", "LanguageName", "LanguageCode", "AdExtensionPropertyValue", "TargetTypeName" } },
            { ClickhouseQueryType.prc_GetMeteredCallDetails, new List<string>(){ "PagePositionId", "RelationshipId", "CallStatus" , "CampaignType", "CallDuration" } },
            { ClickhouseQueryType.rpt_FeedItemSummary, new List<string>(){ "AccountStatusName", "AdScenarioType", "AdExtensionId", "AdExtensionTypeId", "CampaignStatusName", "FeedOrigin", "AudienceId" } },
            { ClickhouseQueryType.rpt_BSCSearchQuerySummary, new List<string>(){ "RelationshipId" } }
        };

        public static List<string> rpt_SearchVerticalCategoryInsightsReport_Coulumns = new List<string>()
        {
            "ICEL1Id", "ICEL2Id", "ICEL3Id", "CountryCode", "AccountId", "StartDate", "EndDate"
        };

        public static List<string> rpt_SearchVerticalCategoryClickShareReport_Coulumns = new List<string>()
        {
            "ICEL1Id", "ICEL2Id", "ICEL3Id", "CountryCode", "StartDate", "EndDate"
        };
    }
}
