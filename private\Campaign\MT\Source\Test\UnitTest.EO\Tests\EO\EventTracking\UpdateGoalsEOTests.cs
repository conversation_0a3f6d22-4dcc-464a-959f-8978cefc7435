﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.UnitTest.Tests.EO.EventTracking
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Common;
    using DAO;
    using Entities;
    using Messages.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Audience;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Dao.SharedLibrary;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Audience;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.SharedLibrary;
    using Microsoft.AdCenter.MsfClientLibrary;
    using Microsoft.AdCenter.Shared.MT;
    using MT.EO.EventTracking;
    using Unity;
    using Rhino.Mocks;
    using VisualStudio.TestTools.UnitTesting;
    using EventTracking = Entities.EventTracking;
    using Microsoft.BingAds.CommonInterfaces;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.UnitTest.TestHarness;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;

    [TestClass]
    public class UpdateGoalsEOTests : EventTrackingEOTests
    {
        private static IClientCenterCaller ccMock;
        private static ISharedLibraryDao sharedLibraryDaoMock;
        private static IOfflineTaskDao offlineTaskDao;

        [TestInitialize]
        public void Setup()
        {
            MsfContext.InitializeContext();
            MsfContext.SecurityTicket = new SecurityTicket();
            MsfContext.SecurityTicket[MsfClientLibrary.Constants.KeyClientCenterProviderTicket] = TestTools.UserSecurityToken;
            ccMock = MockRepository.GenerateStub<IClientCenterCaller>();

            SingleInstanceFactory<IClientCenterCaller, ClientCenterCaller>.Clear();
            InterfaceBindings.AddBinding(typeof(IClientCenterCaller), typeof(IClientCenterCaller), ccMock);

            sharedLibraryDaoMock = MockRepository.GenerateMock<ISharedLibraryDao>();
            Container.RegisterInstance(sharedLibraryDaoMock);

            offlineTaskDao = MockRepository.GenerateMock<IOfflineTaskDao>();
            Container.RegisterInstance(offlineTaskDao);
            DynamicConfigValuesHelper.SetConfigValue<int>("EnableGoalNullableProperties", "true");
            DynamicConfigValuesHelper.SetConfigValue<int>("EnableAutoGoalApiAndValidation", "true");

            var campaignInfoMock = MockRepository.GenerateMock<ICampaignInfoGetter>();
            campaignInfoMock.Expect(mock => mock.GetCampaignDeliverySettingForMSAN(
                Arg<AccountCallContext>.Is.Anything,
                Arg<long>.Is.Anything))
            .Return(false).Repeat.Any();
            Container.RegisterInstance(campaignInfoMock);
        }

        [TestCleanup]
        public void Cleanup()
        {
            InterfaceBindings.RemoveBinding(typeof(IClientCenterCaller));
        }

        [TestMethod]
        public void UpdateGoalsWithSharedLibrary_ValidRequest_Success()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null);
            var otherGoals = GenerateGoals(null);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var othertagIds = otherGoals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var taglist = GenerateValidTags(tagIds.Union(othertagIds).ToArray(), (int)request.AdvertiserCustomerId);
            otherGoals[0].Id = goals[0].Id;

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(taglist, context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(taglist);
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();

            var audiences = GenerateAudiences(taglist, (int)request.AdvertiserCustomerId);
            var providerMock = MockRepository.GenerateMock<IAudienceProvider>();
            var providerMockResult = new Result<Entities.Audience.Audience>();
            providerMockResult.AddEntities(audiences);

            providerMock.Expect(provider => provider.GetAudiencesByCustomerId(
                    Arg<CustomerCallContext>.Is.Anything,
                    Arg<AudienceTypeFilter>.Is.Anything,
                    Arg<DeliveryChannelFilter>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<int?>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(providerMockResult).Repeat.Once();
            Container.RegisterInstance(providerMock);

            var expectedResult = new Dictionary<int, long>();
            for (int index = 0; index < goals.Length; index++)
            {
                expectedResult.Add(index, goals[index].Id.Value);
            }
            var daoMockResult = new BatchResult<long>();
            daoMockResult.AddEntities(expectedResult);
            daoMock.Expect(dao => dao.UpdateUetGoals(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<EventTracking.Goal>>.Matches(g => g.Count == goals.Count()),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<List<long>>.Matches(l => l.Count == taglist.Length),
                Arg<List<long>>.Is.Anything))
                .Return(daoMockResult).Repeat.Once();

            var expectedGetResult = new Dictionary<int, EventTracking.Goal>() { { 0, otherGoals[0] } };
            for (int index = 1; index < goals.Length; index++)
            {
                expectedGetResult.Add(index, goals[index]);
            }
            var getGoalMockResult = new BatchResult<EventTracking.Goal>();
            getGoalMockResult.AddEntities(expectedGetResult);
            daoMock.Expect(dao => dao.GetUetGoalsByIds(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<long>>.Is.Anything,
                Arg<Dictionary<long, TagTrackingStatus>>.Matches(d => d.Count == taglist.Length),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<bool>.Is.Anything))
                .Return(getGoalMockResult).Repeat.Once();

            Container.RegisterInstance(daoMock);

            offlineTaskDao.Expect(
                e =>
                e.AddUETTagUsedByRecountTask(
                    Arg<BasicCallContext>.Matches(c => !c.AccountId.HasValue),
                    Arg<List<long>>.Is.NotNull,
                    Arg<int>.Is.Same(SpWrappers.RecountUsedbyAccountForUETTagAssociateToGoalOperation),
                    Arg<bool>.Is.Anything))
                .Repeat
                .Once();

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoalsWithSharedLibrary_ValidRequest_AccountLevel_Success()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null, isAccount: true);
            var otherGoals = GenerateGoals(null, isAccount: true);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var othertagIds = otherGoals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var taglist = GenerateValidTags(tagIds.Union(othertagIds).ToArray(), (int)request.AdvertiserCustomerId);
            otherGoals[0].Id = goals[0].Id;

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(taglist, context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();
            sharedLibraryDaoMock.Expect(dao => dao.UpdateUsedBy(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<List<UsedByInfo>>.Is.Anything,
                    Arg<bool>.Is.Equal(true)))
                .Return(new BatchResult<long>()).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(taglist);
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();

            var audiences = GenerateAudiences(taglist, (int)request.AdvertiserCustomerId);
            var providerMock = MockRepository.GenerateMock<IAudienceProvider>();
            var providerMockResult = new Result<Entities.Audience.Audience>();
            providerMockResult.AddEntities(audiences);

            providerMock.Expect(provider => provider.GetAudiencesByCustomerId(
                    Arg<CustomerCallContext>.Is.Anything,
                    Arg<AudienceTypeFilter>.Is.Anything,
                    Arg<DeliveryChannelFilter>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<int?>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(providerMockResult).Repeat.Once();
            Container.RegisterInstance(providerMock);

            var expectedResult = new Dictionary<int, long>();
            for (int index = 0; index < goals.Length; index++)
            {
                expectedResult.Add(index, goals[index].Id.Value);
            }
            var daoMockResult = new BatchResult<long>();
            daoMockResult.AddEntities(expectedResult);
            daoMock.Expect(dao => dao.UpdateUetGoals(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<EventTracking.Goal>>.Matches(g => g.Count == goals.Count()),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<List<long>>.Matches(l => l.Count == taglist.Length),
                Arg<List<long>>.Is.Anything))
                .Return(daoMockResult).Repeat.Once();

            var expectedGetResult = new Dictionary<int, EventTracking.Goal>() { { 0, otherGoals[0] } };
            for (int index = 1; index < goals.Length; index++)
            {
                expectedGetResult.Add(index, goals[index]);
            }
            var getGoalMockResult = new BatchResult<EventTracking.Goal>();
            getGoalMockResult.AddEntities(expectedGetResult);
            daoMock.Expect(dao => dao.GetUetGoalsByIds(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<long>>.Is.Anything,
                Arg<Dictionary<long, TagTrackingStatus>>.Matches(d => d.Count == taglist.Length),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<bool>.Is.Anything))
                .Return(getGoalMockResult).Repeat.Once();

            Container.RegisterInstance(daoMock);

            offlineTaskDao.Expect(
                e =>
                e.AddUETTagUsedByRecountTask(
                    Arg<BasicCallContext>.Matches(c => c.AccountId.Value == AccountId),
                    Arg<List<long>>.Is.NotNull,
                    Arg<int>.Is.Same(SpWrappers.RecountUsedbyAccountForUETTagAssociateToGoalOperation),
                    Arg<bool>.Is.Anything))
                .Repeat
                .Once();

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoalsWithSharedLibrary_ValidRequest_WithoutSharedTag_Success()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null);
            var otherGoals = GenerateGoals(null);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var othertagIds = otherGoals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var taglist = GenerateValidTags(tagIds.Union(othertagIds).ToArray(), (int)request.AdvertiserCustomerId);
            otherGoals[0].Id = goals[0].Id;

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(Array.Empty<Tag>(), context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(taglist);
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();

            var audiences = GenerateAudiences(taglist, (int)request.AdvertiserCustomerId);
            var providerMock = MockRepository.GenerateMock<IAudienceProvider>();
            var providerMockResult = new Result<Entities.Audience.Audience>();
            providerMockResult.AddEntities(audiences);

            providerMock.Expect(provider => provider.GetAudiencesByCustomerId(
                    Arg<CustomerCallContext>.Is.Anything,
                    Arg<AudienceTypeFilter>.Is.Anything,
                    Arg<DeliveryChannelFilter>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<int?>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(providerMockResult).Repeat.Once();
            Container.RegisterInstance(providerMock);

            var expectedResult = new Dictionary<int, long>();
            for (int index = 0; index < goals.Length; index++)
            {
                expectedResult.Add(index, goals[index].Id.Value);
            }
            var daoMockResult = new BatchResult<long>();
            daoMockResult.AddEntities(expectedResult);
            daoMock.Expect(dao => dao.UpdateUetGoals(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<EventTracking.Goal>>.Matches(g => g.Count == goals.Count()),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<List<long>>.Matches(l => l.Count == 0),
                Arg<List<long>>.Is.Anything))
                .Return(daoMockResult).Repeat.Once();

            var expectedGetResult = new Dictionary<int, EventTracking.Goal>() { { 0, otherGoals[0] } };
            for (int index = 1; index < goals.Length; index++)
            {
                expectedGetResult.Add(index, goals[index]);
            }
            var getGoalMockResult = new BatchResult<EventTracking.Goal>();
            getGoalMockResult.AddEntities(expectedGetResult);
            daoMock.Expect(dao => dao.GetUetGoalsByIds(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<long>>.Is.Anything,
                Arg<Dictionary<long, TagTrackingStatus>>.Matches(d => d.Count == 0),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<bool>.Is.Anything))
                .Return(getGoalMockResult).Repeat.Once();

            Container.RegisterInstance(daoMock);

            offlineTaskDao.Expect(
                e =>
                e.AddUETTagUsedByRecountTask(
                    Arg<BasicCallContext>.Matches(c => !c.AccountId.HasValue),
                    Arg<List<long>>.Is.NotNull,
                    Arg<int>.Is.Same(SpWrappers.RecountUsedbyAccountForUETTagAssociateToGoalOperation),
                    Arg<bool>.Is.Anything))
                .Repeat
                .Once();

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoals_Validation_And_DB_BatchErrors()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null);
            goals[3].Name = string.Empty;
            var otherGoals = GenerateGoals(null);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var othertagIds = otherGoals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var taglist = GenerateValidTags(tagIds.Union(othertagIds).ToArray(), (int)request.AdvertiserCustomerId);
            otherGoals[0].Id = goals[0].Id;

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(Array.Empty<Tag>(), context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var expectedResult = new Dictionary<int, long> { { 1, (long)goals[1].Id }, { 2, (long)goals[2].Id } };
            var daoMockResult = new BatchResult<long>();
            daoMockResult.AddEntities(expectedResult);
            daoMockResult.BatchErrors.Add(
                0,
                new List<CampaignManagementErrorDetail>
                {
                    new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalId, "error message")
                });

            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(taglist);
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();

            daoMock.Expect(dao => dao.UpdateUetGoals(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<Entities.EventTracking.Goal>>.Matches(t => t.Count == goals.Count() - 1),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<List<long>>.Is.Anything,
                Arg<List<long>>.Is.Anything))
                .Return(daoMockResult).Repeat.Once();

            var expectedGetResult = new Dictionary<int, EventTracking.Goal>();
            for (int index = 0; index < goals.Length; index++)
            {
                expectedGetResult.Add(index, goals[index]);
            }

            var getGoalMockResult = new BatchResult<Entities.EventTracking.Goal>();
            getGoalMockResult.AddEntities(expectedGetResult);

            daoMock.Expect(dao => dao.GetUetGoalsByIds(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<long>>.Is.Anything,
                Arg<Dictionary<long, TagTrackingStatus>>.Is.Anything,
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<bool>.Is.Anything))
                .Return(getGoalMockResult).Repeat.Once();

            Container.RegisterInstance(daoMock);

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(2, result.BatchErrors.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoals_Validation_And_DB_Error()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null);
            goals[3].Name = string.Empty;
            var otherGoals = GenerateGoals(null);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerId,
                CustomerAccountId = AccountId
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var othertagIds = otherGoals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    && g.Type != GoalEntityType.OfflineConversionGoal && g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();
            var taglist = GenerateValidTags(tagIds.Union(othertagIds).ToArray(), (int)request.AdvertiserCustomerId);
            otherGoals[0].Id = goals[0].Id;

            var context = new CustomerCallContext(Logger, request, CustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(Array.Empty<Tag>(), context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var expectedResult = new Dictionary<int, long> { { 1, (long)goals[1].Id }, { 2, (long)goals[2].Id } };
            var daoMockResult = new BatchResult<long>();
            daoMockResult.AddEntities(expectedResult);

            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(taglist);
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();

            daoMockResult.Errors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidCustomerId, "error message"));

            daoMock.Expect(dao => dao.UpdateUetGoals(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<Entities.EventTracking.Goal>>.Matches(t => t.Count == goals.Count() - 1),
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<List<long>>.Is.Anything,
                Arg<List<long>>.Is.Anything))
                .Return(daoMockResult).Repeat.Once();

            var expectedGetResult = new Dictionary<int, EventTracking.Goal>();
            for (int index = 0; index < goals.Length; index++)
            {
                expectedGetResult.Add(index, goals[index]);
            }

            var getGoalMockResult = new BatchResult<Entities.EventTracking.Goal>();
            getGoalMockResult.AddEntities(expectedGetResult);

            daoMock.Expect(dao => dao.GetUetGoalsByIds(
                Arg<CustomerCallContext>.Matches(c => c == context),
                Arg<LineItemContainer<long>>.Is.Anything,
                Arg<Dictionary<long, TagTrackingStatus>>.Is.Anything,
                Arg<IDictionary<int, bool>>.Is.Anything,
                Arg<bool>.Is.Anything))
                .Return(getGoalMockResult).Repeat.Once();

            Container.RegisterInstance(daoMock);

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(1, result.BatchErrors.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoalsWithSharedLibrary_DB_Error()
        {
            EnableSharedLibrary();
            var goals = GenerateGoals(null);
            var request = new UpdateGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId
            };
            var tagIds = new long[1] { goals[0].TagId ?? 0 };
            var taglist = GenerateValidTags(tagIds, (int)request.AdvertiserCustomerId);

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);
            var basicCallContext = BasicCallContext.Create(context);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(taglist, context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();
            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Once();

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.Errors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidCustomerId, "error message"));
            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Once();
            Container.RegisterInstance(daoMock);

            var audiences = GenerateAudiences(taglist, (int)request.AdvertiserCustomerId);
            var providerMock = MockRepository.GenerateMock<IAudienceProvider>();
            var providerMockResult = new Result<Entities.Audience.Audience>();
            providerMockResult.AddEntities(audiences);

            providerMock.Expect(provider => provider.GetAudiencesByCustomerId(
                    Arg<CustomerCallContext>.Is.Anything,
                    Arg<AudienceTypeFilter>.Is.Anything,
                    Arg<DeliveryChannelFilter>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<int?>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(providerMockResult).Repeat.Once();
            Container.RegisterInstance(providerMock);

            var result = Container.Resolve<EventTrackingEO>().UpdateGoals(context, request.Goals);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(0, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_DestinationGoal_To_DestinationGoal_Sucess()
        {
            var existingGoal = new DestinationGoal
            {
                TagId = 2,
                Id = 123,
                IsAccountLevel = true,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.DestinationGoal,
                Name = "Destination goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                UrlString = "goal url string",
                Operator = EventTracking.ExpressionOperator.EqualsTo,
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new DestinationGoal();
            goal.Id = 123;
            goal.Operator = EventTracking.ExpressionOperator.NoExpression;
            goal.Type = GoalEntityType.DestinationGoal;
            
            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic});

            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_DurationGoal_To_DurationGoal_Sucess()
        {
            var existingGoal = new DurationGoal
            {
                TagId = 2,
                Id = 123,
                IsAccountLevel = true,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.DurationGoal,
                Name = "Goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                Hours = 1,
                Minutes = 2,
                Seconds = 3,
                Operator = ValueOperator.EqualTo,
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new DurationGoal();
            goal.Id = 123;
            goal.Operator = ValueOperator.NoValue;
            goal.Type = GoalEntityType.DurationGoal;

            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic });


            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_PageViewsPerVisitGoal_To_PageViewsPerVisitGoal_Sucess()
        {
            var existingGoal = new PageViewsPerVisitGoal
            {
                TagId = 2,
                Id = 123,
                IsAccountLevel = true,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.PageViewsPerVisitGoal,
                Name = "Goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                PageViews = 2,
                Operator = ValueOperator.EqualTo,
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new PageViewsPerVisitGoal();
            goal.Id = 123;
            goal.Operator = ValueOperator.NoValue;
            goal.Type = GoalEntityType.PageViewsPerVisitGoal;

            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic });

            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_EventGoal_To_EventGoal_Sucess()
        {
            var existingGoal = new EventGoal
            {
                TagId = 2,
                Id = 123,
                IsAccountLevel = true,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.EventGoal,
                Name = "Goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                Category = "category",
                CategoryOperator = EventTracking.ExpressionOperator.BeginsWith,
                Label = "label",
                LabelOperator = EventTracking.ExpressionOperator.Contains,
                Action = "action",
                ActionOperator = EventTracking.ExpressionOperator.RegularExpression,
                Value = 4.3m,
                ValueOperator = ValueOperator.LessThan,
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new EventGoal();
            goal.Id = 123;
            goal.ValueOperator = ValueOperator.NoValue;
            goal.ActionOperator = EventTracking.ExpressionOperator.NoExpression;
            goal.CategoryOperator = EventTracking.ExpressionOperator.NoExpression;
            goal.LabelOperator = EventTracking.ExpressionOperator.NoExpression;
            goal.Type = GoalEntityType.EventGoal;

            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic });

            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_AppGoal_To_AppGoal_Sucess()
        {
            var existingGoal = new ApplicationInstallGoal
            {
                Id = 123,
                IsAccountLevel = false,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.ApplicationInstallGoal,
                Name = "Goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                ApplicationPlatform = "Windows",
                ApplicationStoreId = "abcd",
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new ApplicationInstallGoal();
            goal.Id = 123;
            goal.Type = GoalEntityType.ApplicationInstallGoal;

            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic });

            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void BackfillGoalsForUpdateGoals_OfflineConversionGoal_To_OfflineConversionGoal_Sucess()
        {
            var existingGoal = new OfflineConversionGoal()
            {
                TagId = 2,
                Id = 123,
                IsAccountLevel = true,
                ConversionCountType = ConversionCountType.All,
                Status = GoalStatus.Active,
                Type = GoalEntityType.OfflineConversionGoal,
                Name = "Goal name",
                LookbackWindowDays = 1,
                LookbackWindowHours = 2,
                LookbackWindowMinutes = 3,
                Revenue = new GoalRevenue()
                {
                    Type = GoalValueType.FixedValue,
                    Value = (decimal)1.02,
                },
                GoalTrackingStatus = GoalTrackingStatus.Unverified,
                ViewThroughLookbackWindowinDays = 0,
                ViewThroughLookbackWindowinHours = 0,
                ViewThroughLookbackWindowinMinutes = 0
            };
            var goal = new OfflineConversionGoal();
            goal.Id = 123;
            goal.Type = GoalEntityType.OfflineConversionGoal;

            var daoMock = RegisterDaoMock();
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { goal };
            var dic = new Dictionary<int, EventTracking.Goal>();
            var pilotDic = new Dictionary<int, bool>();
            dic.Add(0, existingGoal);
            RunMethod(eo.GetType(), "BackfillGoalsForUpdateGoals", eo, new object[] { new CustomerCallContext(Logger, new UpdateGoalsRequest(), CustomerId), goals, dic, new BatchResult<long>(), pilotDic });

            AreEqual(existingGoal, goal);

            daoMock.VerifyAllExpectations();
        }

        private IEventTrackingDao RegisterDaoMock(bool IsCustomerEnabledForViewThroughConversion = false, bool isCustomerEnabledForExternalAttribution = true)
        {
            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            IDictionary<int, bool> PilotOfCustomerDict = new Dictionary<int, bool>()
            {
                [(int)CustomerFeatureFlag.ViewThroughConversion] = IsCustomerEnabledForViewThroughConversion,
            };
            daoMock.Expect(dao => dao.UpdateUetGoals(null, null, PilotOfCustomerDict)).Repeat.Never();
            daoMock.Expect(dao => dao.GetUetGoalsByIds(null, null, null, PilotOfCustomerDict, false)).Repeat.Never();
            
            Container.RegisterInstance(daoMock);

            return daoMock;
        }

        private void AreEqual(EventTracking.Goal existingGoal, EventTracking.Goal goal)
        {
            Assert.AreEqual(existingGoal.Id, goal.Id);
            Assert.AreEqual(existingGoal.Name, goal.Name);
            Assert.AreEqual(existingGoal.TagId, goal.TagId);
            Assert.AreEqual(existingGoal.Status, goal.Status);
            Assert.AreEqual(existingGoal.Type, goal.Type);
            Assert.AreEqual(existingGoal.IsAccountLevel, goal.IsAccountLevel);
            Assert.AreEqual(existingGoal.ConversionCountType, goal.ConversionCountType);
            Assert.AreEqual(existingGoal.Revenue.Value, goal.Revenue.Value);
            Assert.AreEqual(existingGoal.Revenue.Type, goal.Revenue.Type);
            Assert.AreEqual(existingGoal.LookbackWindowHours, goal.LookbackWindowHours);
            Assert.AreEqual(existingGoal.LookbackWindowDays, goal.LookbackWindowDays);
            Assert.AreEqual(existingGoal.LookbackWindowMinutes, goal.LookbackWindowMinutes);
            if (existingGoal.Type == GoalEntityType.DestinationGoal)
            {
                var existingDestinationGoal = existingGoal as DestinationGoal;
                var desitnationGoal = goal as DestinationGoal;
                Assert.AreEqual(existingDestinationGoal.UrlString, desitnationGoal.UrlString);
                Assert.AreEqual(existingDestinationGoal.Operator, desitnationGoal.Operator);
            }
            else if (existingGoal.Type == GoalEntityType.DurationGoal)
            {
                var existingDurationGoal = existingGoal as DurationGoal;
                var durationGoal = goal as DurationGoal;
                Assert.AreEqual(existingDurationGoal.Operator, durationGoal.Operator);
                Assert.AreEqual(existingDurationGoal.Hours, durationGoal.Hours);
                Assert.AreEqual(existingDurationGoal.Minutes, durationGoal.Minutes);
                Assert.AreEqual(existingDurationGoal.Seconds, durationGoal.Seconds);
            }
            else if (existingGoal.Type == GoalEntityType.PageViewsPerVisitGoal)
            {
                var existingPageGoal = existingGoal as PageViewsPerVisitGoal;
                var pageGoal = goal as PageViewsPerVisitGoal;
                Assert.AreEqual(existingPageGoal.PageViews, pageGoal.PageViews);
                Assert.AreEqual(existingPageGoal.Operator, pageGoal.Operator);
            }
            else if (existingGoal.Type == GoalEntityType.EventGoal)
            {
                var existingEventGoal = existingGoal as EventGoal;
                var eventGoal = goal as EventGoal;
                Assert.AreEqual(existingEventGoal.Value, eventGoal.Value);
                Assert.AreEqual(existingEventGoal.ValueOperator, eventGoal.ValueOperator);
                Assert.AreEqual(existingEventGoal.Category, eventGoal.Category);
                Assert.AreEqual(existingEventGoal.CategoryOperator, eventGoal.CategoryOperator);
                Assert.AreEqual(existingEventGoal.Label, eventGoal.Label);
                Assert.AreEqual(existingEventGoal.LabelOperator, eventGoal.LabelOperator);
                Assert.AreEqual(existingEventGoal.Action, eventGoal.Action);
                Assert.AreEqual(existingEventGoal.ActionOperator, eventGoal.ActionOperator);
            }
            else if (existingGoal.Type == GoalEntityType.ApplicationInstallGoal)
            {
                var existingAppGoal = existingGoal as ApplicationInstallGoal;
                var appGoal = goal as ApplicationInstallGoal;
                Assert.AreEqual(existingAppGoal.ApplicationPlatform, appGoal.ApplicationPlatform);
                Assert.AreEqual(existingAppGoal.ApplicationStoreId, appGoal.ApplicationStoreId);
            }
        }
    }
}
