namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter
{
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.Piloting;
    using System;
    using System.Collections;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Threading;
    using PilotCC = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade.Pilot;

    //We are currently changing pilot flags from a long to a BitArray. If adding new pilot flags while this comment
    //is still here, please add the flags to both flag enums and add the mapping in the dictionary in the Pilot class
    public enum AccountFeatureFlag : int
    {
        NoFeature = 0,
        DynamicFeedCampaign = 766,
        CampaignMonthlyBudgetSimplication = 767,
        DSAMixedModeCampaign = 791,
        HouseholdIncomeTargeting = 801,
        AdsGlobalizationPhase1 = 794,
        ExcludeAccMulImgExtension = 828,
        AudienceAssociationBidRecommendation = 834,
        SharedRemarketingAudienceGoogleImport = 856,
        PortfolioBidStrategy = 823,
        VideoAds = 840,
        ManualCpm = 858,
        CampaignDailyCapNewPipeline = 862,
        HotelPayPerStay = 875,
        PersonalizedOffers = 880,
        AdsGlobalizationPhase2 = 881,
        FeedItemInNewShard = 894,
        DSASubdomain = 896,
        VideoAdEditor = 904,
        AllowPNGImages = 908,
        AllowCrawlImagesFromLandingPage = 913,
        DynamicDescription = 939,
        HotelPayPerStayPhase2 = 944,
        PersonalizedOffersMVP = 949,
        AutoBiddingForAudienceNetwork = 952,
        EnhancedProductAdsFilterPhase1 = 953,
        EnhancedProductAdsFilterPhase2 = 1097,
        VideoAdsEditorCropClip = 967,
        VideoAdsEditorCustomThumbnail = 968,
        VideoAdsEditorCaption = 969,
        AutoBiddingForAudienceNetworkPhase2 = 971,
        HotelCampaign = 973,
        PersonalizedOffersV3 = 979,
        CategoryandIdfilterforaudiencecampaigin = 990,
        CostPerSale = 991,
        ShoppingSettingForMMA = 994,
        ImportGoogleImageAdExtension = 1016,
        UrlEqualsInDSATarget = 1025,
        GoogleImportVideoAds = 1028,
        ProjectEndor = 1041,
        AdsGlobalizationPhase2Japanese = 1043,
        AudienceCampaigneCPCAutoBidding = 1053,
        AdsGlobalizationPhase3America = 1046,
        AdsGlobalizationPhase3APAC = 1047,
        MultiMediaAdsInDSAAdGroup = 1060,
        SmartCampaignImport = 1066,
        IncreasedAdLevelImpressionTrackingUrlLimit = 1077,
        AdsGlobalizationPhase5Africa = 1062,
        AdsGlobalizationPhase6MENA = 1063,
        MediaLibrary = 1094,
        LeadGen = 1104,
        Boost = 1105,
        BoostConversionBasedSegment = 1431,
        MMAAdCustomizer = 1132,
        IsEnabledForManagerAccountSharedWebsiteExclusions = 697,
        EditorialCategoryload = 1133,
        AdsGlobalizationSimplifiedChinese = 1134,
        PerformanceMaxCampaign = 1147,
        Click2CallMigration = 1148,
        ShoppableAds = 1150,
        AdsGlobalizationPhase7 = 1154,
        DayTimeTargetBidBoost = 1172,
        AssetLibraryV2MVP = 1174,
        CNMarketExpansion = 1183,
        RSAAutoGeneratedAssets = 1187,
        AssetLibraryV3 = 1188,
        XandrVideoAds = 1195,
        ESCAudienceAdsSupport = 1201,
        LogoAdExtension = 1215,
        SearchCampaignPredictiveTargeting = 1216,
        DeprecateManualCpcForAudienceCampaign = 1221,
        MMAV2 = 1225,
        AdsGlobalizationPhase8 = 1226,
        MutableKeywordMatchType = 1227,
        ResponsiveAdAssetPerformance = 1228,
        AssetLibraryV4 = 1234,
        IASForBrandAwarenessCampaign = 1241,
        IASForNativeAdCPM = 1242,
        MMAV2ConvertExclusion = 1248,
        CampaignAutomatedCallToActionOptOut = 1251,
        DealForMsAds = 1252,
        DisplayAds = 1258,
        PMaxLite = 1268,
        MutableDisclaimers = 1269,
        NegativeAgeTargetForAdGroups = 1274,
        AssetLibraryAIGC = 1276,
        MSANVideoAdsAudienceTarget = 1277,
        AdStrength = 1284,
        EnableMCVForSearchCampaigns = 1285,
        GenreTargets = 1286,
        AccountNegativeKeywordList = 1289,
        TransparentImageAnnotation = 1292,
        GoogleDiscoveryCampaignImport = 1299,
        AdsGlobalizationPhase9 = 1301,
        AdsGlobalizationPhase9VI = 1302,
        PerformanceMaxMaxClicks = 1314,
        LocationTargetingImprovement = 1317,
        IASForNativeAdNonCPM = 1318,
        KeywordTargeting = 1323,
        BaiduFileImport = 1324,
        BroadOnlyCampaign = 1325,
        PerformanceMaxAutomatedCTA = 1327,
        EnableMCVForStandardShoppingCampaigns = 1332,
        ImageAdExtensionSmartCrop = 1337,
        AudienceAdSmartCrop = 1336,
        MSANServingForDisplayCampaigns = 1344,
        MSANServingForVideoCampaigns = 1345,
        MAEConversionGoals = 1347,
        AssetBasedEditorialRSA = 1350,
        SSCDeprecation = 1352,
        MSANLogoAndCTAForMMA = 1353,
        EnableMCVForPortfolioBidStrategy = 1354,
        PerformanceMaxCampaignLevelAutoGenAssetsControl = 1355,
        PerformanceMaxPhaseZeroWhitelist = 1358,
        EnableDataExclusionForAudienceCampaignType = 1359,
        MSANServingForCTVCampaigns = 1360,
        Enable1PPlacementTargeting = 1361,
        PerformanceMaxBrandExclusion = 1363,
        GoalImportPhase1 = 1365,
        MOATCPM = 1366,
        DSAinAssetGrids = 1367,
        MMAinAssetGrids = 1368,
        WindowsAppCampaign = 1373,
        EnabledForVideoAsAsset = 1380,
        MSANDisclaimer = 1383,
        SyndicationOnlyDeprecation = 1397,
        PerformanceMaxCampaignLevelCostPerSaleOptOut = 1399,
        SyndicationOnlyWhitelist = 1400,
        MultiFormatAds = 1401,
        VideoLibraryTranscoding = 1412,
        OfflineConversionReport = 1415,
        BrandSafety = 1417,
        PerformanceMaxCampaignAssetGroupSearchTheme = 1424,
        LinkedInCampaign = 1425,
        SearchCampaignPredictiveMatching = 1426,
        MSANLiftExperiment = 1421,
        PlacementTargeting = 1433,
        AssetResharding = 1442,
        PerformanceMaxCampaignAssetGroupUrlTarget = 1440,
        MSANISPOT = 1439,
        DoubleVerifyActivation = 1446,
        CallToActionOptOut = 1459,
        PmaxNewCustomerAcquisition = 1460,
        PerformanceMaxCampaignInChina = 1461,
        AppCampaignInChina = 1465,
        BrandSafetyWithUnscored = 1468,
        ModeledConversionUpliftReport = 1457,
        AudienceCampaignExpandedHeadlineCharLimits = 1478,
        UnifiedAppCampaign = 1479,
        CallToActionDefaultOptOut = 1480,
        ImageFitting = 1487,
        ImageFittingExcludeForMAE = 1488,
        AdsStudioFeatures = 1490,
        PremiumStreamingOTTPhase1 = 1494,
        PerformanceMaxCampaignAudienceInsight = 1495,
        BrandKit = 1505,
        PMaxDsaUrlValidation = 1503,
        ImpressionTrackingForMultiFormatAds = 1509,
        AppCampaignPlacementTargeting = 1515,
        EnablePmaxAssetInAssetGrid = 1516,
        ROIUnify = 1517,
        PMaxVideoAsAsset = 1521,
        AssetBasedEditorialAIM = 1522,
        AccountPlacementExclusionList = 1525,
        AccountPlacementInclusionList = 1526,
        SubPlacementTargeting = 1528,
        EnableLinkedInTargetBulkUpload = 1534,
        LifetimeBudget = 1535,
        EnableLifetimeBudgetForImport = 1536,
        AudienceShoppingCampaignFeedLabel = 1538,
        VideoRecommendationForAudienceCampaign = 1542,
        PMaxV2AspectRatios = 1543,
        PMaxV2AspectRatiosExclusions = 1544,
        MAELifetimeBudgetExclusion = 1545,
        VerticalVideoSupport = 1549,
        AssetSharding = 1550,
        VideoAdsGeneration = 1551,
        GoogleImageAdImportWithAssetIdMissing = 1554,
        Diagnostics = 1555,
        MTAssetBasedEditorialRSA = 1570,
        MTAssetBasedEditorialAIM = 1571,
        AdsGlobalizationPAKorea = 1537,
        PerformanceMaxSelect = 1573,
        BrandKitPhase2 = 1575,
        RecommendationAPIV2 = 1581,
        TopicTargeting = 1582,
        PmaxDisclaimer = 1561,
        SmartCampaignToPMaxMigration = 1565,
        BrandKitBulk = 1594,
        WindowsAppCampaignStartExperience = 1595,
        WindowsAppCampaignLegacyCreationFlowDeprecation = 1596,
        HTML5Asset = 1597,
        PMaxEnhanceListings = 1604,
        EnableAIGCForAADUser = 1608,
        EnableNoneLanguageCallToAction = 1620,
        PMaxAssetGroupSegmentation = 1624,
        ConversionDelayMetrics = 1629,
        MobileAppCampaignConversionGoal = 1631
    }

    //This is a wrapper for BitArray such that OnGetLockTimeout is implementable with both PilotCC flags and account feature flags
    public class AccountFeatures
    {
        public BitArray Flags { get; set; }

        public AccountFeatures(params AccountFeatureFlag[] initialFlags)
        {
            Flags = new BitArray(PilotAccount.accountFeatureFlags.Length, false);
            foreach (AccountFeatureFlag flag in initialFlags)
            {
                Flags[PilotAccount.ccToMTPilotFlagMapping[flag]] = true;
            }
        }

        public void AddFlags(params AccountFeatureFlag[] flags)
        {
            foreach (AccountFeatureFlag flag in flags)
            {
                Flags[PilotAccount.ccToMTPilotFlagMapping[flag]] = true;
            }
        }
    }

    public class CachedPilotAccountFlags
    {
        public CachedPilotAccountFlags(long accountId, BitArray flags)
        {
            this.AccountId = accountId;
            this.Flags = flags;
        }

        public long AccountId { get; set; }

        public BitArray Flags { get; set; }
    }


    public class PilotAccount : ICacheDictionaryBehavior<long, AccountFeatures, CampaignManagementRequest>, ICacheDictionaryBehavior<long, BitArray, CampaignManagementRequest>
    {
        private static readonly PilotAccount instance = new PilotAccount();

        internal CacheDictionary<long, AccountFeatures> accountFeatures = new CacheDictionary<long, AccountFeatures>();

        private readonly CacheDictionary<int, bool> bypassPilotChecksUsersCache = new CacheDictionary<int, bool>();

        internal static AccountFeatureFlag[] accountFeatureFlags = CreateAccountFeatureFlagsEnumArray();

        internal static Dictionary<AccountFeatureFlag, int> ccToMTPilotFlagMapping = CreateCCToMTPilotFlagMapping();

        private static long accountFeaturesCacheHits;

        private static long accountFeaturesCacheMisses;

        private static int accountFeaturesCacheLogCounter;

        //stored as longs because Interlocked.Exchange() cannot be used with DateTime
        private static long timeSinceLastAccountFeaturesCacheLog;

        private readonly long accountFeaturesCacheLogSpan = new TimeSpan(0, 5, 0).Ticks;



        private Thread refreshThread;

        //Dictionary used to track which pilot flags are used by admin accounts and therefore returned true regardless of whether they're turned on
        private readonly ConcurrentDictionary<int, byte> adminPilotFlags = new ConcurrentDictionary<int, byte>();

        /// <summary>
        /// Expose a way to used pilot checks against cached pilot flags
        /// </summary>
        /// <param name="cachedPilotFlags">the cached pilot flags</param>
        public PilotAccount(CachedPilotAccountFlags cachedPilotAccountFlags)
        {
            var flags = this.GetAccountFeature(cachedPilotAccountFlags.Flags);
            this.accountFeatures.Add(cachedPilotAccountFlags.AccountId, flags);
        }

        private PilotAccount()
        {
        }

        private static AccountFeatureFlag[] CreateAccountFeatureFlagsEnumArray()
        {
            return (AccountFeatureFlag[])Enum.GetValues(typeof(AccountFeatureFlag));
        }

        private static Dictionary<AccountFeatureFlag, int> CreateCCToMTPilotFlagMapping()
        {
            Dictionary<AccountFeatureFlag, int> mapping = new Dictionary<AccountFeatureFlag, int>(accountFeatureFlags.Length);
            foreach (AccountFeatureFlag flag in accountFeatureFlags)
            {
                mapping[flag] = Array.IndexOf(accountFeatureFlags, flag);
            }
            return mapping;
        }

        public static int CacheRefreshIntervalInMs { get; private set; }

        public static PilotAccount Instance
        {
            get { return instance; }
        }

        /// <summary>
        /// Gets or sets CheckForCachedPilotFlags. A delegate that checks for pre-cached pilot flags. e.g. BulkApi doesn't have cc token
        /// but prefetches flags on task submit time
        /// </summary>
        public Func<int, BitArray> GetCachedPilotFlagsByAccountId { get; set; }

        public void Initialize(int cacheRefreshIntervalInMs)
        {
            CacheRefreshIntervalInMs = cacheRefreshIntervalInMs;

            //remove after piloting done
            this.accountFeatures.Clear();
            this.bypassPilotChecksUsersCache.Clear();

            if (this.refreshThread == null)
            {
                this.refreshThread = new Thread(RefreshData)
                {
                    IsBackground = true
                };
                this.refreshThread.Start();
            }

            accountFeaturesCacheHits = 0;
            accountFeaturesCacheMisses = 0;
            timeSinceLastAccountFeaturesCacheLog = DateTime.UtcNow.Ticks;
            accountFeaturesCacheLogCounter = 0;
        }

        public bool InValidateAccountCache(ILogShared logger, CampaignManagementRequest request)
        {
            return this.accountFeatures.Remove(request.CustomerAccountId.Value);
        }

        /// <summary>
        /// Get raw information about pilot flags from CC
        /// </summary>
        public BitArray GetAccountFeaturesBitArray(ILogShared logger, long accountId, CampaignManagementRequest request)
        {
            return PilotCC.GetAccountFeature(logger, accountId, request.SecurityTicket.SecurityTicketId, request.Tracking.TrackingId.ToString());
        }

        public bool OnGetLockTimeout(ILogShared logger, long accountId, CampaignManagementRequest request, out AccountFeatures features)
        {
            features = this.GetAccountFeature(logger, accountId, request);
            return true;
        }

        //For PilotCC mapping
        public bool OnGetLockTimeout(ILogShared logger, long accountId, CampaignManagementRequest request, out BitArray features)
        {
            features = this.GetAccountFeaturesBitArray(logger, accountId, request);
            return true;
        }

        public bool IsFeatureEnabled(ILogShared logger, CampaignManagementRequest request, uint featurePilotId)
        {
            long accountId = EOHelper.GetCustomerAccountId(logger, request);
            return IsFeatureEnabled(logger, request, accountId, featurePilotId);
        }

        private bool IsFeatureEnabled(ILogShared logger, CampaignManagementRequest request, long accountId, uint featurePilotId)
        {
            if (ccToMTPilotFlagMapping.ContainsKey((AccountFeatureFlag)featurePilotId))
            {
                return CheckFeature(logger, request, accountId, (AccountFeatureFlag)featurePilotId);
            }

            throw new InvalidOperationException($"Pilot feature {featurePilotId} not found in middle tier feature flags");
        }



        public bool IsFeatureEnabled(
            ILogShared logger,
            CampaignManagementRequest request,
            AccountFeatureFlag requestedFeature,
            bool enableIfUserAdmin = true)
        {
            // NOTE: should not consider admin user enabled, if checking to deprecate some feature (if new one is enabled)
            if (enableIfUserAdmin && this.IsUserAlwaysEnabledForPilot(logger, request))
            {
                if (DynamicConfigValues.EnableAdminPilotFlagLogging)
                {
                    LogAdminFlagUsage(logger, request, requestedFeature);
                }
                return true;
            }

            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return CheckFeature(logger, request, accountId, requestedFeature);
        }

        public bool IsAccountEnabledForFeature(
            ILogShared logger,
            CampaignManagementRequest request,
            int accountId,
            AccountFeatureFlag requestedFeature,
            bool enableIfUserAdmin = true)
        {
            // NOTE: should not consider admin user enabled, if checking to deprecate some feature (if new one is enabled)
            if (enableIfUserAdmin && this.IsUserAlwaysEnabledForPilot(logger, request))
            {
                if (DynamicConfigValues.EnableAdminPilotFlagLogging)
                {
                    LogAdminFlagUsage(logger, request, requestedFeature);
                }
                return true;
            }

            return CheckFeature(logger, request, accountId, requestedFeature);
        }

        public bool IsAccountEnabledForHouseholdIncomeTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableHouseholdIncomeTargeting &&
                   this.IsFeatureEnabled(logger, request, AccountFeatureFlag.HouseholdIncomeTargeting);
        }


        public bool IsEnabledForDynamicFeedCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.DynamicFeedCampaign);
        }

        public bool IsEnabledForEnhancedProductAdsFilterPhase1(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.EnhancedProductAdsFilterPhase1);
        }

        public bool IsEnabledForEnhancedProductAdsFilterPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.EnhancedProductAdsFilterPhase2);
        }

        public bool IsCategoryandIdfilterforaudiencecampaiginEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.CategoryandIdfilterforaudiencecampaigin);
        }

        public bool IsAccountEnabledForDSASubdomain(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.DSASubdomain);
        }

        public bool IsAccountEnabledForShoppingSettingForMMA(ILogShared logger, CampaignManagementRequest request)
        {
            return IsFeatureEnabled(logger, request, AccountFeatureFlag.ShoppingSettingForMMA);
        }

        private bool CheckFeature(ILogShared logger, CampaignManagementRequest request, long accountId, AccountFeatureFlag requestedFeature)
        {
            if (!this.accountFeatures.Get(logger, this, accountId, request, out AccountFeatures existingFeatures))
            {
                Interlocked.Increment(ref accountFeaturesCacheMisses);
                existingFeatures = this.GetAccountFeature(logger, accountId, request);
                this.accountFeatures.Add(accountId, existingFeatures);
            }
            else
            {
                Interlocked.Increment(ref accountFeaturesCacheHits);
            }

            if (DateTime.UtcNow.Ticks - timeSinceLastAccountFeaturesCacheLog > accountFeaturesCacheLogSpan)
            {
                Interlocked.Exchange(ref timeSinceLastAccountFeaturesCacheLog, DateTime.UtcNow.Ticks);
                Interlocked.Increment(ref accountFeaturesCacheLogCounter);
                LogAccountFeaturesCache(logger);
            }

            return existingFeatures.Flags[ccToMTPilotFlagMapping[requestedFeature]];
        }

        private void LogAccountFeaturesCache(ILogShared logger)
        {
            logger.LogInfo($"Account feature cache misses: {accountFeaturesCacheMisses}. Hits: {accountFeaturesCacheHits}");
        }

        private static void RefreshData()
        {
            InternalCallTrackingData callTracking = new InternalCallTrackingData();
            ILogShared logger = new AdvertiserLogger(callTracking, null, null, "PilotCacheRefreshData");

            bool keepRunning = true;
            while (keepRunning)
            {
                try
                {
                    Thread.Sleep(CacheRefreshIntervalInMs);

                    //remove after piloting done
                    SafeSharedLog.LogVerbose(logger, "Account feature cache has {0} entries. Removing all of them.", instance.accountFeatures.Count);
                    instance.accountFeatures.Clear();

                    SafeSharedLog.LogVerbose(logger, "Always enabled user cache has {0} entries. Removing all of them.", instance.bypassPilotChecksUsersCache.Count);
                    instance.bypassPilotChecksUsersCache.Clear();
                }
                catch (ThreadAbortException)
                {
                    keepRunning = false;
                }
                catch (Exception e)
                {
                    SafeSharedLog.LogException(logger, e);
                }
            }
        }

        private AccountFeatures GetAccountFeature(ILogShared logger, long accountId, CampaignManagementRequest request)
        {
            var pilotCCMappedFeatures = this.GetAccountFeaturesBitArray(logger, accountId, request);
            return this.GetAccountFeature(pilotCCMappedFeatures);
        }

        private AccountFeatures GetAccountFeature(BitArray pilotCCMappedFeatures)
        {
            AccountFeatures accountFeatureFlagMappedFeatures = new AccountFeatures();
            foreach (AccountFeatureFlag flag in accountFeatureFlags)
            {
                accountFeatureFlagMappedFeatures.Flags[ccToMTPilotFlagMapping[flag]] = this.GetFeature(pilotCCMappedFeatures, flag);
            }
            //also need to check CallTrackingId with CallAdExtensionsOrCallTracking flag
            return accountFeatureFlagMappedFeatures;
        }

        private bool GetFeature(BitArray features, AccountFeatureFlag feature)
        {
            int featureId = (int)feature;
            return features.Length > featureId && features[featureId];
        }


        private bool IsUserAlwaysEnabledForPilot(ILogShared logger, CampaignManagementRequest request)
        {
            if (request.SecurityTicket != null && !string.IsNullOrEmpty(request.SecurityTicket.SecurityTicketId))
            {
                bool isAlwaysEnabled;

                if (!this.bypassPilotChecksUsersCache.Get(logger, null, request.UserId, out isAlwaysEnabled))
                {
                    isAlwaysEnabled = MTAuthorizationManager.Instance.IsAuthorizedWithRetry(
                        logger,
                        request.SecurityTicket.SecurityTicketId,
                        request.UserId,
                        AuthorizationActions.BypassPilotChecks,
                        false,
                        request.AuthorizationEntity);

                    this.bypassPilotChecksUsersCache.Add(request.UserId, isAlwaysEnabled);
                }

                return isAlwaysEnabled;
            }

            return false;
        }

        private void LogAdminFlagUsage(
            ILogShared logger,
            CampaignManagementRequest request,
            AccountFeatureFlag requestedFeature)
        {
            var userId = request.UserId;
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            bool expectedValue = CheckFeature(logger, request, accountId, requestedFeature);

            //we should log each unique combination of userId, feature requested, and whether the actual pilot flag is on or off
            int hashValue = ComputeAdminFlagHash(userId, requestedFeature, expectedValue);

            //returns true if the pair was added and therefore didn't exist before, so we log if so
            if (adminPilotFlags.TryAdd(hashValue, 0))
            {
                logger.LogInfo($"User ID {userId} is an admin account, so flag {requestedFeature} automatically returned true. Its actual value for the account used should be {expectedValue}.");
            }
        }

        private int ComputeAdminFlagHash(int userId, AccountFeatureFlag flag, bool expectedValue)
        {
            return userId.GetHashCode() ^ flag.GetHashCode() ^ expectedValue.GetHashCode();
        }

        public bool IsAccountEnabledForDynamicDescription(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DynamicDescription);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase1(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase1 &&
                   this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase1);
        }

        public List<uint> AdsGblPilotIds(ILogShared logger, CampaignManagementRequest request)
        {
            List<uint> adsGBlPilotIds = new List<uint>();

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationTextAdsJapan &&
                    this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase2Japanese)
                    )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhaseTextAdsJapan);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase3America &&
                  this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase3America)
                  )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase3AmericaMkt);
            }


            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase3APAC &&
                    this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase3APAC)
                    )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase3APACMkt);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase5Africa &&
                    this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase5Africa)
                    )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase5Africa);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase6MENA &&
                    this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase6MENA)
                    )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase6MENA);
            }


            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhaseSimplifiedChinese &&
                 this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationSimplifiedChinese)
                 )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhaseSimplifiedChinese);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase7 &&
               this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase7)
               )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase7);
            }

            if (DynamicConfigValues.EnablePilotCheckForCNMarketExpansion
                && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CNMarketExpansion, false) || Pilot.Instance.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CNMarketExpansion)))
            {
                adsGBlPilotIds.Add(LanguagePilot.CNMarketExpansion);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase8 &&
              this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase8)
              )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase8);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase9 &&
              this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase9)
              )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase9);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase9VI &&
              this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase9VI)
              )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPhase9VI);
            }

            if (DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPAKorea &&
              this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPAKorea)
              )
            {
                adsGBlPilotIds.Add(LanguagePilot.AdsGlobalizationPAKorea);
            }

            return adsGBlPilotIds;
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase3America(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase3America &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase3America);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase3APSC(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase3APAC &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase3APAC);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase5Africa(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase5Africa &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase5Africa);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase6MENA(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase6MENA &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase6MENA);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase7(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase7 &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase7);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase8(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase8 &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase8);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase9(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase9 &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase9);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase9VI(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase9VI &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase9VI);
        }

        public bool IsAccountEnabledForAdsGlobalizationPAKorea(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPAKorea &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPAKorea);
        }

        public bool IsAccountEnabledForAdsGlobalizationTextAdsJapan(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationTextAdsJapan &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase2Japanese);
        }

        public bool IsAccountEnabledForAdsGlobalizationPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhase2 &&
                   this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationPhase2);
        }

        public bool IsAccountEnabledAudienceAssociationBidRecommendation(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.AudienceAssociationBidRecommendation &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AudienceAssociationBidRecommendation);
        }

        public bool IsAccountEnabledForResponsiveAdAssetPerformance(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ResponsiveAdAssetPerformance, false);
        }

        public bool IsAccountEnabledForAssetLibraryV2MVP(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetLibraryV2MVP);
        }

        public bool IsAccountEnabledForAssetLibraryV3(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetLibraryV3, false);
        }

        public bool IsAccountEnabledForAdsStudio(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return DynamicConfigValues.EnableAdsStudioFeatures
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsStudioFeatures, false)
                || (accountId % 100 < DynamicConfigValues.EnableAdsStudioFeaturesPilotPercentage));
        }

        public bool IsAccountEnabledForAdsStudioBackfill(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return DynamicConfigValues.EnableAdsStudioFeatures && (accountId % 100 < DynamicConfigValues.EnableAdStudioIndexingBackfillPilotPercentage);
        }

        public bool IsAccountEnabledForBrandKit(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return DynamicConfigValues.EnableBrandKit
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandKit, false)
                || (accountId % 100 < DynamicConfigValues.EnableBrandKitPilotPercentage));
        }

        public bool IsAccountEnabledForBrandKitPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            return DynamicConfigValues.EnableBrandKitForPhase2
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandKitPhase2, false)
                || (accountId % 100 < DynamicConfigValues.EnableBrandKitForPhase2PilotPercentage));
        }

        public bool IsAccountEnabledForBrandKitBulk(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            return DynamicConfigValues.EnableBrandKitBulk
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandKitBulk, false)
                || (accountId % 100 < DynamicConfigValues.EnableBrandKitBulkPilotPercentage));
        }

        public bool IsAccountEnabledForConversionDelayMetrics(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            return DynamicConfigValues.EnableConversionDelayMetrics &&
                (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ConversionDelayMetrics, false));
        }

        public bool IsAccountEnabledForRecommendationAPIV2(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            return DynamicConfigValues.EnableRecommendationAPIV2
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.RecommendationAPIV2, false)
                || (accountId % 100 < DynamicConfigValues.EnableRecommendationAPIV2PilotPercentage));
        }

        public bool IsAccountEnabledForVideoAdsGeneration(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return DynamicConfigValues.EnableVideoAdsGeneration
            && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAdsGeneration, false)
                || (accountId % 100 < DynamicConfigValues.EnableVideoAdsGenerationPilotPercentage));
        }

        public bool IsAccountEnabledForGoogleImportVideoAds(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return (accountId % 100 < DynamicConfigValues.GoogleVideoCampaignImportEnabledForAccountPercentage) ||
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.GoogleImportVideoAds, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForGoogleImageAdImportWithAssetIdMissing(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.GoogleImageAdImportWithAssetIdMissing, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForVideoAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAds, enableIfUserAdmin: false)
                || this.IsFeatureEnabled(logger, request, AccountFeatureFlag.XandrVideoAds, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForShowPmaxAssetInAssetGrid(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnablePmaxAssetInAssetGrid, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForXandrVideoAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.XandrVideoAds, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForDisplayAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DisplayAds, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMSANServingForVideoCampaigns(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANServingForVideoCampaigns, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForVideoAsAsset(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnabledForVideoAsAsset, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMSANServingForCTVCampaigns(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANServingForCTVCampaigns, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForPremiumStreamingOTTPhase1(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PremiumStreamingOTTPhase1, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMSANServingForDisplayCampaigns(ILogShared logger, CampaignManagementRequest request)
        {
            return (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANServingForDisplayCampaigns, enableIfUserAdmin: false)) ||
              // We essentially GA this.
              DynamicConfigValues.EnableMSANServingForAllDisplay;
        }

        public bool IsAccountEnabledForMSANVideoAdsAudienceTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANVideoAdsAudienceTarget, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForHTML5Asset(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.HTML5Asset, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMultiFormatAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MultiFormatAds, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForDeprecateManualCpcForAudienceCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DeprecateManualCpcForAudienceCampaign, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForManualCpm(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ManualCpm) || this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LeadGen);
        }

        public bool IsAccountEnabledForVideoAdEditor(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAdEditor);
        }

        public bool IsAccountEnabledForVideoAdsEditorCropClip(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAdsEditorCropClip);
        }

        public bool IsAccountEnabledForVideoAdsEditorCustomThumbnail(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAdsEditorCustomThumbnail);
        }

        public bool IsAccountEnabledForVideoAdsEditorCaption(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoAdsEditorCaption);
        }

        public bool IsAccountEnabledForMediaLibrary(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MediaLibrary);
        }

        public bool IsAccountEnabledForPortfolioBidStrategy(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PortfolioBidStrategy);
        }

        public bool IsAccountEnabledForHotelPayPerStay(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.HotelPayPerStay);
        }

        public bool IsAccountEnabledForHotelPayPerStayPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.HotelPayPerStayPhase2);
        }

        public bool IsAccountEnabledForHotelCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.HotelCampaign);
        }

        public bool IsAccountEnabledForPerformanceMaxCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaign) && !Pilot.Instance.IsEnabledForAdultAds(logger, request);
        }

        public bool IsAccountEnabledForPerformanceMaxCampaignBrandExclusion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxBrandExclusion, false);
        }

        public bool IsAccountInPerformanceMaxPhaseZeroWhitelist(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxPhaseZeroWhitelist, false);
        }

        public bool IsAccountEnabledForPersonalizedOffers(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PersonalizedOffers, false) ||
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PersonalizedOffersMVP, false) ||
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PersonalizedOffersV3, false) ||
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CostPerSale, false);
        }

        public bool IsAccountEnabledForPersonalizedOffersMVP(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PersonalizedOffersMVP, false);
        }

        public bool IsAccountEnabledForPersonalizedOffersV3(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PersonalizedOffersV3, false) ||
                 this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CostPerSale, false);
        }

        public bool IsAccountEnabledForCostPerSaleBiddingScheme(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CostPerSale, false);
        }

        public bool IsAccountEnabledForFeedItemInNewShard(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.FeedItemInNewShard, false);
        }

        public bool IsAccountEnabledForAssetSharding(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetSharding, false);
        }

        public bool IsAccountEnabledForCrawImagesFromLandingPage(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AllowCrawlImagesFromLandingPage);
        }

        public bool IsAccountEnabledForCampaignDailyCapNewPipeline(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CampaignDailyCapNewPipeline);
        }

        public bool IsAccountEnabledForUrlEqualsInDSATarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.UrlEqualsInDSATarget);
        }

        public bool IsAccountEnabledForSmartCampaignImport(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SmartCampaignImport);
        }

        public bool IsAccountEnabledForPerformanceMaxCampaignLevelAutoGenAssetsControl(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignLevelAutoGenAssetsControl);
        }

        public bool IsAccountEnabledForPerformanceMaxCampaignLevelCostPerSaleOptOut(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignLevelCostPerSaleOptOut);
        }

        public bool IsAccountEnabledForPerformanceMaxCampaignAssetGroupSearchTheme(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignAssetGroupSearchTheme);
        }

        public bool IsAccountEnabledForPerformanceMaxAudienceInsight(ILogShared logger, CampaignManagementRequest request)
        {
            return (DynamicConfigValues.IsTestInProdEnv || this.IsFeatureEnabled(logger, request,
                AccountFeatureFlag.PerformanceMaxCampaignAudienceInsight));
        }
        
        public bool IsAccountEnabledForAssetGroupSegmentation(ILogShared logger, CampaignManagementRequest request)
        {
            return (DynamicConfigValues.IsTestInProdEnv || this.IsFeatureEnabled(logger, request,
                AccountFeatureFlag.PMaxAssetGroupSegmentation));
        }

        public bool IsAccountEnabledForPerformanceMaxCampaignInChina(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignInChina);
        }
        public bool IsAccountEnabledForPerformanceMaxCampaignAssetGroupUrlTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignAssetGroupUrlTarget);
        }

        public bool IsAccountEnabledForMCVForSearchCampaigns(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableMCVForSearchCampaigns, false);
        }

        public bool IsAccountEnabledForMCVForStandardShoppingCampaigns(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableMCVForStandardShoppingCampaigns, false);
        }

        public bool IsAccountEnabledForMMAInDSA(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MultiMediaAdsInDSAAdGroup, false);
        }

        public bool IsAccountEnabledForMMAAdCustomizer(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MMAAdCustomizer, false);
        }

        public bool IsAccountEnabledForLeadGen(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LeadGen, false);
        }

        public bool IsAccountEnabledForBoost(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.Boost, false);
        }

        public bool IsAccountEnabledForBoostConversionBasedSegment(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BoostConversionBasedSegment, false);
        }

        public bool IsAccountEnabledForROI(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ROIUnify, false);
        }

        public bool IsAccountEnabledForVideoAsAssetInAssetGroup(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxVideoAsAsset, false);
        }

        public bool IsAccountEnabledForROIUnify(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsAccountEnabledForROI(logger, request);
        }

        public bool IsAccountEnabledFor1PTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.Enable1PPlacementTargeting, false);
        }

        public bool IsAccountEnabledForMSANLogoAndCTAForMMA(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANLogoAndCTAForMMA, false);
        }

        public bool IsAccountEnabledForMMAV2(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MMAV2, false);
        }

        public bool IsAccountEnabledMMAV2ConvertExclusion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MMAV2ConvertExclusion, false);
        }

        public bool IsAccountEnabledForSimplifiedChinese(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnablePilotCheckForAdsGlobalizationPhaseSimplifiedChinese &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AdsGlobalizationSimplifiedChinese, false);
        }

        public bool IsAccountEnabledForClick2CallMigration(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.Click2CallMigration, false);
        }

        public bool IsAccountEnabledForShoppableAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ShoppableAds, false);
        }

        public bool IsAccountEnabledForCNMarketExpansion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.CNMarketExpansion, false);
        }

        public bool IsAccountEnabledForRSAAutoGeneratedAssets(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);
            return (accountId % 100 < DynamicConfigValues.RSAAutoAssetAccountPilotPercentage) ||
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.RSAAutoGeneratedAssets, false);
        }


        public bool IsAccountEnabledForSearchCampaignPredictiveTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)AccountFeatureFlag.SearchCampaignPredictiveTargeting);
        }

        public bool IsAccountEnabledForSearchCampaignPredictiveMatching(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)AccountFeatureFlag.SearchCampaignPredictiveMatching);
        }

        public bool IsAccountEnabledForBrandAwarenessCampaignIAS(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableCampaignVerifyTrackingSetting &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.IASForBrandAwarenessCampaign);
        }

        public bool IsAccountEnabledForIASForBrandAwarenessVideoAdCPM(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.IASForBrandAwarenessCampaign);
        }

        public bool IsAccountEnabledForIASForNativeAdCPM(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.IASForNativeAdCPM);
        }

        public bool IsAccountEnabledForIASForDisplayAdCPM(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DisplayAds);
        }

        public bool IsAccountEnabledForDeal(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DealForMsAds, false);
        }

        public bool IsAccountEnabledForPlacementTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PlacementTargeting, false);
        }

        public bool IsAccountEnabledForTopicTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.TopicTargeting, false);
        }

        public bool IsAccountEnabledForSubPlacementTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SubPlacementTargeting, false);
        }

        public bool IsAccountEnabledForAssetResharding(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetResharding, false);
        }

        public bool IsAccountEnabledForOfflineConversionReport(ILogShared logger, CampaignManagementRequest request)
        {
            if (!DynamicConfigValues.OfflineConversionReportEnabled)
            {
                return false;
            }

            if (!request.CustomerAccountId.HasValue)
            {
                return false;
            }

            if (request.AdvertiserCustomerId.HasValue && DynamicConfigValues.OfflineConversionReportLargeFileCustomerBlockList.Contains(request.AdvertiserCustomerId.Value))
            {
                logger?.LogInfo($"[OfflineConversionReportLargeFileCustomerBlockList] {request.AdvertiserCustomerId.Value}");
                return false;
            }

            var accountId = request.CustomerAccountId.Value;

            return accountId % 100 < DynamicConfigValues.PercentageOfOfflineConversionReportPilot || this.IsFeatureEnabled(logger, request, AccountFeatureFlag.OfflineConversionReport, false);
        }

        public bool isAccountEnabledForLocationIntentChange(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LocationTargetingImprovement, false);
        }

        public bool IsAccountEnabledForNegativeAgeTargetForAdGroups(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.NegativeAgeTargetForAdGroups, false);
        }

        public bool IsAccountEnabledForAssetLibraryAIGC(ILogShared logger, CampaignManagementRequest request)
        {
            return true;
        }

        public bool IsAccountEnabledForPMaxLite(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxLite, false);
        }

        public bool IsAccountEnabledForPerformanceMaxInChina(ILogShared logShared, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logShared, request, AccountFeatureFlag.PerformanceMaxCampaignInChina, false);
        }

        public bool IsAccountEnabledForGenreTargets(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.GenreTargets, false);
        }

        public bool IsAccountEnabledForAccountNegativeKeywordList(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AccountNegativeKeywordList);
        }

        public bool IsAccountEnabledForPerformanceMaxMaxClicks(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxMaxClicks) || this.IsAccountEnabledForPMaxLite(logger, request);
        }

        public bool IsAccountEnabledForPerformanceMaxSelect(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxSelect);
        }

        public bool IsAccountEnabledForPerformanceMaxAutomatedCTA(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxAutomatedCTA, false)
                   && !this.IsAccountEnabledForPerformanceMaxInChinaAndCNMarketExpansion(logger, request);
        }

        public bool IsAccountEnabledForPerformanceMaxInChinaAndCNMarketExpansion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PerformanceMaxCampaignInChina, false)
                   && IsAccountEnabledForCNMarketExpansion(logger, request);
        }

        public bool IsAccountEnabledForAudienceAdSmartCrop(ILogShared logger, CampaignManagementRequest request, IAccountCallContext callContext)
        {
            bool enabledByPercentage = PilotedFeature.EnabledSmartCropOfflineProcessing.IsEnabled(callContext, pilotingLevel: PilotingLevel.Account, logPilotStatus: true);
            bool enabledByCCPilot = this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AudienceAdSmartCrop);
            return enabledByCCPilot || enabledByPercentage;
        }

        public bool IsAccountEnabledForAdExtensionSmartCrop(ILogShared logger, CampaignManagementRequest request, IAccountCallContext callContext)
        {
            bool enabledByPercentage = PilotedFeature.EnabledSmartCropOfflineProcessing.IsEnabled(callContext, pilotingLevel: PilotingLevel.Account, logPilotStatus: true);
            bool enabledByCCPilot = this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ImageAdExtensionSmartCrop, false);
            return enabledByCCPilot || enabledByPercentage;
        }

        public bool IsAccountEnabledForMTAssetBasedEditorialRSA(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAssetBasedEditorial &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MTAssetBasedEditorialRSA, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMTAssetBasedEditorialAIM(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAssetBasedEditorial &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MTAssetBasedEditorialAIM, enableIfUserAdmin: false);
        }

        private bool IsAccountEnabledForAssetBasedEditorialRSA(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAssetBasedEditorial &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetBasedEditorialRSA, enableIfUserAdmin: false);
        }

        private bool IsAccountEnabledForAssetBasedEditorialAIM(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAssetBasedEditorialForAIM &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AssetBasedEditorialAIM, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForAssetBasedEditorialFeature(AccountCallContext context)
        {
            return this.IsAccountEnabledForAssetBasedEditorialRSA(context.Logger, context.Request) || this.IsAccountEnabledForAssetBasedEditorialAIM(context.Logger, context.Request);
        }

        public bool IsAccountEnabledForMCVForPortfolioBidStrategy(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableMCVForPortfolioBidStrategy);
        }

        public bool IsAccountEnabledForMSANLiftExperiment(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANLiftExperiment);
        }

        public bool IsAccountEnabledForAudienceDataExclusion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableDataExclusionForAudienceCampaignType);
        }

        public bool IsAccountEnabledForBroadMatchOnlyCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BroadOnlyCampaign, false);
        }

        public bool IsSSCDeprecationEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SSCDeprecation, false);
        }

        public bool IsAccountEnabledForWindowsAppCampaignStartExperience(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.WindowsAppCampaignStartExperience, false);
        }

        public bool IsAccountEnabledForWindowsAppCampaignLegacyCreationFlowDeprecation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.WindowsAppCampaignLegacyCreationFlowDeprecation, false);
        }

        public bool IsAccountEnabledForWindowsAppCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.WindowsAppCampaign, false);
        }

        public bool IsAccountEnabledForUniversalAppCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.UnifiedAppCampaign, false);
        }

        public bool IsAccountEnabledForSyndicationOnlyDeprecation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SyndicationOnlyDeprecation);
        }

        public bool IsAccountEnabledForGoalImport(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.GoalImportPhase1, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForMAEConversionGoals(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MAEConversionGoals, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForVideoLibraryTranscoding(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoLibraryTranscoding);
        }

        public bool IsAccountInSyndicationOnlyWhitelist(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SyndicationOnlyWhitelist, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForBrandSafety(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandSafety, false) || this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandSafetyWithUnscored, false);
        }

        public bool IsAccountEnabledForBrandSafetyWithUnscored(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.BrandSafetyWithUnscored, false);
        }

        public bool IsAccountEnabledForMSANDisclaimer(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANDisclaimer, false);
        }

        public bool IsAccountEnabledForMSANISPOT(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MSANISPOT, false);
        }

        public bool IsAccountEnabledForLinkedInCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LinkedInCampaign, false);
        }

        public bool IsAccountEnabledForDoubleVerifyActivation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.DoubleVerifyActivation, false);
        }

        public bool IsAccountEnabledForModeledConversionUpliftReport(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ModeledConversionUpliftReport, false);
        }
        public bool IsAccountEnabledForAudienceCampaignExpandedHeadlineCharLimits(ILogShared logger, CampaignManagementRequest request)
        {
            int customerId = EOHelper.GetAdvertiserCustomerId2(logger, request);
            return !this.IsFeatureEnabled(logger, request, AccountFeatureFlag.Boost, false)
                   && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AudienceCampaignExpandedHeadlineCharLimits, false)
                       || (customerId % 100 < DynamicConfigValues.AudienceCampaignExpandedHeadlineCharLimitsCustomerPercentage));
        }

        public bool IsAccountEnabledForPMaxDsaUrlValidation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxDsaUrlValidation, false);
        }

        public bool IsAccountEnabledForPMaxV2AspectRatios(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxV2AspectRatios, false)
                && !this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxV2AspectRatiosExclusions, false);
        }

        public bool IsAccountEnabledForPmaxNewCustomerAcquisition(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PmaxNewCustomerAcquisition);
        }

        public bool IsMobileAppCampaignConversionGoalEnabledForAccount(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MobileAppCampaignConversionGoal);
        }

        public bool IsAccountEnabledForMultiFormatAdsImpressionTracking(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.ImpressionTrackingForMultiFormatAds, false);
        }

        public bool IsAccountEnabledForAppCampaignPlacementTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AppCampaignPlacementTargeting);
        }

        public bool IsAccountPlacementExclusionListEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAccountPlacementExclusionLists &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AccountPlacementExclusionList);
        }

        public bool IsAccountPlacementInclusionListEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAccountPlacementInclusionLists &&
                this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AccountPlacementInclusionList);
        }

        public bool IsAccountLinkedInTargetBulkUploadEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableLinkedInTargetBulkUpload);
        }

        public bool IsAccountEnabledForLifetimeBudget(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.LifetimeBudgetEnabled
                && this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LifetimeBudget, enableIfUserAdmin: false)
                && !this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MAELifetimeBudgetExclusion, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForLifetimeBudgetForImport(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.LifetimeBudgetEnabledForImport && DynamicConfigValues.LifetimeBudgetEnabled
                && this.IsFeatureEnabled(logger, request, AccountFeatureFlag.LifetimeBudget, enableIfUserAdmin: false)
                && this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableLifetimeBudgetForImport, enableIfUserAdmin: false)
                && !this.IsFeatureEnabled(logger, request, AccountFeatureFlag.MAELifetimeBudgetExclusion, enableIfUserAdmin: false);
        }

        public bool IsAccountEnabledForAudienceShoppingFeedLabel(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.AudienceShoppingCampaignFeedLabel);
        }

        public bool IsAccountEnabledForVerticalVideoSupport(ILogShared logger, CampaignManagementRequest request)
        {
            var accountId = EOHelper.GetCustomerAccountId(logger, request);

            return DynamicConfigValues.VerticalVideoSupport
                && (this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VerticalVideoSupport, false)
                || (accountId % 100 < DynamicConfigValues.VerticalVideoSupportPilotPercentage));
        }

        public bool IsAccountEnabledForDiagnostics(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.Diagnostics);
        }

        public bool IsAccountEnabledForPmaxDisclaimer(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PmaxDisclaimer, false);
        }

        public bool IsAccountEnabledForSmartCampaignMigration(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.SmartCampaignToPMaxMigration, false);
        }


        public bool IsAccountEnabledForAudienceVideoRecommendation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.VideoRecommendationForAudienceCampaign, false);
        }

        public bool IsAccountEnabledForPMaxEnhanceListings(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, AccountFeatureFlag.PMaxEnhanceListings, false);
        }

        public bool IsAccountEnabledForAIGCForAADUser(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAIGCForAADUser && this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableAIGCForAADUser, false);
        }

        public bool IsAccountEnabledForNoneLanguageCallToAction(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.SetCTALanguageAsNone || this.IsFeatureEnabled(logger, request, AccountFeatureFlag.EnableNoneLanguageCallToAction, false);
        }

    }
}

