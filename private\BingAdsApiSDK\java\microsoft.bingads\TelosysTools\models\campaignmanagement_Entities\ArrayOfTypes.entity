@Context(campaignmanagement)
ArrayOfTypes {
  AppUrl : string { @DefaultValue(AppUrl) @Label( "AppUrls") } ;
  CustomParameter : string { @DefaultValue(CustomParameter) @Label( "CustomParameters") } ;
  AssetLink : string { @DefaultValue(AssetLink) @Label( "AssetLinks") } ;
  TargetSettingDetail : string { @DefaultValue(TargetSettingDetail) @Label( "TargetSettingDetails") } ;
  Campaign : string { @DefaultValue(Campaign) @Label( "Campaigns") } ;
  Setting : string { @DefaultValue(Setting) @Label( "Settings") } ;
  BatchError : string { @DefaultValue(BatchError) @Label( "BatchErrors") } ;
  OperationError : string { @DefaultValue(OperationError) @Label( "OperationErrors") } ;
  CampaignNegativeSites : string { @DefaultValue(CampaignNegativeSites) @Label( "CampaignNegativeSites") } ;
  AdGroup : string { @DefaultValue(AdGroup) @Label( "AdGroups") } ;
  FrequencyCapSettings : string { @DefaultValue(FrequencyCapSettings) @Label( "FrequencyCapSettings") } ;
  AdGroupNegativeSites : string { @DefaultValue(AdGroupNegativeSites) @Label( "AdGroupNegativeSites") } ;
  Ad : string { @DefaultValue(Ad) @Label( "Ads") } ;
  EditorialError : string { @DefaultValue(EditorialError) @Label( "EditorialErrors") } ;
  AdType : string { @DefaultValue(AdType) @Label( "AdTypes") } ;
  Keyword : string { @DefaultValue(Keyword) @Label( "Keywords") } ;
  EntityIdToParentIdAssociation : string { @DefaultValue(EntityIdToParentIdAssociation) @Label( "EntityIdToParentIdAssociations") } ;
  EditorialReasonCollection : string { @DefaultValue(EditorialReasonCollection) @Label( "EditorialReasonCollections") } ;
  EditorialReason : string { @DefaultValue(EditorialReason) @Label( "EditorialReasons") } ;
  AccountMigrationStatusesInfo : string { @DefaultValue(AccountMigrationStatusesInfo) @Label( "AccountMigrationStatusesInfos") } ;
  MigrationStatusInfo : string { @DefaultValue(MigrationStatusInfo) @Label( "MigrationStatusInfos") } ;
  AccountProperty : string { @DefaultValue(AccountProperty) @Label( "AccountProperties") } ;
  AccountPropertyName : string { @DefaultValue(AccountPropertyName) @Label( "AccountPropertyNames") } ;
  AdExtension : string { @DefaultValue(AdExtension) @Label( "AdExtensions") } ;
  DayTime : string { @DefaultValue(DayTime) @Label( "DayTimes") } ;
  PriceTableRow : string { @DefaultValue(PriceTableRow) @Label( "PriceTableRows") } ;
  AdExtensionIdentity : string { @DefaultValue(AdExtensionIdentity) @Label( "AdExtensionIdentities") } ;
  BatchErrorCollection : string { @DefaultValue(BatchErrorCollection) @Label( "BatchErrorCollections") } ;
  AdExtensionIdToEntityIdAssociation : string { @DefaultValue(AdExtensionIdToEntityIdAssociation) @Label( "AdExtensionIdToEntityIdAssociations") } ;
  AdExtensionEditorialReasonCollection : string { @DefaultValue(AdExtensionEditorialReasonCollection) @Label( "AdExtensionEditorialReasonCollections") } ;
  AdExtensionEditorialReason : string { @DefaultValue(AdExtensionEditorialReason) @Label( "AdExtensionEditorialReasons") } ;
  AdExtensionAssociationCollection : string { @DefaultValue(AdExtensionAssociationCollection) @Label( "AdExtensionAssociationCollections") } ;
  AdExtensionAssociation : string { @DefaultValue(AdExtensionAssociation) @Label( "AdExtensionAssociations") } ;
  Media : string { @DefaultValue(Media) @Label( "Medias") } ;
  MediaMetaData : string { @DefaultValue(MediaMetaData) @Label( "MediaMetaDatas") } ;
  MediaRepresentation : string { @DefaultValue(MediaRepresentation) @Label( "MediaRepresentations") } ;
  ArrayOfMediaAssociation : string { @DefaultValue(ArrayOfMediaAssociation) @Label( "ArrayOfMediaAssociations") } ;
  MediaAssociation : string { @DefaultValue(MediaAssociation) @Label( "MediaAssociations") } ;
  AdGroupCriterion : string { @DefaultValue(AdGroupCriterion) @Label( "AdGroupCriterions") } ;
  ProductCondition : string { @DefaultValue(ProductCondition) @Label( "ProductConditions") } ;
  WebpageCondition : string { @DefaultValue(WebpageCondition) @Label( "WebpageConditions") } ;
  AdGroupCriterionAction : string { @DefaultValue(AdGroupCriterionAction) @Label( "AdGroupCriterionActions") } ;
  AssetGroupListingGroupAction : string { @DefaultValue(AssetGroupListingGroupAction) @Label( "AssetGroupListingGroupActions") } ;
  AssetGroupListingGroup : string { @DefaultValue(AssetGroupListingGroup) @Label( "AssetGroupListingGroups") } ;
  BMCStore : string { @DefaultValue(BMCStore) @Label( "BMCStores") } ;
  EntityNegativeKeyword : string { @DefaultValue(EntityNegativeKeyword) @Label( "EntityNegativeKeywords") } ;
  NegativeKeyword : string { @DefaultValue(NegativeKeyword) @Label( "NegativeKeywords") } ;
  IdCollection : string { @DefaultValue(IdCollection) @Label( "IdCollections") } ;
  SharedEntity : string { @DefaultValue(SharedEntity) @Label( "SharedEntities") } ;
  SharedListItem : string { @DefaultValue(SharedListItem) @Label( "SharedListItems") } ;
  SharedEntityAssociation : string { @DefaultValue(SharedEntityAssociation) @Label( "SharedEntityAssociations") } ;
  CampaignSize : string { @DefaultValue(CampaignSize) @Label( "CampaignSizes") } ;
  CampaignCriterion : string { @DefaultValue(CampaignCriterion) @Label( "CampaignCriterions") } ;
  Budget : string { @DefaultValue(Budget) @Label( "Budgets") } ;
  BidStrategy : string { @DefaultValue(BidStrategy) @Label( "BidStrategies") } ;
  AudienceGroup : string { @DefaultValue(AudienceGroup) @Label( "AudienceGroups") } ;
  AudienceGroupDimension : string { @DefaultValue(AudienceGroupDimension) @Label( "AudienceGroupDimensions") } ;
  AgeRange : string { @DefaultValue(AgeRange) @Label( "AgeRanges") } ;
  GenderType : string { @DefaultValue(GenderType) @Label( "GenderTypes") } ;
  AudienceInfo : string { @DefaultValue(AudienceInfo) @Label( "AudienceInfos") } ;
  ProfileInfo : string { @DefaultValue(ProfileInfo) @Label( "ProfileInfos") } ;
  AssetGroup : string { @DefaultValue(AssetGroup) @Label( "AssetGroups") } ;
  AssetGroupSearchTheme : string { @DefaultValue(AssetGroupSearchTheme) @Label( "AssetGroupSearchThemes") } ;
  AssetGroupUrlTarget : string { @DefaultValue(AssetGroupUrlTarget) @Label( "AssetGroupUrlTargets") } ;
  AssetGroupEditorialReasonCollection : string { @DefaultValue(AssetGroupEditorialReasonCollection) @Label( "AssetGroupEditorialReasonCollections") } ;
  AssetGroupEditorialReason : string { @DefaultValue(AssetGroupEditorialReason) @Label( "AssetGroupEditorialReasons") } ;
  AudienceGroupAssetGroupAssociation : string { @DefaultValue(AudienceGroupAssetGroupAssociation) @Label( "AudienceGroupAssetGroupAssociations") } ;
  Audience : string { @DefaultValue(Audience) @Label( "Audiences") } ;
  CustomerAccountShare : string { @DefaultValue(CustomerAccountShare) @Label( "CustomerAccountShares") } ;
  CustomerAccountShareAssociation : string { @DefaultValue(CustomerAccountShareAssociation) @Label( "CustomerAccountShareAssociations") } ;
  RuleItemGroup : string { @DefaultValue(RuleItemGroup) @Label( "RuleItemGroups") } ;
  RuleItem : string { @DefaultValue(RuleItem) @Label( "RuleItems") } ;
  CombinationRule : string { @DefaultValue(CombinationRule) @Label( "CombinationRules") } ;
  UetTag : string { @DefaultValue(UetTag) @Label( "UetTags") } ;
  ConversionGoal : string { @DefaultValue(ConversionGoal) @Label( "ConversionGoals") } ;
  OfflineConversion : string { @DefaultValue(OfflineConversion) @Label( "OfflineConversions") } ;
  OfflineConversionAdjustment : string { @DefaultValue(OfflineConversionAdjustment) @Label( "OfflineConversionAdjustments") } ;
  OnlineConversionAdjustment : string { @DefaultValue(OnlineConversionAdjustment) @Label( "OnlineConversionAdjustments") } ;
  DailySummary : string { @DefaultValue(DailySummary) @Label( "DailySummaries") } ;
  Label : string { @DefaultValue(Label) @Label( "Labels") } ;
  LabelAssociation : string { @DefaultValue(LabelAssociation) @Label( "LabelAssociations") } ;
  Experiment : string { @DefaultValue(Experiment) @Label( "Experiments") } ;
  Company : string { @DefaultValue(Company) @Label( "Companies") } ;
  ImportJob : string { @DefaultValue(ImportJob) @Label( "ImportJobs") } ;
  CampaignAdGroupIds : string { @DefaultValue(CampaignAdGroupIds) @Label( "CampaignAdGroupIds") } ;
  ImportResult : string { @DefaultValue(ImportResult) @Label( "ImportResults") } ;
  ImportEntityStatistics : string { @DefaultValue(ImportEntityStatistics) @Label( "ImportEntityStatistics") } ;
  Video : string { @DefaultValue(Video) @Label( "Videos") } ;
  CampaignConversionGoal : string { @DefaultValue(CampaignConversionGoal) @Label( "CampaignConversionGoals") } ;
  DataExclusion : string { @DefaultValue(DataExclusion) @Label( "DataExclusions") } ;
  CampaignAssociation : string { @DefaultValue(CampaignAssociation) @Label( "CampaignAssociations") } ;
  SeasonalityAdjustment : string { @DefaultValue(SeasonalityAdjustment) @Label( "SeasonalityAdjustments") } ;
  AdRecommendationImageSuggestion : string { @DefaultValue(AdRecommendationImageSuggestion) @Label( "AdRecommendationImageSuggestions") } ;
  AdRecommendationCustomizedProperty : string { @DefaultValue(AdRecommendationCustomizedProperty) @Label( "AdRecommendationCustomizedProperties") } ;
  AdRecommendationImageAssetProperty : string { @DefaultValue(AdRecommendationImageAssetProperty) @Label( "AdRecommendationImageAssetProperties") } ;
  AdRecommendationTextAssetProperty : string { @DefaultValue(AdRecommendationTextAssetProperty) @Label( "AdRecommendationTextAssetProperties") } ;
  AdRecommendationVideoSuggestion : string { @DefaultValue(AdRecommendationVideoSuggestion) @Label( "AdRecommendationVideoSuggestions") } ;
  AdRecommendationTextRefineOperation : string { @DefaultValue(AdRecommendationTextRefineOperation) @Label( "AdRecommendationTextRefineOperations") } ;
  AdRecommendationImageRefineOperation : string { @DefaultValue(AdRecommendationImageRefineOperation) @Label( "AdRecommendationImageRefineOperations") } ;
  AdRecommendationTextRefineResult : string { @DefaultValue(AdRecommendationTextRefineResult) @Label( "AdRecommendationTextRefineResults") } ;
  AdRecommendationMediaRefineResult : string { @DefaultValue(AdRecommendationMediaRefineResult) @Label( "AdRecommendationMediaRefineResults") } ;
  AdRecommendationRefinedMedia : string { @DefaultValue(AdRecommendationRefinedMedia) @Label( "AdRecommendationRefinedMedias") } ;
  ConversionValueRule : string { @DefaultValue(ConversionValueRule) @Label( "ConversionValueRules") } ;
  AudienceConditionItem : string { @DefaultValue(AudienceConditionItem) @Label( "AudienceConditionItems") } ;
  LocationConditionItem : string { @DefaultValue(LocationConditionItem) @Label( "LocationConditionItems") } ;
  BrandKit : string { @DefaultValue(BrandKit) @Label( "BrandKits") } ;
  BrandKitFont : string { @DefaultValue(BrandKitFont) @Label( "BrandKitFonts") } ;
  BrandKitImage : string { @DefaultValue(BrandKitImage) @Label( "BrandKitImages") } ;
  BrandKitPalette : string { @DefaultValue(BrandKitPalette) @Label( "BrandKitPalettes") } ;
  BrandKitColor : string { @DefaultValue(BrandKitColor) @Label( "BrandKitColors") } ;
  NewCustomerAcquisitionGoal : string { @DefaultValue(NewCustomerAcquisitionGoal) @Label( "NewCustomerAcquisitionGoals") } ;
  AudienceIdName : string { @DefaultValue(AudienceIdName) @Label( "AudienceIdNames") } ;
  ClipchampTemplateInfo : string { @DefaultValue(ClipchampTemplateInfo) @Label( "ClipchampTemplateInfos") } ;
  SupportedClipchampAudio : string { @DefaultValue(SupportedClipchampAudio) @Label( "SupportedClipchampAudios") } ;
  SupportedFont : string { @DefaultValue(SupportedFont) @Label( "SupportedFonts") } ;
  HealthCheckEntity : string { @DefaultValue(HealthCheckEntity) @Label( "HealthCheckEntities") } ;
  HealthCheckMetadata : string { @DefaultValue(HealthCheckMetadata) @Label( "HealthCheckMetadatas") } ;
  HealthCheckColumnMetadata : string { @DefaultValue(HealthCheckColumnMetadata) @Label( "HealthCheckColumnMetadatas") } ;
  HealthCheckActionLinkMetadata : string { @DefaultValue(HealthCheckActionLinkMetadata) @Label( "HealthCheckActionLinkMetadatas") } ;
  HealthCheckData : string { @DefaultValue(HealthCheckData) @Label( "HealthCheckDatas") } ;
  HealthCheckError : string { @DefaultValue(HealthCheckError) @Label( "HealthCheckErrors") } ;
  DiagnosticCategoryData : string { @DefaultValue(DiagnosticCategoryData) @Label( "DiagnosticCategoryDatas") } ;
  DiagnosticCardData : string { @DefaultValue(DiagnosticCardData) @Label( "DiagnosticCardDatas") } ;
  AnnotationOptOut : string { @DefaultValue(AnnotationOptOut) @Label( "AnnotationOptOuts") } ;
  string : string { @DefaultValue(String) @Label( "Strings") } ;
  long : string { @DefaultValue(Long) @Label( "Longs") } ;
  KeyValueOfstringstring : string { @DefaultValue(KeyValueOfstringstring) @Label( "KeyValueOfstringstrings") } ;
  int : string { @DefaultValue(Integer) @Label( "Ints") } ;
  KeyValuePairOfstringstring : string { @DefaultValue(KeyValuePairOfstringstring) @Label( "KeyValuePairOfstringstrings") } ;
  ArrayOfKeyValuePairOfstringstring : string { @DefaultValue(ArrayOfKeyValuePairOfstringstring) @Label( "ArrayOfKeyValuePairOfstringstrings") } ;
  KeyValuePairOflonglong : string { @DefaultValue(KeyValuePairOflonglong) @Label( "KeyValuePairOflonglongs") } ;
  NullableOflong : string { #SpecialProperty };
  AdApiError : string { @DefaultValue(AdApiError) @Label( "AdApiErrors") } ;
}
