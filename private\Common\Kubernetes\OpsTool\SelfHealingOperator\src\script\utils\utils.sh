#!/bin/bash

# Define all available options and their descriptions
declare -A OPTION_DESCRIPTIONS=(
    ["-h"]="Print help info in plain text format"
    ["--json-help"]="Print help info in JSON format"
    ["-n"]="Target namespace"
    ["-d"]="Target deployment name, target argo rollout name, target cronjob name"
    ["-r"]="Target replica name"
    ["-p"]="Target pod name(s), support multiple pod names splitting by ','"
    ["-c"]="Target container names(s)"
    ["-l"]="Target label selector, support multiple label selectors splitting by ','"
    ["-w"]="Target worker node name"
    ["-j"]="Additional args provided in JSON format"
    ["--bashCmd"]="Target bashcmd that requires to be executed in the pod"
    ["--logOptions"]="Pass the available kubectl log options, i.e. --tail=100, --since=1h"
    ["--requestId"]="RequestId to track this self healing action"
    ["--size"]="Target replica size to scale up/down"
    ["--minSize"]="Target min size to auto scale settting"
    ["--maxSize"]="Target max size to auto scale settting"
    ["--imageTag"]="New image tag to update"
    ["--failed-only"]="Only show failed pods (not in Running or Succeeded state)"
    ["--alertName"]="Name of the Prometheus alert that triggered this script"
)

ExecCmd() {
    local cmd=$@
    echo "===================================================="
    echo "Target cmd:" $cmd
    bash -c "$cmd"
    echo "===================================================="
}

PrintSeperator() {
    echo "===================================================="
}

PrintHelpInfo() {
    local overview=$1
    local author=$2
    echo -e "Overview:"
    echo -e "\tThis script is used to $overview"
    echo -e "\tMaintained by $author"
}

PrintJsonHelpInfo() {
    local overview=$1
    local author=$2
    local sample=$3
    shift 3  # Remove first three arguments
    local supported_opts=("$@")

    # Construct the full overview text and syntax
    local full_overview="Script is used to $overview"
    local syntax=""
    local first=true

    # Build syntax string
    for opt in "${supported_opts[@]}"; do
        if [ "$first" = true ]; then
            syntax="$0 [$opt"
            first=false
        else
            syntax+="|$opt"
        fi
    done
    syntax+="]"
    
    # Build script options object
    local options_json="{}"
    for opt in "${supported_opts[@]}"; do
        if [[ -n "${OPTION_DESCRIPTIONS[$opt]}" ]]; then
            options_json=$(echo "$options_json" | jq --arg key "$opt" --arg desc "${OPTION_DESCRIPTIONS[$opt]}" '. + {($key): $desc}')
        fi
    done

    # Build final JSON
    JSON_STRING=$(jq -n \
        --arg overview "$full_overview" \
        --arg author "$author" \
        --arg syntax "$syntax" \
        --arg sample "$sample" \
        --argjson options "$options_json" \
        '{
            "overview": $overview,
            "author": $author,
            "syntax": $syntax,
            "script_options": $options,
            "sample": $sample
        }'
    )
    echo "$JSON_STRING"
}

PrintCommonHelpInfo() {
    local supported_opts=("$@")
    local syntax_opts=""
    local first=true

    # Build syntax options string
    for opt in "${supported_opts[@]}"; do
        if [ "$first" = true ]; then
            syntax_opts="$opt"
            first=false
        else
            syntax_opts+="|$opt"
        fi
    done

    echo -e "Syntax: $0 [$syntax_opts]"
    echo -e "Options:"

    # Show only script options
    for opt in "${supported_opts[@]}"; do
        if [[ -n "${OPTION_DESCRIPTIONS[$opt]}" ]]; then
            echo -e "\t$opt    ${OPTION_DESCRIPTIONS[$opt]}"
        fi
    done
}

PrintAdditionalInfo() {
    local title=$1
    local info=$2
    echo -e "$title:"
    echo -e "\t$info"
}

ParseArgs() {
    local OPTIND
    # Use associated array to hold all args
    
    # getopts format explanation:
    # - Each letter represents a valid option (e.g., n,d,r,p,l,w,c,j,h)
    # - A colon ':' after a letter means that option requires an argument
    # - No colon means the option is a flag (doesn't need argument)
    # - A single '-' at the end allows parsing long options (--name)
    # - A colon after '-' means long options require arguments
    # Example: "n:d:" means -n and -d both require arguments
    while getopts "n:d:r:p:l:w:c:j:h-:" option; do
        case $option in
        n)
            if [ -z "${inputArgs[namespace]}" ]; then
                inputArgs[namespace]="$OPTARG"
            else
                if [ "${inputArgs[namespace]}" != "$OPTARG" ]; then
                    echo "Multiple namespaces are not supported. Please provide only one namespace"
                    Help
                    exit
                fi
            fi
            ;;
        d)
            inputArgs[deployment]="$OPTARG"
            ;;
        r)
            inputArgs[replica]="$OPTARG"
            ;;
        p)
            inputArgs[pods]="$OPTARG"
            ;;
        l)
            inputArgs[labels]="$OPTARG"
            ;;
        w)
            inputArgs[workerNodeName]="$OPTARG"
            ;;
        c)
            inputArgs[container]="$OPTARG"
            ;;
        j)
            inputArgs[jsonArgs]="$OPTARG"
            ;;
        -)
            case "${OPTARG}" in
                "requestId")
                    inputArgs[requestId]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "bashCmd")
                    inputArgs[bashCmd]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "logOptions")
                    inputArgs[logOptions]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "size")
                    inputArgs[size]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "minSize")
                    inputArgs[minSize]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "maxSize")
                    inputArgs[maxSize]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "imageTag")
                    inputArgs[imageTag]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "alertName")
                    inputArgs[alertName]="${!OPTIND}"
                    OPTIND=$((OPTIND + 1))
                    ;;
                "json-help")
                    # Handle JSON help output
                    inputArgs[json_help]=true
                    Help
                    exit
                    ;;
                "failed-only")
                    inputArgs[failed_only]=true
                    ;;
                *)
                    echo "Invalid option: --${OPTARG}" >&2
                    exit 1
                    ;;
            esac
            ;;
        h) # display Help
            Help
            exit
            ;;

        *)
            echo "Invalid option"
            Help
            exit
            ;;
        esac
    done

    echo "namespace is ${inputArgs[namespace]}"
    if [ -z "${inputArgs[namespace]}" ]; then
        echo "Namespace is required. Set it to default"
        inputArgs[namespace]="default"
    fi

}

SendEmail() {

    #prepare smtp configuration
    smtpConf="/etc/ssmtp/ssmtp.conf"
    if [ $(cat $smtpConf | grep "root=adstst" -c) -eq 0 ]; then
        templateFile="/app/sendemail.conf"
        cp $smtpConf $smtpConf".bak"
        sendemailAuth=$(cat /mnt/secrets-store-keyvault/SendEmailAuthPass)
        sed -e "s/<AUTHPASSPLACEHOLDER>/$sendemailAuth/g" $templateFile >$smtpConf
    fi

    mailTo=$1
    mailSubject=$2
    mailContent=$3
    mailContentFile="/tmp/mailcontent.txt."$(date +%s)
    echo "Sending email to $mailTo with subjet $mailSubject, content $mailContent"
    #su adstst -c "echo \"Subject: $mailSubject\" | sendmail $mailTo"
    echo "Subject: $mailSubject" >"$mailContentFile"
    echo "" >>"$mailContentFile"
    echo "$mailContent" >>"$mailContentFile"
    su adstst -c "sendmail $mailTo < $mailContentFile"
    echo "Email sent"
    rm -rf $mailContentFile
}

Submit_CronExecution_Metrics() {

    local scriptName=$1
    local message=$2
    local requestor="cron"

    local port=$METRICSPORT
    if [ -z "$port" ]; then
        port="8081"
    fi

    JSON_STRING=$(jq -n \
        --arg scriptName "$scriptName" \
        --arg requestor "$requestor" \
        --arg msg "$message" \
        '{scriptName:$scriptName, additionalInfo: { requestor:$requestor, message: $msg} }')
    #echo $JSON_STRING | jq .

    curl -X POST http://localhost:$port/SelfHealing/metrics -d "$JSON_STRING"

}


Submit_ScriptExecution_Metrics() {

    local scriptName=$1
    local message=$2
    local requestID=$3
    local requestor="script"

    local port=$METRICSPORT
    if [ -z "$port" ]; then
        port="8081"
    fi

    JSON_STRING=$(jq -n \
        --arg scriptName "$scriptName" \
        --arg requestor "$requestor" \
        --arg msg "$message" \
        --arg requestId "$requestId" \
        '{scriptName:$scriptName, additionalInfo: { message: $msg, requestId: $requestId } }')
    #echo $JSON_STRING | jq .

    curl -X POST http://localhost:$port/SelfHealing/metrics -d "$JSON_STRING"
}


Submit_ScriptExecution_AuditLogs_OnDemand() {

    local scriptName=$1  # must have 
    local message=$2
    local requestID=$3
    local scriptArgs=$4
    local requestor="script"

    local port=$METRICSPORT
    if [ -z "$port" ]; then
        port="8081"
    fi

    JSON_STRING=$(jq -n \
        --arg scriptName "$scriptName" \
        --arg requestor "$requestor" \
        --arg msg "$message" \
        --arg requestId "$requestId" \
        --arg scriptArgs "$scriptArgs" \
        '{scriptName:$scriptName, message: $msg, requestId: $requestId, scriptArgs: $scriptArgs}')
    #echo $JSON_STRING | jq .

    curl -X POST http://localhost:$port/SelfHealing/auditLogs -d "$JSON_STRING"

}


Submit_AlertExecution_AuditLogs() {

    local scriptName=$1  # must have
    local alertName=$2   # must have
    local message=$3
    local requestID=$4
    local scriptArgs=$5
    local requestor="prometheus"

    local port=$METRICSPORT
    if [ -z "$port" ]; then
        port="8081"
    fi

    JSON_STRING=$(jq -n \
        --arg scriptName "$scriptName" \
        --arg alertName "$alertName" \
        --arg requestor "$requestor" \
        --arg msg "$message" \
        --arg requestId "$requestId" \
        --arg scriptArgs "$scriptArgs" \
        '{scriptName:$scriptName, alertName: $alertName, message: $msg, requestId: $requestId, scriptArgs: $scriptArgs, requestor: $requestor}')
    #echo $JSON_STRING | jq .

    curl -X POST http://localhost:$port/SelfHealing/auditLogs -d "$JSON_STRING"

}


GetPodResourceUsageGrafanaDashboard() {
    local namespace=$1
    local podname=$2
    
    #need to build grafana url like below format
    #https://prod-ldc1.grafana.ads.trafficmanager.net/d/4llq4wc7f/mt_resource_metric?orgId=1&var-namespace=default&var-pod=odata-bbfcddff9-6lz6f&from=1669383368899&to=1669384756851

    #load from env setting
    grafanaDashboardURL=$GRAFANA_DASHBOARD_URL
    if [ -z "$grafanaDashboardURL" ]; then
        grafanaHostname=$INGRESSHOST
        if [ -z "$grafanaHostname" ]; then
            return
        fi
        grafanaDomain="grafana.ads.trafficmanager.net"
        grafanaDashboardId="d/4llq4wc7f/mt_resource_metric"
        grafanaDashboardURL="https://"$grafanaHostname.$grafanaDomain/$grafanaDashboardId
    fi
    startTime=$(date --date="-2 hour" +%s)"000"
    endTime=$(date --date="+1 hour" +%s)"000"
    applicationName=$(echo $podname | cut -d'-' -f1)

    url=$grafanaDashboardURL"?orgId=1&var-namespace="$namespace"&var-application=$applicationName&var-pod="$podname"&from="$startTime"&to="$endTime
    echo -e "\nPlease check the resource usage of $podname with grafana dashboard:"
    echo $url
    echo
}

GetCoreDumpFileFromStorageAccount(){
    local namespace=$1
    storageClassName=$(kubectl get pvc adhocfileshare -n $namespace -ojsonpath='{.spec.storageClassName}')
    storageAccountName=$(kubectl get storageclass $storageClassName -n $namespace -ojsonpath='{.parameters.storageAccount}')
    fileShareName=$(kubectl get storageclass $storageClassName -n $namespace -ojsonpath='{.parameters.shareName}')
    storageAccountURL="https://portal.azure.com/#view/HubsExtension/BrowseResource/resourceType/Microsoft.Storage%2FStorageAccounts"

    echo "Please visit \"$storageAccountURL\" -> Select \"$storageAccountName\" -> File Share -> \"$fileShareName\" -> Browse -> \"auto-coredump\" to download the core dump file"
    echo "Note: JIT token is required to access storage account contents from Azure Portal"
    echo
    echo "To analysis the dump, please refer to https://eng.ms/docs/experiences-devices/webxt/microsoft-ads/bingadsplatform/campaignplatform/campaign-platform-tsg-docs/campaignmt/aks/dri-procedures/monitoring-aks-cpu-and-memory-usage/take-a-dump-perf-trace-for-a-pod"
}

GetCoreDumpFileURL() {
    local namespace=$1
    local podname=$2

    #Note: below is the magic word to check whether dump is generated in kusto dashboard.  Please not change it.
    echo "Auto-coredump file has been generated within /adhoc fileshare"
    kubectl -n $namespace exec $podname -- /bin/bash -c "ls -l /mnt/adhoc/auto-coredump/core_$podname*"

    GetCoreDumpFileFromStorageAccount $namespace
}

GetAzureProfileHelper() {
    echo "Triggered AzureProfiling. Please view profiling result with http://azprofilerclko"
    echo "Please refer to TSG https://eng.ms/docs/experiences-devices/webxt/microsoft-ads/bingadsplatform/campaignplatform/campaign-platform-tsg-docs/campaignmt/aks/dri-procedures/monitoring-aks-cpu-and-memory-usage/azure-profiler-in-aks"
    echo "Note: Profiling will take some time to complete and upload the result."
}