namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors
{
    using AppMetaDataSync;
    using MapOffersTreeNodesService.Implementation;
    using MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask;
    using MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors.Ad;
    using MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors.RSACustomizer;
    using MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors.Video;
    using Microsoft.Advertising.ServiceLocation;
    using PrivacyCheck;
    using SharedLibrary;
    using StockPhoto;
    using System;
    using System.Collections.Generic;

    internal class JobProcessorFactory
    {
        private static IDictionary<OperationType, IShardEntityJobProcessor> processorMap =
            new Dictionary<OperationType, IShardEntityJobProcessor>()
            {
                {OperationType.Migration, new MigrationJobProcessor()},
                {OperationType.AdLimit, new EntityCountJobProcessor(EntityCountType.AdLimit)},
                {OperationType.KeywordLimit, new EntityCountJobProcessor(EntityCountType.KeywordLimit)},
                {OperationType.OrderLimit, new EntityCountJobProcessor(EntityCountType.AdGroupLimit)},
                {OperationType.OrderAudienceLimit, new EntityCountJobProcessor(EntityCountType.AdGroupAudienceLimit)},
                {OperationType.AllEntityLimit, new EntityCountJobProcessor(EntityCountType.AllLimits)},
                {OperationType.NegativeKeywordLimit, new EntityCountJobProcessor(EntityCountType.NegativeKeywordLimit)},
                {OperationType.OrderDelete, new OrderDeleteJobProcessor()},
                {OperationType.OpcCampaignId, new OpcCampaignJobProcessor()},
                {OperationType.OpcOrderId, new OpcUpdateJobProcessor()},
                {OperationType.AdExOrderEditorialStatus, new AdExtensionOrderJobProcessor()},
                {OperationType.CampaignDelete, new CampaignDeleteProcessor()},
                {OperationType.CampaignActive, new CampaignActiveProcessor()},
                {OperationType.LabelDelete, new LabelDeleteProcessor()},
                {OperationType.LabelIdLevelRecount, new LabelLevelRecountProcessor()},
                {OperationType.AccountLevelLabelRecount, new AccountLevelLabelRecountProcessor()},
                {OperationType.AccountDeleteForPurge,new AccountDeleteForPurgeJobProcessor()},
                {OperationType.AccountLevelLabelsPerEntityRecount, new AccountLevelLabelsPerEntityRecountProcessor()},
                {OperationType.LabelIdLevelLabelsPerEntityRecount, new LabelLevelLabelsPerEntityRecountProcessor()},
                {OperationType.OrderEditorialStatusQueueProcessor, new OrderEditorialQueueJobProcessor() },
                {OperationType.AdexOrderEditorialExpending, new AdexOrderEditorialExpendingProcessor() },
                {OperationType.AimOrderLevelPrivacyCheck, new OrderLevelPrivacyCheckProcessor() },
                {OperationType.AudienceIdLevelRecount, new AudienceLevelRecountProcessor() },
                {OperationType.AccountLevelAudienceRecount, new AccountLevelAudienceRecountProcessor() },
                {OperationType.AudienceDelete, new AudienceDeleteProcessor() },
                {OperationType.CampaignLevelOrderTargetSizeRecount, new CampaignLevelOrderTargetSizeRecountJobProcessor() },
                {OperationType.PauseSharedLibraryEntityAssociations, new PauseSharedLibraryEntityAssociationsProcessor(PauseEntityType.ShareLibraryEntity) },
                {OperationType.PauseAudienceAssociationForAccount, new PauseSharedLibraryEntityAssociationsProcessor(PauseEntityType.Audience) },
                {OperationType.PauseGoalForAccount, new PauseSharedLibraryEntityAssociationsProcessor(PauseEntityType.Goal) },
                {OperationType.TagUsedByAccountGoalRecount, new TagLevelUsedByGoalRecountProcessor(EntityUsedByType.UsedbyAccountForUETTagAssociateToGoalOperation) },
                {OperationType.TagUsedByCustomerGoalRecount, new TagLevelUsedByGoalRecountProcessor(EntityUsedByType.UsedbyCustomerForUETTagAssociateToGoalOperation) },
                {OperationType.TagUsedByAccountRemarketingListRecount, new TagLevelUsedByAudienceRecountProcessor(EntityUsedByType.UsedbyAccountForUETTagAssociateToRemarketingListOperation) },
                {OperationType.TagUsedByCustomerRemarketingListRecount, new TagLevelUsedByAudienceRecountProcessor(EntityUsedByType.UsedbyCustomerForUETTagAssociateToRemarketingListOperation) },
                {OperationType.TagUsedByAccountProductAudienceRecount, new TagLevelUsedByAudienceRecountProcessor(EntityUsedByType.UsedbyAccountForUETTagAssociateToProductAudienceOperation) },
                {OperationType.TagUsedByCustomerProductAudienceRecount, new TagLevelUsedByAudienceRecountProcessor(EntityUsedByType.UsedbyCustomerForUETTagAssociateToProductAudienceOperation) },
                {OperationType.AudienceLevelUsedbyAdGroupRecount, new AudienceLevelUsedByRecountProcessor(EntityUsedByType.AudienceLevelUsedbyRecountforAudienceAssociateToAdGroup) },
                {OperationType.AccountLevelAudienceUsedbyAdGroupRecount, new AccountLevelAudienceUsedByRecountProcessor(EntityUsedByType.AccountLevelUsedbyRecountforAudienceAssociateToAdGroup) },
                {OperationType.AudienceLevelCampaignAudienceAssociationRecount, new AudienceLevelCampaignAudienceAssociationRecountProcessor()},
                {OperationType.AccountLevelCampaignAudienceAssociationRecount, new AccountLevelCampaignAudienceAssociationRecountProcessor() },
                {OperationType.AdsByBingAutoApply, ServiceLocator.Current.Resolve<AdsByBingAutoApply.AdsByBingAutoApplyProcessor>() },
                {OperationType.AimCampaignLevelPrivacyCheck, new CampaignLevelPrivacyCheckProcessor() },
                {OperationType.SharedEntityUsedByRecount, new SharedEntityUsedByRecountProcessor() },
                {OperationType.AccoutImageReLicense, new AccountImageRelicenseProcessor() },
                {OperationType.LicenseStockImage, new LicenseStockImageProcessor() },
                {OperationType.StockImageRekey, new StockImageRekeyProcessor() },
                {OperationType.NegativeKeywordCatalogMigration, new NegativeKeywordCatalogMigration() },
                {OperationType.AdExEditorialStatusByAccount, new AccountLevelAdExtensionEditorialProcessor() },
                {OperationType.AppextensionsMetaDataSync, new AppextensionsMetaDataSyncProcessor() },
                {OperationType.AppInstallAdsMetaDataSync, new AppInstallAdsMetaDataSyncProcessor() },
                {OperationType.CustomerSharedEntityInvalidAssociationCleanup, ServiceLocator.Current.Resolve<CustomerSharedEntity.CustomerSharedEntityInvalidAssociationCleanupProcessor>() },
                {OperationType.AdExEditorialStatusByCampaign, new CampaignLevelAdExtensionEditorialProcessor() },
                {OperationType.PublishSmartPage, new PublishSmartPageProcessor() },
                {OperationType.PublishSmartPagePreview, new PublishSmartPagePreviewProcessor() },
                {OperationType.CreateOrUpdateDraftCampaignForSmartPage, new CreateOrUpdateDraftCampaignForSmartPageProcessor() },
                {OperationType.SmartPageLeadsTracking, new SmartPageLeadsTrackingProcessor() },
                {OperationType.SetM365FlagForSmartPage, new SetM365FlagForSmartPageProcessor() },
                {OperationType.VideoAdaptiveStreamingTranscode, new VideoAdaptiveStreamingTranscodeProcessor() },
                {OperationType.PauseAdGroupAudienceAssociationForEURestriction, new PauseAdGroupAudienceAssociationForEURestrictionProcessor() },
                {OperationType.PauseCampaignAudienceAssociationForEURestriction, new PauseCampaignAudienceAssociationForEURestrictionProcessor() },
                {OperationType.VideoDeleteCleanup, new VideoDeleteCleanupProcessor() },
                {OperationType.AdImpressionTrackingUrl, new AdImpressionTrackingUrlProcessor() },
                {OperationType.FreeUpSmartPageSubdomain, new FreeUpSmartPageSubdomainProcessor() },
                {OperationType.AdExtensionOfflineProcessing, new AdExtensionOfflineProcessor() },
                {OperationType.StaToExtaAutoApply, ServiceLocator.Current.Resolve<StaToExtaAutoApplyProcessor.StaToExtaAutoApplyProcessor>()},
                {OperationType.MarkSmartPageEditorialRejected, new MarkSmartPageEditorialRejectedProcessor() },
                {OperationType.VideoDownload, new VideoDownloadProcessor()},
                {OperationType.LabelMccDelete, new LabelMccDeleteProcessor() },
                {OperationType.LabelMccEntityLevelDelete, new LabelMccEntityLevelDeleteProcessor() },
                {OperationType.LabelMccIdLevelRecount, new LabelMccIdLevelRecountProcessor() },
                {OperationType.AccountLevelLabelMccRecount, new AccountLevelLabelMccRecountProcessor() },
                {OperationType.LabelMccIdLevelLabelsPerEntityRecount, new LabelMccIdLevelLabelsPerEntityRecountProcessor()},
                {OperationType.DSAAutoTargetLimit, new EntityCountJobProcessor(EntityCountType.DSAAutoTargetLimit) },
                {OperationType.MultiMediaAdsAutoApply, ServiceLocator.Current.Resolve<MultiMediaAdsAutoApplyProcessor.MultiMediaAdsAutoApplyProcessor>() },
                {OperationType.ResponsiveSearchAdsOptInAutoApplyAutoGenAsset, ServiceLocator.Current.Resolve<ResponsiveSearchAdsOptInAutoApplyAutoGenAssetProcessor.ResponsiveSearchAdsOptInAutoApplyAutoGenAssetProcessor>() },
                {OperationType.AdOfflineValidation, new AdOfflineValidationProcessor() },
                {OperationType.FeedSize, new FeedSizeProcessor() },
                {OperationType.FeedStatusSync, new FeedStatusSyncProcessor() },
                {OperationType.MultiMediaAdsToTextAssets, ServiceLocator.Current.Resolve<MultiMediaAdsToTextAssetsProcessor.MultiMediaAdsToTextAssetsProcessor>() },
                {OperationType.RSACustomizereOfflineDelete, ServiceLocator.Current.Resolve<RSACustomizerOfflineDeleteProcessor>() },            
                {OperationType.NewStockImageRekey, new NewStockImageRekeyProcessor() },
                {OperationType.FreeUpSmartPageCustomDomain, new FreeUpSmartPageCustomDomainProcessor() },
                {OperationType.TextAssetCountsAndLimitCheck, ServiceLocator.Current.Resolve<AccountSizeProcessor>() },
                {OperationType.TextAssetBlobRefresh, ServiceLocator.Current.Resolve<TextAssetBlobRefreshProcessor>() },
                {OperationType.ImageBulkCropping, ServiceLocator.Current.Resolve<ImageBulkCropProcessor>() },
                {OperationType.MigrateMMAUnderDSAAdGroup, new MMAForDSAMigrationProcessor() },
                {OperationType.AccountPilotMigrationProcessor, new AccountPilotMigrationProcessor() },
                {OperationType.SmartPageCustomDomainDnsSetup, new SmartPageCustomDomainDnsSetupProcessor() },
                {OperationType.SmartPageUpdateESCUrl, new SmartPageUpdateESCUrlProcessor() },
                {OperationType.SmartPageCustomDomainPostUpdate, new SmartPageCustomDomainPostUpdateProcessor() },
                {OperationType.EnablePredictiveTargeting, new PredictiveTargetingKeywordProcessor() },
                {OperationType.DisablePredictiveTargeting, new PredictiveTargetingKeywordProcessor() },
                {OperationType.SmartPageCustomDomainRefresh, new SmartPageCustomDomainRefreshProcessor() },
                {OperationType.AssetGroupEditorialRollup, new AssetGroupEditorialRollupJobProcessor() },
                {OperationType.AssetGroupAssetEditorialStatus, new AssetGroupAssetEditorialStatusJobProcessor() },
                {OperationType.SmartShoppingToPerformanceMaxUpgrade, new SmartShoppingToPerformanceMaxProcessor() },
                {OperationType.BingPlacesCreateOrClaimBusinessListing, new BingPlacesCreateOrClaimBusinessListingProcessor() },
                {OperationType.AccountLevelImageAnnotation, ServiceLocator.Current.Resolve<AccountLevelImageAnnotationProcessor>() },
                {OperationType.AssetLevelImageAnnotation, new AssetLevelImageAnnotationProcessor() },
                {OperationType.AudienceAdSmartCropping, new AudienceAdSmartCroppingProcessor()},
                {OperationType.PauseAllMcaCampaigns, new PauseAllMcaCampaignsProcessor()},
                {OperationType.ESCAccountMigration, new ESCAccountMigrationProcessor()},
                {OperationType.TextAdAssetAutoGeneration, new TextAdAssetAutoGenerationProcessor()},
                {OperationType.VideoDataBackfill, new VideoDataBackfillProcessor()},
                {OperationType.DSAToPMaxIDMappingProcessor, new DSAToPMaxIDMappingProcessor() },
                {OperationType.DSAToPMaxMigrationProcessor, new DSAToPMaxMigrationProcessor() },
                {OperationType.SSOBlockingMigration, new SSOBlockingMigrationProcessor()},
                {OperationType.AdVideoAssetsMetadataJsonFill, new AdVideoAssetsMetadataJsonFillProcessor() },
                {OperationType.CroppingTypeForImageAdExtensionBackfill, ServiceLocator.Current.Resolve<ImageAdExtensionCroppingTypeBackfillProcessor>() },
                {OperationType.UpdateAIGCSearchIndexProcessor, new UpdateAIGCSearchIndexProcessor() },
                {OperationType.LogoAdExtensionAutoGenerationProcessor, new LogoAdExtensionAutoGenerationTaskProcessor() },
                {OperationType.AdOfflineAdd, new AdOfflineAddProcessor() },
                {OperationType.UpdateExternalCampaignIdMapDetailByTaskStatus, new UpdateExternalCampaignIdMapDetailByTaskStatusProcessor() },
                {OperationType.AssetGroupVideoAssetMetadataJsonFill, new AssetGroupVideoAssetsMetadataFillProcessor() },
                {OperationType.CCPilotPropagation, new CCPilotPropagationProcessor() },
                {OperationType.VideoClipchampConversionProcessor, new VideoClipchampConversionProcessor() },
                {OperationType.AccountsActiveProcessor, new AccountsActiveProcessor() }
            };

        private static IDictionary<OperationType, bool> processorMapForPreValidation =
            new Dictionary<OperationType, bool>() {
                { OperationType.AdExtensionOfflineProcessing, true },
            };

        public static IShardEntityJobProcessor GetJobProcessor(OperationType opType)
        {
            IShardEntityJobProcessor processor;
            if (!processorMap.TryGetValue(opType, out processor))
            {
                throw new NotImplementedException("No processor is implemented for the operation type:" + opType);
            }
            return processor;
        }
        
        public static bool ShouldPreValidateForAccounts(OperationType opType)
        {
            if (processorMapForPreValidation.TryGetValue(opType, out var shouldPreValidate)) {
                return shouldPreValidate;
            }

            return false;
        }
    }
}
