using ClickHouse.Client.ADO;
using Microsoft.Data.SqlClient;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Interface for providing SQL queries used in metadata generation.
    /// This abstraction allows for easier testing and configuration management.
    /// </summary>
    public interface ISqlQueryProvider
    {
        /// <summary>
        /// Gets the query to retrieve all ClickHouse BI data columns.
        /// </summary>
        string GetAllCHBiDataColumnsQuery();

        /// <summary>
        /// Gets the query to retrieve BI data groups information.
        /// </summary>
        string GetBiDataGroupsQuery();

        /// <summary>
        /// Gets the query to retrieve query metadata information.
        /// </summary>
        string GetQueryMetadataQuery();

        /// <summary>
        /// Gets the query to retrieve column default values.
        /// </summary>
        string GetColumnDefaultValuesQuery();

        /// <summary>
        /// Gets the query to retrieve dimension table information.
        /// </summary>
        string GetDimensionTablesQuery();

        /// <summary>
        /// Gets the query to retrieve fact table information.
        /// </summary>
        string GetFactTablesQuery();

        /// <summary>
        /// Creates a SQL connection using the configured connection string.
        /// </summary>
        SqlConnection CreateSqlConnection();
    }

    /// <summary>
    /// Interface for providing ClickHouse queries used in metadata generation.
    /// </summary>
    public interface IClickHouseQueryProvider
    {
        /// <summary>
        /// Gets the query to retrieve ClickHouse table schema information.
        /// </summary>
        string GetTableSchemaQuery();

        /// <summary>
        /// Gets the query to retrieve ClickHouse column information.
        /// </summary>
        string GetColumnInfoQuery();

        /// <summary>
        /// Creates a ClickHouse connection using the configured connection string.
        /// </summary>
        ClickHouseConnection CreateClickHouseConnection();
    }

    /// <summary>
    /// Default implementation of ISqlQueryProvider with standard SQL queries.
    /// </summary>
    public class DefaultSqlQueryProvider : ISqlQueryProvider
    {
        private readonly string _connectionString;

        public DefaultSqlQueryProvider(string? connectionString = null)
        {
            _connectionString = connectionString ?? GetDefaultConnectionString();
        }

        public string GetAllCHBiDataColumnsQuery()
        {
            return @"
                SELECT DISTINCT 
                    LOWER(COLUMN_NAME) as ColumnName,
                    COLUMN_NAME as OriginalColumnName
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'dbo' 
                    AND TABLE_NAME LIKE '%Fact%'
                ORDER BY COLUMN_NAME";
        }

        public string GetBiDataGroupsQuery()
        {
            return @"
                SELECT 
                    bg.CategoryName,
                    bg.TableName,
                    bg.StreamingEnabled,
                    bg.HasConversionData,
                    bg.ConversionTableName,
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    c.IS_NULLABLE
                FROM BiDataGroups bg
                INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON c.TABLE_NAME = bg.TableName
                WHERE c.TABLE_SCHEMA = 'dbo'
                ORDER BY bg.CategoryName, c.ORDINAL_POSITION";
        }

        public string GetQueryMetadataQuery()
        {
            return @"
                SELECT 
                    qm.QueryType,
                    qm.BiDataCategories,
                    qm.QueryParameters,
                    rc.ColumnName,
                    rc.JoinTableAlias,
                    rc.ColumnExpression,
                    rc.DependentColumnName
                FROM QueryMetadata qm
                LEFT JOIN RequestColumns rc ON qm.QueryType = rc.QueryType
                ORDER BY qm.QueryType, rc.ColumnName";
        }

        public string GetColumnDefaultValuesQuery()
        {
            return @"
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'dbo' 
                    AND COLUMN_DEFAULT IS NOT NULL
                    AND TABLE_NAME LIKE '%Fact%'
                ORDER BY TABLE_NAME, COLUMN_NAME";
        }

        public string GetDimensionTablesQuery()
        {
            return @"
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'dbo' 
                    AND TABLE_NAME LIKE '%Dim'
                ORDER BY TABLE_NAME, ORDINAL_POSITION";
        }

        public string GetFactTablesQuery()
        {
            return @"
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'dbo' 
                    AND TABLE_NAME LIKE '%Fact%'
                ORDER BY TABLE_NAME, ORDINAL_POSITION";
        }

        public SqlConnection CreateSqlConnection()
        {
            return new SqlConnection(_connectionString);
        }

        private static string GetDefaultConnectionString()
        {
            // Default connection string - should be configurable in production
            return "Server=localhost;Database=AdvertiserBI;Integrated Security=true;";
        }
    }

    /// <summary>
    /// Default implementation of IClickHouseQueryProvider with standard ClickHouse queries.
    /// </summary>
    public class DefaultClickHouseQueryProvider : IClickHouseQueryProvider
    {
        private readonly string _connectionString;

        public DefaultClickHouseQueryProvider(string? connectionString = null)
        {
            _connectionString = connectionString ?? GetDefaultConnectionString();
        }

        public string GetTableSchemaQuery()
        {
            return @"
                SELECT 
                    database,
                    table,
                    name as column_name,
                    type as data_type
                FROM system.columns 
                WHERE database = 'advertiserbi'
                ORDER BY database, table, position";
        }

        public string GetColumnInfoQuery()
        {
            return @"
                SELECT 
                    table,
                    name,
                    type,
                    default_expression
                FROM system.columns 
                WHERE database = 'advertiserbi'
                    AND table LIKE '%Fact%'
                ORDER BY table, position";
        }

        public ClickHouseConnection CreateClickHouseConnection()
        {
            return new ClickHouseConnection(_connectionString);
        }

        private static string GetDefaultConnectionString()
        {
            // Default ClickHouse connection string - should be configurable in production
            return "Host=localhost;Port=9000;Database=advertiserbi;";
        }
    }
}
