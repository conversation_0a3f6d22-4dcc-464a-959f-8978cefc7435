package webserver

// Prometheus' adapter
// Adapter parses the description of the warning and extracts the parameters required for execution
import (
	"bytes"
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"
)

func prometheusAdapter(ctx *gin.Context) ([]bytes.Buffer, error) {
	argStr, err := ctx.GetRawData()
	if err != nil {
		err = fmt.Errorf("GetRawData Failed:%s!prometheusData: %s", err.Error(), string(argStr[:]))
		return nil, err
	}
	type AlertLabel struct {
		Alertname  string `json:"alertname" binding:"required"`
		Namespace  string `json:"namespace" binding:"required"`
		Node 	   string `json:"node" binding:"required"`
		Pod        string `json:"pod" binding:"required"`
		Controller string `json:"controller" binding:"required"`
	}
	type PrometheusAlertItem struct {
		Label AlertLabel `json:"labels" binding:"required"`
	}
	type PrometheusAlert struct {
		Alerts []PrometheusAlertItem `json:"alerts" binding:"required"`
	}
	var prometheusJsonData PrometheusAlert
	if err := json.Unmarshal(argStr, &prometheusJsonData); err != nil {
		err = fmt.Errorf("wrong Args:%s,prometheusData: %s", err.Error(), string(argStr[:]))
		return nil, err
	}
	var buffers []bytes.Buffer
	for _, item := range prometheusJsonData.Alerts {
		if item.Label.Alertname == "" {
			continue
		}
		args := ""
		if item.Label.Namespace != "" {
			args += " -n " + item.Label.Namespace
		}
		if item.Label.Pod != "" {
			args += " -p " + item.Label.Pod
		}
		if item.Label.Node != "" {
			args += " -w " + item.Label.Node
		}
		if item.Label.Controller != "" {
			args += " -d " + item.Label.Controller
		}
		if item.Label.Alertname != "" {
			args += " --alertName " + item.Label.Alertname
		}
		alert := alertData{
			AlertName: item.Label.Alertname,
			Args:      args,
			UserId:    "prometheus",
		}
		var buf bytes.Buffer
		err := json.NewEncoder(&buf).Encode(alert)
		if err != nil {
			err = fmt.Errorf("convert struct to json bytes failed:%s,structData: %s", err.Error(), alert)
			return nil, err
		} else {
			buffers = append(buffers, buf)
		}
	}
	return buffers, nil
}
