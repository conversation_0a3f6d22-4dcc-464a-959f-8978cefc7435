﻿using CampaignMiddleTierTest.Framework;
using CampaignMiddleTierTest.Framework.Utilities;
using CampaignTest.ApiFunctionalTests;
using CampaignTest.ApiFunctionalTests.Collections;
using Microsoft.BingAds.CampaignManagement;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using FieldNames = CampaignTest.Framework.FieldNames;
using ResponseValidator = CampaignTest.ApiFunctionalTests.Validators.ResponseValidator;
using ApiFactories = CampaignTest.ApiFunctionalTests.Factories;

namespace Microsoft.Advertising.Advertiser.APIV13.Goal
{
    [TestClass]
    public class GetConversionGoalsByIds : CampaignTestBase
    {
        private static CustomerInfo cInfo = GoalsCustomer;
        private static CustomerInfo ViewThroughConversionsCustomerInfo = ViewThroughConversionPilotCustomer;
        private static CustomerInfo MainGoalConversionsCustomerInfo = MainConversionGoalPilotCustomer;
        private static CustomerInfo ThirdPartyConversionCustomerInfo = ThirdPartyConversionPilotCustomer;
        private static CustomerInfo GoalCategoryPilotCustomerInfo = GoalCategoryPilotCustomer;
        private static long tagId;
        private static long tagId_ViewThroughConversionCustomer;
        private static long tagId_MainGoalConversionCustomer;
        private static long tagId_GoalCategoryCustomer;

        [ClassInitialize]
        public static void Initialize(TestContext context)
        {
            tagId = GetUetTagId(cInfo);
            tagId_ViewThroughConversionCustomer = GetUetTagId(ViewThroughConversionsCustomerInfo);
            tagId_MainGoalConversionCustomer = GetUetTagId(MainGoalConversionsCustomerInfo);
            tagId_GoalCategoryCustomer = GetUetTagId(GoalCategoryPilotCustomerInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
            goalsCollection.AreEqualInSequence(getGoals);
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.IsNull(realValue, "VTC doesn't match");
        }

        [TestMethod, Priority(2)]
        public void GetConversionGoals_EnhancedConversions_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions, Features.InStoreTransaction });
            long tagId = GetUetTagId(customer);

            dynamic goal = ConversionGoalCollection.CreateDestinationGoal(tagId);
            goal.IsEnhancedConversionsEnabled = true;
            var response = ConversionGoalCollection.PostGoal(cInfo: customer, goal: goal);
            long goalId = response.value;

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            response = goalsCollection.Get_Success(goalIds: null, customer);
            Assert.IsNull(response[0].IsEnhancedConversionsEnabled);

           // Get the goal and verify that enhanced conversions is enabled
            goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            response = goalsCollection.Get_Success(goalIds: null, customer, ConversionGoalAdditionalField.IsEnhancedConversionsEnabled);
            Assert.IsTrue(response[0].IsEnhancedConversionsEnabled);
        }
        
        [TestMethod, Priority(2)]
        public void GetConversionGoals_EnhancedConversions_Not_Piloted_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.InStoreTransaction });
            long tagId = GetUetTagId(customer);

            // Create a goal with enhanced conversions enabled through UI
            dynamic goal = ConversionGoalCollection.CreateDestinationGoal(tagId);
            var response = ConversionGoalCollection.PostGoal(cInfo: customer, goal: goal);
            long goalId = response.value;

            // Get the goal and verify that enhanced conversions is enabled
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId);
            response = goalsCollection.Get_Success(goalIds: new long[] { goalId }, customer, ConversionGoalAdditionalField.IsEnhancedConversionsEnabled);
            Assert.IsFalse(response[0].IsEnhancedConversionsEnabled);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ViewThroughConversionsPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Add_RewriteId(ViewThroughConversionsCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ViewThroughConversionsCustomerInfo, ConversionGoalAdditionalField.ViewThroughConversionWindowInMinutes);
            var expectedValue = goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes;
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.AreEqual(expectedValue, realValue, "VTC doesn't match");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ConversionGoalSelectionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_MainGoalConversionCustomer);
            goalsCollection.Goals[0].ExcludeFromBidding = true;
            goalsCollection.Add_RewriteId(MainGoalConversionsCustomerInfo);
            List<object> getGoals =
                goalsCollection.Get_Success(goalsCollection.GetGoalIds(), MainGoalConversionsCustomerInfo);
            var expectedValue = goalsCollection.Goals[0].ExcludeFromBidding;
            var vtcProperty = getGoals[0].GetType().GetProperty("ExcludeFromBidding");
            var realValue = (bool?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.AreEqual(expectedValue, realValue, "ExcludeFromBidding doesn't match");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ViewThroughConversionsPilotEnabled_Success_NoAdditionalFields()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Add_RewriteId(ViewThroughConversionsCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ViewThroughConversionsCustomerInfo);
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.IsNull(realValue);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByTagsId_ViewThroughConversionsPilotEnabled_Success()
        {
            var ViewThroughConversionsCustomerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                1,
                false,
                Features.ViewThroughConversionFlagId,
                Features.EnableNativeAdsFlagId,
                Features.LinkedInTargetingForSearchBscDsa,
                Features.ConversionGoalSelectionFlagId,
                Features.DSAPilot, Features.SharedBudget,
                Features.InStoreTransaction);
            var tagId_ViewThroughConversionCustomer = GetUetTagId(ViewThroughConversionsCustomerInfo);
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Add_RewriteId(ViewThroughConversionsCustomerInfo);
            var response = goalsCollection.Get_ByTagIds(new long[] { tagId_ViewThroughConversionCustomer }, ViewThroughConversionsCustomerInfo, ConversionGoalAdditionalField.ViewThroughConversionWindowInMinutes);
            List<object> getGoals = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            var expectedValue = goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes;
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.AreEqual(expectedValue, realValue, "VTC doesn't match");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByTagsId_ViewThroughConversionsPilotEnabled_Success_NoAdditionalFields()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Duration, tagId_ViewThroughConversionCustomer);
            goalsCollection.Goals[0].ViewThroughConversionWindowInMinutes = ApiTestSetting.Factories.ApiConversionGoalFactory.Produce(
                    ApiFactories.ConversionGoalFactory.ViewThroughConversionWindowInMinutes.ValidRange).ViewThroughConversionWindowInMinutes;
            goalsCollection.Add_RewriteId(ViewThroughConversionsCustomerInfo);
            var response = goalsCollection.Get_ByTagIds(new long[] { tagId_ViewThroughConversionCustomer }, ViewThroughConversionsCustomerInfo);
            List<object> getGoals = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.IsNull(realValue);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_InvalidConversionGoalId()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            long[] Ids = new long[] { goalsCollection.Goals[0].Id.Value, 0, goalsCollection.Goals[1].Id.Value, 999999 };
            var response = goalsCollection.Get(Ids, cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.InvalidConversionGoalId);
            ResponseValidator.ValidateAPIPatialError(response, 3, ErrorCodes.InvalidConversionGoalId);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            goalsCollection.AreEqual(list.Where(x=> x != null).ToList());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_ConversionGoalTypesDoNotMatchExistingValue_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] {ConversionGoalType.Url, ConversionGoalType.Duration}, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            long[] Ids = new long[] { goalsCollection.Goals[0].Id.Value, goalsCollection.Goals[1].Id.Value };
            var response = goalsCollection.Get(Ids, ConversionGoalType.Duration, cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.ConversionGoalTypesDoNotMatchExistingValue);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            goalsCollection.GoalCollectionContains(list.Where(x => x != null).ToList());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_Success()
        {

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            List<object> getGoals = goalsCollection.Get_Success(null, cInfo);
            goalsCollection.GoalCollectionAreContained(getGoals);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_FilterByConversionGoalType_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.OfflineConversion, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            var response = goalsCollection.Get(null, ConversionGoalType.OfflineConversion, cInfo);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            goalsCollection.GoalCollectionAreContained(list.Where(x => x != null).ToList());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_InStoreTransactionPilotNotEnabled()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(null);
            var response = goalsCollection.Get(null, null);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.InStoreTransactionPilotNotEnabledForCustomer);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_DuplicateConversionGoalId()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            long[] Ids = { goalsCollection.Goals[0].Id.Value, goalsCollection.Goals[0].Id.Value };
            var response = goalsCollection.Get(Ids, cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.DuplicateConversionGoalId);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_WithTooMuchIds()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(null);
            long[] Ids = new long[101];
            var response = goalsCollection.Get(Ids.Select(x => x = 1).ToArray(), cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalIdArrayExceedsLimit);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByIds_WithoutConversionGoalTypes()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(null);
            var response = goalsCollection.Get(new long[] {1}, 0, cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalTypesNotPassed);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_Success()
        {
            UETTagCollection tagsCollection = new UETTagCollection(1);
            tagsCollection.Add_Success(cInfo);
            long tagId1 = tagsCollection.Tags[0].Id.Value;

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(2, ConversionGoalType.Url, tagId1);
            goalsCollection.Add_RewriteId(cInfo);
            List<object> getGoals = goalsCollection.Get_ByTagIds_Success(new long[] {tagId1}, cInfo);
            goalsCollection.AreEqual(getGoals);
            var vtcProperty = getGoals[0].GetType().GetProperty("ViewThroughConversionWindowInMinutes");
            var realValue = (int?)(vtcProperty.GetValue(getGoals[0], null));
            Assert.IsNull(realValue, "VTC doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_InvalidTagId_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            var response = goalsCollection.Get_ByTagIds(new long[] { 0, tagId, 1111111 }, cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 0, ErrorCodes.InvalidUetTagId);
            ResponseValidator.ValidateAPIPatialError(response, 2, ErrorCodes.InvalidUetTagId);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            goalsCollection.GoalCollectionAreContained(list);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_DuplicateTagId_BatchError()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.Url, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            var response = goalsCollection.Get_ByTagIds(new long[] { tagId, tagId }, cInfo);
            ResponseValidator.ValidateAPIPatialError(response, 1, ErrorCodes.DuplicateUetTagId);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            goalsCollection.GoalCollectionAreContained(list);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_FilterByConversionGoalType_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(new[] { ConversionGoalType.Event }, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            long id = goalsCollection.Ids[0];
            var response = goalsCollection.Get_ByTagIds(new long[] {tagId}, ConversionGoalType.Url, cInfo);
            List<object> list = ObjectParser.GetFieldValues(response, FieldNames.ConversionGoalField_ConversionGoals);
            var idList = list.Select(x => ((ConversionGoal) x).Id);
            Assert.IsFalse(idList.Contains(id));
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_WithTooMuchIds()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(null);
            long[] Ids = new long[101];
            var response = goalsCollection.Get_ByTagIds(Ids.Select(x => x = 1).ToArray(), cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.UetTagArrayExceedsLimit);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByTagIds_WithoutConversionGoalTypes()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(null);
            var response = goalsCollection.Get_ByTagIds(new long[] { 1 }, 0, cInfo);
            ResponseValidator.ValidateAPIOperationError(response, ErrorCodes.ConversionGoalTypesNotPassed);
        }
        
        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ExternalAttributionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            (goalsCollection.Goals[0] as OfflineConversionGoal).IsExternallyAttributed = true;
            goalsCollection.Add_RewriteId(ThirdPartyConversionCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ThirdPartyConversionCustomerInfo, ConversionGoalAdditionalField.IsExternallyAttributed);
            var expectedValue = ((OfflineConversionGoal)goalsCollection.Goals[0]).IsExternallyAttributed;
            var property = getGoals[0].GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoals[0], null));
            Assert.AreEqual(expectedValue, realValue, "IsExternallyAttributed doesn't match");
        }
        
        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ExternalAttributionPilotEnabled_NoAdditionalFields_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            (goalsCollection.Goals[0] as OfflineConversionGoal).IsExternallyAttributed = true;
            goalsCollection.Add_RewriteId(ThirdPartyConversionCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ThirdPartyConversionCustomerInfo);
            var property = getGoals[0].GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoals[0], null));
            Assert.IsNull(realValue, "IsExternallyAttributed doesn't match");
        }
        
        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_ByIds_ExternalAttributionPilotDisabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            List<object> getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), cInfo);
            var property = getGoals[0].GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoals[0], null));
            Assert.IsNull(realValue, "IsExternallyAttributed doesn't match");
        }        

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_ExternalAttributionPilotEnabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            (goalsCollection.Goals[0] as OfflineConversionGoal).IsExternallyAttributed = true;
            goalsCollection.Add_RewriteId(ThirdPartyConversionCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(null, ConversionGoalType.OfflineConversion, ThirdPartyConversionCustomerInfo, ConversionGoalAdditionalField.IsExternallyAttributed);
            var expectedValue = ((OfflineConversionGoal)goalsCollection.Goals[0]).IsExternallyAttributed;
            var getGoal = getGoals.FirstOrDefault( o => ((OfflineConversionGoal) o).Id == goalsCollection.Goals[0].Id);
            var property = getGoal.GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoal, null));
            Assert.AreEqual(expectedValue, realValue, "IsExternallyAttributed doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_ExternalAttributionPilotDisabled_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            goalsCollection.Add_RewriteId(cInfo);
            List<object> getGoals = goalsCollection.Get_Success(null, ConversionGoalType.OfflineConversion, cInfo);
            var getGoal = getGoals.FirstOrDefault( o => ((OfflineConversionGoal) o).Id == goalsCollection.Goals[0].Id);
            var property = getGoal.GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoal, null));
            Assert.IsNull(realValue, "IsExternallyAttributed doesn't match");
        }
        
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        public void GetConversionGoals_ByCustomerIds_ExternalAttributionPilotEnabled_NoAdditionalFields_Success()
        {
            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, ConversionGoalType.OfflineConversion, tagId);
            (goalsCollection.Goals[0] as OfflineConversionGoal).IsExternallyAttributed = true;
            goalsCollection.Add_RewriteId(ThirdPartyConversionCustomerInfo);
            List<object> getGoals = goalsCollection.Get_Success(null, ConversionGoalType.OfflineConversion, ThirdPartyConversionCustomerInfo);
            var getGoal = getGoals.FirstOrDefault( o => ((OfflineConversionGoal) o).Id == goalsCollection.Goals[0].Id);
            var property = getGoal.GetType().GetProperty("IsExternallyAttributed");
            var realValue = (bool?)(property.GetValue(getGoal, null));
            Assert.IsNull(realValue, "IsExternallyAttributed doesn't match");
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [SkipInit]
        [Owner(TestOwners.Conversions)]
        public void GetConversionGoals_AutoGoal_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new []{ Features.AutoConversion });
            long tagId = GetUetTagId(customer);

            ConversionGoalCollection goalsCollection =
                new ConversionGoalCollection(1, ConversionGoalType.Event, tagId);
            goalsCollection.Goals[0].IsAutoGoal = true;
            goalsCollection.Goals[0].GoalCategory = ConversionGoalCategory.Purchase;

            ((EventGoal)goalsCollection.Goals[0]).ActionExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).ActionOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).Value = null;
            ((EventGoal)goalsCollection.Goals[0]).ValueOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).LabelOperator = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryExpression = null;
            ((EventGoal)goalsCollection.Goals[0]).CategoryOperator = null;

            goalsCollection.Add_Success(customer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsAutoGoal);

            var getGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), ConversionGoalType.Event, customer, ReturnAdditionalFields: ConversionGoalAdditionalField.IsAutoGoal);
            Assert.IsNotNull(getGoals);
            Assert.IsTrue(((EventGoal)getGoals[0]).IsAutoGoal);
        }

        private static long GetUetTagId(CustomerInfo cInfo = null)
        {
            var uetTagCollection = new UETTagCollection(1);
            var uetTags = uetTagCollection.Get_Success(null, cInfo);
            if (uetTags.Count > 0)
            {
                return ((UetTag)uetTags[0]).Id.Value;
            }
            uetTagCollection.Add_Success(cInfo);
            return uetTagCollection.Tags[0].Id.Value;
        }
    }
}
