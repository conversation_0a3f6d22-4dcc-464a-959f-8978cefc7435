﻿namespace Advertiser.CampaignManagement.MT.Import2.Implementation.EO.FileParsing
{
    using Advertiser.CampaignManagement.MT.BulkUpload2.Processors;
    using Advertiser.CampaignManagement.MT.Import2.Implementation.DAO;
    using Advertiser.CampaignManagement.MT.Import2.Implementation.DAO.AdExtension;
    using Advertiser.CampaignManagement.MT.Import2.Implementation.EO.ImportToBulk;
    using Advertiser.CampaignManagement.MT.Import2.Interface.Internal.Entity;
    using MT = Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Import2.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Import2.EO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Import2.EO.ImportToBulk;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertising.Client.Import;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT;

    public class SharedBrandFileProcessor : ImportParserBase, ISharedBrandFileProcessor
    {
        private readonly GetBrandsDao _getBrandsDao;
        public readonly IList<ImportSharedBrand> listForDeletion = new List<ImportSharedBrand>();
        public readonly IList<ImportSharedBrand> listForAddition = new List<ImportSharedBrand>();

        private int _importSourceId;

        public SharedBrandFileProcessor(
            IImportDataRepository importDataRepository,
            ICampaignDataRepository campaignDataRepository,
            IAccountToCustomerLookup accountToCustomerLookup,
            IImportDynamicValues importDynamicValues,
            ISerializer serializer,
            IDataRowToImportEntityConverter dataRowToImportEntityConverter,
            IUpdateImportCampaignIdProcessor updateImportCampaignIdProcessor,
            IImportStaticValues importStaticValues,
            ILocationCache locationCache,
            GetBrandsDao getBrandsDao)
            : base(importDataRepository, campaignDataRepository, accountToCustomerLookup, importDynamicValues, serializer, dataRowToImportEntityConverter, updateImportCampaignIdProcessor, importStaticValues, locationCache)
        {
            this._getBrandsDao = getBrandsDao;
        }

        public void CompleteFileProcessing(ImportContextualInformation importContext, IList<ImportSharedBrand> ImportSharedBrands)
        {
            var methodName = nameof(SharedBrandFileProcessor) + "." + nameof(CompleteFileProcessing);
            using (importContext?.GetPerformanceLogger(methodName))
            {
                try
                {
                    var brandListMap = importContext.BrandListMap;
                    var newBrandListIds = brandListMap.NewBrandListIds.ToHashSet(id => id);
                    var updatedBrandListIds = brandListMap.UpdatedBrandListIds.ToHashSet(id => id);
                    var deletedBrandListIds = brandListMap.DeletedBrandListIds.ToHashSet(id => id);

                    int importSourceId = FetchImportSourceId(importContext);

                    var brandListIdSet = new HashSet<long>(updatedBrandListIds);

                    var result = new BatchResult<List<SharedListItem>>(brandListIdSet.Count);
                    Dictionary<int, List<SharedListItem>> existingBrandListIdToBrandItems = GetExistingBrandItems(importContext, brandListIdSet, result);
                    var existingBrandItemsFromImport = existingBrandListIdToBrandItems.SelectMany(b => b.Value).Select(b => (BrandItem)b).Where(b => b.SourceId == importSourceId).ToList();

                    var brandNameUrlToBrandIdDict = new Dictionary<string, long>();
                    var newBrands = new List<ImportSharedBrand>();
                    var brandNameUrlToBrandListIdsDict = new Dictionary<string, HashSet<long>>(); // for check duplicated brand items
                    foreach (var importSharedBrand in ImportSharedBrands)
                    {
                        if (importSharedBrand == null || importSharedBrand.ExternalBrandListId == null || importSharedBrand.ExternalId == null)
                        {
                            continue;
                        }

                        // handle brand list id
                        var externalBrandListId = importSharedBrand.ExternalBrandListId.Value;
                        var brandListId = importContext.BrandListMap.FindInImportList(externalBrandListId)?.Id;
                        if (brandListId == null || !brandListId.HasValue)
                        {
                            continue;
                        }
                        importSharedBrand.ExistingBrandListId = brandListId;

                        // handle brand id
                        // fix duplicate brand items
                        // 1. google return brands with same name and url, but different external id, fix it by using brand name and url as key
                        // 2. google return brands with same name but url with or without '/' at the end, fix it by trimming
                        string brandNameUrlKey = $"{importSharedBrand.BrandName.Trim().ToLower()}_{importSharedBrand.BrandUrl.Trim().TrimEnd('/').ToLower()}";
                        if (brandNameUrlToBrandIdDict.TryGetValue(brandNameUrlKey, out var existingBrandId))
                        {
                            importSharedBrand.ExistingSharedBrandId = existingBrandId;
                        }
                        else
                        {
                            var brandId = this.FindBrandId(importSharedBrand, importContext, brandNameUrlToBrandIdDict.Count + 1);
                            if (!brandId.HasValue)
                            {
                                importContext.LogWarning($"{methodName} Brand ExternalId={importSharedBrand.ExternalId} not found for {importSharedBrand.BrandName} with URL {importSharedBrand.BrandUrl}.");
                                continue;
                            }
                            brandNameUrlToBrandIdDict[brandNameUrlKey] = brandId.Value;
                            importSharedBrand.ExistingSharedBrandId = brandId.Value;
                            if (brandId < 0)
                            {
                                newBrands.Add(importSharedBrand);
                            }
                        }

                        if(!brandNameUrlToBrandListIdsDict.TryGetValue(brandNameUrlKey, out var brandListIds))
                        {
                            brandListIds = new HashSet<long>();
                            brandNameUrlToBrandListIdsDict[brandNameUrlKey] = brandListIds;
                        }
                        if (brandListIds.Contains(brandListId.Value))
                        {
                            importContext.LogWarning($"{methodName} BrandListId={brandListId.Value} already exists association for {importSharedBrand.BrandName} with URL {importSharedBrand.BrandUrl}. Skip current Brand ExternalId={importSharedBrand.ExternalId}.");
                            continue;
                        }
                        brandListIds.Add(brandListId.Value);

                        // handle brand items based on new and updated brand list
                        if (newBrandListIds.Contains(brandListId.Value))
                        {
                            this.listForAddition.Add(importSharedBrand);
                            continue;
                        }
                        else if (updatedBrandListIds.Contains(brandListId.Value))
                        {
                            if (existingBrandItemsFromImport.Any(b => b.BrandId == importSharedBrand.ExistingSharedBrandId && b.BrandListId == importSharedBrand.ExistingBrandListId))
                            {
                                // skip brand item due to exists
                                existingBrandItemsFromImport.RemoveAll(b => b.BrandId == importSharedBrand.ExistingSharedBrandId && b.BrandListId == importSharedBrand.ExistingBrandListId);
                                continue;
                            }
                            else
                            {
                                this.listForAddition.Add(importSharedBrand);
                            }
                        }
                    }

                    // remove deleted brand items
                    foreach (var importSharedBrand in existingBrandItemsFromImport)
                    {
                        this.listForDeletion.Add(new ImportSharedBrand
                        {
                            ExistingSharedBrandId = importSharedBrand.BrandId,
                            ExistingBrandListId = importSharedBrand.BrandListId,
                            Status = LifeCycleStatus.Deleted,
                        });
                    }

                    WriteSharedBrandToBulk(importContext, newBrands);
                }
                catch (Exception ex)
                {
                    importContext.LogApplicationError($"{methodName} throwed an exception: {ex}");
                }
            }
        }

        private Dictionary<int, List<SharedListItem>> GetExistingBrandItems(ImportContextualInformation importContext, HashSet<long> brandListIdSet, BatchResult<List<SharedListItem>> result)
        {
            var methodName = nameof(SharedBrandFileProcessor) + "." + nameof(GetExistingBrandItems);
            var existingBrandItemResult = new BatchResult<List<SharedListItem>>();
            if (brandListIdSet.Count > 0)
            {
                existingBrandItemResult = this._getBrandsDao.GetBrandsByListIds(importContext.AccountCallContext, new LineItemContainer<long>(brandListIdSet.ToList(), result.BatchErrors));
            }
            if (existingBrandItemResult == null || existingBrandItemResult.Failed)
            {
                importContext.LogApplicationError($"{methodName} GetBrandsByListIds returns null.");
            }
            var existingBrandListIdToBrandItems = existingBrandItemResult.Entities;
            return existingBrandListIdToBrandItems;
        }

        private void WriteSharedBrandToBulk(ImportContextualInformation importContext, List<ImportSharedBrand> newBrands)
        {
            var methodName = nameof(SharedBrandFileProcessor) + "." + nameof(WriteSharedBrandToBulk);
            // write brand items
            using (importContext.GetPerformanceLogger($"{methodName} WriteAssociatoionRowsForDeletion: Entity Size {this.listForDeletion.Count}"))
            {
                var csvBuilder = new SharedBrandBulkCsvBuilder(
                    this.ImportDataRepository,
                    this.ImportDynamicValues,
                    this.ImportStaticValues.ImportToBulkFiles);
                var filePath = csvBuilder.WriteEntityToFileFromMemory(importContext, listForDeletion);
                if (!string.IsNullOrEmpty(filePath))
                {
                    importContext.ImportBulkFileList.Add(new ImportBulkFileInfo
                    {
                        EntityType = ImportDBEntityType.BrandItem,
                        FilePath = filePath,
                        ForDeleted = true,
                    });
                }
            }

            using (importContext.GetPerformanceLogger($"{methodName} WriteAssociatoionRowsForAddition: Entity Size {this.listForAddition.Count}"))
            {
                var csvBuilder = new SharedBrandBulkCsvBuilder(
                    this.ImportDataRepository,
                    this.ImportDynamicValues,
                    this.ImportStaticValues.ImportToBulkFiles);
                var filePath = csvBuilder.WriteEntityToFileFromMemory(importContext, listForAddition);
                if (!string.IsNullOrEmpty(filePath))
                {
                    importContext.ImportBulkFileList.Add(new ImportBulkFileInfo
                    {
                        EntityType = ImportDBEntityType.BrandItem,
                        FilePath = filePath,
                    });
                }
            }

            // write brands
            using (importContext.GetPerformanceLogger($"{methodName} WriteBrandRowsForAddition: Entity Size {newBrands.Count}"))
            {
                var csvBuilder = new BrandBulkCsvBuilder(
                    this.ImportDataRepository,
                    this.ImportDynamicValues,
                    this.ImportStaticValues.ImportToBulkFiles);
                var filePath = csvBuilder.WriteEntityToFileFromMemory(importContext, newBrands);
                if (!string.IsNullOrEmpty(filePath))
                {
                    importContext.ImportBulkFileList.Add(new ImportBulkFileInfo
                    {
                        EntityType = ImportDBEntityType.Brand,
                        FilePath = filePath,
                    });
                }
            }
        }

        private long? FindBrandId(ImportSharedBrand importSharedBrand, ImportContextualInformation importContext, int index)
        {
            if(importSharedBrand == null || string.IsNullOrEmpty(importSharedBrand.BrandUrl))// should be good format here, just keep null check
            {
                return null;
            }
            var brandResult = BrandCache.Cache.SearchAllBrandsByNameAndUri(importContext.Logger, importSharedBrand.BrandName, new Uri(importSharedBrand.BrandUrl))?.ToList();
            if (brandResult != null && brandResult.Count > 0)
            {
                var existingApprovedBrand = brandResult.FirstOrDefault(b => b.Status == EditorialStatus.Approved);
                if (existingApprovedBrand != null && existingApprovedBrand.Id > 0)
                {
                    return existingApprovedBrand.Id;
                }
                else
                {
                    var existingBrand = brandResult[0];
                    importContext.LogWarning($"[{nameof(FindBrandId)}]: Brand ExternalId={importSharedBrand.ExternalId} with Name {importSharedBrand.BrandName} with URL {importSharedBrand.BrandUrl} no approved ones, but found Status={existingBrand.Status}");
                    return existingBrand.Id;
                }
            }
            return -index;
        }

        private int FetchImportSourceId(ImportContextualInformation context)
        {
            if (_importSourceId != 0) return _importSourceId;
            var trackingData = MT.SafeSharedLog.CallTrackingData(context.Logger);
            _importSourceId = MiddleTierConstants.SourceIdForImport;

            if (trackingData == null || !ClientApplicationCache.Instance.TryGet(context.Logger, trackingData.ClientApplication, out _importSourceId))
            {
                context.Logger.LogError($"[{nameof(FetchImportSourceId)}]: SourceId not available. Falling back to 11");
            }

            return _importSourceId;
        }
    }
}
