@Context(reporting)
#skipType
ReportRequest {
  AccountPerformanceReportRequest : string { @DefaultValue(AccountPerformanceReportRequest) } ; 
  CampaignPerformanceReportRequest : string { @DefaultValue(CampaignPerformanceReportRequest) } ; 
  AdDynamicTextPerformanceReportRequest : string { @DefaultValue(AdDynamicTextPerformanceReportRequest) } ; 
  AdGroupPerformanceReportRequest : string { @DefaultValue(AdGroupPerformanceReportRequest) } ; 
  AdPerformanceReportRequest : string { @DefaultValue(AdPerformanceReportRequest) } ; 
  KeywordPerformanceReportRequest : string { @DefaultValue(KeywordPerformanceReportRequest) } ; 
  DestinationUrlPerformanceReportRequest : string { @DefaultValue(DestinationUrlPerformanceReportRequest) } ; 
  BudgetSummaryReportRequest : string { @DefaultValue(BudgetSummaryReportRequest) } ; 
  AgeGenderAudienceReportRequest : string { @DefaultValue(AgeGenderAudienceReportRequest) } ; 
  ProfessionalDemographicsAudienceReportRequest : string { @DefaultValue(ProfessionalDemographicsAudienceReportRequest) } ; 
  UserLocationPerformanceReportRequest : string { @DefaultValue(UserLocationPerformanceReportRequest) } ; 
  PublisherUsagePerformanceReportRequest : string { @DefaultValue(PublisherUsagePerformanceReportRequest) } ; 
  SearchQueryPerformanceReportRequest : string { @DefaultValue(SearchQueryPerformanceReportRequest) } ; 
  ConversionPerformanceReportRequest : string { @DefaultValue(ConversionPerformanceReportRequest) } ; 
  GoalsAndFunnelsReportRequest : string { @DefaultValue(GoalsAndFunnelsReportRequest) } ; 
  NegativeKeywordConflictReportRequest : string { @DefaultValue(NegativeKeywordConflictReportRequest) } ; 
  SearchCampaignChangeHistoryReportRequest : string { @DefaultValue(SearchCampaignChangeHistoryReportRequest) } ; 
  AdExtensionByAdReportRequest : string { @DefaultValue(AdExtensionByAdReportRequest) } ; 
  AdExtensionByKeywordReportRequest : string { @DefaultValue(AdExtensionByKeywordReportRequest) } ; 
  AudiencePerformanceReportRequest : string { @DefaultValue(AudiencePerformanceReportRequest) } ; 
  AdExtensionDetailReportRequest : string { @DefaultValue(AdExtensionDetailReportRequest) } ; 
  ShareOfVoiceReportRequest : string { @DefaultValue(ShareOfVoiceReportRequest) } ; 
  ProductDimensionPerformanceReportRequest : string { @DefaultValue(ProductDimensionPerformanceReportRequest) } ; 
  ProductPartitionPerformanceReportRequest : string { @DefaultValue(ProductPartitionPerformanceReportRequest) } ; 
  ProductPartitionUnitPerformanceReportRequest : string { @DefaultValue(ProductPartitionUnitPerformanceReportRequest) } ; 
  ProductSearchQueryPerformanceReportRequest : string { @DefaultValue(ProductSearchQueryPerformanceReportRequest) } ; 
  ProductMatchCountReportRequest : string { @DefaultValue(ProductMatchCountReportRequest) } ; 
  ProductNegativeKeywordConflictReportRequest : string { @DefaultValue(ProductNegativeKeywordConflictReportRequest) } ; 
  CallDetailReportRequest : string { @DefaultValue(CallDetailReportRequest) } ; 
  GeographicPerformanceReportRequest : string { @DefaultValue(GeographicPerformanceReportRequest) } ; 
  DSASearchQueryPerformanceReportRequest : string { @DefaultValue(DSASearchQueryPerformanceReportRequest) } ; 
  DSAAutoTargetPerformanceReportRequest : string { @DefaultValue(DSAAutoTargetPerformanceReportRequest) } ; 
  DSACategoryPerformanceReportRequest : string { @DefaultValue(DSACategoryPerformanceReportRequest) } ; 
  HotelDimensionPerformanceReportRequest : string { @DefaultValue(HotelDimensionPerformanceReportRequest) } ; 
  HotelGroupPerformanceReportRequest : string { @DefaultValue(HotelGroupPerformanceReportRequest) } ; 
  AssetGroupPerformanceReportRequest : string { @DefaultValue(AssetGroupPerformanceReportRequest) } ; 
  SearchInsightPerformanceReportRequest : string { @DefaultValue(SearchInsightPerformanceReportRequest) } ; 
  AssetPerformanceReportRequest : string { @DefaultValue(AssetPerformanceReportRequest) } ; 
  CategoryInsightsReportRequest : string { @DefaultValue(CategoryInsightsReportRequest) } ; 
  CategoryClickCoverageReportRequest : string { @DefaultValue(CategoryClickCoverageReportRequest) } ; 
  CombinationPerformanceReportRequest : string { @DefaultValue(CombinationPerformanceReportRequest) } ; 
  AppsPerformanceReportRequest : string { @DefaultValue(AppsPerformanceReportRequest) } ; 
  FeedItemPerformanceReportRequest : string { @DefaultValue(FeedItemPerformanceReportRequest) } ; 
  TravelQueryInsightReportRequest : string { @DefaultValue(TravelQueryInsightReportRequest) } ; 
}
