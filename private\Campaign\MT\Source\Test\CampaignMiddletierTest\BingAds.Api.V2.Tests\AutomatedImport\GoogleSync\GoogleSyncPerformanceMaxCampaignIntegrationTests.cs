﻿#if V2

namespace Microsoft.Advertising.Advertiser.Api.V2.AutomatedImport.GoogleSync
{
    using System;
    using System.Collections.Generic;
    using System.Dynamic;
    using System.IO;
    using System.Linq;
    using Microsoft.AdCenter.Deployment.Configurations;
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Azure;
    using CampaignMiddleTierTest.Framework.DBValidators;
    using CampaignMiddleTierTest.Framework.ImportTestObjects;
    using CampaignMiddleTierTest.Framework.Utilities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.AudienceGroup;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Import2.Entities;
    using Microsoft.Advertising.Advertiser.Api.V2.MultiAccountDownload;
    using Microsoft.Advertising.Advertiser.MT.Import2;
    using Microsoft.Advertising.Client.GoogleSync.Common;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.Advertising.Advertiser.MT;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using static Microsoft.Advertising.Advertiser.Api.V2.AutomatedImport.ImportTestHelper;
    using static Microsoft.Advertising.Advertiser.Api.V2.TestTaskItemOperations;
    using TestOwners = CampaignMiddleTierTest.Framework.TestOwners;

    [TestClass]
    [TestCategory(CampaignTest.Framework.Constants.Constants.GoogleSync)]
    [DeploymentItem("ImportStaticTestFiles\\GSyncFile_GoogleNonRetailPMCMaxConversionTcpaCampaignId_ChangeToMaxConversionValueWithTargetRoas.csv", "ImportStaticTestFiles")]
    [DeploymentItem("ImportStaticTestFiles\\GSyncFile_GooglePMCCampaignMaxConversionValueWithTroasCampaignId_ChangeToMaxConversionsWithTargetCPA.csv", "ImportStaticTestFiles")]
    public class GoogleSyncPerformanceMaxCampaignIntegrationTests : CampaignTestBase
    {
        CustomerInfo customerInfo;

        private ShoppingStore store = null;
        private long providerId;
        private const string AssetGroup = "Asset Group";

        [TestInitialize]
        public void TestInitialize()
        {
            // Piloted user
            customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            InsertUETTag(customerInfo);

            this.providerId = 1;
            this.store = null;

            if (System.Configuration.ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                this.store = new ShoppingStore(customerInfo, storeUrlOverride: "http://" + StringUtil.GenerateUniqueId() + ".Baidu.com");
                this.providerId = this.store.ProviderId;
            }
        }

        public static void InsertUETTag(CustomerInfo customerInfo)
        {
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var tagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(customerInfo, goal);
        }

        #region Migration
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        [Ignore]  // impacted by 5m cache on the account features - maybe can put a request in with the databasehelper to reset the cache for that account
        public void GoogleSyncImport_NativePerformanceMaxCampaign_DsaMigration_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCCamapignId });

            {
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Ad Group"].Additions, "importstatistics: 1 ad group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Group"].Errors, "importstatistics: 0 ad group error expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
            }

            {
                DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                    false,
                    Features.SmartShoppingCampaign,
                    Features.AutoBiddingMaxConversionValueEnabled,
                    Features.MaxConversionForSSC,
                    Features.PerformanceMaxCampaigns,
                    Features.UrlEqualsInDSATarget);

                var overrideConfigDict = new Dictionary<string, string>
                {
                    { "EnableDsaToPMaxMigration", "GA" },
                };

                var reimportId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2,
                    overrideConfigValuesFromTest: overrideConfigDict);

                var reImportService = new ImportService2();
                reImportService.Submit(customerInfo, reimportId, overrideConfigValuesFromTest: overrideConfigDict);

                var reImportSubmittedCounts = reImportService.GetSubmittedCount(customerInfo, reimportId);
                Assert.AreEqual(1, reImportSubmittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 0 campaign changes expected");  // was updated pre-import so no statistic
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Campaign"].Deletions, "importstatistics: 0 campaign deletions expected");
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Ad Group"].Additions, "importstatistics: 0 ad group add expected");
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Ad Group"].Errors, "importstatistics: 0 ad group error expected");
                Assert.AreEqual(1, reImportSubmittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
                Assert.AreEqual(0, reImportSubmittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

                var searchCampaign = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.Default);
                Assert.AreEqual(1, searchCampaign.Count, "should not have added any campaigns - the existing one will be captured by name");
                Assert.AreEqual(CampaignStatus.UserPaused, searchCampaign[0].Status);
                Assert.IsTrue(searchCampaign[0].Name.StartsWith("PMAX2DSA_"));

                var pmaxCampaign = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, pmaxCampaign.Count, "should not have added any campaigns - the existing one will be captured by name");
                Assert.AreEqual(CampaignStatus.Active, pmaxCampaign[0].Status);
            }
        }
        #endregion

        #region Native

        [Ignore] // moved to Import_BVT.cs so it can be included in FFTP
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_ListingGroupReImport_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxListingGroupCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            {  // import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Additions >= 10, "importstatistics: asset group listing group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Errors, "importstatistics: 0 asset group listing group error expected");
            }

            {  // re-import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 2 asset group change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Total >= 10, "importstatistics: asset group listing group add expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Changes >= 10, "importstatistics: asset group listing group change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Errors, "importstatistics: 0 asset group listing group error expected");
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AudienceGroupReImport_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxAudienceGroupCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);
            long[] assetGroupIds = new long[] { 1 };

            {  // import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Additions >= 3, "importstatistics: 3 audience group add expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group Asset Group Association"].Additions >= 2, "importstatistics: 2 audience group association add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Errors, "importstatistics: 0 audience group error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);
                var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
                assetGroupIds = assetGroups.Where(a => a.Id.HasValue).Select(a => a.Id.Value).ToArray();
                Assert.AreEqual(2, assetGroupIds.Length);

                var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
                Assert.IsTrue(audienceGroups.Count >= 3, "3 audience groups - the existing one will be captured by name and add 2 new added");
                var audienceGroup = audienceGroups.First(a => a.Name.Equals("[AT] AudienceGroup2 - test"));
                Assert.AreEqual(4, ((AudienceDimension)audienceGroup.Dimensions.First(d => d is AudienceDimension)).Audiences.Length);
                // include unknown age range in the audience group 
                Assert.IsTrue(((AgeDimension)audienceGroup.Dimensions.First(d => d is AgeDimension)).AgeRanges.Contains(Ads.Mca.BusinessMT.Model.Ads1.McaAgeRange.Unknown));
                var audienceGroupWithAllAges = audienceGroups.First(a => a.Name.Equals("[AT] AudienceGroup1"));
                Assert.IsTrue(((AgeDimension)audienceGroupWithAllAges.Dimensions.First(d => d is AgeDimension)).AgeRanges.Contains(Ads.Mca.BusinessMT.Model.Ads1.McaAgeRange.Unknown));

                var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
                Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);
            }

            {  // re-import and don't update audience target
                string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile-3AudienceGroup_1Updated1Deleted.csv", ImportConstants.ImportStaticTestFiles);

                var pref = ImportService2.GetAllTrueImportPrefNew();
                pref.UpdateAudienceTargets = false;
                pref.UpdateDemographicTargets = false;
                var importService = new ImportService2();
                importService.FilePath = sasUrlForImport;
                importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true, importPreference: pref);
                importService.ProcessImport(customerInfo, null, null);
                importService.Submit(customerInfo);

                var submittedCounts = importService.GetSubmittedCount(customerInfo);
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Total >= 3, "importstatistics: 3 audience group total expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Changes >= 1, "importstatistics: 1 audience group change expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Deletions == 1, "importstatistics: 1 audience group delete expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Errors == 1, "importstatistics: 1 audience group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group Asset Group Association"].Changes == 0, "importstatistics: 0 audience group association change expected");

                var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
                Assert.IsTrue(audienceGroups.Count >= 1);
                var audienceGroup = audienceGroups.First(a => a.Name.Equals("[AT] AudienceGroup2 - test"));
                Assert.AreEqual(4, ((AudienceDimension)audienceGroup.Dimensions.First(d => d is AudienceDimension)).Audiences.Length);
                // due to UpdateDemographicTargets=false, should not update age range in the audience group
                Assert.IsTrue(((AgeDimension)audienceGroup.Dimensions.First(d => d is AgeDimension)).AgeRanges.Contains(Ads.Mca.BusinessMT.Model.Ads1.McaAgeRange.Unknown));

                var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
                Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);
            }

            {  // re-import update 1 audience group and 2 delete error due to association
                string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile-3AudienceGroup_1Updated1Deleted.csv", ImportConstants.ImportStaticTestFiles);

                var importService = new ImportService2();
                importService.FilePath = sasUrlForImport;
                importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true);
                importService.ProcessImport(customerInfo, null, null);
                importService.Submit(customerInfo);

                var submittedCounts = importService.GetSubmittedCount(customerInfo);
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Total >= 3, "importstatistics: 2 audience group total expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Changes >= 1, "importstatistics: 1 audience group change expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Deletions == 0, "importstatistics: 1 audience group delete expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Errors == 2, "importstatistics: 0 audience group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group Asset Group Association"].Changes == 0, "importstatistics: 0 audience group association change expected");

                var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
                Assert.IsTrue(audienceGroups.Count >= 1);
                var audienceGroup = audienceGroups.First(a => a.Name.Equals("[AT] AudienceGroup2 - test"));
                Assert.AreEqual(3, ((AudienceDimension)audienceGroup.Dimensions.First(d => d is AudienceDimension)).Audiences.Length);
                // due to UpdateDemographicTargets=true, replace age range in the audience group
                Assert.IsFalse(((AgeDimension)audienceGroup.Dimensions.First(d => d is AgeDimension)).AgeRanges.Contains(Ads.Mca.BusinessMT.Model.Ads1.McaAgeRange.Unknown));

                var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
                Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AudienceGroupAssociationDeleted_ReImport_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxAudienceGroupCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);
            long[] assetGroupIds = new long[] { 1 };

            {  // import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Additions >= 3, "importstatistics: 3 audience group add expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group Asset Group Association"].Additions >= 2, "importstatistics: 2 audience group association add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group Listing Group"].Errors, "importstatistics: 0 audience group error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);
                var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
                assetGroupIds = assetGroups.Where(a => a.Id.HasValue).Select(a => a.Id.Value).ToArray();
                Assert.AreEqual(2, assetGroupIds.Length);

                var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
                Assert.IsTrue(audienceGroups.Count >= 3, "3 audience groups - the existing one will be captured by name and add 2 new added");

                var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
                Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);

                Import2Helper.DeleteAudienceGroupAssociations(customerInfo, audienceGroupAssetGroupAssociations.ToArray());
            }

            {  // re-import 
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Total >= 3, "importstatistics: 3 audience group total expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Changes >= 3, "importstatistics: 3 audience group change expected");
                Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group Asset Group Association"].Additions >= 2, "importstatistics: 2 audience group association add expected");

                var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
                Assert.IsTrue(audienceGroups.Count >= 3);
                var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
                Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AudienceGroupSameName_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax, CampaignFactory.CampaignTimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon, CampaignFactory.DaylightSaving.Disabled, CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[0].Data.Name = "[AT] Retail Performance Max (Audience Group)-G19847215341";
            // update by name will fail if the msft campaign does not have shopping settings but the new google one does - add shopping settings here.
            campaignCollection.Campaigns[0].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings(), new PerformanceMaxSetting() { FinalUrlExpansionOptOut = true } };
            ShoppingSettings shoppingSetting1 = campaignCollection.Campaigns[0].Data.CampaignSettings[0] as ShoppingSettings;
            shoppingSetting1.Priority = CampaignPriority.L5;
            shoppingSetting1.SalesCountry = "US";
            shoppingSetting1.ProviderId = this.providerId;
            ResponseValidator.ValidateBatchSuccess(campaignCollection.Add(customerInfo));

            var assetGroupCollection = new TestAssetGroupCollection(campaignCollection.Campaigns[0], this.customerInfo, 1);
            assetGroupCollection.SetFinalUrlFromStore(this.store);
            assetGroupCollection.AssetGroups[0].Data.Name = "Asset Group 1";
            ResponseValidator.ValidateBatchSuccess(assetGroupCollection.Add(customerInfo));

            var audienceGroupCollection = GetAudienceGroupsTests.CreateAudienceGroup(customerInfo);
            audienceGroupCollection.AudienceGroups[0].Data.Name = "[AT] AudienceGroup1";
            ResponseValidator.ValidateBatchSuccess(audienceGroupCollection.Add(customerInfo));

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxAudienceGroupCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
            Assert.IsTrue(submittedCounts.EntityNameToImportStatistics["Audience Group"].Additions >= 2, "importstatistics: 2 audience group add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Audience Group"].Changes, "importstatistics: 1 audience group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Audience Group"].Errors, "importstatistics: 0 audience group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should not have added any campaigns - the existing one will be captured by name");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            var assetGroupIds = assetGroups.Where(a => a.Id.HasValue).Select(a => a.Id.Value).ToArray();
            Assert.AreEqual(2, assetGroups.Count, "should not have added any asset groups - the existing one will be captured by name");

            var audienceGroups = Import2Helper.FetchAudienceGroups(customerInfo, customerInfo.AccountIds[0]);
            Assert.IsTrue(audienceGroups.Count >= 3, "3 audience groups - the existing one will be captured by name and add 2 new added");
            audienceGroups.Where(a => a.Name.StartsWith("Asset Group")).ToList().ForEach(a =>
            {
                Assert.IsTrue(((AgeDimension)a.Dimensions.First(d => d is AgeDimension)).AgeRanges.Contains(Ads.Mca.BusinessMT.Model.Ads1.McaAgeRange.Unknown));
            });

            var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
            Assert.AreEqual(2, audienceGroupAssetGroupAssociations.Count);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupLevelImport_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var prefs = CreateImportUserPreference();
            prefs.ImportNewCampaignsAndChildEntities = true;

            var googleEntities = new GoogleEntities
            {
                CampaignIds = new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxAudienceGroupCampaignId },
                AdGroupIdMap = new Dictionary<long, long[]>
                {
                    { GoogleTestAccountData.GoogleRetailPerformanceMaxListingGroupCampaignId, new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxListingGroupCampaignAssetGroupId } }
                }
            };
            var fileName = SerializeAndUploadGoogleEntitiesToAzureBlob(googleEntities, customerInfo);

            var taskItemExecution =
                ImportAndGetTaskItemExecution(customerInfo, null, new JArray(), googleTestAccountId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId, googleEntitiesFileName: fileName, pref: prefs);
            var statistics = GetImportStatisticsFromExecution(taskItemExecution["EntityStatistics"]);
            Assert.AreEqual(2, statistics["Campaign"].Additions);
            Assert.AreEqual(2, statistics["Campaign"].Total);
            Assert.IsTrue(statistics["Asset Group"].Additions == 3);
            Assert.IsTrue(statistics["Asset Group"].Errors == 0);
            Assert.IsTrue(statistics["Asset Group Listing Group"].Additions >= 10);
            Assert.IsTrue(statistics["Asset Group Listing Group"].Errors == 0);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(2, campaigns.Count, "should have added 2 campaigns");

            var assetGroups1 = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            var assetGroups2 = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[1].Id);
            var assetGroups = (assetGroups1.Count < assetGroups2.Count) ? assetGroups1 : assetGroups2;
            var assetGroupIds = assetGroups.Where(a => a.Id.HasValue).Select(a => a.Id.Value).ToArray();
            Assert.AreEqual(1, assetGroups.Count, "should have added 1 asset groups");

            var audienceGroupAssetGroupAssociations = Import2Helper.FetchAudienceGroupAssociations(customerInfo, assetGroupIds);
            Assert.AreEqual(0, audienceGroupAssetGroupAssociations.Count);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupReImport_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxMaxConversionsTcpaCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            {  // import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);
            }

            {  // re-import
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupName_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax, CampaignFactory.CampaignTimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon, CampaignFactory.DaylightSaving.Disabled, CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[0].Data.Name = "[AT] Retail Performance Max (MaxConversion + TCPA)-G18575475543";
            // update by name will fail if the msft campaign does not have shopping settings but the new google one does - add shopping settings here.
            campaignCollection.Campaigns[0].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings(), new PerformanceMaxSetting() { FinalUrlExpansionOptOut = true } };
            ShoppingSettings shoppingSetting1 = campaignCollection.Campaigns[0].Data.CampaignSettings[0] as ShoppingSettings;
            shoppingSetting1.Priority = CampaignPriority.L5;
            shoppingSetting1.SalesCountry = "US";
            shoppingSetting1.ProviderId = this.providerId;
            ResponseValidator.ValidateBatchSuccess(campaignCollection.Add(customerInfo));

            var assetGroupCollection = new TestAssetGroupCollection(campaignCollection.Campaigns[0], this.customerInfo, 1);
            assetGroupCollection.SetFinalUrlFromStore(this.store);
            assetGroupCollection.AssetGroups[0].Data.Name = "Asset Group 1";
            ResponseValidator.ValidateBatchSuccess(assetGroupCollection.Add(customerInfo));

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxMaxConversionsTcpaCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should not have added any campaigns - the existing one will be captured by name");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should not have added any asset groups - the existing one will be captured by name");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_DeletedAssetGroup_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxDeletedAssetGroupCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Deletions, "importstatistics: - asset group add expected");  // but there were 2 deleted asset group rows.
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_PausedAssetGroup_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCAssetGroupStatusCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");  // 1 active, 1 paused
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Deletions, "importstatistics: - asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_CamapignAdExtension_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCCampaignExtensionCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Ad Extension"].Additions, "importstatistics: 3 ad extension add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Extension"].Deletions, "importstatistics: 0 ad extension deletions expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Extension"].Errors, "importstatistics: 0 ad extension error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_MaxConversionTcpa_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCMaxConversionTcpaCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.FeedLabel,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            // just check that we didn't error out bid strategy for max conversions tcpa
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count);

            var campaign = campaigns[0];
            Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversions");

            if (campaign.BiddingScheme is MaxConversionsBiddingScheme maxConversionsBiddingScheme)
            {
                Assert.AreEqual(3, maxConversionsBiddingScheme.TargetCpa, "TargetCPA should be 3");
            }
            else
            {
                Assert.Fail("BiddingScheme type should be MaxConversionsBiddingScheme");
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaignLite_MaxConversionValueNoTargetRoas_NoGoalSuccess_SIOnly()
        {
            // Create a new Test customer without account level conversion goal
            var customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCCampaignExtensionCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                AccountPilotFeatures.PMaxLite);

            { //Import, PMax campaign created with MaxConversionValueWithoutTargetRoas successfully for no goal scenario
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversion value
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversionValue, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversionValue");
            }

            { //Reimport, PMax campaign update successfully for no goal and no bidding strategy change scenario 
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversion value
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversionValue, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversionValue");
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaignLite_MaxConversionValueWithTargetRoas_NoGoalSuccess_SIOnly()
        {
            // Create a new Test customer without account level conversion goal
            var customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCCampaignMaxConversionValueWithTroasCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                AccountPilotFeatures.PMaxLite);

            { //import, PMax campaign created with MaxConversionValueWithTargetRoas successfully for no goal scenario
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversion value
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversionValue, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversionValue");

                if (campaign.BiddingScheme is MaxConversionValueBiddingScheme maxConversionValueBiddingScheme)
                {
                    Assert.AreEqual(1.1, maxConversionValueBiddingScheme.TargetRoas, "TargetRoas should be 110%");
                }
                else
                {
                    Assert.Fail("BiddingScheme type should be MaxConversionValueBiddingScheme with target Roas");
                }
            }

            {  // re-import update campaign from MaxConversionValueWithTargetRoas to MaxConversionWithTargetCPA successfully for no goal scenario
                string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_GooglePMCCampaignMaxConversionValueWithTroasCampaignId_ChangeToMaxConversionsWithTargetCPA.csv", ImportConstants.ImportStaticTestFiles);

                var importService = new ImportService2();
                importService.FilePath = sasUrlForImport;
                importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true);
                importService.ProcessImport(customerInfo, null, null);
                importService.Submit(customerInfo);

                var submittedCounts = importService.GetSubmittedCount(customerInfo);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign changes expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversion");

                if (campaign.BiddingScheme is MaxConversionsBiddingScheme maxConversionsBiddingScheme)
                {
                    Assert.AreEqual(5, maxConversionsBiddingScheme.TargetCpa, "TargetCPA should be 5");
                }
                else
                {
                    Assert.Fail("BiddingScheme type should be MaxConversionsBiddingScheme with target CPA");
                }
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxLite_MaxConversionNoTargetCPA_NoGoalSuccess_SIOnly()
        {
            // Create a new Test customer without account level conversion goal
            var customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCCamapignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                AccountPilotFeatures.PMaxLite);

            { //import, PMax campaign created with MaxConversionsNoTargetCPA successfully for no goal scenario
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversions tcpa
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversions");
            }

            { //Reimport, PMax campaign update successfully for no goal and no bidding strategy change scenario
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversions tcpa
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign change expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversions");
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxLite_MaxConversionWithTargetCPA_NoGoalSuccess_SIOnly()
        {
            // Create a new Test customer without account level conversion goal
            var customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCMaxConversionTcpaCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                AccountPilotFeatures.PMaxLite);

            { //import, PMax campaign created with MaxConversionsWithTargetCPA successfully for no goal scenario
                var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                    customerInfo,
                    googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                    googleCampaignIds: null,
                    campaignIdBlobFile: fileName,
                    refreshToken: GSyncTestConsts.GSyncRefreshToken2);

                var importService = new ImportService2();
                importService.Submit(customerInfo, importId);

                var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
                // just check that we didn't error out bid strategy for max conversions tcpa
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversions");


                if (campaign.BiddingScheme is MaxConversionsBiddingScheme maxConversionsBiddingScheme)
                {
                    Assert.AreEqual(3, maxConversionsBiddingScheme.TargetCpa, "TargetCPA should be 3");
                }
                else
                {
                    Assert.Fail("BiddingScheme type should be MaxConversionsBiddingScheme with target CPA");
                }
            }

            {  // re-import update campaign from MaxConversionWithTargetCPA to MaxConversionValueWithTargetRoas successfully for no goal scenario
                string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_GoogleNonRetailPMCMaxConversionTcpaCampaignId_ChangeToMaxConversionValueWithTargetRoas.csv", ImportConstants.ImportStaticTestFiles);

                var importService = new ImportService2();
                importService.FilePath = sasUrlForImport;
                importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true);
                importService.ProcessImport(customerInfo, null, null);
                importService.Submit(customerInfo);

                var submittedCounts = importService.GetSubmittedCount(customerInfo);
                Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign changes expected");
                Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

                var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
                Assert.AreEqual(1, campaigns.Count);

                var campaign = campaigns[0];
                Assert.AreEqual(BiddingStrategyType.MaxConversionValue, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversionValue");

                if (campaign.BiddingScheme is MaxConversionValueBiddingScheme maxConversionValueBiddingScheme)
                {
                    Assert.AreEqual(2, maxConversionValueBiddingScheme.TargetRoas, "TargetRoas should be 200%");
                }
                else
                {
                    Assert.Fail("BiddingScheme type should be MaxConversionValueBiddingScheme with target Roas");
                }
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_EmptyAssetGroup_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.PerformanceMaxTestMultipleAssetGroupCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleSSCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 0 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            // all asset groups were skipped
            // In CI, should fail due to domain mismatch
            // In SI, should fail due to business name not present and draft store not having domain
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 0 asset group update expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 2 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have added one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(0, assetGroups.Count, "should not have added any asset groups - all errored out");
        }


        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_DraftStore_CIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PMaxDraftStoreImport);

            var campaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax,
                CampaignFactory.CampaignSettings.Valid,
                CampaignSettingsFactory.ShoppingSettingsCount.One,
                CampaignSettingsFactory.ShoppingSetting.Valid,
                ShoppingSettingsFactory.Priority.L5,
                ShoppingSettingsFactory.ProviderId.One,
                CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[0].Data.Name = "PMax Multiple Asset Group-G17972678564";
            ((ShoppingSettings)campaignCollection.Campaigns[0].Data.CampaignSettings[0]).ProviderId = 202;

            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.PerformanceMaxTestMultipleAssetGroupCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleSSCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 0 asset group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(2, assetGroups.Count, "should have added two asset groups");

            foreach (var assetGroup in assetGroups)
            {
                Assert.AreEqual(null, assetGroup.BusinessName);
                Assert.IsTrue(assetGroup.FinalUrls[0].EndsWith("://bookmyload.com"));
            }

        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_DraftStore_EmptyAssetGroup_CIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PMaxDraftStoreImport);

            var campaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax,
                CampaignFactory.CampaignSettings.Valid,
                CampaignSettingsFactory.ShoppingSettingsCount.One,
                CampaignSettingsFactory.ShoppingSetting.Valid,
                ShoppingSettingsFactory.Priority.L5,
                ShoppingSettingsFactory.ProviderId.One,
                CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[0].Data.Name = "Retail Performance Max (listing group)-G19828701835";
            ((ShoppingSettings)campaignCollection.Campaigns[0].Data.CampaignSettings[0]).ProviderId = 202;

            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleEmptyAssetGroupPerfomanceMaxCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleEmptyAssetGroupPerfomanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 0 asset group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(2, assetGroups.Count, "should have added two asset groups");

            foreach (var assetGroup in assetGroups)
            {
                if (assetGroup.Name == "Asset Group 2")
                {
                    Assert.AreEqual(null, assetGroup.BusinessName);
                    Assert.AreEqual(null, assetGroup.FinalUrls);
                }

            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetAutomationOptOut_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCAssetAutomationOptOut, GoogleTestAccountData.GoogleNonRetailPMCCamapignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.PerformanceMaxCampaignLevelAutoGenAssetsControl,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            // just check that we didn't error out bid strategy for max conversions tcpa
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 2 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(2, campaigns.Count);

            campaigns = campaigns.OrderBy(_ => _.Name).ToList();  // enforce ordering

            {
                var campaign = campaigns[0];  // this one does not opt-out
                var campaignSetting = campaign.CampaignSettings[0] as PerformanceMaxSetting;
                Assert.IsFalse(campaignSetting.AutoGeneratedTextOptOut);
                Assert.IsFalse(campaignSetting.AutoGeneratedImageOptOut);
            }

            {
                var campaign = campaigns[1];  // this one does opt-out
                var campaignSetting = campaign.CampaignSettings[0] as PerformanceMaxSetting;
                Assert.IsTrue(campaignSetting.AutoGeneratedTextOptOut);
                Assert.IsTrue(campaignSetting.AutoGeneratedImageOptOut);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_FeedLabelEnabledNoSalesCountry_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.FeedLabel,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns);

            TestCampaignCollection.Clean(customerInfo);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[]
            {
                GoogleTestAccountData.GoogleRetailPerformanceMaxListingGroupCampaignId  // no feed label
            });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 0 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should not have added any campaigns - the existing one will be captured by name");
            var campaign = campaigns[0];
            var shoppingSettings = campaign.CampaignSettings[0] as ShoppingSettings;
            Assert.IsNull(shoppingSettings.SalesCountry, "Sales Country should not be set.");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_RemoveSalesCountry_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.FeedLabel,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns);

            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax, CampaignFactory.CampaignTimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon, CampaignFactory.DaylightSaving.Disabled, CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[0].Data.Name = "[AT] Retail Performance Max (listing group)-G19825790348";
            // update by name will fail if the msft campaign does not have shopping settings but the new google one does - add shopping settings here.
            campaignCollection.Campaigns[0].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings(), new PerformanceMaxSetting() { FinalUrlExpansionOptOut = true } };
            ShoppingSettings shoppingSetting1 = campaignCollection.Campaigns[0].Data.CampaignSettings[0] as ShoppingSettings;
            shoppingSetting1.Priority = CampaignPriority.L5;
            shoppingSetting1.SalesCountry = "US";
            shoppingSetting1.ProviderId = this.providerId;
            ResponseValidator.ValidateBatchSuccess(campaignCollection.Add(customerInfo));

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] {
                GoogleTestAccountData.GoogleRetailPerformanceMaxListingGroupCampaignId  // no feed label
            });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 0 campaign add expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should not have added any campaigns - the existing one will be captured by name");
            var campaign = campaigns[0];
            var shoppingSettings = campaign.CampaignSettings[0] as ShoppingSettings;
            Assert.IsNull(shoppingSettings.SalesCountry, "Sales Country should have been deleted.");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupBusinessName_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleAssetGroupBusinessNamePerformanceMaxCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 0 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");

            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 2 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 0 asset group update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(2, assetGroups.Count, "should have added two asset groups");

            foreach (var assetGroup in assetGroups)
            {
                if (assetGroup.Name == "Long Business Name")
                {
                    Assert.AreEqual("Long Business Name 123456", assetGroup.BusinessName);
                }
                else
                {
                    Assert.AreEqual("\"Bu, \"sin \\m/ ss name'asd", assetGroup.BusinessName);
                }
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupWithFreeStockImage_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleAssetGroupWithFreeStockImagePerformanceMaxCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Deletions, "importstatistics: 0 asset group delete expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_BrandGuidelines_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCBrandGuidelinesCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GooglePMCBrandGuidelinesCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Deletions, "importstatistics: 0 asset group delete expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count);
            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count);
            var assetGroup = assetGroups[0];
            Assert.IsNotNull(assetGroup.BusinessName);
            Assert.AreEqual(1, assetGroup.Images.Count(_ => _.AssociationType==AssetAssociationType.LandscapeLogoMedia));
            Assert.AreEqual(2, assetGroup.Images.Count(_ => _.AssociationType == AssetAssociationType.SquareLogoMedia));
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_BrandGuidelinesNoLandscape_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMCBrandGuidelinesCampaignId2 });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GooglePMCBrandGuidelinesCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Deletions, "importstatistics: 0 asset group delete expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count);
            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count);
            var assetGroup = assetGroups[0];
            Assert.IsNotNull(assetGroup.BusinessName);
            Assert.AreEqual(0, assetGroup.Images.Count(_ => _.AssociationType == AssetAssociationType.LandscapeLogoMedia));
            Assert.AreEqual(1, assetGroup.Images.Count(_ => _.AssociationType == AssetAssociationType.SquareLogoMedia));
        }
        #endregion

        #region Retail
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        [Ignore]
        public void GoogleSyncImport_RetailPerformanceMaxCampaign_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.PerformanceMaxTestProductConditionCampaignId,
                    GoogleTestAccountData.PerformanceMaxTestProductChannelCampaignId,
                    GoogleTestAccountData.PerformanceMaxTestAllProductGroupsCampaignId,
                    GoogleTestAccountData.PerformanceMaxTestSSCCampaignId,
                    GoogleTestAccountData.PerformanceMaxTestSSCAllProductGroupsCampaignId, });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleSSCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(3, submittedCounts.CampaignsAddedCount, "3 campaign adds expected");
            Assert.AreEqual(0, submittedCounts.CampaignsSkippedCount, "0 campaign skip expected (all pmax campaigns imported)");
            Assert.AreEqual(3, submittedCounts.AdgroupsAddedCount, "3 adgroup adds expected");
            Assert.AreEqual(0, submittedCounts.AdgroupsSkippedCount, "0 adgroup skip expected");
            Assert.AreEqual(3, submittedCounts.AdsAddedCount, "3 ad adds expected");
            Assert.AreEqual(0, submittedCounts.AdsSkippedCount, "0 ad skips  expected");

            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 3 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Ad Group"].Additions, "importstatistics: 3 ad group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Group"].Errors, "importstatistics: 0 ad group error expected");
            Assert.AreEqual(9, submittedCounts.EntityNameToImportStatistics["Ad Group Product Partition"].Additions, "importstatistics: 9 ad group product group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Group Product Partition"].Errors, "importstatistics: 0 ad group product group error expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Ad"].Additions, "importstatistics: 3 ad adds expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad"].Errors, "importstatistics: 0 ad errors expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        [Ignore]
        public void GoogleSyncImport_RetailPerformanceMaxCampaign_MultipleAdSet_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.PerformanceMaxTestMultipleAssetGroupCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleSSCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.CampaignsAddedCount, "1 campaign adds expected");
            Assert.AreEqual(0, submittedCounts.CampaignsSkippedCount, "0 campaign skip expected (all pmax campaigns imported)");
            Assert.AreEqual(2, submittedCounts.AdgroupsAddedCount, "2 adgroup adds expected");
            Assert.AreEqual(0, submittedCounts.AdgroupsSkippedCount, "0 adgroup skip expected");
            Assert.AreEqual(2, submittedCounts.AdsAddedCount, "2 ad adds expected");
            Assert.AreEqual(0, submittedCounts.AdsSkippedCount, "0 ad skips  expected");

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Campaign Location Criterion"].Additions, "importstatistics: 2 campaign location criterions add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Location Criterion"].Errors, "importstatistics: 0 campaign location criterions add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Ad Group"].Additions, "importstatistics: 2 ad group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Group"].Errors, "importstatistics: 0 ad group error expected");
            Assert.AreEqual(5, submittedCounts.EntityNameToImportStatistics["Ad Group Product Partition"].Additions, "importstatistics: 5 ad group product group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad Group Product Partition"].Errors, "importstatistics: 0 ad group product group error expected");
            Assert.AreEqual(2, submittedCounts.EntityNameToImportStatistics["Ad"].Additions, "importstatistics: 2 ad adds expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Ad"].Errors, "importstatistics: 0 ad errors expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        [Ignore]
        public void GoogleSyncImport_RetailPerformanceMaxCampaign_MaxConversionsTcpa_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxMaxConversionsTcpaCampaignId });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.Shopping);
            Assert.AreEqual(1, campaigns.Count);

            var campaign = campaigns[0];
            Assert.AreEqual(BiddingStrategyType.MaxConversions, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversions");

            if (campaign.BiddingScheme is MaxConversionsBiddingScheme maxConversionsBiddingScheme)
            {
                Assert.AreEqual(3, maxConversionsBiddingScheme.TargetCpa, "TargetCpa should be 3");
            }
            else
            {
                Assert.Fail("BiddingScheme type should be MaxConversionsBiddingScheme");
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        [Ignore]
        public void GoogleSyncImport_RetailPerformanceMaxCampaign_ShowPausedAdGroups_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var prefs = CreateImportUserPreference();
            prefs.ImportNewCampaignsAndChildEntities = true;

            var googleEntities = new GoogleEntities
            {
                CampaignIdsWithActiveAdGroups = new long[] { GoogleTestAccountData.PerformanceMaxTestMultipleAssetGroupCampaignId }
            };
            var fileName = SerializeAndUploadGoogleEntitiesToAzureBlob(googleEntities, customerInfo);

            var taskItemExecution =
                ImportAndGetTaskItemExecution(customerInfo, null, new JArray(), googleTestAccountId: GoogleTestAccountData.GoogleSSCTestAccountCustomerId, googleEntitiesFileName: fileName, pref: prefs);
            var statistics = GetImportStatisticsFromExecution(taskItemExecution["EntityStatistics"]);
            Assert.AreEqual(1, statistics["Campaign"].Additions);
            Assert.AreEqual(1, statistics["Campaign"].Total);
            Assert.IsTrue(statistics["Ad Group"].Additions >= 2);
            Assert.IsTrue(statistics["Ad Group"].Total >= 2);
        }
        #endregion

        #region Non-Retail
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NonRetailPerformanceMaxCampaign_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.PerformanceMaxPhaseZeroWhitelist);
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCCamapignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.CampaignsAddedCount, "1 campaign adds expected");
            Assert.AreEqual(1, submittedCounts.AdgroupsAddedCount, "1 adgroup adds expected");
            Assert.AreEqual(1, submittedCounts.AdsAddedCount, "1 ad adds expected");
        }
        
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NonRetailPerformanceMaxCampaign_NotConversionGoalAtAccount_SIOnly()
        {
            // Create a new Test customer without account level conversion goal
            var customerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCCamapignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.CampaignsSkippedCount, "1 campaign dropped as there is no conversion goal");

            var errors = importService.GetExportErrors(customerInfo, importId);
            var campaignWithBiddingStrategyError = errors.Where(x => x.Contains("ConversionGoalCriteriaNotMetForBiddingScheme"));
            Assert.AreEqual(1, campaignWithBiddingStrategyError.Count());
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NonRetailPerformanceMaxCampaign_MaxConversionValueNoTRoas_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.PerformanceMaxPhaseZeroWhitelist);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailPMCMaxConversionValueNoTroasCampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.CampaignsAddedCount, "1 campaign add expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.Default);
            Assert.AreEqual(1, campaigns.Count);

            var campaign = campaigns[0];
            Assert.AreEqual(BiddingStrategyType.MaxConversionValue, campaign.BiddingScheme.Type, "BiddingScheme should be MaxConversionValue");

            if (campaign.BiddingScheme is MaxConversionValueBiddingScheme maxConversionValueBiddingScheme)
            {
                Assert.AreEqual(2, maxConversionValueBiddingScheme.TargetRoas, "TargetRoas should be 200%");
            }
            else
            {
                Assert.Fail("BiddingScheme type should be MaxConversionValueBiddingScheme");
            }
        }
        #endregion

        [TestMethod]
        [Priority(2)]
        [DeploymentItem("ImportStaticTestFiles\\GSyncFile_CampaignNegativeWebpage_UpdateDelete.csv", "ImportStaticTestFiles")]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_NonRetail_CampaignNegativeWebpage_AddUpdateDelete()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.UrlEqualsInDSATarget);

           var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { ***********, ***********, *********** });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions,
                "importstatistics: 3 campaigns add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes,
                "importstatistics: 0 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors,
                "importstatistics: 0 campaign error expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Additions,
                "importstatistics: 3 Campaign Negative Webpages add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Changes,
                "importstatistics: 0 Campaign Negative Webpage update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Errors,
                "importstatistics: 0 Campaign Negative Webpage error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(3, campaigns.Count,
                "should not have added any campaigns - the existing one will be captured by name");

            string baseUrl = new AdCenterConfiguration().CampaignManagement.BingAdsODataApi.ServiceUri + "/V2";
            foreach (var campaign in campaigns)
            {
                if (campaign.Name.StartsWith("PMax11"))
                {
                    Assert.AreEqual(true, campaign.CampaignSettings[0] is PerformanceMaxSetting);
                    var setting = campaign.CampaignSettings[0] as PerformanceMaxSetting;
                    Assert.AreEqual(false, setting.FinalUrlExpansionOptOut);

                    var url = baseUrl +
                              string.Format(
                                  "/Customers({0})/Accounts({1})/Campaigns({2})/CampaignNegativeWebpages/Default.GetCampaignNegativeWebpages",
                                  customerInfo.CustomerId, customerInfo.AccountIds[0], campaign.Id);
                    var responseJson = ApiHelper.CallApi(
                        customerInfo,
                        c => c.GetAsync(url),
                        m => Assert.IsTrue(m.IsSuccessStatusCode));

                    Assert.AreEqual(1, responseJson["value"].Count);

                    Assert.AreEqual("Url", responseJson["value"][0]["Criterion"]["Parameter"]["Conditions"][0]["Operand"].ToString());
                    Assert.AreEqual("Equals", responseJson["value"][0]["Criterion"]["Parameter"]["Conditions"][0]["Operator"].ToString());
                    Assert.AreEqual("https://bingtest.com", responseJson["value"][0]["Criterion"]["Parameter"]["Conditions"][0]["ArgumentOperand"].ToString());
                }
                else if (campaign.Name.StartsWith("Sales-Performance Max-3"))
                {
                    Assert.AreEqual(true, campaign.CampaignSettings[0] is PerformanceMaxSetting);
                    var setting = campaign.CampaignSettings[0] as PerformanceMaxSetting;
                    Assert.AreEqual(false, setting.FinalUrlExpansionOptOut);

                    var url = baseUrl +
                              string.Format(
                                  "/Customers({0})/Accounts({1})/Campaigns({2})/CampaignNegativeWebpages/Default.GetCampaignNegativeWebpages",
                                  customerInfo.CustomerId, customerInfo.AccountIds[0], campaign.Id);
                    var responseJson = ApiHelper.CallApi(
                        customerInfo,
                        c => c.GetAsync(url),
                        m => Assert.IsTrue(m.IsSuccessStatusCode));

                    Assert.AreEqual(2, responseJson["value"].Count);
                    foreach (var response in responseJson["value"])
                    {
                        if (response["Criterion"]["Parameter"]["Conditions"].Count == 2)
                        {
                            Assert.AreEqual("Url", response["Criterion"]["Parameter"]["Conditions"][0]["Operand"].ToString());
                            Assert.AreEqual("Contains", response["Criterion"]["Parameter"]["Conditions"][0]["Operator"].ToString());
                            Assert.AreEqual("yyyy", response["Criterion"]["Parameter"]["Conditions"][0]["ArgumentOperand"].ToString());
                            Assert.AreEqual("Url", response["Criterion"]["Parameter"]["Conditions"][1]["Operand"].ToString());
                            Assert.AreEqual("Contains", response["Criterion"]["Parameter"]["Conditions"][1]["Operator"].ToString());
                            Assert.AreEqual("nnnu", response["Criterion"]["Parameter"]["Conditions"][1]["ArgumentOperand"].ToString());
                        }
                        else if (response["Criterion"]["Parameter"]["Conditions"].Count == 3)
                        {
                            Assert.AreEqual("Url", response["Criterion"]["Parameter"]["Conditions"][0]["Operand"].ToString());
                            Assert.AreEqual("Contains", response["Criterion"]["Parameter"]["Conditions"][0]["Operator"].ToString());
                            Assert.AreEqual("aaa", response["Criterion"]["Parameter"]["Conditions"][0]["ArgumentOperand"].ToString());
                            Assert.AreEqual("Url", response["Criterion"]["Parameter"]["Conditions"][1]["Operand"].ToString());
                            Assert.AreEqual("Contains", response["Criterion"]["Parameter"]["Conditions"][1]["Operator"].ToString());
                            Assert.AreEqual("bbb", response["Criterion"]["Parameter"]["Conditions"][1]["ArgumentOperand"].ToString());
                            Assert.AreEqual("Url", response["Criterion"]["Parameter"]["Conditions"][2]["Operand"].ToString());
                            Assert.AreEqual("Contains", response["Criterion"]["Parameter"]["Conditions"][2]["Operator"].ToString());
                            Assert.AreEqual("ccc", response["Criterion"]["Parameter"]["Conditions"][2]["ArgumentOperand"].ToString());
                        }
                        else
                        {
                            Assert.Fail("Expected CampaignNegativeWebpages not found in response.");
                        }
                    }
                }
                else
                {
                    Assert.AreEqual(true, campaign.CampaignSettings[0] is PerformanceMaxSetting);
                    var setting = campaign.CampaignSettings[0] as PerformanceMaxSetting;
                    Assert.AreEqual(true, setting.FinalUrlExpansionOptOut);
                }
            }

            // Delete 2 CampaignNegativeWebpages, update 1 CampaignNegativeWebpage. The updated one should be deleted and the new one is added 
            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_CampaignNegativeWebpage_UpdateDelete.csv", ImportConstants.ImportStaticTestFiles);
            var ImportService = new ImportService2();
            ImportService.FilePath = sasUrlForImport;

            ImportService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true);
            ImportService.ProcessImport(customerInfo, null, null);
            ImportService.Submit(customerInfo);

            submittedCounts = ImportService.GetSubmittedCount(customerInfo);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions,
                "importstatistics: 0 campaigns add expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes,
                "importstatistics: 3 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors,
                "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Additions,
                "importstatistics: 3 Campaign Negative Webpages add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Changes,
                "importstatistics: 0 Campaign Negative Webpage update expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Deletions,
                "importstatistics: 0 Campaign Negative Webpage error expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Errors,
                "importstatistics: 0 Campaign Negative Webpage error expected");

            //re-import, should be no update and no errors, the campaign has inactive id mapping for Campaign Negative Webpage
            ImportService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true);
            ImportService.ProcessImport(customerInfo, null, null);
            ImportService.Submit(customerInfo);

            submittedCounts = ImportService.GetSubmittedCount(customerInfo);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions,
                "importstatistics: 0 campaigns add expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes,
                "importstatistics: 3 campaign update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors,
                "importstatistics: 0 campaign error expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Additions,
                "importstatistics: 3 Campaign Negative Webpages add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Changes,
                "importstatistics: 0 Campaign Negative Webpage update expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Deletions,
                "importstatistics: 0 Campaign Negative Webpage error expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Webpage"].Errors,
                "importstatistics: 0 Campaign Negative Webpage error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_LocationRadiusAdScheduleTarget()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.UrlEqualsInDSATarget);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { *********** });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            //1 location target, 1 location exclusion, 1 radius target, 1 ad schedule target
            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign Location Criterion"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Location Criterion"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Location Criterion"].Errors);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign Negative Location Criterion"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Location Criterion"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Negative Location Criterion"].Errors);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign Radius Criterion"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Radius Criterion"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign Radius Criterion"].Errors);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign DayTime Criterion"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign DayTime Criterion"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign DayTime Criterion"].Errors);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_SearchTheme()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { *********** });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxCampaignAssetGroupSearchTheme);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
            Assert.AreEqual(5, submittedCounts.EntityNameToImportStatistics["Asset Group Search Theme"].Additions, "importstatistics: 5 asset group search theme add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group Search Theme"].Errors, "importstatistics: 0 asset group search theme error expected");
            Assert.AreEqual(5, submittedCounts.EntityNameToImportStatistics["Asset Group Search Theme"].Total, "importstatistics: 5 asset group search theme total expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            var searchThemes = assetgroups[0].AssetGroupSearchThemes;
            Assert.AreEqual(5, searchThemes.Length, "5 search themes expected");
            Assert.IsTrue(!string.IsNullOrEmpty(searchThemes[0].SearchTheme), "search theme should not be empty");


            // reimport, delete 2 search themes, add 1 search themes
            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile-SearchThemeImport-Update.csv", ImportConstants.ImportStaticTestFiles);
            var importService2 = new ImportService2();
            importService2.FilePath = sasUrlForImport;

            var importId2 = importService2.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true).Value;
            importService2.ProcessImport(customerInfo, null, null, overrideConfigValuesFromTest: new Dictionary<string, string> { { "OnePassImportForceOnForTest", "true" }, { "NewGSyncFileFlagOverride", "true" } });
            importService2.Submit(customerInfo, importId2);
            var submittedCounts2 = importService2.GetSubmittedCount(customerInfo, importId2);

            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group update expected");
            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Asset Group Search Theme"].Additions, "importstatistics: 1 asset group search theme add expected");
            Assert.AreEqual(2, submittedCounts2.EntityNameToImportStatistics["Asset Group Search Theme"].Deletions, "importstatistics: 2 asset group search themes delete expected");
        }

        /// <summary>
        /// The test verfies the merge logic for images in asset group.
        /// Only images created or edited by import should be modified. All other images should be left as is.
        /// </summary>
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupMerge()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA
                );

            // First import has one extra association and one fewer association than the actual Google Account. So re-import from Google should add/delete it.
            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_PMax_AssetMerge.csv", ImportConstants.ImportStaticTestFiles);
            var importService = new ImportService2();
            importService.FilePath = sasUrlForImport;

            var importId = importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true).Value;
            importService.ProcessImport(customerInfo, null, null, overrideConfigValuesFromTest: new Dictionary<string, string> { { "OnePassImportForceOnForTest", "true" }, { "NewGSyncFileFlagOverride", "true" } });
            importService.Submit(customerInfo, importId);
            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);

            Assert.AreEqual(10, assetgroups[0].Images.Count(), "# of imported images doesn't match");

            var imagesIds = ResponsiveAdTestHelper.CreateImagesForAssetGroup(customerInfo, customerInfo.AccountIds[0], createLogo: true);

            // Create manually generated asset links. Note that this has 4 logos, combined with 1 from Google makes it at limit on update.
            var imagesToAddManually = new AssetLink[]
                    {
                        new AssetLink() {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[0],
                                AssetAssociationType = AssetAssociationType.LandscapeImageMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[1],
                                AssetAssociationType = AssetAssociationType.SquareImageMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[2],
                                AssetAssociationType = AssetAssociationType.SquareLogoMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[0],
                                AssetAssociationType = AssetAssociationType.LandscapeLogoMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[4],
                                AssetAssociationType = AssetAssociationType.SquareLogoMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[5],
                                AssetAssociationType = AssetAssociationType.LandscapeLogoMedia,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[1],
                                AssetAssociationType = AssetAssociationType.ImageMedia15X10,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[2],
                                AssetAssociationType = AssetAssociationType.ImageMedia133X100,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[0],
                                AssetAssociationType = AssetAssociationType.ImageMedia178X100,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[1],
                                AssetAssociationType = AssetAssociationType.ImageMedia1X2,
                            }
                        },
                        new AssetLink
                        {
                            Asset = new ImageAsset
                            {
                                Type = AssetType.Image,
                                Id = imagesIds[2],
                                AssetAssociationType = AssetAssociationType.ImageMedia4X1,
                            }
                        },
                    };

            var manuallyAddedImagesHashSet = new HashSet<(long, AssetAssociationType)>(imagesToAddManually.Select(x => (ImageAsset)x.Asset).Select(x => (x.Id, x.AssetAssociationType)));

            var campaignCollection = new TestCampaignCollection(1);
            var assetGroupCollection = new TestAssetGroupCollection(campaignCollection.FetchByCampaignId(customerInfo, campaigns[0].Id), customerInfo, 1, true);
            assetGroupCollection.FetchByCampaign(customerInfo);
            // Delete one logo image so re-import tries to create it and reaches the limit.
            var importImagesWithouSquareLogo = assetGroupCollection.AssetGroups[0].Data.Images.Where(x => ((ImageAsset)x.Asset).AssetAssociationType != AssetAssociationType.SquareLogoMedia);
            assetGroupCollection.AssetGroups[0].Data.Images = importImagesWithouSquareLogo.Concat(imagesToAddManually).ToArray();
            ResponseValidator.ValidateBasicBatchSuccess(assetGroupCollection.Update(this.customerInfo));

            assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetgroups.Count, "Asset group fetch by campaign id result count mismatch");
            Assert.AreEqual(20, assetgroups[0].Images.Count(), "# of images after update mismatch");

            // re-import, should add 1 square image, but no logo images as logo limit has reached. Will also delete the extra association.
            var adWordsImportContext = ImportTestHelper.CreateAdWordsImportContext(
                  customerInfo,
                  campaignIds: new JArray { GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxNoCTACampaignId } ,
                  googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                  googleAccountName: GoogleAccountNameForE2EIntegration2,
                  refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                customerInfo,
                cronStr: CRON_RUNNOW,
                ruleName: "NativePerformanceMaxCampaign_AssetGroupMerge",
                notificationType: V2.NotificationType.None);

            var response = PostAccountTaskItem(customerInfo, importTaskItem);
            Assert.IsNotNull(importTaskItem.Id, "Cannot get task Id from response");

            var taskItemExecutions = PollImportTaskItemExecutionsWithTimeout(
                customerInfo,
                filter: GoogleImportTaskFilter);
            Assert.IsNotNull(taskItemExecutions, "TaskItemExecutions should not be null");
            Assert.IsTrue(taskItemExecutions.value.Count > 0, "TaskItemExecutions count should be greater than 0");
            var taskItemExecution = taskItemExecutions.value[0];

            VerifyAPIImportTaskItemExecution(taskItemExecution,
                (long)importTaskItem.Id,
                  googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                  googleAccountName: GoogleAccountNameForE2EIntegration2,
                  expectedTaskName : "",
                expectedImportEntityStatistics: null);

            var rerunImportId = (long)taskItemExecution.ImportId;

            var submittedCounts2 = importService.GetSubmittedCount(customerInfo, rerunImportId);

            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group update expected");
            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");
            
            campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);

            var updatedImagesHashSet = assetgroups[0].Images.Select(x => (ImageAsset)x.Asset).Select(x => (x.Id, x.AssetAssociationType)).ToHashSet();
          
            // No change in value as 1 image was added instead of 2 (because logo limit reached) and 1 image was deleted.
            Assert.AreEqual(20, updatedImagesHashSet.Count(), "# of images after re-import mismatch");

            // Validate all customer created images are still present.
            foreach (var image in manuallyAddedImagesHashSet)
            {
                Assert.IsTrue(updatedImagesHashSet.Contains(image));
            }
        }

        /// <summary>
        /// The test verfies the deduplication logic for image links in asset group.
        /// If two links are pointing to the same image and they share the same cropping setting, only one link should be present.
        /// </summary>
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_DuplicateImageIdMerge()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA
                );

            // First import has one extra association and one fewer association than the actual Google Account. So re-import from Google should add/delete it.
            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_PMax_DuplicateImageIdMerge.csv", ImportConstants.ImportStaticTestFiles);
            var importService = new ImportService2();
            importService.FilePath = sasUrlForImport;

            var importId = importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true).Value;
            importService.ProcessImport(customerInfo, null, null, overrideConfigValuesFromTest: new Dictionary<string, string> { { "OnePassImportForceOnForTest", "true" }, { "NewGSyncFileFlagOverride", "true" } });
            importService.Submit(customerInfo, importId);
            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);

            // 11 images. One is duplicate
            Assert.AreEqual(10, assetgroups[0].Images.Count(), "# of imported images doesn't match");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_AssetGroupTextAssetsMerge()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA
                );

            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile_PMax_TextAssetsMerge.csv", ImportConstants.ImportStaticTestFiles);
            var importService = new ImportService2();
            importService.FilePath = sasUrlForImport;

            var importId = importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true).Value;
            importService.ProcessImport(customerInfo, null, null, overrideConfigValuesFromTest: new Dictionary<string, string> { { "OnePassImportForceOnForTest", "true" }, { "NewGSyncFileFlagOverride", "true" } });
            importService.Submit(customerInfo, importId);
            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 1 asset group add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);

            Assert.AreEqual(4, assetgroups[0].LongHeadlines.Count(), "# of imported LongHeadlines doesn't match");
            Assert.AreEqual(4, assetgroups[0].Descriptions.Count(), "# of imported Descriptions doesn't match");
            Assert.AreEqual(14, assetgroups[0].Headlines.Count(), "# of imported Headlines doesn't match");

            var campaignCollection = new TestCampaignCollection(1);
            var assetGroupCollection = new TestAssetGroupCollection(campaignCollection.FetchByCampaignId(customerInfo, campaigns[0].Id), customerInfo, 1, true);
            assetGroupCollection.FetchByCampaign(customerInfo);

            // update asset group and include new text assets from api
            var newLongHeadlines = assetGroupCollection.AssetGroups[0].Data.LongHeadlines.Append(
                new AssetLink() { AssociationType=AssetAssociationType.LongHeadlines, Asset=new TextAsset() { Id = 0, Text = "Test long headline from API" } }
            );
            assetGroupCollection.AssetGroups[0].Data.LongHeadlines = newLongHeadlines.ToArray();
            var newDescs = assetGroupCollection.AssetGroups[0].Data.Descriptions.Append(
                new AssetLink() { AssociationType = AssetAssociationType.Descriptions, Asset = new TextAsset() { Id = 0, Text = "Test description from API" } }
            );
            assetGroupCollection.AssetGroups[0].Data.Descriptions = newDescs.ToArray();
            var newHeadlines = assetGroupCollection.AssetGroups[0].Data.Headlines.Append(
                new AssetLink() { AssociationType = AssetAssociationType.Headlines, Asset = new TextAsset() { Id = 0, Text = "Test hl from API" } }
            );
            assetGroupCollection.AssetGroups[0].Data.Headlines = newHeadlines.ToArray();
            ResponseValidator.ValidateBasicBatchSuccess(assetGroupCollection.Update(this.customerInfo));

            assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetgroups.Count, "Asset group fetch by campaign id result count mismatch");
            Assert.AreEqual(5, assetgroups[0].LongHeadlines.Count(), "# of LongHeadlines after update mismatch");
            Assert.AreEqual(5, assetgroups[0].Descriptions.Count(), "# of Descriptions after update mismatch");
            Assert.AreEqual(15, assetgroups[0].Headlines.Count(), "# of Headlines after update mismatch");

            // re-import, use odata to keep sourceid=11, should add 1 long headline, 1 description, 1 headline
            var adWordsImportContext = ImportTestHelper.CreateAdWordsImportContext(
                  customerInfo,
                  campaignIds: new JArray { *********** },
                  googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                  googleAccountName: GoogleAccountNameForE2EIntegration2,
                  refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                customerInfo,
                cronStr: CRON_RUNNOW,
                ruleName: "NativePerformanceMaxCampaign_AssetGroupTextAssetsMerge",
                notificationType: V2.NotificationType.None);

            var response = PostAccountTaskItem(customerInfo, importTaskItem);
            Assert.IsNotNull(importTaskItem.Id, "Cannot get task Id from response");

            var taskItemExecutions = PollImportTaskItemExecutionsWithTimeout(
                customerInfo,
                filter: GoogleImportTaskFilter);
            Assert.IsNotNull(taskItemExecutions, "TaskItemExecutions should not be null");
            Assert.IsTrue(taskItemExecutions.value.Count > 0, "TaskItemExecutions count should be greater than 0");
            var taskItemExecution = taskItemExecutions.value[0];

            VerifyAPIImportTaskItemExecution(taskItemExecution,
                (long)importTaskItem.Id,
                  googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                  googleAccountName: GoogleAccountNameForE2EIntegration2,
                  expectedTaskName: "",
                expectedImportEntityStatistics: null);

            var importId2 = (long)taskItemExecution.ImportId;

            var submittedCounts2 = importService.GetSubmittedCount(customerInfo, importId2);

            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Additions, "importstatistics: 0 asset group add expected");
            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Changes, "importstatistics: 1 asset group update expected");
            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Asset Group"].Errors, "importstatistics: 0 asset group error expected");

            campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            assetgroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);

            Assert.AreEqual(5, assetgroups[0].LongHeadlines.Count(), "# of LongHeadlines after re-import mismatch");
            Assert.AreEqual(5, assetgroups[0].Descriptions.Count(), "# of Descriptions after re-import mismatch");
            Assert.AreEqual(15, assetgroups[0].Headlines.Count(), "# of Headlines after re-import mismatch");

            // Validate all customer created text assets are still present.
            var textAssets = assetgroups[0].LongHeadlines.Concat(assetgroups[0].Descriptions).Concat(assetgroups[0].Headlines).Select(x => (TextAsset)x.Asset).Select(x => x.Text).ToHashSet();
            Assert.IsTrue(textAssets.Contains("Test long headline from API"));
            Assert.IsTrue(textAssets.Contains("Test description from API"));
            Assert.IsTrue(textAssets.Contains("Test hl from API"));
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_BrandItemImport_DuplicateBrand()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA
                );

            // 5 brand items but 3 duplicated, 5 brand lists, 3 campaigns
            string sasUrlForImport = Import2Helper.copyFileFromStore("GSyncFile-duplicate-branditem.csv", ImportConstants.ImportStaticTestFiles);
            var importService = new ImportService2();
            importService.FilePath = sasUrlForImport;

            var importId = importService.StartImport(customerInfo, customerInfo.AccountIds[0], action: r => r.ImportType = ImportType.GoogleAPIImport, isGsyncFileImport: true).Value;
            importService.ProcessImport(customerInfo, null, null, overrideConfigValuesFromTest: new Dictionary<string, string> { { "OnePassImportForceOnForTest", "true" }, { "NewGSyncFileFlagOverride", "true" } });
            importService.Submit(customerInfo, importId);
            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 3 campaign add expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 3 campaign total expected");
            Assert.AreEqual(3, submittedCounts.EntityNameToImportStatistics["Brand Item"].Additions, "importstatistics: 3 brand item add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Brand Item"].Errors, "importstatistics: 0 brand item error expected");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_NonRetailDefaultCTA()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA
                );

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxNoCTACampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should have added one asset groups");

            var CTA = assetGroups[0].CallToAction;
            Assert.AreEqual(CallToAction.Automated, CTA);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_NativePerformanceMaxCampaign_RetailDefaultCTA()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.MaxConversionForSSC,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxAutomatedCTA);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GoogleRetailPerformanceMaxNoCTACampaignId });

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleRetailPerformanceMaxCustomerId,
                googleCampaignIds: null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should have added one asset groups");

            var CTA = assetGroups[0].CallToAction;
            Assert.AreEqual(CallToAction.Automated, CTA);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_PMax_ValidateTargetCpaLargerThanDailyBudget_ErrorInErrorFile_SIOnly()
        {
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { *********** });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxCampaignAssetGroupSearchTheme);

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                GoogleTestAccountData.GoogleNativePMAxSearchAndReplaceCustomerId,
                null,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var filePath = new ImportService2().ExportImportedEntities(
                customerInfo,
                importId,
                request => { request.ImportEntityFilter = ContentsFilter.Errors; });
            var fileContent = FileUtil.DownloadFile(filePath);
            var errorMes = "Your campaign's total cost per acquisition is higher than the daily budget, which could prevent you from receiving any conversions. Please consider lowering your total cost per acquisition or raising your daily budget.";
            Assert.IsTrue(fileContent.Contains(errorMes), "Campaign warning");
        }

        /// <summary>
        /// Should honor search and replace options for final urls and mobile final urls
        /// UTM_Source should be replaced automatically 
        /// Google Import Pmax Final Url & mobile Final Url Utm Params should be removed
        /// Enhance Pmax Listings update from HTTP:// to HTTPS://
        /// </summary>
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_PMax_FinalURLReplace_AndSuffix_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                    false,
                    Features.SmartShoppingCampaign,
                    Features.AutoBiddingMaxConversionValueEnabled,
                    Features.MaxConversionForSSC,
                    Features.PerformanceMaxCampaigns,
                    Features.PerformanceMaxAutomatedCTA, 
                    AccountPilotFeatures.PMaxEnhanceListings);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { *********** });

            var importUserPreference = ImportService2.GetAllTrueImportPrefNew();
            importUserPreference.SearchAndReplaceForUrls = new ImportSearchAndReplaceForStringProperty
            {
                EnableSearchAndReplace = true,
                SearchString = "google",
                ReplaceString = "bing"
            };

            importUserPreference.PrefixAndSuffixForUrls = new ImportPrefixAndSuffixForStringProperty
            {
                EnablePrefixAndSuffix = true,
                PrefixString = "",
                SuffixString = "&test"
            };

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                GoogleTestAccountData.GoogleNativePMAxSearchAndReplaceCustomerId,
                null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Changes);
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should have added one asset groups");

            var finalUrls = assetGroups[0].FinalUrls;
            var mobileFinalUrls = assetGroups[0].MobileFinalUrls;
            Assert.AreEqual(1, finalUrls.Count(), "should have one final url");
            Assert.AreEqual(1, mobileFinalUrls.Count(), "should have one mobile final url");
            // Original URL: contoso.com/find?utm_source=google_pmax
            Assert.AreEqual("https://contoso.com/find?test", finalUrls[0], "Import options and auto utm_source removal wasn't honored in Final Urls");
            // Original URL: m.contoso.com/find?utm_source=google_pmax
            Assert.AreEqual("https://m.contoso.com/find?test", mobileFinalUrls[0], "Import options and auto utm_source removal wasn't honored in Mobile Final Urls");

            var errors = importService.GetExportErrors(customerInfo, importId);
            var EnhanceHTTPError = errors.Where(x => x.Contains("PerformanceMaxEnhanceHTTPFinalURLEnabled"));
            Assert.AreEqual(1, EnhanceHTTPError.Count());
            var AssetGroupTrackingParamError = errors.Where(x => x.Contains("PerformanceMaxAssetGroupFinalURLExtractTrackingParametersEnabled"));
            Assert.AreEqual(1, AssetGroupTrackingParamError.Count());
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_PMax_FinalURL_RemoveUTMParams_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                    false,
                    Features.SmartShoppingCampaign,
                    Features.AutoBiddingMaxConversionValueEnabled,
                    Features.MaxConversionForSSC,
                    Features.PerformanceMaxCampaigns,
                    Features.PerformanceMaxAutomatedCTA);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMAXFinalUrlRemoveUTMCampaignId });

            var importUserPreference = ImportService2.GetAllTrueImportPrefNew();

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                GoogleTestAccountData.GooglePMAXFinalUrlRemoveUTMAccountId,
                null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken3);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should have added one asset groups");

            var finalUrls = assetGroups[0].FinalUrls;
            var mobileFinalUrls = assetGroups[0].MobileFinalUrls;
            Assert.AreEqual(1, finalUrls.Count(), "should have one final url");
            Assert.AreEqual(1, mobileFinalUrls.Count(), "should have one mobile final url");
            // finalURL: https://www.contoso.com/find?utm_source=google&utm_medium=cpc&test&utm_campaign=xy 
            Assert.AreEqual("https://www.contoso.com/find?test", finalUrls[0], "Pmax remove UTM params was not honored in Final Urls");
            // Original URL:  https://www.contoso.com/find?utm_source=google&utm_medium=cpc&test&utm_campaign=xy
            Assert.AreEqual("https://www.m.contoso.com/find?test", mobileFinalUrls[0], "Pmax remove UTM params was not honored in Mobile Final Urls");

            var errors = importService.GetExportErrors(customerInfo, importId);
            var AssetGroupTrackingParamError = errors.Where(x => x.Contains("PerformanceMaxAssetGroupFinalURLExtractTrackingParametersEnabled"));
            Assert.AreEqual(1, AssetGroupTrackingParamError.Count());
        }

        [TestMethod]
        public void GoogleSyncImport_PMax_FinalURL_RemoveParam_EnhanceHTTP_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                    false,
                    Features.SmartShoppingCampaign,
                    Features.AutoBiddingMaxConversionValueEnabled,
                    Features.MaxConversionForSSC,
                    Features.PerformanceMaxCampaigns,
                    Features.PerformanceMaxAutomatedCTA,
                    AccountPilotFeatures.PMaxEnhanceListings);

            var adWordsImportContext = CreateAdWordsImportContext(
                customerInfo,
                new JArray { *********** },
                GoogleTestAccountData.GoogleNativePMAxSearchAndReplaceCustomerId,
                RemarketingTestAccountName2,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importUserPreference = ImportService2.GetAllTrueImportPrefNew();
            importUserPreference.SearchAndReplaceForUrls = new ImportSearchAndReplaceForStringProperty
            {
                EnableSearchAndReplace = true,
                SearchString = "google",
                ReplaceString = "bing"
            };
            importUserPreference.PrefixAndSuffixForUrls = new ImportPrefixAndSuffixForStringProperty
            {
                EnablePrefixAndSuffix = true,
                PrefixString = "",
                SuffixString = "&test"
            };
            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                customerInfo,
                importUserPreference: importUserPreference,
                cronStr: CRON_RUNNOW,
                notificationType: NotificationType.None);
            PostAccountTaskItem(customerInfo, importTaskItem, expand: "TaskItemExecutions");

            var executions = PollImportTaskItemExecutionsWithTimeout(customerInfo, filter: GoogleImportTaskFilter);
            Assert.AreEqual(1, executions.value.Count);

            var taskItemExecution = executions.value[0];
            Assert.IsNotNull(taskItemExecution, "Execution for the import should not be null.");

            var actualStatisticsForFirstImport = ConvertEntityStatistics(taskItemExecution.EntityStatistics);
            VerifyImportEntityStatisticsForGsyncImport(actualStatisticsForFirstImport, AssetGroup, additions: 1, total: 1);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_PMax_FinalURL_RemoveTrackingParams_SIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                    false,
                    Features.SmartShoppingCampaign,
                    Features.AutoBiddingMaxConversionValueEnabled,
                    Features.MaxConversionForSSC,
                    Features.PerformanceMaxCampaigns,
                    Features.PerformanceMaxAutomatedCTA);

            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { GoogleTestAccountData.GooglePMAXFinalUrlREmoveTrackingParamsCampaignId });

            var importUserPreference = ImportService2.GetAllTrueImportPrefNew();

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                GoogleTestAccountData.GooglePMAXFinalUrlRemoveTrackingParamsAccountId,
                null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken3);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            Assert.AreEqual(1, campaigns.Count, "should have one campaign");

            var assetGroups = Import2Helper.FetchAssetGroupInfo(customerInfo, campaigns[0].Id);
            Assert.AreEqual(1, assetGroups.Count, "should have added one asset groups");

            var finalUrls = assetGroups[0].FinalUrls;
            var mobileFinalUrls = assetGroups[0].MobileFinalUrls;
            Assert.AreEqual(1, finalUrls.Count(), "should have one final url");
            Assert.AreEqual(1, mobileFinalUrls.Count(), "should have one mobile final url");
            // finalURL: https://bing.com/shop/?msclkid={msclkid}&AdId={AdId}&BidMatchType={BidMatchType}&CampaignId={CampaignId}&TargetId={TargetId}&QueryString={QueryString}&IfSearch:string={IfSearch:string}&keyword={keyword}&MatchType={MatchType}&AdGroupId={AdGroupId}&cpid=605f7734-4b96-4887-a24b-9ec7dc2828e3&lpid=9ff0863f-e238-4112-bc0d-f16869e0cc11
            Assert.AreEqual("https://bing.com/shop/?cpid=605f7734-4b96-4887-a24b-9ec7dc2828e3&lpid=9ff0863f-e238-4112-bc0d-f16869e0cc11", finalUrls[0], "Pmax remove tracking params was not honored in Final Urls");
            Assert.AreEqual("https://m.bing.com/shop/?cpid=605f7734-4b96-4887-a24b-9ec7dc2828e3&lpid=9ff0863f-e238-4112-bc0d-f16869e0cc11", mobileFinalUrls[0], "Pmax remove tracking params was not honored in Mobile Final Urls");

            var errors = importService.GetExportErrors(customerInfo, importId);
            var AssetGroupTrackingParamError = errors.Where(x => x.Contains("PerformanceMaxAssetGroupFinalURLExtractTrackingParametersEnabled"));
            Assert.AreEqual(1, AssetGroupTrackingParamError.Count());
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void GoogleSyncImport_PMax_ImportOptions_AutoGeneratedAsset_SIOnly()
        {
            // one campaign with auto generated assets opt-out True
            var fileName = GoogleSyncIntegrationTestBase.CreateAndUploadCampaignIdBlobFile(new long[] { 22675434624 });

            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId,
                false,
                Features.SmartShoppingCampaign,
                Features.AutoBiddingMaxConversionValueEnabled,
                Features.PerformanceMaxCampaigns,
                Features.PerformanceMaxCampaignAssetGroupSearchTheme,
                Features.PerformanceMaxCampaignLevelAutoGenAssetsControl);

            var importUserPreference = ImportService2.GetAllTrueImportPrefNew();
            importUserPreference.UpdateAssetAutomationCampaignSetting = false;

            var importId = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService = new ImportService2();
            importService.Submit(customerInfo, importId);

            var submittedCounts = importService.GetSubmittedCount(customerInfo, importId);

            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Additions, "importstatistics: 1 campaign add expected");
            Assert.AreEqual(0, submittedCounts.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");
            
            var campaigns = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var pmaxSetting = (PerformanceMaxSetting) campaigns[0].CampaignSettings.First(s => s is PerformanceMaxSetting);
            // For creation, always sync google
            Assert.IsTrue(pmaxSetting.AutoGeneratedImageOptOut, "AutoGeneratedImageOptOut should be true");
            Assert.IsTrue(pmaxSetting.AutoGeneratedTextOptOut, "AutoGeneratedTextOptOut should be true");


            // reimport
            importUserPreference.UpdateAssetAutomationCampaignSetting = true;
            var importId2 = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService2 = new ImportService2();
            importService2.Submit(customerInfo, importId2);

            var submittedCounts2 = importService2.GetSubmittedCount(customerInfo, importId2);

            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign change expected");
            Assert.AreEqual(0, submittedCounts2.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts2.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");

            var campaigns2 = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var pmaxSetting2 = (PerformanceMaxSetting)campaigns2[0].CampaignSettings.First(s => s is PerformanceMaxSetting);
            Assert.IsTrue(pmaxSetting2.AutoGeneratedImageOptOut, "AutoGeneratedImageOptOut should be true");
            Assert.IsTrue(pmaxSetting2.AutoGeneratedTextOptOut, "AutoGeneratedTextOptOut should be true");


            // Update the campaign to set AutoGeneratedImageOptOut to false and set UpdateAssetAutomationCampaignSetting to false
            pmaxSetting2.AutoGeneratedImageOptOut = false;
            Import2Helper.UpdateCampaign(customerInfo, new CampaignUpdate { Id = campaigns2[0].Id, CampaignSettings = new CampaignSettings[] { pmaxSetting2 } });
            importUserPreference.UpdateAssetAutomationCampaignSetting = false;
            // reimport
            var importId3 = GoogleSyncIntegrationTests.StartAndProcessGoogleImport(
                customerInfo,
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailNativePerformanceMaxCustomerId,
                googleCampaignIds: null,
                importUserPreference: importUserPreference,
                campaignIdBlobFile: fileName,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);

            var importService3 = new ImportService2();
            importService3.Submit(customerInfo, importId3);

            var submittedCounts3 = importService3.GetSubmittedCount(customerInfo, importId3);
            Assert.AreEqual(1, submittedCounts3.EntityNameToImportStatistics["Campaign"].Changes, "importstatistics: 1 campaign change expected");
            Assert.AreEqual(0, submittedCounts3.EntityNameToImportStatistics["Campaign"].Errors, "importstatistics: 0 campaign error expected");
            Assert.AreEqual(1, submittedCounts3.EntityNameToImportStatistics["Campaign"].Total, "importstatistics: 1 campaign total expected");

            var campaigns3 = Import2Helper.FetchCampaignInfo(customerInfo, CampaignType.PerformanceMax);
            var pmaxSetting3 = (PerformanceMaxSetting)campaigns3[0].CampaignSettings.First(s => s is PerformanceMaxSetting);
            // no sync as AutoGeneratedImageOptOut is still false
            Assert.IsFalse(pmaxSetting3.AutoGeneratedImageOptOut, "AutoGeneratedImageOptOut should be false");
            Assert.IsTrue(pmaxSetting3.AutoGeneratedTextOptOut, "AutoGeneratedTextOptOut should be true");
        }

        #region PMax Import Recommendation

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void Import_GoogleApi_PMaxRecommendationTask_ValidateImportUserPreference_NoPaxCampaignImported()
        {
            var cInfo = CustomerInfo.CreateStandardAdvertiser();
            DatabaseHelper.AddPilotFeatureToCustomer(cInfo.CustomerId, new List<int> { Features.WhitelistSTA });

            var taskName = "PMaxRecommendationTask";
            var adWordsImportContext = ImportTestHelper.CreateAdWordsImportContext(
                cInfo,
                campaignIds: null,
                googleCustomerId: GoogleCustomerIdForE2EIntegration2,
                googleAccountName: GoogleAccountNameForE2EIntegration2,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);
            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                cInfo,
                ruleName: taskName,
                cronStr: CRON_RUNONCE_PLACEHOLDER,
                notificationType: V2.NotificationType.None,
                importUserPreference: null,
                useDefaultImportUserPreference: false);

            importTaskItem.IsSmartImport = true;
            importTaskItem.IsAutoFrequency = false;
            var defaultImportSettings = "PMaxRecommendation";
            importTaskItem.DefaultImportSettings = defaultImportSettings;

            // calculate expected cron string
            var utcTimeToRun = DateTime.UtcNow.AddHours(24).AddSeconds(1);
            var timeToRun = utcTimeToRun.UtcToTimeZone((int?)importTaskItem.TimeZoneId);
            var ExpectedCron = $"0 {timeToRun.Hour} {timeToRun.Day} {timeToRun.Month} * {timeToRun.Year}";

            var response = PostAccountTaskItem(cInfo, importTaskItem);
            Assert.IsNotNull(importTaskItem.Id, "Cannot get task Id from response");

            var taskItem = GetAccountTaskItemById(cInfo, importTaskItem.Id);
            Assert.IsNotNull(taskItem, "TaskItem should not be null");
            Assert.IsTrue((bool)taskItem.IsSmartImport);
            Assert.IsFalse((bool)taskItem.IsAutoFrequency);
            Assert.AreEqual(defaultImportSettings, (string)taskItem.DefaultImportSettings);
            Assert.AreEqual(taskName, (string)taskItem.Name);
            Assert.AreEqual(ExpectedCron, (string)taskItem.Cron, "Unexpected Cron. The schedule should be 24 hours later (run-once).");
            Assert.IsFalse(string.IsNullOrEmpty(Convert.ToString(taskItem.ImportUserPreference)), "ImportUserPreference needs to be filled up for back compatibility");
            Assert.IsFalse(string.IsNullOrEmpty(Convert.ToString(taskItem.ImportUserPreferences)), "ImportUserPreferences needs to be filled up for back compatibility");
            // PMaxRecommendation import will disable entity update and deletion
            Assert.IsFalse((bool)taskItem.ImportUserPreference.ImportUpdatedEntities);
            Assert.IsFalse((bool)taskItem.ImportUserPreference.ImportDeletedEntities);
            Assert.IsTrue((bool)taskItem.ImportUserPreference.ImportNewEntities);
            Assert.IsTrue((bool)taskItem.ImportUserPreference.SetLowerBidsToAdCenterMinBids);

            // Update Scheduled time to let task engine execute the task early
            string updateRuleExecutionQueueQuery = $"update RuleExecutionQueue set NextRunDTim = '{DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")}' where ruleid= {importTaskItem.Id} and CustomerId = {cInfo.CustomerId} and PluginTypeID = 24 and ExecutionStatusid = 1";
            DBHelper.ExecuteQueryInCampaignBulkDB(cInfo.CustomerId, updateRuleExecutionQueueQuery);

            // verify the import results
            var taskItemExecutions = PollImportTaskItemExecutionsWithTimeout(
                cInfo,
                filter: GoogleImportTaskFilter);
            Assert.IsNotNull(taskItemExecutions, "TaskItemExecutions should not be null");
            Assert.IsTrue(taskItemExecutions.value.Count > 0, "TaskItemExecutions count should be greater than 0");
            var taskItemExecution = taskItemExecutions.value[0];
            VerifyAPIImportTaskItemExecution(taskItemExecution,
                (long)importTaskItem.Id,
                googleCustomerId: GoogleCustomerIdForE2EIntegration2,
                googleAccountName: GoogleAccountNameForE2EIntegration2,
                expectedTaskName: taskName,
                expectedIsScheduled: true,
                expectedImportEntityStatistics: null); // We don't check statistics as smart import may change the preference in the future.

            // verify the import notification
            response = ApiHelper.CallApi(
                cInfo,
                c => c.GetAsync($"{ApiVersion.BaseUrl}/Customers({cInfo.CustomerId})/Accounts({cInfo.AccountIds[0]})/ImportNotifications"),
                e => Assert.AreEqual(true, e.IsSuccessStatusCode));
            var completedWithoutPMaxTask = response.PMaxImportRecommendation.CompletedWithoutPMax[0].Tasks[0];
            Assert.IsNotNull(completedWithoutPMaxTask, "PMaxImportRecommendation notification should not be null.");
            Assert.AreEqual(((long)importTaskItem.Id).ToString(), (string)completedWithoutPMaxTask.TaskId, "TaskId should be the same.");
            Assert.AreEqual(taskName, (string)completedWithoutPMaxTask.TaskName, "TaskName should be the same.");
            Assert.AreEqual(GoogleAccountNameForE2EIntegration2, (string)completedWithoutPMaxTask.GoogleUserEmail, "GoogleUserEmail should be the same.");
            Assert.AreEqual(GoogleCustomerIdForE2EIntegration2.ToString(), (string)completedWithoutPMaxTask.GoogleAdsCustomerId, "GoogleCustomerId should be the same.");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void Import_GoogleApi_PMaxRecommendationTask_HasPMaxCampaignImported()
        {
            var cInfo = CustomerInfo.CreateStandardAdvertiser();
            DatabaseHelper.AddPilotFeatureToCustomer(cInfo.CustomerId, new List<int> { Features.WhitelistSTA });

            var taskName = "PMaxRecommendationTask";
            var adWordsImportContext = ImportTestHelper.CreateAdWordsImportContext(
                cInfo,
                campaignIds: new JArray { },
                googleCustomerId: GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId,
                googleAccountName: GoogleAccountNameForE2EIntegration2,
                refreshToken: GSyncTestConsts.GSyncRefreshToken2);
            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                cInfo,
                ruleName: taskName,
                cronStr: CRON_RUNONCE_PLACEHOLDER,
                notificationType: V2.NotificationType.None,
                importUserPreference: null,
                useDefaultImportUserPreference: false);

            importTaskItem.IsSmartImport = true;
            importTaskItem.IsAutoFrequency = false;
            var defaultImportSettings = "PMaxRecommendation";
            importTaskItem.DefaultImportSettings = defaultImportSettings;

            var response = PostAccountTaskItem(cInfo, importTaskItem);
            Assert.IsNotNull(importTaskItem.Id, "Cannot get task Id from response");

            // Update Scheduled time to let task engine execute the task early
            string updateRuleExecutionQueueQuery = $"update RuleExecutionQueue set NextRunDTim = '{DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")}' where ruleid= {importTaskItem.Id} and CustomerId = {cInfo.CustomerId} and PluginTypeID = 24 and ExecutionStatusid = 1";
            DBHelper.ExecuteQueryInCampaignBulkDB(cInfo.CustomerId, updateRuleExecutionQueueQuery);

            // verify the import results
            var taskItemExecutions = PollImportTaskItemExecutionsWithTimeout(cInfo, filter: GoogleImportTaskFilter);
            Assert.IsNotNull(taskItemExecutions, "TaskItemExecutions should not be null");

            // verify the import notification
            response = ApiHelper.CallApi(
                cInfo,
                c => c.GetAsync($"{ApiVersion.BaseUrl}/Customers({cInfo.CustomerId})/Accounts({cInfo.AccountIds[0]})/ImportNotifications"),
                e => Assert.AreEqual(true, e.IsSuccessStatusCode));
            var completedTask = response.PMaxImportRecommendation.Completed[0].Tasks[0];
            Assert.IsNotNull(completedTask, "PMaxImportRecommendation notification should not be null.");
            Assert.AreEqual(((long)importTaskItem.Id).ToString(), (string)completedTask.TaskId, "TaskId should be the same.");
            Assert.AreEqual(taskName, (string)completedTask.TaskName, "TaskName should be the same.");
            Assert.AreEqual(GoogleAccountNameForE2EIntegration2, (string)completedTask.GoogleUserEmail, "GoogleUserEmail should be the same.");
            Assert.AreEqual(GoogleTestAccountData.GoogleNonRetailPMCTestAccountCustomerId.ToString(), (string)completedTask.GoogleAdsCustomerId, "GoogleCustomerId should be the same.");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.Import)]
        public void Import_GoogleApi_PMaxRecommendationTask_TokenInvalid()
        {
            var cInfo = CustomerInfo.CreateStandardAdvertiser();
            DatabaseHelper.AddPilotFeatureToCustomer(cInfo.CustomerId, new List<int> { Features.WhitelistSTA });

            var taskName = "PMaxRecommendationTask";
            var adWordsImportContext = TestTaskItemOperations.CreateAdWordsImportContext(
                cInfo,
                sessionId: Guid.NewGuid().ToString(), // invalid session id
                campaignIds: null,
                customerId: GoogleCustomerIdForE2EIntegration2,
                accountName: GoogleAccountNameForE2EIntegration2);
            var importTaskItem = CreateGoogleApiImportTaskItem(
                adWordsImportContext,
                cInfo,
                ruleName: taskName,
                cronStr: CRON_RUNONCE_PLACEHOLDER,
                notificationType: V2.NotificationType.None,
                importUserPreference: null,
                useDefaultImportUserPreference: false);

            importTaskItem.IsSmartImport = true;
            importTaskItem.IsAutoFrequency = false;
            var defaultImportSettings = "PMaxRecommendation";
            importTaskItem.DefaultImportSettings = defaultImportSettings;

            var response = PostAccountTaskItem(cInfo, importTaskItem);
            Assert.IsNotNull(importTaskItem.Id, "Cannot get task Id from response");

            // Update Scheduled time to let task engine execute the task early
            string updateRuleExecutionQueueQuery = $"update RuleExecutionQueue set NextRunDTim = '{DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")}' where ruleid= {importTaskItem.Id} and CustomerId = {cInfo.CustomerId} and PluginTypeID = 24 and ExecutionStatusid = 1";
            DBHelper.ExecuteQueryInCampaignBulkDB(cInfo.CustomerId, updateRuleExecutionQueueQuery);

            // verify the import results
            var taskItemExecutions = PollImportTaskItemExecutionsWithTimeout(cInfo, filter: GoogleImportTaskFilter, expectedImportCompleteStatus: ImportStatus.Failed);
            Assert.IsNotNull(taskItemExecutions, "TaskItemExecutions should not be null");

            // verify the import notification
            response = ApiHelper.CallApi(
                cInfo,
                c => c.GetAsync($"{ApiVersion.BaseUrl}/Customers({cInfo.CustomerId})/Accounts({cInfo.AccountIds[0]})/ImportNotifications"),
                e => Assert.AreEqual(true, e.IsSuccessStatusCode));
            var failedTask = response.PMaxImportRecommendation.Failed[0].Tasks[0];
            Assert.IsNotNull(failedTask, "PMaxImportRecommendation notification should not be null.");
            Assert.AreEqual(((long)importTaskItem.Id).ToString(), (string)failedTask.TaskId, "TaskId should be the same.");
            Assert.AreEqual(taskName, (string)failedTask.TaskName, "TaskName should be the same.");
            Assert.AreEqual(GoogleAccountNameForE2EIntegration2, (string)failedTask.GoogleUserEmail, "GoogleUserEmail should be the same.");
            Assert.AreEqual(GoogleCustomerIdForE2EIntegration2.ToString(), (string)failedTask.GoogleAdsCustomerId, "GoogleCustomerId should be the same.");
        }
        #endregion

        private static string SerializeAndUploadGoogleEntitiesToAzureBlob(GoogleEntities googleEntities, CustomerInfo customerInfo)
        {
            // Same filename as UI
            var filename = $"{customerInfo.CustomerId}_{Guid.NewGuid()}_{DateTime.UtcNow:yyyyMMddHHmmssfff}.txt";
            var googleEntitiesSerializedString = JsonConvert.SerializeObject(googleEntities);
            using (var sw = new StreamWriter(filename, true))
            {
                sw.Write(googleEntitiesSerializedString);
            }

            AzureHelper.UploadToAzureAsync(GSyncTestConsts.CampaignIdBlobContainer, filename).GetAwaiter().GetResult();
            File.Delete(filename);

            return Path.GetFileName(filename);
        }

        private static JObject ImportAndGetTaskItemExecution(CustomerInfo customerInfo, ExpandoObject prefs,
            JArray googleCampaignIds, long googleTestAccountId = GoogleTestAccountData.GoogleAdCustomizerFeedCustomerId,
            string googleEntitiesFileName = null, string refreshToken = null, ExpandoObject pref = null)
        {
            var adWordsImportContext = CreateAdWordsImportContext(customerInfo, null, googleCampaignIds, googleTestAccountId,
                refreshToken: refreshToken ?? GSyncTestConsts.GSyncRefreshToken2, googleEntitiesFileName: googleEntitiesFileName);
            var importTaskItem = CreateGoogleApiImportTaskItem(adWordsImportContext, customerInfo, cronStr: CRON_RUNNOW,
                notificationType: NotificationType.None, importUserPreference: pref);
            var taskItem = PostAccountTaskItem(customerInfo, importTaskItem, expand: "TaskItemExecutions");
            var executions =
                PollImportTaskItemExecutionByIdWithTimeout(customerInfo, (long)taskItem.TaskItemExecutions[0].Id, maxTry: 400);
            return (JObject)executions;
        }

        private static Dictionary<string, ImportStatistics> GetImportStatisticsFromExecution(JToken entityStatistics)
        {
            var keys = entityStatistics["Keys"].Values<string>();
            var values = entityStatistics["Values"].ToObject<ImportStatistics[]>();
            return keys.Zip(values, (k, v) => new { k, v }).ToDictionary(a => a.k, a => a.v);
        }
    }
}

#endif