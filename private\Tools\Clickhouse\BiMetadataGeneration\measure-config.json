{"nonRequestMeasures": ["AccountId", "CampaignId", "OrderId", "KeywordId", "AdId", "AdGroupId", "CustomerId", "<PERSON><PERSON><PERSON>", "Time<PERSON>ey", "Hour<PERSON>ey", "Week<PERSON>ey", "<PERSON><PERSON><PERSON>", "QuarterKey", "<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON>", "CampaignName", "OrderName", "KeywordText", "AdTitle", "AdGroupName", "CustomerName", "Date", "Time", "Hour", "Week", "Month", "Quarter", "Year", "DeviceType", "Network", "Language", "Country", "State", "City", "MetroArea", "PostalCode", "BidMatchType", "DeliveredMatchType", "TopVsOther", "ClickType", "ConversionType", "ConversionName", "GoalType", "Revenue", "Assists", "Returns", "RevenuePerConversion", "RevenuePerAssist", "RevenuePerReturn", "CostPerConversion", "CostPerAssist", "CostPerReturn", "ConversionRate", "AssistRate", "ReturnRate"], "queryMeasureCustomizations": {"AccountPerformanceReport": {"excludedMeasures": ["KeywordId", "AdId"], "includedMeasures": ["Spend", "<PERSON>licks", "Impressions"]}, "CampaignPerformanceReport": {"excludedMeasures": ["KeywordId", "AdId", "AdGroupId"], "includedMeasures": ["Spend", "<PERSON>licks", "Impressions", "Conversions"]}}, "queryDimensionCustomizations": {"AccountPerformanceReport": {"requiredDimensions": ["AccountId", "Account<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "optionalDimensions": ["DeviceType", "Network"]}, "CampaignPerformanceReport": {"requiredDimensions": ["AccountId", "CampaignId", "CampaignName", "<PERSON><PERSON><PERSON>"], "optionalDimensions": ["DeviceType", "Network", "Language"]}}}