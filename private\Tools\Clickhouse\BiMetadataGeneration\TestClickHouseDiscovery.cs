using Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Test class to verify ClickHouse auto-discovery functionality
    /// </summary>
    public class TestClickHouseDiscovery
    {
        /// <summary>
        /// Test method to verify the ClickHouse discovery integration
        /// </summary>
        public static async Task TestDiscoveryAsync()
        {
            try
            {
                Console.WriteLine("Testing ClickHouse auto-discovery functionality...");

                // Create a sample dictionary of column mappings
                var allCHColumnsDic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "accountid", "AccountId" },
                    { "campaignid", "CampaignId" },
                    { "datekey", "DateKey" },
                    { "impressioncnt", "ImpressionCnt" },
                    { "clickcnt", "ClickCnt" },
                    { "totalamount", "TotalAmount" },
                    { "loadtime", "LoadTime" }
                };

                // Test the GenerateBiDataGroups method with ClickHouse discovery
                string testOutputPath = @".\TestBiDataGroups.json";
                
                Console.WriteLine("Calling GenerateBiDataGroups with ClickHouse discovery...");
                var biDataGroups = await BiMetadataGenerator.GenerateBiDataGroups(testOutputPath, allCHColumnsDic);

                Console.WriteLine($"Generated {biDataGroups.Count} BiDataGroups");

                // Check if ContentPerformanceUsage was discovered
                if (biDataGroups.ContainsKey(BiDataCategory.ContentPerformanceUsage))
                {
                    var contentPerfGroup = biDataGroups[BiDataCategory.ContentPerformanceUsage];
                    Console.WriteLine($"✓ ContentPerformanceUsage discovered with {contentPerfGroup.Columns.Count} columns");
                    Console.WriteLine($"  - Streaming Enabled: {contentPerfGroup.StreamingEnabled}");
                    Console.WriteLine($"  - Has Conversion Data: {contentPerfGroup.HasConversionData}");
                    Console.WriteLine($"  - Conversion Table: {contentPerfGroup.ConversionTableName ?? "None"}");
                    
                    Console.WriteLine("  - Sample columns:");
                    int count = 0;
                    foreach (var column in contentPerfGroup.Columns.Take(5))
                    {
                        Console.WriteLine($"    {column.Key}: BitMask={column.Value.ExistInTablesBitMask}, Default={column.Value.DefaultValueIfNonExist ?? "None"}");
                        count++;
                    }
                    if (contentPerfGroup.Columns.Count > 5)
                    {
                        Console.WriteLine($"    ... and {contentPerfGroup.Columns.Count - 5} more columns");
                    }
                }
                else
                {
                    Console.WriteLine("⚠ ContentPerformanceUsage was not discovered (tables may not exist in ClickHouse)");
                }

                Console.WriteLine("✓ Test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Test failed with error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Main method for standalone testing
        /// </summary>
        public static async Task Main(string[] args)
        {
            await TestDiscoveryAsync();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
