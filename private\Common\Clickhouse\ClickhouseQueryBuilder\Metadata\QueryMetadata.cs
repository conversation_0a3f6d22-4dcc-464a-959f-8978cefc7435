﻿using System.Text.Json.Serialization;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public class QueryMetadata
    {
        public HashSet<QueryParameter> QueryParameters { get; set; }
        public List<BiDataCategory> BIDataCategories { get; set; }

        [JsonConverter(typeof(CaseInsensitiveDictionaryConverter<RequestColumn>))]
        public Dictionary<string, RequestColumn> RequestColumns { get; set; }
        [JsonConverter(typeof(CaseInsensitiveDictionaryConverter<ColumnTransformation>))]
        public Dictionary<string, ColumnTransformation> ColumnTransformations { get; set; }

        [JsonConverter(typeof(CaseInsensitiveDictionaryConverter<QueryJoinCondition>))]
        public Dictionary<string, QueryJoinCondition> JoinConditions { get; set; }
        public QueryMetadata(HashSet<QueryParameter> queryParameters, Dictionary<string, RequestColumn> requestColumns,
            Dictionary<string, ColumnTransformation> columnTransformations, Dictionary<string, QueryJoinCondition> joinConditions, List<BiDataCategory> bIDataCategories)
        {
            QueryParameters = queryParameters;
            RequestColumns = requestColumns;
            ColumnTransformations = columnTransformations;
            JoinConditions = joinConditions;
            BIDataCategories = bIDataCategories;
        }
    }

    public class ColumnTransformation
    {
        public string DependentColumns { get; set; }
        public string TransformTo { get; set; }
        public ColumnTransformation(string dependentColumns, string transformTo)
        {
            DependentColumns = dependentColumns;
            TransformTo = transformTo;
        }
    }

    public class QueryJoinCondition
    {
        public string JoinTableName { get; set; }
        public int JoinHierarchyId { get; set; }
        public string RightTableAlias { get; set; }
        public string JoinType { get; set; }
        public List<JoinColumn> JoinColumns { get; set; }

        public string FilterExperssion { get; set; }

        public QueryJoinCondition(string joinTableName, int joinHierarchyId, string joinType, string rightTableAlias, List<JoinColumn> joinColumns, string filterExperssion = "")
        {
            JoinTableName = joinTableName;
            JoinHierarchyId = joinHierarchyId;
            JoinType = joinType;
            RightTableAlias = rightTableAlias;
            JoinColumns = joinColumns;
            FilterExperssion = filterExperssion;
        }
    }

    public class JoinColumn
    {
        public string LeftColumn { get; set; }
        public string RightColumn { get; set; }
        public JoinColumn(string leftColumn, string rightColumn)
        {
            LeftColumn = leftColumn;
            RightColumn = rightColumn;
        }
    }

    public class RequestColumn
    {
        public string JoinTableAlias { get; set; }
        public string DependentColumnName { get; set; }
        public string ColumnExpression { get; set; }

        public RequestColumn()
        {
        }

        public RequestColumn(string joinTableAlias, string dependentColumnName, string columnExpression)
        {
            JoinTableAlias = joinTableAlias;
            DependentColumnName = dependentColumnName;
            ColumnExpression = columnExpression;
        }

        private static readonly HashSet<string> PushdownDimenstions = new HashSet<string>(StringComparer.OrdinalIgnoreCase) 
        {
           "MediumName"
        };

        public bool ShouldPushdownToFactTable(string columnName)
        {
            return string.IsNullOrEmpty(JoinTableAlias) && PushdownDimenstions.Contains(columnName);
        }

    }

    public class QueryParameter
    {
        public string ParameterName { get; set; }
        public string ParameterType { get; set; }

        public bool UserDefined { get; set; }
        public QueryParameter(string parameterName, string parameterType, bool userDefined)
        {
            ParameterName = parameterName;
            ParameterType = parameterType;
            UserDefined = userDefined;
        }
    }
}
