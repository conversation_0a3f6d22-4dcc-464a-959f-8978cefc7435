﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.UnitTest.Tests.EO.EventTracking
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Common;
    using DAO;
    using Entities;
    using Messages.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Dao.SharedLibrary;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Audience;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.SharedLibrary;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Audience;
    using Microsoft.AdCenter.MsfClientLibrary;
    using Microsoft.AdCenter.Shared.MT;
    using MT.EO.EventTracking;
    using Unity;
    using Rhino.Mocks;
    using VisualStudio.TestTools.UnitTesting;
    using EventTracking = Entities.EventTracking;
    using Microsoft.BingAds.CommonInterfaces;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.UnitTest.TestHarness;
    using Microsoft.Advertiser.ClientCenter.MT.Proxy;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;

    [TestClass]
    public class AddGoalsEOTests : EventTrackingEOTests
    {
        private static IClientCenterCaller ccMock;
        private static ISharedLibraryDao sharedLibraryDaoMock;
        private static IOfflineTaskDao offlineTaskDao;
        private static IDictionary<int, bool> PilotOfCustomerDict;

        [TestInitialize]
        public void Setup()
        {
            MsfContext.InitializeContext();
            MsfContext.SecurityTicket = new SecurityTicket();
            MsfContext.SecurityTicket[MsfClientLibrary.Constants.KeyClientCenterProviderTicket] = TestTools.UserSecurityToken;
            ccMock = MockRepository.GenerateStub<IClientCenterCaller>();

            SingleInstanceFactory<IClientCenterCaller, ClientCenterCaller>.Clear();
            InterfaceBindings.AddBinding(typeof(IClientCenterCaller), typeof(IClientCenterCaller), ccMock);

            sharedLibraryDaoMock = MockRepository.GenerateMock<ISharedLibraryDao>();
            Container.RegisterInstance(sharedLibraryDaoMock);

            offlineTaskDao = MockRepository.GenerateMock<IOfflineTaskDao>();
            Container.RegisterInstance(offlineTaskDao);

            var campaignInfoMock = MockRepository.GenerateMock<ICampaignInfoGetter>();
            campaignInfoMock.Expect(mock => mock.GetCampaignDeliverySettingForMSAN(
                Arg<AccountCallContext>.Is.Anything,
                Arg<long>.Is.Anything))
            .Return(false).Repeat.Any();
            Container.RegisterInstance(campaignInfoMock);

            PilotOfCustomerDict = new Dictionary<int, bool>()
            {
                [(int)CustomerFeatureFlag.ViewThroughConversion] = false,
                [(int)CustomerFeatureFlag.InStoreVisitConversion] = false
            };
            DynamicConfigValuesHelper.SetConfigValue<int>("EnableGoalNullableProperties", "true");
            DynamicConfigValuesHelper.SetConfigValue<int>("EnableLockForAddGoals", "true");
            DynamicConfigValuesHelper.SetConfigValue<int>("MockLockForAddGoals", "true");
            DynamicConfigValuesHelper.SetConfigValue<int>("EnableAutoGoalApiAndValidation", "true");

            EnableSharedLibrary();
        }

        [TestCleanup]
        public void Cleanup()
        {
            InterfaceBindings.RemoveBinding(typeof(IClientCenterCaller));
        }

        [TestMethod]
        public void AddGoalsWithSharedLibrary_ValidRequest_NoAppInstallGoals_Success()
        {
            bool isAccountLevel = false;
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: false, isAccount: isAccountLevel);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId,
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    || g.Type != GoalEntityType.OfflineConversionGoal || g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: goals.Length, expectedTagCount: 0, tagIds: tagIds, isSharedTag: true, isAccountLevel: isAccountLevel);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);
            Assert.AreEqual(2, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddGoalsWithSharedLibrary_ValidRequest_NoAppInstallGoals_WithoutSharedTag_Success()
        {
            bool isAccountLevel = false;
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: false, isAccount: isAccountLevel);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId,
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    || g.Type != GoalEntityType.OfflineConversionGoal || g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: goals.Length, expectedTagCount: 0, tagIds: tagIds, isAccountLevel: isAccountLevel);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);
            Assert.AreEqual(2, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddGoalsWithSharedLibrary_ValidRequest_NoAppInstallGoals_AccountLevel_Success()
        {
            bool isAccountLevel = true;
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: false, isAccount: isAccountLevel);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId,
            };
            var tagIds = goals
                .Where(g => g.TagId.HasValue && (g.Type != GoalEntityType.ApplicationInstallGoal
                    || g.Type != GoalEntityType.OfflineConversionGoal || g.Type != GoalEntityType.InStoreTransactionGoal))
                .Select(g => g.TagId.Value)
                .Distinct().ToArray();

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: goals.Length, expectedTagCount: 0, tagIds: tagIds, isSharedTag: true, isAccountLevel: isAccountLevel);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);
            Assert.AreEqual(2, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        // Call once.
        public void AddGoals_OperationErrorInAddTags()
        {
            #region Generate results
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: true);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerId,
                CustomerAccountId = AccountId,
            };
            var context = new CustomerCallContext(Logger, request, CustomerId);
            var daoMock = this.RegisterDaoMock(context, goals, expectedGoalCount: 0, expectedTagCount: 3, saveTagsOperationError: CampaignManagementErrorCode.AccountIdInvalid, sharedEntityGAed: true);
            #endregion

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(0, result.BatchErrors.Count);
            Assert.AreEqual(0, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        // Call twice.
        public void AddGoals_Validation_And_DB_BatchErrors()
        {
            var goals = GenerateGoals(null);
            goals[3].Name = string.Empty;
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerId,
                CustomerAccountId = AccountId,
            };
            var context = new CustomerCallContext(Logger, request, CustomerId);

            var batchErrors = new Dictionary<int, List<CampaignManagementErrorDetail>>()
                                    {
                                        { 0, new List<CampaignManagementErrorDetail> { new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidGoalId, "error message") } }
                                    };

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: goals.Length - 1, expectedTagCount: 3, saveGoalsBatchErrors: batchErrors);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(0, result.Errors.Count);
            Assert.AreEqual(2, result.BatchErrors.Count);
            Assert.AreEqual(2, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void UpdateGoals_Validation_And_DB_Error()
        {
            var goals = GenerateGoals(null);
            goals[3].Name = string.Empty;
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerId,
                CustomerAccountId = AccountId,
            };
            var context = new CustomerCallContext(Logger, request, CustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: goals.Length - 1, expectedTagCount: 3, saveGoalsOperationError: CampaignManagementErrorCode.InvalidCustomerId);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(1, result.BatchErrors.Count);
            Assert.AreEqual(2, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddGoalsWithSharedLibrary_DB_Error()
        {
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: false);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId,
            };
            var tagIds = new long[1] { goals[0].TagId ?? 0 };
            var taglist = GenerateValidTags(tagIds, (int)request.AdvertiserCustomerId);

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: 0, expectedTagCount: 0, tagIds: tagIds, getTagsOperationError: new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidCustomerId, "error message"));

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(0, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddGoalsWithSharedLibrary_DB_Error_WithGoalCategory()
        {
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: false);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerIdForSharedLibrary,
                CustomerAccountId = AccountId,
            };
            var tagIds = new long[1] { goals[0].TagId ?? 0 };
            var taglist = GenerateValidTags(tagIds, (int)request.AdvertiserCustomerId);

            var context = new CustomerCallContext(Logger, request, (int)request.AdvertiserCustomerId);

            var daoMock = this.RegisterDaoMockWithSharedLibrary(context, goals, expectedGoalCount: 0, expectedTagCount: 0, tagIds: tagIds, getTagsOperationError: new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidCustomerId, "error message"));

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(0, result.Entities.Count);

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddGoals_AgencyLink()
        {
            #region Generate results
            var goals = GenerateGoals(null, generateAppInstallGoalOrOfflineConversionGoal: true);
            var request = new AddGoalsRequest
            {
                Goals = goals,
                SecurityTicket = new UserSecurityTicket(UserId, "UserId"),
                AdvertiserCustomerId = CustomerId,
                CustomerAccountId = AccountId,
            };
            var context = new CustomerCallContext(Logger, request, CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            #endregion

            ccMock.Stub(caller => caller.GetAccountCustomerRelationsByUserToken(
                Arg<ILogShared>.Is.Anything,
                Arg<GetAccountCustomerRelationsByUserTokenRequest>.Is.Anything))
                .Repeat.Any()
                .Return(new GetAccountCustomerRelationsByUserTokenResponse
                {
                    AccountCustomerPropogatedRelations = new List<AccountCustomerPropogatedRelation>
                    {
                        new AccountCustomerPropogatedRelation
                        {
                            AccountCustomerLinkType = AccountCustomerLinkType.AgencyCustomer,
                            AccountLinkPermissionType = AccountLinkPermissionType.AccountCampaignManagement,
                            AccountId = AccountId,
                            CustomerId = CustomerId,
                        }
                    }
                });
            InterfaceBindings.AddBinding(typeof(IClientCenterCaller), typeof(IClientCenterCaller), ccMock);

            var result = Container.Resolve<EventTrackingEO>().AddGoals(context, request.Goals, ignoreLock: true);

            Assert.AreEqual(1, result.Errors.Count);
            Assert.AreEqual(CampaignManagementErrorCode.PermissionDenied, result.Errors[0].Error);
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_DestinationGoal_Sucess()
        {
            var destinationGoal = new EventTracking.DestinationGoal();
            destinationGoal.TagId = 1;
            destinationGoal.UrlString = "www.bing.com";
            destinationGoal.Name = "GoalName";
            destinationGoal.Type = EventTracking.GoalEntityType.DestinationGoal;
            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);

            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { destinationGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, destinationGoal.ConversionCountType);
            Assert.AreEqual(null, destinationGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalValueType.NoValue, destinationGoal.Revenue.Type);
            Assert.AreEqual(EventTracking.GoalStatus.Active, destinationGoal.Status);
            Assert.AreEqual(30, destinationGoal.LookbackWindowDays);
            Assert.AreEqual(0, destinationGoal.LookbackWindowHours);
            Assert.AreEqual(0, destinationGoal.LookbackWindowMinutes);
            Assert.AreEqual(EventTracking.ExpressionOperator.EqualsTo, destinationGoal.Operator);


            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_DurationGoal_Sucess()
        {
            var durationGoal = new EventTracking.DurationGoal();
            durationGoal.TagId = 1;
            durationGoal.Name = "GoalName";
            durationGoal.Type = EventTracking.GoalEntityType.DurationGoal;

            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { durationGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, durationGoal.ConversionCountType);
            Assert.AreEqual(null, durationGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalValueType.NoValue, durationGoal.Revenue.Type);
            Assert.AreEqual(EventTracking.GoalStatus.Active, durationGoal.Status);
            Assert.AreEqual(30, durationGoal.LookbackWindowDays);
            Assert.AreEqual(0, durationGoal.LookbackWindowHours);
            Assert.AreEqual(0, durationGoal.LookbackWindowMinutes);
            Assert.AreEqual(EventTracking.ValueOperator.GreaterThan, durationGoal.Operator);
            Assert.AreEqual(0, durationGoal.Hours);
            Assert.AreEqual(0, durationGoal.Minutes);
            Assert.AreEqual(0, durationGoal.Seconds);

            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_PageViewsPerVisitGoal_Sucess()
        {
            var pageViewsPerVisitGoal = new EventTracking.PageViewsPerVisitGoal();
            pageViewsPerVisitGoal.TagId = 1;
            pageViewsPerVisitGoal.Name = "GoalName";
            pageViewsPerVisitGoal.Type = EventTracking.GoalEntityType.PageViewsPerVisitGoal;
            pageViewsPerVisitGoal.Revenue = new EventTracking.GoalRevenue()
            {
                Type = EventTracking.GoalValueType.FixedValue
            };

            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { pageViewsPerVisitGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, pageViewsPerVisitGoal.ConversionCountType);
            Assert.AreEqual(1, pageViewsPerVisitGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalStatus.Active, pageViewsPerVisitGoal.Status);
            Assert.AreEqual(30, pageViewsPerVisitGoal.LookbackWindowDays);
            Assert.AreEqual(0, pageViewsPerVisitGoal.LookbackWindowHours);
            Assert.AreEqual(0, pageViewsPerVisitGoal.LookbackWindowMinutes);
            Assert.AreEqual(EventTracking.ValueOperator.GreaterThan, pageViewsPerVisitGoal.Operator);
            Assert.AreEqual(0, pageViewsPerVisitGoal.PageViews);

            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_EventGoal_Sucess()
        {
            var eventGoal = new EventTracking.EventGoal();
            eventGoal.TagId = 1;
            eventGoal.Value = 1;
            eventGoal.Action = "action";
            eventGoal.Category = "category";
            eventGoal.Label = "label";
            eventGoal.Name = "GoalName";
            eventGoal.Type = EventTracking.GoalEntityType.EventGoal;
            eventGoal.Revenue = new EventTracking.GoalRevenue()
            {
                Type = EventTracking.GoalValueType.VariantValue,
                CurrencyCode = "USD"
            };

            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { eventGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, eventGoal.ConversionCountType);
            Assert.AreEqual(1, eventGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalStatus.Active, eventGoal.Status);
            Assert.AreEqual(30, eventGoal.LookbackWindowDays);
            Assert.AreEqual(0, eventGoal.LookbackWindowHours);
            Assert.AreEqual(0, eventGoal.LookbackWindowMinutes);
            Assert.AreEqual(EventTracking.ExpressionOperator.EqualsTo, eventGoal.ActionOperator);
            Assert.AreEqual(EventTracking.ExpressionOperator.EqualsTo, eventGoal.CategoryOperator);
            Assert.AreEqual(EventTracking.ExpressionOperator.EqualsTo, eventGoal.LabelOperator);
            Assert.AreEqual(EventTracking.ValueOperator.EqualTo, eventGoal.ValueOperator);

            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_AppInstallGoal_Sucess()
        {
            var applicationInstallGoal = new EventTracking.ApplicationInstallGoal();
            applicationInstallGoal.IsAccountLevel = false;
            applicationInstallGoal.Name = "GoalName";
            applicationInstallGoal.Type = EventTracking.GoalEntityType.ApplicationInstallGoal;
            applicationInstallGoal.ApplicationStoreId = "abc";

            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { applicationInstallGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, applicationInstallGoal.ConversionCountType);
            Assert.AreEqual(null, applicationInstallGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalValueType.NoValue, applicationInstallGoal.Revenue.Type);
            Assert.AreEqual(EventTracking.GoalStatus.Active, applicationInstallGoal.Status);
            Assert.AreEqual(30, applicationInstallGoal.LookbackWindowDays);
            Assert.AreEqual(0, applicationInstallGoal.LookbackWindowHours);
            Assert.AreEqual(0, applicationInstallGoal.LookbackWindowMinutes);
            Assert.AreEqual("Windows Phone", applicationInstallGoal.ApplicationPlatform);

            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        [TestMethod]
        public void AddDefaultValueForAddGoals_Validation_OfflineGoal_Sucess()
        {
            var offlineConversionGoal = new EventTracking.OfflineConversionGoal();
            offlineConversionGoal.TagId = 1;
            offlineConversionGoal.Name = "GoalName";
            offlineConversionGoal.Type = EventTracking.GoalEntityType.OfflineConversionGoal;
            offlineConversionGoal.IsAccountLevel = true;
            offlineConversionGoal.Revenue = new EventTracking.GoalRevenue()
            {
                Type = EventTracking.GoalValueType.FixedValue,
                CurrencyCode = "USD"
            };

            var context = new CustomerCallContext(Logger, new AddGoalsRequest(), CustomerId);
            var daoMock = this.RegisterDaoMock(null, null, 0);
            var eo = Container.Resolve<EventTrackingEO>();
            EventTracking.Goal[] goals = { offlineConversionGoal };
            RunMethod(eo.GetType(), "AddDefaultValueForAddGoals", eo, new object[] { context, goals, new BatchResult<long>(), PilotOfCustomerDict });

            Assert.AreEqual(EventTracking.ConversionCountType.All, offlineConversionGoal.ConversionCountType);
            Assert.AreEqual(1, offlineConversionGoal.Revenue.Value);
            Assert.AreEqual(EventTracking.GoalStatus.Active, offlineConversionGoal.Status);
            Assert.AreEqual(30, offlineConversionGoal.LookbackWindowDays);
            Assert.AreEqual(0, offlineConversionGoal.LookbackWindowHours);
            Assert.AreEqual(0, offlineConversionGoal.LookbackWindowMinutes);

            var result = new BatchResult<long>();
            GoalValidator.ValidateGoals(context, goals, result, PilotOfCustomerDict);
            Assert.AreEqual(OperationStatus.Success, result.OperationStatus, "Incorrect operation status.");

            daoMock.VerifyAllExpectations();
        }

        private IEventTrackingDao RegisterDaoMock(
            CustomerCallContext context,
            EventTracking.Goal[] goals,
            int expectedGoalCount = 0,
            CampaignManagementErrorCode? saveGoalsOperationError = null,
            Dictionary<int, List<CampaignManagementErrorDetail>> saveGoalsBatchErrors = null,
            int expectedTagCount = 0,
            CampaignManagementErrorCode? saveTagsOperationError = null,
            Dictionary<int, List<CampaignManagementErrorDetail>> saveTagsBatchErrors = null,
            bool sharedEntityGAed = false)
        {
            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var daoResult = new Result<EventTracking.Goal>();
            var tagGetMockResult = new Result<Tag>();
            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            BasicCallContext basicCallContext;
            var uetGoalGetMockResult = new Result<Entities.EventTracking.Goal>();

            if (context != null)
            {
                if (sharedEntityGAed)
                {
                    basicCallContext = BasicCallContext.Create(context);
                    daoMock.Expect(dao => dao.GetUetGoalsByCustomerIdAccountId(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<Dictionary<long, TagTrackingStatus>>.Is.Anything,
                        Arg<Dictionary<int, bool>>.Is.Anything,
                        Arg<bool>.Is.Equal(false)))
                        .Return(daoResult).Repeat.Once();
                    daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                               Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                           .Return(tagGetMockResult).Repeat.Once();
                    sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                            Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                            Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                            Arg<List<SharedLibraryEntityType>>.Is.Anything,
                            Arg<bool>.Is.Anything))
                        .Return(getSharedLibraryByCustomerIdResult).Repeat.Any();
                }
                else
                {
                    daoMock.Expect(
                        dao =>
                        dao.GetUetGoalsByCustomerIdAccountId(
                            Arg<CustomerCallContext>.Matches(c => c == context),
                            Arg<Dictionary<long, TagTrackingStatus>>.Matches(t => t == null),
                            Arg<IDictionary<int, bool>>.Is.Anything,
                            Arg<bool>.Is.Equal(false)))
                        .Return(daoResult)
                        .Repeat.Once();
                }
            }

            if (expectedGoalCount > 0)
            {
                var expectedGoalsResult = new Dictionary<int, long> { { 0, (long)goals[0].Id }, { 1, (long)goals[1].Id } };

                var addGoalsResult = CreateBatchResult(saveGoalsOperationError, saveGoalsBatchErrors, expectedGoalsResult);

                daoMock.Expect(
                    dao =>
                    dao.AddUetGoals(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<LineItemContainer<EventTracking.Goal>>.Matches(g => g.Count == expectedGoalCount),
                        Arg<bool>.Is.Equal(false),
                        Arg<IDictionary<int, bool>>.Is.Anything,
                        Arg<List<long>>.Is.Anything))
                    .Return(addGoalsResult)
                    .Repeat.Once();
            }
            else
            {
                IDictionary<int, bool> PilotOfCustomerDict = new Dictionary<int, bool>()
                {
                    [(int)CustomerFeatureFlag.ViewThroughConversion] = false,
                };
                daoMock.Expect(dao => dao.AddUetGoals(null, null, false, PilotOfCustomerDict, null)).Repeat.Never();
            }

            if (expectedTagCount > 0)
            {
                var expectedTagsResult =
                    Enumerable.Range(0, goals.Length)
                        .Where(i => goals[i] is EventTracking.ApplicationInstallGoal || goals[i] is EventTracking.OfflineConversionGoal || goals[i] is EventTracking.InStoreTransactionGoal)
                        .ToDictionary(i => i, i => new EventTracking.Tag() { Id = i });


                var addTagsResult = CreateBatchResult(saveTagsOperationError, saveTagsBatchErrors, expectedTagsResult);

                daoMock.Expect(
                    dao =>
                    dao.AddTagsV2(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<LineItemContainer<EventTracking.Tag>>.Matches(t => t.Count == expectedTagCount && t.All(tag => tag.LineItem.Type == EventTracking.TagType.SystemManaged)),
                        Arg<bool>.Is.Anything))
                    .Return(addTagsResult)
                    .Repeat.Once();
            }
            else
            {
                daoMock.Expect(dao => dao.AddTagsV2(null, null, false)).Repeat.Never();
            }

            this.Container.RegisterInstance(daoMock);

            return daoMock;
        }

        private IEventTrackingDao RegisterDaoMockWithSharedLibrary(
            CustomerCallContext context,
            EventTracking.Goal[] goals,
            int expectedGoalCount = 0,
            CampaignManagementErrorCode? saveGoalsOperationError = null,
            Dictionary<int, List<CampaignManagementErrorDetail>> saveGoalsBatchErrors = null,
            int expectedTagCount = 0,
            CampaignManagementErrorCode? saveTagsOperationError = null,
            Dictionary<int, List<CampaignManagementErrorDetail>> saveTagsBatchErrors = null,
            long[] tagIds = null,
            CampaignManagementErrorDetail getTagsOperationError = null,
            bool isSharedTag = false,
            bool isAccountLevel = false)
        {
            var basicCallContext = BasicCallContext.Create(context);
            var tags = GenerateValidTags(tagIds.OrEmpty(), context.AdvertiserCustomerId);

            var getSharedLibraryByCustomerIdResult = new Result<Tuple<SharedMapping, List<UsedByInfo>>>();
            var expectedsharedLibraryResult = GeneraterSharedTagListResult(isSharedTag ? tags : Array.Empty<Tag>(), context.AdvertiserCustomerId, context.AdvertiserCustomerId + 1, true);
            getSharedLibraryByCustomerIdResult.AddEntities(expectedsharedLibraryResult);
            var getSharedEntityError = new List<SharedLibraryEntityError>();

            sharedLibraryDaoMock.Expect(dao => dao.GetSharedLibraryByCustomerIdsAccountIds(
                    Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                    Arg<LineItemContainer<AccountEntity>>.Is.Anything,
                    Arg<List<SharedLibraryEntityType>>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(getSharedLibraryByCustomerIdResult).Repeat.Twice();
            if (isSharedTag)
            {
                sharedLibraryDaoMock.Expect(dao => dao.UpdateUsedBy(
                        Arg<BasicCallContext>.Matches(c => c.AdvertiserCustomerId == basicCallContext.AdvertiserCustomerId && c.AccountId == basicCallContext.AccountId),
                        Arg<List<UsedByInfo>>.Matches(u => u.Count == 1),
                        Arg<bool>.Is.Equal(true)))
                    .Return(new BatchResult<long>()).Repeat.Once();
            }

            var daoMock = MockRepository.GenerateMock<IEventTrackingDao>();
            var daoResult = new Result<EventTracking.Goal>();
            var tagGetMockResult = new Result<Tag>();
            tagGetMockResult.AddEntities(tags);
            if (getTagsOperationError != null)
            {
                tagGetMockResult.AddError(getTagsOperationError);
            }

            if (context != null && getTagsOperationError == null)
            {
                daoMock.Expect(
                    dao =>
                    dao.GetUetGoalsByCustomerIdAccountId(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<Dictionary<long, TagTrackingStatus>>.Matches(t => t != null),
                        Arg<IDictionary<int, bool>>.Is.Anything,
                        Arg<bool>.Is.Equal(false)))
                    .Return(daoResult)
                    .Repeat.Once();
            }

            daoMock.Expect(dao => dao.GetTagsByCustomerIdV6(
                    Arg<CustomerCallContext>.Matches(c => c == context), Arg<bool>.Is.Anything, Arg<bool>.Is.Anything))
                .Return(tagGetMockResult).Repeat.Twice();

            var audiences = GenerateAudiences(tags, (int)context.AdvertiserCustomerId);
            var providerMock = MockRepository.GenerateMock<IAudienceProvider>();
            var providerMockResult = new Result<Entities.Audience.Audience>();
            providerMockResult.AddEntities(audiences);

            providerMock.Expect(provider => provider.GetAudiencesByCustomerId(
                    Arg<CustomerCallContext>.Is.Anything,
                    Arg<AudienceTypeFilter>.Is.Anything,
                    Arg<DeliveryChannelFilter>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<int?>.Is.Anything,
                    Arg<bool>.Is.Anything,
                    Arg<bool>.Is.Anything))
                .Return(providerMockResult).Repeat.Once();
            Container.RegisterInstance(providerMock);

            if (expectedGoalCount > 0)
            {
                var expectedGoalsResult = new Dictionary<int, long> { { 0, (long)goals[0].Id }, { 1, (long)goals[1].Id } };

                var addGoalsResult = CreateBatchResult(saveGoalsOperationError, saveGoalsBatchErrors, expectedGoalsResult);

                daoMock.Expect(
                    dao =>
                    dao.AddUetGoals(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<LineItemContainer<EventTracking.Goal>>.Matches(g => g.Count == expectedGoalCount),
                        Arg<bool>.Is.Equal(false),
                        Arg<IDictionary<int, bool>>.Is.Anything,
                        Arg<List<long>>.Matches(l => l.Count == (isSharedTag ? tags.Length : 0))))
                    .Return(addGoalsResult)
                    .Repeat.Once();
            }
            else
            {
                IDictionary<int, bool> PilotOfCustomerDict = new Dictionary<int, bool>()
                {
                    [(int)CustomerFeatureFlag.ViewThroughConversion] = false,
                };
                daoMock.Expect(dao => dao.AddUetGoals(null, null, false, PilotOfCustomerDict)).Repeat.Never();
            }

            if (expectedTagCount > 0)
            {
                var expectedTagsResult =
                    Enumerable.Range(0, goals.Length)
                        .Where(i => goals[i] is EventTracking.ApplicationInstallGoal || goals[i] is EventTracking.OfflineConversionGoal || goals[i] is EventTracking.InStoreTransactionGoal)
                        .ToDictionary(i => i, i => new EventTracking.Tag() { Id = i });


                var addTagsResult = CreateBatchResult(saveTagsOperationError, saveTagsBatchErrors, expectedTagsResult);

                daoMock.Expect(
                    dao =>
                    dao.AddTagsV2(
                        Arg<CustomerCallContext>.Matches(c => c == context),
                        Arg<LineItemContainer<EventTracking.Tag>>.Matches(t => t.Count == expectedTagCount && t.All(tag => tag.LineItem.Type == EventTracking.TagType.SystemManaged)),
                        Arg<bool>.Is.Anything))
                    .Return(addTagsResult)
                    .Repeat.Once();
            }
            else
            {
                daoMock.Expect(dao => dao.AddTagsV2(null, null, false)).Repeat.Never();
            }
            this.Container.RegisterInstance(daoMock);

            if (isSharedTag)
            {
                offlineTaskDao.Expect(
                    e =>
                    e.AddUETTagUsedByRecountTask(
                        Arg<BasicCallContext>.Matches(c => isAccountLevel ? c.AccountId.Value == context.Request.CustomerAccountId.Value : !c.AccountId.HasValue),
                        Arg<List<long>>.Is.NotNull,
                        Arg<int>.Is.Same(isAccountLevel ? SpWrappers.RecountUsedbyAccountForUETTagAssociateToGoalOperation : SpWrappers.RecountUsedbyCustomerForUETTagAssociateToGoalOperation),
                        Arg<bool>.Is.Anything))
                    .Repeat
                    .Once();
            }

            return daoMock;
        }

        private static BatchResult<T> CreateBatchResult<T>(
            CampaignManagementErrorCode? operationError,
            Dictionary<int, List<CampaignManagementErrorDetail>> batchErrors,
            Dictionary<int, T> expectedResult)
        {
            var result = new BatchResult<T>();

            result.AddEntities(expectedResult);

            if (operationError.HasValue)
            {
                result.AddError(operationError.Value);
            }

            if (batchErrors != null)
            {
                result.AddEntityErrors(batchErrors);
            }

            return result;
        }
    }
}
