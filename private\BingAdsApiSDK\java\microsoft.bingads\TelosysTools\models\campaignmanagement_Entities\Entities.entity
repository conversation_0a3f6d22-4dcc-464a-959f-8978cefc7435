@Context(campaignmanagement)
Entities {
  AddCampaigns : string { @DefaultValue(HttpPost) @Label( "/Campaigns") @InitialValue(addCampaigns) } ;
  GetCampaignsByAccountId : string { @DefaultValue(HttpPost) @Label( "/Campaigns/QueryByAccountId") @InitialValue(getCampaignsByAccountId) } ;
  GetCampaignsByIds : string { @DefaultValue(HttpPost) @Label( "/Campaigns/QueryByIds") @InitialValue(getCampaignsByIds) } ;
  DeleteCampaigns : string { @DefaultValue(HttpDelete) @Label( "/Campaigns") @InitialValue(deleteCampaigns) } ;
  UpdateCampaigns : string { @DefaultValue(HttpPut) @Label( "/Campaigns") @InitialValue(updateCampaigns) } ;
  GetNegativeSitesByCampaignIds : string { @DefaultValue(HttpPost) @Label( "/NegativeSites/QueryByCampaignIds") @InitialValue(getNegativeSitesByCampaignIds) } ;
  SetNegativeSitesToCampaigns : string { @DefaultValue(HttpPost) @Label( "/NegativeSites/SetToCampaigns") @InitialValue(setNegativeSitesToCampaigns) } ;
  GetConfigValue : string { @DefaultValue(HttpPost) @Label( "/ConfigValue/Query") @InitialValue(getConfigValue) } ;
  GetBSCCountries : string { @DefaultValue(HttpPost) @Label( "/BSCCountries/Query") @InitialValue(getBSCCountries) } ;
  AddAdGroups : string { @DefaultValue(HttpPost) @Label( "/AdGroups") @InitialValue(addAdGroups) } ;
  DeleteAdGroups : string { @DefaultValue(HttpDelete) @Label( "/AdGroups") @InitialValue(deleteAdGroups) } ;
  GetAdGroupsByIds : string { @DefaultValue(HttpPost) @Label( "/AdGroups/QueryByIds") @InitialValue(getAdGroupsByIds) } ;
  GetAdGroupsByCampaignId : string { @DefaultValue(HttpPost) @Label( "/AdGroups/QueryByCampaignId") @InitialValue(getAdGroupsByCampaignId) } ;
  UpdateAdGroups : string { @DefaultValue(HttpPut) @Label( "/AdGroups") @InitialValue(updateAdGroups) } ;
  GetNegativeSitesByAdGroupIds : string { @DefaultValue(HttpPost) @Label( "/NegativeSites/QueryByAdGroupIds") @InitialValue(getNegativeSitesByAdGroupIds) } ;
  SetNegativeSitesToAdGroups : string { @DefaultValue(HttpPost) @Label( "/NegativeSites/SetToAdGroups") @InitialValue(setNegativeSitesToAdGroups) } ;
  GetGeoLocationsFileUrl : string { @DefaultValue(HttpPost) @Label( "/GeoLocationsFileUrl/Query") @InitialValue(getGeoLocationsFileUrl) } ;
  AddAds : string { @DefaultValue(HttpPost) @Label( "/Ads") @InitialValue(addAds) #isSpecial } ;
  DeleteAds : string { @DefaultValue(HttpDelete) @Label( "/Ads") @InitialValue(deleteAds) } ;
  GetAdsByEditorialStatus : string { @DefaultValue(HttpPost) @Label( "/Ads/QueryByEditorialStatus") @InitialValue(getAdsByEditorialStatus) } ;
  GetAdsByIds : string { @DefaultValue(HttpPost) @Label( "/Ads/QueryByIds") @InitialValue(getAdsByIds) } ;
  GetAdsByAdGroupId : string { @DefaultValue(HttpPost) @Label( "/Ads/QueryByAdGroupId") @InitialValue(getAdsByAdGroupId) } ;
  UpdateAds : string { @DefaultValue(HttpPut) @Label( "/Ads") @InitialValue(updateAds) #isSpecial } ;
  AddKeywords : string { @DefaultValue(HttpPost) @Label( "/Keywords") @InitialValue(addKeywords) #isSpecial } ;
  DeleteKeywords : string { @DefaultValue(HttpDelete) @Label( "/Keywords") @InitialValue(deleteKeywords) } ;
  GetKeywordsByEditorialStatus : string { @DefaultValue(HttpPost) @Label( "/Keywords/QueryByEditorialStatus") @InitialValue(getKeywordsByEditorialStatus) } ;
  GetKeywordsByIds : string { @DefaultValue(HttpPost) @Label( "/Keywords/QueryByIds") @InitialValue(getKeywordsByIds) } ;
  GetKeywordsByAdGroupId : string { @DefaultValue(HttpPost) @Label( "/Keywords/QueryByAdGroupId") @InitialValue(getKeywordsByAdGroupId) } ;
  UpdateKeywords : string { @DefaultValue(HttpPut) @Label( "/Keywords") @InitialValue(updateKeywords) #isSpecial } ;
  AppealEditorialRejections : string { @DefaultValue(HttpPost) @Label( "/EditorialRejections/Appeal") @InitialValue(appealEditorialRejections) } ;
  GetEditorialReasonsByIds : string { @DefaultValue(HttpPost) @Label( "/EditorialReasons/QueryByIds") @InitialValue(getEditorialReasonsByIds) } ;
  GetAccountMigrationStatuses : string { @DefaultValue(HttpPost) @Label( "/AccountMigrationStatuses/Query") @InitialValue(getAccountMigrationStatuses) } ;
  SetAccountProperties : string { @DefaultValue(HttpPost) @Label( "/AccountProperties/Set") @InitialValue(setAccountProperties) } ;
  GetAccountProperties : string { @DefaultValue(HttpPost) @Label( "/AccountProperties/Query") @InitialValue(getAccountProperties) } ;
  AddAdExtensions : string { @DefaultValue(HttpPost) @Label( "/AdExtensions") @InitialValue(addAdExtensions) } ;
  GetAdExtensionsByIds : string { @DefaultValue(HttpPost) @Label( "/AdExtensions/QueryByIds") @InitialValue(getAdExtensionsByIds) } ;
  UpdateAdExtensions : string { @DefaultValue(HttpPut) @Label( "/AdExtensions") @InitialValue(updateAdExtensions) #isSpecial } ;
  DeleteAdExtensions : string { @DefaultValue(HttpDelete) @Label( "/AdExtensions") @InitialValue(deleteAdExtensions) } ;
  GetAdExtensionsEditorialReasons : string { @DefaultValue(HttpPost) @Label( "/AdExtensionsEditorialReasons/Query") @InitialValue(getAdExtensionsEditorialReasons) } ;
  SetAdExtensionsAssociations : string { @DefaultValue(HttpPost) @Label( "/AdExtensionsAssociations/Set") @InitialValue(setAdExtensionsAssociations) #isSpecial } ;
  GetAdExtensionsAssociations : string { @DefaultValue(HttpPost) @Label( "/AdExtensionsAssociations/Query") @InitialValue(getAdExtensionsAssociations) } ;
  DeleteAdExtensionsAssociations : string { @DefaultValue(HttpDelete) @Label( "/AdExtensionsAssociations") @InitialValue(deleteAdExtensionsAssociations) } ;
  GetAdExtensionIdsByAccountId : string { @DefaultValue(HttpPost) @Label( "/AdExtensionIds/QueryByAccountId") @InitialValue(getAdExtensionIdsByAccountId) } ;
  AddMedia : string { @DefaultValue(HttpPost) @Label( "/Media") @InitialValue(addMedia) } ;
  DeleteMedia : string { @DefaultValue(HttpDelete) @Label( "/Media") @InitialValue(deleteMedia) } ;
  GetMediaMetaDataByAccountId : string { @DefaultValue(HttpPost) @Label( "/MediaMetaData/QueryByAccountId") @InitialValue(getMediaMetaDataByAccountId) } ;
  GetMediaMetaDataByIds : string { @DefaultValue(HttpPost) @Label( "/MediaMetaData/QueryByIds") @InitialValue(getMediaMetaDataByIds) } ;
  GetMediaAssociations : string { @DefaultValue(HttpPost) @Label( "/MediaAssociations/Query") @InitialValue(getMediaAssociations) } ;
  GetAdGroupCriterionsByIds : string { @DefaultValue(HttpPost) @Label( "/AdGroupCriterions/QueryByIds") @InitialValue(getAdGroupCriterionsByIds) } ;
  AddAdGroupCriterions : string { @DefaultValue(HttpPost) @Label( "/AdGroupCriterions") @InitialValue(addAdGroupCriterions) #isSpecial } ;
  UpdateAdGroupCriterions : string { @DefaultValue(HttpPut) @Label( "/AdGroupCriterions") @InitialValue(updateAdGroupCriterions) #isSpecial } ;
  DeleteAdGroupCriterions : string { @DefaultValue(HttpDelete) @Label( "/AdGroupCriterions") @InitialValue(deleteAdGroupCriterions) } ;
  ApplyProductPartitionActions : string { @DefaultValue(HttpPost) @Label( "/ProductPartitionActions/Apply") @InitialValue(applyProductPartitionActions) } ;
  ApplyHotelGroupActions : string { @DefaultValue(HttpPost) @Label( "/HotelGroupActions/Apply") @InitialValue(applyHotelGroupActions) } ;
  ApplyAssetGroupListingGroupActions : string { @DefaultValue(HttpPost) @Label( "/AssetGroupListingGroupActions/Apply") @InitialValue(applyAssetGroupListingGroupActions) } ;
  GetAssetGroupListingGroupsByIds : string { @DefaultValue(HttpPost) @Label( "/AssetGroupListingGroups/QueryByIds") @InitialValue(getAssetGroupListingGroupsByIds) } ;
  GetBMCStoresByCustomerId : string { @DefaultValue(HttpPost) @Label( "/BMCStores/QueryByCustomerId") @InitialValue(getBMCStoresByCustomerId) } ;
  AddNegativeKeywordsToEntities : string { @DefaultValue(HttpPost) @Label( "/EntityNegativeKeywords") @InitialValue(addNegativeKeywordsToEntities) } ;
  GetNegativeKeywordsByEntityIds : string { @DefaultValue(HttpPost) @Label( "/NegativeKeywords/QueryByEntityIds") @InitialValue(getNegativeKeywordsByEntityIds) } ;
  DeleteNegativeKeywordsFromEntities : string { @DefaultValue(HttpDelete) @Label( "/EntityNegativeKeywords") @InitialValue(deleteNegativeKeywordsFromEntities) } ;
  GetSharedEntitiesByAccountId : string { @DefaultValue(HttpPost) @Label( "/SharedEntities/QueryByAccountId") @InitialValue(getSharedEntitiesByAccountId) } ;
  GetSharedEntities : string { @DefaultValue(HttpPost) @Label( "/SharedEntities/Query") @InitialValue(getSharedEntities) } ;
  AddSharedEntity : string { @DefaultValue(HttpPost) @Label( "/SharedEntity") @InitialValue(addSharedEntity) } ;
  GetListItemsBySharedList : string { @DefaultValue(HttpPost) @Label( "/ListItems/QueryBySharedList") @InitialValue(getListItemsBySharedList) } ;
  AddListItemsToSharedList : string { @DefaultValue(HttpPost) @Label( "/ListItems") @InitialValue(addListItemsToSharedList) } ;
  UpdateSharedEntities : string { @DefaultValue(HttpPut) @Label( "/SharedEntities") @InitialValue(updateSharedEntities) } ;
  DeleteListItemsFromSharedList : string { @DefaultValue(HttpDelete) @Label( "/ListItems") @InitialValue(deleteListItemsFromSharedList) } ;
  SetSharedEntityAssociations : string { @DefaultValue(HttpPost) @Label( "/SharedEntityAssociations/Set") @InitialValue(setSharedEntityAssociations) } ;
  DeleteSharedEntityAssociations : string { @DefaultValue(HttpDelete) @Label( "/SharedEntityAssociations") @InitialValue(deleteSharedEntityAssociations) } ;
  GetSharedEntityAssociationsBySharedEntityIds : string { @DefaultValue(HttpPost) @Label( "/SharedEntityAssociations/QueryBySharedEntityIds") @InitialValue(getSharedEntityAssociationsBySharedEntityIds) } ;
  GetSharedEntityAssociationsByEntityIds : string { @DefaultValue(HttpPost) @Label( "/SharedEntityAssociations/QueryByEntityIds") @InitialValue(getSharedEntityAssociationsByEntityIds) } ;
  DeleteSharedEntities : string { @DefaultValue(HttpDelete) @Label( "/SharedEntities") @InitialValue(deleteSharedEntities) } ;
  GetCampaignSizesByAccountId : string { @DefaultValue(HttpPost) @Label( "/CampaignSizes/QueryByAccountId") @InitialValue(getCampaignSizesByAccountId) } ;
  AddCampaignCriterions : string { @DefaultValue(HttpPost) @Label( "/CampaignCriterions") @InitialValue(addCampaignCriterions) } ;
  UpdateCampaignCriterions : string { @DefaultValue(HttpPut) @Label( "/CampaignCriterions") @InitialValue(updateCampaignCriterions) } ;
  DeleteCampaignCriterions : string { @DefaultValue(HttpDelete) @Label( "/CampaignCriterions") @InitialValue(deleteCampaignCriterions) } ;
  GetCampaignCriterionsByIds : string { @DefaultValue(HttpPost) @Label( "/CampaignCriterions/QueryByIds") @InitialValue(getCampaignCriterionsByIds) } ;
  AddBudgets : string { @DefaultValue(HttpPost) @Label( "/Budgets") @InitialValue(addBudgets) } ;
  UpdateBudgets : string { @DefaultValue(HttpPut) @Label( "/Budgets") @InitialValue(updateBudgets) } ;
  DeleteBudgets : string { @DefaultValue(HttpDelete) @Label( "/Budgets") @InitialValue(deleteBudgets) } ;
  GetBudgetsByIds : string { @DefaultValue(HttpPost) @Label( "/Budgets/QueryByIds") @InitialValue(getBudgetsByIds) } ;
  GetCampaignIdsByBudgetIds : string { @DefaultValue(HttpPost) @Label( "/CampaignIds/QueryByBudgetIds") @InitialValue(getCampaignIdsByBudgetIds) } ;
  AddBidStrategies : string { @DefaultValue(HttpPost) @Label( "/BidStrategies") @InitialValue(addBidStrategies) } ;
  UpdateBidStrategies : string { @DefaultValue(HttpPut) @Label( "/BidStrategies") @InitialValue(updateBidStrategies) } ;
  DeleteBidStrategies : string { @DefaultValue(HttpDelete) @Label( "/BidStrategies") @InitialValue(deleteBidStrategies) } ;
  GetBidStrategiesByIds : string { @DefaultValue(HttpPost) @Label( "/BidStrategies/QueryByIds") @InitialValue(getBidStrategiesByIds) } ;
  GetCampaignIdsByBidStrategyIds : string { @DefaultValue(HttpPost) @Label( "/CampaignIds/QueryByBidStrategyIds") @InitialValue(getCampaignIdsByBidStrategyIds) } ;
  AddAudienceGroups : string { @DefaultValue(HttpPost) @Label( "/AudienceGroups") @InitialValue(addAudienceGroups) } ;
  UpdateAudienceGroups : string { @DefaultValue(HttpPut) @Label( "/AudienceGroups") @InitialValue(updateAudienceGroups) } ;
  DeleteAudienceGroups : string { @DefaultValue(HttpDelete) @Label( "/AudienceGroups") @InitialValue(deleteAudienceGroups) } ;
  GetAudienceGroupsByIds : string { @DefaultValue(HttpPost) @Label( "/AudienceGroups/QueryByIds") @InitialValue(getAudienceGroupsByIds) } ;
  AddAssetGroups : string { @DefaultValue(HttpPost) @Label( "/AssetGroups") @InitialValue(addAssetGroups) } ;
  UpdateAssetGroups : string { @DefaultValue(HttpPut) @Label( "/AssetGroups") @InitialValue(updateAssetGroups) } ;
  DeleteAssetGroups : string { @DefaultValue(HttpDelete) @Label( "/AssetGroups") @InitialValue(deleteAssetGroups) } ;
  GetAssetGroupsByIds : string { @DefaultValue(HttpPost) @Label( "/AssetGroups/QueryByIds") @InitialValue(getAssetGroupsByIds) } ;
  GetAssetGroupsByCampaignId : string { @DefaultValue(HttpPost) @Label( "/AssetGroups/QueryByCampaignId") @InitialValue(getAssetGroupsByCampaignId) } ;
  GetAssetGroupsEditorialReasons : string { @DefaultValue(HttpPost) @Label( "/AssetGroupsEditorialReasons/Query") @InitialValue(getAssetGroupsEditorialReasons) } ;
  SetAudienceGroupAssetGroupAssociations : string { @DefaultValue(HttpPost) @Label( "/AudienceGroupAssetGroupAssociations/Set") @InitialValue(setAudienceGroupAssetGroupAssociations) } ;
  DeleteAudienceGroupAssetGroupAssociations : string { @DefaultValue(HttpDelete) @Label( "/AudienceGroupAssetGroupAssociations") @InitialValue(deleteAudienceGroupAssetGroupAssociations) } ;
  GetAudienceGroupAssetGroupAssociationsByAssetGroupIds : string { @DefaultValue(HttpPost) @Label( "/AudienceGroupAssetGroupAssociations/QueryByAssetGroupIds") @InitialValue(getAudienceGroupAssetGroupAssociationsByAssetGroupIds) } ;
  GetAudienceGroupAssetGroupAssociationsByAudienceGroupIds : string { @DefaultValue(HttpPost) @Label( "/AudienceGroupAssetGroupAssociations/QueryByAudienceGroupIds") @InitialValue(getAudienceGroupAssetGroupAssociationsByAudienceGroupIds) } ;
  AddAudiences : string { @DefaultValue(HttpPost) @Label( "/Audiences") @InitialValue(addAudiences) } ;
  UpdateAudiences : string { @DefaultValue(HttpPut) @Label( "/Audiences") @InitialValue(updateAudiences) } ;
  DeleteAudiences : string { @DefaultValue(HttpDelete) @Label( "/Audiences") @InitialValue(deleteAudiences) } ;
  GetAudiencesByIds : string { @DefaultValue(HttpPost) @Label( "/Audiences/QueryByIds") @InitialValue(getAudiencesByIds) } ;
  ApplyCustomerListItems : string { @DefaultValue(HttpPost) @Label( "/CustomerListItems/Apply") @InitialValue(applyCustomerListItems) } ;
  ApplyCustomerListUserData : string { @DefaultValue(HttpPost) @Label( "/CustomerListUserData/Apply") @InitialValue(applyCustomerListUserData) } ;
  GetUetTagsByIds : string { @DefaultValue(HttpPost) @Label( "/UetTags/QueryByIds") @InitialValue(getUetTagsByIds) } ;
  AddUetTags : string { @DefaultValue(HttpPost) @Label( "/UetTags") @InitialValue(addUetTags) } ;
  UpdateUetTags : string { @DefaultValue(HttpPut) @Label( "/UetTags") @InitialValue(updateUetTags) } ;
  GetConversionGoalsByIds : string { @DefaultValue(HttpPost) @Label( "/ConversionGoals/QueryByIds") @InitialValue(getConversionGoalsByIds) } ;
  GetConversionGoalsByTagIds : string { @DefaultValue(HttpPost) @Label( "/ConversionGoals/QueryByTagIds") @InitialValue(getConversionGoalsByTagIds) } ;
  AddConversionGoals : string { @DefaultValue(HttpPost) @Label( "/ConversionGoals") @InitialValue(addConversionGoals) } ;
  UpdateConversionGoals : string { @DefaultValue(HttpPut) @Label( "/ConversionGoals") @InitialValue(updateConversionGoals) } ;
  ApplyOfflineConversions : string { @DefaultValue(HttpPost) @Label( "/OfflineConversions/Apply") @InitialValue(applyOfflineConversions) } ;
  ApplyOfflineConversionAdjustments : string { @DefaultValue(HttpPost) @Label( "/OfflineConversionAdjustments/Apply") @InitialValue(applyOfflineConversionAdjustments) } ;
  ApplyOnlineConversionAdjustments : string { @DefaultValue(HttpPost) @Label( "/OnlineConversionAdjustments/Apply") @InitialValue(applyOnlineConversionAdjustments) } ;
  GetOfflineConversionReports : string { @DefaultValue(HttpPost) @Label( "/OfflineConversionReports/Query") @InitialValue(getOfflineConversionReports) } ;
  AddLabels : string { @DefaultValue(HttpPost) @Label( "/Labels") @InitialValue(addLabels) } ;
  DeleteLabels : string { @DefaultValue(HttpDelete) @Label( "/Labels") @InitialValue(deleteLabels) } ;
  UpdateLabels : string { @DefaultValue(HttpPut) @Label( "/Labels") @InitialValue(updateLabels) } ;
  GetLabelsByIds : string { @DefaultValue(HttpPost) @Label( "/Labels/QueryByIds") @InitialValue(getLabelsByIds) } ;
  SetLabelAssociations : string { @DefaultValue(HttpPost) @Label( "/LabelAssociations/Set") @InitialValue(setLabelAssociations) } ;
  DeleteLabelAssociations : string { @DefaultValue(HttpDelete) @Label( "/LabelAssociations") @InitialValue(deleteLabelAssociations) } ;
  GetLabelAssociationsByEntityIds : string { @DefaultValue(HttpPost) @Label( "/LabelAssociations/QueryByEntityIds") @InitialValue(getLabelAssociationsByEntityIds) } ;
  GetLabelAssociationsByLabelIds : string { @DefaultValue(HttpPost) @Label( "/LabelAssociations/QueryByLabelIds") @InitialValue(getLabelAssociationsByLabelIds) } ;
  AddExperiments : string { @DefaultValue(HttpPost) @Label( "/Experiments") @InitialValue(addExperiments) } ;
  DeleteExperiments : string { @DefaultValue(HttpDelete) @Label( "/Experiments") @InitialValue(deleteExperiments) } ;
  UpdateExperiments : string { @DefaultValue(HttpPut) @Label( "/Experiments") @InitialValue(updateExperiments) } ;
  GetExperimentsByIds : string { @DefaultValue(HttpPost) @Label( "/Experiments/QueryByIds") @InitialValue(getExperimentsByIds) } ;
  GetProfileDataFileUrl : string { @DefaultValue(HttpPost) @Label( "/ProfileDataFileUrl/Query") @InitialValue(getProfileDataFileUrl) } ;
  SearchCompanies : string { @DefaultValue(HttpPost) @Label( "/Companies/Search") @InitialValue(searchCompanies) } ;
  GetFileImportUploadUrl : string { @DefaultValue(HttpPost) @Label( "/FileImportUploadUrl/Query") @InitialValue(getFileImportUploadUrl) } ;
  AddImportJobs : string { @DefaultValue(HttpPost) @Label( "/ImportJobs") @InitialValue(addImportJobs) } ;
  GetImportResults : string { @DefaultValue(HttpPost) @Label( "/ImportResults/Query") @InitialValue(getImportResults) } ;
  GetImportJobsByIds : string { @DefaultValue(HttpPost) @Label( "/ImportJobs/QueryByIds") @InitialValue(getImportJobsByIds) } ;
  DeleteImportJobs : string { @DefaultValue(HttpDelete) @Label( "/ImportJobs") @InitialValue(deleteImportJobs) } ;
  GetImportEntityIdsMapping : string { @DefaultValue(HttpPost) @Label( "/ImportEntityIdsMapping/Query") @InitialValue(getImportEntityIdsMapping) } ;
  UpdateImportJobs : string { @DefaultValue(HttpPut) @Label( "/ImportJobs") @InitialValue(updateImportJobs) } ;
  AddVideos : string { @DefaultValue(HttpPost) @Label( "/Videos") @InitialValue(addVideos) } ;
  DeleteVideos : string { @DefaultValue(HttpDelete) @Label( "/Videos") @InitialValue(deleteVideos) } ;
  GetVideosByIds : string { @DefaultValue(HttpPost) @Label( "/Videos/QueryByIds") @InitialValue(getVideosByIds) } ;
  UpdateVideos : string { @DefaultValue(HttpPut) @Label( "/Videos") @InitialValue(updateVideos) } ;
  AddCampaignConversionGoals : string { @DefaultValue(HttpPost) @Label( "/CampaignConversionGoals") @InitialValue(addCampaignConversionGoals) } ;
  DeleteCampaignConversionGoals : string { @DefaultValue(HttpDelete) @Label( "/CampaignConversionGoals") @InitialValue(deleteCampaignConversionGoals) } ;
  AddDataExclusions : string { @DefaultValue(HttpPost) @Label( "/DataExclusions") @InitialValue(addDataExclusions) } ;
  UpdateDataExclusions : string { @DefaultValue(HttpPut) @Label( "/DataExclusions") @InitialValue(updateDataExclusions) } ;
  DeleteDataExclusions : string { @DefaultValue(HttpDelete) @Label( "/DataExclusions") @InitialValue(deleteDataExclusions) } ;
  GetDataExclusionsByIds : string { @DefaultValue(HttpPost) @Label( "/DataExclusions/QueryByIds") @InitialValue(getDataExclusionsByIds) } ;
  GetDataExclusionsByAccountId : string { @DefaultValue(HttpPost) @Label( "/DataExclusions/QueryByAccountId") @InitialValue(getDataExclusionsByAccountId) } ;
  AddSeasonalityAdjustments : string { @DefaultValue(HttpPost) @Label( "/SeasonalityAdjustments") @InitialValue(addSeasonalityAdjustments) } ;
  UpdateSeasonalityAdjustments : string { @DefaultValue(HttpPut) @Label( "/SeasonalityAdjustments") @InitialValue(updateSeasonalityAdjustments) } ;
  DeleteSeasonalityAdjustments : string { @DefaultValue(HttpDelete) @Label( "/SeasonalityAdjustments") @InitialValue(deleteSeasonalityAdjustments) } ;
  GetSeasonalityAdjustmentsByIds : string { @DefaultValue(HttpPost) @Label( "/SeasonalityAdjustments/QueryByIds") @InitialValue(getSeasonalityAdjustmentsByIds) } ;
  GetSeasonalityAdjustmentsByAccountId : string { @DefaultValue(HttpPost) @Label( "/SeasonalityAdjustments/QueryByAccountId") @InitialValue(getSeasonalityAdjustmentsByAccountId) } ;
  CreateAssetGroupRecommendation : string { @DefaultValue(HttpPost) @Label( "/AssetGroupRecommendation/Create") @InitialValue(createAssetGroupRecommendation) } ;
  CreateResponsiveAdRecommendation : string { @DefaultValue(HttpPost) @Label( "/ResponsiveAdRecommendation/Create") @InitialValue(createResponsiveAdRecommendation) } ;
  CreateResponsiveSearchAdRecommendation : string { @DefaultValue(HttpPost) @Label( "/ResponsiveSearchAdRecommendation/Create") @InitialValue(createResponsiveSearchAdRecommendation) } ;
  RefineAssetGroupRecommendation : string { @DefaultValue(HttpPost) @Label( "/AssetGroupRecommendation/Refine") @InitialValue(refineAssetGroupRecommendation) } ;
  RefineResponsiveAdRecommendation : string { @DefaultValue(HttpPost) @Label( "/ResponsiveAdRecommendation/Refine") @InitialValue(refineResponsiveAdRecommendation) } ;
  RefineResponsiveSearchAdRecommendation : string { @DefaultValue(HttpPost) @Label( "/ResponsiveSearchAdRecommendation/Refine") @InitialValue(refineResponsiveSearchAdRecommendation) } ;
  GetResponsiveAdRecommendationJob : string { @DefaultValue(HttpPost) @Label( "/ResponsiveAdRecommendationJob/Query") @InitialValue(getResponsiveAdRecommendationJob) } ;
  UpdateConversionValueRules : string { @DefaultValue(HttpPut) @Label( "/ConversionValueRules") @InitialValue(updateConversionValueRules) } ;
  UpdateConversionValueRulesStatus : string { @DefaultValue(HttpPut) @Label( "/ConversionValueRulesStatus") @InitialValue(updateConversionValueRulesStatus) } ;
  AddConversionValueRules : string { @DefaultValue(HttpPost) @Label( "/ConversionValueRules") @InitialValue(addConversionValueRules) } ;
  GetConversionValueRulesByAccountId : string { @DefaultValue(HttpPost) @Label( "/ConversionValueRules/QueryByAccountId") @InitialValue(getConversionValueRulesByAccountId) } ;
  GetConversionValueRulesByIds : string { @DefaultValue(HttpPost) @Label( "/ConversionValueRules/QueryByIds") @InitialValue(getConversionValueRulesByIds) } ;
  AddBrandKits : string { @DefaultValue(HttpPost) @Label( "/BrandKits") @InitialValue(addBrandKits) } ;
  UpdateBrandKits : string { @DefaultValue(HttpPut) @Label( "/BrandKits") @InitialValue(updateBrandKits) } ;
  DeleteBrandKits : string { @DefaultValue(HttpDelete) @Label( "/BrandKits") @InitialValue(deleteBrandKits) } ;
  CreateBrandKitRecommendation : string { @DefaultValue(HttpPost) @Label( "/BrandKitRecommendation/Create") @InitialValue(createBrandKitRecommendation) } ;
  AddNewCustomerAcquisitionGoals : string { @DefaultValue(HttpPost) @Label( "/NewCustomerAcquisitionGoals") @InitialValue(addNewCustomerAcquisitionGoals) } ;
  UpdateNewCustomerAcquisitionGoals : string { @DefaultValue(HttpPut) @Label( "/NewCustomerAcquisitionGoals") @InitialValue(updateNewCustomerAcquisitionGoals) } ;
  GetNewCustomerAcquisitionGoalsByAccountId : string { @DefaultValue(HttpPost) @Label( "/NewCustomerAcquisitionGoals/QueryByAccountId") @InitialValue(getNewCustomerAcquisitionGoalsByAccountId) } ;
  GetBrandKitsByAccountId : string { @DefaultValue(HttpPost) @Label( "/BrandKits/QueryByAccountId") @InitialValue(getBrandKitsByAccountId) } ;
  GetBrandKitsByIds : string { @DefaultValue(HttpPost) @Label( "/BrandKits/QueryByIds") @InitialValue(getBrandKitsByIds) } ;
  GetClipchampTemplates : string { @DefaultValue(HttpPost) @Label( "/ClipchampTemplates/Query") @InitialValue(getClipchampTemplates) } ;
  GetSupportedClipchampAudio : string { @DefaultValue(HttpPost) @Label( "/SupportedClipchampAudio/Query") @InitialValue(getSupportedClipchampAudio) } ;
  GetSupportedFonts : string { @DefaultValue(HttpPost) @Label( "/SupportedFonts/Query") @InitialValue(getSupportedFonts) } ;
  GetHealthCheck : string { @DefaultValue(HttpPost) @Label( "/HealthCheck/Query") @InitialValue(getHealthCheck) } ;
  GetDiagnostics : string { @DefaultValue(HttpPost) @Label( "/Diagnostics/Query") @InitialValue(getDiagnostics) } ;
  GetAnnotationOptOut : string { @DefaultValue(HttpPost) @Label( "/AnnotationOptOut/Query") @InitialValue(getAnnotationOptOut) } ;
  UpdateAnnotationOptOut : string { @DefaultValue(HttpPut) @Label( "/AnnotationOptOut") @InitialValue(updateAnnotationOptOut) } ;
}
