using System;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public partial class ColumnMap
    {
        public static Dictionary<string, string> ColumnDefaultValues = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "absolutetopimpressioncnt","(0)" },
            { "accountid","(0)" },
            { "actiondate","getutcdate()" },
            { "adextensionid","(0)" },
            { "adextensiontypeattributebitmask","NULL" },
            { "adextensiontypeid","(0)" },
            { "adextimpressioncnt","(0)" },
            { "adid","(0)" },
            { "adimpressioncnt","(0)" },
            { "adlandingpageurlid","(0)" },
            { "adposition","''" },
            { "adscenariotype","(0)" },
            { "adunitid","(0)" },
            { "advancedbookingwindow","(1)" },
            { "advertiserreportedrevenue","(0)" },
            { "advertiserreportedrevenueadjustment","(0)" },
            { "advertisingchanneltypeid","(0)" },
            { "advertisingsubchanneltypeid","(0)" },
            { "allconversioncredit","(0)" },
            { "areacode","(0)" },
            { "assetbasedentityassociationtypeid","(0)" },
            { "assistcnt","(0)" },
            { "assistcount","(0)" },
            { "auctionlosttobudgetabstopcnt","(0)" },
            { "auctionlosttobudgettopcnt","(0)" },
            { "auctionlosttoothercnt","(0)" },
            { "auctionlosttorankabstopcnt","(0)" },
            { "auctionlosttorankaggcnt","(0)" },
            { "auctionlosttoranktopcnt","(0)" },
            { "auctionparticipantclickcnt","(0)" },
            { "audienceauctionlosttobudgetcnt","(0)" },
            { "audienceauctionlosttorankcnt","(0)" },
            { "audienceauctionparticipantcnt","(0)" },
            { "audienceauctionwoncnt","(0)" },
            { "audienceclicks","(0)" },
            { "audienceid","(0)" },
            { "audiencetypeid","(0)" },
            { "baseprice","(0)" },
            { "batchid","(-1)" },
            { "biddedmatchtypeid","(0)" },
            { "biddingschemeid","(0)" },
            { "bidstrategyid","(0)" },
            { "bookedadvancedbookingwindow","(0)" },
            { "bscauctionparticipation","(0)" },
            { "budgetid","(0)" },
            { "callendreason","(0)" },
            { "campaignfeaturebitmask","(0)" },
            { "campaignid","(0)" },
            { "channeltypeid","(1)" },
            { "city","''" },
            { "clickcnt","(0)" },
            { "clicks","(0)" },
            { "clicktimesconversioncredit","(0)" },
            { "clicktypeclickcnt","(0)" },
            { "clicktypeconversioncnt","(0)" },
            { "clicktypeconversioncredit","(0)" },
            { "clicktypeid","(0)" },
            { "clicktypeimpressioncnt","(0)" },
            { "collectionid","(0)" },
            { "combinationflag","(1)" },
            { "completedviewcnt","(0)" },
            { "contractversion","''" },
            { "conversioncnt","(0)" },
            { "conversioncount","(0)" },
            { "conversioncredit","(0)" },
            { "conversioncreditsumofsquare","(0)" },
            { "conversioncredittimesrevenue","(0)" },
            { "conversionenabledclickcnt","(0)" },
            { "conversionenabledtotalamount","(0)" },
            { "cooperativeclickcnt","(0)" },
            { "cooperativeconversioncnt","(0)" },
            { "cooperativeconversioncredit","(0)" },
            { "cooperativeimpressioncnt","(0)" },
            { "createdby","suser_name()" },
            { "createdbyuserid","(-1)" },
            { "createddatetime","getutcdate()" },
            { "createddtim","getutcdate()" },
            { "createdtime","getutcdate()" },
            { "createprocessid","(-1)" },
            { "customerid","(0)" },
            { "daily","(-1)" },
            { "datafilepathpattern","''" },
            { "datarowcount","(0)" },
            { "datekey","CONVERT([varchar],getdate(),(112))" },
            { "datetype","(0)" },
            { "dayofweek1","(-1)" },
            { "dayofweek104","(-1)" },
            { "dayofweek156","(-1)" },
            { "dayofweek20","(-1)" },
            { "dayofweek4","(-1)" },
            { "dayofweek52","(-1)" },
            { "deliverychanneltypeid","(0)" },
            { "deliveryformatid","(0)" },
            { "deltatablename","''" },
            { "demandtypeid","(0)" },
            { "dependentfactgroups","''" },
            { "deploymentdtime","getdate()" },
            { "deviceosid","(0)" },
            { "deviceosid2","(0)" },
            { "devicetypeid","(0)" },
            { "distributionchannelid","(0)" },
            { "domaintypeid","(0)" },
            { "downloadenabled","(1)" },
            { "downloads","(0)" },
            { "downloadtimeinsecond","(0)" },
            { "elementid","(0)" },
            { "emauctionparticipation","(0)" },
            { "emauctionwon","(0)" },
            { "extendedcost","(0)" },
            { "externaleffectiveeditorialstatusid","(1)" },
            { "extractstarteddatetime","getutcdate()" },
            { "extrasplitpartitions","(0)" },
            { "feedorigin","(0)" },
            { "firstlaunches","(0)" },
            { "forcesequence","(0)" },
            { "formatfilepathpattern","''" },
            { "fulladvertiserreportedrevenue","(0)" },
            { "fulladvertiserreportedrevenueadjustment","(0)" },
            { "fullclicktimesconversion","(0)" },
            { "fullclicktimesconversioncredit","(0)" },
            { "fullconversioncnt","(0)" },
            { "fullconversioncredit","(0)" },
            { "fullconversioncreditsumofsquare","(0)" },
            { "fullconversioncredittimesrevenue","(0)" },
            { "fullconversionsumofsquare","(0)" },
            { "fullconversiontimesrevenue","(0)" },
            { "fullconversionvaluecnt","(0)" },
            { "fullconversionvaluesumofsquare","(0)" },
            { "fullconversionvaluetimesrevenue","(0)" },
            { "fullviewadvertiserreportedrevenue","(0)" },
            { "fullviewadvertiserreportedrevenueadjustment","(0)" },
            { "fullviewconversioncnt","(0)" },
            { "fullviewconversioncredit","(0)" },
            { "fullviewconversioncreditsumofsquare","(0)" },
            { "geolocationid","(0)" },
            { "goalcategory","(0)" },
            { "goalid","(0)" },
            { "goaltypeid","(0)" },
            { "hourlystartdtimutc","getutcdate()" },
            { "impressioncnt","(0)" },
            { "impressionsumofsquare","(0)" },
            { "impressionwithpositioncnt","(0)" },
            { "installcnt","(0)" },
            { "invalidconversioncredit","(0)" },
            { "invalidgeneralclickcnt","(0)" },
            { "isderivedtable","(0)" },
            { "isglobalstore","(0)" },
            { "islastbatch","(0)" },
            { "ismainconversiongoal","(1)" },
            { "ismsanswfrecord","(0)" },
            { "isother","(0)" },
            { "ispageurl","(0)" },
            { "lastmodifieddatetime","getutcdate()" },
            { "lengthofstay","(0)" },
            { "loadgroupsequence","(1)" },
            { "loadtotablename","''" },
            { "localstorecode","''" },
            { "logdate","getutcdate()" },
            { "matchtypeid","(0)" },
            { "mediumid","(0)" },
            { "metafilepathpattern","''" },
            { "modifiedbyuser","suser_sname()" },
            { "modifieddatetime","getutcdate()" },
            { "modifieddtim","getutcdate()" },
            { "modifiedtime","getutcdate()" },
            { "monthly","(-1)" },
            { "networkid","(0)" },
            { "newcustomerconversioncredit","(0)" },
            { "newcustomerrevenue","(0)" },
            { "nextactiontime","getutcdate()" },
            { "onlinephonecallcnt","(0)" },
            { "onlinephonecost","(0)" },
            { "orderid","(0)" },
            { "orderitemid","(0)" },
            { "pagepositionid","(0)" },
            { "partnerclick","(0)" },
            { "partnereligibleimpression","(0)" },
            { "partnerimpression","(0)" },
            { "partnermissedimpression","(0)" },
            { "partnermissedimpressioninsufficientbid","(0)" },
            { "partnermissedimpressionnobid","(0)" },
            { "partnermissedimpressionnotax","(0)" },
            { "partnermissedimpressionother","(0)" },
            { "partnermissedimpressionspendingcapreached","(0)" },
            { "percent25viewcnt","(0)" },
            { "percent50viewcnt","(0)" },
            { "percent75viewcnt","(0)" },
            { "phonecost","(0)" },
            { "phoneimpressioncnt","(0)" },
            { "pricingmodelid","(0)" },
            { "processid","(-1)" },
            { "productboughttitle","''" },
            { "productid","(0)" },
            { "providerid","(0)" },
            { "publishercountryid","(191)" },
            { "purchasedgtin","''" },
            { "purchases","(0)" },
            { "qualitybandid","(0)" },
            { "quantitybought","(0)" },
            { "quarterly","(-1)" },
            { "querygeolocationid","(0)" },
            { "region","''" },
            { "relationshipid","(0)" },
            { "reorderdata","(0)" },
            { "retries","(0)" },
            { "retrycount","(0)" },
            { "rollupfromsamegrain","(0)" },
            { "rowid","(1)" },
            { "runinci","(1)" },
            { "runtimeinsecond","(0)" },
            { "salescnt","(0)" },
            { "samesectionclickcnt","(0)" },
            { "samesectionimpressioncnt","(0)" },
            { "searchabstoppositionsumofsquare","(0)" },
            { "searchtoppositionsumofsquare","(0)" },
            { "searchuniqueimpressioncnt","(0)" },
            { "searchuniqueimpressionsumofsquare","(0)" },
            { "shoppingabstoppositionsumofsquare","(0)" },
            { "shoppinguniqueimpressioncnt","(0)" },
            { "shoppinguniqueimpressionsumofsquare","(0)" },
            { "sitetype","(1)" },
            { "skippedviewcnt","(0)" },
            { "slotid","(0)" },
            { "sosid","(0)" },
            { "sourcefactgroup","''" },
            { "startdtimutc","getutcdate()" },
            { "startedviewcnt","(0)" },
            { "status","(3)" },
            { "submatchtypeid","(0)" },
            { "subscriptions","(0)" },
            { "targetcompanysizeid","(0)" },
            { "targetedlocationtypeid","(0)" },
            { "targettypeid","(0)" },
            { "targetvalueid","(0)" },
            { "topimpressioncnt","(0)" },
            { "totalamount","(0)" },
            { "totalamountusd","(0)" },
            { "totalbookednights","(0)" },
            { "totalconversioncnt","(0)" },
            { "totalconversioncredit","(0)" },
            { "totalconversioncreditsumofsquare","(0)" },
            { "totalconversionsumofsquare","(0)" },
            { "totalpartnerclick","(0)" },
            { "totalposition","(0)" },
            { "totalprice","(0)" },
            { "totalsearchabstopposition","(0)" },
            { "totalsearchtopposition","(0)" },
            { "totalshoppingabstopposition","(0)" },
            { "totalwatchtime","(0)" },
            { "undonedate","getutcdate()" },
            { "uniqueconversioncredit","(0)" },
            { "updateddatetime","getutcdate()" },
            { "updateddtim","getutcdate()" },
            { "version","(0)" },
            { "viewcnt","(0)" },
            { "viewthroughlookbackwindowinminutes","(0)" },
            { "weekly","(-1)" },
            { "yearly","(-1)" }
        };
    }
}
