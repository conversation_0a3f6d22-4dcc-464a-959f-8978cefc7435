namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter
{
    using Common;
    using Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO;
    using Microsoft.AdCenter.Shared.MT;
    using System;
    using System.Collections;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Threading;
    using System.Xml.Serialization;
    using PilotCC = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade.Pilot;

    //We are currently changing pilot flags from a long to a BitArray. If adding new pilot flags while this comment
    //is still here, please add the flags to both flag enums and add the mapping in the dictionary in the Pilot class
    public enum CustomerFeatureFlag : int
    {
        NoFeature = 0,
        Content = 1,
        MobileAd = 2,
        LocalAd = 4,
        CPM = 5,
        QualityScore = 10,
        PremiumBanner = 14,
        PremiumFlash = 15,
        AgeAndGenderExclusiveTargeting = 17,
        ReMessaging = 18,
        TPCAd = 19,
        AdultAds = 21,
        NegativeKeywords = 23,
        RichSearchAd = 26,
        RichSearchAd4DeepLinks = 30,
        MarketRedef = 35,
        LocalAttributes = 42,
        OSTargetting = 43,
        ClickToCallOnly = 47,
        SiteLinksAdExtensions = 56,
        InLineBudgetSuggestions = 58,
        AdRotation = 59,
        LocationExclusion = 62,
        LocationAdExtensions = 66,
        CallAdExtensionsOrCallTracking = 67,
        SearchTerms = 68,
        ProductListingAd = 70,
        CallTracking = 78,
        CPDAd = 87,
        ArabicLanguage = 102,
        SimplifiedChineseLanguage = 104,
        DayTimePostalCodeTarget = 116,
        KeywordVariantMatchType = 118,
        IncreasedKeywordLimit = 120,
        EnhancedSiteLinksAdExtensions = 122,
        ImageAdExtensions = 123,
        RadiusTargetingImprovements = 124,
        AggregatorPerfImprovement = 128,
        CampaignImageAdExtensions = 133,
        BscPilotFlagId = 134,
        AppAdExtensions = 135,
        GeoLocations10KLimit = 138,
        DeviceTargetingConsolidation = 139,
        DeviceTargetingConsolidationOptOut = 144,
        NewsAdExtension = 159,
        EditableKeywordText = 162,
        EnhancedShoppingCampaigns = 163,
        DeviceTargetingConsolidationPhase2 = 166,
        EnableNativeAds = 190,
        UpgradedUrls = 194,
        ImageExtensionForNativeAds = 203,
        ContentAdvertising = 208,
        MultiAccountDownloadUpLoad = 209,
        SegmentationPhase1 = 210,
        CalloutAdExtension = 216,
        ReviewExtension = 218,
        AdvertiserRulesHourly = 225,
        BlockProductTargets = 232,
        AccountAlerts = 239,
        UpgradedUrlsForEnhancedShoppingCampaigns = 242,
        ActionLinkExtension = 244,
        CampaignAnalyticsMigration = 249,
        SitelinkExtensionFlattening = 253,
        StructuredSnippetExtension = 255, // Needed here for AdExtension Pilot to DBType Mapping
        DynamicSearchAds = 268,
        ExpandedDeviceTargeting = 274,
        PriceAdExtension = 302, // Needed here for AdExtension Pilot to DBType Mapping
        VanityUrl = 311,
        InMarketAudience = 315,
        RemarketingSimilarAudience = 317,
        AdExtensionDeviceTarget = 321,
        AutoBiddingSupportEnhancedCpcForBSC = 340,
        HotelAdsV1 = 344,
        MatchedProductCount = 363,
        LocalInventoryAdsEnabled = 351,
        InStoreTransaction = 353,
        GoalCurrencyAcrossAllAccounts = 359,
        DisableSTA = 370,
        WhitelistSTA = 371,
        AIM = 388,
        AdGroupNameCharLimit = 391,
        CoOpCampaign = 425,
        ReportEmailRule = 429,
        CustomerHierarchy = 449,
        UserBasedEditorialPhase1 = 450,
        InHousePromotion = 453,
        LinkedInTargetingforSearchDsaBsc = 467,
        GoogleImportInMarketAudienceCriterion = 472,
        AdClickParallelTracking = 474,
        AdCustomizer = 480,
        LocalInventoryAdsV2Enabled = 486,
        ProductPartitionNodeLowBidWarning = 487,
        Experiment = 500,
        SharedLibrary = 506,
        ProductPartitionNodeRaiseLowBidToMinServing = 508,
        CampaignLevelAudienceTargeting = 514,
        ResponsiveSearchAd = 525,
        EnhancedResponsiveAd = 526,
        KwdDestUrlUpdateDisabledExclusion = 531,
        AIMCampaignLevelAudienceTargeting = 534,
        IFFunctionForEXTA = 570,
        SmartCampaign = 571,
        ConversionGoalSelection = 574,
        CustomerMatch = 579,
        ProductConversionGoalFlagId = 643,
        CustomerMatchCRM = 580,
        SOVTopISColumns = 594,
        ExpandedDsaPhase2 = 600,
        SiteLinkAdExtensionDestUrlUpdateDisabledInclusion = 601,
        SiteLinkAdExtensionDestUrlUpdateDisabledExclusion = 603,
        DynamicDataFeed = 607,
        ViewThroughConversion = 616,
        ScheduledFeedUpload = 617,
        DisclaimerAds = 622, // Needed here for AdExtension Pilot to DBType Mapping
        CampaignInMarketAudienceCriterionGoogleImport = 632,
        CookieBasedExperiments = 637,
        RsaCombinationExtendedBI = 640,
        // EnLanguageExpansionToFiveMarkets = 641,
        //DynamicSearchAdsInCHandNLandITandESandSE = 653,
        AdScheduleTimeZoneSetting = 655,
        SponsoredProductAdsV2 = 684,
        // MultiAccountImport = 687,
        ManagerAccountSharedWebsiteExclusions = 697,
        AutoBiddingTargetRoasEnabledForTop5IntlMarkets = 704,
        AutoBiddingMaxConversionValueEnabled = 716,
        SmartShoppingCampaign = 718,
        //SpanishLanguageExpansionToUSMarket = 719,
        PromotionAdExtension = 720, // Needed here for AdExtension Pilot to DBType Mapping
        AutoBiddingTargetRoasEnabledNewPilot = 721,
        FilterLinkAdExtension = 732, // Needed here for AdExtension Pilot to DBType Mapping
        GoogleImportApiEnabled = 734,
        AutoBiddingDropConversionCountAndRevenueCheckPilot = 740,
        ConversionGoalCategoryFlagId = 743,
        FileImportApiEnabled = 755,
        AutoBiddingTargetImpressionShareEnabled = 762,
        IsCustomerEnabledForHotelPropertyPromotionAd = 764,
        //FeedItemInBlob = 765,
        AccountReparentingUET = 770,
        ThirdPartyConversions = 773,
        EnableResponsiveAdsForSmartShopping = 777,
        SmartPage = 782,
        ManualBiddingDeprecationOptOut = 786,
        OfflineConversionRestateRetract = 787,
        ShoppingAdsJapanMarket = 796,
        FlyerAdExtension = 802, // Needed here for AdExtension Pilot to DBType Mapping
        DynamicSearchAdsGlobalizationEUPhase1 = 803,
        DSADanishFinnishNorwegianSupport = 804,
        UnifiedProduct = 810,
        ShoppingAdsPolandMarket = 818,
        ShoppingAdsPortugalMarket = 819,
        ShoppingAdsGreeceMarket = 821,
        //EnglishLanguageMarketExpansion = 827,
        VideoAds = 840,
        VideoAdExtension = 841, // Needed here for AdExtension Pilot to DBType Mapping
        AccountLabels = 847,
        ManualCpm = 858,
        BSCImportMappingImprovement = 876,
        RSACountdown = 883,
        RSALocation = 884,
        ParallelTrackingExceptionList = 890,
        CCAPISupportingUnifiedProduct = 891,
        SharedRemarketingAudienceGoogleImport = 856,
        InStoreVisitConversion = 902,
        VideoAdEditor = 904,
        DynamicSearchAdsGlobalizationEUPhase2 = 916,
        UnifiedProductCampaignAPI = 928,
        UETTagClarityIntegration = 936,
        GoogleTagManagerImport = 964,
        HotelCampaign = 973,
        ShoppingAdsGBLPhase3APACMarket = 978,
        AutoTagAssociationForRemarketingList = 982,
        ShoppingAdsGBLPhase3HKTWMarkets = 992,
        ChannelPartnerExpertCampaignSupport = 996,
        NeighborhoodLocationTargeting = 1008,
        RemarketingListBasedParameters = 1017,
        SubscriptionAds = 1021,
        GoogleImportVideoAds = 1028,
        DynamicSearchAdsGlobalizationJapan = 1044,
        ShoppingAdsGBLPhase3America = 1048,
        ShoppingAdsGBLPhase3APAC = 1049,
        AudienceCampaigneCPCAutoBidding = 1053,
        ConversionGoalAttributionModel = 1054,
        M365IntegrationSupport = 1059,
        ShoppingAdsGBLPhase5Africa = 1064,
        ShoppingAdsGBLPhase6Mena = 1065,
        WhitelistEXTA = 1074,
        DisableEXTA = 1075,
        DynamicSearchAdsGlobalizationMENA = 1063,
        DynamicDataAutosListingFeed = 1081,
        DynamicDataCreditCardsFeed = 1082,
        DynamicDataCruisesFeed = 1083,
        DynamicDataCustomFeed = 1084,
        DynamicDataEventsFeed = 1085,
        DynamicDataHealthInsuranceFeed = 1086,
        DynamicDataHotelsAndVacationRentalsFeed = 1087,
        DynamicDataProfessionalServiceFeed = 1088,
        DynamicDataToursAndActivitiesFeed = 1089,
        DynamicDataMortgageLendersFeed = 1090,
        DynamicDataAutosAggregateFeed = 1091,
        Boost = 1105,
        WhitelistEXTAAdCustomizerFeedUpdate = 1117,
        DynamicDataDebitCardsFeed = 1122,
        OnlineConversion = 1124,
        SponsoredPromotionsForBrands = 1128,
        OfflineConversionDailyReport = 1135,
        DynamicSearchAdsGlobalizationSimplifiedChinese = 1134,
        DynamicSearchAdsGlobalizationPhase9 = 1396,
        DynamicSearchAdsGlobalizationPhase9VI = 1302,
        CustomEventTool = 1163,
        ConversionTile = 1126,
        ShoppingAdsGBLPhase7 = 1155,
        LeadFormAdExtension = 1170,
        EnhancedCustomCombinationLists = 1171,
        CNMarketExpansion = 1183,
        EnableGoalLevelChange = 1191,
        TagDeletion = 1192,
        DynamicDataJobListingsFeed = 1200,
        ShoppingFeedLabel = 1224,
        EnhancedConversions = 1229,
        ESCAudienceAdsSupportV2 = 1235,
        LodgingRename = 1239,
        AppealDecisionUpheld = 1244,
        WebInsights = 1253,
        EnableAutobiddingLimitedStatus = 1255,
        MultiChannelCampaign = 1259,
        YahooExclusionInSWF = 1265,
        PMaxLite = 1268,
        SharedLibraryForOtherAudienceType = 1281,
        ConversionValueRule = 1282,
        DatamartClickhouseMigration = 1291,
        ShoppingAdsGBLPhase8 = 1294,
        DatamartClickhouseMigrationPhase2 = 1297,
        BingPlacesMSASignup = 1320,
        KeywordTargeting = 1323,
        EnableGoalBulk = 1356,
        ShoppingFeedLabelDisable = 1382,
        ImpressionBasedRemarketingList = 1393,
        VanityPharmaWebsiteDescription = 1402,
        ShoppingMRANECPC = 1403, 
        ShoppingMRANTROAS = 1404,
        AutoConversion = 1369,
        PerformanceMaxCampaignAssetGroupSearchTheme = 1424,
        ShoppingAimandNativePAUnification = 1371,
        BoostConversionBasedSegment = 1431,
        LogoAdExtensionCN = 1436,
        ImpressionBasedRemarketingForSearchAndPMax = 1454,
        CombinationReporting = 1489,
        EnableMistralTextGeneration = 1504,
        BrandKit = 1505,
        BrandKitForPhase2 = 1575,
        EnableDisplayAdsTemplateV1_1 = 1506,
        EnableDKI = 1511,
        AccountPlacementExclusionList = 1525,
        AccountPlacementInclusionList = 1526,
        CustomerMigratedFromBulkDB = 1553,
        LifetimeBudget = 1535,
        VerticalCatetoryReport = 1560,
        CustomSegmentEditorialIntegration = 1566,
        ShoppingAdsGlobalizationPAKorea = 1537,
        EnableBestPracticeThemeGeneration = 1588,
        TravelQueryInsightsReport = 1610,
        AutobiddingConsolidationFillGaps = 1618,
        AutobiddingConsolidation = 1619,
        PmaxSovMetrics = 1625,
        IBRMultiCampaigns = 1626,
        CallOutSSAndPriceAdExtensionCN = 1628,
        ConsentModeValue = 1639,
    }

    //This is a wrapper for BitArray such that OnGetLockTimeout is implementable with both PilotCC flags and customer feature flags
    public class CustomerFeatures
    {
        public BitArray Flags { get; set; }

        public CustomerFeatures(params CustomerFeatureFlag[] initialFlags)
        {
            Flags = new BitArray(Pilot.customerFeatureFlags.Length, false);
            foreach (CustomerFeatureFlag flag in initialFlags)
            {
                Flags[Pilot.ccToMTPilotFlagMapping[flag]] = true;
            }
        }

        public void AddFlags(params CustomerFeatureFlag[] flags)
        {
            foreach (CustomerFeatureFlag flag in flags)
            {
                Flags[Pilot.ccToMTPilotFlagMapping[flag]] = true;
            }
        }
    }

    public class CachedPilotFlags
    {
        public CachedPilotFlags(int customerId, BitArray flags)
        {
            this.CustomerId = customerId;
            this.Flags = flags;
        }

        public int CustomerId { get; set; }

        public BitArray Flags { get; set; }
    }

    /// <summary>
    /// Structure for PilotFlags. If the DBId for a particular feature is X, then the X bit of the BitArray will be set. (BitArray as part of Systems.Collections)
    /// Example:
    /// FeatureID    FeatureName BitIndexSet
    /// ------------------------------------
    ///  1           Content
    ///  2           Mobile
    ///  3           Image
    ///  4           Local
    ///  5           CPM
    ///  6           MinBid
    ///  7           RichTextAd
    ///  8           Desktop
    ///  9           Pwp
    /// 10           QualityScore
    /// 12           BT Bidding
    /// 13           BT Targetting
    /// 14           Premium Banner Pilot Flag
    /// 15           Premium Flash Pilot Flag
    /// 16           CPA
    /// 17           Age and gender exclusive targeting
    /// 18           Re-messaging pilot
    /// 19           Third Party Creatives
    /// 20           Custom Segment
    /// 30           RichSearchAd4DeepLinks
    /// 56           Site Links AdExtensions
    /// 57           Inline Editorial Appeal
    /// 58           Inline budget suggestions
    /// 59           Ad Rotation
    /// 62           Location Exclusion
    /// 63           LocationExtension
    /// 64           CallExtension
    /// 65
    /// 66           Location Extension v2
    /// 67           Call Extension v2
    /// 68           Search Terms
    /// 78           Call Metering
    /// 87           CPDAd
    /// 122          EnhancedSiteLinksAdExtension
    /// 135          AppAdExtension
    /// 162          EditableKeywordText
    /// </summary>
    public class Pilot : ICacheDictionaryBehavior<int, CustomerFeatures, CampaignManagementRequest>, ICacheDictionaryBehavior<int, BitArray, CampaignManagementRequest>
    {
        private static readonly Pilot instance = new Pilot();

        internal CacheDictionary<int, CustomerFeatures> customerFeatures = new CacheDictionary<int, CustomerFeatures>();

        private readonly CacheDictionary<int, bool> bypassPilotChecksUsersCache = new CacheDictionary<int, bool>();

        internal static CustomerFeatureFlag[] customerFeatureFlags = CreateCustomerFeatureFlagsEnumArray();

        internal static Dictionary<CustomerFeatureFlag, int> ccToMTPilotFlagMapping = CreateCCToMTPilotFlagMapping();

        private static long customerFeaturesCacheHits;

        private static long customerFeaturesCacheMisses;

        private static int customerFeaturesCacheLogCounter;

        //stored as longs because Interlocked.Exchange() cannot be used with DateTime
        private static long timeSinceLastCustomerFeaturesCacheLog;

        private readonly long customerFeaturesCacheLogSpan = new TimeSpan(0, 5, 0).Ticks;

        private Thread refreshThread;

        //Dictionary used to track which pilot flags are used by admin accounts and therefore returned true regardless of whether they're turned on
        private readonly ConcurrentDictionary<int, byte> adminPilotFlags = new ConcurrentDictionary<int, byte>();

        /// <summary>
        /// Expose a way to used pilot checks against cached pilot flags
        /// </summary>
        /// <param name="cachedPilotFlags">the cached pilot flags</param>
        public Pilot(CachedPilotFlags cachedPilotFlags)
        {
            CustomerFeatures flags = this.GetCustomerFeature(cachedPilotFlags.Flags);
            this.customerFeatures.Add(cachedPilotFlags.CustomerId, flags);
        }

        private Pilot()
        {
        }

        private static CustomerFeatureFlag[] CreateCustomerFeatureFlagsEnumArray()
        {
            return (CustomerFeatureFlag[])Enum.GetValues(typeof(CustomerFeatureFlag));
        }

        private static Dictionary<CustomerFeatureFlag, int> CreateCCToMTPilotFlagMapping()
        {
            Dictionary<CustomerFeatureFlag, int> mapping = new Dictionary<CustomerFeatureFlag, int>(customerFeatureFlags.Length);
            foreach (CustomerFeatureFlag flag in customerFeatureFlags)
            {
                mapping[flag] = Array.IndexOf(customerFeatureFlags, flag);
            }
            return mapping;
        }

        public static int CacheRefreshIntervalInMs { get; private set; }

        public static Pilot Instance
        {
            get { return instance; }
        }

        /// <summary>
        /// Gets or sets CheckForCachedPilotFlags. A delegate that checks for pre-cached pilot flags. e.g. BulkApi doesn't have cc token
        /// but prefetches flags on task submit time
        /// </summary>
        public Func<int, BitArray> GetCachedPilotFlagsByCustomerId { get; set; }

        public void Initialize(int cacheRefreshIntervalInMs)
        {
            CacheRefreshIntervalInMs = cacheRefreshIntervalInMs;

            //remove after piloting done
            this.customerFeatures.Clear();
            this.bypassPilotChecksUsersCache.Clear();

            if (this.refreshThread == null)
            {
                this.refreshThread = new Thread(RefreshData)
                {
                    IsBackground = true
                };
                this.refreshThread.Start();
            }

            customerFeaturesCacheHits = 0;
            customerFeaturesCacheMisses = 0;
            timeSinceLastCustomerFeaturesCacheLog = DateTime.UtcNow.Ticks;
            customerFeaturesCacheLogCounter = 0;
        }

        public bool IsEnabledForContent(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.Content);
        }

        public bool IsEnabledForOSDeviceTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.OSTargetting);
        }

        public bool IsEnabledForLocalAttrbites(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.LocalAttributes);
        }

        public bool IsEnabledForEnhancedConversions(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnhancedConversions && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.EnhancedConversions);
        }

        public bool IsEnabledForClickToCallOnly(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ClickToCallOnly);
        }

        public bool IsEnabledForPremiumBanner(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.PremiumBanner);
        }

        public bool IsEnabledForPremiumFlash(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.PremiumFlash);
        }

        public bool IsEnabledForTPCAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.TPCAd);
        }

        public bool IsEnabledForRichSearchAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.RichSearchAd);
        }

        public bool IsEnabledForCPDAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CPDAd);
        }

        public bool IsEnabledForRichSearchAd4DeepLinks(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.RichSearchAd4DeepLinks);
        }

        public bool IsEnabledForSiteLinkAdExtensionsFeature(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.SiteLinksAdExtensions);
        }

        public bool IsEnabledForLocationAdExtensionsFeature(ILogShared logger, CampaignManagementRequest request, bool enableIfUserAdmin = true)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.LocationAdExtensions, enableIfUserAdmin);
        }

        public bool IsEnabledForCallAdExtensionsOrCallTrackingFeature(ILogShared logger, CampaignManagementRequest request, bool enableIfUserAdmin = true)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CallAdExtensionsOrCallTracking, enableIfUserAdmin);
        }

        public bool IsEnabledForAppAdExtensionsFeature(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AppAdExtensions);
        }

        public bool IsEnabledForAdRotation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AdRotation);
        }

        public bool IsEnabledForLocationExclusion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.LocationExclusion);
        }

        public bool IsEnabledForSearchTerms(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.SearchTerms);
        }

        public bool IsEnabledForCallTracking(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CallTracking);
        }

        //public bool IsEnabledForPLA(ILogShared logger, CampaignManagementRequest request)
        //{
        //    return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ProductAdExtensions);
        //}

        public bool IsEnabledForKeywordVariantMatchType(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.KeywordVariantMatchType);
        }

        public bool IsEnabledForDayTimePostalCodeTarget(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.DayTimePostalCodeTarget, false);
        }

        public bool IsEnabledForImageAdExtensions(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ImageAdExtensions);
        }

        public bool IsEnabledForIncreasedKeywordLimit(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.IncreasedKeywordLimit, false);
        }

        public bool IsEnabledForRadiusTargetingImprovements(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.RadiusTargetingImprovements, false);
        }

        public bool IsEnabledForAggregatorPerfImprovement(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AggregatorPerfImprovement);
        }

        public bool IsEnabledForCampaignImageAdExtensions(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CampaignImageAdExtensions);
        }

        public bool IsEnabledForGeoLocations10KLimit(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.GeoLocations10KLimit);
        }

        public bool IsEnabledForDeviceTargetingConsolidationPhase2(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.DeviceTargetingConsolidationPhase2);
        }

        public bool IsEnabledForDeviceTargetingConsolidationOptOut(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.DeviceTargetingConsolidationOptOut, enableIfUserAdmin: false);
        }

        public bool IsEnabledForExpandedDeviceTargeting(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ExpandedDeviceTargeting, enableIfUserAdmin: false);
        }

        public bool IsCustomerEnabledForCampaignAnalyticsMigration(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CampaignAnalyticsMigration);
        }

        public bool IsEnabledForKeywordTextEdit(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.EditableKeywordText, enableIfUserAdmin: false);
        }

        public bool IsEnabledForEnhancedShoppingCampaigns(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.EnhancedShoppingCampaigns)
                || this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.CNMarketExpansion);
        }

        public bool IsEnableForNativeAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.EnableNativeAds, enableIfUserAdmin: false);
        }

        public bool IsEnabledForNativeAdsImageExtension(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ImageExtensionForNativeAds, enableIfUserAdmin: false);
        }

        public bool IsEnabledForLodgingRename(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.LodgingRename, enableIfUserAdmin: false);
        }

        public bool IsEnabledForAdScheduleTimeZoneSetting(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AdScheduleTimeZoneSetting, enableIfUserAdmin: false);
        }
        public bool IsEnabledAutoConversion(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AutoConversion);
        }

        public bool IsEnabledForUpgradedUrls(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.UpgradedUrls);
        }

        public bool IsEnabledForUpgradedUrlsForProductPartitons(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.UpgradedUrlsForEnhancedShoppingCampaigns);
        }

        public bool IsEnabledForKwdDestUrlUpdateDisabledExclusion(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.KwdDestUrlUpdateDisabledExclusion, false);
        }

        public bool IsEnabledForProductTargetBlock(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.BlockProductTargets, false);
        }

        public bool IsEnabledForDynamicSearchAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAds);
        }

        public bool IsEnabledForAIM(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AIM)
                && !this.IsEnabledForAdultAds(logger, request);
        }

        public bool IsEnabledForEnhancedResponsiveAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.EnhancedResponsiveAd);
        }

        public bool IsEnabledForResponsiveSearchAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ResponsiveSearchAd);
        }

        public bool IsEnabledForVanityUrls(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.VanityUrl);
        }

        public bool IsEnabledForVanityPharmaWebsiteDescription(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsEnabledForVanityUrls(logger, request) && this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.VanityPharmaWebsiteDescription);
        }

        public bool IsEnabledForCampaignLanguages(ILogShared logger, CampaignManagementRequest request)
        {
            return true;
        }

        public bool IsEnabledForHotelAdsV1(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.HotelAdsV1);
        }

        public bool IsCustomerEnabledForHotelPropertyPromotionAd(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.IsCustomerEnabledForHotelPropertyPromotionAd);
        }

        public bool IsEnabledForLocalInventoryAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.LocalInventoryAdsEnabled);
        }

        public bool IsEnabledForCoOpCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CoOpCampaign);
        }

        public bool IsEnabledForInHousePromotion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.InHousePromotion);
        }

        public bool IsEnabledForSTA(ILogShared logger, CampaignManagementRequest request)
        {
            // TODO: brche - After I do SI testing, commented code will be checked in to prod
            //return !this.IsFeatureEnabled(logger, request, DisableSTA) || this.IsFeatureEnabled(logger, request, WhitelistSTA);

            return !(DynamicConfigValues.ShouldReturnTrueForDisableSTAPilot
                || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DisableSTA))
                || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.WhitelistSTA);
        }

        public bool IsEnabledForEXTA(ILogShared logger, CampaignManagementRequest request)
        {
            // when ShouldReturnTrueForDisableEXTAPilot is true, IsEnabledForEXTA is true only if customer is in WhitelistEXTA
            // when ShouldReturnTrueForDisableEXTAPilot is false, IsEnabledForEXTA is true if either customer is not in DisableEXTA or customer is in WhitelistEXTA
            return !(DynamicConfigValues.ShouldReturnTrueForDisableEXTAPilot || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DisableEXTA))
                || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.WhitelistEXTA);
        }

        public bool IsEnabledForEXTAAdCustomizerFeedUpdate(ILogShared logger, CampaignManagementRequest request)
        {
            return IsEnabledForEXTA(logger, request) || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.WhitelistEXTAAdCustomizerFeedUpdate);
        }

        public bool IsEnabledFor256CharAdGroupName(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AdGroupNameCharLimit);
        }

        public bool IsEnabledForAdultAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AdultAds);
        }

        public bool IsEnabledForFeedService(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsEnabledForDynamicSearchAds(logger, request)
                || this.IsEnabledForAdCustomizer(logger, request)
                || this.IsEnabledForDynamicDataFeed(logger, request);
        }

        public bool IsAutoBiddingTargetRoasEnabledNewPilot(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AutoBiddingTargetRoasEnabledNewPilot);
        }

        public bool IsGoogleImportApiEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.GoogleImportApiEnabled);
        }

        public bool IsFileImportApiEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.FileImportApiEnabled);
        }

        public bool IsAutoBiddingMaxConversionValueEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AutoBiddingMaxConversionValueEnabled);
        }

        public bool IsAutoBiddingTargetImpressionShareEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AutoBiddingTargetImpressionShareEnabled);
        }

        public bool IsAutoBiddingCustomerInDropConversionCountAndRevenueChecksPilot(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AutoBiddingDropConversionCountAndRevenueCheckPilot);
        }

        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationEUPhase1(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationEUPhase1);
        }

        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationEUPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationEUPhase2);
        }

        public bool IsCustomerEnabledForDSAInDanishFinnishNorwegian(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DSADanishFinnishNorwegianSupport);
        }
        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationJapan(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationJapan);
        }

        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationPhase6Mena(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationMENA);
        }

        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationSimplifiedChinese(ILogShared logger, CampaignManagementRequest request)
        {
            // DSA Simplified Chinese for HK, Pilot ID 1134
            var isFeatureEnabled = this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationSimplifiedChinese);

            // DSA Simplified Chinese for Mainland, PilotID 1183
            isFeatureEnabled |= IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CNMarketExpansion);

            return isFeatureEnabled;
        }
        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationPhase9(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationPhase9);
        }
        public bool IsCustomerEnabledForDynamicSearchAdsGlobalizationPhase9VI(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationPhase9VI);
        }

        public bool IsCustomerEnabledForShoppingAdsGlobalizationPAKorea(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ShoppingAdsGlobalizationPAKorea);
        }

        public bool IsCustomerEnabledM365IntegrationSupport(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.M365IntegrationSupport);
        }

        public bool IsCustomerEnabledAdClickParallelTracking(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AdClickParallelTracking);
        }

        public bool IsCustomerEnabledChannelPartnerOnboarding(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.UnifiedProductCampaignAPI);
        }

        public bool IsCustomerEnabledChannelPartnerExpertCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ChannelPartnerExpertCampaignSupport);
        }

        public bool isEnabledForParallelTrackingExceptionList(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ParallelTrackingExceptionList);
        }

        public bool IsEnabledForAdCustomizer(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AdCustomizer);
        }

        public bool IsEnabledForExperiment(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.Experiment);
        }

        public bool IsLinkedInTargetingEnabledforSearchDsaBsc(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.LinkedInTargetingforSearchDsaBsc);
        }

        public bool IsEnabledForLocalInventoryAdsV2(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.LocalInventoryAdsV2Enabled);
        }

        public bool IsEnabledForAIMCampaignLevelAudienceTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AIMCampaignLevelAudienceTargeting);
        }

        public bool IsEnabledForSmartCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SmartCampaign);
        }

        public bool IsEnabledForSmartShoppingCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SmartShoppingCampaign);
        }

        public bool IsEnabledForArabicLanguage(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return !DynamicConfigValues.EnablePilotCheckForInvalidLanguages
                    || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ArabicLanguage)
                        || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationMENA);
        }

        public bool IsEnabledForSimplifiedChineseLanguage(ILogAdvertiserCampaign logger, CampaignManagementRequest request)
        {
            return !DynamicConfigValues.EnablePilotCheckForInvalidLanguages
                        || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SimplifiedChineseLanguage)
                            || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicSearchAdsGlobalizationSimplifiedChinese)
                                || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CNMarketExpansion);
        }

        public bool IsEnabledForSOVTopISColumns(ILogShared logger, CampaignManagementRequest request)
        {
            return (request != null) ? this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SOVTopISColumns) : false;
        }

        public bool IsEnabledForSiteLinkAdExtensionDestUrlDeprecation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.SiteLinkAdExtensionDestUrlUpdateDisabledInclusion) &&
                !this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.SiteLinkAdExtensionDestUrlUpdateDisabledExclusion);
        }

        public bool IsEnabledForIFFunctionForEXTA(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.IFFunctionForEXTA);
        }

        public bool IsEnabledForScheduledFeedUpload(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ScheduledFeedUpload);
        }

        public bool IsEnabledForExpandedDynamicSearchAdPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ExpandedDsaPhase2);
        }

        public bool IsEnabledForDynamicDataFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataFeed);
        }

        public bool IsEnabledForDynamicDataMortgageLendersFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataMortgageLendersFeed);
        }

        public bool IsEnabledForDynamicDataAutosAggregateFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataAutosAggregateFeed);
        }

        public bool IsEnabledForDynamicDataAutosListingFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataAutosListingFeed);
        }

        public bool IsEnabledForDynamicDataCreditCardsFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataCreditCardsFeed);
        }

        public bool IsEnabledForDynamicDataDebitCardsFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataDebitCardsFeed);
        }

        public bool IsEnabledForDynamicDataJobListingsFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataJobListingsFeed);
        }

        public bool IsEnabledForDynamicDataCruisesFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataCruisesFeed);
        }

        public bool IsEnabledForDynamicDataCustomFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataCustomFeed);
        }

        public bool IsEnabledForDynamicDataEventsFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataEventsFeed);
        }

        public bool IsEnabledForDynamicDataHealthInsuranceFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataHealthInsuranceFeed);
        }

        public bool IsEnabledForDynamicDataHotelsAndVacationRentalsFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataHotelsAndVacationRentalsFeed);
        }

        public bool IsEnabledForDynamicDataProfessionalServiceFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataProfessionalServiceFeed);
        }

        public bool IsEnabledForDynamicDataToursAndActivitiesFeed(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DynamicDataToursAndActivitiesFeed);
        }

        public bool IsEnabledForClarityIngetration(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableClarityTagIntegration && this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.UETTagClarityIntegration);
        }

        public bool IsEnabledForCookieBasedExperiments(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CookieBasedExperiments);
        }
        public bool InValidateCustomerCache(ILogShared logger, CampaignManagementRequest request)
        {
            int customerId = EOHelper.GetAdvertiserCustomerId2(logger, request);
            return this.customerFeatures.Remove(customerId);
        }

        public bool IsEnabledForSponsoredProductAdsV2(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableSponsoredProductAdsV2(request) && this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SponsoredProductAdsV2);
        }

        public bool IsEnabledForManagerAccountSharedWebsiteExclusions(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ManagerAccountSharedWebsiteExclusions);
        }

        public bool IsUrlTrimmingEnabledForWebsiteExclusionLists()
        {
            return DynamicConfigValues.IsUrlTrimmingEnabledForWebsiteExclusionLists;
        }

        public bool IsEnabledForSmartPage(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SmartPage);
        }

        public bool IsCustomerEnabledForManualBiddingDeprecation(ILogShared logger, CampaignManagementRequest request)
        {
            // A few FTs need to create Manual Bidding campaigns for testing purpose. So we need to use the config from request.
            return DynamicConfigValues.EnableManualBiddingDeprecation(request) &&
                   // Manual Bidding has been GAed. However, we still have some customer in OptOut pilot.
                   !this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.ManualBiddingDeprecationOptOut);
        }

        public bool IsEnabledForScheduledOfflineConversionUpload(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.OfflineConversionRestateRetract);
        }

        public bool IsEnabledForOfflineConversionRestateRetract(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.SupportOfflineConversionRestateRetract && this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.OfflineConversionRestateRetract);
        }

        public bool IsEnabledOnlineConversion(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableOnlineConversion && this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.OnlineConversion);
        }

        public bool IsEnabledForAccountLabels(CustomerCallContext context)
        {
            return this.IsFeatureEnabled(context, (int)CustomerFeatureFlag.AccountLabels);
        }

        public bool IsEnabledForBSCImportMappingImprovement(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.BSCImportMappingImprovement);
        }

        public bool IsCustomerEnabledForNeighborhoodLocationTargeting(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.NeighborhoodLocationTargeting);
        }

        public bool IsEnabledForSubscriptionAds(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SubscriptionAds);
        }

        public bool IsCustomerEnabledForAutoBiddingLimitedStatus(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.EnableAutobiddingLimitedStatus);
        }

        public bool IsEnabledForSponsoredPromotionsForBrands(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.SponsoredPromotionsForBrands);
        }

        public bool IsCustomerEnabledESCAudienceAdsSupport(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ESCAudienceAdsSupportV2);
        }

        public bool IsEnabledForLeadFormAdExtensions(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.LeadFormAdExtension);
        }

        public bool IsCustomerEnabledForFeedLabel(ILogShared logger, CampaignManagementRequest request)
        {
            return ( this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ShoppingFeedLabel) 
                    && !(this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ShoppingFeedLabelDisable))); 
        }
        public bool IsCustomerEnabledForAimandNativePAUnification(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ShoppingAimandNativePAUnification);
        }

        public bool IsCustomerEnabledForMultiChannelCampaign(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.MultiChannelCampaign);
        }

        public bool IsCustomerEnabledForAllowYahooExclusionInSWF(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.YahooExclusionInSWF);
        }

        public bool IsCustomerEnabledForAllowDatamartClickhouseMigration(ILogShared logger, CampaignManagementRequest request)
        {
            if (DynamicConfigValues.EnableClickhouseQueriesByPassPilot || this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DatamartClickhouseMigrationPhase2))
            {
                // If customer is enabled for phase 2,  phase 1 set as false by default
                return false;
            }

            if(ClickhouseQueryBuilderConfiguration.DatamartClickhouseMigrationCustomerList != null)
            {
                if(ClickhouseQueryBuilderConfiguration.DatamartClickhouseMigrationCustomerList.Contains(EOHelper.GetAdvertiserCustomerId2(logger, request).ToString()))
                {
                       return true;
                }
            }

            //only refresh pilot cache for ci
            if (ClickhouseQueryBuilderConfiguration.ClickhouseEnv.Equals("CI") && ClickhouseQueryBuilderConfiguration.EnabledClickhousePilotDbTypes != null && ClickhouseQueryBuilderConfiguration.EnabledClickhousePilotDbTypes.Count > 0)
            {
                // Invalidate customer pilot feature cache to force sync with CC, will check if si need later
                if (this.InValidateCustomerCache(logger, request))
                {
                    logger.LogWarning(
                        $"[{nameof(IsCustomerEnabledForAllowDatamartClickhouseMigrationPhase2)}]: Failed to invalidate cache.");
                }
            }

            //test in ci/si first
            if((ClickhouseQueryBuilderConfiguration.EnableClickhousePhase1ForAccoutPercent > 0 || ClickhouseQueryBuilderConfiguration.EnableClickhousePhase1ForAdgroupPercent > 0))
            {
                return true;
            }

            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DatamartClickhouseMigration);
        }

        public bool IsCustomerEnabledForAllowDatamartClickhouseMigrationPhase2(ILogShared logger, CampaignManagementRequest request)
        {
            if(DynamicConfigValues.EnableClickhouseQueriesByPassPilot)
            {
                return true;
            }
            if (ClickhouseQueryBuilderConfiguration.DatamartClickhouseMigrationPhase2TestCustomerList != null)
            {
                if (ClickhouseQueryBuilderConfiguration.DatamartClickhouseMigrationPhase2TestCustomerList.Contains(EOHelper.GetAdvertiserCustomerId2(logger, request).ToString()))
                {
                    return true;
                }
            }

            //only refresh pilot cache for ci
            if (ClickhouseQueryBuilderConfiguration.ClickhouseEnv.Equals("CI") && ClickhouseQueryBuilderConfiguration.EnabledClickhousePilotDbTypes != null && ClickhouseQueryBuilderConfiguration.EnabledClickhousePilotDbTypes.Count > 0)
            {
                // Invalidate customer pilot feature cache to force sync with CC, will check if si need later
                if (this.InValidateCustomerCache(logger, request))
                {
                    logger.LogWarning(
                        $"[{nameof(IsCustomerEnabledForAllowDatamartClickhouseMigrationPhase2)}]: Failed to invalidate cache.");
                }
            }

            return ( this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.DatamartClickhouseMigrationPhase2) 
                        || DynamicConfigValues.EnableClickhouseQueriesByPassPilot
                   );
        }

        public bool IsCustomerEnabledForCNMarketExpansion(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CNMarketExpansion);
        }

        public bool IsCustomerEnabledForBingPlacesMSASignup(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.BingPlacesMSASignup
                   && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.BingPlacesMSASignup);
        }

        public bool IsEnabledForAutobiddingConsolidationFillGaps(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AutobiddingConsolidationFillGaps);
        }

        public bool IsEnabledForAutobiddingConsolidation(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.AutobiddingConsolidation);
        }

        public bool IsGoalBulkEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableGoalBulk
                   && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.EnableGoalBulk);
        }

        public bool IsGoalImportEnabledAccountLevel(ILogShared logger, CampaignManagementRequest request, long accountId)
        {
            return DynamicConfigValues.EnableConversionActionImport &&
                (DynamicConfigValues.IsTestAccountgForGoalImport(accountId) || PilotAccount.Instance.IsAccountEnabledForGoalImport(logger, request));
        }

        public bool IsMobileAppCampaignConversionGoalEnabledForAccount(ILogShared logger, CampaignManagementRequest request)
        {
            return PilotAccount.Instance.IsMobileAppCampaignConversionGoalEnabledForAccount(logger, request);
        }

        public bool IsPmaxNewCustomerAcquisitionEnabledForAccount(ILogShared logger, CampaignManagementRequest request)
        {
            return PilotAccount.Instance.IsAccountEnabledForPmaxNewCustomerAcquisition(logger, request);
        }

        public bool IsLinkedInTargetBulkUploadEnabledForAccount(ILogShared logger, CampaignManagementRequest request)
        {
            return PilotAccount.Instance.IsAccountLinkedInTargetBulkUploadEnabled(logger, request);
        }

        public bool IsCustomerMatchListMSIDEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableCustomerMatchListMSID && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.Boost) &&
                this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.BoostConversionBasedSegment);
        }

        public bool IsAccountPlacementExclusionListEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAccountPlacementExclusionLists
                   && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AccountPlacementExclusionList);
        }
        public bool IsAccountPlacementInclusionListEnabled(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableAccountPlacementInclusionLists
                   && this.IsFeatureEnabled(logger, request, CustomerFeatureFlag.AccountPlacementInclusionList);
        }
        public bool IsAccountEnabledForVerticalVideoSupport(ILogShared logger, CampaignManagementRequest request)
        {
            return PilotAccount.Instance.IsAccountEnabledForVerticalVideoSupport(logger, request);
        }

        public bool IsAccountEnabledForBrandKitBulk(ILogShared logger, CampaignManagementRequest request)
        {
            return PilotAccount.Instance.IsAccountEnabledForBrandKit(logger, request) && 
                PilotAccount.Instance.IsAccountEnabledForBrandKitPhase2(logger, request) &&
                PilotAccount.Instance.IsAccountEnabledForBrandKitBulk(logger, request);
        }
        public bool IsAccountEnabledForConversionDelay(ILogShared logger, CampaignManagementRequest request)
        {
            return DynamicConfigValues.EnableConversionDelayMetrics && 
                PilotAccount.Instance.IsAccountEnabledForConversionDelayMetrics(logger, request);
        }

        /// <summary>
        /// Get raw information about pilot flags from CC
        /// </summary>
        public BitArray GetCustomerFeaturesBitArray(ILogShared logger, int customerId, CampaignManagementRequest request)
        {
            return PilotCC.GetCustomerFeature(logger, customerId, request.SecurityTicket.SecurityTicketId, request.Tracking.TrackingId.ToString());
        }

        public bool OnGetLockTimeout(ILogShared logger, int customerId, CampaignManagementRequest request, out CustomerFeatures features)
        {
            features = this.GetCustomerFeature(logger, customerId, request);
            return true;
        }

        //For PilotCC mapping
        public bool OnGetLockTimeout(ILogShared logger, int customerId, CampaignManagementRequest request, out BitArray features)
        {
            features = this.GetCustomerFeaturesBitArray(logger, customerId, request);
            return true;
        }

        public bool IsFeatureEnabled(CustomerCallContext context, uint featurePilotId)
        {
            return IsFeatureEnabled(context.Logger, context.Request, context.AdvertiserCustomerId, featurePilotId);
        }

        public bool IsFeatureEnabled(ILogShared logger, CampaignManagementRequest request, uint featurePilotId)
        {
            int customerId = EOHelper.GetAdvertiserCustomerId2(logger, request);
            return IsFeatureEnabled(logger, request, customerId, featurePilotId);
        }

        private bool IsFeatureEnabled(ILogShared logger, CampaignManagementRequest request, int customerId, uint featurePilotId)
        {
            if (ccToMTPilotFlagMapping.ContainsKey((CustomerFeatureFlag)featurePilotId))
            {
                return CheckFeature(logger, request, customerId, (CustomerFeatureFlag)featurePilotId);
            }

            throw new InvalidOperationException($"Pilot feature {featurePilotId} not found in middle tier feature flags");
        }

        public bool IsFeatureEnabled(
            ILogShared logger,
            CampaignManagementRequest request,
            CustomerFeatureFlag requestedFeature,
            bool enableIfUserAdmin = true)
        {
            // NOTE: should not consider admin user enabled, if checking to deprecate some feature (if new one is enabled)
            if (enableIfUserAdmin && this.IsUserAlwaysEnabledForPilot(logger, request))
            {
                if (DynamicConfigValues.EnableAdminPilotFlagLogging)
                {
                    LogAdminFlagUsage(logger, request, requestedFeature);
                }
                return true;
            }

            int customerId = EOHelper.GetAdvertiserCustomerId2(logger, request);

            return CheckFeature(logger, request, customerId, requestedFeature);
        }

        public bool IsEnabledConversionValueRule(ILogShared logger, CampaignManagementRequest request)
        {
            if(logger == null || request == null)
            {
                return false;
            }
            else
            {
                return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.ConversionValueRule);
            }
        }


        private bool CheckFeature(ILogShared logger, CampaignManagementRequest request, int customerId, CustomerFeatureFlag requestedFeature)
        {
            CustomerFeatures existingFeatures;
            if (!this.customerFeatures.Get(logger, this, customerId, request, out existingFeatures))
            {
                Interlocked.Increment(ref customerFeaturesCacheMisses);
                existingFeatures = this.GetCustomerFeature(logger, customerId, request);
                this.customerFeatures.Add(customerId, existingFeatures);
            }
            else
            {
                Interlocked.Increment(ref customerFeaturesCacheHits);
            }

            if (DateTime.UtcNow.Ticks - timeSinceLastCustomerFeaturesCacheLog > customerFeaturesCacheLogSpan)
            {
                Interlocked.Exchange(ref timeSinceLastCustomerFeaturesCacheLog, DateTime.UtcNow.Ticks);
                Interlocked.Increment(ref customerFeaturesCacheLogCounter);
                LogCustomerFeaturesCache(logger);
            }
            return existingFeatures.Flags[ccToMTPilotFlagMapping[requestedFeature]];
        }

        private void LogCustomerFeaturesCache(ILogShared logger)
        {
            logger.LogInfo($"Customer feature cache misses: {customerFeaturesCacheMisses}. Hits: {customerFeaturesCacheHits}");
        }

        private static void RefreshData()
        {
            InternalCallTrackingData callTracking = new InternalCallTrackingData();
            ILogShared logger = new AdvertiserLogger(callTracking, null, null, "PilotCacheRefreshData");

            bool keepRunning = true;
            while (keepRunning)
            {
                try
                {
                    Thread.Sleep(CacheRefreshIntervalInMs);

                    //remove after piloting done
                    SafeSharedLog.LogVerbose(logger, "Customer feature cache has {0} entries. Removing all of them.", instance.customerFeatures.Count);
                    instance.customerFeatures.Clear();

                    SafeSharedLog.LogVerbose(logger, "Always enabled user cache has {0} entries. Removing all of them.", instance.bypassPilotChecksUsersCache.Count);
                    instance.bypassPilotChecksUsersCache.Clear();
                }
                catch (ThreadAbortException)
                {
                    keepRunning = false;
                }
                catch (Exception e)
                {
                    SafeSharedLog.LogException(logger, e);
                }
            }
        }

        private CustomerFeatures GetCustomerFeature(ILogShared logger, int customerId, CampaignManagementRequest request)
        {
            BitArray pilotCCMappedFeatures = this.GetCustomerFeaturesBitArray(logger, customerId, request);
            return this.GetCustomerFeature(pilotCCMappedFeatures);
        }

        private CustomerFeatures GetCustomerFeature(BitArray pilotCCMappedFeatures)
        {
            CustomerFeatures customerFeatureFlagMappedFeatures = new CustomerFeatures();
            foreach (CustomerFeatureFlag flag in customerFeatureFlags)
            {
                customerFeatureFlagMappedFeatures.Flags[ccToMTPilotFlagMapping[flag]] = this.GetFeature(pilotCCMappedFeatures, flag);
            }
            //also need to check CallTrackingId with CallAdExtensionsOrCallTracking flag
            SpecialFeatureSetCallAdExtensionsOrCallTracking(pilotCCMappedFeatures, customerFeatureFlagMappedFeatures);
            return customerFeatureFlagMappedFeatures;
        }

        private bool GetFeature(BitArray features, CustomerFeatureFlag feature)
        {
            int featureId = (int)feature;
            return features.Length > featureId && features[featureId];
        }

        private void SpecialFeatureSetCallAdExtensionsOrCallTracking(BitArray pilotMappedFeatures, CustomerFeatures customerFeatureFlagMappedFeatures)
        {
            if (this.GetFeature(pilotMappedFeatures, CustomerFeatureFlag.CallTracking))
            {
                customerFeatureFlagMappedFeatures.AddFlags(CustomerFeatureFlag.CallAdExtensionsOrCallTracking);
            }
        }

        private bool IsUserAlwaysEnabledForPilot(ILogShared logger, CampaignManagementRequest request)
        {
            if (request.SecurityTicket != null && !string.IsNullOrEmpty(request.SecurityTicket.SecurityTicketId))
            {
                bool isAlwaysEnabled;

                if (!this.bypassPilotChecksUsersCache.Get(logger, null, request.UserId, out isAlwaysEnabled))
                {
                    isAlwaysEnabled = MTAuthorizationManager.Instance.IsAuthorizedWithRetry(
                        logger,
                        request.SecurityTicket.SecurityTicketId,
                        request.UserId,
                        AuthorizationActions.BypassPilotChecks,
                        false,
                        request.AuthorizationEntity);

                    this.bypassPilotChecksUsersCache.Add(request.UserId, isAlwaysEnabled);
                }

                return isAlwaysEnabled;
            }

            return false;
        }

        private void LogAdminFlagUsage(
            ILogShared logger,
            CampaignManagementRequest request,
            CustomerFeatureFlag requestedFeature)
        {
            int userId = request.UserId;
            int customerId = EOHelper.GetAdvertiserCustomerId2(logger, request);
            bool expectedValue = CheckFeature(logger, request, customerId, requestedFeature);

            //we should log each unique combination of userId, feature requested, and whether the actual pilot flag is on or off
            int hashValue = ComputeAdminFlagHash(userId, requestedFeature, expectedValue);

            //returns true if the pair was added and therefore didn't exist before, so we log if so
            if (adminPilotFlags.TryAdd(hashValue, 0))
            {
                logger.LogInfo($"User ID {userId} is an admin account, so flag {requestedFeature} automatically returned true. Its actual value for the customer used should be {expectedValue}.");
            }
        }

        public bool IsCustomerMigratedFromBulkDB(ILogShared logger, CampaignManagementRequest request)
        {            
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.CustomerMigratedFromBulkDB);
        }

        public bool IsCustomerEnabledForPmaxSovMetrics(ILogShared logger, CampaignManagementRequest request)
        {
            return this.IsFeatureEnabled(logger, request, (int)CustomerFeatureFlag.PmaxSovMetrics);
        }

        private int ComputeAdminFlagHash(int userId, CustomerFeatureFlag flag, bool expectedValue)
        {
            return userId.GetHashCode() ^ flag.GetHashCode() ^ expectedValue.GetHashCode();
        }
    }
}

