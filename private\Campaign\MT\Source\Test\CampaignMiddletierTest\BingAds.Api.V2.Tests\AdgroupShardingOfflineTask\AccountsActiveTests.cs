﻿namespace Microsoft.Advertising.Advertiser.Api.V2.AdgroupShardingOfflineTask
{
    using System;
    using System.Data;
    using System.Linq;
    using CampaignMiddleTierTest.Framework;
    using ClientCenter.MT.Service;
    using VisualStudio.TestTools.UnitTesting;
    using AdCenter.Shared.MT;
    using MT.Database;
    using ApiResponseValidator = CampaignTest.ApiFunctionalTests.Validators.ResponseValidator;

    [TestClass]
    public sealed class AccountsActiveTests : TaskTestBase
    {
        private const byte AccountsActiveOpType = 156;

        [TestMethod]
        [Ignore]
        [Priority(0)]
        [Owner(TestOwners.AdgroupShardingOfflineTask)]
        public void AccountsActiveTest()
        {
            CustomerInfo customer = CreateCustomers(CustomerFactory.ServiceLevel.SelfServe);
            var accountCollection = new TestAccountCollection( AccountType.Advertiser,
                AdvertiserAccountFactory.LifecycleStatus.Active,
                AdvertiserAccountFactory.CountryCode.US, AdvertiserAccountFactory.LanguageType.English, AdvertiserAccountFactory.CurrencyType.USDollar);
            accountCollection.Add(customer.CustomerId);
            int accountId = accountCollection.Account.Id;
            Console.WriteLine("Account id: " + accountId);

            #region Updating customer, account, campaign status so we don't generate incorrect data into campaignsActive

            // waiting for account to become pending and then update its' status
            AccountBase account = accountCollection.Fetch(accountId);
            Assert.AreEqual((byte)AccountLifecycleStatus.Pending, account.LifecycleStatus);

            // add campaigns
            TestCampaignCollection.Clean(customer);
            var campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customer));

            var adgroupCollection = new TestAdGroupCollection(campaignCollection.Campaigns[0], 10);

            // Update customer, account & campaign status to active
            var dbHelper = new DbHelper(customer.CustomerId, accountId);
            dbHelper.UpdateCampaignToActiveStatus(campaignCollection.Ids);

            // Confirm account's status
            account = accountCollection.Fetch(accountId);
            Assert.AreEqual((byte)AccountLifecycleStatus.Active, account.LifecycleStatus, "invalid account status:" + account.LifecycleStatus);

            #endregion

            dbHelper.InsertAccountsActiveTable(customer.CustomerId, accountId);

            TestRunner.RunTest(() =>
            {
                return new TaskDescription()
                {
                    OperationType = AccountsActiveOpType,
                    EntityIds = new[] { new Tuple<long?, long?, long?>(-1, null, null) },
                    IsTaskInShardDB = false,
                    AccountId = accountId,
                    CustomerId = customer.CustomerId
                };
            }, (dbHelper2, data) =>
            {
                // Add AccountsActive row if not exists.

                DataTable tableInMain = dbHelper.GetAccountsActiveTable(customer.CustomerId, DatabaseTypes.CampaignDB, accountId)[0];
                DataTable[] tableInShards = dbHelper.GetAccountsActiveTable(customer.CustomerId, DatabaseTypes.CampaignAdGroupShard, accountId);

                // Compare on AdGroup shard
                foreach (DataTable tableInShard in tableInShards)
                {
                    for (int row = 0; row < tableInMain.Rows.Count; row++)
                    {
                        DataRow mainRow = tableInMain.Rows[row];
                        DataRow shardRow = tableInShard.Rows[row];
                        Assert.AreEqual((int)mainRow[0], (int)shardRow[0], "unmatched accountId");
                        Assert.AreEqual((long)mainRow[1], (long)shardRow[1], "unmatched lastUpdatedProcessId");
                    }
                }

            }, null, null);

            // Delete these campaigns and then CampaignStatus table in main should not include these campaigns
            TestRunner.RunTest(() =>
            {
                ApiResponseValidator.ValidateAPISuccess(accountCollection.Delete(account));
                // delete from AccountsActive table if it has not been updated
                dbHelper.DeleteAccountsActiveTable(customer.CustomerId, accountId);
                return new TaskDescription()
                {
                    OperationType = AccountsActiveOpType,
                    EntityIds = new[] { new Tuple<long?, long?, long?>(-1, null, null) },
                    IsTaskInShardDB = false,
                    AccountId = accountId,
                    CustomerId = customer.CustomerId
                };
            }, (dbHelper2, data) =>
            {
                DataTable[] tableInShards = dbHelper.GetAccountsActiveTable(DefaultCustomer.CustomerId, DatabaseTypes.CampaignAdGroupShard, accountId );
                foreach (DataTable tableInShard in tableInShards)
                {
                    Assert.AreEqual(0, tableInShard.Rows.Count, "unmatch rows");
                }

            }, null, null);
        }

    }
}
