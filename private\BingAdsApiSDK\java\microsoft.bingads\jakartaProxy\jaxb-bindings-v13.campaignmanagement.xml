﻿<?xml version="1.0" encoding="utf-8"?>
<bindings xmlns="https://jakarta.ee/xml/ns/jaxb" xmlns:xs="http://www.w3.org/2001/XMLSchema" version="3.0">
  <bindings node="//xs:simpleType[@name='AdSubType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdSubType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdSubTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdSubTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='HotelAdGroupType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;HotelAdGroupType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.HotelAdGroupTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.HotelAdGroupTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AppStore']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AppStore&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AppStoreConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AppStoreConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='CampaignType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;CampaignType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='CampaignAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;CampaignAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AdGroupAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdGroupAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdGroupAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdGroupAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AdAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='KeywordAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;KeywordAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.KeywordAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.KeywordAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AdExtensionsTypeFilter']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdExtensionsTypeFilter&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdExtensionsTypeFilterConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdExtensionsTypeFilterConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AdExtensionAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdExtensionAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdExtensionAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdExtensionAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='MediaEnabledEntityFilter']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;MediaEnabledEntityFilter&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.MediaEnabledEntityFilterConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.MediaEnabledEntityFilterConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='MediaAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;MediaAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.MediaAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.MediaAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AdGroupCriterionType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AdGroupCriterionType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AdGroupCriterionTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AdGroupCriterionTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='CriterionAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;CriterionAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.CriterionAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.CriterionAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AudienceType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AudienceType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='ProfileType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;ProfileType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.ProfileTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.ProfileTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AssetGroupListingGroupAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AssetGroupListingGroupAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AssetGroupListingGroupAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AssetGroupListingGroupAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='BMCStoreAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;BMCStoreAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.BMCStoreAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.BMCStoreAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='CampaignCriterionType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;CampaignCriterionType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignCriterionTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.CampaignCriterionTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='PortfolioBidStrategyAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;PortfolioBidStrategyAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.PortfolioBidStrategyAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.PortfolioBidStrategyAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AudienceGroupAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AudienceGroupAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceGroupAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceGroupAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AssetGroupAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AssetGroupAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AssetGroupAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AssetGroupAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='ProductAudienceType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;ProductAudienceType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.ProductAudienceTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.ProductAudienceTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='AudienceAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;AudienceAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.AudienceAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='ConversionGoalType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;ConversionGoalType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.ConversionGoalTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.ConversionGoalTypeConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='ConversionGoalAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;ConversionGoalAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.ConversionGoalAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.ConversionGoalAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='ImportAdditionalField']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;ImportAdditionalField&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.ImportAdditionalFieldConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.ImportAdditionalFieldConverter.convertToString" />
  </bindings>
  <bindings node="//xs:simpleType[@name='DeviceType']" schemaLocation="https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc?xsd=xsd1">
    <javaType name="java.util.Collection&lt;DeviceType&gt;" parseMethod="com.microsoft.bingads.v13.campaignmanagement.DeviceTypeConverter.convertToList" printMethod="com.microsoft.bingads.v13.campaignmanagement.DeviceTypeConverter.convertToString" />
  </bindings>
</bindings>