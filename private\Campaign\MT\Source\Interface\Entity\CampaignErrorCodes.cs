﻿
namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities
{
    using DAO;

    public enum MigrationErrorCode : int
    {
        // The format below is chosen such that the higher bits convey severity to the MT clients
        // 0x4... - Error
        // 0x2... ?Warning
        // 0x1... ?Info
        // The 31st bit must stay 0.
        EditorialAdTitleTooLong = 0x40000001,
        EditorialAdDisplayUrlTooLong = 0x40000002,
        KeywordBroadBidBelowFloorPrice = 0x10000003,
        KeywordPhraseBidBelowFloorPrice = 0x10000004,
        KeywordExactBidBelowFloorPrice = 0x10000005,
        KeywordContentBidBelowFloorPrice = 0x10000006,
        AdGroupBidBelowFloorPrice = 0x10000007,
    }

    public enum CampaignManagementErrorCode
    {
        [DbErrorCodeMapping(-100100)] // Insert Failure
        [DbErrorCodeMapping(-100105)] // Update Failure
        [DbErrorCodeMapping(-100110)] // Delete Failure
        [DbErrorCodeMapping(-100125)] // Error on internal procedure call
        [DbErrorCodeMapping(-100185)] // Error performing operations on temporary table
        [DbErrorCodeMapping(-100235)] // Dynamic Query Execution Failure
        [DbErrorCodeMapping(-100699)] // fv currently unavailable
        [DbErrorCodeMapping(-101001)] // Can not insert records in the campaign table.
        [DbErrorCodeMapping(-102010)] // City/State/BusinessLocation/CustomLocation targets not available for BackCompat
        [DbErrorCodeMapping(-103605)] // ExclusionListItemDataTable could not be locked for Customer
        [DbErrorCodeMapping(-103617)] // SiteExclusionListItemDataTable could not be locked for Account
        InternalError = 1,

        DatabaseTimeout = 2,

        [DbErrorCodeMapping(-101455)]
        [DbErrorCodeMapping(-100135)]
        InvalidParameters = 3,

        AccountWordBreakerLocationIdIsNull = 4,

        [DbErrorCodeMapping(-600021)]
        [DbErrorCodeMapping(-400012)]
        [DbErrorCodeMapping(-500005)]
        [DbErrorCodeMapping(-102200)]
        [DbErrorCodeMapping(-102163)]
        [DbErrorCodeMapping(-450017)]
        [DbErrorCodeMapping(-450018)]
        AccountIdInvalid = 5,

        [DbErrorCodeMapping(-100739)] // CustomerID is not associated with the AccountID passed in as an input parameter
        [DbErrorCodeMapping(-211112)] // Usualy happens when CustomerAccountId and AccountId has different values e.g. AddCampaigns operations
        [DbErrorCodeMapping(-102014)] // thrown by prc_PublicAccountExclusionSet_v35
        CustomerAccountIdInvalid = 6,

        AccountIdMissingForAggregatorUser = 7,

        RequestSecurityTicketNull = 8,

        RequestSecurityTicketRolesNull = 9,

        RequestSecurityTicketUserInfoNull = 10,

        SystemInReadOnlyMode = 11,

        [DbErrorCodeMapping(-100165)]
        InvalidEntityState = 12,

        [DbErrorCodeMapping(-100151)]
        TimestampRequiredForEdit = 13,

        [DbErrorCodeMapping(-101448)]
        [DbErrorCodeMapping(-101453)]
        [DbErrorCodeMapping(-101454)]
        [DbErrorCodeMapping(-211111)]
        [DbErrorCodeMapping(-109100)]
        [DbErrorCodeMapping(-109101)]
        [DbErrorCodeMapping(-109107)]
        [DbErrorCodeMapping(-109102)]
        [DbErrorCodeMapping(-109103)]
        CustomerUnderMigration = 14,

        IdShouldBeNullOnAdd = 15,

        [DbErrorCodeMapping(-101451)]
        FullSyncRequired = 16,

        AdvertiserCustomerIdInvalid = 17,

        CampaignOperationIsDeprecated = 18,

        EntityTypeInvalid = 19,

        IdsNotPassed = 20,

        DuplicateId = 21,

        InvalidEnumValue = 22,

        DateTooOld = 23, // Date is earlier than SqlDateTime

        CustomerBatchLimitExceeded = 24,

        EntityIdsNotPassed = 25,

        EntityAlreadyExists = 26,

        DuplicateEntity = 27,

        EntityDoesNotExist = 28,

        MaxLimitReached = 29,

        EntityIdNotPassed = 30,

        MixedEntityTypesNotAllowed = 31,

        ParentCampaignIdInvalid = 32,

        ParentAdGroupIdInvalid = 33,

        OperationCancelled = 34,

        UrlNotAccessible = 35,

        [DbErrorCodeMapping(3906)]
        DatabaseReadOnly = 36,

        BlockedByCloudflare = 37,

        UrlSSLVerificationFailed = 38,

        UrlScanFailed = 39,

        #region Negative keywords errors

        NegativeKeywordTooLong = 100,

        NegativeKeywordsTotalLengthExceeded = 101,

        NegativeKeywordHasInvalidChars = 102,

        NegativeKeywordMatchesKeyword = 103,

        NegativeKeywordNotPassed = 104,

        [DbErrorCodeMapping(-100880)]
        NegativeKeywordsEntityLimitExceeded = 105,

        NegativeKeywordsLimitExceeded = 106,

        [DbErrorCodeMapping(-100759)]
        [DbErrorCodeMapping(-100754)]
        NegativeKeywordItemExceedsLimit = 107,

        NegativeKeywordHasInvalidMatchTypeFormat = 108,

        NegativeKeywordTooManyParentTypes = 109,

        NegativeKeywordDuplicateFound = 110,

        NegativeKeywordNotFound = 111,

        NegativeKeywordInvalidMatchType = 112,

        NegativeKeywordInvalidParentType = 113,

        NegativeKeywordEntitiesNotPassed = 114,

        NegativeKeywordEntityNull = 115,

        NegativeKeywordEntityTypesMismatch = 116,

        [DbErrorCodeMapping(-102406)]
        NegativeKeywordDuplicate = 117,

        NegativeKeywordsNotPassed = 118,

        StructuredNegativeKeywordPilotNotEnabledForCustomer = 119,

        NegativeKeywordPartialError = 120,
        #endregion

        #region Editorial errors

        EditorialAdExists = 200,

        /// <summary>
        /// TODO: remove this error in 7.3
        /// </summary>
        EditorialDefaultAdExists = 201,

        [ErrorField(EntityField.AdTitle)]
        EditorialSyntaxErrorInTitle = 202,

        [ErrorField(EntityField.AdText)]
        EditorialSyntaxErrorInText = 203,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialSyntaxErrorInDisplayUrl = 204,

        [ErrorField(EntityField.AdTitle)]
        EditorialForbiddenKeywordInTitle = 205,

        [ErrorField(EntityField.AdText)]
        EditorialForbiddenKeywordInText = 206,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialForbiddenKeywordInDisplayUrl = 207,

        [ErrorField(EntityField.KeywordText)]
        EditorialForbiddenKeywordInKeywordText = 208,

        [ErrorField(EntityField.KeywordParam1)]
        EditorialForbiddenKeywordInParam1 = 209,

        [ErrorField(EntityField.KeywordParam2)]
        EditorialForbiddenKeywordInParam2 = 210,

        [ErrorField(EntityField.KeywordParam3)]
        EditorialForbiddenKeywordInParam3 = 211,

        [ErrorField(EntityField.AdTitle)]
        EditorialIncorrectAdFormatInTitle = 212,

        [ErrorField(EntityField.AdText)]
        EditorialIncorrectAdFormatInText = 213,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialIncorrectAdFormatInDisplayUrl = 214,

        [ErrorField(EntityField.AdTitle)]
        EditorialTooMuchAdTextInTitle = 215,

        [ErrorField(EntityField.AdText)]
        EditorialTooMuchAdTextInText = 216,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialTooMuchAdTextInDisplayUrl = 217,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialTooMuchAdTextInDestinationUrl = 218,

        [ErrorField(EntityField.AdText)]
        EditorialNotEnoughAdText = 219,

        [ErrorField(EntityField.AdTitle)]
        EditorialExclusiveKeywordInTitle = 220,

        [ErrorField(EntityField.AdText)]
        EditorialExclusiveKeywordInText = 221,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialExclusiveKeywordInDisplayUrl = 222,

        [ErrorField(EntityField.KeywordText)]
        EditorialExclusiveKeywordInKeywordText = 223,

        [ErrorField(EntityField.KeywordParam1)]
        EditorialExclusiveKeywordInParam1 = 224,

        [ErrorField(EntityField.KeywordParam2)]
        EditorialExclusiveKeywordInParam2 = 225,

        [ErrorField(EntityField.KeywordParam3)]
        EditorialExclusiveKeywordInParam3 = 226,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialInvalidDisplayUrlFormat = 227,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialInvalidDestinationUrlFormat = 228,

        [ErrorField(EntityField.AdTitle)]
        EditorialDefaultAdSyntaxErrorInTitle = 229,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdSyntaxErrorInText = 230,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdSyntaxErrorInDisplayUrl = 231,

        [ErrorField(EntityField.AdTitle)]
        EditorialDefaultAdForbiddenKeywordInTitle = 232,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdForbiddenKeywordInText = 233,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdForbiddenKeywordInDisplayUrl = 234,

        [ErrorField(EntityField.AdTitle)]
        EditorialDefaultAdIncorrectAdFormatInTitle = 235,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdIncorrectAdFormatInText = 236,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdIncorrectAdFormatInDisplayUrl = 237,

        [ErrorField(EntityField.AdTitle)]
        EditorialDefaultAdTooMuchAdTextInTitle = 238,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdTooMuchAdTextInText = 239,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdTooMuchAdTextInDisplayUrl = 240,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialDefaultAdTooMuchAdTextInDestinationUrl = 241,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdNotEnoughAdText = 242,

        [ErrorField(EntityField.AdTitle)]
        EditorialDefaultAdExclusiveKeywordInTitle = 243,

        [ErrorField(EntityField.AdText)]
        EditorialDefaultAdExclusiveKeywordInText = 244,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdExclusiveKeywordInDisplayUrl = 245,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialDefaultAdInvalidDisplayUrlFormat = 246,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialDefaultAdInvalidDestinationUrlFormat = 247,

        [ErrorField(EntityField.AdTitle)]
        EditorialTooMuchAdTextInTitleAcrossAllAssociations = 248,

        [ErrorField(EntityField.AdText)]
        EditorialTooMuchAdTextInTextAcrossAllAssociations = 249,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialTooMuchAdTextInDisplayUrlAcrossAllAssociations = 250,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialPhoneNumHasNoDigits = 251,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialPhoneNumHasInvalidChars = 252,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialPhoneNumNotAllowedForCountry = 253,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialPhoneNumBlocked = 254,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialPhoneNumTooMuchText = 255,

        [ErrorField(EntityField.AdTitle)]
        EditorialPhoneNumNotAllowedInAdTitle = 256,

        [ErrorField(EntityField.AdText)]
        EditorialPhoneNumNotAllowedInAdText = 257,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialPhoneNumNotAllowedInAdDisplayUrl = 258,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialPhoneNumNotAllowedInAdBusinessName = 259,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialBusinessNameTooMuchText = 260,

        [ErrorField(EntityField.AdText)]
        EditorialErrorInAdTitle = 261,

        [ErrorField(EntityField.AdText)]
        EditorialErrorInAdText = 262,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialErrorInAdDisplayUrl = 263,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialErrorInAdDestinationUrl = 264,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialErrorInAdBusinessName = 265,

        [ErrorField(EntityField.AdPhoneNumber)]
        EditorialErrorInAdPhoneNumber = 266,

        [ErrorField(EntityField.AdTitle)]
        EditorialAdTitleBlankAcrossAllAssociations = 267,

        [ErrorField(EntityField.AdText)]
        EditorialAdTextBlankAcrossAllAssociations = 268,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialAdDisplayUrlBlankAcrossAllAssociations = 269,

        [ErrorField(EntityField.AdTitle)]
        EditorialAdTitleBlank = 270,

        [ErrorField(EntityField.AdText)]
        EditorialAdTextBlank = 271,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialAdDisplayUrlBlank = 272,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialAdDestinationUrlBlank = 273,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialDefaultAdTooMuchAdTextInBusinessName = 274,

        EditorialGenericError = 275,

        EditorialError = 276,

        // new errors for adult ads
        [ErrorField(EntityField.AdTitle)]
        EditorialKeywordParamDisallowedInAdTitle = 279,

        [ErrorField(EntityField.AdText)]
        EditorialKeywordParamDisallowedInAdText = 280,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialKeywordParamDisallowedInAdDisplayUrl = 281,

        [ErrorField(EntityField.AdTitle)]
        EditorialAdultSensitiveTermsNotificationRequiredAdTitle = 282,

        [ErrorField(EntityField.AdText)]
        EditorialAdultSensitiveTermsNotificationRequiredAdText = 283,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialAdultSensitiveTermsNotificationRequiredDisplayUrl = 284,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialAdultSensitiveTermsNotificationRequiredDestinationUrl = 285,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialAdultSensitiveTermsNotificationRequiredBusinessName = 286,

        [ErrorField(EntityField.AdText)]
        EditorialAdultSensitiveTermsNoNotificationAdTitle = 287,

        [ErrorField(EntityField.AdText)]
        EditorialAdultSensitiveTermsNoNotificationAdText = 288,

        [ErrorField(EntityField.AdDisplayUrl)]
        EditorialAdultSensitiveTermsNoNotificationDisplayUrl = 289,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialAdultSensitiveTermsNoNotificationDestinationUrl = 290,

        [ErrorField(EntityField.AdBusinessName)]
        EditorialAdultSensitiveTermsNoNotificationBusinessName = 291,

        [ErrorField(EntityField.AdDestinationUrl)]
        EditorialAdDestinationUrlBlankAcrossAllAssociations = 292,

        EditorialAppealEntityErrorUndefined = 293,

        EditorialAppealCreationQuotaExceeded = 294,

        EditorialAppealCreationQuotaExceededForLast24Hours = 295,

        EditorialAppealEntityAlreadyAppealed = 296,

        EditorialAppealResponseProcessingFault = 297,

        EditorialAppealCreationFault = 298,

        EditorialUnsupportedAppealEntity = 299,

        [ErrorField(EntityField.KeywordDestinationUrl)]
        EditorialTooMuchKeywordTextInDestinationUrl = 300,

        #endregion

        [DbErrorCodeMapping(-100630)]
        UserNotFoundOrInactive = 501,

        [DbErrorCodeMapping(-100900)]
        AdvertiserCustomerInvalidLifecycleStatus = 502, // need to be more explicit

        [DbErrorCodeMapping(-100901)]
        CustomerLocked = 503,

        [DbErrorCodeMapping(-100902)]
        AdvertiserCustomerInvalidFinancialstatus = 504,

        [DbErrorCodeMapping(-100920)]
        AgencyCustomerInvalidLifecycleStatus = 505,

        [DbErrorCodeMapping(-100921)]
        AgencyCustomerLocked = 506,

        [DbErrorCodeMapping(-100930)]
        SalesHouseCustomerLifeCycleStatusInvalid = 507,

        [DbErrorCodeMapping(-100931)]
        SaleshouseCustomerInvalidFinancialStatus = 508,

        [DbErrorCodeMapping(-100932)]
        SaleshouseCustomerLocked = 509,

        [DbErrorCodeMapping(-100940)]
        BilltoCustomerInvalidLifecycleStatus = 510,

        [DbErrorCodeMapping(-100941)]
        BilltoCustomerLocked = 511,

        [DbErrorCodeMapping(-100942)]
        BilltoCustomerInvalidFinancialStatus = 512,

        [DbErrorCodeMapping(-100910)]
        AccountInvalidLifecycleStatus = 513,

        [DbErrorCodeMapping(-100912)]
        AccountInvalidFinancialStatus = 514,

        [DbErrorCodeMapping(-100911)]
        AccountLocked = 515,

        [DbErrorCodeMapping(-100838)]
        PermissionDenied = 516, // User does not have permission for this operation

        NotAllowedToDoCurrentOperation = 517,

        MarketCountryOrLanguageForAccountInvalid = 518,

        EntityIdInvalid = 519,

        EntityIdDuplicate = 520,

        IdTypeDoesNotMatchFilterType = 522,

        InvalidGridFilterMetadataVersion = 523,

        MsClickIdTaggingEnabledValueInvalid = 524,

        CannotUpdateAccountPropertyByCustomerIdForThisPropertyName = 525,

        CustomerNotInLanguagePilot = 526,

        SearchStringNotSufficient = 527,

        BusinessAttributesValueInvalid = 528,

        EnableMMAUnderDSAAdgroupsValueInvalid = 529,

        AutoGoalEnabledValueInvalid = 530,

        #region Campaign between 1001 to 2000

        CampaignsNotPassed = 1001,

        [ErrorField(EntityField.CampaignName)]
        CampaignNameMissing = 1002,

        [ErrorField(EntityField.CampaignName)]
        CampaignNameTooLong = 1003,

        [ErrorField(EntityField.CampaignDescription)]
        CampaignDescriptionMissing = 1004,

        [ErrorField(EntityField.CampaignDescription)]
        CampaignDescriptionTooLong = 1005,

        [ErrorField(EntityField.CampaignName)]
        CampaignNameHasInvalidChars = 1006,

        [ErrorField(EntityField.CampaignMonthlyBudgetAmount)]
        CampaignBudgetAmountIsAboveLimit = 1007,

        [ErrorField(EntityField.CampaignMonthlyBudgetAmount)]
        CampaignBudgetAmountIsLessThanSpendAmount = 1008,

        [ErrorField(EntityField.CampaignDescription)]
        CampaignDescriptionHasInvalidChars = 1009,

        CampaignWithIdAlreadyExists = 1010,

        [ErrorField(EntityField.CampaignName)]
        CampaignAlreadyExists = 1011,

        [DbErrorCodeMapping(-106007)]
        CampaignBudgetTypeInvalid = 1012,

        CustomerNotEligibleToAddShoppingCampaign = 1013,
        CampaignHasDuplicateSettings = 1014,
        CampaignSettingsMissing = 1015,
        CampaignHasUnsupportedSettings = 1016,
        CampaignInvalidShoppingCampaignPriority = 1017,
        CampaignInvalidShoppingCampaignSalesCountry = 1018,
        CampaignInvalidShoppingCampaignProviderId = 1019,
        CampaignShoppingCampaignSalesCountryShouldNotBeSet = 1020,
        CampaignShoppingCampaignProviderIdShouldNotBeSet = 1021,
        CampaignAdvertiserChannelTypeShouldNotBeSet = 1022,
        CampaignOnlyOneCampaignTypeShouldBeSet = 1023,
        CampaignInvalidShoppingCampaignSubType = 1024,
        CampaignCoOpCampaignShouldNotBeReturned = 1025,
        CustomerNotEnabledForSponsoredProductAdsV2 = 1026,
        CustomerNotEnabledForSmartShoppignCampaign = 1027,
        CustomerHasNoUETForMaxConversionValueBiddingScheme = 1028,
        AccountHasNoRevenueConversionGoalForMaxConversionValueBiddingScheme = 1029,
        ShoppingSmartAdsBidAdjustmentNotSupported = 1030,
        ShoppingSmartAdsEntityNotSupported = 1031,
        SmartShoppingCampaignLimitExceeded = 1032,
        SmartShoppingUpdateInvalidDeleteItIfWantImportSuccess = 1033,
        ShoppingCampaignStoreIdInferredFromMapping = 1034,
        CampaignBudgetIsMissingOrNotCalculable = 1035,
        InvalidPlaceholderStoreId = 1036,
        CampaignHasDraftStore = 1037,
        GoalLevelCannotBeChanged = 1038,
        GoalLevelCannotBeDowngraded = 1039,
        GoalIsReadOnly = 1040,
        TagIsReadOnly = 1041,
        NoMappedMicrosoftAppFound = 1042,
        DuplicateCampaignForTheSameMicrosoftApp = 1043,
        SmartShoppingCampaignCreationNotSupported = 1044,
        LegacyWinstoreAdsCampaignCreationNotSupported = 1045,
        PlacementTargetingNotSupported = 1046,

        [DbErrorCodeMapping(-600023)]
        [DbErrorCodeMapping(-100834)]
        [DbErrorCodeMapping(-102021)] // "Campaigns must belong to the same Account."
        [DbErrorCodeMapping(-101201)]
        [DbErrorCodeMapping(-100736)] // When associating with Negative Keyword list
        [ErrorField(EntityField.CampaignId)]
        CampaignIdInvalid = 1501,

        [DbErrorCodeMapping(-100951)]
        [ErrorField(EntityField.CampaignStatus)]
        CampaignCannotPauseResumeDueToInvalidStatus = 1502,

        [DbErrorCodeMapping(-101005)]
        [ErrorField(EntityField.CampaignMonthlyBudgetAmount)]
        CampaignBudgetAmountIsBelowConfiguredLimit = 1503,

        [DbErrorCodeMapping(-100950)]
        CampaignHasInvalidStatus = 1504,

        [DbErrorCodeMapping(-101204)]
        CampaignIsDeleted = 1505,

        [DbErrorCodeMapping(-100841)]
        [ErrorField(EntityField.CampaignName)]
        CampaignWithNameAlreadyExists = 1506,

        [DbErrorCodeMapping(-100450)]
        CampaignStartDateLaterThanAdGroupStartDate = 1507,

        [DbErrorCodeMapping(-100200)]
        CampaignCannotModifyStartDate = 1508,

        [DbErrorCodeMapping(-100190)]
        CampaignDatesOverlapwithAdGroupDates = 1509,

        [DbErrorCodeMapping(-100150)]
        CampaignTimestampMismatch = 1510,

        [DbErrorCodeMapping(-101006)]
        CampaignBudgetLessThanAdGroupBudget = 1511,

        [DbErrorCodeMapping(-100742)]
        MaxActiveCampaignsLimitReached = 1512,

        [DbErrorCodeMapping(-100113)]
        [ErrorField(EntityField.CampaignDailyTargetBudgetAmount)]
        CampaignDailyTargetBudgetAmountIsInvalid = 1513,

        CampaignNotEligibleToModifyCashBack = 1514,

        AccountNotEligibleToModifyCashBack = 1515,

        CampaignCashbackNeedToBeEnabledBeforeEnablingAdgroup = 1516,

        // Todo: XRG caskback: remove
        CashBackStatusRequied = 1517,

        InvalidCashbackAmount = 1518,

        CashbackTextTooLong = 1519,

        CashbackAllowedOnlyForSearchMedium = 1520,

        CashbackNotAllowedForAdgroupsDistributionChannel = 1521,

        AccountNotEligbleForKeywordLevelCashback = 1522,

        AccountNotEligbleToSetCashbackAmount = 1523,

        CashbackInfoShouldBeNullForBackwardCompatability = 1524,

        [DbErrorCodeMapping(-190140)]
        CampaignIdAndAccountIdAreBothNull = 1525,

        PhoneExtensionPhoneNumberMissing = 1526,

        PhoneExtensionPhoneNumberTooLong = 1527,

        PhoneExtensionPhoneNumberHasInvalidChars = 1528,

        PhoneExtensionInvalidCountry = 1529,

        CustomerNotEligibleToSetClickToCallOnly = 1530,

        PhoneExtensionDataNotNull = 1531,

        CustomerNotEligibleToSetAdExtension = 1532,

        PhoneExtensionPhoneNumberInvalid = 1533,

        MaxCampaignIdsLimitExceeded = 1534,

        AccountNotEligbleToCreateInlineAppeal = 1535,

        JustificationTextMissingForInlineAppeal = 1536,

        JustificationTextTooLongForInlineAppeal = 1537,

        [DbErrorCodeMapping(-100885)]
        [DbErrorCodeMapping(-100879)]
        IPExclusionLimitExceeded = 1538,

        IPAddressInvalid = 1539,


        KeywordVariantMatchTypePilotNotEnabledForCustomer = 1540,

        CampaignIdsNotPassed = 1541,

        CampaignIdRequiredWhenAdgroupIdPassed = 1542,

        CampaignProductFiltersAlreadyExist = 1543,

        EntityExclusionBatchLimitExceeded = 1544,

        //Video Ads
        CampaignSubtypeNotAllowedInCampaignType = 1545,
        AccountNotEnabledForVideoCampaign = 1546,
        AccountNotEnabledForVideoAdEditor = 1547,
        AccountNotEnabledForCustomVideoAssetThumbnail = 1548,
        AccountNotEnabledForVideoAdsEditorCaption = 1549,
        AccountNotEnabledForVideoAdsEditorCropClip = 1550,
        #endregion

        // Campaign Conversion Goal
        AccountNotEligibleForCampaignConversionGoal = 1551,
        [DbErrorCodeMapping(-120010)]
        StoreVisitNotSupportForCampaignConversionGoal = 1552,
        DuplicateCampaignConversionGoal = 1553,
        [DbErrorCodeMapping(-120013)]
        CampaignConversionGoalNotExist = 1554,
        InvalidCampaignConversionGoalSubType = 1555,
        CampaignNotEligibleForCampaignConversionGoal = 1556,

        // Boost
        UnsupportedCampaignTypeForBoostAccount = 1600,
        BidAdjustmentNotSupportedForBoostAccount = 1601,
        OptimizedTargetingMustBeTurnOnForBoost = 1602,

        // Measurement
        AccountNotEnabledForDoubleVerifyMeasurement = 1700,
        InvalidDoubleVerifyLinkToken = 1701,
        UnableToPerformDoubleVerifyAdvertiserLink = 1702,
        UnableToPerformDoubleVerifyAccountUnlink = 1703,

        TargetCpaLowerThanDailyTargetBudgetAmount = 1801,

        #region AdGroup between 2001 to 3000

        AdGroupsNotPassed = 2001,

        [ErrorField(EntityField.AdGroupName)]
        AdGroupNameMissing = 2002,

        [ErrorField(EntityField.AdGroupName)]
        AdGroupNameTooLong = 2003,

        [ErrorField(EntityField.AdGroupName)]
        AdGroupNameHasInvalidChars = 2004,

        UserNotAuthorizedForDistributionChannel = 2005,

        DistributionSiteNotAuthorizedForKeywordLanguage = 2006,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupMediumMissing = 2007,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupUserNotAllowedContentMedium = 2008,

        [DbErrorCodeMapping(-100305)]
        AdGroupStartDateLessThanCurrentDate = 2501,

        [DbErrorCodeMapping(-101101)]
        [DbErrorCodeMapping(-100195)]
        AdGroupStartDateLessThanEndDate = 2502,

        [DbErrorCodeMapping(-100743)]
        [DbErrorCodeMapping(-100744)]
        MaxNoOfAdGroupsExceededForCampaign = 2503,

        [DbErrorCodeMapping(-100842)]
        [ErrorField(EntityField.AdGroupName)]
        AdGroupAlreadyExists = 2504,

        [DbErrorCodeMapping(-100590)]
        AdGroupInExpiredStateCannotBeUpdated = 2505,

        [DbErrorCodeMapping(-100837)]
        AdGroupInSubmittedStateCannotBeUpdated = 2506,

        [DbErrorCodeMapping(-101211)]
        AdGroupNotValidInGivenCampaign = 2507,

        [DbErrorCodeMapping(-100960)]
        AdGroupHasInvalidStatus = 2508,

        [DbErrorCodeMapping(-101214)]
        AdGroupIsDeleted = 2509,

        [DbErrorCodeMapping(-100962)]
        AdGroupCannotPauseResumeDueToInvalidStatus = 2510,

        [DbErrorCodeMapping(-100591)]
        AdGroupExpired = 2511,

        [DbErrorCodeMapping(-600001)]
        [DbErrorCodeMapping(-190054)]
        [DbErrorCodeMapping(-100844)]
        [DbErrorCodeMapping(-102026)] // Returned when UpdateTarget fails due to mismatch in adgroupId and targetId
        [DbErrorCodeMapping(-102020)] // "Orders must belong to the same Account."
        AdGroupIdInvalid = 2512,

        [DbErrorCodeMapping(-101221)]
        EntityDoesNotBelongToAdGroup = 2513,

        AdGroupWithIdAlreadyExists = 2514,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupInvalidMedium = 2515,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupInvalidDistributionChannel = 2516,

        AdGroupDistributionChannelNoLanguage = 2517,

        [DbErrorCodeMapping(-100205)]
        AdGroupCannotBeSubmittedWithoutAtleastOneAdAndOneKeyword = 2518,

        [DbErrorCodeMapping(-100595)]
        AdGroupStartDateCannotBeEarlierThanSubmitDate = 2519,

        AdGroupTimestampMismatch = 2520,

        [DbErrorCodeMapping(-102117)]
        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupMediumNotAllowedForImageAds = 2521,

        [DbErrorCodeMapping(-100868)]
        [DbErrorCodeMapping(-100869)]
        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupPricingModelCpmRequiresContentMedium = 2522,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupInvalidDistributionChannelForSpecifiedMedium = 2523,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupInvalidMediumForCustomer = 2524,

        AdGroupPricingModelCpmIsNotEnabledForCustomer = 2525,

        AdGroupPricingModelValueEmpty = 2526,

        NegativeSiteURLInvalid = 2527,

        NegativeSiteURLTooLong = 2528,

        NegativeSiteURLExceedMaxCount = 2529,

        OptimizationTargetNotSpecified = 2530,

        OptimizationTargetInvalid = 2531,

        AdGroupBidsUpdateDisabledForOptimizationManagedAdGroup = 2532,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupMediumUpdateDisabledForOptimizationManagedAdGroup = 2533,

        CanSetBidTargetToBehaviorSegmentOnlyForContentAdGroups = 2534,

        CanSetBidTargetToSiteOnlyForContentAdGroups = 2535,

        CannotChangeMediumForThisBidTarget = 2536,

        InvalidMediumAndBiddingStrategyCombination = 2537,

        TooManyAdGroupsInAccount = 2538,

        [DbErrorCodeMapping(-100846)]
        InvalidLCIDForDistributionChannel = 2539,

        [DbErrorCodeMapping(-100441)]
        [DbErrorCodeMapping(-100442)]
        AdGroupBidIsBelowFloorPrice = 2540, // this is when OrderItem Content bid is NULL

        AdGroupMediumNotAllowedForTPCAds = 2541,

        [DbErrorCodeMapping(-101426)] // !!!TODO-grigoryc: it is inconsistent with similar error for Campaigns (101201) - should map to 'AdGroupIdInvalid'
        AdGroupDoesNotBelongToAccount = 2542,

        [ErrorField(EntityField.AdGroupExactMatchBid)]
        AdGroupExactBidAmountsGreaterThanCeilingPrice = 2543,

        [ErrorField(EntityField.AdGroupPhraseMatchBid)]
        AdGroupPhraseBidAmountsGreaterThanCeilingPrice = 2544,

        [ErrorField(EntityField.AdGroupBroadMatchBid)]
        AdGroupBroadBidAmountsGreaterThanCeilingPrice = 2545,

        [ErrorField(EntityField.AdGroupContentMatchBid)]
        AdGroupContentBidAmountsGreaterThanCeilingPrice = 2546,

        [ErrorField(EntityField.AdGroupExactMatchBid)]
        AdGroupExactBidAmountsLessThanFloorPrice = 2547,

        [ErrorField(EntityField.AdGroupPhraseMatchBid)]
        AdGroupPhraseBidAmountsLessThanFloorPrice = 2548,

        [ErrorField(EntityField.AdGroupBroadMatchBid)]
        AdGroupBroadBidAmountsLessThanFloorPrice = 2549,

        [ErrorField(EntityField.AdGroupContentMatchBid)]
        AdGroupContentBidAmountsLessThanFloorPrice = 2550,

        AdGroupAudienceCriterionBidTypeInvalid = 2551,

        NegativeSiteURLExceedMaxSubDirectories = 2600,
        NegativeSiteURLExceedMaxSubDomains = 2601,

        [ErrorField(EntityField.AdGroupNetwork)]
        CannotSetNetworkForContentAdGroup = 2650,

        [ErrorField(EntityField.AdGroupNetwork)]
        InvalidNetwork = 2651,

        NegativeSiteCannotBeOwnedOrOperatedSite = 2652,

        [DbErrorCodeMapping(-100872)]
        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupMediumNotAllowedForRichSearchAds = 2653,

        [DbErrorCodeMapping(-100870)]
        AdgroupHasRichSearchAdMediumCannotBeChanged = 2654,

        [DbErrorCodeMapping(-100871)]
        [DbErrorCodeMapping(-102131)]
        AdgroupHasNonRichSearchAdCannotHaveRichSearchAd = 2655,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        MissingPublisherCountries = 2656,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        PublisherCountriesNotAllowedForAdcenterManaged = 2657,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        InvalidPublisherCountryForLanguage = 2658,

        [ErrorField(EntityField.AdGroupMedium)]
        AdGroupMediumNotAllowedForPublisherCountries = 2659,

        [ErrorField(EntityField.AdGroupLanguage)]
        [DbErrorCodeMapping(-100873)]
        MissingLanguage = 2660,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        MultiplePublisherCountriesNotAllowed = 2661,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        PublisherCountriesUpdateNotAllowed = 2662,

        AdGroupNetworkValueNotAllowedForPublisherCountries = 2663,

        // ad rotation error codes
        DatesMustBeNullWhenAdRotationIsOptimizedForClicks = 2664,
        [DbErrorCodeMapping(-101459)]
        AdRotationStartDateMustBeBeforeAdGroupEndDate = 2665,
        [DbErrorCodeMapping(-101457)]
        AdRotationStartDateMustBeAfterAdGroupStartDate = 2666,
        [DbErrorCodeMapping(-101458)]
        AdRotationEndDateMustBeAfterStartDate = 2667,

        [DbErrorCodeMapping(-100884)]
        NegativeSiteEntityLimitExceeded = 2668,

        CustomerNotEligibleToSetAdRotation = 2669,

        AdGroupIdsNotPassed = 2670,

        [DbErrorCodeMapping(-101502)]
        MaxAdGroupIdCountExceeded = 2671,

        [DbErrorCodeMapping(-101501)]
        MaxTopNExceeded = 2672,

        InvalidTopN = 2673,

        InvalidAdRotationType = 2674,

        CoOpSettingBidOptionInvalid = 2675,

        CoOpSettingBidBoostValueInvalid = 2676,

        CoOpSettingBidMaxValueInvalid = 2677,

        CustomerNotEnableForInHousePromotion = 2678,

        CoOpAdGroupShouldNotBeReturned = 2679,

        ProductPartitionNodeBidBelowServing = 2680,

        [DbErrorCodeMapping(-101503)]
        AdGroupLanguageCannotBeRemovedUntilCampaignLanguagesAreProcessed = 2681,

        AdGroupBiddingSchemeNotSupported = 2682,

        InvalidPricingModel = 2683,

        #endregion

        #region keyword between 3001 and 4000

        KeywordsNotPassed = 3001,

        [DbErrorCodeMapping(-100730)]
        [ErrorField(EntityField.KeywordText)]
        KeywordIsBlank = 3002,

        [ErrorField(EntityField.KeywordText)]
        KeywordTooLong = 3003,

        [ErrorField(EntityField.KeywordText)]
        KeywordInvalid = 3004,

        [DbErrorCodeMapping(-100449)]
        [DbErrorCodeMapping(-101450)]
        InvalidSearchBids = 3005,

        [DbErrorCodeMapping(-100448)]
        InvalidContentBid = 3006,

        // TODO: remove when BusLib is updated
        InvalidBidsForSearchAdGroup = 3007,

        // TODO: remove when BusLib is updated
        [ErrorField(EntityField.AdGroupContentMatchBid)]
        InvalidBidsForContentAdGroup = 3008,

        // TODO: remove when BusLib is updated
        InvalidBidsForHybridAdGroup = 3009,

        [ErrorField(EntityField.KeywordParam1)]
        Param1Invalid = 3010,

        [ErrorField(EntityField.KeywordParam1)]
        Param1TooLong = 3011,

        [ErrorField(EntityField.KeywordParam2)]
        Param2Invalid = 3012,

        [ErrorField(EntityField.KeywordParam2)]
        Param2TooLong = 3013,

        [ErrorField(EntityField.KeywordParam3)]
        Param3Invalid = 3014,

        [ErrorField(EntityField.KeywordParam3)]
        Param3TooLong = 3015,

        [DbErrorCodeMapping(-101215)]
        [DbErrorCodeMapping(-101217)]
        [DbErrorCodeMapping(-100854)]
        [ErrorField(EntityField.KeywordId)]
        KeywordIdInvalid = 3016,

        NegativeKeywordRequiresPartialMatchBid = 3017,

        [ErrorField(EntityField.KeywordExactMatchBid)]
        InvalidExactBidForHybridAdGroup = 3018,

        [ErrorField(EntityField.KeywordPhraseMatchBid)]
        InvalidPhraseBidForHybridAdGroup = 3019,

        [ErrorField(EntityField.KeywordBroadMatchBid)]
        InvalidBroadBidForHybridAdGroup = 3020,

        [ErrorField(EntityField.KeywordContentMatchBid)]
        InvalidContentBidForHybridAdGroup = 3021,

        [ErrorField(EntityField.KeywordExactMatchBid)]
        InvalidExactBidForSearchAdGroup = 3022,

        [ErrorField(EntityField.KeywordPhraseMatchBid)]
        InvalidPhraseBidForSearchAdGroup = 3023,

        [ErrorField(EntityField.KeywordBroadMatchBid)]
        InvalidBroadBidForSearchAdGroup = 3024,

        [ErrorField(EntityField.KeywordContentMatchBid)]
        InvalidContentBidForSearchAdGroup = 3025,

        [ErrorField(EntityField.KeywordExactMatchBid)]
        InvalidExactBidForContentAdGroup = 3026,

        [ErrorField(EntityField.KeywordPhraseMatchBid)]
        InvalidPhraseBidForContentAdGroup = 3027,

        [ErrorField(EntityField.KeywordBroadMatchBid)]
        InvalidBroadBidForContentAdGroup = 3028,

        [ErrorField(EntityField.KeywordContentMatchBid)]
        InvalidContentBidForContentAdGroup = 3029,

        [DbErrorCodeMapping(-100445)]
        [ErrorField(EntityField.AdGroupCriterionExactMatchBid)]
        BidAmountsLessThanFloorPrice = 3501,

        [DbErrorCodeMapping(-100444)]
        [ErrorField(EntityField.AdGroupCriterionExactMatchBid)]
        BidsAmountsGreaterThanCeilingPrice = 3502,

        [DbErrorCodeMapping(-100285)]
        KeywordAlreadyExists = 3503,

        [DbErrorCodeMapping(-100746)]
        MaxKeywordsLimitExceededForAccount = 3504,

        [DbErrorCodeMapping(-100745)]
        MaxKeywordsLimitExceededForAdGroup = 3505,

        [DbErrorCodeMapping(-100825)]
        [DbErrorCodeMapping(-100826)]
        [DbErrorCodeMapping(-101218)]
        KeywordDoesNotBelongToAdGroupId = 3506,

        [DbErrorCodeMapping(-101234)]
        KeywordIsDeleted = 3507,

        [DbErrorCodeMapping(-100970)]
        KeywordInInvalidStatus = 3508,

        [ErrorField(EntityField.KeywordText)]
        KeywordUpdateEmpty = 3509,

        [ErrorField(EntityField.KeywordId)]
        KeywordIdDuplicateInRequest = 3510,

        KeywordModificationDisabledForOptimizationManagedAdGroup = 3511,

        KeywordUpdateDisabledForUserManagedAdGroup = 3512,

        CannotAddKeywordToAdGroupTypeMismatch = 3513,

        [DbErrorCodeMapping(-190141)]
        [DbErrorCodeMapping(-101216)]
        [ErrorField(EntityField.KeywordText)]
        KeywordDoesNotBelongToAccount = 3514,

        [DbErrorCodeMapping(-100750)] // Ceiling price errors have occurred for ExactMatch
        [ErrorField(EntityField.KeywordExactMatchBid)]
        KeywordExactBidAmountsGreaterThanCeilingPrice = 3515,

        [DbErrorCodeMapping(-100751)] // Ceiling price errors have occurred for PhraseMatch
        [ErrorField(EntityField.KeywordPhraseMatchBid)]
        KeywordPhraseBidAmountsGreaterThanCeilingPrice = 3516,

        [DbErrorCodeMapping(-100752)] // Ceiling price errors have occurred for BroadMatch
        [ErrorField(EntityField.KeywordBroadMatchBid)]
        KeywordBroadBidAmountsGreaterThanCeilingPrice = 3517,

        [DbErrorCodeMapping(-100753)] // Ceiling price errors have occurred for Content
        [ErrorField(EntityField.KeywordContentMatchBid)]
        KeywordContentBidAmountsGreaterThanCeilingPrice = 3518,

        [DbErrorCodeMapping(-100755)] // Floor price errors have occurred for ExactMatch
        [ErrorField(EntityField.KeywordExactMatchBid)]
        KeywordExactBidAmountsLessThanFloorPrice = 3519,

        [DbErrorCodeMapping(-100756)] // Floor price errors have occurred for PhraseMatch
        [ErrorField(EntityField.KeywordPhraseMatchBid)]
        KeywordPhraseBidAmountsLessThanFloorPrice = 3520,

        [DbErrorCodeMapping(-100757)] // Floor price errors have occurred for BroadMatch
        [ErrorField(EntityField.KeywordBroadMatchBid)]
        KeywordBroadBidAmountsLessThanFloorPrice = 3521,

        [DbErrorCodeMapping(-100758)] // Floor price errors have occurred for Content
        [ErrorField(EntityField.KeywordContentMatchBid)]
        KeywordContentBidAmountsLessThanFloorPrice = 3522,

        TooManyKeywordsToUpdate = 3523,
        MultipleBidTypesNotAllowed = 3524,

        [DbErrorCodeMapping(-101449)]
        [ErrorField(EntityField.KeywordTextAndMatchType)]
        KeywordAndMatchTypeCombinationAlreadyExists = 3525,

        KeywordBidRequired = 3526,
        ZeroBidAmountNotAllowed = 3527,
        MatchTypeChangeNotAllowedInUpdate = 3528,
        MatchTypeRequiredForAdd = 3529,
        BidAmountNotAllowedForAmountSourceUseFromAdGroup = 3530,
        KeywordByMatchTypePilotNotEnabledForCustomer = 3531,
        MatchTypeNotUpdated = 3532,
        KeywordTextEditTransactionalDependencyFailed = 3533,
        EntityNotAllowedForShoppingCampaign = 3534,

        [DbErrorCodeMapping(-100768)]
        MaxKeywordsLimitExceededForCustomer = 3535,

        InvalidMatchTypes = 3536,
        InvalidBid = 3537,
        #endregion

        #region Ad errors between 4001 and 5000

        AdsNotPassed = 4001,

        [ErrorField(EntityField.AdTitle)]
        AdTitleInvalid = 4002,

        [ErrorField(EntityField.AdText)]
        AdTextInvalid = 4003,

        [ErrorField(EntityField.AdDisplayUrl)]
        AdDisplayUrlInvalid = 4004,

        [ErrorField(EntityField.AdDestinationUrl)]
        AdDestinationUrlInvalid = 4005,

        [DbErrorCodeMapping(-600055)]
        [ErrorField(EntityField.AdId)]
        AdIdInvalid = 4006,

        AdUpdateEmpty = 4007,

        [ErrorField(EntityField.AdType)]
        AdTypeInvalidForThisOperation = 4008,

        AdIsNull = 4009,

        [ErrorField(EntityField.AdPhoneNumber)]
        MobileAdPhoneNumberInvalid = 4010,

        [ErrorField(EntityField.AdBusinessName)]
        MobileAdBusinessNameInvalid = 4011,

        [ErrorField(EntityField.AdUrlsPhoneNumBusinessName)]
        MobileRequiredDataMissing = 4012,

        [ErrorField(EntityField.AdType)]
        MobileAdSupportedForSearchOnlyAdGroups = 4013,

        [ErrorField(EntityField.AdType)]
        AdTypeInvalidForCustomer = 4015,

        [ErrorField(EntityField.AdType)]
        AdTypeDoesNotMatchExistingValue = 4016,

        ImageAdUpdateHeightWidthRequiredWithAssetId = 4017,

        ImageAdUpdateAssetIDRequiredWithHeightWidth = 4018,

        AdIdAlreadyExists = 4019,

        [ErrorField(EntityField.AdType)]
        AdTypeInvalidForCampaign = 4020,

        [DbErrorCodeMapping(-190037)]
        [DbErrorCodeMapping(-100983)]
        AdInInvalidStatus = 4501,

        [DbErrorCodeMapping(-100737)]
        [DbErrorCodeMapping(-101427)]
        AdDoesNotBelongToAccount = 4502,

        [DbErrorCodeMapping(-100748)]
        [DbErrorCodeMapping(-100747)]
        MaxAdsLimitExceeded = 4503,

        [DbErrorCodeMapping(-100732)]
        AdAlreadyPresent = 4504,

        [DbErrorCodeMapping(-102116)]
        ImageAdAssetIdInvalid = 4505,

        [DbErrorCodeMapping(-102118)]
        MaxImageAdsLimitExceededForCustomer = 4507,

        [DbErrorCodeMapping(-101224)]
        AdDeleted = 4508,

        [DbErrorCodeMapping(-101222)]
        AdCannotPauseResumeDueToInvalidStatus = 4509,

        TPCAdAssetIdInvalid = 4510,

        RichSearchAdAssetIdInvalid = 4511,

        AdTypeNotEnabledForMarket = 4512,

        [DbErrorCodeMapping(-101223)]
        ResumeFailedDueToMigrationErrors = 4513,
        [ErrorField(EntityField.PromotionalText)]
        ProductAdPromotionalTextTooLong = 4514,
        [ErrorField(EntityField.PromotionalText)]
        ProductAdPromotionalTextInvalid = 4515,

        DevicePreferenceIncompatibleWithAdType = 4516,

        InvalidAdDevicePreference = 4517,

        DuplicateAdId = 4518,

        [ErrorField(EntityField.AppPlatform)]
        AppInstallAdAppPlatformNullOrEmpty = 4550,

        [ErrorField(EntityField.AppPlatform)]
        AppInstallAdAppPlatformInvalid = 4551,

        [ErrorField(EntityField.AppStoreId)]
        AppInstallAdAppStoreIdIsNullOrEmpty = 4552,

        [ErrorField(EntityField.AppStoreId)]
        AppInstallAdAppStoreIdTooMuchText = 4553,

        [ErrorField(EntityField.AppStoreId)]
        AppInstallAdAppStoreIdInvalid = 4554,

        [ErrorField(EntityField.AppPlatform)]
        AppInstallAdUpdateAppPlatformChanged = 4555,

        [ErrorField(EntityField.AppStoreId)]
        AppInstallAdUpdateAppStoreIdChanged = 4556,

        AppInstallAdAppMetaMaturityRatingTooMuchText = 4557,
        AppMetadataIsNullOrEmpty = 4558,
        AppInstallAdIsNotEnabled = 4559,
        RequiredAppMetadataPropertyIsNullOrEmpty = 4560,

        [ErrorField(EntityField.AdTitlePart1)]
        ExpandedTextAdTitlePart1TooLong = 4561,

        [ErrorField(EntityField.AdTitlePart1)]
        ExpandedTextAdTitlePart1Invalid = 4562,

        [ErrorField(EntityField.AdTitlePart2)]
        ExpandedTextAdTitlePart2TooLong = 4563,

        [ErrorField(EntityField.AdTitlePart2)]
        ExpandedTextAdTitlePart2Invalid = 4564,

        [ErrorField(EntityField.AdPath1)]
        ExpandedTextAdPath1TooLong = 4565,

        [ErrorField(EntityField.AdPath1)]
        ExpandedTextAdPath1Invalid = 4566,

        [ErrorField(EntityField.AdPath2)]
        ExpandedTextAdPath2TooLong = 4567,

        [ErrorField(EntityField.AdPath2)]
        ExpandedTextAdPath2Invalid = 4568,

        ExpandedTextAdPath2SetWithoutPath1 = 4569,
        ExpandedTextAdCombinedTitleTooLong = 4570,
        ExpandedTextAdFinalUrlDomainTooLong = 4571,
        ExpandedTextAdDisplayUrlDomainTooLong = 4572,
        ExpandedTextAdDisplayUrlDomainInvalid = 4573,
        ExpandedTextAdDisplayUrlMissing = 4574,
        ExpandedTextAdDisplayUrlDomainExtractionFailed = 4575,
        CustomerNotEnabledForLocalInventoryAds = 4576,

        [ErrorField(EntityField.AdType)]
        AdTypeInvalid = 4577,

        // Function Error Code
        ExpandedTextAdInvalidFunctionFormat = 4578,
        ExpandedTextAdUnknownFunction = 4579,
        ExpandedTextAdMissingDelimiterBetweenFunctions = 4580,

        ExpandedTextAdCountdownInvalidDateTime = 4581,
        ExpandedTextAdCountdownInvalidLanguageCode = 4582,
        ExpandedTextAdCountdownInvalidDaysBefore = 4583,
        ExpandedTextAdCountdownDaysBeforeOutOfRange = 4584,
        ExpandedTextAdCountdownInvalidParameters = 4585,
        ExpandedTextAdCountdownPastDateTime = 4586,
        ExpandedTextAdCountdownInvalidDefaultText = 4587,

        [DbErrorCodeMapping(-100769)]
        MaxAdsLimitExceededForCustomer = 4588,

        [DbErrorCodeMapping(-100770)]
        MaxAdsLimitExceededForAccount = 4589,

        ExpandedTextAdDefaultTextRequiredForKeywordParameter = 4590,

        AdDisplayUrlDomainExtractionFailed = 4591,

        // Adcustomizer function validations
        AdCustomizerFeedNameMissing = 4592,
        AdCustomizerFeedAttributeMissing = 4593,
        AdCustomizerDefaultValueMissing = 4594,
        FeedPerAdLimitExceeded = 4595,
        FeedNameDoesNotExist = 4596,
        InvalidFeedForAdType = 4597,
        FeedAttributeDoesNotExist = 4598,
        InvalidFeedAttributeForAdType = 4599,

        // Expanded Text Ad V2
        [ErrorField(EntityField.AdTitlePart3)]
        ExpandedTextAdTitlePart3TooLong = 4600,
        [ErrorField(EntityField.AdTitlePart3)]
        ExpandedTextAdTitlePart3Invalid = 4601,
        [ErrorField(EntityField.AdText)]
        ExpandedTextAdTextPart1TooLong = 4602,
        [ErrorField(EntityField.AdText)]
        ExpandedTextAdTextPart1Invalid = 4603,
        [ErrorField(EntityField.AdTextPart2)]
        ExpandedTextAdTextPart2TooLong = 4604,
        [ErrorField(EntityField.AdTextPart2)]
        ExpandedTextAdTextPart2Invalid = 4605,
        InvalidFeedAttributeTypeInCountdown = 4606,

        // Responsive Ad
        [ErrorField(EntityField.ImpressionTrackingUrls)]
        ImpressionTrackingUrlsExceedMaxCount = 4607,
        [ErrorField(EntityField.ImpressionTrackingUrls)]
        ImpressionTrackingUrlInvalid = 4608,
        [ErrorField(EntityField.ImpressionTrackingUrls)]
        ImpressionTrackingUrlInaccessible = 4609,
        CallToActionNotSupported = 4610,
        CallToActionCallToActionLanguagePairNotSupported = 4611,
        AccountNotEnabledForShoppableAds = 4612,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        InvalidHotSpot = 4613,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        HotSpotMissingRequiredField = 4614,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        InvalidGlyph = 4615,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        HotSpotsHasDuplication = 4616,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        CustomerIsNotAllowedForHotSpots = 4617,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        InvalidPlacement = 4618,
        [ErrorField(EntityField.ResponsiveAdHotSpots)]
        DuplicatePlacement = 4619,
        [ErrorField(EntityField.ResponsiveBoostAnchors)]
        InvalidBoostAnchors = 4620,
        [ErrorField(EntityField.CallToAction)]
        CallToActionTextInvalid = 4621,
        [ErrorField(EntityField.CallToAction)]
        AccountNotEnabledForCampaignAutomatedCallToActionOptOut = 4622,
        [ErrorField(EntityField.ResponsiveAdVerifiedTrackingSettings)]
        VerifiedTrackingSettingsIsNotAllowed = 4623,
        [ErrorField(EntityField.ResponsiveAdVerifiedTrackingSettings)]
        EmptyVerifiedTrackingSettings = 4624,
        [ErrorField(EntityField.ResponsiveAdVerifiedTrackingSettings)]
        TooManyItemsInVerifiedTrackingSettings = 4625,
        [ErrorField(EntityField.CallToAction)]
        CallToActionAriaTextInvalid = 4626,
        CallToActionAriaTextNotSupported = 4627,
        [ErrorField(EntityField.CallToAction)]
        AccountNotEnabledForCampaignCallToActionOptOut = 4628,
        [ErrorField(EntityField.CallToAction)]
        CampaignAutomatedCallToActionOptOutNotAllowedForCampaignType = 4629,
        [ErrorField(EntityField.CallToAction)]
        CampaignCallToActionOptOutNotAllowedForCampaignType = 4630,

        #endregion

        #region Target between 5001 and 5500

        TargetNotPassed = 5001,

        [DbErrorCodeMapping(-101466)]
        TargetsNotPassed = 5002,

        TargetAlreadyExists = 5003,

        MaxTargetsLimitsExceeded = 5004,

        [DbErrorCodeMapping(-102000)] // Target does not belong to customer
        InvalidTargetId = 5005,

        NoBidsInTarget = 5006,

        [ErrorField(EntityField.Name)]
        InvalidTargetName = 5007,

        InvalidDayTarget = 5008,

        InvalidHourTarget = 5009,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        InvalidLocationTarget = 5010,

        InvalidGenderTarget = 5011,

        InvalidAgeTarget = 5012,

        InvalidDeviceTarget = 5013,

        InvalidBusinessLocationTarget = 5014,

        [DbErrorCodeMapping(-101440)]
        InvalidGeoCodeStatusForBusinessLocation = 5015,

        InvalidCustomLocationTarget = 5016,

        [ErrorField(EntityField.Radius)]
        InvalidTargetRadius = 5017,

        [ErrorField(EntityField.Latitude)]
        InvalidLatitude = 5018,

        [ErrorField(EntityField.Longitude)]
        InvalidLongitude = 5019,

        DuplicateDayTarget = 5020,

        DuplicateHourTarget = 5021,

        DuplicateMetroAreaLocationTarget = 5022,

        DuplicateCountryLocationTarget = 5023,

        DuplicateGenderTarget = 5024,

        DuplicateAgeTarget = 5025,

        DuplicateSubGeographyTarget = 5026,

        DuplicateCityTarget = 5027,

        DuplicateBusinessLocationTarget = 5028,

        DuplicateCustomLocationTarget = 5029,

        DuplicateDeviceTarget = 5030,

        CountryAndMetroAreaTargetsMutuallyExclusive = 5031,

        MetroAreaTargetsFromMultipleCountries = 5032,

        [DbErrorCodeMapping(-101435)] // When adding/updating target
        InvalidLocationId = 5033,

        InvalidGeoLocationLevel = 5034,

        GeoLocationOptionsRequired = 5035,

        UnsupportedCombinationOfLocationIdAndOptions = 5036,

        InvalidGeographicalLocationSearchString = 5037,

        GeoTargetsAndBusinessTargetsMutuallyExclusive = 5038,

        IsLibraryTargetNotNull = 5039,

        [DbErrorCodeMapping(-100980)]
        TargetDeleted = 5040,

        [DbErrorCodeMapping(-102017)]
        AdGroupMediumInvalidWithBusinessTargets = 5041,

        TargetsBatchLimitExceeded = 5042,

        TargetingShouldBeExclusiveForThisTargetType = 5044,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        TargetsLocationBidBatchLimitExceeded = 5045,

        TargetsAgeBidBatchLimitExceeded = 5046,

        // Todo: XRG targets removal: remove
        TargetsSegmentBidBatchLimitExceeded = 5047,

        // Todo: XRG targets removal: remove
        TargetsBehaviorBidBatchLimitExceeded = 5048,

        TargetsDayBidBatchLimitExceeded = 5049,

        TargetsHourBidBatchLimitExceeded = 5050,

        TargetsGenderBidBatchLimitExceeded = 5051,

        // Target library
        [DbErrorCodeMapping(-102016)]
        CustomerIdRequired = 5052,

        [DbErrorCodeMapping(-101429)]
        TargetTypeAndVersionMismatch = 5053,

        [DbErrorCodeMapping(-102008)] // when deleting target from entity
        TargetNotAssociatedWithEntity = 5054,

        [DbErrorCodeMapping(-102006)] // When trying to assign adgroup-level target
        TargetAlreadyAssociatedWithEntity = 5055,

        [DbErrorCodeMapping(-102004)] // When trying delete library-target
        TargetHasActiveAssociations = 5056,

        [DbErrorCodeMapping(-102009)] // When trying to get target by id
        TargetDoesNotExist = 5057,

        BTTargettingNotEnabledForPilot = 5058,

        // Todo: XRG targets removal: remove
        InvalidBehaviorTarget = 5059,

        // Todo: XRG targets removal: remove
        DuplicateBehaviorTarget = 5060,

        // Todo: XRG targets removal: remove
        InvalidSegmentTarget = 5061,

        // Todo: XRG targets removal: remove
        DuplicateSegmentTarget = 5062,

        NegativeBiddingNotAllowedForThisTargetType = 5063,

        BiddingOtherThanZeroNotAllowedForThisTargetType = 5064,

        InvalidCashbackTextinSegmentTarget = 5065,

        InvalidParam1inSegmentTarget = 5066,

        InvalidParam2inSegmentTarget = 5067,

        InvalidParam3inSegmentTarget = 5068,

        InvalidSegmentParam1inSegmentTarget = 5069,

        InvalidSegmentParam2inSegmentTarget = 5070,

        // Todo: XRG targets removal: remove
        [DbErrorCodeMapping(-101444)]
        CannotSpecifySegmentTargetsWithAnyOtherTarget = 5071,

        InvalidCashbackAmountInSegmentTarget = 5072,

        [DbErrorCodeMapping(-101443)]
        BusinessAndRadiusTargetsAllowedOnlyForSearchMedium = 5073,

        [DbErrorCodeMapping(-102025)]
        AssociatingNonLibraryTargetNotAllowed = 5074,

        [DbErrorCodeMapping(-102024)] // User does not have permission to all entities that this TargetGroup is assigned. This error code has been added for sprocs prc_PublicTargetGroupUpdate and prc_PublicTargetGroupDelete.
        TargetGroupPermissionMismatch = 5075,

        AssociationStatusNotPassed = 5076,

        InvalidModifiedAfterDate = 5077,

        [DbErrorCodeMapping(-101113)]
        IncrementalBudgetAmountRequiredForDayTarget = 5078,

        [DbErrorCodeMapping(-102012)] // Mismatch CustomerId between Order and BusinessLocation
        [DbErrorCodeMapping(-102130)] // Business Location not found.
        [DbErrorCodeMapping(-101431)] // Invalid Business location id when adding BL target
        [DbErrorCodeMapping(-101433)] // Business location does not belong to customer
        InvalidBusinessLocationId = 5079,

        [DbErrorCodeMapping(-102015)]
        AdGroupGeoTargetLimitReached = 5080,

        AdModificationDisabledForOptimizationManagedAdGroup = 5081,

        AgeAndGenderExclusiveTargetingIsNotEnabledForCustomer = 5082,

        [DbErrorCodeMapping(-101425)]
        TargetDoesNotBelongToCustomer = 5083,

        TargetAssociationsNotPassed = 5084,

        TargetAssociationNull = 5085,

        TargetAssociationDucplicate = 5086,

        OSTargetingNotEnabledForPilot = 5087,

        PhysicalIntentNotEnabledForPilot = 5088,

        CustomerNotEnabledForLocationExclusion = 5089,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        LocationExclusionsBatchLimitExceeded = 5090,

        LocationExclusionsTargetAssociationsNotPassed = 5091,

        InvalidLocationExclusion = 5092,

        DuplicateExcludedGeoEntity = 5093,

        LocationExclusionNotPassed = 5094,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        ExcludedGeoEntitiesBatchLimitExceeded = 5095,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        ConflictWithLocationExclusion = 5096,

        ExcludedGeoEntityNotPassed = 5097,

        [DbErrorCodeMapping(-102147)]
        InvalidExclusionBitMaskPassed = 5098,

        LocationTargetVersionIsNotSuported = 5099,

        BusinessLocationTargetingIsNotSupported = 5100,

        InvalidTarget = 5101,

        InvalidDeviceTargetBidAdjustment = 5102,

        [ErrorField(EntityField.BidAdjustment)]
        InvalidLocationTargetBidAdjustment = 5103,

        InvalidAgeTargetBidAdjustment = 5104,

        InvalidGenderTargetBidAdjustment = 5105,

        InvalidDayTargetBidAdjustment = 5106,

        InvalidHourTargetBidAdjustment = 5107,

        InvalidSegmentedTargetBidAdjustment = 5108,

        BidAdjustmentNullPassedForAddTarget = 5109,



        [ErrorField(EntityField.Radius)]
        InvalidTargetRadiusUnit = 5110,

        InvalidDayTimeTarget = 5111,

        TargetsDayTimeBidBatchLimitExceeded = 5112,

        InvalidDayTimeTargetBidAdjustment = 5113,

        TargetDayTimeOverlapping = 5114,

        TargetDayTimeIntervalInvalid = 5115,

        CustomerNotEnabledForDayTimePilot = 5116,

        DuplicatePostalCodeTarget = 5117,

        PostalCodeNotSupportedForCustomer = 5118,

        TargetDayTimeAndDayHourAreExclusive = 5119,

        // Audience target error codes
        InvalidAudienceTarget = 5120,

        DuplicateAudienceTarget = 5121,

        TargetsAudienceBidBatchLimitExceeded = 5122,

        InvalidAudienceTargetBidAdjustment = 5123,

        CustomerNotEnableForNativeAdsPilot = 5125,

        [DbErrorCodeMapping(-120000)]
        InvalidNativeBidAdjustmentValue = 5126,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        ParentMappedTargetLocation = 5127,

        OSNameIsNotSupported = 5128,

        InvalidIntentOption = 5129,

        RelatedCriterionActionError = 5130, // Target Library

        InvalidCriterionId = 5131, // Target Library

        CriterionDoesNotMatch = 5132, // Target Library

        IncompleteDeviceCriterionSet = 5133,

        [DbErrorCodeMapping(-102033)]
        CampaignServiceInvalidTarget = 5134,

        LocationIntentCriterionCannotBeDeleted = 5135,

        ImportInvalidDeviceTargetBidAdjustment = 5136,

        InvalidGeolocationsFileVersion = 5137,

        InvalidLanguageLocale = 5138,

        BidsMustBeEqualForDeviceType = 5139,

        CustomerNotEnabledForCountyTargets = 5140, // Deprecated, feature GA

        RelatedCriterionTransactionError = 5141,

        ImportMismatchedAgeRangeTarget = 5142,

        ConflictWithExclusionTarget = 5143,

        InvalidGeolocationsFileFormat = 5144,

        InvalidDealIdTarget = 5145,

        UnsupportedBiddingSchemeForDeal = 5146,

        UnsupportedAgeTargetForDeal = 5147,

        UnsupportedGenderTargetForDeal = 5148,

        UnsupportedLocationTargetForDeal = 5149,

        UnsupportedDeviceTargetForDeal = 5150,

        AdgroupBidAmountLessThanDealAskPrice = 5151,

        InvalidAdQualityForDeal = 5152,

        VideoDurationIsInvalidForDeal = 5153,

        InvalidGenreTargetBidAdjustment = 5154,

        InvalidGenreIdTarget = 5155,

        AgeCriterionCannotBeEmptyForBrandAwarenessCampaign = 5156,

        GenderCriterionCannotBeEmptyForBrandAwarenessCampaign = 5157,

        VideoMimeTypeCannotResolve = 5168,

        UnsupportedDealFilter = 5169,

        UnsupportedDealSorter = 5170,

        InvalidBrandId = 5171,

        DeviceCriterionCannotBeDeletedForBrandAwarenessCampaign = 5172,

        JobAndCompanyIMACannotBeUsedByVideoAdsAndDisplay = 5173,

        UnsupportedAudienceTargetForDeal = 5174,

        ImpressionAudienceCannotBeUsedByDisplay = 5175,

        ImpressionAudienceCannotBeUsedByOLV = 5176,

        ImpressionAudienceCannotBeUsedByCTV = 5177,

        DealCampaignIsImmutable = 5178,

        EntityOnlySupportForDealCampaign = 5179,

        ShouldAssociateDealBeforeAddAdGroup = 5180,

        InvalidPlacementIdTarget = 5181,

        InvalidPlacementTargetBidAdjustment = 5182,

        NonAudienceCampaignImpressionCannotBeUsedByNonAudienceCampaign = 5183,

        InvalidTargetCombination = 5184,
        #endregion

        #region BusinessLocation between 6000 and 7000

        [DbErrorCodeMapping(-102011)]
        BusinessLocationNotSet = 6000,

        BusinessNameRequired = 6001,

        LattitudeLongitudeRequired = 6002,

        [DbErrorCodeMapping(-102112)]
        BusinessNameTooLong = 6003,

        [DbErrorCodeMapping(-102136)]
        DomainNameAlreadyTaken = 6004,

        BusinessDescriptionTooLong = 6006,

        InvalidBusinessTypeId = 6007,

        [DbErrorCodeMapping(-100996)]
        InvalidPaymentTypeId = 6008,

        InvalidBusinessHoursEntry = 6009,

        AddressRequired = 6010,

        AddressInvalid = 6011,

        AddressTooLong = 6012,

        CityNameRequired = 6013,

        CityNameTooLong = 6014,

        CountryCodeRequired = 6015,

        CountryCodeTooLong = 6016,

        StateOrProvinceRequired = 6017,

        StateOrProvinceTooLong = 6018,

        ZipOrPostalCodeTooLong = 6019,

        PhoneNumberInvalid = 6020,

        PhoneNumberTooLong = 6021,

        EmailInvalid = 6022,

        EmailTooLong = 6023,

        [DbErrorCodeMapping(-102139)]
        LocationIsNotSpecified = 6024,

        Open24HoursAndBusinessHoursMutuallyExclusive = 6025,

        BusinessNameAndAddressAlreadyExists = 6026, // TODO: what's the DB error code?

        BusinessLocationListTooLong = 6027,

        [DbErrorCodeMapping(-100953)]
        [DbErrorCodeMapping(-450019)]
        InvalidCustomerId = 6028,

        [DbErrorCodeMapping(-102143)]
        BusinessDomainAlreadySet = 6033,

        [DbErrorCodeMapping(-102142)]
        OnlyOneBusinessLocationPerCustomerIsAllowed = 6035,

        BusinessLocationsNotPassed = 6036,

        BusinessLocationHoursNotSet = 6037,

        BusinessLocationHoursInvalid = 6038,

        BusinessLocationBeginAndEndHoursMismatch = 6039,

        BusinessLocationHoursDuplicate = 6040,

        UnableToObtainLatLongInvalidAddress = 6041,

        UnableToObtainLatLongVirtualEarthError = 6042,

        [DbErrorCodeMapping(-101424)]
        [DbErrorCodeMapping(-102013)]
        UnableToDeleteAdAssociated = 6043,

        [DbErrorCodeMapping(-102144)]
        MaxBusinessLocationTargetsLimitReachedForCustomer = 6044,

        [DbErrorCodeMapping(-102145)]
        MaxBusinessLocationTargetsLimitReachedForAdGroup = 6045,

        [DbErrorCodeMapping(-102146)]
        MaxBusinessLocationTargetsLimitReachedForCampaign = 6046,

        DuplicatePaymentType = 6047,

        [DbErrorCodeMapping(-101445)]
        BusinessAddressShouldBeValidForUpdate = 6048,

        [DbErrorCodeMapping(-101446)]
        DuplicateBusinessLocation = 6049,

        [DbErrorCodeMapping(-101447)]
        MultipleDuplicateBusinessLocation = 6050,

        BusinessLocationReadOnlyForLocationAdExtensionV2Pilot = 6051,
        LocationExtensionV1ReadOnlyForLocationAdExtensionV2Pilot = 6052,
        PhoneExtensionV1ReadOnlyForCallAdExtensionV2Pilot = 6053,

        LatitudeOutOfRange = 6054,
        LongitudeOutOfRange = 6055,

        UnsupportedAdExtensionPropertyValue = 6056,
        #endregion

        #region Campaign sync between 7000 and 8000

        InvalidSyncDateRange = 7000,

        // CustomSegment - 8000 to 9000
        SegmentsNotPassed = 8000,

        [DbErrorCodeMapping(-202152)]
        [DbErrorCodeMapping(-101432)] // When adding segment target
        SegmentIdInvalid = 8001,

        SegmentOperationNotEnabledForPilot = 8002,

        SegmentInputExceeded = 8003,

        SegmentNameTooLong = 8004,

        [DbErrorCodeMapping(-102151)]
        SegmentAlreadyExists = 8005,

        SegmentNameMissing = 8006,

        SegmentNameHasInvalidChars = 8007,

        [DbErrorCodeMapping(-102152)]
        SegmentIdNotFound = 8008,

        [DbErrorCodeMapping(-101434)] // When adding segment target
        SegmentDoesNotBelongToCustomer = 8009,

        [DbErrorCodeMapping(-101439)]
        MaxSegmentsForCustomerHasBeenReached = 8010,

        [DbErrorCodeMapping(-102153)]
        SegmentAlreadyDeleted = 8011,

        [DbErrorCodeMapping(-101441)]
        SegmentNotAllowedForDistributionChannel = 8012,

        AdditionalSegmentNotAllowedWithVideoAdsSegmentation = 8013,

        AdditionalSegmentNotAllowedWithCountryOfSaleSegmentation = 8014,

        #endregion

        #region Asset between 9001 and 10000

        [DbErrorCodeMapping(-102120)] // When adding RAIS
        AssetIdInvalid = 9001,

        [DbErrorCodeMapping(-102119)] // When adding image ads
        AssetDoesNotBelongToCustomer = 9002,

        AssetDoesNotMatchCAMSchema = 9003,

        CAMSchemaIsNotSupported = 9004,

        AssetTypeIsNotSupported = 9005,

        ImageDataInvalid = 9006,

        ImageMimeTypeInvalid = 9007,

        ImageTooLarge = 9008,

        ImageOverweight = 9009,

        AnimatedImageNotAllowed = 9010,

        AssetIdsNotPassed = 9011,

        AssetEntitiesNotPassed = 9012,

        AssetEntityLimitExceeded = 9013,

        AssetIsNull = 9014,

        CAMSchemaNotPassed = 9015,

        AssetDataIsNull = 9016,

        AssetDataInvalid = 9017,

        MediaEnabledEntitiesInvalid = 9018,

        MediaTypeNotSupportedByOperation = 9019,

        AssetDataEncodingInvalid = 9020,

        AssetDataInvalidWidth = 9021,

        AssetDataInvalidHeight = 9022,

        AssetDataInvalidAspect = 9023,

        MediaFormatNotSupported = 9024,

        DuplicateAssetId = 9025,

        [DbErrorCodeMapping(-102127)] // When deleting media
        AssetDoesNotBelongToAccount = 9026,

        [DbErrorCodeMapping(-102128)] // When deleting media
        CamAssetCannotBeDeleted = 9027,

        [DbErrorCodeMapping(-102125)] // When deleting media
        AssetIsUsed = 9028,

        [DbErrorCodeMapping(-102126)] // When deleting media
        AssetIsAlreadyDeleted = 9029,


        OnlyAllowedToChangeAssetText = 9030,

        EntitiesNotPassed = 9031,

        EntityLimitExceeded = 9032,

        InvalidStockImageId = 9044,

        InvalidDuration = 9045,
        VideoOverweight = 9046,
        DuplicateVideo = 9047,
        VideoTranscodingError = 9048,
        AzureMediaServiceError = 9049,
        VideoBlobStorageError = 9050,
        VideoDownloadError = 9051,
        VideoSourceIsNull = 9052,
        VideoUrlDataIsNull = 9053,
        VideoDescriptionDataIsNull = 9054,
        VideoUrlTextTooLong = 9055,
        VideoDescriptionTextTooLong = 9056,
        VideoSourceLimitExceeded = 9057,

        AssetFieldMinimumNotMet = 9058,
        AssetFieldLimitExceeded = 9059,
        DuplicateVideoResource = 9060,
        InvalidVideoResource = 9061,
        InvalidVideoEditingParam = 9062,

        [DbErrorCodeMapping(-101029)]
        VideoLimitExceededPerAccount = 9063,

        InvalidVideoStatusForThisOperation = 9064,
        AccountNotEnabledForVideoRepair = 9065,

        VideoDownloadNotAllowed = 9066,

        AssetCropSettingInvalid = 9067,
        AssetPlacementSettingInvalid = 9068,

        [DbErrorCodeMapping(-101030)]
        AssetHasCropTaskInProgress = 9069,

        MediaLibraryNotEnabledForPilot = 9070,

        SystemGeneratedAssetNotAllowed = 9071,
        ImageDoesntMeetMinPixelRequirements = 9072,
        ImageDoesntFitAspectRatio = 9073,
        InvalidFileExtension = 9074,

        VideoScanTypeNotSupported = 9075,
        InvalidFrameRate = 9076,
        OnlySingleTrackSupported = 9077,
        VideoAudioDurationMustMatch = 9078,
        ChromaSubsamplingNotSupported = 9079,
        UnsupportedNumberOfAudioChannels = 9080,
        VideoProfileNotSupported = 9081,
        InvalidAudioBitRate = 9082,
        UnsupportedAudioBitDepth = 9083,
        UnsupportedAudioSampleRate = 9084,
        UnsupportedFrameRateMode = 9085,

        AssetDescriptionTooLong = 9086,
        VideoMetadataSaveToBlobFailed = 9087,

        FreeStockImageDownloadNotAllowed = 9088,
        ImageAIUpScaleFailed = 9089,
        InvalidROIForAsset = 9090,

        //Video Asset
        InvalidVideoAsset = 9091,
        DuplicateVideoAsset = 9092,
        VideoInvalidStatus = 9093,
        VideoWidthTooSmall = 9094,
        VideoHeightTooSmall = 9095,
        VideoInvalidAspectRatio = 9096,
        VideoInvalidDuration = 9097,
        VideoBitRateTooSmall = 9098,
        VideoSourceLengthTooLarge = 9099,
        VideoUnsupportedFileFormat = 9100,
        VideoAsAssetNotEnabledForAccount = 9101,

        #endregion

        #region SitePlacement between 10001 and 11000

        SitePlacementsNotPassed = 10001,

        InvalidBidForSitePlacement = 10002,

        SitePlacementBidExceedsCeilingPrice = 10003,

        SitePlacementUpdateEmpty = 10004,

        CannotAddSiteToAdGroupTypeMismatch = 10005,

        SitePlacementIdInvalid = 10006,

        InvalidSitePlacementPath = 10007,

        InvalidSiteId = 10008,

        SitePlacementAlreadyExists = 10009,

        SitePlacementIdDoesNotBelongToAdGroupId = 10010,

        SitePlacementIsDeleted = 10011,

        SitePlacementInInvalidStatus = 10012,

        DuplicateSitePlacement = 10013,

        InvalidPlacementId = 10014,

        DuplicateUrl = 10015,

        InvalidUrl = 10016,

        UncrawlableUrl = 10018,

        #endregion

        #region ConvertCurrency between 11001 and 12000

        CurrencyIdInvalid = 11019,

        PricesNotPassed = 11020,

        InvalidPrice = 11021,

        #endregion

        // CAM MT errors between 13001 and 14000
        // used to avoid collisions between error codes in CAM MT and CM MT
        #region OrderItem between 15000 and 16000

        OrderItemsNotPassed = 15000,

        CannotMixOrderItemsInInputArray = 15001,

        [DbErrorCodeMapping(-600003)]
        OrderItemIdInvalid = 15002,

        InvalidOrderItem = 15003,

        [DbErrorCodeMapping(-100287)]
        MultipleOrderItemTypesInCollection = 15004, // Invalid combination of BidTargetTypeIds

        #endregion

        #region NetworkNode related between 16000 and 17000

        DuplicateNetwork = 16000,

        InvalidBidForNetwork = 16001,

        NetworkBidExceedsCeilingPrice = 16002,

        NetworkUpdateEmpty = 16003,

        DistributionChannelsNotPassed = 16004,

        CouldNotLookupIdForDistributionChannel = 16005,

        NodeIdsNotPassed = 16006,

        NodeIdsListExceedsLimit = 16007,

        NodeIdsHasDuplicates = 16008,

        CannotAddNetworkToAdGroupTypeMismatch = 16009,

        #endregion

        InvalidSearchTerm = 16010,

        #region Import related between 17001 and 18000
        InvalidImportFilePath = 17001,

        InvalidImportId = 17002,

        ImportAlreadyInProgressForAccount = 17003,

        [DbErrorCodeMapping(-101115)]
        ImportIdInvalidForAccount = 17004,

        [ErrorField(EntityField.ImportEntityStatus)]
        ImportEntityStatusNotAllowed = 17005,

        [ErrorField(EntityField.ImportEntityFilter)]
        ImportEntityFilterInvalid = 17006,

        ImportValidParentNotFound = 17007,

        [ErrorField(EntityField.CampaignTimeZone)]
        ImportCampaignTimeZoneNotSpecified = 17008,

        [ErrorField(EntityField.CampaignTimeZone)]
        ImportCampaignUnknownTimeZone = 17009,

        [ErrorField(EntityField.CampaignMonthlyBudgetAmount)]
        ImportCampaignMonthlyBudgetNotSpecified = 17010,

        [ErrorField(EntityField.AdType)]
        ImportAdTypeInvalid = 17011,

        ImportInvalidFileFormat = 17012,

        ImportCampaignIsNull = 17013,

        ImportAdgroupIsNull = 17014,

        ImportTotalRowsExceedLimit = 17015,

        [DbErrorCodeMapping(-102028)]
        ImportCampaignTargetAlreadyExists = 17016,

        ImportStatusInvalid = 17017,

        [ErrorField(EntityField.LocationTargetUniqueName)]
        ImportLocationTooLong = 17018,
        ImportUnknownConversionTracking = 17019,

        [ErrorField(EntityField.CampaignBudgetType)]
        ImportUnknownBudgetType = 17020,

        [ErrorField(EntityField.AdGroupPricingModel)]
        ImportNotSupportedAdGroupColumnPlacementMaxCpc = 17021,

        [ErrorField(EntityField.AdGroupPricingModel)]
        ImportNotSupportedAdGroupCpmPricingModel = 17022,

        [ErrorField(EntityField.KeywordMatchType)]
        ImportUnknownMatchType = 17023,

        [ErrorField(EntityField.KeywordMatchType)]
        ImportMatchTypeRequiredOnKeywordDelete = 17024,

        ImportTextAdRequiredDataMissing = 17025,

        [ErrorField(EntityField.AdGroupLanguage)]
        ImportLanguageTooLong = 17026,

        [ErrorField(EntityField.AdGroupPublisherCountries)]
        ImportPublisherCountriesTooLong = 17027,

        ImportInvalidColumnMapping = 17028,

        [ErrorField(EntityField.SiteLinkText)]
        ImportSiteLinkMissingText = 17029,

        [ErrorField(EntityField.SiteLinkDestinationUrl)]
        ImportSiteLinkMissingDestinationUrl = 17030,

        [ErrorField(EntityField.SiteLinkText)]
        ImportSiteLinkTextTooLong = 17031,

        [ErrorField(EntityField.SiteLinkDestinationUrl)]
        ImportSiteLinkDestinationUrlTooLong = 17032,

        ImportDuplicateColumnMapping = 17033,

        ImportLocationExtensionMapIconNameTooLong = 17046,

        ImportCampaignInvalidSalesCountry = 17047,

        ImportCampaignInvalidProductSalesChannel = 17048,

        ImportOtherProductGroupWithinAdGroupHasError = 17049,

        ImportPluginTimeOut = 17050,

        ImportPluginThrowExceptionDuringProcess = 17051,

        [DbErrorCodeMapping(-101114)]
        ImportPluginAnotherImportIsRunning = 17052,

        ImportInvalidExcelFileFormatHavingMultipleWorkSheets = 17053,

        ImportPluginImportIdIsNull = 17054,

        ImportPluginImportIsCanceledByUser = 17055,

        ImportPluginImportIsCanceledByOtherReason = 17056,

        FileImportCommitTaskInitialImportStatusIsNotExpected = 17057,

        ImportEmptyAccountName = 17058,

        ImportEntityDemandLimitReached = 17059,

        ImportPluginImportServiceCallTimeOut = 17060,

        ImportFailedPreserveImportStatus = 17061,

        ImportSubmitAlreadyInProgress = 17062,

        MITaskTimeOut = 17063,

        NoImportColumnIsMapped = 17064,

        FileImportTimeOut = 17065,

        ImportTimedOutDuringBulkUpload = 17066,

        ImportNotSupportedShoppingShowcaseAdGroupType = 17067,

        ImportShoppingCampaignHaveNoSetting = 17068,

        ImportShoppingCampaignPriorityInvalid = 17069,

        UnSupportSmartShoppingCampaignPriorityAndChangeToHighPriority = 17070,

        AmazonImportInvalidCredentials = 17101,

        ProductPartitionExcludedDueToNoActiveAds = 17102,

        NoGtinMappingFound = 17103,

        // User Preference Errors 17200-17250
        CampaignSkippedDueToUserPreference = 17200,
        AdsSkippedDueToUserPreference = 17201,

        BingPlacesImportUserError = 17801,

        BingPlacesImportOtherError = 17802,

        BingPlacesInvalidCredentials = 17803,

        GoogleMyBusinessImportUserError = 17900,

        GoogleMyBusinessTransientError = 17901,

        GoogleMyBusinessUnknownError = 17902,

        InvalidImportJobId = 17903,

        ImportJobNameEmpty = 17904,

        ImportFromAccountIdInvalid = 17905,

        ImportJobCannotUpdateFromApi = 17906,

        PinterestImportInvalidCredentials = 17907,

        OneOrMoreGooglePlaceIdsCouldNotBeResolved = 17908,

        AccountNotEligibleForMSABingPlacesIntegration = 17909,

        BingPlacesAPIInternalError = 17910,

        BingPlacesMSAInvalidServiceTokenError = 17911,

        BingPlacesMSAInvalidUserTokenError = 17912,

        BingPlacesMSAAPIInvalidUserInputError = 17913,

        BingPlacesMSABusinessListingForSearchQueryCouldNotBeFound = 17914,

        BingPlacesMSAInternalError = 17915,

        BingPlacesMSAInvalidAccessTokenError = 17916,

        BingPlacesMSAInvalidAccountPropertyError = 17917,

        BingPlacesMSACreateBusinessFailed = 17918,

        BingPlacesMSAClaimBusinessFailed = 17919,

        BingPlacesMSABusinessNameNullOrEmpty = 17920,

        BingPlacesMSAAddressLine1NullOrEmpty = 17921,

        BingPlacesMSACityNameNullOrEmpty = 17922,

        BingPlacesMSAZipCodeNullOrEmpty = 17923,

        BingPlacesMSAStateIdNullOrEmpty = 17924,

        BingPlacesMSACountryCodeNullOrEmpty = 17925,

        BingPlacesMSAYpidNullOrEmpty = 17926,

        BingPlacesMSAMarketNullOrEmpty = 17927,

        #endregion

        #region miscellaneous
        [DbErrorCodeMapping(-100761)] // 'Number of items exceeds maximum requested'
        TooMuchDataToDownload = 18000,
        TooMuchKeywordDataToDownload = 18001,
        TooMuchAudienceDataToDownload = 18002,
        TooMuchExcelDataToDownload = 18003,
        TooMuchDataForInlineDownload = 18004,
        TooMuchSearchTermDataToDownload = 18005,
        #endregion

        #region Analytics error codes between 19001 - 20000
        // Goals
        GoalNotPassed = 19001,
        GoalsListEmpty = 19002,
        GoalNameMissing = 19003,
        GoalNameTooLong = 19004,
        [DbErrorCodeMapping(-102160)]
        GoalNameAlreadyExists = 19005,
        GoalNameHasInvalidChars = 19006,
        DaysApplicableForConversionIsRequired = 19007,
        GoalIdIsRequired = 19008,
        [DbErrorCodeMapping(-102164)]
        GoalIdIsInvalid = 19009,
        GoalIdMustBeNull = 19010,
        GoalIdAlreadyExists = 19011,
        GoalCostModelCannotbeCombinedWithNone = 19012,
        GoalRevenueAmountRequiredForConstantModel = 19013,
        GoalRevenueAmountMustBeNullForVariableModel = 19014,
        GoalRevenueAmountMustBeNullForNullModel = 19015,
        GoalRevenueAmountValidOnlyForConstantModel = 19016,
        GoalRevenueModelIsRequired = 19018,
        GoalCostModelIsRequired = 19019,
        [DbErrorCodeMapping(-102161)]
        MaxGoalsLimitExceededForAccount = 19020,
        GoalRevenueAmountLessThanMinimum = 19021,
        GoalRevenueAmountMoreThanMax = 19022,

        // Stages
        StageNotPassed = 19100,
        StageNameMissing = 19101,
        StageNameTooLong = 19102,
        StageNameAlreadyExists = 19103,
        StageNameHasInvalidChars = 19104,
        StageIdIsRequired = 19105,
        StageIdMustBeNull = 19106,
        StageIdAlreadyExists = 19107,
        StageTypeIsRequired = 19108,
        StageTypeMustBeNull = 19109,
        ActionIdMustBeNull = 19110,
        PositionNumberIsRequired = 19111,
        PositionNumberMustBeNull = 19112,
        PositionNumberAlreadyExists = 19113,
        [DbErrorCodeMapping(-101162)]
        MaxStagesLimitExceededForGoal = 19114,
        AtleaseOneStageRequiredForGoal = 19115,
        AtleaseOneConversionStageRequiredForGoal = 19116,
        OnlyOneLeadStageAllowedForGoal = 19117,
        OnlyOneConversionStageAllowedForGoal = 19118,

        // Dimensions
        DimensionNotPassed = 19200,
        DimensionsListEmpty = 19201,
        DimensionNameMissing = 19202,
        DimensionNameTooLong = 19203,
        DimensionNameAlreadyExists = 19204,
        [DbErrorCodeMapping(-102180)]
        DimensionTypeAlreadyExists = 19205,
        DimensionNameHasInvalidChars = 19206,
        DimensionNameMustBeNull = 19207,
        DimensionKeyMustBeNull = 19208,
        DimensionKeyTooLong = 19209,
        [DbErrorCodeMapping(-102184)]
        DimensionKeyAlreadyExists = 19210,
        DimensionKeyHasInvalidChars = 19211,
        DimensionIdIsRequired = 19212,
        DimensionIdIsInvalid = 19213,
        DimensionIdMustBeNull = 19214,
        DimensionIdAlreadyExists = 19215,
        DimensionTypeCannotBeUpdated = 19216,
        MaxDimensionsLimitExceededForAccount = 19217,
        DimensionKeyMissing = 19218,

        // Dimension Value
        DimensionValueNotPassed = 19300,
        DimensionValueNameMissing = 19301,
        DimensionValueNameTooLong = 19302,
        [DbErrorCodeMapping(-102185)]
        DimensionValueNameAlreadyExists = 19303,
        DimensionValueNameHasInvalidChars = 19304,
        DimensionValueKeyMustBeNull = 19305,
        DimensionValueKeyTooLong = 19306,
        [DbErrorCodeMapping(-102186)]
        DimensionValueKeyAlreadyExists = 19307,
        DimensionValueKeyHasInvalidChars = 19308,
        DimensionValueKeyEmptyNotAllowed = 19309,
        DimensionValueIdIsRequired = 19310,
        DimensionValueIdMustBeNull = 19311,
        DimensionValueIdAlreadyExists = 19312,
        DimensionValuesListEmpty = 19313,
        [DbErrorCodeMapping(-102181)]
        MaxDimensionValuesLimitExceededForDimension = 19315,
        AtleaseOneDimensionValueRequiredForDimension = 19316,

        AnalyticsSettingCampaignLevelDeprecated = 19400,
        #endregion

        #region delivery parameters 20-21K
        FilterScoreNotPassed = 20000,
        [DbErrorCodeMapping(-102029)]
        MoreRecentVersionExists,
        #endregion

        #region BulkApi error codes between 21001 and 22000
        AccountsAndCampaignsMissing = 21001,
        AccountsAndCampaignsLengthExceeded = 21003,
        CampaignsContainsNullScope = 21004,
        InvalidDownloadRequestId = 21005,
        BulkApiNotEnabledForPilot = 21006,
        AccountTooBigToDownload = 21007,
        LastSyncTimeTooOld = 21008,
        CampaignsTooBigToDownload = 21009,
        DataScopeInvalid = 21010,
        #endregion

        #region Aggregator error codes between 22001 and 23000
        CacheIdNotFound = 22001,
        InvalidBudgetChangeSettings = 22002,
        FilterIdsIsEmpty = 22003,
        FiltersListIsEmpty = 22004,
        FilterHasNoExpressions = 22005,
        FilterHasInvalidExpressions = 22006,
        FilterIsNull = 22007,
        FilterExpressionsExceedsMaxCount = 22008,
        FilterExpressionsHasInvalidValueType = 22009,

        [DbErrorCodeMapping(-102510)]
        [DbErrorCodeMapping(-100882)] // Filter Limit Reached for User & AccountId
        FiltersExceedsMaxCount = 22010,

        [DbErrorCodeMapping(-100883)] // Filter name exists
        FiltersNameAlreadyExists = 22011,

        FiltersColumnIsNotFilterableInMetadata = 22012,
        FiltersNameIsEmpty = 22013,
        FilterOperationInvalidForDataType = 22014,

        InvalidOrderItemBids = 22015,

        InvalidPaginationLimits = 22016,
        InvalidCultureInfoLCID = 22017,

        FiltersNameLengthExceeded = 22018,
        FilterExpressionsValueLengthExceeded = 22019,
        UnsupportedLanguage = 22020,
        LanguageMismatchUserAd = 22021,

        MSANAWFMetricsPilotNotEnabled = 22022,

        AIGCException = 22023,

        VideoAdsMetricsPilotNotEnabled = 22024,

        PMaxMetricsNotEnabled = 22025,
        #endregion

        #region AdIntelligence error codes between 30000 and 31000
        KeywordCollectionExceededLimit = 30001,
        UrlNotPassed = 30002,
        MaxKeywordsOutputExceededLimit = 30003,
        LanguageNotSupported = 30004,
        InvalidMinConfidenceScore = 30005,

        KSPTimeoutError = 30006,
        KSPCommunicationError = 30007,
        KSPProviderNotFound = 30008,
        KSPProviderError = 30009,
        InvalidMaxKeywordsOutput = 30010,
        KspStartDateNull = 30011,
        KspEndDateNull = 30012,
        KspStartDateBiggerThanEndDate = 30013,

        InvalidMaxSuggestionsPerKeyword = 30020,
        MaxSuggestionsPerKeywordExceededLimit = 30021,
        LanguageAndCountryNotSupported = 30022,
        MatchTypesNotPassed = 30023,
        PublisherCountriesCollectionSizeExceedsLimit = 30024,
        InvalidStartDateFormat = 30025,
        StartDateBiggerThanCurrentDate = 30026,
        InvalidEndDateFormat = 30027,
        EndDateBiggerThanCurrentDate = 30028,

        SortExpressionColumnIsNotSupported = 30061,

        #endregion

        #region FraudCallback error codes between 32000 and 33000

        [DbErrorCodeMapping(-104100)]
        [ErrorField(EntityField.AdGroupId)]
        InvalidOrderId = 32001,

        [DbErrorCodeMapping(-104101)]
        HoldAlreadyReleased = 32002,

        [DbErrorCodeMapping(-100760)]
        HoldTimeRequired = 32003,

        FraudEvaluationResultsIsNullOrEmpty = 32004,
        UpdateFraudStatusRequestItemsIsNullOrEmpty = 32005,

        #endregion

        #region RichAdsEdit error codes between 33000 and 34000

        AssetAdsRelationshipsIsNullOrEmpty = 33001,

        AssetIdsIsNullOrEmpty = 33002,

        #endregion

        #region DomainData

        InvalidVersionToken = 34001,
        UnsupportedLcid = 34002,

        #endregion

        #region db load balancer 34201 to 34300
        [DbErrorCodeMapping(-101456)]
        [DbErrorCodeMapping(-103707)]
        WrongPartitionId = 34201,
        DbMigrationLockOperationError = 34202,

        [DbErrorCodeMapping(-101462)]
        InvalidMigrationStatusPassed = 34203,

        #endregion

        #region Ad extension errors between 35001 and 36000
        [DbErrorCodeMapping(-101016)]
        InvalidAdExtensionStatus = 35001,
        InvalidAdExtensionType = 35002,
        AdExtensionSiteLinkArrayIsNullOrEmpty = 35003,
        AdExtensionSiteLinkIsNull = 35004,

        [ErrorField(EntityField.SiteLinkDestinationUrl)]
        SiteLinkDestinationUrlNullOrEmpty = 35005,

        [ErrorField(EntityField.SiteLinkText)]
        SiteLinkDisplayTextNullOrEmpty = 35006,

        [ErrorField(EntityField.SiteLinkDestinationUrl)]
        SiteLinkDestinationUrlTooLong = 35007,

        [ErrorField(EntityField.SiteLinkText)]
        SiteLinkDisplayTextTooLong = 35008,

        [DbErrorCodeMapping(-101015)]
        TooManyAdExtensionsPerAccount = 35009,
        TooManySiteLinks = 35010,

        AdExtensionsNullOrEmpty = 35011,
        AdExtensionIsNull = 35012,
        AdExtensionsEntityLimitExceeded = 35013,

        [ErrorField(EntityField.SiteLinkDestinationUrl)]
        SiteLinkDestinationUrlInvalid = 35014,
        SiteLinkAdExtensionPilotNotEnabledForCustomer = 35015,
        DuplicateSiteLink = 35016,

        [DbErrorCodeMapping(-101013)]
        [DbErrorCodeMapping(-101009)]
        AdExtensionIdInvalid = 34016,
        AdExtensionIdToCampaignIdAssociationsNotPassed = 35017,
        AdExtensionIdToCampaignIdAssociationNotPassed = 35018,
        AdExtensionIdsNotPassed = 35019,

        [DbErrorCodeMapping(-101014)]
        CannotAssignMoreThanOneAdExtensionTypeToAnEntity = 35020,

        [ErrorField(EntityField.SiteLinkText)]
        SiteLinkDisplayTextInvalid = 35021,

        [DbErrorCodeMapping(-101017)]
        InvalidAdExtensionAssociation = 35022,
        DuplicateAdExtensionIdToCampaignIdAssociation = 35023,
        DuplicateAdExtensionId = 35024,

        AdExtensionAddressIsNull = 35025,

        [ErrorField(EntityField.AdExtensionStreetAddress)]
        AdExtensionStreetAddressNullOrEmpty = 35026,

        [ErrorField(EntityField.AdExtensionStreetAddress)]
        AdExtensionStreetAddressTooLong = 35027,

        [ErrorField(EntityField.AdExtensionStreetAddress)]
        AdExtensionStreetAddressInvalid = 35028,

        [ErrorField(EntityField.AdExtensionStreetAddress2)]
        AdExtensionStreetAddress2TooLong = 35029,

        [ErrorField(EntityField.AdExtensionStreetAddress2)]
        AdExtensionStreetAddress2Invalid = 35030,

        [ErrorField(EntityField.AdExtensionCityName)]
        AdExtensionCityNameNullOrEmpty = 35031,

        [ErrorField(EntityField.AdExtensionCityName)]
        AdExtensionCityNameTooLong = 35032,

        [ErrorField(EntityField.AdExtensionCityName)]
        AdExtensionCityNameInvalid = 35033,

        [ErrorField(EntityField.AdExtensionProvinceName)]
        AdExtensionProvinceNameTooLong = 35034,

        [ErrorField(EntityField.AdExtensionProvinceName)]
        AdExtensionProvinceNameInvalid = 35035,

        [ErrorField(EntityField.AdExtensionPostalCode)]
        AdExtensionPostalCodeNullOrEmpty = 35036,

        [ErrorField(EntityField.AdExtensionPostalCode)]
        AdExtensionPostalCodeTooLong = 35037,

        [ErrorField(EntityField.AdExtensionPostalCode)]
        AdExtensionPostalCodeInvalid = 35038,

        [ErrorField(EntityField.AdExtensionCountryCode)]
        AdExtensionCountryCodeNull = 35039,

        [ErrorField(EntityField.AdExtensionCountryCode)]
        AdExtensionCountryCodeWrongLength = 35040,

        [ErrorField(EntityField.AdExtensionCountryCode)]
        AdExtensionCountryCodeInvalid = 35041,

        [ErrorField(EntityField.AdExtensionGeoPoint)]
        AdExtensionGeoPointIsNotNull = 35042,

        [ErrorField(EntityField.AdExtensionGeoCodeStatus)]
        AdExtensionGeoCodeStatusIsNotNull = 35043,

        [ErrorField(EntityField.AdExtensionCompanyName)]
        AdExtensionCompanyNameNullOrEmpty = 35044,

        [ErrorField(EntityField.AdExtensionCompanyName)]
        AdExtensionCompanyNameTooLong = 35045,

        [ErrorField(EntityField.AdExtensionCompanyName)]
        AdExtensionCompanyNameInvalid = 35046,

        [ErrorField(EntityField.AdExtensionPhoneNumber)]
        AdExtensionPhoneNumberTooLong = 35047,

        [ErrorField(EntityField.AdExtensionPhoneNumber)]
        AdExtensionPhoneNumberInvalid = 35048,

        [ErrorField(EntityField.AdExtensionIconMediaId)]
        AdExtensionIconMediaIdInvalid = 35049,

        [ErrorField(EntityField.AdExtensionIconMediaId)]
        AdExtensionIconSizeTooLarge = 35050,

        [ErrorField(EntityField.AdExtensionImageMediaId)]
        AdExtensionImageMediaIdInvalid = 35051,

        [ErrorField(EntityField.AdExtensionImageMediaId)]
        AdExtensionImageSizeTooLarge = 35052,

        LocationAdExtensionPilotNotEnabledForCustomer = 35053,

        CallAdExtensionPilotNotEnabledForCustomer = 35054,

        [ErrorField(EntityField.AdExtensionProvinceCode)]
        AdExtensionProvinceCodeTooLong = 35055,

        [ErrorField(EntityField.AdExtensionProvinceCode)]
        AdExtensionProvinceCodeInvalid = 35056,

        [ErrorField(EntityField.AdExtensionProvinceCode)]
        AdExtensionProvinceCodeRequiredIfProvinceNameEmpty = 35057,

        [ErrorField(EntityField.AdExtensionIconMediaUrl)]
        AdExtensionIconMediaUrlIsNotNull = 35058,

        [ErrorField(EntityField.AdExtensionImageMediaUrl)]
        AdExtensionImageMediaUrlIsNotNull = 35059,

        [ErrorField(EntityField.AdExtensionPhoneNumber)]
        AdExtensionPhoneNumberNullOrEmpty = 35060,

        LocationAdExtensionsEntityLimitExceeded = 35061,
        AdExtensionTypeMismatch = 35062,
        ProductAdExtensionPilotNotEnabledForCustomer = 35063,
        [ErrorField(EntityField.AdExtensionProductConditionCollection)]
        ProductAdExtensionTooManyProductConditionCollections = 35064,
        ProductAdExtensionTooManyConditions = 35065,
        [ErrorField(EntityField.AdExtensionProductConditionCollection)]
        ProductAdExtensionDuplicateProductFilter = 35066,
        [ErrorField(EntityField.ProductCondition)]
        ProductAdExtensionDuplicateProductCondition = 35067,
        [ErrorField(EntityField.AdExtensionStoreId)]
        ProductAdExtensionInvalidProductCollectionId = 35068,
        [ErrorField(EntityField.ProductCondition)]
        ProductAdExtensionProductConditionsArrayIsNullOrEmpty = 35070,
        [ErrorField(EntityField.ProductCondition)]
        ProductAdExtensionProductConditionIsNull = 35071,
        [ErrorField(EntityField.AdExtensionProductConditionCollection)]
        ProductAdExtensionProductConditionCollectionIsNull = 35072,
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionOperandInvalid = 35073,
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionAttributeIsInvalid = 35074,
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionAttributeTooLong = 35075,

        // maps to API code ProductAdExtensionAttributeIsInvalid
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionAttributeInvalid = 35076,
        [ErrorField(EntityField.ProductCondition)]
        AdGroupCriterionTooManyConditions = 35077,
        ExtensionProductSelectionIsNull = 35078,
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionDuplicateOperand = 35079,
        InvalidAdExtensionTypeFilter = 35080,
        CallAdExtensionRequireTollFreeTrackingNumberMustBeNullWhenTrackingNotEnabled = 35081,
        ProductCollectionNameCannotBeSet = 35082,
        AdGroupCriterionEditorialStatusCannotBeSet = 35083,
        [ErrorField(EntityField.AdGroupCriterionDestinationUrl)]
        ProductTargetDestinationUrlTooLong = 35084,
        ProductValueTooLong = 35085,
        [ErrorField(EntityField.AdGroupCriterionParam1)]
        ProductTargetParam1TooLong = 35086,
        [ErrorField(EntityField.AdGroupCriterionParam2)]
        ProductTargetParam2TooLong = 35087,
        [ErrorField(EntityField.AdGroupCriterionParam3)]
        ProductTargetParam3TooLong = 35088,

        [ErrorField(EntityField.AdExtensionGeoPoint)]
        AdExtensionGeoPointInvalid = 35089,

        [ErrorField(EntityField.AdExtensionCountryCode)]
        CallAdExtensionCallTrackingNotSupportedForCountry = 35090,
        ProductAdExtensionCannotHaveBothAllAndSpecificProducts = 35091,
        MultipleProductAdExtensionCannotBeAssignedToSingleCampaign = 35092,
        [ErrorField(EntityField.AdExtensionName)]
        ProductAdExtensionNameTooLong = 35093,
        [ErrorField(EntityField.AdExtensionName)]
        ProductAdExtensionNameInvalid = 35094,

        InvalidAssociationType = 35095,
        AdExtensionIdToAdGroupIdAssociationsNotPassed = 35096,
        AdExtensionIdToAdGroupIdAssociationNotPassed = 35097,
        DuplicateAdExtensionIdToAdGroupIdAssociation = 35098,
        InvalidAdExtensionTypeForAdGroupAssociation = 35099,
        InvalidAdExtensionTypeForAccountAssociation = 35100,
        InvalidAdExtensionTypeForBIData = 35103,
        InvalidAdExtensionTypeForSubAccountOrHotelGroupAssociationsBySubAccount = 35104,
        InvalidAdExtensionTypeForHotelGroupAssociationsByHotelGroup = 35105,

        EnhancedSiteLinkAdExtensionPilotNotEnabledForCustomer = 35120,

        [ErrorField(EntityField.SiteLinkDescription1)]
        SiteLinkDescription1TooLong = 35121,
        SiteLinkDescription1Invalid = 35122,
        [ErrorField(EntityField.SiteLinkDescription2)]
        SiteLinkDescription2TooLong = 35124,
        SiteLinkDescription2Invalid = 35125,
        [ErrorField(EntityField.SiteLinkDevicePreference)]
        SiteLinkDevicePreferenceInvalid = 35126,

        SiteLinkDescriptionAllOrNoneRequired = 35127,

        // esldcr : pilot,error
        AdExtensionSchedulingPilotNotEnabledForCustomer = 35140,
        AdExtensionScheduleInvalidStartTime = 35141,
        AdExtensionScheduleInvalidEndTime = 35142,
        InvalidScheduleDayTimeRange = 35143,
        ScheduleDayTimeRangesDayBatchLimitExceeded = 35144,
        ScheduleDayTimeRangesOverlapping = 35145,
        InvalidScheduleDayTimeRangeInterval = 35146,
        AdExtensionScheduleInvalidUseSearcherTimeZone = 35147,
        ScheduleDaysNotInDateRange = 35148,

        ImageAdExtensionPilotNotEnabledForCustomer = 35300,
        CampaignImageAdExtensionPilotNotEnabledForCustomer = 35301,
        ImageAdExtensionV2PilotNotEnabledForCustomer = 35302,

        [ErrorField(EntityField.ImageExtensionAlternativeText)]
        ImageAdExtensionAlternativeTextNullOrEmpty = 35304,
        ImageAdExtensionAlternativeTextInvalid = 35305,
        ImageAdExtensionAlternativeTextTooLong = 35306,

        [ErrorField(EntityField.ImageExtensionDestinationUrl)]
        ImageAdExtensionDestinationUrlInvalid = 35307,
        ImageAdExtensionDestinationUrlTooLong = 35308,

        [ErrorField(EntityField.ImageExtensionImageMediaId)]
        ImageAdExtensionImageMediaIdInvalid = 35309,

        [ErrorField(EntityField.ImageExtensionDescription)]
        ImageAdExtensionDescriptionInvalid = 35310,

        [ErrorField(EntityField.ImageExtensionDescription)]
        ImageAdExtensionDescriptionTooLong = 35311,

        [ErrorField(EntityField.ImageExtensionImageMediaIds)]
        ImageAdExtensionTooManyImages = 35312,

        [ErrorField(EntityField.ImageExtensionImageMediaIds)]
        ImageAdExtensionImageMediaIdsNullOrEmpty = 35313,

        [ErrorField(EntityField.ImageExtensionDisplayText)]
        ImageAdExtensionDisplayTextInvalid = 35316,
        ImageAdExtensionDisplayTextTooLong = 35317,
        ImageAdExtensionDisplayTextNullOrEmpty = 35318,

        [DbErrorCodeMapping(-101019)]
        AdExtensionAssociationsLimitExceededPerEntityType = 3967,

        [DbErrorCodeMapping(-101021)]
        AssociationsLimitExceededPerAdExtensionType = 3969,

        [DbErrorCodeMapping(-101022)]
        ImagesLimitExceededPerAccount = 4028,

        [DbErrorCodeMapping(-101023)]
        AssociationsLimitExceededPerAdExtensionTypeForEntity = 3970,

        [DbErrorCodeMapping(-101024)]
        StockImageLimitExceededPerCustomer = 4029,

        AppAdExtensionPilotNotEnabledForCustomer = 35350,

        [ErrorField(EntityField.AppPlatform)]
        AppAdExtensionAppPlatformNullOrEmpty = 35351,
        [ErrorField(EntityField.AppPlatform)]
        AppAdExtensionInvalidAppPlatform = 35352,

        [ErrorField(EntityField.AppStoreId)]
        AppAdExtensionAppStoreIdNullOrEmpty = 35353,
        [ErrorField(EntityField.AppStoreId)]
        AppAdExtensionAppStoreIdTooLong = 35354,
        [ErrorField(EntityField.AppStoreId)]
        AppAdExtensionAppStoreIdInvalid = 35355,

        [ErrorField(EntityField.AppDisplayText)]
        AppAdExtensionDisplayTextNullOrEmpty = 35356,
        [ErrorField(EntityField.AppDisplayText)]
        AppAdExtensionDisplayTextTooLong = 35357,
        [ErrorField(EntityField.AppDisplayText)]
        AppAdExtensionDisplayTextInvalid = 35358,

        [ErrorField(EntityField.AppDestinationUrl)]
        AppAdExtensionDestinationUrlNullOrEmpty = 35359,
        [ErrorField(EntityField.AppDestinationUrl)]
        AppAdExtensionDestinationUrlTooLong = 35360,
        [ErrorField(EntityField.AppDestinationUrl)]
        AppAdExtensionDestinationUrlInvalid = 35361,

        [ErrorField(EntityField.AppDevicePreference)]
        AppAdExtensionDevicePreferenceInvalid = 35362,

        [ErrorField(EntityField.AppVersion)]
        AppAdExtensionVersionTooLong = 35363,
        [ErrorField(EntityField.AppVersion)]
        AppAdExtensionVersionInvalid = 35364,

        [ErrorField(EntityField.AppDescription)]
        AppAdExtensionDescriptionTooLong = 35365,
        [ErrorField(EntityField.AppDescription)]
        AppAdExtensionDescriptionInvalid = 35366,

        [ErrorField(EntityField.AppMediaUrl)]
        AppAdExtensionMediaUrlTooLong = 35367,
        [ErrorField(EntityField.AppMediaUrl)]
        AppAdExtensionMediaUrlInvalid = 35368,

        [ErrorField(EntityField.AppAltText)]
        AppAdExtensionAltTextTooLong = 35369,
        [ErrorField(EntityField.AppAltText)]
        AppAdExtensionAltTextInvalid = 35370,

        [ErrorField(EntityField.AppUserRating)]
        AppAdExtensionUserRatingTooLong = 35371,
        [ErrorField(EntityField.AppUserRating)]
        AppAdExtensionUserRatingInvalid = 35372,

        [ErrorField(EntityField.AppMaturityRating)]
        AppAdExtensionMaturityRatingTooLong = 35373,
        [ErrorField(EntityField.AppMaturityRating)]
        AppAdExtensionMaturityRatingInvalid = 35374,

        AppAdExtensionAppStoreIdAppPlatformUpdateNotAllowed = 35375,
        [ErrorField(EntityField.IsAppInstallTrackingEnabled)]
        AppInstallTrackingPilotNotEnabledForCustomer = 35376,

        AdExtensionPilotNotEnabledForCustomer = 35400,
        AdExtensionCampaignAssociationPilotNotEnabledForCustomer = 35401,
        AdExtensionAdGroupAssociationPilotNotEnabledForCustomer = 35402,
        ValueTooShort = 35403,
        ValueTooLong = 35404,
        ValueOutOfRange = 35405,
        ValueIsMissing = 35406,
        [DbErrorCodeMapping(-101430)]
        InvalidValue = 35407,
        InvalidHeader = 35408,
        InvalidStructuredSnippetCharacter = 35409,
        TooManyStructuredSnippetText = 35410,
        TooFewStructuredSnippetText = 35411,
        AdExtensionAccountAssociationPilotNotEnabledForCustomer = 35412,
        AdExtensionAccountAssociationEntityIdNotEqualToAccountId = 35413,
        AdExtensionAccountAssociationShouldHaveOneEntityId = 35414,
        DuplicateAdExtensionIdToAccountIdAssociation = 35415,
        AdExtensionIdToAccountIdAssociationNotPassed = 35416,

        [ErrorField(EntityField.FeedId)]
        DynamicDataExtensionFeedIdInvalid = 35417,
        [ErrorField(EntityField.FeedId)]
        [DbErrorCodeMapping(-102735)]
        DynamicDataExtensionFeedIdAlreadyUsed = 35418,
        DynamicDataExtensionFeedDataUpdateNotAllowed = 35419,
        DynamicDataExtensionInvalidFeedType = 35420,

        AdExtensionIdToSubAccountIdAssociationsNotPassed = 35421,
        AdExtensionIdToSubAccountIdAssociationNotPassed = 35422,
        DuplicateAdExtensionIdToSubAccountIdAssociation = 35423,
        InvalidAdExtensionTypeForSubAccountAssociation = 35424,

        AdExtensionIdToHotelGroupIdAssociationsNotPassed = 35425,
        AdExtensionIdToHotelGroupIdAssociationNotPassed = 35426,
        DuplicateAdExtensionIdToHotelGroupIdAssociation = 35427,
        InvalidAdExtensionTypeForHotelGroupAssociation = 35428,

        ScheduleNotAllowedForFeedType = 35429,
        UrlNotAllowedForFeedType = 35430,

        #endregion Ad extension errors between 35001 and 36000

        #region Decoupling DB errors between 36001 and 37000
        BillingCallbackEntitiesNotPassed = 36001,
        BillingCallbackEntityLimitExceeded = 36002,
        DuplicateCampaignId = 36003,
        BillingCallbackEntityNotPassed = 36004,
        DuplicateAdgroupId = 36005,
        #endregion Decoupling DB errors between 36001 and 37000

        #region Search Terms from 37001 to 38000

        SearchTermsNotEnabledForCustomer = 37001,

        #endregion

        #region AdGroupCriterion errors from 38001 to 39000

        [DbErrorCodeMapping(-104046)]
        [DbErrorCodeMapping(-102003)] // When update non-library target
        AdGroupCriterionIdInvalid = 38001,
        ProductCriterionPilotNotEnabledForCustomer = 38002,
        AdGroupCriterionIdArrayNullOrEmpty = 38003,
        DuplicateAdGroupCriterionId = 38004,
        AdGroupCriterionIdListExceedsLimit = 38005,

        AdGroupCriterionsNullOrEmpty = 38006,
        AdGroupCriterionsEntityLimitExceeded = 38007,
        AdGroupCriterionIsNull = 38008,
        AdGroupCriterionInvalidConditionType = 38009,
        AdGroupCriterionInvalidBidType = 38010,
        AdGroupCriterionInvalidBidValue = 38011,
        AdGroupCriterionIdShouldBeNullOnAdd = 38012,

        [DbErrorCodeMapping(-104048)]
        InvalidAdGroupCriterionStatus = 38013,
        InvalidAdGroupCriterionType = 38014,
        [ErrorField(EntityField.ProductCondition)]
        ProductAdGroupCriterionTooManyConditions = 38015,
        [ErrorField(EntityField.ProductCondition)]
        AdGroupCriterionProductConditionIsNull = 38016,
        [ErrorField(EntityField.ProductCondition)]
        ProductConditionAttributeNullOrEmpty = 38017,
        AdGroupCriterionIsDeleted = 38018,

        [DbErrorCodeMapping(-101490)]
        DuplicateProductTarget = 38019,
        AddOrUpdateAcrossMultipleAdGroupsNotSupported = 38020,
        ProductPartitionIsNull = 38021,
        ProductDimensionIsNull = 38022,

        // maps to API code: InvalidProductConditionOperand
        InvalidProductConditionOperand = 38023,
        ProductConditionOperandNotSupported = 38024,
        ParentProductConditionMissing = 38025,
        DuplicateProductConditions = 38026,
        TooFewProductPartitionsForSubdivision = 38027,
        ProductConditionEverythingElseMissing = 38028,
        ProductConditionOperandUnderSubDivisionMustBeSame = 38029,
        InvalidProductPartitionType = 38030,
        ProductPartitionTypeUnitMustBeLeaf = 38031,
        ProductPartitionTypeSubdivisionMustBeBiddable = 38032,
        InvalidOrDuplicateProductPartitionsInSubdivision = 38033,
        AddOrUpdateAcrossMultipleCriterionTypesNotSupported = 38034,
        CustomerNotEnabledForShoppingCampaigns = 38035,
        ProductPartitionDestinationUrlTooLong = 38036,
        ProductPartitionParam1TooLong = 38037,
        ProductPartitionParam2TooLong = 38038,
        ProductPartitionParam3TooLong = 38039,
        InvalidProductConditionAttribute = 38040, // Maps to  API code InvalidProductConditionAttribute
        AllCriterionsMustHaveSameType = 38041,
        AdGroupCriterionAudienceAssociationsCannotBeNull = 38042,
        AdGroupCriterionDuplicateAudienceIds = 38043,
        AdGroupCriterionInvalidBidMultiplierValue = 38044,
        AdGroupCriterionInvalidAudienceId = 38045,
        AdGroupCriterionInvalidAudienceName = 38046,
        AllCriterionsMustHaveSameBidType = 38047,
        AllNullCriterionTypesNotAllowedOnCreate = 38048,
        AdGroupCriterionDuplicateAdGroupId = 38049,

        [DbErrorCodeMapping(-104044)]
        ConcurrentStoreModification = 38050,

        OperationsForTooManyAdgroups = 38051,
        AdGroupCriterionActionIsNull = 38052,
        AdGroupCriterionActionsNullOrEmpty = 38053,
        CampaignIsNotOfTypeShopping = 38054,

        AdGroupCriterionCriterionIsNull = 38055,

        InvalidAdGroupCriterionCriterionType = 38056,

        // not used.
        InvalidAdGroupCriterionCriterionBidType = 38057,

        // not used.
        ProductPartitionDoesNotExist = 38058,

        [DbErrorCodeMapping(-104045)]
        CannotChangeAdgroupCriterionType = 38059,

        CannotModifyNegativeAdGroupCriterion = 38060,
        ProductPartitionLimitExceededForAdGroup = 38061,

        InvalidProductConditionHierarchy = 38062,
        HeightOfProductPartitionTreeExceeededLimit = 38063,
        DuplicateRootNodeForProductPartitionTree = 38064,
        CannotAddChildrenToUnitNodeType = 38065,
        ParentProductPartitionNodeDoesNotExist = 38066,
        CannotUpdateCriterionForProductPartition = 38067,
        EverythingElseNodeCannotBeDeleted = 38068,
        CriterionTypeNotAllowed = 38069,
        AnotherOperationForSameAdGroupHasError = 38070,
        CriterionTypeMismatch = 38071,

        DestinationUrlProtocolInvalid = 38072,
        DestinationUrlTooLong = 38074,

        // following three error codes maps to one API error code: DestinationUrlInvalid
        NonMatchingBraces = 38075,
        CrossScriptingUnsafeUrl = 38076,
        DestinationUrlInvalid = 38077,

        Param1NotSupportedForCriterionType = 38078,
        Param2NotSupportedForCriterionType = 38079,
        Param3NotSupportedForCriterionType = 38080,

        ProductGroupTooLong = 38081,

        AdGroupCriterionAudienceAssociationNotExist = 38082,
        AudienceCanNotAssociateWithContentOnlyAdGroup = 38083,
        DuplicateAdGroupCriterionAudienceAssociation = 38084,

        AdGroupCriterionIdNotMatchCriterionType = 38085,
        AdGroupCriterionBidMultiplierValueNotSet = 38086,

        AdGroupCriterionTransactionAcrossAdGroups = 38087,

        AudienceDeliveryChannelNotMatchCampaignType = 38088,

        AdGroupAlreadyHasCampaignAudienceCriterion = 38089,

        AdGroupCriterionBidMultiplierAndCashbackAdjustmentValueNotSet = 38090,

        ContentTargetingOnlySupportAudienceCampaign = 38091,

        AccountIsNotEligibleForPlacementTargeting = 38092,

        AccountIsNotEligibleForSubPlacementTargeting = 38093,

        /// <summary>
        /// The topic ID specified in the target is invalid
        /// </summary>
        InvalidTopicIdTarget = 38094,

        /// <summary>
        /// The bid adjustment value for topic target is invalid
        /// </summary>
        InvalidTopicTargetBidAdjustment = 38095,

        /// <summary>
        /// The account is not eligible for topic targeting
        /// </summary>
        AccountIsNotEligibleForTopicTargeting = 38096,

        #endregion

        #region Express Service Error codes 50000 and 51000

        [DbErrorCodeMapping(-450005)]
        [DbErrorCodeMapping(-450006)]
        CategoryIDNotValid = 50000,
        BusinessToCategoryMappingAddFailed = 50001,
        BusinessToCategoryMappingUpdateFailed = 50002,
        BusinessToCategoryMappingGetByCustomerIDFailed = 50003,
        BusinessToCategoryMappingGetbyIdsFailed = 50004,
        CategoryAdGroupSetBatchLimitExceed = 50005,
        CategoryAdGroupInvalid = 50006,
        BusinessToCategoryToAdgroupMappingGetByCustomerIDFailed = 50007,
        NoCategoriesFound = 50008,
        BusinessToCategoryToAdgroupMappingAddFailed = 50009,
        BusinessToCategoryClickRangeUpdateFailed = 50010,
        TagLineTooLong = 50011,
        BusinessToCategoryClickRangeMappingGetByCustomerIDFailed = 50012,
        InvalidMaxCpc = 560003,
        DuplicateBusinessListing = 560004,
        InvalidCategorySelections = 560005,
        InvalidId = 560006,
        InvalidStatus = 560007,
        InvalidRadius = 560008,
        InvalidNegativeKeyword = 560009,
        InvalidBusinessAddress = 560010,
        InvalidCategoryOrSubcategory = 560011,
        BusinessListNull = 560012,
        TextAdForExpressIsNull = 560013,
        InvalidMonthyBudget = 560014,
        BusinessInfoIsNull = 560015,
        AddressIsNull = 560016,
        InvalidStateOrProvince = 560017,
        DuplicateCategorySelections = 560018,
        BusinessNameWithInvalidMinLength = 560019,
        BusinessNameWithInvalidMaxLength = 560020,
        BusinessNameWithInvalidControlCharacters = 560021,
        InvalidCountryCode = 560022,
        InvalidTargetAddress = 560023,
        AddressCityInvalid = 560024,
        AddressStateOrProvinceInvalid = 560025,
        InvalidBusinessName = 560026,
        InvalidState = 560027,
        InvalidCity = 560028,

        [DbErrorCodeMapping(-200026)]
        UserIsNotAuthorized = 560029,
        InvalidPostalCode = 560030,
        InvalidResellerCustomer = 560031,
        InvalidGeoCode = 560032,
        CategoryBingPlacesInvalid = 50033,

        CategoryDataDetailsMissing = 50034,
        InValidCountForCategoryAdExtension = 50035,

        [DbErrorCodeMapping(-450003)]
        [DbErrorCodeMapping(-450004)]
        InvalidTemplateId = 50036,

        [DbErrorCodeMapping(-100864)]
        InvalidAdId = 50037,

        [DbErrorCodeMapping(-450001)]
        [DbErrorCodeMapping(-450002)]
        MissingAdIdInTemplateTable = 50038,

        [DbErrorCodeMapping(-450007)]
        CategoryIdDeleted = 50039,

        [DbErrorCodeMapping(-450008)]
        BusinessIdCategoryIdAlreadyPresent = 50040,

        [DbErrorCodeMapping(-450009)]
        [DbErrorCodeMapping(-450010)]
        [DbErrorCodeMapping(-450011)]
        BusinessIdCategoryIdNotAvaliable = 50041,
        InvalidBingPlaceBusiness = 50042,

        [DbErrorCodeMapping(-450012)]
        [DbErrorCodeMapping(-450013)]
        BusinessIdAlreadyPresent = 50043,

        [DbErrorCodeMapping(-450014)]
        [DbErrorCodeMapping(-450015)]
        [DbErrorCodeMapping(-450016)]
        BusinessIdNotPresent = 50044,

        UserTokenInvalid = 50045,

        #endregion Express Service Error codes 50000 and 51000

        #region Express AI Service Error codes 30020 and 30050
        InvalidMaxLocations = 30029,
        TimeIntervalNotSupportedForPublisherMonetization = 30030,
        InvalidSuggestionType = 30031,
        UnsupportedCountryForSuggestionType = 30032,
        InvalidBidAmount = 30033,

        UnsupportedMatchType = 30034,
        UnsupportedAdPosition = 30035,

        InvalidCategoryId = 30036,
        GeoLocationNotPassed = 30037,
        InvalidGeoLocation = 30038,
        InvalidMaxLocalBudgetOptions = 30039,
        InvalidBudget = 30040,
        InvalidNumberOfTileIds = 30042,
        InvalidLocalBuinessName = 30044,
        TimeIntervalNotSupported = 30045,
        InValidLandScapeGenerated = 30046,
        NoKewordsinBothBags = 30047,
        ZeroRowsFromDB = 30048,
        LowCPC = 30049,
        #endregion Express Service AI Error codes 30020 and 30050

        #region CallTracking error codes from 39001 to 40000

        CallTrackingNotEnabledForCustomer = 39001,

        #endregion

        #region Bulk Upload codes from 40001 to 41000

        RequestIdInvalid = 40002,
        InvalidItemToDelete = 40003,
        CloudTableFailure = 40004,
        FailedBecauseOfOtherItem = 40005,
        UnknownRowType = 40006,

        [DbErrorCodeMapping(-111460)]
        ThrottlingLimitReached = 40007,
        UploadFileRowCountExceeded = 40008,
        UploadFileFormatNotSupported = 40009,
        CannotRemoveLastGoodAdExtensionItem = 40010,
        FormatVersionNotSupported = 40011,
        FormatVersionRequired = 40012,

        BulkEditActionCannotApplyToField = 40500,
        BulkEditActionsForDifferentEntities = 40501,
        BulkEditActionNotApplicable = 40502,
        BulkEditParametersInvalid = 40503,
        BulkEditDescriptionInvalid = 40504,
        #endregion

        #region Search codes from 41001 to 41050

        SearchCampaignsFailed = 41001,
        SearchAdGroupsFailed = 41002,
        SearchKeywordsFailed = 41003,
        SearchAdsFailed = 41004,
        SearchTextTooLong = 41005,
        SearchContextInvalid = 41006,

        #endregion

        RelevanceOverrideAndRankingPenaltyIsNull = 42000,

        #region Negative Keyword List Error Codes from 43000 to 43100
        [DbErrorCodeMapping(-102405)]
        MaxNegativeKeywordLimitExceededForList = 43000,

        [DbErrorCodeMapping(-102410)]
        NegativeKeywordDeleted = 43001,

        [DbErrorCodeMapping(-102411)]
        InvalidNegativeKeywordId = 43002,

        [DbErrorCodeMapping(-102408)]
        [DbErrorCodeMapping(-102409)]
        NegativeKeywordListWithActiveAssociationsCannotBeDeleted = 43003,

        NegativeKeywordTypeInvalid = 43004,
        NegativeKeywordTextRequired = 43005,

        [DbErrorCodeMapping(-102401)]
        DuplicateNegativeKeywordListName = 43006,
        #endregion

        #region Shared Entity Error Codes from 43101 to 43200
        SharedEntityNameNullOrEmpty = 43101,

        [DbErrorCodeMapping(-102400)]
        [DbErrorCodeMapping(-103603)]
        [DbErrorCodeMapping(-103703)]
        [DbErrorCodeMapping(-103614)]
        [DbErrorCodeMapping(-103615)]
        SharedEntityLimitExceeded = 43102,

        SharedEntityInvalidType = 43103,
        SharedEntityAssociationsNullOrEmpty = 43104,
        EntityTypeNotSupported = 43105,
        MultipleSharedEntityTypesNotAllowed = 43106,

        SharedEntityAssociationsBatchLimitExceeded = 43107,
        SharedEntityAssociationsListItemNullOrEmpty = 43108,

        [DbErrorCodeMapping(-102412)]
        SharedEntityAssociationDuplicate = 43109,

        SharedEntityIdsNullOrEmpty = 43110,
        SharedEntityNullOrEmpty = 43111,
        DuplicateSharedEntityId = 43112,
        SharedEntityTypeNullOrEmpty = 43113,

        [DbErrorCodeMapping(-102404)]
        [DbErrorCodeMapping(-102407)]
        SharedListDeleted = 43114,

        SharedListsNullOrEmpty = 43115,
        SharedListNullOrEmpty = 43116,

        [DbErrorCodeMapping(-102402)]
        [DbErrorCodeMapping(-102403)]
        [DbErrorCodeMapping(-103601)] // List does not belong to customer
        [DbErrorCodeMapping(-103612)]
        [DbErrorCodeMapping(-103701)]
        SharedListIdInvalid = 43117,

        SharedListDuplicate = 43118,
        SharedListItemsNullOrEmpty = 43119,

        [DbErrorCodeMapping(-103709)]
        [DbErrorCodeMapping(-103704)]
        [DbErrorCodeMapping(-103616)]
        SharedListItemIdInvalid = 43120,
        SharedListItemIdsLimitExceeded = 43121,
        SharedEntitiesNullOrEmpty = 43122,
        SharedEntityNameTooLong = 43123,
        SharedEntityNameInvalid = 43124,
        SharedListIdNotAllowed = 43125,
        SharedEntityIdInvalid = 43126,

        [DbErrorCodeMapping(-102413)]
        [DbErrorCodeMapping(-103610)]
        [DbErrorCodeMapping(-103620)]
        [DbErrorCodeMapping(-103710)]
        SharedEntityAssociationDoesNotExist = 43127,

        NotInPilotForManagerAccountSharedWebsiteExclusions = 43128,

        SharedEntityBatchLimitExceeded = 43129,
        EntityBatchLimitExceeded = 43130,

        ////prevent Structured Negative Keyword migrated customer to call blob negative keyword methods
        [DbErrorCodeMapping(-102416)]
        CustomerNotAllowedToManipulateBlobNegativeKeywords = 43131,

        [DbErrorCodeMapping(-102414)]
        NegativeKeywordsAccountLimitExceeded = 43132,

        [DbErrorCodeMapping(-102415)]
        SharedEntityAssociationsAccountLimitExceeded = 43133,

        [DbErrorCodeMapping(-103600)]
        [DbErrorCodeMapping(-103700)]
        [DbErrorCodeMapping(-103621)]
        DuplicateSharedListName = 43134,

        [DbErrorCodeMapping(-103602)]
        [DbErrorCodeMapping(-103702)]
        MaxListItemLimitExceededForList = 43135,

        [DbErrorCodeMapping(-103604)]
        DuplicateListItemInList = 43136,

        [DbErrorCodeMapping(-103708)]
        SharedEntitiesWithActiveAssociationsCannotBeDeleted = 43137,

        [DbErrorCodeMapping(-103606)]
        InvalidListItemTypeForList = 43138,

        [DbErrorCodeMapping(-103609)]
        [DbErrorCodeMapping(-103619)]
        SharedListItemNotInList = 43139,

        SharedListItemBatchLimitExceeded = 43140,

        #endregion

        #region Conversion Tag and Goal Entity Error Codes from 43201 to 43300

        /// <summary>
        /// Tag array is null or empty.
        /// </summary>
        TagsNotPassed = 43201,

        /// <summary>
        /// Either tag name is empty or doesn't conform to its length limits.
        /// </summary>
        InvalidTagName = 43202,

        /// <summary>
        /// Either tag description is empty or doesn't conform to its length limits.
        /// </summary>
        InvalidTagDescription = 43203,

        /// <summary>
        /// Tag tracking code doesn't conform to its length limits.
        /// </summary>
        InvalidTagTrackingCode = 43204,

        /// <summary>
        /// Either tag id is invalid or doesn't belong to current customer.
        /// </summary>
        InvalidTagId = 43205,

        /// <summary>
        /// Tag status is invalid.
        /// </summary>
        InvalidTagStatus = 43206,

        /// <summary>
        /// Tag Ids is null or empty
        /// </summary>
        TagIdsNotPassed = 43207,

        /// <summary>
        /// Tag Id is duplicate
        /// </summary>
        DuplicateTagId = 43208,

        /// <summary>
        /// Too many tags passed in
        /// </summary>
        TagsBatchSizeExceesdLimit = 43209,

        /// <summary>
        /// Tag Id passed for update does not exist in the DB
        /// </summary>
        [DbErrorCodeMapping(-620020)]
        TagIdDoesNotExist = 43210,

        /// <summary>
        /// Attempting to update something other than status on a legacy tag
        /// </summary>
        [DbErrorCodeMapping(-620021)]
        OnlyStatusCanBeUpdatedForLegacyTags = 43211,

        /// <summary>
        /// Tag Id already exists in the DB
        /// </summary>
        [DbErrorCodeMapping(-620023)]
        TagWithSameNameAlreadyExistsUnderCustomer = 43212,

        /// <summary>
        /// Associating the goal to a tag that doesn't exist
        /// </summary>
        [DbErrorCodeMapping(-620000)]
        TagDoesNotExistForPassedGoal = 43213,

        /// <summary>
        /// Associating the goal to a tag that doesn't exist
        /// </summary>
        [DbErrorCodeMapping(-620014)]
        GoalWithSameNameAlreadyExistsUnderTag = 43214,

        InvalidIsClarityTagCheckBoxValue = 43215,

        /// <summary>
        /// Goal array is null or empty
        /// </summary>
        GoalsNotPassed = 43221,

        /// <summary>
        /// Goal name is null or empty
        /// </summary>
        GoalNameNullOrEmpty = 43222,

        /// <summary>
        /// Goal name text has invalid characters
        /// </summary>
        GoalNameHasInvalidCharacters = 43223,

        /// <summary>
        /// Goal name is too long
        /// </summary>
        GoalNameIsTooLong = 43224,

        /// <summary>
        /// Goal lookback window is invalid
        /// </summary>
        InvalidGoalLookbackWindow = 43225,

        /// <summary>
        /// Goal array is null or empty
        /// </summary>
        GoalIdsNotPassed = 43226,

        /// <summary>
        /// Too many goals passed in
        /// </summary>
        GoalsBatchSizeExceedsLimit = 43227,

        /// <summary>
        /// Goal status is invalid
        /// </summary>
        InvalidGoalStatus = 43228,

        /// <summary>
        /// Goal entity type is invalid
        /// </summary>
        InvalidGoalEntityType = 43229,

        /// <summary>
        /// Goal monetary value is invalid
        /// </summary>
        InvalidGoalValue = 43230,

        /// <summary>
        /// Destination goal expression operator is invalid
        /// </summary>
        InvalidDestinationGoalExpressionOperator = 43231,

        /// <summary>
        /// Destination goal url string is null or empty
        /// </summary>
        DestinationGoalUrlStringNullOrEmpty = 43232,

        /// <summary>
        /// Destination goal url string has invalid characters
        /// </summary>
        DestinationGoalUrlStringHasInvalidCharacters = 43233,

        /// <summary>
        /// Destination goal url string is too long
        /// </summary>
        DestinationGoalUrlStringTooLong = 43234,

        /// <summary>
        /// Invalid duration goal duration
        /// </summary>
        InvalidDurationGoalDuration = 43235,

        /// <summary>
        /// Invalid duration goal value operator
        /// </summary>
        InvalidDurationGoalValueOperator = 43236,

        /// <summary>
        /// Invalid event goal category
        /// </summary>
        InvalidEventGoalCategory = 43237,

        /// <summary>
        /// Invalid event goal category operator
        /// </summary>
        InvalidEventGoalCategoryOperator = 43238,

        /// <summary>
        /// Invalid event goal action
        /// </summary>
        InvalidEventGoalAction = 43239,

        /// <summary>
        /// Invalid event goal action operator
        /// </summary>
        InvalidEventGoalActionOperator = 43240,

        /// <summary>
        /// Invalid event goal label
        /// </summary>
        InvalidEventGoalLabel = 43241,

        /// <summary>
        /// Invalid event goal label operator
        /// </summary>
        InvalidEventGoalLabelOperator = 43242,

        /// <summary>
        /// Invalid event goal value
        /// </summary>
        InvalidEventGoalValue = 43243,

        /// <summary>
        /// Invalid event goal value operator
        /// </summary>
        InvalidEventGoalValueOperator = 43244,

        /// <summary>
        /// Invalid page views per visit value operator
        /// </summary>
        InvalidPageViewsPerVisitValueOperator = 43245,

        /// <summary>
        /// Invalid page views count
        /// </summary>
        InvalidPageViews = 43246,

        /// <summary>
        /// Invalid goal id
        /// </summary>
        [DbErrorCodeMapping(-620015)]
        [DbErrorCodeMapping(-120011)]
        InvalidGoalId = 43247,

        /// <summary>
        /// Goal Id is duplicate
        /// </summary>
        DuplicateGoalId = 43248,

        AppInstalGoalAppPlatformNullOrEmpty = 43249,

        AppInstalGoalAppPlatformInvalid = 43250,

        AppInstalGoalAppStoreIdNullOrEmpty = 43251,

        AppInstalGoalAppStoreIdHasInvalidCharacters = 43252,

        AppInstalGoalAppStoreIdTooLong = 43253,

        /// <summary>
        /// Cannot set Flex Tag Script, which is auto-migrated from Campaign Analytics goals
        /// </summary>
        FlexTagScriptShouldBeNullOrEmptyOnAddAndUpdate = 43254,

        /// <summary>
        /// Cannot set Campaign Analytics Goal Name, which is auto-migrated from Campaign Analytics goals
        /// </summary>
        CampaignAnalyticsGoalNameShouldBeNullOrEmptyOnAddAndUpdate = 43255,

        /// <summary>
        /// At least 1 stage should be in a multi-stage goal
        /// </summary>
        GoalStagesNotPassed = 43256,

        /// <summary>
        /// At most 6 stages in a multi-stage goal
        /// </summary>
        [DbErrorCodeMapping(-620027)]
        GoalStagesCountExceedsLimit = 43257,

        /// <summary>
        /// Invalid goal stage type
        /// </summary>
        [DbErrorCodeMapping(-620026)]
        InvalidGoalStageType = 43258,

        /// <summary>
        /// MultiStage goaltype (StageTypeId=1) has unmatched Event attributes
        /// </summary>
        [DbErrorCodeMapping(-620028)]
        MultiStageGoalHasUnmatchedEventAttributes = 43259,

        /// <summary>
        /// MultiStage goaltype (StageTypeId=2) has unmatched DestinationURL attributes
        /// </summary>
        [DbErrorCodeMapping(-620029)]
        MultiStageGoalHasUnmatchedDestinationUrlAttributes = 43260,

        /// <summary>
        /// Max UET Tag per customer limit reached
        /// </summary>
        [DbErrorCodeMapping(-620030)]
        MaxUetTagPerCustomerLimitReached = 43261,

        /// <summary>
        /// Max Conversion Goal per account limit reached
        /// </summary>
        [DbErrorCodeMapping(-620031)]
        MaxConversionGoalPerAccountLimitReached = 43262,

        /// <summary>
        /// Max Conversion Goal per customer limit reached
        /// </summary>
        [DbErrorCodeMapping(-620032)]
        MaxConversionGoalPerCustomerLimitReached = 43263,

        /// <summary>
        /// Goal value type of App Install goal/duration goal/page per visit goal couldn't be variant value
        /// </summary>
        InvalidGoalValueType = 43264,

        /// <summary>
        /// One goal is valid, but not in request's goal type filter
        /// </summary>
        GoalEntityTypeNotInTypeFilter = 43265,

        /// <summary>
        /// App Install goal scope should be always customer level
        /// </summary>
        InvalidAppInstallGoalScope = 43266,

        /// <summary>
        /// Invalid Conversion Count Type
        /// </summary>
        InvalidConversionCountType = 43267,

        /// <summary>
        /// At least one expression and operator pair is required for an Event Goal.
        /// </summary>
        EventGoalExpressionWithOperatorNotPassed = 43268,

        /// <summary>
        /// Goal monetary value currency code is invalid
        /// </summary>
        InvalidGoalValueCurrencyCode = 43269,

        /// <summary>
        /// Goal monetary value currency code should be null if it's not destination goal or event goal
        /// </summary>
        GoalValueCurrencyCodeShouldBeNull = 43270,

        /// <summary>
        /// Goal monetary value currency code should not be null if it's not duration goal, pages viewed goal or app install goal
        /// </summary>
        GoalValueCurrencyCodeShouldNotBeNull = 43271,

        /// <summary>
        /// Goal type can not be changed for in-store transaction goal or offline conversion goal
        /// </summary>
        GoalTypeCannotBeChanged = 43272,

        /// <summary>
        /// In-Store transaction goal should be acrossa all accounts
        /// </summary>
        InStoreTransactionGoalShouldBeAcrossAllAccounts = 43273,

        /// <summary>
        /// Only one in-store transaction goal be allowed per customer
        /// </summary>
        [DbErrorCodeMapping(-620033)]
        OnlyOneInStoreTransactionGoalBeAllowedPerCustomer = 43274,

        /// <summary>
        /// In-store transaction pilot not enabled for customer
        /// </summary>
        InStoreTransactionPilotNotEnabledForCustomer = 43275,

        /// <summary>
        /// Offline conversion goal scope should be always account level
        /// </summary>
        InvalidOfflineConversionGoalScope = 43276,

        /// <summary>
        /// Conversion Goal selection is not avaliable for the customer
        /// </summary>
        CustomerNotEligibleForConversionGoalSelection = 43277,

        /// <summary>
        /// View Through Conversion is not avaliable for the customer
        /// </summary>
        CustomerNotEligibleForViewThroughConversion = 43278,

        /// <summary>
        /// Goal view throught lookback window is invalid
        /// </summary>
        InvalidGoalViewThroughLookbackWindow = 43279,

        ViewThroughAccountSettingValueInvalid = 43280,

        ViewThroughConversionNotApplicableToGoalType = 43281,

        /// <summary>
        /// Customer Not Eligible For ProductConversionGoal
        /// </summary>
        CustomerNotEligibleForProductConversionGoal = 43283,

        [DbErrorCodeMapping(-620034)]
        InvalidProductConversionGoal = 43284,

        CustomerNotEligibleForLinkedInTargeting = 43285,

        LinkedInInferenceAccountSettingValueInvalid = 43286,

        /// <summary>
        /// Customer Not Eligible For GoalCategory
        /// </summary>
        CustomerNotEligibleForGoalCategory = 43287,

        /// <summary>
        /// Customer Not Eligible For ExternalAttribution
        /// </summary>
        CustomerNotEligibleForExternalAttribution = 43288,

        /// <summary>
        /// Customer Not Eligible For ExternalAttribution
        /// </summary>
        AttributionModelTypeCannotBeUpdated = 43289,

        /// <summary>
        /// AttributionModelType is not applicable to this goal type
        /// </summary>
        AttributionModelTypeNotApplicableToGoalType = 43290,

        /// <summary>
        /// The requested category is not valid for this goal type.
        /// </summary>
        InvalidCategoryForGoalType = 43291,

        /// <summary>
        /// Smart Goal Should Be Account Level
        /// </summary>
        SmartGoalShouldBeAccountLevel = 43292,

        /// <summary>
        /// Smart Goal should be only one for one account
        /// </summary>
        [DbErrorCodeMapping(-620036)]
        SmartGoalShouldBeOnlyOne = 43293,

        /// <summary>
        /// Smart Goal could not be edited in some parameters
        /// </summary>
        SmartGoalCouldNotBeEditInSomeParameters = 43294,

        /// <summary>
        /// Smart Goal could not be created by customer
        /// </summary>
        SmartGoalCouldNotBeCreatedByCustomer = 43295,

        /// <summary>
        /// Smart Goal is not available for the account
        /// </summary>
        AccountNotEligibleForSmartGoal = 43296,


        /// <summary>
        /// M365 Integration feature set are not available for the customer
        /// </summary>
        CustomerNotEligibleForM365Integration = 43297,

        /// <summary>
        /// Mca Account Tier has invalid value.
        /// </summary>
        McaAccountTierInvalid = 43298,

        /// <summary>
        /// todo-esldcr: errorCode
        /// ItemId is null
        /// </summary>
        AdExtensionItemIdInvalid = 43299,
        /// <summary>
        /// Not GoalCategoryEnum Value
        /// </summary>
        InvalidGoalCategory = 43300,

        /// <summary>
        /// Invalid Regular Expression
        /// </summary>
        InvalidRegularExpression = 433000,
        #endregion

        #region AnnotationsOptOut Error Codes from 43301 to 43400
        [DbErrorCodeMapping(-100888)]
        InvalidExclusionTypeIdOrSubTypeId = 43301,

        AnnotationOptOutTicketNumberTooLong = 43302,
        AnnotationOptOutJustificationTextTooLong = 43303,
        AnnotationOptOutDisplayUrlTooLong = 43304,
        AnnotationOptOutBatchLimitExceeded = 43305,
        AnnotationOptOutCollectionNullOrEmpty = 43306,
        AnnotationOptOutJustificationTextNullOrEmpty = 43307,
        AnnotationOptOutTicketNumberNullOrEmpty = 43308,

        [DbErrorCodeMapping(-100886)]
        AnnotationOptOutAccountOptOutConflictsWithCustomerOptOut = 43309,
        CustomerNotInAutomatedExtensionPilot = 43310,

        #endregion

        #region AdExtensionDeviceTarget
        AdExtensionDeviceTargeEntityIdsNullOrEmpty = 43400,
        AdExtensionDeviceTargeEntityIdsInvalid = 43401,
        AdExtensionDeviceTargetEntityLimitExceeded = 43402,
        CustomerNotEnabledForAdExtensionDeviceTargetPilot = 43403,
        #endregion

        #region Audience Management Error Codes from 43501 to 43600

        /// <summary>
        /// Audience array is null or empty.
        /// </summary>
        AudiencesNotPassed = 43501,

        /// <summary>
        /// Too many audiences passed in
        /// </summary>
        AudienceBatchSizeExceedsLimit = 43502,

        /// <summary>
        /// Either audience id is invalid or doesn't belong to current customer.
        /// </summary>
        InvalidAudienceId = 43503,

        /// <summary>
        /// Audience status is invalid.
        /// </summary>
        InvalidAudienceStatus = 43504,

        /// <summary>
        /// Either audience description is empty or doesn't conform to its length limits.
        /// </summary>
        InvalidAudienceDescription = 43505,

        /// <summary>
        /// Either audience name is empty or doesn't conform to its length limits.
        /// </summary>
        InvalidAudienceName = 43506,

        /// <summary>
        /// Audience Ids is null or empty
        /// </summary>
        AudienceIdsNotPassed = 43507,

        /// <summary>
        /// Audience Id is duplicate
        /// </summary>
        DuplicateAudienceId = 43508,

        /// <summary>
        /// Invalid Remessaging Audience
        /// </summary>
        InvalidRemessagingAudience = 43509,

        /// <summary>
        /// Error calling MDS Service
        /// </summary>
        MdsServiceError = 43510,

        /// <summary>
        /// Audience Id is duplicate
        /// </summary>
        DuplicateAudienceName = 43511,

        /// <summary>
        /// Invalid Tag Id.
        /// </summary>
        InvalidAudienceTagId = 43513,

        /// <summary>
        /// Invalid Look back window days.
        /// </summary>
        InvalidAudienceLookbackWindow = 43514,

        /// <summary>
        /// Maximum audience per customer limit reached
        /// </summary>
        MaxAudiencesPerCustomerLimitReached = 43515,

        /// <summary>
        /// Audience cannot be deleted since it's associated
        /// </summary>
        AudienceCannotBeDeletedDueToExistingAssociation = 43516,

        /// <summary>
        /// Maximum audience criterions per account limit reached
        /// </summary>
        [DbErrorCodeMapping(-103400)]
        MaxAudienceCriterionsPerAccountLimitReached = 43517,

        /// <summary>
        /// Invalid audience custom event value.
        /// </summary>
        InvalidAudienceCustomEventValue = 43518,

        /// <summary>
        /// Invalid audience custom expression.
        /// </summary>
        InvalidAudienceCustomEventExression = 43519,

        /// <summary>
        /// Invalid audience constraints.
        /// </summary>
        InvalidAudienceConstraints = 43520,

        /// <summary>
        /// IsAccountLevel cannot be changed on Update.
        /// </summary>
        AudienceIsAccountLevelCannotBeUpdated = 43521,

        /// <summary>
        /// Invalid audience type
        /// </summary>
        InvalidAudienceType = 43522,

        /// <summary>
        /// Invalid audience
        /// </summary>
        InvalidAudience = 43523,

        /// <summary>
        /// SegmentId is null
        /// </summary>
        SegmentIdIsNull = 43524,

        /// <summary>
        /// Invalid partnerName
        /// </summary>
        InvalidPartnerName = 43525,

        /// <summary>
        /// Only support remessaging audience and product audience delete
        /// </summary>
        CannotDeleteAudience = 43526,

        /// <summary>
        /// Audience Id doesn't match AudienceTypeFilter.
        /// </summary>
        AudienceIdNotMatchAudienceTypeFilter = 43527,

        /// <summary>
        /// The given audience type is not supported by current operation.
        /// </summary>
        AudienceTypeNotSupport = 43528,

        /// <summary>
        /// Maximum audience per account limit reached
        /// </summary>
        MaxAudiencesPerAccountLimitReached = 43529,

        /// <summary>
        /// Segment Id is duplicate
        /// </summary>
        DuplicateSegmentId = 43530,

        /// <summary>
        /// Product audience's action type is invalid
        /// </summary>
        InvalidActionType = 43531,

        /// <summary>
        /// Product audience's key is duplicate
        /// </summary>
        DuplicateProductAudience = 43532,

        /// <summary>
        /// Audience Id doesn't match DeliveryChannelFilter
        /// </summary>
        AudienceIdNotMatchDeliveryChannelFilter = 43533,

        /// <summary>
        /// Maximum in-market audience exclusion per account limit reached
        /// </summary>
        MaxInMarketAudienceExclusionPerAccountLimitReached = 43534,

        /// <summary>
        /// Customer not eligible for similar audience
        /// </summary>
        CustomerNotEligibleForSimilarAudience = 43535,

        /// <summary>
        /// Audience can not be deleted due to its paired similar audience has association
        /// </summary>
        AudienceCannotBeDeletedDueToPairedSimilarAudienceHasAssociation = 43536,

        /// <summary>
        /// Remarketing similar audience can not be deleted
        /// </summary>
        RemarketingSimilarAudienceCannotBeDeleted = 43537,

        /// <summary>
        /// InMarket audience is mapped to its parent
        /// </summary>
        ParentMappedInMarketAudienceTarget = 43540,
        InvalidCampaignCriterionStatus = 43541,

        /// <summary>
        /// AIM Campaign level flag for audiecne association has not being enabled yet.
        /// </summary>
        AIMCampaignLevelAudienceTargetingNotEnabled = 43542,

        /// <summary>
        /// Customer not eligible for customer match
        /// </summary>
        CustomerNotEligibleForCustomerMatch = 43543,

        /// <summary>
        /// Customer not eligible for customer match CRM
        /// </summary>
        CustomerNotEligibleForCustomerMatchCRM = 43544,

        /// <summary>
        /// Customer list action type is invalid
        /// </summary>
        InvalidCustomerListActionType = 43545,

        /// <summary>
        /// Customer list file identifier is invalid
        /// </summary>
        InvalidCustomerListFileIdentifier = 43546,

        /// <summary>
        /// Customer Id or account Id invalid for sharing.
        /// </summary>
        InvalidCustomerIdAccountIdForSharing = 43547,

        /// <summary>
        /// AudienceSets of CombinedListAudience is empty.
        /// </summary>
        AudienceSetsIsEmpty = 43548,

        /// <summary>
        /// The size of AudienceSets or the size of AudienceIds in AudienceSet exceeds the maximum.
        /// </summary>
        AudienceSetsIsTooLarge = 43549,

        /// <summary>
        /// No audience is selected for the CombinedListAudience.
        /// </summary>
        NoAudienceSelected = 43550,

        /// <summary>
        /// The operator of AudienceSet is invalid.
        /// </summary>
        InvalidAudienceSetOperator = 43551,

        /// <summary>
        /// All AudienceSets with NOT operator is not allowed.
        /// </summary>
        AllNOTOperatorIsNotAllowed = 43552,

        /// <summary>
        /// The audience type is not supported for AND operator.
        /// </summary>
        AudienceTypeIsNotSupportedForANDOperator = 43553,

        /// <summary>
        /// The audience type is not supported for OR operator.
        /// </summary>
        AudienceTypeIsNotSupportedForOROperator = 43554,

        /// <summary>
        /// The audience type is not supported for NOT operator.
        /// </summary>
        AudienceTypeIsNotSupportedForNOTOperator = 43555,

        /// <summary>
        /// Similar audience can only be used in a single OR set.
        /// </summary>
        SimilarAudienceCanOnlyBeInSingleORSet = 43556,

        /// <summary>
        /// Customer List Audience can only be combined with Customer List Audience.
        /// </summary>
        CustomerListsCanOnlyBeCombinedWithOtherCustomerLists = 43557,

        /// <summary>
        /// Audience can not be deleted due to it is used by a Combined List Audience.
        /// </summary>
        AudienceCannotBeDeletedDueToUsedByCombinedList = 43558,

        /// <summary>
        /// Audience can not be deleted due to its paired similar audience is used by a Combined List Audience.
        /// </summary>
        AudienceCannotBeDeletedDueToPairedSimilarAudienceUsedByCombinedList = 43559,

        /// <summary>
        /// Audience can not be deleted due to it is used by a conversion value rule
        /// </summary>
        AudienceCannotBeDeletedDueToConversionValueRule = 43560,

        /// <summary>
        /// Maximum criterions per customer limit reached
        /// </summary>
        [DbErrorCodeMapping(-103450)]
        MaxCriterionLimitExceededForCustomer = 43561,

        /// <summary>
        /// Combined List Audience can only be edited by its creator.
        /// </summary>
        CombinedListCanOnlyBeEditedByCreator = 43562,

        /// <summary>
        /// Combined List Audience can only be deleted by its creator.
        /// </summary>
        CombinedListCanOnlyBeDeletedByCreator = 43563,

        /// <summary>
        /// Custom Audience can only be combined with other Custom Audience.
        /// </summary>
        CustomAudienceCanOnlyBeCombinedWithOtherCustomAudience = 43564,

        /// <summary>
        /// Invalid Customer List Id.
        /// </summary>
        InvalidCustomerListId = 43565,

        InvalidAudienceSets = 43566,

        AudienceShouldNotBeAccountLevel = 43567,

        TermsAndConditionsNotAccepted = 43568,

        CombinedAudienceForLocationNotAllowed = 43569,

        AutomaticallyAssociateUETTag = 43570,

        InvalidSeedAudienceId = 43571,

        AudienceAccountInfoCannotBeUpdatedForOtherAccounts = 43572,

        AccountLevelAudienceAcccountInfoCannotBeNull = 43573,

        AccountIdIsRequiredWhenDeleteMultipleAccountLevelAudeince = 43574,

        SystemGeneratedListCouldNotBeDeleted = 43575,

        SystemGeneratedListIsReadOnly = 43576,

        SystemGeneratedListCouldNotBeAdded = 43577,

        CustomerNotEligibleForSystemGeneratedAudience = 43578,

        CustomerNotEligibleForRemarketingListBasedParameters = 43579,

        NotSupportAudienceSharedLibrary = 43580,

        TagCannotBeDeletedDueToExistingSystemGeneratedListAssociation = 43581,

        CustomerIsNotEligibleForImpressionBasedRemarketingList = 43582,

        CustomerCannotOperateImpressionBasedRemarketingList = 43583,

        InvalidEntityIdForImpressionBasedRemarketingList = 43584,

        InvalidEntityTypeForImpressionBasedRemarketingList = 43585,

        ImpressionBasedRemarketingListCanOnlyBeEditedByCreator = 43586,

        ImpressionBasedRemarketingListCanOnlyBeDeletedByCreator = 43587,

        DuplicatedEntityIdAndTypeForImpressionBasedRemarketingList = 43588,

        EntityCountExceededForImpressionBasedRemarketingList = 43589,

        #endregion

        #region Google Sync Error Code from 43601 to 43650

        GoogleSyncUserIsNotAuthorized = 43601,

        GoogleSyncInvalidCredentials = 43602,

        GoogleSyncNotAdWordsUser = 43603,

        GoogleSyncOAuthTokenRevoked = 43604,

        GoogleSyncInvalidCustomerId = 43605,

        GoogleSyncNone = 43606,

        GoogleSyncOperationCanceled = 43607,

        GoogleSyncUnknownError = 43608,

        GoogleSyncWebExceptionNameResolutionFailure = 43609,

        GoogleSyncWebExceptionProtocolError = 43610,

        GoogleSyncWebExceptionKeepAlive = 43611,

        GoogleSyncWebExceptionTimeout = 43612,

        GoogleSyncWebExceptionProxyNameResolutionFailure = 43613,

        GoogleSyncWebExceptionRequestProhibitedByProxy = 43614,

        GoogleSyncWebExceptionOther = 43615,

        GoogleSyncWebExceptionProxyAuthenticationRequired = 43616,

        GoogleSyncQuotaNotAvailable = 43617,

        GoogleSyncApiInternalError = 43618,

        GoogleSyncSizeLimitError = 43619,

        GoogleSyncTooMuchDataToTransmit = 43620,

        GoogleSyncInvalidParameter = 43621,

        GoogleSyncAccountNotSetUp = 43622,

        GoogleSyncOAuth2RefreshTokenMissing = 43623,

        GoogleSyncUserPermissionDenied = 43624,

        GoogleSyncFeedItemHasInvalidCharacter = 43625,

        GoogleSyncTwoStepVerificationNotEnrolled = 43626,
        #endregion

        #region Facebook Sync Error Code from 43651 to 43700

        FacebookSyncTokenExpired = 43651,

        FacebookSyncPermissionDenied = 43652,

        FacebookSyncThrottlingLimitReached = 43653,

        FacebookSyncApiInternalError = 43654,

        #endregion

        #region CampaignCriterion errors from 43701 to 43899

        ProductCampaignCriterionPilotNotEnabledForCustomer = 43701,

        CampaignCriterionsNullOrEmpty = 43702,

        CampaignCriterionsEntityLimitExceeded = 43703,

        CampaignCriterionTypeInvalid = 43705,

        CampaignCriterionIsNull = 43706,

        CampaignCriterionIdShouldBeNullOnAdd = 43707,

        DuplicateCampaignCriterionId = 43708,


        [DbErrorCodeMapping(-104038)]
        [ErrorField(EntityField.CampaignCriterionId)]
        CampaignCriterionIdInvalid = 43709,

        CampaignCriterionInvalidConditionType = 43710,

        CampaignTypeIsNotShoppingCampaign = 43711,

        CampaignCriterionCriterionIsNullOrEmpty = 43712,

        CampaignCriterionTooManyConditions = 43713,

        CampaignCriterionDuplicateCampaignId = 43714,

        CampaignCriterionIdArrayNullOrEmpty = 43715,

        CampaignCriterionIdListExceedsLimit = 43716,

        ProductConditionIsNull = 43717,

        CampaignCriterionProductConditionDuplicateOperand = 43718,

        [DbErrorCodeMapping(-104037)]
        ProductCampaignCriterionAlreadyExist = 43719,

        AllNullCampaignCriterionTypesNotAllowedOnCreate = 43800,

        InvalidCampaignCriterionType = 43801,

        ProductCampaignCriterionCrierionIsNullOrEmpty = 43802,

        ProductCampaignCriterionTooManyConditions = 43803,

        CampaignCriterionActionsNullOrEmpty = 43804,

        CampaignCriterionBidMultiplierValueNotSet = 43805,

        CampaignAlreadyHasAdGroupAudienceCriterion = 43806,

        DuplicateCampaignCriterionAudienceAssociation = 43807,

        IllegalAudienceAssociationConversionFromExclusion = 43808,

        CampaignCriterionAudienceAssociationNotExist = 43809,

        CampaignCriterionBidMultiplierAndCashbackAdjustmentValueNotSet = 43810,
        #endregion

        #region MultiAccountBulkUpload Codes from 43900 to 44000
        /// <summary>
        /// Occurs when size check in validation exceeds configured max
        /// </summary>
        MultiAccountBulkUploadFileSizeExceeded = 43901,

        /// <summary>
        /// Parsing error when no Account ID column is present in passed in file
        /// </summary>
        MultiAccountBulkUploadAccountIdColumnNotFound = 43902,

        /// <summary>
        /// Parsring error when there are no entries in the uploaded zip file.
        /// </summary>
        MultiAccountBulkUploadZipFileEmpty = 43903,

        /// <summary>
        /// Parsring error when there are multiple csv files in the uploaded zip file.
        /// </summary>
        MultiAccountBulkUploadMultipleFilesInZipFile = 43904,

        /// <summary>
        /// Parsring error when there are multiple csv files in the uploaded zip file.
        /// </summary>
        MultiAccountBulkUploadAccountCountExceeded = 43905,
        #endregion

        #region Upgraded Urls codes from 44001 to 44100 - some values are missing, as they can be reused from other sections

        // CommonErrorCode from 44001 to 44030
        InvalidUrlScheme = 44001,
        TrackingTemplateTooLong = 44002,
        MissingLandingPageUrlTag = 44003,
        CountExceedsLimit = 44004,
        InvalidCharactersInKey = 44005,
        [DbErrorCodeMapping(-102726)]
        InvalidCharactersInValue = 44006,
        InvalidTag = 44007,
        InvalidOsType = 44008,
        KeyTooLong = 44009,
        UrlTooLong = 44010,
        DuplicateOsTypeForAppUrl = 44011,
        KeyNullOrEmpty = 44012,
        ValueNullOrEmpty = 44013,
        ParameterIsNull = 44014,
        DuplicatesInField = 44015,
        ValueImmutable = 44016,
        DuplicatedTextField = 44017,
        GeneratePuidInfoError = 44018,
        ManualTaggingDetectedInQueryParameters = 44019,

        // Others start from 44031 to 44100
        UpgradedUrlsPilotNotEnabledForCustomer = 44031,
        FinalUrlRequiredWhenUsingTrackingUrlTemplateOrUrlCustomParameter = 44032,
        FinalUrlRequiredWhenUsingMobileFinalUrl = 44033,
        MobileFinalUrlNotAllowedWithMobileDevicePreference = 44034,
        BothDestinationUrlAndFinalUrlNotAllowed = 44035,
        BothDestinationUrlAndUrlCustomParameterNotAllowed = 44036,
        BothDestinationUrlAndTrackingTemplateNotAllowed = 44037,
        FinalUrlAndMobileUrlNotAllowedForProductPartition = 44038,
        TemplateAndParametersNotAllowedForProductPartitionSubdivision = 44039,
        InvalidCustomParametersText = 44040,
        UpgradedUrlsPilotNotEnabledCustomParametersNotImported = 44041,
        CustomParametersNotFlattenedToDestinationUrl = 44042,
        CustomerNotEnabledForFinalUrlSuffixPhase1Pilot = 44043,
        CustomerNotEnabledForFinalUrlSuffixPhase2Pilot = 44044,
        FinalUrlRequiredWhenUsingFinalUrlSuffix = 44045,
        #endregion

        #region PLA to BSC Migration codes from 44101 to 44200

        [DbErrorCodeMapping(-100767)]
        [DbErrorCodeMapping(-101477)]
        AnActiveRuleAlreadyExists = 44101,

        [DbErrorCodeMapping(-104041)]
        OnlyOneActiveNodeIsAllowed = 44102,

        #endregion

        #region Auto-Bidding error codes 44200 - 44299
        BiddingSchemeNotEnabledForCustomer = 44200,
        UnsupportedBiddingScheme = 44201,
        TargetCpaIsRequired = 44202,
        MaxCpcExceedsMonthlyBudget = 44203,
        InheritFromParentBiddingSchemeNotAllowedForCampaign = 44204,
        SupportOnlyManualAndInheritFromParentBiddingStrategy = 44205,
        EnhancedCpcNotAllowedForShoppingCampaign = 44206,
        UnsupportedGoogleBidStrategyType = 44207,
        MaxCpcLessThanOrEqualToZero = 44208,
        TargetCpaLessThanOrEqualToZero = 44209,
        TargetCpaExceedsMonthlyBudget = 44210,
        UnsupportedBiddingSchemeForMonthlyBudgetType = 44211,
        MaxCpcBidAmountsLessThanFloorPrice = 44212,
        MaxCpcBidsAmountsGreaterThanCeilingPrice = 44213,
        TargetCpaBidAmountsLessThanFloorPrice = 44214,
        TargetCpaBidsAmountsGreaterThanCeilingPrice = 44215,
        NoEnoughConversionForMaxConversionsBiddingScheme = 44216,
        NoEnoughConversionForTargetCpaBiddingScheme = 44217,
        BidUpdatingNotAllowedForAutoBiddingKeyword = 44218,
        BidUpdatingNotAllowedForAutoBiddingAdGroup = 44219,
        MaxConversionAndSharedBudgetAreMutuallyExclusive = 44220,
        TargetCpaAndSharedBudgetAreMutuallyExclusive = 44221,
        MaxConversionsNotEnabledForDynamicSearchAds = 44222,
        TargetCpaNotEnabledForDynamicSearchAds = 44223,
        MaxConversionsNotEnabledForTheMarkets = 44224,
        TargetCpaNotEnabledForTheMarkets = 44225,
        BiddingSchemeAndSharedBudgetAreMutuallyExclusive = 44226,
        SharedBudgetAndBiddingSchemeAreMutuallyExclusive = 44227,
        BiddingSchemeNotEnabledForTheLocations = 44228,
        LocationNotEnabledForTheBiddingScheme = 44229,
        BidStrategyUnchangedBecauseNoEnoughConversions = 44230,
        // TargetROAS
        NotEnoughConversionsForTargetRoasBiddingScheme = 44231,
        NotEnoughRevenueForTargetRoasBiddingScheme = 44232,
        InvalidTargetRoasValue = 44234,
        CustomerNotInPilotForTargetRoas = 44235,
        BidStrategyUnchangedBecauseNotEnoughRevenue = 44236,
        BidUpdatingNotAllowedForAutoBiddingProductGroup = 44237,
        // MaxConversionValue
        ConversionGoalCriteriaNotMetForBiddingScheme = 44238,
        CustomerNotInPilotForMaxConversionValue = 44239,
        // TargetImpressionShare
        [ErrorField(EntityField.TargetAdPosition)]
        InvalidTargetAdPositionValue = 44240,

        [ErrorField(EntityField.TargetImpressionShare)]
        TargetImpressionShareIsRequired = 44241,

        [ErrorField(EntityField.TargetImpressionShare)]
        InvalidTargetImpressionShareValue = 44242,

        CustomerNotInPilotForTargetImpressionShare = 44243,

        BidStrategyUpdatedButBidNotEnabledForUpdate = 44245,

        CrossAccountPortfolioBidStrategyAreNotSupported = 44246,

        TargetCostPerSaleInvalid = 44247,
        CostPerSaleBiddingSchemeCannotBeUpdated = 44248,
        InvalidShoppingCampaignPriorityForCostPerSale = 44249,
        BidCannotBeManagedForBiddingScheme = 44250,
        InvalidMaxCpcValue = 44251,
        InvalidTargetCpaValue = 44252,
        ImmutableBiddingScheme = 44253,
        DailyBudgetAmountLessThanTargetCostPerSale = 44254,
        CostPerSaleNotWorkWithShoppableAds = 44255,
        InvalidManualCpcValue = 44256,
        ManualCpcIsRequired = 44257,
        ManualCpcLessThanOrEqualToZero = 44258,
        MaxCpmLessThanOrEqualToZero = 44259,
        InvalidMaxCpmValue = 44260,
        MaxCpcGreaterThanCeiling = 44261,
        MaxCpmGreaterThanCeiling = 44262,

        #endregion

        #region Labels error codes 44300 - 44399
        LabelsListIsNullOrEmpty = 44300,

        [DbErrorCodeMapping(-101751)]
        [DbErrorCodeMapping(-101801)]
        LabelsEntityLimitExceeded = 44301,

        LabelIsNull = 44302,

        [ErrorField(EntityField.LabelId)]
        LabelIdShouldBeNullOnAdd = 44303,

        [ErrorField(EntityField.LabelId)]
        DuplicateLabelId = 44304,

        [DbErrorCodeMapping(-101803)]
        [DbErrorCodeMapping(-101804)]
        [DbErrorCodeMapping(-101805)]
        [DbErrorCodeMapping(-101753)]
        [DbErrorCodeMapping(-101754)]
        [DbErrorCodeMapping(-101755)]
        [ErrorField(EntityField.LabelId)]
        LabelIdInvalid = 44305,

        [ErrorField(EntityField.LabelName)]
        LabelNameInvalid = 44306,

        [ErrorField(EntityField.LabelName)]
        LabelNameLengthExceeded = 44307,

        [DbErrorCodeMapping(-101802)]
        [DbErrorCodeMapping(-101752)]
        [ErrorField(EntityField.LabelName)]
        LabelNameDuplicate = 44308,

        [ErrorField(EntityField.LabelDescription)]
        LabelDescriptionLengthExceeded = 44309,

        [ErrorField(EntityField.LabelColor)]
        LabelColorCodeInvalid = 44310,

        [ErrorField(EntityField.LabelDescription)]
        LabelDescriptionInvalid = 44311,

        LabelPilotNotEnabledForCustomer = 44312,

        LabelAssociationListIsNullOrEmpty = 44313,

        LabelAssociationsBatchLimitExceeded = 44314,

        LabelAssociationIsNull = 44315,

        [DbErrorCodeMapping(-101806)]
        [DbErrorCodeMapping(-101756)]
        DuplicateLabelAssociation = 44316,

        [DbErrorCodeMapping(-101807)]
        [DbErrorCodeMapping(-101757)]
        LabelAssociationsPerEntityLimitExceeded = 44317,

        [DbErrorCodeMapping(-101808)]
        [DbErrorCodeMapping(-101758)]
        LabelAssociationDoesNotExist = 44318,

        LabelCampaignAssociationsAccountLimitExceeded = 44319,

        LabelAdGroupAssociationsAccountLimitExceeded = 44320,

        LabelAdAssociationsAccountLimitExceeded = 44321,

        LabelKeywordAssociationsAccountLimitExceeded = 44322,

        LabelBatchLimitExceeded = 44323,

        LabelAssociationEntityTypeInvalid = 44324,

        LabelAssociationEntityIdInvalid = 44325,

        LoadLabelAssociationsEntityIdInvalid = 44326,

        LabelIdsBatchLimitExceeded = 44327,
        #endregion

        #region Shared-Budget Error Codes 44400 - 44499

        [DbErrorCodeMapping(-106001)] // Invalid Budget Id
        [DbErrorCodeMapping(-106002)] // Budget Id passed in does not belong to the Account
        BudgetIdInvalid = 44400,

        BudgetNameTooLong = 44401,
        MonthlyBudgetNotAllowed = 44402,
        BudgetAmountMissing = 44403,
        BudgetAmountIsAboveLimit = 44404,
        BudgetAmountIsBelowLimit = 44405,
        BudgetPilotNotEnabledForCustomer = 44406,
        BudgetIdShouldBeNullOnAdd = 44407,
        BudgetNameMissing = 44408,

        [DbErrorCodeMapping(-106000)]
        DuplicateBudgetName = 44409,

        [DbErrorCodeMapping(-106003)]
        BudgetEntityLimitExceeded = 44410,
        BudgetsNullOrEmpty = 44411,
        BudgetOperationsBatchLimitExceeded = 44412,
        DuplicateBudgetId = 44413,
        BudgetNameHasInvalidChars = 44414,
        BudgetIsNull = 44415,
        BudgetTypeCannotBeNullOnAdd = 44416,
        BudgetTypeSwitchFromSharedToMonthlyNotSupported = 44417,
        DailyBudgetAmountInvalidForSharedBudget = 44418,
        MonthlyBudgetAmountInvalidForSharedBudget = 44419,
        BudgetTypeInvalidForSharedBudget = 44420,
        CannotUpdateBudgetAndCampaignInSameCall = 44421,

        [DbErrorCodeMapping(-106004)]
        BudgetIsSharedWithCampaigns = 44422,

        [DbErrorCodeMapping(-106005)]
        CampaignIsUsingSharedBudget = 44423,

        SharedBudgetNotAllowedWithPerformanceTargetCampaign = 44424,

        #endregion

        #region DynamicSearch Ads codes from 44500 - 44599
        CustomerNotEligibleForDynamicSearchAds = 44500,
        CampaignInvalidDynamicSearchAdsDomainName = 44501,
        CampaignInvalidDynamicSearchAdsLanguage = 44502,
        DynamicSearchAdsSettingNotAllowedForUpdate = 44503,
        EntityNotAllowedForDynamicSearchAdsCampaign = 44504,
        DynamicSearchAdNotAllowedForNonDynamicSearchAdsCampaign = 44505,
        BiddingSchemeMustInheritFromParentEntity = 44506,
        OnlyCampaignLevelNegativeDsaTargetRequestAllowed = 44507,
        SubstitutionNotSupportedForDynamicSearchAd = 44508,

        [DbErrorCodeMapping(-104049)]
        MaxCampaignWebpageCriterionsLimitExceededForCampaign = 44509,

        [ErrorField(EntityField.AdPath1)]
        DynamicSearchAdPath1TooLong = 44510,

        [ErrorField(EntityField.AdPath1)]
        DynamicSearchAdPath1Invalid = 44511,

        [ErrorField(EntityField.AdPath2)]
        DynamicSearchAdPath2TooLong = 44512,

        [ErrorField(EntityField.AdPath2)]
        DynamicSearchAdPath2Invalid = 44513,

        DynamicSearchAdPath2SetWithoutPath1 = 44514,

        BiddableOrNegativeStatusCannotBeUpdated = 44515,

        UpdateCampaignTypeAsSearchForExistingDSACampaignNotAllowed = 44516,

        UpdateCampaignTypeAsDSAForExistingSearchCampaignNotAllowed = 44517,

        DynamicSearchAdTextPart2PilotNotEnabledForCustomer = 44518,

        [ErrorField(EntityField.AdTextPart2)]
        DynamicSearchAdTextPart2TooLong = 44519,

        [ErrorField(EntityField.AdTextPart2)]
        DynamicSearchAdTextPart2Invalid = 44520,

        DSADomainLanguagesPhase2PilotNotEnabledForCustomer = 44521,
        DSADomainLanguagesPhase3PilotNotEnabledForCustomer = 44522,
        DSADomainLanguagesPhase4PilotNotEnabledForCustomer = 44551,

        //MixedDSACampaign 44523 - 44550
        CampaignServiceAccountNotInPilotForMixedModeCampaign = 44523,
        CampaignServiceInvalidAdGroupTypeForCampaignType = 44524,
        CampaignServiceCannotAddDynamicSearchAdGroupToCampaignWithoutDynamicSearchSettings = 44525,
        CampaignServiceDynamicSearchAdNotAllowedForNonDynamicSearchAdsAdGroup = 44526,
        CampaignServiceAdTypeInvalidForAdGroup = 44527,
        CampaignServiceInvalidCriterionForCampaignType = 44528,
        CampaignServiceInvalidCriterionForAdGroupType = 44529,
        CampaignServiceDynamicSearchAdsNotSupportedForDisclaimerCampaign = 44530,
        CampaignServiceEntityNotAllowedForDynamicSearchAdsAdGroup = 44531,
        CampaignServiceExperimentDoesNotSupportMixedModeCampaign = 44532,
        CampaignServiceDSASettingCannotBeAddedToExperimentRelatedCampaign = 44533,

        [DbErrorCodeMapping(-110000)]
        CampaignServiceAdGroupTypeInvalid = 44534,
        CampaignServiceCannotAddCriterionToCampaignWithoutDynamicSearchSettings = 44535,
        [DbErrorCodeMapping(-110001)]
        CampaignServiceAdvertisingChannelTypeIdInvalid = 44536,
        CampaignServiceMaxDSAAutoTargetPerAccountLimitReached = 44537,
        CampaignServiceDynamicSearchAdCampaignCreationNotAllowed = 44538,
        AccountNotEligibleForDynamicDescription = 44539,
        #endregion

        #region Webpage Criterion codes from 44600 - 44699

        CampaignTypeIsNotDynamicSearchAdsCampaign = 44600,
        WebpageCriterionParameterIsNull = 44601,
        WebpageCriterionNameInvalid = 44602,
        WebpageCriterionConditionsContainDuplicateValues = 44603,
        TooManyWebpageCriterionConditions = 44604,
        WebpageCriterionConditionInvalid = 44605,
        WebpageCriterionWebpageConditionOperandInvalid = 44606,
        WebpageCriterionWebpageConditionArgumentInvalid = 44607,
        NegativeWebpageCriterionConditionIsNullOrEmpty = 44608,
        CannotUpdateCriterionForWebpageCriterion = 44609,
        IllegalCampaignCriterionType = 44610,

        [DbErrorCodeMapping(-104051)]
        [DbErrorCodeMapping(-104050)]
        WebpageCriterionAlreadyExists = 44611,
        InvalidUpgradedUrlsForWebpageCriterion = 44612,
        BidUpdatingNotAllowedForAutoBiddingAutoTargets = 44613,
        WebpageCriterionWebpageConditionOperatorInvalid = 44614,
        WebpageCriterionWebpageConditionUrlEqualsValueDoesNotMatchDomain = 44615,
        AccountIsNotInPilotForWebpageCriterionWebpageConditionOperatorOrUrlEquals = 44616,
        #endregion

        #region Campaign Language 44700 - 44799
        CampaignLanguagesNotEnabledForCustomer = 44700,
        AllCampaignLanguagesCannotBeRemoved = 44701,
        CampaignLanguageSpecifiedMoreThanOnce = 44702,
        LanguageUpdateNotAllowed = 44703,

        #endregion

        #region Price Extension 44800 - 44899
        DuplicateHeaders = 44800,
        TooFewPriceTableRows = 44801,
        NegativePrice = 44802,
        CurrencyCodeNotSupported = 44803,

        #endregion

        #region Offline Conversion 44900 - 44999
        /// <summary>
        /// Occurs when size check in validation exceeds configured max
        /// </summary>
        OfflineConversionUploadFileSizeExceeded = 44900,

        /// <summary>
        /// Parsing error when no Account ID column is present in passed in file
        /// </summary>
        OfflineConversionUploadAccountIdColumnNotFound = 44901,

        /// <summary>
        /// Parsring error when there are no entries in the uploaded zip file.
        /// </summary>
        OfflineConversionUploadZipFileEmpty = 44902,

        /// <summary>
        /// Parsring error when there are multiple csv files in the uploaded zip file.
        /// </summary>
        OfflineConversionUploadMultipleFilesInZipFile = 44903,

        /// <summary>
        /// Offline conversion file format invalid
        /// </summary>
        OfflineConversionFileFormatInvalid = 44904,

        /// <summary>
        /// Offline conversion file Timezone invalid
        /// </summary>
        OfflineConversionTimezoneInvalid = 44905,

        /// <summary>
        /// Offline conversion file headers row is not found
        /// </summary>
        OfflineConversionHeadersRowNotFound = 44906,

        /// <summary>
        /// Offline conversion file column Microsoft Click Id not found
        /// </summary>
        OfflineConversionColumnMicrosoftClickIdNotFound = 44907,

        /// <summary>
        /// Offline conversion file column Conversion Name not found
        /// </summary>
        OfflineConversionColumnConversionNameNotFound = 44908,

        /// <summary>
        /// Offline conversion file column Conversion Time not found
        /// </summary>
        OfflineConversionColumnConversionTimeNotFound = 44909,

        /// <summary>
        /// Offline conversion file column Conversion Value not found
        /// </summary>
        OfflineConversionColumnConversionValueNotFound = 44910,

        /// <summary>
        /// Offline conversion file column Conversion Currency not found
        /// </summary>
        OfflineConversionColumnConversionCurrencyNotFound = 44911,

        /// <summary>
        /// Offline conversion file do not sent to g-server correctly
        /// </summary>
        OfflineConversionGServerError = 44912,

        /// <summary>
        /// The number of offline conversions exceeds the limit
        /// </summary>
        OfflineConversionBatchSizeExceedsLimit = 44913,

        /// <summary>
        /// The MSClickId of offline conversion is null or empty
        /// </summary>
        OfflineConversionMSClickIdNullOrEmpty = 44914,

        /// <summary>
        /// The MSClickId of offline conversion is invalid
        /// </summary>
        OfflineConversionMSClickIdInvalid = 44915,

        /// <summary>
        /// The conversion name of offline conversion is null or empty
        /// </summary>
        OfflineConversionNameNullOrEmpty = 44916,

        /// <summary>
        /// The conversion name of offline conversion is invalid
        /// </summary>
        OfflineConversionNameInvalid = 44917,

        /// <summary>
        /// The conversion time of offline conversion is null or empty
        /// </summary>
        OfflineConversionTimeNullOrEmpty = 44918,

        /// <summary>
        /// The conversion time of offline conversion is invalid
        /// </summary>
        OfflineConversionTimeInvalid = 44919,

        /// <summary>
        /// The conversion time is out of the conversion window
        /// </summary>
        OfflineConversionTimeOutOfWindow = 44920,

        /// <summary>
        /// There is no available time zone information neither in request parameter nor in conversion time
        /// </summary>
        OfflineConversionTimeZoneUnspecified = 44921,

        /// <summary>
        /// The conversion value of offline conversion is invalid
        /// </summary>
        OfflineConversionValueInvalid = 44922,

        /// <summary>
        /// The currency code of offline conversion is invalid
        /// </summary>
        OfflineConversionCurrencyCodeInvalid = 44923,

        /// <summary>
        /// The list of offline conversions cannot be null or empty
        /// </summary>
        OfflineConversionsNullOrEmpty = 44924,

        /// <summary>
        /// The offline conversion cannot be null
        /// </summary>
        OfflineConversionIsNull = 44925,

        /// <summary>
        /// A future conversion time cannot be set.
        /// </summary>
        FutureConversionTimeCannotBeSet = 44927,

        /// <summary>
        /// The conversion goal was created too recently.
        /// Offline conversion goal should be created at least two hours earlier than importing offline conversions.
        /// </summary>
        OfflineConversionGoalTooRecent = 44928,

        /// <summary>
        /// Click Time is later than conversion time
        /// </summary>
        ConversionTimeEarlierThanClickTime = 44929,

        /// <summary>
        /// Click Id outside the conversion window of the goal
        /// </summary>
        ClickIdDateTimeOutsideGoalConversionWindow = 44930,

        OfflineConversionInvalidAdjustmentType = 44931,

        OfflineConversionAdjustmentTimeNullOrEmpty = 44932,

        OfflineConversionAdjustmentTimeInvalid = 44933,

        OfflineConversionFutureAdjustmentTimeCannotBeSet = 44934,

        OfflineConversionAdjustmentTimeOutOfWindow = 44935,

        OfflineConversionAdjustmentValueNotExpected = 44936,

        OfflineConversionAdjustmentValueRequired = 44937,

        OfflineConversionCurrencyValueRequired = 44938,

        OfflineConversionAdjustmentTimeEarlierThanConversionTime = 44939,

        OfflineConversionRestateRetractNotSupported = 44940,

        ScheduledOfflineConversionUploadUnableToFetchFile = 44941,

        OfflineConversionAdjustmentColumnsNotExpectedInHeader = 44942,

        OfflineConversionAdditionColumnsNotExpectedInHeader = 44943,

        ShouldAcceptTermsBeforeUsingEnhancedConversions = 44944,

        NotEligibleForEnhancedConversions = 44945,

        ConversionEmailAddressIsNotHashed = 44946,

        ConversionPhoneNumberIsNotHashed = 44947,

        CustomerNotEligibleForAutoConversion = 44948,

        InvalidGoalSource = 44949,

        GoalTagCannotBeChanged = 44950,

        GoalCategoryCannotBeChanged = 44951,

        #endregion

        #region Online Conversion 45000 - 45999
        OnlineConversionIsNull = 45000,

        OnlineConversionAdjustmentValueInvalid = 45001,

        OnlineConversionCurrencyCodeInvalid = 45002,

        OnlineConversionAdjustmentTimeInvalid = 45003,

        OnlineConversionFutureAdjustmentTimeCannotBeSet = 45004,

        OnlineConversionsNullOrEmpty = 45005,

        OnlineConversionBatchSizeExceedsLimit = 45006,

        OnlineConversionAdjustmentValueNotExpected = 45007,

        OnlineConversionAdjustmentValueRequired = 45008,

        OnlineConversionAdjustmentTypeIsNull = 45009,

        OnlineConversionInvalidAdjustmentType = 45010,

        OnlineConversionNameNullOrEmpty = 45011,

        TransactionIdIsNull = 45012,

        TransactionIdIsInvalid = 45013,

        OnlineConversionInvalidTimeZone = 45014,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        UploadedConversionFileIsEmpty = 45015,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        CantDistinguishUploadedConversionType = 45016,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        UploadedConversionFileHeadersRowNotFound = 45017,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        OnlineConversionInvalidHeader = 45018,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        OnlineConversionTransactionIdNotFound = 45019,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        OnlineConversionConversionTimeNotExpected = 45020,

        OnlineConversionNotEnabled = 45021,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        CantGetRowCountFromUploadedFile = 45022,
        // When UI is ready. Update some error codes using it. For UI & Bulk
        UploadedConversionFileTimeZoneInvalid = 45023,
        // When UI is ready. Update some error codes using it. For UI
        OnlineConversionCanOnlyBeAdjusted = 45024,
        // SOAP API Error 
        OnlineConversionNameNotFound = 45025,
        OnlineConversionHashedEmailAddressNotExpected = 45026,
        OnlineConversionHashedEmailPhoneNumberNotExpected = 45027,
        #endregion

        #region Travel 60000-60100
        CustomerNotEligibleForHotelAds = 60000,
        TooManyValuesInRequest = 60002,
        TooManyValues = 60003,
        DuplicateValues = 60004,

        [DbErrorCodeMapping(-101904)] // Cannot update the BidMultipliers for the Entity need to be delete
        [DbErrorCodeMapping(-101907)] // BidMultipliers cannot be cleared and updated for an entity in the same call
        InvalidBidMultiplier = 60005,

        ImmutableProperty = 60006,

        InvalidSubAccount = 60007,

        [DbErrorCodeMapping(-101905)] // A VerticalCampaign with the specified name already exists under the account
        [ErrorField(EntityField.SubAccountName)]
        SubAccountWithNameAlreadyExists = 60008,

        [DbErrorCodeMapping(-101901)] // A VerticalCampaign with the specified id does not exist
        VerticalCampaignNotExist = 60009,

        [DbErrorCodeMapping(-101902)] // VerticalCampaign is deleted
        VerticalCampaignIsDeleted = 60010,

        [DbErrorCodeMapping(-101906)] // Limit on the number of active VerticalCampaigns for an account reached
        MaxActiveVerticalCampaignsLimitReached = 60011,

        [DbErrorCodeMapping(-101908)] // A VerticalItem with the specified id does not exist
        [DbErrorCodeMapping(-101919)]
        VerticalItemNotExist = 60012,

        [DbErrorCodeMapping(-101909)] // VerticalItem is deleted
        VerticalItemIsDeleted = 60013,

        [DbErrorCodeMapping(-101910)] // No default VerticalItemGroup exists for the Customer
        NoDefaultVerticalItemGroupExists = 60014,

        [DbErrorCodeMapping(-101912)] // A VerticalItemGroup with the specified id does not exist
        VerticalItemGroupNotExist = 60015,

        InvalidHotelGroup = 600016,

        [DbErrorCodeMapping(-101914)] // A Vertical Hotel Group with the specified name already exists under the account
        [ErrorField(EntityField.HotelGroupName)]
        HotelGroupWithNameAlreadyExists = 600017,

        [DbErrorCodeMapping(-101913)] // VerticalCampaign is deleted
        VerticalItemGroupIsDeleted = 60018,

        [DbErrorCodeMapping(-101915)] // Default Vertical Item Group cannot be updated
        DefaultVerticalItemGroupNotAllowedToUpdate = 60019,

        UnsupportedAssociationType = 60020,
        DuplicateHotelId = 60021,

        [DbErrorCodeMapping(-101916)] //NonEmpty VerticalItem group can NOT be deleted
        HotelGroupHasActiveAssociations = 60022,

        /// <summary>
        /// The TaskType parameter for Pps task is invalid
        /// </summary>
        InvalidPpsTaskType = 60023,

        /// <summary>
        /// The file size of the uploaded report is above the accepted threshold
        /// </summary>
        PpsReconciliationReportUploadFileSizeExceeded = 60024,

        /// <summary>
        /// Error while downloading file from blob storage
        /// </summary>
        PpsReconciliationReportUploadBlobDownloadError = 60025,

        /// <summary>
        /// Error while uploading file to blob storage
        /// </summary>
        PpsReconciliationReportUploadBlobUploadError = 60026,

        PpsReconciliationReportFileFormatInvalid = 60027,

        PpsReconciliationReportFileMissingHeaderRow = 60028,

        PpsReconciliationReportServiceFeeColumnsPartiallyNotFound = 60029,

        PpsReconciliationReportUploadZipFileEmpty = 60030,

        PpsReconciliationReportColumnHotelIDNotFound = 60031,

        PpsReconciliationReportColumnHotelNameNotFound = 60032,

        PpsReconciliationReportColumnHotelAddressNotFound = 60033,

        PpsReconciliationReportColumnHotelCityNotFound = 60034,

        PpsReconciliationReportColumnHotelStateRegionNotFound = 60035,

        PpsReconciliationReportColumnHotelPostalCodeNotFound = 60036,

        PpsReconciliationReportColumnHotelCountryCodeNotFound = 60037,

        PpsReconciliationReportColumnHotelPhoneNumberNotFound = 60038,

        PpsReconciliationReportColumnBookingReferenceNotFound = 60039,

        PpsReconciliationReportColumnBookingDateandTimeNotFound = 60040,

        PpsReconciliationReportColumnCheckinDateNotFound = 60041,

        PpsReconciliationReportColumnCheckoutDateNotFound = 60042,

        PpsReconciliationReportColumnNumberofRoomsNotFound = 60043,

        PpsReconciliationReportColumnNumberofGuestsNotFound = 60044,

        PpsReconciliationReportColumnBookingRevenueNotFound = 60045,

        PpsReconciliationReportColumnBookingRevenueCurrencyNotFound = 60046,

        PpsReconciliationReportColumnBookingRevenueCurrencytoBillingCurrencyConversionRateNotFound = 60047,

        PpsReconciliationReportColumnBookingStatusNotFound = 60048,

        PpsReconciliationReportColumnCommissionNotFound = 60049,

        PpsReconciliationReportColumnCommissionCurrencyNotFound = 60050,

        PpsReconciliationReportColumnCommissionCurrencytoBillingCurrencyConversionRateNotFound = 60051,

        PpsReconciliationReportColumnPaymentDateNotFound = 60052,

        PpsReconciliationReportColumnPaymentStatusNotFound = 60053,

        PpsReconciliationReportParseFailedInvalidValuesInDataRows = 60054,

        PpsReconciliationReportColumnServiceFeeNotFound = 60055,

        PpsReconciliationReportColumnServiceFeeCurrencyNotFound = 60056,

        PpsReconciliationReportColumnServiceFeeCurrencytoBillingCurrencyConversionRateNotFound = 60057,

        PpsReconciliationReportColumnTotalPaymentNotFound = 60058,

        PpsReconciliationReportColumnBillingCurrencyNotFound = 60059,

        HotelCampaignNotEnabledForAccount = 60060,

        PpsReconciliationReportColumnProcessedDateTimeNotFound = 60061,

        PpsReconciliationReportColumnStatusNotFound = 60062,

        PpsReconciliationReportColumnErrorNotFound = 60063,

        PpsReconciliationReportColumnAccountIdNotFound = 60064,

        PpsReconciliationReportColumnUploadDateTimeNotFound = 60065,

        PpsReconciliationReportProcessedAccountIdsMismatch = 60066,

        PpsReconciliationReportInvalidProcessedDateTimeFormat = 60067,

        PpsReconciliationReportBlobStorageError = 60068,

        PpsReconciliationReportTaskCreationError = 60069,

        PpsReconciliationReportEOError = 60070,

        SwitchingofBidTypeFromPPStoNonPPSAndViceVersaIsNotAllowed = 60071,

        OnlySupportedBMValueIsDecreaseby100OrIncreaseby0 = 60072,

        HotelCampaignDeprecated = 60073,

        #endregion

        #region CloudImport 60100-60200
        CloudImportTimeOut = 60100,
        #endregion

        #region AIM 60201 - 60249
        InvalidProfileTargetBidAdjustment = 60201,
        EntityNotAllowedForAudienceCampaign = 60207,

        CustomerNotEligibleForAudienceCampaign = 60208,
        CampaignLanguageShouldIncludeAll = 60209,
        [DbErrorCodeMapping(-197600)]
        NotSupportedForThisCampaignType = 60210,
        AdGroupLanguageNotSupported = 60211,
        AdModificationNotAllowedOnThisCampaign = 60212,
        OnlyOneProductScopeCampaignCriterionIsAllowed = 60213,
        UnSupportedProductTypeForCampaignProductFilter = 60214,

        InvalidFunctionFormat = 60215,
        UnknownFunction = 60216,
        MissingDelimiterBetweenFunctions = 60217,
        CountDownInvalidDateTime = 60218,
        CountDownInvalidDaysBefore = 60219,
        CountDownDaysBeforeOutOfRange = 60220,
        CountDownPastDateTime = 60221,
        CountDownInvalidDefaultText = 60222,
        CountDownInvalidLanguageCode = 60223,
        CountDownInvalidParameters = 60224,
        CustomerNotEligibleForProductAudience = 60225,

        CustomerNotEligibleForEnhancedResponsiveAd = 60226,
        ResponsiveAdInvalidImage = 60227,
        ResponsiveAdDuplicateImage = 60228,
        ResponsiveAdRequiredImageMissing = 60229,
        NotEligibleForAudienceCampaignSubType = 60230,
        LocationFunctionInvalidParameters = 60231,

        // video ads
        ResponsiveAdInvalidVideo = 60232,
        ResponsiveAdDuplicateVideo = 60233,
        ResponsiveAdRequiredVideoMissing = 60234,

        AccountNotEligibleForAutoBiddingForAudienceNetwork = 60235,
        IncludeAutoBiddingViewThroughConversionsValueInvalid = 60236,

        ResponsiveAdInvalidHeadlines = 60237,
        ResponsiveAdInvalidLongHeadlines = 60238,
        ResponsiveAdInvalidDescriptions = 60239,

        InvalidLeadGenSetting = 60240,
        CampaignLeadGenSettingIsImmutable = 60241,
        LeadGenCampaignOnlyAllowCPM = 60242,
        AccountNotEligibleForOptOutFromMCM = 60244,
        OptOutFromMCMValueInvalid = 60245,
        InvalidBoostPlacementSetting = 60246,
        AccountNotEligibleForBoostTargeting = 60247,
        EligibleTargetNeededForOptimizedTargetingOptout = 60248,
        NoHeadlinesQualifiedForMMA = 60249,


        #endregion

        #region Hotel Entity (60250-60299)

        MaxPercentCpcLessThanOrEqualToZero = 60250,

        MaxPercentCpcGreaterThanOneThousand = 60251,

        CommissionRateIsRequired = 60252,

        CommissionRateLessThanOrEqualToZero = 60253,

        CommissionRateGreaterThanOneHundred = 60254,

        MissingPercentCpcBiddingScheme = 60255,

        InvalidHotelSetting = 60256,

        EntityNotAllowedForHotelCampaign = 60257,

        CannotSetBiddingSchemeForHotelAdGroup = 60258,

        HotelSettingCanNotBeChanged = 60259,

        InvalidTargetTypeForCommisionBiddingScheme = 60260,

        #endregion

        #region Tip 60301-60399
        InvalidTipId = 60301,
        #endregion

        #region FeedService 60500-60599
        CustomerNotEligibleForFeedService = 60500,


        [ErrorField(EntityField.FeedName)]
        [DbErrorCodeMapping(-102701)]
        DuplicateFeedName = 60501,
        DuplicateFeedId = 60502,
        NoEntityExistsInRequest = 60503,
        InvalidFeedAttribute = 60504,

        [DbErrorCodeMapping(-102704)]
        [ErrorField(EntityField.FeedAttributeName)]
        DuplicateFeedAttributeName = 60505,

        [DbErrorCodeMapping(-102706)]
        [ErrorField(EntityField.FeedAttributeType)]
        InvalidFeedAttributeType = 60506,

        DuplicateFeedMappingId = 60507,

        InvalidFeedAttributeMapping = 60508,

        DuplicateFeedAttributeId = 60509,
        DuplicateFeedPropertyId = 60510,

        [DbErrorCodeMapping(-102736)]
        InvalidFeedType = 60511,

        CustomerNotEligibleForDsaPageFeed = 60512,

        CustomerNotEligibleForAdCustomizersFeed = 60513,

        DuplicateFeedItemId = 60514,
        [DbErrorCodeMapping(-102703)]
        TargetFeedStatusInvalid = 60515,
        CannotUpdateFeedAttributeKey = 60516,

        TargetFeedInvalid = 60517,

        [DbErrorCodeMapping(-102711)]
        InvalidFeedAssociationType = 60518,
        [DbErrorCodeMapping(-102709)]
        DuplicateFeedAssociation = 60519,
        [DbErrorCodeMapping(-102712)]
        FeedAssociationDoesNotExists = 60520,

        [DbErrorCodeMapping(-102725)]
        [ErrorField(EntityField.FeedMapping)]
        DuplicateFeedTypeMapping = 60521,

        CustomerNotEligibleForDynamicDataFeed = 60522,

        CustomerNotEligibleForDynamicDataAutosAggregateFeed = 60524,
        CustomerNotEligibleForDynamicDataAutosListingFeed = 60525,
        CustomerNotEligibleForDynamicDataCreditCardsFeed = 60526,
        CustomerNotEligibleForDynamicDataCruisesFeed = 60527,
        CustomerNotEligibleForDynamicDataCustomFeed = 60528,
        CustomerNotEligibleForDynamicDataEventsFeed = 60529,

        InvalidFeedItemAttributeValue = 60530,

        CustomerNotEligibleForDynamicDataHealthInsuranceFeed = 60531,
        CustomerNotEligibleForDynamicDataHotelsAndVacationRentalsFeed = 60532,
        CustomerNotEligibleForDynamicDataMortgageLendersFeed = 60533,
        CustomerNotEligibleForDynamicDataProfessionalServiceFeed = 60534,
        CustomerNotEligibleForDynamicDataToursAndActivitiesFeed = 60535,
        CustomerNotEligibleForDynamicDataDebitCardsFeed = 60536,
        CustomerNotEligibleForDynamicDataJobListingsFeed = 60537,

        [DbErrorCodeMapping(-102714)]
        FeedItemMaxLimitReached = 60540,

        InvalidBooleanFeedItemAttributeValue = 60550,
        InvalidInt64FeedItemAttributeValue = 60551,
        InvalidDoubleFeedItemAttributeValue = 60552,
        InvalidStringFeedItemAttributeValue = 60553,
        InvalidUrlFeedItemAttributeValue = 60554,
        InvalidDateTimeFeedItemAttributeValue = 60555,
        InvalidInt64ListFeedItemAttributeValue = 60556,
        InvalidDoubleListFeedItemAttributeValue = 60557,
        InvalidStringListFeedItemAttributeValue = 60558,
        InvalidBooleanListFeedItemAttributeValue = 60559,
        InvalidUrlListFeedItemAttributeValue = 60560,
        InvalidDateTimeListFeedItemAttributeValue = 60561,
        InvalidPriceFeedItemAttributeValue = 60562,

        [DbErrorCodeMapping(-102707)]
        DuplicateFeedMappingForOneFeedType = 60563,

        [DbErrorCodeMapping(-102708)]
        [ErrorField(EntityField.FeedAttributeId)]
        InvalidFeedAttributeId = 60564,

        [DbErrorCodeMapping(-102702)]
        [ErrorField(EntityField.FeedId)]
        InvalidFeedId = 60565,

        [DbErrorCodeMapping(-102715)]
        [ErrorField(EntityField.FeedItemId)]
        InvalidFeedItemId = 60566,

        [DbErrorCodeMapping(-102716)]
        [ErrorField(EntityField.FeedItemStatus)]
        InvalidFeedItemLifeCycleStatus = 60567,

        [DbErrorCodeMapping(-102717)]
        [ErrorField(EntityField.FeedItemAttributeValue)]
        KeyFeedItemAttributeValueConfliction = 60568,

        [DbErrorCodeMapping(-102718)]
        [ErrorField(EntityField.FeedUploadHistory)]
        FeedUploadHistoryNotFound = 60569,

        [DbErrorCodeMapping(-102719)]
        [ErrorField(EntityField.FeedUploadHistory)]
        OperationOnInvalidFeedUploadApplyStatus = 60570,

        [DbErrorCodeMapping(-102720)]
        [ErrorField(EntityField.FeedUploadHistory)]
        DuplicateApplyOperationOnFeedUpload = 60571,

        [DbErrorCodeMapping(-102721)]
        [ErrorField(EntityField.FeedUploadHistory)]
        RejectOngoingApplyOperationOnFeedUpload = 60572,

        [ErrorField(EntityField.FeedUploadHistory)]
        InvalidFeedUploadHistory = 60573,

        [DbErrorCodeMapping(-102700)]
        AccountLevelFeedLimitationReached = 60580,
        [DbErrorCodeMapping(-102705)]
        AttributeLimitationPerFeedReached = 60581,
        [DbErrorCodeMapping(-102710)]
        FeedAssociationLimitationReached = 60582,

        [DbErrorCodeMapping(-102722)]
        MaxFeedIdCountExceeded = 60583,

        FeedReferencedInAd = 60584,

        OriginalFeedAttributesCannotBeDeleted = 60585,

        InvalidFeedCustomAttributesDefinitionText = 60586,
        DualFeedNotSupported = 60587,
        FeedTypeNotSupportedForBulkUpload = 60588,
        CannotUseStandardFeedAttributeName = 60589,
        CannotUseTargetingFeedAttributeName = 60590,
        KeyPropertyCannotBeUpdated = 60591,
        CustomIdAttributeShouldBeOfStringType = 60592,
        AttributeValueStringTooLong = 60593,

        [DbErrorCodeMapping(-102727)]
        InvalidFeedIdsForAssociation = 60594,

        CustomAttributeValuesEmpty = 60595,
        FeedItemIdNotGenerated = 60596,
        FeedItemBlobIdNotGenerated = 60597,
        FeedItemBlobBlobStorageError = 60598,
        InvalidFeedObject = 60599,
        #endregion

        #region FeedUploadService 60600-60699

        InvalidColumnHeaderInFeedUploadFile = 60600,
        BlankOrEmptyColumnHeaderInFeedUploadFile = 60601,
        DuplicateColumnHeadersInFeedUploadFile = 60602,
        RequiredColumnHeaderMissingInFeedUploadFile = 60603,
        IncompatibleColumnHeaderInFeedUploadFile = 60604,

        FeedUploadTaskTimeout = 60605,

        InvalidFeedUploadFile = 60606,
        MissingFeedAttributeColumnInFeedUploadFile = 60607,
        DuplicateFeedItemRowInFile = 60608,
        DuplicateFeedItemRowWithSameKeyAttributes = 60609,
        InvalidRowInFeedUploadFile = 60610,

        [DbErrorCodeMapping(-102713)]
        DuplicateFeedUploadHistroyRecord = 60611,

        FeedUploadFileRowCountLimitationExceed = 60612,

        DuplicateFeedItemIdInFeedUpdateFile = 60613,
        InvalidActionInFeedUpdateFile = 60614,
        FeedItemIdIsNotAllowedForCreation = 60615,
        FeedItemScheduleInvalidStartTime = 60616,
        FeedItemScheduleInvalidEndTime = 60617,

        FeedScheduleAlreadyExists = 60619,

        [DbErrorCodeMapping(-102728)]
        FeedItemCountExceedFeedTypeLevelLimitation = 60620,

        AttributeValueLengthExceeded = 60621,

        FeedUploadScheduleUnableToFetchFile = 60630,
        FeedUploadScheduleUsernameRequiredForPassword = 60631,
        FeedUploadScheduleUsernamePasswordHaveInvalidCharacters = 60632, // colon
        FeedUploadScheduleInvalidUrl = 60633,
        FileFetchConnectionError = 60635,
        FileFetchAuthenticationError = 60636,
        FileFetchFilePathNotFound = 60637,
        FileFetchFailedToOpenFile = 60638,
        FileFetchPermissionLevelError = 60640,
        FileFetchNoSuchHost = 60641,

        InvalidFeedUploadFileExtension = 60642,
        MultipleEntriesNotAllowdInZipfile = 60643,
        MultipleWorksheetsNotAllowdInFeedUploadFile = 60644,
        InvalidFeedUploadZippedFile = 60645,

        #endregion

        #region FeedUploadService 60700-60799

        InvalidPageFeedLabel = 60700,
        TooManyPageFeedLabels = 60701,
        PageFeedLabelTooLong = 60702,
        PageFeedUrlTooLong = 60703,
        InvalidPageFeedUrl = 60704,
        InvalidImportPayload = 60705,
        NoAvailableFeedMapping = 60706,
        PageFeedUrlShouldBeOneOfKeys = 60707,

        InvalidCustomIdAttributeValue = 60708,
        InvalidDevicePreferenceAttributeValue = 60709,
        InvalidEndDateAttributeValue = 60710,
        InvalidSchedulingAttributeValue = 60711,
        InvalidStartDateAttributeValue = 60712,
        InvalidTargetAdGroupAttributeValue = 60713,
        InvalidTargetAudienceIdAttributeValue = 60714,
        InvalidTargetCampaignAttributeValue = 60715,
        InvalidTargetKeywordAttributeValue = 60716,
        InvalidTargetKeywordMatchTypeAttributeValue = 60717,
        InvalidTargetKeywordTextAttributeValue = 60718,
        InvalidTargetLocationAttributeValue = 60719,
        InvalidTargetLocationRestrictionAttributeValue = 60720,

        StartDateComesAfterEndDate = 60721,
        TargetAdgroupWithoutTargetCampaign = 60722,
        MissingTargetKeywordMatchType = 60723,
        HavingBothTargetKeywordAndTargetKeywordText = 60724,
        InvalidTargetLocationIdAttributeValue = 60725,
        HavingBothTargetLocationIdAndTargetLocation = 60726,
        NestedParameterInCustomAttributeNotSupported = 60727,
        CannotSpecifyDatatypeOfBuiltInAttributeInHeader = 60728,

        InvalidPageFeedAdTitle = 60729,
        PageFeedAdTitleTooLong = 60730,

        InvalidFeedPhoneNumber = 60731,
        InvalidFeedYear = 60732,
        InvalidFeedEmailAddress = 60733,

        InvalidDynamicDataFeedRatings = 60734,
        InvalidDynamicDataFeedHotelStarRating = 60735,
        InvalidDynamicDataFeedFuelType = 60736,
        InvalidDynamicDataFeedVehicleState = 60737,
        InvalidDynamicDataFeedMileageUnit = 60738,
        InvalidDynamicDataFeedTransmission = 60739,
        InvalidDynamicDataFeedBodyStyle = 60740,
        InvalidDynamicDataFeedDriveTrain = 60741,
        InvalidDynamicDataFeedCondition = 60742,

        InvalidDynamicDataAutoAggregateFeedMetric = 60743,

        InvalidDynamicDataFeedDuration = 60744,
        [DbErrorCodeMapping(-102753)]
        BlobPurgeWatermarkNotUpdate = 60745,

        [DbErrorCodeMapping(-102760)]
        BlobSoftDeleteNonzeroRefCount = 60746,

        [DbErrorCodeMapping(-102761)]
        CompactJobSetFailureOrTimeout = 60747,

        [DbErrorCodeMapping(-102762)]
        LBStartedDuringCompactJob = 60748,

        [DbErrorCodeMapping(-102763)]
        CompactJobSetInvalidFeedItemId = 60749,

        InvalidDynamicDataFeedHealthInsuranceOrganizationCategory = 60750,

        InvalidDynamicDataFeedToursAndActivitiesPriceType = 60751,

        InvalidDynamicDataFeedNumOfReviews = 60752,

        InvalidDynamicDataFeedSponsored = 60753,

        InvalidDynamicDataFeedLocationTargetingScope = 60754,

        InvalidDynamicDataFeedSponsor = 60755,

        #endregion

        #region PerformanceTargets error codes 60800 - 60899

        [DbErrorCodeMapping(-102101)]
        PerformanceTargetTemplatesEntityLimitExceeded = 60800,

        [DbErrorCodeMapping(-102102)]
        [ErrorField(EntityField.PerformanceTargetTemplateName)]
        PerformanceTargetTemplateNameDuplicate = 60801,

        PerformanceTargetTemplateListIsNullOrEmpty = 60802,

        PerformanceTargetTemplateIsNull = 60803,

        [DbErrorCodeMapping(-102106)]
        [ErrorField(EntityField.PerformanceTargetType)]
        PerformanceTargetTypeInvalid = 60804,

        [ErrorField(EntityField.PerformanceTargetTemplateId)]
        PerformanceTargetTemplateIdShouldBeNullOnAdd = 60805,

        [ErrorField(EntityField.PerformanceTargetTemplateName)]
        PerformanceTargetTemplateNameInvalid = 60806,

        [ErrorField(EntityField.PerformanceTargetTemplateName)]
        PerformanceTargetTemplateNameLengthExceeded = 60807,

        [ErrorField(EntityField.PerformanceTargetTemplateStartDate)]
        PerformanceTargetTemplateStartDateIsNull = 60808,

        [ErrorField(EntityField.PerformanceTargetTemplateEndDate)]
        PerformanceTargetTemplateEndDateIsNull = 60809,

        [ErrorField(EntityField.PerformanceTargetTemplateStartDate)]
        PeriodPerformanceTargetTemplateStartDateShouldBeNull = 60810,

        [ErrorField(EntityField.PerformanceTargetTemplateEndDate)]
        PeriodPerformanceTargetTemplateEndDateShouldBeNull = 60811,

        [ErrorField(EntityField.PerformanceTargetTemplateEndDate)]
        PerformanceTargetTemplateDateRangeInvalid = 60812,

        [ErrorField(EntityField.PerformanceTargetMetrics)]
        PerformanceTargetMetricListIsNullOrEmpty = 60813,

        [ErrorField(EntityField.PerformanceTargetMetrics)]
        PerformanceTargetMetricTypeInvalid = 60814,

        [ErrorField(EntityField.PerformanceTargetMetrics)]
        PerformanceTargetMetricTypeDuplicate = 60815,

        InvalidTimeZone = 60816,

        PerformanceTargetPilotNotEnabledForCustomer = 60817,

        [ErrorField(EntityField.PerformanceTargetTemplateId)]
        PerformanceTargetTemplateIdInvalid = 60818,

        [DbErrorCodeMapping(-102103)]
        [ErrorField(EntityField.PerformanceTargetTemplateId)]
        PerformanceTargetTemplateDoesNotExist = 60819,

        [DbErrorCodeMapping(-102104)]
        [ErrorField(EntityField.PerformanceTargetTemplateId)]
        PerformanceTargetTemplateIsDeleted = 60820,

        [ErrorField(EntityField.PerformanceTargetType)]
        PerformanceTargetTypeUpdateNotSupported = 60821,

        [ErrorField(EntityField.PerformanceTargetTemplateId)]
        PerformanceTargetTemplateIdDuplicate = 60822,

        [DbErrorCodeMapping(-102105)]
        [ErrorField(EntityField.PerformanceTargetStatus)]
        PerformanceTargetTemplateIsPaused = 60824,

        PerformanceTargetTemplateStatusInvalid = 60825,

        PerformanceTargetTemplateUpdateEmpty = 60826,

        CampaignsPerPerformanceTargetLimitExceeded = 60827,

        PerformanceTargetSegmentationNotSupported = 60828,

        #endregion

        #region Experiments error codes 60900 - 60999

        ExperimentsListIsNullOrEmpty = 60900,
        ExperimentIsNull = 60901,
        ExperimentsEntityLimitExceeded = 60902,
        ExperimentNameIsSameAsBaseCampaignName = 60903,
        ExperimentStartDateGreaterThanEndDate = 60904,
        ExperimentEndDateLessThanToday = 60905,
        ExperimentStartDateLessThanToday = 60906,
        ExperimentTrafficSplitPercentInvalid = 60907,
        ExperimentNameIsEmpty = 60908,
        [DbErrorCodeMapping(-102187)]
        [ErrorField(EntityField.ExperimentId)]
        ExperimentIdInvalid = 60909,
        DuplicateExperimentIds = 60910,
        ExperimentIdListNullOrEmpty = 60911,
        [DbErrorCodeMapping(-102107)]
        [ErrorField(EntityField.ExperimentStartDateEndDate)]
        ExperimentTimeperiodOverlapping = 60912,
        [DbErrorCodeMapping(-102108)]
        [ErrorField(EntityField.ExperimentBaseCampaignId)]
        ExperimentBaseCampaignIdIsLocked = 60922,
        ExperimentCampaignIdInvalid = 60923,
        [DbErrorCodeMapping(-102109)]
        ExperimentsEntityLimitPerCampaignExceeded = 60924,
        ExperimentPilotNotEnabledForCustomer = 60925,
        ExperimentStatusInvalid = 60926,
        ExperimentBaseCampaignIsExperimentCampaign = 60927,
        ExperimentStartDateCannotBeChanged = 60928,
        ExperimentEndDateCannotBeChanged = 60929,
        ExperimentStartDateInvalid = 60930,
        ExperimentBaseCampaignIdInvalid = 60931,
        CampaignOrExperimentWithNameAlreadyExists = 60932,
        BaseCampaignTypeInvalid = 60933,
        BaseCampaignBudgetTypeInvalid = 60934,
        ExperimentNameTooLong = 60935,
        ExperimentNameHasInvalidCharacters = 60936,
        ExperimentCampaignInvalid = 60937,
        ExperimentBaseCampaignIdCannotBeChanged = 60938,
        ExperimentTrafficSplitPercentCannotBeChanged = 60939,
        EndedExperimentCannotBeChanged = 60940,
        ExperimentCampaignCannotBeUpdated = 60941,
        ExperimentBaseCampaignCannotBeChangedToSharedBudget = 60942,
        ExperimentTypeInvalid = 60950,
        ExperimentEventInvalid = 60943,
        ExperimentEventBatchLimitExceeded = 60944,
        ExperimentEventTypeIsInvalid = 60945,
        ExperimentOrBaseCampaignWasModified = 60946,
        ExperimentEventWasModified = 60947,
        BaseCampaignHadOtherUnendedEvents = 60948,
        ExperimentTypeCannotBeChanged = 60949,
        #endregion

        #region RSA Ads and Text Assets error codes 61000 - 61099
        TextAssetsNotPassed = 61000,

        [DbErrorCodeMapping(-102801)]
        [DbErrorCodeMapping(-102802)]
        TextAssetDoesNotExist = 61001,

        [DbErrorCodeMapping(-102800)]
        TextAssetLimitReachedForAccount = 61002,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionsDuplication = 61003,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsHeadlinesPinnedFieldMismatch = 61004,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionsPinnedFieldMismatch = 61005,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsInvalidHeadline = 61006,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsHeadlineTooLong = 61007,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsHeadlinesMaxCountExceeded = 61009,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionsMaxCountExceeded = 61010,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsHeadlinesLessThanMinRequired = 61011,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionsLessThanMinRequired = 61012,

        ResponsiveSearchAdDefaultTextRequiredForKeywordParameter = 61013,

        ResponsiveSearchAdInvalidFunctionFormat = 61014,
        ResponsiveSearchAdUnknownFunction = 61015,
        ResponsiveSearchAdMissingDelimiterBetweenFunctions = 61016,
        ResponsiveSearchAdCountdownInvalidDateTime = 61017,
        ResponsiveSearchAdCountdownInvalidLanguageCode = 61018,
        ResponsiveSearchAdCountdownInvalidDaysBefore = 61019,
        ResponsiveSearchAdCountdownDaysBeforeOutOfRange = 61020,
        ResponsiveSearchAdCountdownInvalidParameters = 61021,
        ResponsiveSearchAdCountdownPastDateTime = 61022,
        ResponsiveSearchAdCountdownInvalidDefaultText = 61023,

        ResponsiveSearchAdPath1TooLong = 61024,
        ResponsiveSearchAdPath1Invalid = 61025,
        ResponsiveSearchAdPath2SetWithoutPath1 = 61026,
        ResponsiveSearchAdPath2TooLong = 61027,
        ResponsiveSearchAdPath2Invalid = 61028,

        ResponsiveSearchAdDisplayUrlDomainInvalid = 61029,
        ResponsiveSearchAdDisplayUrlDomainTooLong = 61030,
        ResponsiveSearchAdFinalUrlDomainTooLong = 61031,
        ResponsiveAdDisplayUrlDomainExtractionFailed = 61032,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsHeadlinesNullOrEmpty = 61033,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionsNullOrEmpty = 61034,

        [ErrorField(EntityField.AdHeadlines)]
        ResponsiveSearchAdsDuplicateHeadlines = 61035,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsInvalidDescription = 61036,

        [ErrorField(EntityField.AdDescriptions)]
        ResponsiveSearchAdsDescriptionTooLong = 61037,

        [DbErrorCodeMapping(-102803)]
        MaxRSAAdsLimitReachedInAdGroup = 61038,

        AdCustomizerNotSupportedForAdType = 61039,

        ResponsiveSearchAdNotEnoughHeadlinesForPositions = 61040,
        ResponsiveSearchAdNotEnoughDescriptionsForPositions = 61041,
        ResponsiveSearchAdsInvalidPinnedField = 61042,

        [DbErrorCodeMapping(-102850)]
        MaxMMAAdsLimitReachedInAdGroup = 61043,

        ResponsiveAdDefaultTextRequiredForKeywordParameter = 61044,
        ResponsiveAdHeadlinesLessThanMinRequired = 61045,
        ResponsiveAdLongHeadlinesLessThanMinRequired = 61046,
        ResponsiveAdDescriptionsLessThanMinRequired = 61047,
        ResponsiveAdBothCountDownAndGlobalCountDownNotAllowed = 61048,

        InvalidContextualTargetBidAdjustment = 61049,

        AccountNotEnabledForRSAAutoGeneratedAssets = 61050,
        CustomerNotEnabledForVanityPharmaWebsiteDescriptions = 61051,
        InvalidWebsiteDescriptionForDisplayUrlMode = 61052,
        UsingTruncatedHeadlinesForMMA = 61053,

        #endregion

        #region SharedLibrary 62000 - 62099
        CustomerNotEligibleForSharedLibrary = 62000,
        SharedLibraryUserPermissionDenied = 62001,

        [DbErrorCodeMapping(-102900)]
        SharedEntityDoesNotBelongToTheCustomer = 62002,

        [DbErrorCodeMapping(-102901)]
        SharedEntityIsDeleted = 62003,

        [DbErrorCodeMapping(-102902)]
        SharedEntityDoesNotExist = 62004,

        [DbErrorCodeMapping(-102903)]
        SharedEntityIsNotSharedToTheCustomerOrAccount = 62005,

        SharingScopeEntityScopeDoesNotMatch = 62006,

        CannotUpdateAssociationStatusDueToTagNotAvailable = 62007,
        #endregion

        #region Smart Campaign 62100 - 62199
        SmartCampaignNotEnabledForAccount = 62100,
        DuplicateBusinessId = 62101,
        BusinessIdInvalid = 62102,
        BusinessIdsNotPassed = 62103,
        SmartListingsNotPassed = 62104,
        InvalidSmartListingCategoryCount = 62105,
        SmartListingProductOrServiceMaxLimitReached = 62106,
        BusinessesNotPassed = 62107,
        SmartCampaignNotEnabled = 62108,
        SmartCampaignNotEnabledForCustomer = 62109,
        AllLanguageNotSupportedForSmartCampaign = 62110,
        MultiLanguageNotSupportedForSmartCampaign = 62111,
        SharedBudgetNotSupportedForSmartCampaign = 62112,
        MaxCpcShouldBeNullForSmartCampaign = 62113,
        EntityNotAllowedForSmartCampaign = 62114,
        BusinessNameInvalid = 62115,
        BusinessWebsiteInvalid = 62116,
        SearchPhrasesNullOrEmpty = 62117,
        SearchPhraseDuplicate = 62118,
        SearchPhraseAlreadyPaused = 62119,
        SearchPhraseAlreadyActive = 62120,
        SearchPhraseInvalidStatus = 62121,
        InvalidCriterions = 62122,
        BidAdjustmentNotSupportedForSmartCampaignCriterion = 62123,
        SearchPhraseInvalidText = 62124,
        PausedSearchPhrasesAccountLimitExceeded = 62125,
        DuplicateProductOrService = 62126,
        SmartCampaignSubtypeNotMatchCampaignType = 62127,
        EmptySmartListingText = 62128,
        SmartListingTextTooLong = 62129,
        SmartCamapignCannotBeAssociatedWithAudience = 62130,
        MissingMcaPerfData = 62131,
        #endregion

        #region IF Functions 62200 - 62299

        IFFunctionCustomerNotInPilot = 62200,
        IFFunctionIncorrectSyntaxForDevice = 62201,
        IFFunctionIncorrectSyntaxForAudience = 62202,
        IFFunctionSomeHaveDefaultValueButNotAll = 62203,
        IFFunctionInvalidAudience = 62204,
        IFFunctionDuplicateAudiences = 62205,
        IFFunctionSpecialCharactersAreNotEscaped = 62206,
        IFFunctionNestingNotAllowed = 62207,
        IFFunctionSpecialCharactersNotAllowed = 62208,
        IFFunctionInvalidSyntax = 62209,
        IFFunctionNumAudiencesExceedsMaxForAd = 62210,
        IFFunctionAudiencesExceedsMaxFieldLength = 62211,
        IFFunctionErrorGettingAudiences = 62212,

        #endregion

        #region Disclaimer Ads 62300 - 62399

        CustomerNotEligibleForDisclaimerAds = 62300,
        BulkUploadNotSupportedForDisclaimerAds = 62301,
        DisclaimerSettingCannotBeUpdated = 62302,
        DisclaimerLayoutMissing = 62303,
        InvalidDisclaimerLayout = 62304,
        NullOrEmptyDisclaimerPopupText = 62305,
        DisclaimerLineTextShouldbeEmpty = 62306,
        NullOrEmptyDisclaimerLineText = 62307,
        DisclaimerPopupTextShouldbeEmpty = 62308,
        DisclaimerFinalUrlMissing = 62309,
        OnlyOneDisclaimerFinalUrlIsAllowed = 62310,
        OnlyOneDisclaimerFinalMobileUrlIsAllowed = 62311,
        DisclaimerTitleNotAllowedForLineText = 62312,
        EntityOnlyAllowedForDisclaimerCampaign = 62313,
        AppInstallAdNotSupportedForDisclaimerCampaign = 62314,
        ExperimentNotSupportedForDisclaimerCampaign = 62315,
        AdTypeNotSupportedForDisclaimerCampaign = 62316,
        DisclaimerTitleIsRequiredForPopupText = 62317,
        InvalidDisclaimerTitle = 62318,

        #endregion Disclaimer Ads

        #region ImageAsset General 62401 - 62405

        DuplicateImage = 62401,
        InvalidImage = 62402,
        RequiredImageMissing = 62403,
        InvalidImageExtensionLayout = 62404,

        #endregion ImageAsset General

        InvalidStockImageVendor = 62410,

        AssetIsNotStockImageAsset = 62411,

        UnableToLicenseAsset = 62412,

        UnableToDownloadAssetFromUrl = 62413,

        StockImageServiceError = 62414,

        [DbErrorCodeMapping(-106006)]
        CampaignUndeleteNotAllowedBecauseSharedBudgetInvalid = 62420,

        #region Promotion AdExtension 62421 - 62430
        PromotionValueNotSet = 62421,
        PromotionPercentAndMoneyValueSet = 62422,
        PromotionOrdersOverAndPromoCodeSet = 62423,
        PromotionValueNegative = 62424,
        PromotionOrdersOverNegative = 62425,
        PromotionDatesInvalid = 62426,
        CurrencyCodeSetWithoutMonetaryValue = 62427,
        #endregion

        #region Iaas 62480 - 62499
        CustomerNotInGoogleImportApiPilot = 62480,
        TaskNameInvalid = 62481,
        TaskSchedulingInvalid = 62482,
        CustomerNotInFileImportApiPilot = 62483,
        #endregion

        AdScheduleTimeZoneSettingNotInPilot = 62500,

        AccountReparentingValidationFailure = 62501,

        #region FilterLink AdExtension 62502 - 62510
        InvalidFilterLinkTextCharacter = 62502,
        TooManyFilterLinkText = 62503,
        TooFewFilterLinkText = 62504,
        FinalUrlandTextNotMatch = 62505,
        EmptyElementInListNotAllowed = 62506,
        #endregion

        #region Note 62550 - 62560
        NoteCreationPartiallySucceeded = 62550,
        #endregion

        KeywordSubstitutionNotSupported = 62551,

        #region SmartPage 62570 - 62600
        [DbErrorCodeMapping(-109700)]
        SmartPageLimitExceeded = 62570,
        [DbErrorCodeMapping(-109701)]
        SmartPageSiteSuffixAlreadyExists = 62571,
        [DbErrorCodeMapping(-109702)]
        SmartPageIdIsInvalid = 62582,
        [DbErrorCodeMapping(-109703)]
        SmartPageConcurrentUpdatingIsNotAllowed = 62583,

        SmartPageMissingSiteSuffix = 62572,
        SmartPageListIsNullOrEmpty = 62573,
        SmartPageInvalidSiteSuffix = 62574,
        SmartPageIsNull = 62575,
        SmartPageInvalidStatus = 62576,
        SmartPageInvalidSubdomain = 62577,
        CustomerNotInPilotForSmartPage = 62578,
        SmartPageSubdomainIsNotAvailable = 62579,
        SmartPageGetFbPageUserError = 62580,
        SmartPageGetFbPageApiError = 62581,
        SmartPageIncompleteInfoProvided = 62584,
        SmartPageTableStorageError = 62585,
        SmartPageAssociationListIsNullOrEmpty = 62586,
        SmartPageAssociationInvalidStatus = 62587,
        DuplicateSmartPageAssociation = 62588,
        SmartPageAssociationInvalidType = 62589,
        SmartPageSubdomainExceedsMaxLength = 62590,
        SmartPageFbImportParamMissing = 62591,
        SmartPageFbImportNoTokenFoundInKeyVault = 62592,
        SmartPageSubdomainChangeIsNotAllowed = 62593,
        SmartPageInvalidAction = 62594,
        SmartPagePreviewIsNotAvailable = 62595,
        SmartPageAnalyticsTagIdCannotBeUpdated = 62596,
        SmartPageUrlIsRejectedByEditorial = 62597,
        SmartPageInvalidCoverImageUrl = 62598,
        SmartPageInvalidGalleryImageUrl = 62599,

        #endregion

        #region CustomColumn 62600 - 62619
        [DbErrorCodeMapping(-109800)]
        AccountCustomColumnLimitExceeded = 62600,
        [DbErrorCodeMapping(-109801)]
        CustomerCustomColumnLimitExceeded = 62601,
        [DbErrorCodeMapping(-109802)]
        CustomColumnIdIsInvalid = 62602,
        CustomColumnIsUnsupported = 62603,
        CustomColumnIsInvalid = 62604,
        CustomColumnExpressionIsTooLong = 62605,
        #endregion

        #region Flyer Adextension (62620-62629)
        FlyerExtensionInvalidAssetId = 62620,
        FlyerAdExtensionsAssetLimitExceeded = 62621,
        FlyerExtensionImageTooSmall = 62622,
        FlyerExtensionInvalidStoreId = 62623,
        FlyerExtensionSchedulingStartAndEndDateRequired = 62624,
        FlyerExtensionEndDateRangeExceeded = 62625,
        FlyerExtensionStoreIdCannotBeModified = 62626,
        #endregion

        ResponsiveSearchAdsBothCountDownAndGlobalCountDown = 62627,

        #region Portfolio Bid Strategy (62630-62659)
        [DbErrorCodeMapping(-109900)]
        DuplicatePortfolioBidStrategyName = 62630,
        [DbErrorCodeMapping(-109901)]
        PortfolioBidStrategyEntityLimitExceeded = 62631,
        PortfolioBidStrategyNameTooLong = 62632,
        PortfolioBidStrategyNameMissing = 62633,
        PortfolioBidStrategyNameHasInvalidChars = 62634,
        PortfolioBidStrategiesAreNullOrEmpty = 62635,
        PortfolioBidStrategyOperationsBatchLimitExceeded = 62636,
        PortfolioBidStrategyIsNull = 62637,
        PortfolioBidStrategyIdShouldBeNullOnAdd = 62638,
        [DbErrorCodeMapping(-109902)]
        PortfolioBidStrategyIdInvalid = 62639,
        DuplicatePortfolioBidStrategyId = 62640,
        BidStrategyTypeCannotBeNullOnAdd = 62641,
        [DbErrorCodeMapping(-109903)]
        PortfolioBidStrategyIsAssociatedWithActiveCampaigns = 62642,
        [DbErrorCodeMapping(-109904)]
        PortfolioBidStrategyTypeCannotBeChanged = 62643,
        AccountNotInPilotForPortfolioBidStrategy = 62644,
        UnsupportedBidStrategyTypeForPortfolioBidStrategy = 62645,
        CannotUpdatePortfolioBidStrategyPropertyInCampaignEntity = 62646,
        UnsupportedCampaignTypeForPortfolioBidStrategy = 62647,
        CampaignTypeAndBidStrategyTypeAreMutuallyExclusive = 62648,
        [DbErrorCodeMapping(-109905)]
        PortfolioBidStrategyAssociatedCampaignTypeCannotBeChanged = 62649,
        CampaignTypeNotMatchCurrentPortfolioBidStrategy = 62650,
        [DbErrorCodeMapping(-109906)]
        CampaignUndeleteNotAllowedBecausePortfolioBidStrategyInvalid = 62651,
        #endregion

        #region Video Adextension (62660-62680)
        VideoExtensionThumbnailRequired = 62660,
        VideoExtensionInvalidImageFormat = 62661,
        VideoExtensionThumbnailTooSmall = 62662,
        VideoExtensionInvalidAspectRatio = 62663,
        VideoExtensionInvalidThumbnailId = 62664,
        VideoExtensionVideoTooSmall = 62665,
        VideoExtensionInvalidVideoDuration = 62666,
        VideoExtensionInvalidVideoId = 62667,
        VideoExtensionThumbnailIdAndUrlSet = 62668,
        VideoExtensionVideoProcessingFailed = 62669,
        #endregion

        #region Personalized Offers (62681-62700)
        AccountNotEnabledForPersonalizedOffers = 62681,
        PersonalizedOffersCashbackPercentInvalid = 62682,
        PersonalizedOffersCashbackBudgetInvalid = 62683,
        PersonalizedOffersCampaignTypeNotSupported = 62684,
        PersonalizedOffersCampaignBudgetRequired = 62685,
        PersonalizedOffersAdGroupTypeNotSupported = 62686,
        PersonalizedOffersScopeNotSupportedForCampaignType = 62687,
        PersonalizedOffersNotEnabledForCampaign = 62688,
        PersonalizedOffersCouponsNotSupportedForCampaignType = 62689,
        TargetRoasNotSupportedForPersonalizedOffers = 62690,
        PersonalizedOffersSponsoredPromotionsInvalid = 62691,
        PersonalizedOffersUnsupportedOperation = 62692,
        PersonalizedOffersSponsoredPromotionsOnly = 62693,
        #endregion

        TaskThrottlingLimitReached = 62701,

        VerifiedTrackingDataInvalid = 62801,

        AveragePositionIsDeprecated = 63000,

        InvalidImportRecommendationId = 63101,

        #region Conversion Goal (64000-65000)
        CustomerNotEligibleForInStoreVisitGoal = 64000,

        [DbErrorCodeMapping(-620038)]
        OnlyOneInStoreVisitGoalBeAllowedPerCustomer = 64001,

        [DbErrorCodeMapping(-620037)]
        InvalidStoreVisitsConversionGoal = 64002,

        InStoreVisitGoalShoudBeExcludeFromBidding = 64003,

        InStoreVisitGoalShouldBeAcrossAllAccounts = 64004,

        #endregion

        AccountNotEnabledForImageAutoRetrieve = 64005,

        UpdateFailedForIsClarityTagEnabled = 64006,
        DeleteFailedForClarityProject = 64007,
        AccountNotEnabledForBusinessAttributes = 64008,

        #region SmartPage 64009 - 64109
        SmartPageInvalidLogoImageUrl = 64009,
        SmartPageGalleryImagesLimitExceeded = 64010,
        SmartPageInvalidSocialUrl = 64011,
        SmartPageMissingDayInBusinessHours = 64012,
        SmartPageInvalidBusinessHours = 64013,
        SmartPageIncorrectSocialUrlFormat = 64014,
        SmartPageUpdateNotSupported = 64015,
        SmartPageGetFbPageOAuthError = 64016,
        SmartPageServicesLimitExceeded = 64017,
        SmartPageServicesInvalidName = 64018,
        SmartPageServicesInvalidDescription = 64019,
        SmartPageCustomDomainIsNotAvailable = 64020,
        SmartPagePreviousCustomDomainIsNotFreedUp = 64021,
        SmartPagePreviousSubdomainIsNotFreedUp = 64022,
        SmartPageM365TokenIsNullInKeyVault = 64023,
        SmartPageInvalidM365TokenInKeyVault = 64024,
        SmartPageM365OAuthError = 64025,
        SmartPageM365AdminApiError = 64026,
        SmartPageUpdateDomainWithin24HoursNotAllow = 64027,
        SmartPageDnsARecordConfliction = 64028,
        SmartPageDnsCNAMERecordConfliction = 64029,
        SmartPageACMEClientError = 64030,
        SmartPageCustomDomainVerifyError = 64031,
        SmartPageCustomDomainSaveCertToKeyVaultError = 64032,
        SmartPageCustomDomainSaveNginxConfigError = 64033,
        SmartPageCustomDomainUpdateMappingError = 64034,
        SmartPageCustomDomainDeleteCertFromKeyVaultError = 64035,

        #endregion

        #region EnhancedProductAdsFilter (65001-65200)
        CustomerNotEligibleForEnhancedProductAdsFilter = 65001,
        ProductConditionOperatorInvalid = 65002,
        ProductConditionOperatorNotSupportedForThisCampaignType = 65003,
        #endregion

        #region Amazon Sync Error Code from 65201 to 65250

        AmazonSyncAccessDenied = 65201,
        AmazonSyncThrottlingLimitReached = 65202,
        AmazonSyncApiInternalError = 65203,

        #endregion

        #region Google Tag Manager error codes (65251-65300)

        GoogleTagManagerWorkspaceCouldNotBeCreated = 65251,
        GoogleTagManagerPublishFailed = 65252,
        GoogleTagManagerImportFailed = 65253,
        GoogleTagManagerTagCreationFailed = 65254,
        CustomerNotEnabledForGTMImport = 65255,
        GoogleTagManagerTriggerCreationFailed = 65256,
        GoogleTagManagerGetContainerVersionFailed = 65257,
        GoogleTagManagerCreateVariableFailed = 65258,
        GoogleTagManagerScheduledTaskExisted = 65259,
        #endregion

        #region RSA Ad Customizer (65301-65320)
        RSAAdCustomizerPilotNotEnabledForAccount = 65301,

        [DbErrorCodeMapping(-65303)]
        RSAAdCustomizerAttributeTypeChangedInUpdate = 65302,

        [DbErrorCodeMapping(-65302)]
        RSAAdCustomizerAttributeCountMoreThanLimit = 65303,

        RSAAdCustomizerInvalidAttributeType = 65304,

        AttributeNameDoesNotExist = 65305,

        FetchAttributesFailed = 65306,

        AttributeNameLengthExceeded = 65307,

        InvalidAdcustomizerAttributeId = 65308,
        AttributeNameMissing = 65309,
        AttributeReferencedInAd = 65310,

        #endregion

        #region SeasonalityAdjustment (65321 -65350)
        [DbErrorCodeMapping(-110101)]
        CampaignAssociationsLimitExceeded = 65321,
        [DbErrorCodeMapping(-110102)]
        InvalidSeasonalityAdjustmentId = 65322,
        [DbErrorCodeMapping(-110103)]
        InvalidAssociation = 65323,
        [DbErrorCodeMapping(-110104)]
        SeasonalityAdjustmentTimestampMismatch = 65324,
        [DbErrorCodeMapping(-110100)]
        SeasonalityAdjustmentExceedLimit = 65325,
        OperationsBatchLimitExceeded = 65326,
        NameIsEmpty = 65327,
        NameExceededMaxLen = 65328,
        DescriptionIsNull = 65329,
        DescriptionExceededMaxLen = 65330,
        InvalidAdjustmentPercentage = 65331,
        DateShouldNotBeNull = 65332,
        DateGranularityCanOnlyBeToHours = 65333,
        InvalidDateRange = 65334,
        DeviceTypeFilterCannotBeNone = 65335,
        InvalidCampaignAssociationsAndCampaignTypeFilterCombination = 65336,
        InvalidCampaignAssociationsLength = 65337,
        InvalidCampaignType = 65338,
        EntitiesAreNullOrEmpty = 65339,
        DuplicateItemsInBatch = 65340,
        InvalidCampaignSubType = 65341,
        #endregion

        #region DataExclusion (65351 -65380)
        InvalidDataExclusionId = 65351,
        DataExclusionTimestampMismatch = 65352,
        DataExclusionAdjustmentPercentageShouldBeZero = 65353,
        DataExclusionExceedLimit = 65354,
        #endregion

        #region HotelListingGroup errors from 65401 to 65500
        AccountNotEnabledForHotelCampaign = 65401,
        CampaignIsNotOfTypeHotel = 65402,
        InvalidHotelListingType = 65403,
        AdGroupCriterionHotelListingIsNull = 65404,
        InvalidHotelListingOperand = 65405,
        FinalUrlAndMobileUrlNotAllowedForHotelListing = 65406,
        DuplicateRootNodeForHotelListingTree = 65407,

        ParentHotelListingGroupNodeDoesNotExist = 65408,
        HeightOfHotelListingTreeExceeededLimit = 65409,
        HotelListingOperandUnderSubDivisionMustBeSame = 65410,
        DuplicateHotelListing = 65411,
        InvalidHotelListingHierarchy = 65412,
        HotelListingGroupLimitExceededForAdGroup = 65413,
        InvalidHotelListingAttribute = 65414,
        HotelListingAttributeNullOrEmpty = 65415,
        HotelListingAttributeTooLong = 65416,
        InvalidAdGroupCriterionRateBidValue = 65417,
        HotelListingEverythingElseMissing = 65418,
        InvalidLocationIdForHotelListing = 65419,
        InvalidLocationNodeMissingParentLocation = 65420,
        InvalidLocationNodeInvalidParentLocation = 65421,
        InvalidLocationNodeMissingParentOperandNode = 65422,
        InvalidLocationNodeSubLocationAttachedToEENode = 65423,
        InvalidLocationIdForOperandType = 65424,
        InvalidBidTypeForCampaignBiddingScheme = 65425,
        ExternalAttributionRequiredFieldEmpty = 65426,
        ExternalAttributionModelTooLong = 65427,
        ExternalAttributionCreditValueInvalid = 65428,
        GoalNotEligibleForExternalAttribution = 65429,
        #endregion

        #region HotelTargetingErrors 65501 to 65750
        InvalidAdvanceBookingWindowTarget = 65501,
        InvalidAdvanceBookingWindowTargetBidAdjustment = 65502,
        ConflictWithAdvanceBookingWindow = 65503,
        InvalidCheckInDayTarget = 65504,
        InvalidDateSelectionTypeCriterion = 65505,
        InvalidCheckInDayTargetBidAdjustment = 65506,
        InvalidDateSelectionTypeTargetBidAdjustment = 65507,
        InvalidLengthOfStayTarget = 65508,
        InvalidLengthOfStayTargetBidAdjustment = 65509,
        ConflictWithLengthOfStay = 65510,
        InvalidCheckInDateTarget = 65511,
        InvalidCheckInDateTargetBidAdjustment = 65512,
        ConflictWithCheckInDate = 65513,

        #endregion

        #region Pinterest Sync Error Code from 65751 to 65800

        PinterestSyncTokenExpired = 65751,
        PinterestSyncPermissionDenied = 65752,
        PinterestSyncThrottlingLimitReached = 65753,
        PinterestSyncApiInternalError = 65754,
        PinterestSyncInvalidParams = 65755,
        PinterestSyncImportNotFound = 65756,
        PinterestSyncConflict = 65757,
        #endregion

        #region ImportPreferenceBulkUpdate error codes (65801 - 65900)
        InvalidImportPreferenceBulkUpdateTaskType = 65801,
        ImportPreferenceBulkUpdateUploadFileSizeExceeded = 65802,
        ImportPreferenceBulkUpdateBlobDownloadError = 65803,
        ImportPreferenceBulkUpdateBlobUploadError = 65804,
        ImportPreferenceBulkUpdateFileFormatInvalid = 65805,
        ImportPreferenceBulkUpdateFileMissingHeaderRow = 65806,
        ImportPreferenceBulkUpdateColumnTaskIdNotFound = 65807,
        ImportPreferenceBulkUpdateColumnAccountIdNotFound = 65808,
        ImportPreferenceBulkUpdateColumnPreferenceNotFound = 65809,
        ImportPreferenceBulkUpdateColumnImportTypeNotFound = 65810,
        ImportPreferenceBulkUpdateParseFailedInvalidValuesInDataRows = 65811,
        ImportPreferenceBulkUpdateFileIsEmpty = 65812,
        ImportPreferenceBulkUpdateSmartImportWillChangeToExpertImport = 65813,
        #endregion

        #region PerformanceMax Campaigns (65901 - 65920)
        PerformanceMaxCampaignsNotEnabledForAccount = 65901,
        MaxCpcNotSupportedForCampaignType = 65902,
        PerformanceMaxCampaignFinalUrlExpansionOptedOut = 65903,

        [DbErrorCodeMapping(-197200)]
        DuplicateAudienceGroupName = 65904,

        [DbErrorCodeMapping(-197201)]
        AudienceGroupEntityLimitExceeded = 65905,

        [DbErrorCodeMapping(-197202)]
        AudienceGroupIdInvalid = 65906,

        UnsupportedCampaignTypeForAssetGroup = 65907,
        AssetGroupsAreNullOrEmpty = 65908,
        AssetGroupIsNull = 65909,
        AssetGroupOperationsBatchLimitExceeded = 65910,
        Path2SetWithoutPath1 = 65911,
        FinalUrlRequired = 65912,
        DomainInvalid = 65913,
        DomainTooLong = 65914,
        DomainExtractionFailed = 65915,
        AssetFieldWithRequiredLengthMinimumNotMet = 65916,
        AssetFieldMinimumPerSubTypeNotMet = 65917,
        AssetFieldLimitPerSubTypeExceeded = 65918,

        [DbErrorCodeMapping(-197203)]
        AssetGroupAudienceGroupAssociationDuplicate = 65919,

        [DbErrorCodeMapping(-197204)]
        AssetGroupAudienceGroupAssociationDoesNotExist = 65920,

        #endregion

        #region Asset Library (65921 - 65950)
        [DbErrorCodeMapping(-197211)]
        InvalidParentFolderId = 65921,

        AccountNotEnabledForAssetLibraryV2 = 65922,

        [DbErrorCodeMapping(-197210)]
        FolderLimitExceededPerAccount = 65923,

        InvalidFolderName = 65924,
        AccountNotEnabledForAssetLibraryV3 = 65925,

        [DbErrorCodeMapping(-197213)]
        DuplicateFolderName = 65926,

        [DbErrorCodeMapping(-197212)]
        InvalidFolderId = 65927,

        [DbErrorCodeMapping(-197214)]
        FolderHasAssets = 65928,

        [DbErrorCodeMapping(-197215)]
        FolderCanNotMoveToTarget = 65929,

        [DbErrorCodeMapping(-197216)]
        FolderHasSubFolders = 65930,

        [DbErrorCodeMapping(-197217)]
        DuplicateAssetsUnderFolder = 65931,

        AccountNotEnabledForResponsiveAdAssetPerformance = 65932,

        AccountNotEnabledForAssetLibraryV4 = 65933,

        AccountNotEnabledForAssetLibraryAIGC = 65934,

        GenerateImagesPromptLengthInvalid = 65935,

        [DbErrorCodeMapping(-197700)]
        GenerateImagesRequestCountLimitExceeded = 65936,

        GenerateImagesPuidInvalid = 65937,

        GenerateImagesPromptContentInvalid = 65938,

        GenerateImagesAIGCNoRecommendation = 65939,

        GenerateImagesAIGCNoRecommendationWithBrand = 65940,

        GenerateImagesAIGCInvalidImageSize = 65941,

        GenerateImagesAIGCInvalidImageContent = 65942,

        GenerateImageAIGCInvalidUserPrompt = 65943,

        GenerateImageAIGCInvalidGeneratedImage = 65944,

        #endregion

        #region LeadForm Adextension (65951 - 65970)
        LeadFormExtensionActionNameRequired = 65951,
        LeadFormExtensionConfirmationUrlRequired = 65952,
        LeadFormExtensionInvalidLeadDelivery = 65953,
        LeadFormExtensionInvalidEmails = 65954,
        LeadFormExtensionQuestionUpdatesNotSupported = 65955,
        LeadFormExtensionDuplicateQuestionId = 65956,
        LeadFormExtensionInvalidQuestionId = 65957,
        EntityOnlyAllowedForSearchOrPMaxCampaigns = 65958,
        LeadFormExtensionNotFound = 65959,
        OnlyMaxClicksBiddingSchemeForLeadFormExtension = 65970,
        LeadFormExtensionMultipleChoiceAnswersNotAllowed = 65971,
        #endregion

        #region UETTagDeletion error codes (66000 - 66100)
        CustomerNotEligibleForDeletingUETTags = 66001,
        TagCannotBeDeletedForHasSharedLibrary = 66002,
        TagCannotBeDeletedForHasAudience = 66003,
        TagCannotBeDeletedForHasGoal = 66004,
        #endregion

        #region PerformanceMax Campaigns Continued (66101 - 66150)
        StartDateLessThanCurrentDate = 66101,
        EndDateLessThanStartDate = 66102,
        StartDateCannotBeChanged = 66103,

        AudienceGroupNameTooLong = 66104,
        AudienceGroupNameMissing = 66105,
        EntityNotAllowedForPmaxCampaign = 66106,
        AudienceGroupIdShouldBeNullOnAdd = 66107,
        AudienceGroupsAreNullOrEmpty = 66108,
        AudienceGroupOperationsBatchLimitExceeded = 66109,
        AudienceGroupIsNull = 66110,
        DuplicateAudienceGroupId = 66111,
        DimensionsCannotBeNull = 66112,
        UnsupportedAgeRangeForAudienceGroup = 66113,
        UnsupportedGenderTypeForAudienceGroup = 66114,
        AudienceGroupNameHasInvalidChars = 66115,

        AudienceNotFound = 66116,
        AudienceTypeNotSupported = 66117,
        DuplicateAudience = 66118,
        TooManyAudiences = 66119,
        TooManyDimensionsOfSameType = 66120,
        AudienceGroupInUse = 66121,

        OperationsForTooManyAssetGroups = 66122,
        AssetGroupListingGroupsEntityLimitExceeded = 66123,
        AssetGroupListingGroupIsNull = 66124,
        DuplicateAssetGroupListingGroupIds = 66125,
        AssetGroupListingGroupActionIsNull = 66126,
        AssetGroupListingGroupActionsNullOrEmpty = 66127,
        AnotherOperationForSameAssetGroupHasError = 66128,
        [DbErrorCodeMapping(-197304)]
        AssetGroupIdInvalid = 66129,
        CampaignTypeIsNotPerformanceMax = 66130,
        ListingGoupLimitExceededForAssetGroup = 66131,
        AssetGroupListingGroupIdInvalid = 66132,
        AssetGroupListingTypeInvalid = 66133,
        DuplicateRootNodeForListingGroupTree = 66134,
        ParentListingGroupNodeDoesNotExist = 66135,
        HeightOfListingGroupTreeLimitExceeded = 66136,
        ProductConditionHierarchyInvalid = 66137,
        EverythingElseNodeMissing = 66138,
        UpdateIsNotSupportedForListingGroupNode = 66139,
        FailedToGetSalesCountryFromCampaignSettings = 66140,
        InvalidSalesCountry = 66141,
        SharedBudgetNotSupportedForCampaignType = 66142,
        [DbErrorCodeMapping(-197305)]
        PerformanceMaxCampaignLimitExceeded = 66143,
        [DbErrorCodeMapping(-197306)]
        AssetGroupAlreadyExists = 66144,
        AudienceGroupWithActiveAssociationsCannotBeDeleted = 66145,
        AssetGroupLimitExceededForCampaign = 66146,
        EntityDoesNotBelongToCampaign = 66147,
        UnsupportedCampaignTypeForAdGroup = 66148,
        AssetGroupInvalidStatus = 66149,
        BidAdjustmentNotSupportedForPerformanceMaxCampaign = 66150,
        #endregion

        #region AIM Continued (66151 - 66250)
        AccountNotEligibleForFrequencyCap = 66151,
        InvalidCampaignSubtypeForFrequencyCap = 66152,
        InvalidFrequencyCapSettings = 66153,
        UnsupportedSettingInBrandAwarenessVideoAds = 66154,
        InvalidBitRate = 66155,
        ResponsiveAdVideoInvalidStatus = 66156,
        ResponsiveAdVideoWidthTooSmall = 66157,
        ResponsiveAdVideoHeightTooSmall = 66158,
        ResponsiveAdVideoInvalidAspectRatio = 66159,
        ResponsiveAdVideoInvalidDuration = 66160,
        ResponsiveAdVideoBitRateTooSmall = 66161,
        ResponsiveAdVideoSourceLengthTooLarge = 66162,
        ResponsiveAdVideoUnsupportedFileFormat = 66163,
        InvalidCriterionBidAdjustmentValue = 66164,
        OptimizedTargetingIsNotEligibleForBrandAwarenessVideoAds = 66165,
        AccountNotInPilotForMMAV2 = 66167,
        UnsupportedSettingInDisplayAds = 66168,
        AccountNotEnabledForDisplayCampaign = 66169,
        AccountNotEnabledForMultiFormatAds = 66170,
        AdSubTypeCannotBeChanged = 66171,
        UnsupportedSettingInMultiFormatAds = 66172,
        #endregion

        AccountNotEnabledForSearchCampaignPredictiveTargeting = 66166,
        // next region should start after 66250

        #region LogoExtension (66251 - 66275)
        LogoExtensionLogoTooSmall = 66251,
        LogoExtensionLogoTooLarge = 66252,
        LogoExtensionLogoNotSquare = 66253,
        LogoExtensionMissingBusinessLogo = 66254,
        LogoExtensionMissingBusinessName = 66255,
        #endregion

        CustomerNotEnabledForFeedLabel = 66276,
        ShoppingSettingsFeedLabelInvalidLength = 66277,
        ShoppingSettingsFeedLabelInvalidCharacters = 66278,
        ShoppingSettingsFeedLabelAndSalesCountryIncompatible = 66279,

        CustomerDoesNotHaveAnyAccounts = 66280,
        DomainDoesNotMatchCampaignStoreDomain = 66281,
        CampaignStoreDoesNotHaveDomain = 66282,

        #region Web Insights (67301 - 67310)
        IsWebInsightsEnabledMissing = 67301,

        ClarityServiceError = 67302,

        UnauthorizedOrNotFoundForWebInsights = 67303,

        GetTileDataError = 67304,

        GetTotalSessionsTileError = 67305,

        GetQuickBacksTileError = 67306,

        GetAverageActiveTileError = 67307,

        GetDeviceTileError = 67308,

        GetCountryTileError = 67309,

        GetPageUrlTileError = 67310,
        #endregion

        #region MultiChannelAccount (67311 - 67400)
        QueueMutliChannelAccountStatusError = 67311,

        CustomerNotEnabledForMultiChannelCampaign = 67312,
        #endregion

        #region Web Insights Continued(67401 - 67405)
        WebInsightsEnabledTimeMissing = 67401,
        #endregion

        #region Offline Conversion Report(67406 - 67420)
        VcClientConnectionError = 67406,
        VcClientDownloadError = 67407,
        VcClientStreamExistsError = 67408,
        VcClientDirectoryExists = 67409,
        VcClientGetDirectoryInfo = 67410,
        ReadSummaryBlobFailed = 67411,
        #endregion

        #region Conversion Value Rule(67421 ~ 67429) P1
        InputOutputCountMismatch = 67421,
        ConversionValueRuleEnabled = 67422,
        [DbErrorCodeMapping(-210001)]
        ExceedsMaximumNumberOfRules = 67423,
        DuplicateGoalName = 67424,
        [DbErrorCodeMapping(-210003)]
        RuleIdNotFound = 67425,
        LocationTypeMismatch = 67426,
        AudienceTypeMismatch = 67427,
        ConditionOverlap = 67428,
        LocationHierarchyIssue = 67429,
        #endregion

        #region PMax Campaign Bidding Scheme Switch Back (67430 - 67440)
        [DbErrorCodeMapping(-197800)]
        CampaignNotUseMaxClick = 67430,

        [DbErrorCodeMapping(-197801)]
        NotEligibleToSwitchBackBiddingScheme = 67431,
        #endregion

        #region Conversion Value Rule(67441 ~ 67450) P2
        EntityIsEmptyOrNull = 67441,
        EmptyPropertyNotAllowed = 67442,
        CurrencyCodeShouldNotBeNullForAdd = 67443,
        PrimaryConditionShouldNotBeNull = 67444,
        DuplicatedRuleId = 67445,
        ConditionTypeNotAllowed = 67446,
        [DbErrorCodeMapping(-210002)]
        DuplicateRuleName = 67447,
        #endregion

        #region Bulk upload and download Goals (67451 ~ 67470) 
        #endregion

        #region Transparent Image Annotation (67471 ~ 67480)
        [DbErrorCodeMapping(-211001)]
        SystemGeneratedAdExtensionCouldNotBeModified = 67471,
        SystemGeneratedAdExtensionCouldNotBeAssociated = 67472,
        #endregion

        #region CustomSegment Management Error Codes from 67481 to 67510

        /// <summary>
        /// CustomSegment array is null or empty.
        /// </summary>
        CustomSegmentNotPassed = 67481,

        /// <summary>
        /// Too many CustomSegments passed in
        /// </summary>
        CustomSegmentBatchSizeExceedsLimit = 67482,

        /// <summary>
        /// Either customSegment name is empty or doesn't conform to its length limits.
        /// </summary>
        InvalidCustomSegmentName = 67483,

        /// <summary>
        /// The size of customSegment catalog exceeds the maximum.
        /// </summary>
        CustomSegmentCatalogIsTooLarge = 67484,

        /// <summary>
        /// CustomSegment catalog is empty.
        /// </summary>
        CustomSegmentCatalogIsEmpty = 67485,

        /// <summary>
        /// CustomSegment can not find catalog.
        /// </summary>
        CustomSegmentCannotFindCatalog = 67486,

        /// <summary>
        /// CustomSegment can not find catalog.
        /// </summary>
        CustomerIsNotEligibleForKeywordTargeting = 67487,

        /// <summary>
        /// CustomSegment only support audience campaign.
        /// </summary>
        CustomSegmentOnlySupportAudienceCampaign = 67488,

        /// <summary>
        /// Exceed MaxCustomSegmentCriterionCount Per AdGroup.
        /// </summary>
        ExceedMaxCustomSegmentCriterionCountPerAdGroup = 67489,

        /// <summary>
        /// NegativeAdGroupCriterion Is Not Supported By CustomSegment.
        /// </summary>
        NegativeAdGroupCriterionIsNotSupportedByCustomSegment = 67490,

        /// <summary>
        /// CustomSegment only support account level.
        /// </summary>
        CustomSegmentOnlySupportAccountLevel = 67491,

        CustomSegmentNotFound = 67492,

        [DbErrorCodeMapping(-600100)]
        InValidCustomSegmentId = 67493,

        [DbErrorCodeMapping(-600101)]
        DeletedCustomSegment = 67494,

        #endregion

        #region App Campaigns (67511 - 67530)
        AppCampaignsNotEnabledForAccount = 67511,
        AppNotFound = 67512,
        EntityNotAllowedForAppCampaign = 67513,
        TrackingTemplateIsRequiredForMobileAppCampaign = 67514,
        MissingRequiredParameterOrString = 67515,
        InvalidQueryDelimiterPlacement = 67516,
        InvalidAppId = 67517,
        #endregion

        #region LinkedIn Campaigns (67531 - 67560)

        LinkedInCampaignsNotEnabledForAccount = 67531,
        EntityNotAllowedForLinkedInCampaign = 67532,
        TrackingTemplateNotSupportedForLinkedInCampaign = 67533,
        FinalUrlSuffixNotSupportedForLinkedInCampaign = 67534,
        UrlCustomParametersSupportedForLinkedInCampaign = 67535,
        AdScheduleTimeZoneSettingNotSupportedForLinkedInCampaign = 67536,
        AdGroupDeletionIsNotSupportedForLinkedInCampaign = 67537,
        MultiLanguagesNotSupportedForLinkedInCampaign = 67538,
        CannotEditLinkedInCampaignLanguage = 67539,
        BidAdjustmentNotSupportedForLinkedInCampaign = 67540,
        NotSupportedConversionGoalTypeForLinkedInCampaign = 67541,
        NotSupportedConversionGoalScopeForLinkedInCampaign = 67542,
        NotSupportedGoalCountTypeForLinkedInCampaign = 67543,
        NotSupportedExcludeFromBiddingValueForLinkedInCampaign = 67544,
        NotSupportedGoalAttributionModelForLinkedInCampaign = 67545,
        AdGroupStartDateCannotBeChangedForLinkedInCampaign = 67546,

        [DbErrorCodeMapping(-198001)]
        LinkedInCampaignUndeleteNotAllowed = 67547,

        NotSupportedGoalClickLookbackWindowForLinkedInCampaign = 67548,
        LinkedInCampaignAudienceEstimationBelowThreshold = 67549,
        LinkedInCampaignAdInReviewCannotPause = 67550,
        ApplyLinkedInOfflineConversionFailed = 67551,
        ApplyLinkedInOfflineConversionInternalError = 67552,

        #endregion

        ClarityEventNotFound = 600018,

        FeedReferencedInCampaign = 600019,

        NetflixTCAcceptedValueInvalid = 600020,

        CNResellerCodeDoesNotExist = 600021,

        AccountNotEnabledForBroadMatchOnlyCampaign = 600022,

        BidStrategyNotSupportedForBroadMatchOnlyCampaign = 600023,

        UnsupportMatchTypeForBroadMatchOnlyCampaign = 600024,

        BlockedSegmentIdsInvalid = 600025,

        AccountNotEligibleForBrandsafetyFeature = 600026,

        AccountNotEnabledForPerformanceMaxCampaignAssetGroupSearchTheme = 600027,
        DuplicatedBlockedSegmentIds = 600028,

        SearchThemeEntityLimitExceeded = 600029,
        TooLongSearchTheme = 600030,
        SearchThemeNameMissing = 600031,
        SearchThemeNameHasInvalidChars = 600032,
        DuplicateSearchThemeName = 600033,
        InvalidPredictiveMatchingSetting = 600034,

        RemappedGoogleNegativeContentLabelToBlockedSegments = 600035,
        AssetAIEnhancementOptoutAcceptedValueInvalid = 600036,
        PerformanceMaxAssetGroupFinalURLExtractTrackingParametersEnabled = 600037,
        PerformanceMaxEnhanceHTTPFinalURLEnabled = 600038,

        #region Modeled conversion uplifting report
        CustomerNotBePilotedForUpliftReport = 600050,
        GenerateUpliftReportFail = 600051,
        BlobUploadFailedForUpliftReport = 600052,
        GetUpliftReportStatusFail = 600053,
        #endregion

        PageFeedUrlContainsManualTaggingParameters = 600054,
        PageFeedUrlContainsInvalidCharacters = 600055,
        WebpageCriterionWebpageConditionArgumentContainsManualTaggingParameter = 600056,

        #region PMax New Customer Acquisition Goal
        PmaxNewCustomerAcquisitionNotEnabled = 600057,
        [DbErrorCodeMapping(-197900)]
        InvalidAdditionalValue = 600058,
        [DbErrorCodeMapping(-197901)]
        NewCustomerAcquisitionGoalDoesNotExist = 600059,
        [DbErrorCodeMapping(-197902)]
        NewCustomerAcquisitionAudienceCountExceedsLimit = 600060,
        [DbErrorCodeMapping(-197903)]
        DuplicateNewCustomerAcquisitionGoal = 600061,
        NewCustomerAcquisitionNoPurchaseGoal = 600062,
        InvalidBidStrategyForNewCustomerAcquisitionBidHigherMode = 600063,
        InvalidNewCustomerAcquisitionGoalId = 600064,
        PurchaseCampaignConversionGoalOnlyForNcaEnabledCampaign = 600065,
        InvalidAdditionalConversionValue = 600066,
        AudienceAssociationRequiredForNewCustomerAcquisitionGoal = 600067,
        CampaignLevelAdditionalValueNotSupportedForBidOnlyMode = 600068,
        #endregion

        #region Brand Kit (600100 - 600199)

        BrandKitNotEnabledForAccount = 600100,
        [DbErrorCodeMapping(-198100)]
        InvalidBrandKitId = 600101,
        DuplicateInBrandKitIds = 600102,
        InvalidBrandKitColorCode = 600103,
        InvalidBrandKitFontTypeface = 600104,
        InvalidBrandKitFontWeight = 600105,
        InvalidBrandKitFontTextAssetType = 600106,
        BrandKitNameTooLong = 600107,
        BrandKitNameMissing = 600108,
        BrandKitPaletteColorCountExceedsLimit = 600109,
        BrandKitColorNameTooLong = 600110,
        BrandKitImagesCountExceedsLimit = 600111,
        BrandKitSquareLogosCountExceedsLimit = 600112,
        BrandKitLandscapeLogosCountExceedsLimit = 600113,
        BrandKitIdsArrayShouldNotBeNullOrEmpty = 600114,
        BrandKitColorsArrayShouldNotBeNullorEmpty = 600115,
        BrandKitBusinessNameTooLong = 600116,
        BrandKitPhase2NotEnabledForAccount = 600117,
        BrandVoicePersonalityTooLong = 600118,
        BrandVoiceTonesCountExceedsLimit = 600119,
        BrandVoiceTonesTooLong = 600120,
        BrandKitPaletteNameTooLong = 600121,
        #endregion

        #region Lifetime budget (600200 - 600219)

        AccountNotEnabledForCampaignLevelDates = 600200,
        CampaignLevelDatesNotEnabled = 600201,
        CampaignStartDateNotSet = 600202,
        CampaignEndDateNotSet = 600203,
        CampaignEndDateExceedsOneYear = 600204,
        CannotUpdateStartDateAfterCampaignStart = 600205,
        CannotUpdateBudgetTypeAfterCampaignStart = 600206,
        CannotUpdateUseCampaignLevelAfterCampaignStart = 600207,
        AdGroupLevelDatesBudgetTypeCannotBeUpdated = 600208,
        AdGroupLevelDatesCannotBeUpdated = 600209,
        CampaignSubTypeNotSupportedForCampaignLevelDates = 600210,
        CampaignLifetimeBudgetAmountIsAboveLimit = 600211,

        #endregion

        #region AIGC Clipchamp (600220 - 600244)
        AccountNotEnabledForVideoAdsGeneration = 600220,
        NoAudioMatchesFilter = 600221,
        #endregion

        #region Invest MSAP Integration error codes (610001 - 610100)
        XandrInvestServiceError = 610001,
        AccountIdCannotBeNullForUPUetLinking = 610002,
        UniversalPixelUuidCannotBeNull = 610003,
        UniversalPixelCannotLinkToTwoTags = 610004,
        UPUetLinkFailed = 610005,
        UPUetUnlinkFailed = 610006,
        OneUPUetLinkAtOneTime = 610007,
        UniversalPixelNotFound = 610008,
        XandrMemberNotFoundOrInvalid = 610009,
        XandrAdvertiserNotFoundOrInvalid = 610010,
        XandrTokenNotFoundOrInvalid = 610011,
        AccountNotMappedToXandr = 610012,
        CustomerNotMappedToXandr = 610013,
        #endregion

        #region Asse Group Url Target (610200 - 610205)

        AssetGroupUrlTargetDuplicated = 610200,
        AssetGroupUrlTargetValueDuplicated = AssetGroupUrlTargetDuplicated + 1,
        AssetGroupUrlTargetConditionInvalid = AssetGroupUrlTargetValueDuplicated + 1,
        AssetGroupUrlTargetOperatorInvalid = AssetGroupUrlTargetConditionInvalid + 1,
        AssetGroupUrlTargetValueInvalid = AssetGroupUrlTargetOperatorInvalid + 1,
        AssetGroupUrlTargetInvalid = AssetGroupUrlTargetValueInvalid + 1,

        #endregion

        ConsentModeObjectStoreFailed = 611001,
        SendConsentModeEmailFailed = 611002,
        WriteConsentModeDecisionFailed = 611003,
        ConsentModeDecisionAlreadyExist = 611004,
        ReadObjectStoreFailed = 611005,

        NotEnabledForHTML5Asset = 611005,
        InvalidColumn = 611007,

        SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal = 611008,
        InvalidEventParameterForAutoGoal = 611009,
        InvalidGoalTypeForAutoGoal = 611010,
        IsAutoGoalFieldCannotBeChanged = 611011,
        EventParameterNotAllowToChangeForAutoGoal = 611012,
        GoalCategoryCannotBeChangedForAutoGoal = 611013,
        MustHaveCategoryForAutoGoal = 611014,
        TagNotAllowToChangeForAutoGoal = 611015,
        GoalApplicationStoreIdCannotBeChanged = 611016,
        GoalApplicationPlatformCannotBeChanged = 611017,
    }
}