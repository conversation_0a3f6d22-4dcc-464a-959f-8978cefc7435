﻿#if V2
namespace Microsoft.Advertising.Advertiser.Api.V2
{
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Configuration;
    using CampaignMiddleTierTest.Framework.DBValidators;
    using CampaignMiddleTierTest.Framework.MiddletierTestObjects;
    using CampaignMiddleTierTest.Framework.Utilities;
    using CampaignTest.Framework.Constants;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.EventTracking;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages.EventTracking;
    using Microsoft.AdCenter.Shared.MT.ODataClient;
    using Microsoft.Advertising.Advertiser.MT.EventTrackingContract;
    using Microsoft.BingAds.TaskEngine.ObjectModel;
    using Microsoft.Data.SqlClient;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text.RegularExpressions;
    using static NPOI.HSSF.Util.HSSFColor;
    using AddGoalsRequest = Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages.EventTracking.AddGoalsRequest;

    //TODO: Once Get goals are ready, we need to add Get goals. We also need to have more functional tests
    [TestClass]
    public sealed class GoalE2E : CampaignTestBase
    {
        private CustomerInfo cInfo = DefaultCustomer;
        private CustomerInfo cInfoForClarityTagPilot = ClarityTagIntegrationPilotCustomer;
        private CustomerInfo cInfoForCustomEventToolPilot = CustomEventToolPilotCustomer;
        private CustomerInfo sharedLibraryCustomerInfo = SharedLibraryPilotCustomer;
        private CustomerInfo viewThroughConversionCustomerInfo = ViewThroughConversionPilotCustomer;
        private CustomerInfo conversionGoalAttributionModelCustomerInfo = ConversionGoalAttributionModelCustomer;
        private CustomerInfo cInfoForEventToolPilot = EventToolCustomer;
        private CustomerInfo goalBulkPilot;
        private CustomerInfo linkedInCustomerInfo;
        private CustomerInfo mobileAppCampaignCustomerInfo;
        private CustomerInfo conversionDelayCustomer;
        private static long tagId;
        private static long sharedLibraryTagId;
        private static long viewThroughPilotCustomerTagId;
        private static long thirdPartyPilotCustomerTagId;
        private static long conversionGoalAttributionModelCustomerTagId;
        private static long cInfoForEventToolPilotTagId;
        private static long goalBulkPilotTagId;
        private static long linkedInCustomerTagId;
        private static long conversionDelayCustomerTagId;
        private List<long> taskIdNeedToRemove = new List<long>();
        private static long mobileAppCampaignTagId;

        private List<string> cosmosFileNeedToRemove = new List<string>();
        private static readonly Func<int, int, string> MsClickIdTaggingUrl =
            (customerId, accountId) =>
                string.Format(ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})?$select=MsClickIdTaggingEnabled",
                    customerId, accountId);

        [TestInitialize]
        public void Init()
        {
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(cInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            tagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used for all goal tests is " + tagId);

            tagObj = TestUETV2Operations.PostTag(ViewThroughConversionPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            viewThroughPilotCustomerTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by view through conversion pilot customer is " + tagId);

            tagObj = TestUETV2Operations.PostTag(ThirdPartyConversionPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            thirdPartyPilotCustomerTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by third party conversion pilot customer is " + tagId);

            tagObj = TestUETV2Operations.PostTag(conversionGoalAttributionModelCustomerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            conversionGoalAttributionModelCustomerTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by conversion goal attribution model pilot customer is " + (long)tagObj.Id.Value);

            tagObj = TestUETV2Operations.PostTag(cInfoForEventToolPilot, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            cInfoForEventToolPilotTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by conversion goal event tool pilot customer is " + (long)tagObj.Id.Value);

            goalBulkPilot = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.ProductConversionGoalFlagId, Features.InStoreTransaction, Features.InStoreVisit, Features.EnableGoalBulk });

            tagObj = TestUETV2Operations.PostTag(goalBulkPilot, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");
            goalBulkPilotTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by goal bulk pilot customer is " + (long)tagObj.Id.Value);

            linkedInCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.LinkedInCampaign });
            tagObj = TestUETV2Operations.PostTag(linkedInCustomerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");
            linkedInCustomerTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by LinkdedIn campaign pilot customer is " + (long)tagObj.Id.Value);

            mobileAppCampaignCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.MobileAppCampaignConversionGoal });
            tagObj = TestUETV2Operations.PostTag(mobileAppCampaignCustomerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");
            mobileAppCampaignTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by Mobile App campaign pilot customer is " + (long)tagObj.Id.Value);
            
            conversionDelayCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.ConversionDelayMetrics });
            tagObj = TestUETV2Operations.PostTag(conversionDelayCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");
            conversionDelayCustomerTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used by Conversion Delay Metrics pilot customer is " + (long)tagObj.Id.Value);
        }

        [TestMethod, Priority(2)]
        public void Goal_E2E_GoalBulkEnabled()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(goalBulkPilotTagId);
            var obj = TestUETV2Operations.PostGoal(goalBulkPilot, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(goalBulkPilot, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(goalBulkPilot);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(goalBulkPilot, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(goalBulkPilot, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BVT()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InvalidRegex()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            goal.Category = "*Category";
            goal.CategoryOperator = "RegularExpression";
            goal.Action = "*action";
            goal.ActionOperator = "RegularExpression";
            goal.Label = "*label";
            goal.LabelOperator = "RegularExpression";

            var obj = TestUETV2Operations.PostGoal(cInfo, goal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidRegularExpression", obj.value[0].Code.ToString());

            dynamic urlGoal = TestUETV2Operations.CreateDestinationGoal(tagId);
            urlGoal.UrlString = "*testUrl";
            urlGoal.Operator = "RegularExpression";

            var obj2 = TestUETV2Operations.PostGoal(cInfo, urlGoal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidRegularExpression", obj2.value[0].Code.ToString());
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore("No Data Source")]
        public void Goal_E2E_GetGoalRecommendation_Success()
        {
            var goalRecommendationResult = TestUETV2Operations.GetGoalRecommendation(cInfo);
            Assert.IsNotNull(goalRecommendationResult);
            Console.WriteLine(JsonConvert.SerializeObject(goalRecommendationResult["value"]));
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore("No Data Source")]
        public void Goal_E2E_GetGoalRecommendation_FilterByExistingGoal_Success()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);

            //step1: created auto-goal for autoConvCustomer
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);
            dynamic goal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "AddToCart");
            var createGoalResult = TestUETV2Operations.CreateGoals(autoConvCustomer, new dynamic[] { goal });
            Assert.IsNotNull(createGoalResult);

            //step2: mock recommendation data in object-store, should have duplicate data with previous auto-goal
            string cosmosPath = AutoConversionScheduleBackTests.CosmosBasePath
                + (TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName) ? "CI/" : "SI/") 
                + DateTime.UtcNow.ToString("yyyy/MM/")
                + string.Format("CreateConversionGoal_AutoConversion_Rec.{0}.csv", DateTime.UtcNow.ToString("yyyy-MM-dd"));
            var r1 = AutoConversionScheduleBackTests.CreateEventGoalRecommendation(autoConvCustomer.CustomerId, autoConvCustomer.AccountIds[0], uetTagId, "TestTagName");
            var r2 = AutoConversionScheduleBackTests.CreateEventGoalRecommendation(autoConvCustomer.CustomerId, autoConvCustomer.AccountIds[0], uetTagId, "TestTagName");
            r1.GoalCategory = (int)GoalCategory.BeginCheckout;
            r2.GoalCategory = (int)GoalCategory.AddToCart; //duplicate with existing auto goal
            var recommendations = new List<AutoConversionScheduleBackTests.EventGoalRecommendation> { r1, r2 };
            string localPath = AutoConversionScheduleBackTests.BuildLocalCsvFileForPMax(recommendations);
            AutoConversionScheduleBackTests.AuthForCosmos();
            AutoConversionScheduleBackTests.UploadToCosmos(localPath, cosmosPath);
            cosmosFileNeedToRemove.Add(cosmosPath);
            var taskExecution = AutoConversionScheduleBackTests.CreateAutoConversionRecommendationTask(cInfo, out Guid req, out Guid tId, out Guid sId, RunResultState.RetryNeeded);
            taskIdNeedToRemove.Add(taskExecution.TaskId);
            Assert.IsNotNull(taskExecution);

            //step3: get goal recommendation and check
            var goalRecommendationResult = TestUETV2Operations.GetGoalRecommendation(autoConvCustomer);
            Assert.IsNotNull(goalRecommendationResult);
            Assert.IsNotNull(goalRecommendationResult.value);
            //we have mocked 2 recommendations, 1 should be filter out by existing auto goal
            Assert.AreEqual(goalRecommendationResult.value.Count, 1);
            Console.WriteLine(JsonConvert.SerializeObject(goalRecommendationResult["value"]));
            
            AutoConversionScheduleBackTests.CleanUpForAutoConversion(cosmosFileNeedToRemove, taskIdNeedToRemove, (int)autoConvCustomer.UserId);
        }
        
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore("No Data Source")]
        public void Goal_E2E_GetGoalRecommendation_NotFilterByExistingGoal_Success()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);

            //step1: created auto-goal for autoConvCustomer
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);
            dynamic goal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "AddToCart");
            var createGoalResult = TestUETV2Operations.CreateGoals(autoConvCustomer, new dynamic[] { goal });
            Assert.IsNotNull(createGoalResult);

            //step2: mock recommendation data in object-store, should have duplicate data with previous auto-goal
            string cosmosPath = AutoConversionScheduleBackTests.CosmosBasePath
                + (TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName) ? "CI/" : "SI/") 
                + DateTime.UtcNow.ToString("yyyy/MM/")
                + string.Format("CreateConversionGoal_AutoConversion_Rec.{0}.csv", DateTime.UtcNow.ToString("yyyy-MM-dd"));
            var r1 = AutoConversionScheduleBackTests.CreateEventGoalRecommendation(autoConvCustomer.CustomerId, autoConvCustomer.AccountIds[0], uetTagId, "TestTagName");
            var r2 = AutoConversionScheduleBackTests.CreateEventGoalRecommendation(autoConvCustomer.CustomerId, autoConvCustomer.AccountIds[0], uetTagId, "TestTagName");
            r1.GoalCategory = (int)GoalCategory.PageView;
            r2.GoalCategory = (int)GoalCategory.PageView;
            var recommendations = new List<AutoConversionScheduleBackTests.EventGoalRecommendation> { r1, r2 };
            string localPath = AutoConversionScheduleBackTests.BuildLocalCsvFileForPMax(recommendations);
            AutoConversionScheduleBackTests.AuthForCosmos();
            AutoConversionScheduleBackTests.UploadToCosmos(localPath, cosmosPath);
            cosmosFileNeedToRemove.Add(cosmosPath);
            var taskExecution = AutoConversionScheduleBackTests.CreateAutoConversionRecommendationTask(cInfo, out Guid rId, out Guid tId, out Guid sId, RunResultState.RetryNeeded);
            taskIdNeedToRemove.Add(taskExecution.TaskId);
            Assert.IsNotNull(taskExecution);

            //step3: get goal recommendation and check
            var goalRecommendationResult = TestUETV2Operations.GetGoalRecommendation(autoConvCustomer);
            Assert.IsNotNull(goalRecommendationResult);
            Assert.IsNotNull(goalRecommendationResult.value);
            Assert.AreEqual(goalRecommendationResult.value.Count, recommendations.Count);
            Console.WriteLine(JsonConvert.SerializeObject(goalRecommendationResult["value"]));

            AutoConversionScheduleBackTests.CleanUpForAutoConversion(cosmosFileNeedToRemove, taskIdNeedToRemove, (int)autoConvCustomer.UserId);
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BatchCreateGoal_Success()
        {
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(tagId);
            dynamic goal2 = TestUETV2Operations.CreateEventGoal(tagId);
            var createGoalResult = TestUETV2Operations.CreateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(createGoalResult);

            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            var goalId2 = (long)createGoalResult["value"][1].GoalId;
            goal1.Id = goalId1;
            goal2.Id = goalId2;
            Trace.WriteLine("Goal1 Id is " + goalId1);
            Trace.WriteLine("Goal2 Id is " + goalId2);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId1);
            TestUETV2Operations.AssertGoalEqual(goal1, actualGoal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId2);
            TestUETV2Operations.AssertGoalEqual(goal2, actualGoal);
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BatchCreateGoal_OneSuccessOneFail()
        {
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(tagId, GoalName: "CustomEventGoal");
            dynamic goal2 = TestUETV2Operations.CreateEventGoal(tagId, GoalName: "CustomEventGoal");
            var createGoalResult = TestUETV2Operations.CreateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(createGoalResult);

            Assert.AreEqual("DuplicateGoalName", createGoalResult["value"][1].Errors.ToObject<AdsApiError[]>()[0].Code.ToString());

            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            goal1.Id = goalId1;
            Trace.WriteLine("Goal1 Id is " + goalId1);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId1);
            TestUETV2Operations.AssertGoalEqual(goal1, actualGoal);
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BatchUpdateGoal_Success()
        {
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(tagId);
            dynamic goal2 = TestUETV2Operations.CreateEventGoal(tagId);
            var createGoalResult = TestUETV2Operations.CreateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(createGoalResult);

            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            var goalId2 = (long)createGoalResult["value"][1].GoalId;
            goal1.Id = goalId1;
            goal2.Id = goalId2;
            Trace.WriteLine("Goal1 Id is " + goalId1);
            Trace.WriteLine("Goal2 Id is " + goalId2);

            goal1.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            goal2.GoalCategory = "Purchase";
            var failedGoalResult = TestUETV2Operations.UpdateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(failedGoalResult);
            Assert.IsTrue(failedGoalResult.Count == 0);

            var actualGoal1 = TestUETV2Operations.GetGoalByGoalId(cInfo, goal1.Id);
            TestUETV2Operations.AssertGoalEqual(goal1, actualGoal1);

            var actualGoal2 = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId2);
            TestUETV2Operations.AssertGoalEqual(goal2, actualGoal2);
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BatchUpdateGoal_OneSuccessOneFail()
        {
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(tagId);
            dynamic goal2 = TestUETV2Operations.CreateEventGoal(tagId);
            var createGoalResult = TestUETV2Operations.CreateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(createGoalResult);

            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            var goalId2 = (long)createGoalResult["value"][1].GoalId;
            goal1.Id = goalId1;
            goal2.Id = goalId2;
            Trace.WriteLine("Goal1 Id is " + goalId1);
            Trace.WriteLine("Goal2 Id is " + goalId2);

            goal1.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            goal2.Name = goal1.Name;
            var failedGoalResult = TestUETV2Operations.UpdateGoals(cInfo, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(failedGoalResult);
            Assert.IsTrue(failedGoalResult.Count == 1);
            Assert.IsTrue(failedGoalResult[0].Key == 1);
            Assert.AreEqual("DuplicateGoalName", failedGoalResult[0].Value.ToObject<AdsApiError[]>()[0].Code.ToString());

            var actualGoal1 = TestUETV2Operations.GetGoalByGoalId(cInfo, goal1.Id);
            TestUETV2Operations.AssertGoalEqual(goal1, actualGoal1);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore("Feature Closed")]
        public void Goal_AgencyLink()
        {
            var customerInfo1 = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.EnableAudienceBidBoosting, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            var uetTagId = TestUetTag.GetUetTagId(customerInfo1);

            #region setup agency link
            var agencyCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 0, true, Features.EnableAudienceBidBoosting, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.CustomerMatch);
            ClientCenter.MT.Service.AgencyBulkLinkJobAddResponse jobAddResponse = null;
            WCFCaller.Call(TestSetting.ClientCenterProxy, () => jobAddResponse = TestSetting.ClientCenterProxy.AgencyBulkLinkJobAdd(new ClientCenter.MT.Service.AgencyBulkLinkJobAddRequest
            {
                UserToken = agencyCustomer.UserToken,
                TrackingId = Guid.NewGuid().ToString(),
                Link = new ClientCenter.MT.Service.AgencyBulkLinkJob
                {
                    AgencyCustomerId = agencyCustomer.CustomerId,
                    AgencyAccountLinks = new ClientCenter.MT.Service.AgencyAccountLink[]
                    {
                        new ClientCenter.MT.Service.AgencyAccountLink
                        {
                            AccountId = customerInfo1.AccountIds[0],
                            AdvertiserAccountNumber = customerInfo1.AccountNumbers[0]
                        }
                    },
                    Type = (byte)ClientCenter.MT.Service.AgencyLinkJobType.AgencySendingInvitation,
                    LinkPermissionTypeId = (byte)Microsoft.Advertiser.ClientCenter.MT.Proxy.AccountLinkPermissionType.AccountCampaignManagement,
                    EffectiveDateInUtc = DateTime.UtcNow.AddMinutes(-1),
                }
            }));
            ClientCenter.MT.Service.SearchLinkJobResponse jobSearchResponse = null;
            WCFCaller.Call(TestSetting.ClientCenterProxy, () => jobSearchResponse = TestSetting.ClientCenterProxy.SearchLinkJob(new ClientCenter.MT.Service.SearchLinkJobRequest
            {
                TrackingId = Guid.NewGuid().ToString(),
                Predicates = new ClientCenter.MT.Service.Predicate[]
                {
                    new ClientCenter.MT.Service.Predicate
                    {
                        Field = "AccountId",
                        Operator = ClientCenter.MT.Service.PredicateOperator.In,
                        Values = customerInfo1.AccountIds[0].ToString()
                    },
                    new ClientCenter.MT.Service.Predicate
                    {
                        Field = "ReceiverCustomerId",
                        Operator = ClientCenter.MT.Service.PredicateOperator.Equals,
                        Values = customerInfo1.CustomerId.ToString()
                    }
                },
                Fields = new string[0],
                UserToken = customerInfo1.UserToken
            }));
            ClientCenter.MT.Service.AgencyBulkLinkJobUpdateResponse jobUpdateResponse = null;
            WCFCaller.Call(TestSetting.ClientCenterProxy, () => jobUpdateResponse = TestSetting.ClientCenterProxy.AgencyBulkLinkJobUpdate(new ClientCenter.MT.Service.AgencyBulkLinkJobUpdateRequest
            {
                TrackingId = Guid.NewGuid().ToString(),
                AgencyBulkLinkUpdateInfo = new ClientCenter.MT.Service.AgencyBulkLinkRequestInfo[]
                {
                    new ClientCenter.MT.Service.AgencyBulkLinkRequestInfo
                    {
                        AgencyLinkJobId = jobAddResponse.AgencyLinkJobIds.First(),
                        LifecycleStatusId = (byte)ClientCenter.MT.Service.AgencyLinkJobStatus.Accepted,
                        LinkPermissionTypeId = (byte)Microsoft.Advertiser.ClientCenter.MT.Proxy.AccountLinkPermissionType.AccountCampaignManagement,
                        Timestamp = jobSearchResponse.LinkJobs.First().Timestamp,
                    }
                },
                UserToken = customerInfo1.UserToken
            }));
            #endregion setup agency link

            // add customer level goal failed
            dynamic goal = TestUETV2Operations.CreateEventGoal(uetTagId, false);
            var response = TestUETV2Operations.PostGoal(customerInfo1, goal, HttpStatusCode.BadRequest, accountId: customerInfo1.AccountIds[0], user:agencyCustomer);
            Assert.AreEqual("PermissionDenied", response.value[0].Code.ToString());

            // add account level goal ok
            goal = TestUETV2Operations.CreateEventGoal(uetTagId, true);
            response = TestUETV2Operations.PostGoal(customerInfo1, goal, HttpStatusCode.OK, accountId: customerInfo1.AccountIds[0], user: agencyCustomer);
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_Create_Success()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);

            //auto goal in purchase category
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "Purchase");
            goal1.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            goal1.ActionOperator = "EqualsTo";

            //auto goal in add-to-cart category
            dynamic goal2 = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "Clarity", goalCategory: "AddToCart");
            goal2.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.AddToCart);
            goal2.ActionOperator = "EqualsTo";

            var createGoalResult = TestUETV2Operations.CreateGoals(autoConvCustomer, new dynamic[] { goal1, goal2 });
            Assert.IsNotNull(createGoalResult);

            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            var goalId2 = (long)createGoalResult["value"][1].GoalId;
            goal1.Id = goalId1;
            goal2.Id = goalId2;
            Trace.WriteLine("Goal1 Id is " + goalId1);
            Trace.WriteLine("Goal2 Id is " + goalId2);

            var goals = TestUETV2Operations.GetGoals(autoConvCustomer);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");
            for (int i = 0; i < goals.value.Count; i++)
            {
                if (goals.value[i].Id == goalId1)
                {
                    TestUETV2Operations.AssertGoalEqual(goal1, goals.value[i], isVerifiedAutoGoal: true);
                }
                if (goals.value[i].Id == goalId2)
                {
                    TestUETV2Operations.AssertGoalEqual(goal2, goals.value[i], isVerifiedAutoGoal: true);
                }
            }

            goals = TestUETV2Operations.GetGoalsFilterByAutoGoal(autoConvCustomer);
            Assert.IsTrue(goals.value.Count == 2, "goals count");
            Assert.IsTrue(Convert.ToInt64(goals["@odata.count"].ToString()) == 2, "goals count");

            //"AutoGoalEnabled" should be auto enable if we created an auto goal
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", "true");
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_Update_Success()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);

            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);
            dynamic goal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory:"Purchase");
            goal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            goal.ActionOperator = "EqualsTo";

            var obj = TestUETV2Operations.PostGoal(autoConvCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(autoConvCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifiedAutoGoal:true);

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(autoConvCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(autoConvCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifiedAutoGoal: true);

            //"AutoGoalEnabled" should be auto enable if we created an auto goal
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", "true");
        }

        [Ignore]
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_UpdateIsAutoGoal_Failure()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);

            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-1" + StringUtil.GenerateUniqueId(),
                "some description");
            var tagObj = TestUETV2Operations.PostTag(autoConvCustomer, tag);
            var tagId_1 = (long)tagObj.Id.Value;
            dynamic tag_2 = TestUETV2Operations.CreateTag(
                "TestTag-1" + StringUtil.GenerateUniqueId(),
                "some description");
            var tagObj_2 = TestUETV2Operations.PostTag(autoConvCustomer, tag_2);
            var tagId_2 = (long)tagObj_2.Id.Value;

            var goalCategory = "Purchase";
            dynamic autoGoal = TestUETV2Operations.CreateEventGoal(tagId_1, isAutoGoal: true, goalSourceId: "UET2", goalCategory: goalCategory);
            autoGoal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            autoGoal.ActionOperator = "EqualsTo";
            dynamic autoGoal_2 = TestUETV2Operations.CreateEventGoal(tagId_1, isAutoGoal: true, goalCategory: "AddToCart"); //not use same category, should success
            autoGoal_2.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.AddToCart);
            autoGoal_2.ActionOperator = "EqualsTo";
            var createGoalResult = TestUETV2Operations.CreateGoals(autoConvCustomer, new dynamic[] { autoGoal, autoGoal_2});
            Assert.IsNotNull(createGoalResult);
            var goalId1 = (long)createGoalResult["value"][0].GoalId;
            var goalId2 = (long)createGoalResult["value"][1].GoalId;

            autoGoal = TestUETV2Operations.GetGoalByGoalId(autoConvCustomer, goalId1);
            autoGoal.IsAutoGoal = false; //attempt to update "isAutoGoal" fileld for auto goal 1 
            autoGoal_2 = TestUETV2Operations.GetGoalByGoalId(autoConvCustomer, goalId2);
            autoGoal_2.Tag.Id = tagId_2; //attempt to change tag for goal 2 
            var response = TestUETV2Operations.UpdateGoals(autoConvCustomer, new dynamic[] { autoGoal, autoGoal_2 });

            //var failedGoalResult = TestUETV2Operations.UpdateGoals(cInfo, new dynamic[] { goal1, goal2 });

            Assert.AreEqual("IsAutoGoalFieldCannotBeChanged", response[0].Value[0].Code.ToString());
            Assert.AreEqual("TagNotAllowToChangeForAutoGoal", response[1].Value[0].Code.ToString());
            
            var goals = TestUETV2Operations.GetGoals(autoConvCustomer);
            Assert.AreEqual(tagId_1.ToString(), goals.value[0].Tag.Id.ToString());
            Assert.AreEqual(tagId_1.ToString(), goals.value[1].Tag.Id.ToString());
            Assert.AreEqual("True", goals.value[0].IsAutoGoal.ToString());
            Assert.AreEqual("True", goals.value[1].IsAutoGoal.ToString());
        }

        [Ignore]
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_Create_DuplicateCategoryAndTag_DifferentReq_Failure()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);

            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);
            var goalCategory = "Purchase";

            dynamic autoGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: goalCategory);
            autoGoal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            autoGoal.ActionOperator = "EqualsTo";

            var createAutoGoal = TestUETV2Operations.PostGoal(autoConvCustomer, autoGoal);
            Assert.IsNotNull(createAutoGoal, "goal object");

            dynamic manualGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: false, goalCategory: goalCategory);
            var createManualGoal = TestUETV2Operations.PostGoal(autoConvCustomer, manualGoal, HttpStatusCode.BadRequest);
            Assert.AreEqual("SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal", createManualGoal.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_Create_DuplicateCategoryAndTag_SameReq_PartialSuccess()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);

            var goalCategory = "Purchase";
            dynamic autoGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: goalCategory);
            autoGoal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            autoGoal.ActionOperator = "EqualsTo";

            dynamic manualGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: false, goalCategory: goalCategory);

            dynamic autoGoal_2 = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalCategory: "AddToCart"); //not use same category, should success
            autoGoal_2.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.AddToCart);
            autoGoal_2.ActionOperator = "EqualsTo";

            var createGoalResult = TestUETV2Operations.CreateGoals(autoConvCustomer, new dynamic[] { autoGoal, manualGoal, autoGoal_2});
            Assert.IsNotNull(createGoalResult);
            for (int i= 0; i< 3; i++)
            {
                if(createGoalResult.value[i].LineItemId.ToString() == "0" || createGoalResult.value[i].LineItemId.ToString() == "1" )
                {
                    Assert.IsTrue(createGoalResult.value[i].Errors.ToString().Contains("SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal"));
                }
                else
                {
                    Assert.IsFalse(createGoalResult.value[i].Errors.ToString().Contains("SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal"));
                }
            }

            //should only have 1 goal
            var goals = TestUETV2Operations.GetGoals(autoConvCustomer);
            Assert.IsTrue(goals.value.Count == 1, "goals count");
            Assert.AreEqual("AddToCart", goals.value[0].GoalCategory.ToString());
            Assert.AreEqual("True", goals.value[0].IsAutoGoal.ToString());
        }

        [Ignore]
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_Create_InDifferentCategory_Success()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);

            dynamic autoGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "Purchase");
            autoGoal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            autoGoal.ActionOperator = "EqualsTo";

            var createAutoGoal = TestUETV2Operations.PostGoal(autoConvCustomer, autoGoal);
            Assert.IsNotNull(createAutoGoal, "goal object");

            dynamic manualGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: false, goalCategory: "AddToCart");
            var createManualGoal = TestUETV2Operations.PostGoal(autoConvCustomer, manualGoal);
            Assert.IsNotNull(createManualGoal, "goal object");

            dynamic autoGoal_Subscribe = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "Subcribe");
            autoGoal_Subscribe.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Subcribe);
            autoGoal_Subscribe.ActionOperator = "EqualsTo";

            var createAutoGoal_Subscribe = TestUETV2Operations.PostGoal(autoConvCustomer, autoGoal_Subscribe);
            Assert.IsNotNull(createAutoGoal_Subscribe, "goal object");
        }

        [Ignore]
        [TestMethod, Priority(1)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AutoGoal_UpdateManualGoalToSameCategoryWithAutoGoal_Failure()
        {
            var autoConvCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.AutoConversion, Features.CustomerHierarchyFlagId, Features.SharedLibraryFlagId, Features.AIM, Features.SharedLibraryForOtherAudienceType);
            UpdateAccounts.GetAccountPropertyTest(autoConvCustomer, "AutoGoalEnabled", null);
            var uetTagId = TestUetTag.GetUetTagId(autoConvCustomer);

            dynamic autoGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: true, goalSourceId: "UET2", goalCategory: "Purchase");
            autoGoal.Action = TestUETV2Operations.GenerateEventActionForAutoGoal(GoalCategory.Purchase);
            autoGoal.ActionOperator = "EqualsTo";
            var createAutoGoal = TestUETV2Operations.PostGoal(autoConvCustomer, autoGoal);
            Assert.IsNotNull(createAutoGoal, "goal object");

            dynamic manualGoal = TestUETV2Operations.CreateEventGoal(uetTagId, isAutoGoal: false, goalCategory: "AddToCart");
            var createManualGoal = TestUETV2Operations.PostGoal(autoConvCustomer, manualGoal);
            Assert.IsNotNull(createManualGoal, "goal object");

            var goalId = (long)createManualGoal.value;
            manualGoal = TestUETV2Operations.GetGoalByGoalId(autoConvCustomer, goalId);
            manualGoal.GoalCategory = "Purchase"; // attempt update manual goal to same <Category, tagId> with existing aut goal.
            var response = TestUETV2Operations.UpdateGoals(autoConvCustomer, new dynamic[] { manualGoal });
            Assert.AreEqual("SameCategoryAndTagNotAllowedForAutoGoalAndManualGoal", response[0].Value[0].Code.ToString());
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_NotReadOnly_Update_Successful()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            Assert.AreEqual("False", "" + actualGoal.IsReadOnly);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_IsReadOnly_Update_UnSuccessful()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId, isReadOnlyGoal: true);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            var goalName = goal.Name;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            goal.IsReadOnly = false;
            TestUETV2Operations.UpdateGoal(cInfo, goal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
            goal.Name = goalName;

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            Assert.AreEqual("True", "" + actualGoal.IsReadOnly);
        }       

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_OfflineConversionGoal_EnhancedConversion_Pilot()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions, Features.InStoreTransaction });

            dynamic goal = TestUETV2Operations.CreateOfflineGoal();
            goal.IsEnhancedConversionsEnabled = true;
            var obj = TestUETV2Operations.PostGoal(customer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            Assert.AreEqual("True", actualGoal.IsEnhancedConversionsEnabled.ToString());
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Test_Bracket_Format_Success()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            goal.Name = "Booking {Static Pixel} (Shell#3)-" + StringUtil.GenerateUniqueId();

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        public class SetClarityEventRequestBody
        {
            public ClarityEventInfo ClarityEventInfo { get; set; }
        }
        public class ClarityEventInfo
        {
            public string UetTagId { get; set; }

            public string SetupUrl { get; set; }

            public ClarityTrigger Trigger { get; set; }

            public string Status { get; set; }

            public IEnumerable<EventParameter> Data { get; set; }
        }
        public class ClarityTrigger
        {
            public string EventType { get; set; }
            public IDictionary<string, object> EventTarget { get; set; }
        }
        public class EventParameter
        {
            public string Name { get; set; }

            public string Value { get; set; }

            public string Source { get; set; }

            public string DataType { get; set; }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void ClarityGoalSetup_Success()
        {
            dynamic tag = TestUETV2Operations.CreateTagClarityTagEnabled(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfoForCustomEventToolPilot, tag);
            var tagWithClarityId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateEventGoal(tagWithClarityId);

            var goalObj = TestUETV2Operations.PostGoal(cInfoForCustomEventToolPilot, goal);
            Assert.IsNotNull(goalObj, "goal object");
            Assert.IsNotNull(goalObj.value, "goal object id");
            var goalId = (long)goalObj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var originalEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, originalEventGoal);

            var clarityEventRequestBody = new SetClarityEventRequestBody
            {
                ClarityEventInfo = new ClarityEventInfo
                {
                    UetTagId = tagWithClarityId.ToString(),
                    SetupUrl = "https://www.xyztest123.com",
                    Trigger = new ClarityTrigger
                    {
                        EventType = "click",
                        EventTarget = new Dictionary<string, object>
                        {
                            { "h","21zsveaf" }, { "hb","21zsveaf" }, { "t","Add to cart" }
                        }
                    },
                    Status = "Active",
                    Data = new List<EventParameter>()
                    {
                        new EventParameter
                        {
                            Name = "event_action",
                            Value = "Purchase",
                            Source = "preDefined",
                            DataType = "string",
                        }
                    },
                }
            };
            var clarityId = TestUETV2Operations.SetClarityEventInfo(cInfoForCustomEventToolPilot, goalId, clarityEventRequestBody);
            Trace.WriteLine("Goal ClarityEventDefinitionId is " + (string)clarityId.value);

            var currentEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);

            Trace.WriteLine("Goal ClarityEventDefinitionId is " + (string)currentEventGoal.ClarityEventDefinitionId);
            Trace.WriteLine("Goal ClarityEventDefinitionObject is " + (string)currentEventGoal.ClarityEventDefinitionObject);
            Trace.WriteLine("Goal Label is " + (string)currentEventGoal.Label);
            Assert.AreEqual((string)clarityId.value, (string)currentEventGoal.ClarityEventDefinitionId);

            var clarityEventInfo = TestUETV2Operations.GetClarityEventInfo(cInfoForCustomEventToolPilot, goalId);
            Trace.WriteLine("ClarityEventInfo id is " + (string)clarityEventInfo.Id);
            Assert.AreEqual((string)clarityId.value, (string)clarityEventInfo.Id);

            goal.ClarityEventDefinitionId = null;
            goal.ClarityEventDefinitionObject = null;

            var updateResponse = TestUETV2Operations.UpdateGoal(cInfoForCustomEventToolPilot, goal);
            Assert.AreEqual(updateResponse["value"].Value, 0);

            var finalGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            Assert.IsNull((string)finalGoal.ClarityEventDefinitionId);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [TestCategory("SIOnly")]
        [Ignore]
        public void ClarityGoalSetup_ShouldDeleteEventIfTagIdChanges()
        {
            dynamic tag = TestUETV2Operations.CreateTagClarityTagEnabled(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfoForCustomEventToolPilot, tag);
            var tagWithClarityId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateEventGoal(tagWithClarityId);

            var goalObj = TestUETV2Operations.PostGoal(cInfoForCustomEventToolPilot, goal);
            Assert.IsNotNull(goalObj, "goal object");
            Assert.IsNotNull(goalObj.value, "goal object id");
            var goalId = (long)goalObj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var originalEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, originalEventGoal);

            var clarityEventRequestBody = new SetClarityEventRequestBody
            {
                ClarityEventInfo = new ClarityEventInfo
                {
                    UetTagId = tagWithClarityId.ToString(),
                    SetupUrl = "https://www.xyztest123.com",
                    Trigger = new ClarityTrigger
                    {
                        EventType = "click",
                        EventTarget = new Dictionary<string, object>
                        {
                            { "h","21zsveaf" }, { "hb","21zsveaf" }, { "t","Add to cart" }
                        }
                    },
                    Status = "Active",
                    Data = new List<EventParameter>()
                    {
                        new EventParameter
                        {
                            Name = "event_action",
                            Value = "Purchase",
                            Source = "preDefined",
                            DataType = "string",
                        }
                    },
                }
            };
            var clarityId = TestUETV2Operations.SetClarityEventInfo(cInfoForCustomEventToolPilot, goalId, clarityEventRequestBody);
            Trace.WriteLine("Goal ClarityEventDefinitionId is " + (string)clarityId.value);

            var currentEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);

            Trace.WriteLine("Goal ClarityEventDefinitionId is " + (string)currentEventGoal.ClarityEventDefinitionId);
            Trace.WriteLine("Goal ClarityEventDefinitionObject is " + (string)currentEventGoal.ClarityEventDefinitionObject);
            Trace.WriteLine("Goal Label is " + (string)currentEventGoal.Label);
            Assert.AreEqual((string)clarityId.value, (string)currentEventGoal.ClarityEventDefinitionId);

            var clarityEventInfo = TestUETV2Operations.GetClarityEventInfo(cInfoForCustomEventToolPilot, goalId);
            Trace.WriteLine("ClarityEventInfo id is " + (string)clarityEventInfo.Id);
            Assert.AreEqual((string)clarityId.value, (string)clarityEventInfo.Id);

            dynamic tag2 = TestUETV2Operations.CreateTagClarityTagEnabled(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            var tagObj2 = TestUETV2Operations.PostTag(cInfoForCustomEventToolPilot, tag2);
            var tagWithClarityId2 = (long)tagObj2.Id.Value;

            goal.ClarityEventDefinitionId = currentEventGoal.ClarityEventDefinitionId;
            goal.ClarityEventDefinitionObject = currentEventGoal.ClarityEventDefinitionObject;

            dynamic newTag = new System.Dynamic.ExpandoObject();
            newTag.Id = tagWithClarityId2;
            goal.Tag = newTag;

            var updateResponse = TestUETV2Operations.UpdateGoal(cInfoForCustomEventToolPilot, goal);
            Assert.AreEqual(updateResponse["value"].Value, 0);

            var finalGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            Assert.IsNull((string) finalGoal.ClarityEventDefinitionId);
            Assert.IsNull((string)finalGoal.ClarityEventDefinitionObject);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [TestCategory("SIOnly")]
        public void ClarityGoalSetup_ShouldNotSucceedIfTagIdNotMatch_SIOnly()
        {
            dynamic tag = TestUETV2Operations.CreateTagClarityTagEnabled(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfoForCustomEventToolPilot, tag);
            var tagWithClarityId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateEventGoal(tagWithClarityId);

            var goalObj = TestUETV2Operations.PostGoal(cInfoForCustomEventToolPilot, goal);
            Assert.IsNotNull(goalObj, "goal object");
            Assert.IsNotNull(goalObj.value, "goal object id");
            var goalId = (long)goalObj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var originalEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, originalEventGoal);


            dynamic unassociatedTag = TestUETV2Operations.CreateTagClarityTagEnabled(
     "TestTag-" + StringUtil.GenerateUniqueId(),
     "some description");
            var unassociatedTagObj = TestUETV2Operations.PostTag(cInfoForCustomEventToolPilot, unassociatedTag);
            var unassociatedTagId = (long)unassociatedTagObj.Id.Value;

            var clarityEventRequestBody = new SetClarityEventRequestBody
            {
                ClarityEventInfo = new ClarityEventInfo
                {
                    UetTagId = unassociatedTagId.ToString(),
                    SetupUrl = "https://www.xyztest123.com",
                    Trigger = new ClarityTrigger
                    {
                        EventType = "click",
                        EventTarget = new Dictionary<string, object>
                        {
                            { "h","21zsveaf" }, { "hb","21zsveaf" }, { "t","Add to cart" }
                        }
                    },
                    Status = "Active",
                    Data = new List<EventParameter>()
                    {
                        new EventParameter
                        {
                            Name = "event_action",
                            Value = "Purchase",
                            Source = "preDefined",
                            DataType = "string",
                        }
                    },
                }
            };
            var response = TestUETV2Operations.SetClarityEventInfo(cInfoForCustomEventToolPilot, goalId, clarityEventRequestBody, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalIdIsInvalid", (string) response.value[0].Code);
            var currentEventGoal = TestUETV2Operations.GetGoalByGoalId(cInfoForCustomEventToolPilot, goalId);
            Assert.IsNull((string)currentEventGoal.ClarityEventDefinitionId);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_SmartGoal_OData()
        {
            dynamic createdGoal = TestUETV2Operations.CreateSmartGoal(tagId, cInfo.AccountIds[0]);
            var result = TestUETV2Operations.PostODataSmartGoal(cInfo, createdGoal);
            string jsonStr = JsonConvert.SerializeObject(result);
            JObject json = (JObject)JsonConvert.DeserializeObject(jsonStr);
            if (json != null && json.ContainsKey("@odata.context") && !json.GetValue("@odata.context").ToString().Contains("AdsApiError"))
            {
                Assert.IsNotNull(result, "goal object");
                Assert.IsNotNull(result.value, "goal object id");
                var goalId = (long)result.value;
                createdGoal.Id = goalId;
                Trace.WriteLine("smart Goal Id is " + goalId);

                var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
                TestUETV2Operations.AssertGoalEqual(createdGoal, actualGoal);

                var goals = TestUETV2Operations.GetGoals(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");
                for (int i = 0; i < goals.value.Count; i++)
                {
                    if (goals.value[i].Type == GoalEntityType.SmartGoal)
                    {
                        TestUETV2Operations.AssertGoalEqual(createdGoal, goals.value[i]);
                    }
                }
                createdGoal.Status = "Paused";
                TestUETV2Operations.UpdateGoal(cInfo, createdGoal);

                actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
                TestUETV2Operations.AssertGoalEqual(createdGoal, actualGoal);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_SmartGoal()
        {
            var addGoalsRequest = CreateRequest<AddGoalsRequest>(cInfo);
            var goal = EventTrackingHelper.GenerateSmartGoal((long)addGoalsRequest.CustomerAccountId, (long)tagId);
            addGoalsRequest.Goals = new[] { goal };
            var response = TestSetting.EventTrackingProxy.AddGoals(addGoalsRequest);

            if (response.OperationStatus == Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.OperationStatus.Success)
            {
                var goalId = response.GoalIds.Select(v => v.Value).ToArray()[0];
                goal.Id = goalId;
                Trace.WriteLine("smart Goal Id is " + goalId);

                dynamic createdGoal = TestUETV2Operations.ConvertSmartGoalToDynamic(goal);

                var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
                TestUETV2Operations.AssertGoalEqual(createdGoal, actualGoal);

                var goals = TestUETV2Operations.GetGoals(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");
                for (int i = 0; i < goals.value.Count; i++)
                {
                    if (goals.value[i].Type == GoalEntityType.SmartGoal)
                    {
                        TestUETV2Operations.AssertGoalEqual(createdGoal, goals.value[i]);
                    }
                }
                createdGoal.Status = "Paused";
                TestUETV2Operations.UpdateGoal(cInfo, createdGoal);

                actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
                TestUETV2Operations.AssertGoalEqual(createdGoal, actualGoal);
            }

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_DuplicateGoalInUpdateParameter()
        {
            var addGoalsRequest = CreateRequest<AddGoalsRequest>(cInfo);
            var goal = EventTrackingHelper.GenerateDestinationGoal(tagId, true);
            addGoalsRequest.Goals = new[] { goal };
            var addGoalResponse = TestSetting.EventTrackingProxy.AddGoals(addGoalsRequest);
            var targetGoalId = addGoalResponse.GoalIds[0];

            var updateGoalRequest = CreateRequest<UpdateGoalsRequest>(cInfo);
            goal.IsAccountLevel = false;
            goal.Id = targetGoalId;
            string newGoalName = "NewNameForTest";
            goal.Name = newGoalName;
            //updateGoalRequest.Goals = new[] { goal};
            updateGoalRequest.Goals = new[] { goal, goal };
            var updateGoalResponse = TestSetting.EventTrackingProxy.UpdateGoals(updateGoalRequest);
            Assert.AreEqual(updateGoalResponse.OperationStatus, Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.OperationStatus.Failure);

            var currentGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, targetGoalId);
            Assert.AreEqual(newGoalName, currentGoal.Name.ToString());
        }

        //[TestMethod, Priority(0)]
        //[Owner(TestOwners.UETV2)]
        //[TestCategory(Constants.UETV2)]
        //public void Goal_E2E_ForSmartGoalCanNotBeCreatedByCustomer()
        //{
        //    dynamic goal = TestUETV2Operations.CreateSmartGoal(tagId, cInfo.AccountIds[0]);
        //    var obj = TestUETV2Operations.PostSmartGoal(cInfo, goal);

        //    Assert.AreEqual((string)obj.value[0].Code, "SmartGoalCouldNotBeCreatedByCustomer", "Goal_E2E_BVTForSmartGoalForPostValidation");
        //    Trace.WriteLine("error code is:" + (string)obj.value[0].Code);
        //}

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_ForSmartGoalSomeParametersCanNotUpdate()
        {
            var addGoalsRequest = CreateRequest<AddGoalsRequest>(cInfo);
            var goal = EventTrackingHelper.GenerateSmartGoal((long)addGoalsRequest.CustomerAccountId, (long)tagId);
            addGoalsRequest.Goals = new[] { goal };
            var response = TestSetting.EventTrackingProxy.AddGoals(addGoalsRequest);

            if (response.OperationStatus == Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.OperationStatus.Success)
            {
                var goalId = response.GoalIds.Select(v => v.Value).ToArray()[0];
                goal.Id = goalId;
                Trace.WriteLine("smart Goal Id is " + goalId);

                dynamic createdGoal = TestUETV2Operations.ConvertSmartGoalToDynamic(goal);

                createdGoal.Name = "Smart Goal New";
                var obj = TestUETV2Operations.UpdateSmartGoal(cInfo, createdGoal);

                Assert.AreEqual((string)obj.value[0].Code, "SmartGoalCouldNotBeEditInSomeParameters", "Goal_E2E_BVTForSmartGoalForUpdateValidation");
                Trace.WriteLine("error code is:" + (string)obj.value[0].Code);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_BVTForGoalCategoryForGoalTypeValidation()
        {

            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(GoalCategoryPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var localTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used for all goal tests is " + localTagId);


            dynamic goal = TestUETV2Operations.CreateEventGoalForGoalCategoryForGoalTypeValidation(localTagId, isMainConversionGoalValue: false);

            var obj = TestUETV2Operations.PostGoalForGoalCategoryForGoalTypeValidation(GoalCategoryPilotCustomer, goal);
            Assert.AreEqual((string)obj.value[0].Code, "InvalidCategoryForGoalType", "Goal_E2E_BVTForGoalCategoryForGoalTypeValidation");

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_ExecudeFromBidding_BVT_Pilot_CIOnly()
        {
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(MainConversionGoalPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var localTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used for all goal tests is " + localTagId);

            dynamic goal = TestUETV2Operations.CreateEventGoal(localTagId, isMainConversionGoalValue: false);
            var obj = TestUETV2Operations.PostGoal(MainConversionGoalPilotCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(MainConversionGoalPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(MainConversionGoalPilotCustomer);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            goal.IsMainConversionGoal = true;
            TestUETV2Operations.UpdateGoal(MainConversionGoalPilotCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(MainConversionGoalPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }


        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Category_BVT_Pilot()
        {
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(GoalCategoryPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var localTagId = (long)tagObj.Id.Value;
            Trace.WriteLine("Tag Id used for all goal tests is " + localTagId);


            dynamic goal = TestUETV2Operations.CreateEventGoalForGoalCategory(localTagId, isMainConversionGoalValue: false);
            var obj = TestUETV2Operations.PostGoal(GoalCategoryPilotCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(GoalCategoryPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            TestUETV2Operations.AssertGoalCategoryEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(GoalCategoryPilotCustomer);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "new goal name -" + StringUtil.GenerateUniqueId();
            goal.IsMainConversionGoal = true;
            goal.GoalCategory = "Other";

            TestUETV2Operations.UpdateGoal(GoalCategoryPilotCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(GoalCategoryPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            TestUETV2Operations.AssertGoalCategoryEqual(goal, actualGoal);
        }


        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_GetGoals_Filter()
        {
            dynamic goal1 = TestUETV2Operations.CreateEventGoal(tagId);
            var obj1 = TestUETV2Operations.PostGoal(cInfo, goal1);
            Assert.IsNotNull(obj1, "goal object");
            Assert.IsNotNull(obj1.value, "goal object id");

            dynamic tag2 = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");
            var tagObj2 = TestUETV2Operations.PostTag(cInfo, tag2);
            Assert.IsNotNull(tagObj2, "tag object");
            Assert.IsNotNull(tagObj2.Id, "tag object Id");
            long tagId2 = (long)tagObj2.Id.Value;

            dynamic goal2 = TestUETV2Operations.CreateEventGoal(tagId2);
            var obj2 = TestUETV2Operations.PostGoal(cInfo, goal2);
            Assert.IsNotNull(obj2, "goal object");
            Assert.IsNotNull(obj2.value, "goal object id");

            var goals = TestUETV2Operations.GetGoalsFilterByTagId(cInfo, tagId2);
            Assert.IsTrue(goals.value.Count == 1, "goals count");
            Assert.IsTrue(Convert.ToInt64(goals["@odata.count"].ToString()) == 1, "goals count");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_E2E_Currency_BVT()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_EventGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(cInfo, "false");

            goal.ConversionCountType = "Unique";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AddEventGoalWithPartialData_NotPiloting_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            goal.Action = null;
            goal.ActionOperator = "NoExpression";
            goal.Label = null;
            goal.LabelOperator = "NoExpression";
            goal.Value = null;
            goal.ValueOperator = "NoValue";
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_AddEventGoalWithPartialData_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            goal.Action = null;
            goal.ActionOperator = "NoExpression";
            goal.Label = null;
            goal.LabelOperator = "NoExpression";
            goal.Value = null;
            goal.ValueOperator = "NoValue";
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_PageViewGoal_NullProperties_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);
            goal.PageViews = null;
            goal.Operator = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);

            Assert.AreEqual(actualGoal.PageViews.ToString(), "0");
            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_PageViewGoal_NullOperator_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);
            goal.Operator = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);

            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_PageViewGoal_NullPageView_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);
            goal.PageViews = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.PageViews.ToString(), "0");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_DestinartionGoal_EnhancedConversions_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions, Features.InStoreTransaction });
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(customer, tag);
            var tagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.IsEnhancedConversionsEnabled = true;
            var obj = TestUETV2Operations.PostGoal(customer, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customer, goalId);
            Assert.AreEqual(actualGoal.IsEnhancedConversionsEnabled.ToString(), "True");
        }        

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Update_DestinartionGoal_EnhancedConversions_Success()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(new[] { Features.EnhancedConversions, Features.InStoreTransaction });
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(customer, tag);
            var tagId = (long)tagObj.Id.Value;
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(customer, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;
            goal.IsEnhancedConversionsEnabled = true;

            TestUETV2Operations.UpdateGoal(customer, goal);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customer, goalId);
            Assert.AreEqual(actualGoal.IsEnhancedConversionsEnabled.ToString(), "True");
        }
        
        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_DestinartionGoal_NullableProperties_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.Operator = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Operator.ToString(), "EqualsTo");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_DurationGoal_NullProperties_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            goal.Operator = null;
            goal.Duration = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
            Assert.AreEqual(actualGoal.Duration.ToString(), "0");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_DurationGoal_NullType_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            goal.Type = null; // MT will parse its type using odatatype 
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Type = "DurationGoal";
            goal.Id = goalId;
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_ChangeType_BothNullCurrency_Failed()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Id = goalId;
            Assert.AreEqual(actualGoal.CurrencyCode.ToString(), "");

            var goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.DestinationGoal");
            goalWithUpdatedProperties.Type = null;
            goalWithUpdatedProperties.IsAccountLevel = false;

            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_ChangeType_NonNullToNull_Failed()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Id = goalId;
            Assert.AreNotEqual(actualGoal.CurrencyCode.ToString(), "");

            var goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.DurationGoal");
            goalWithUpdatedProperties.Type = null;
            goalWithUpdatedProperties.IsAccountLevel = false;

            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Type.ToString(), "DurationGoal");
            Assert.AreEqual(actualGoal.CurrencyCode.ToString(), "USD");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_DurationGoal_NullableProperties_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            goal.Operator = null;
            goal.Duration = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
            Assert.AreEqual(actualGoal.Duration.ToString(), "0");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_DurationGoal_NullOperator_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            goal.Operator = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_DurationGoal_NullDuration_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);
            goal.Duration = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Duration.ToString(), "0");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_EventGoal_NullParameter_Failed()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.Category = null;
            goal.CategoryOperator = null;
            // Null Category & Action & Label & Value
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_EventGoal_NullOperator_Success()
        {
            // Same validation for Label, Value and Action.
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.CategoryOperator = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.CategoryOperator.ToString(), "EqualsTo");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_Goal_NullStatus_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.Status = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Status.ToString(), "Active");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_Goal_NullType_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.Type = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Type.ToString(), "EventGoal");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_Goal_NullIsAccountLevel_Failed()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.IsAccountLevel = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_Goal_NullGoalValueType_Failed()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.GoalValueType = null;
            // If GoalValueType is null and Value isn't null. Will return a error
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Add_Goal_NullLookbackWindowInMinutes_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePartialEventGoal(tagId);
            goal.LookbackWindowInMinutes = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.LookbackWindowInMinutes.ToString(), "43200"); // default 30 days.
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DestinationGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(cInfo, "false");

            goal.Operator = "Contains";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DestinationGoal_CurrencyCode_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DestinationGoal_EventTool_Success()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new[] { Features.CustomEventTool });

            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var tagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(customerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.ClarityEventDefinitionId = "348a9152-0575-4901-ac5d-776557e45720";
            goal.ClarityEventDefinitionObject = "object1";
            TestUETV2Operations.UpdateGoal(customerInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);

            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyEventTool: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(customerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);

            Assert.IsNotNull(actualGoal);
            Assert.AreEqual(goal.ClarityEventDefinitionId.ToString(), actualGoal.ClarityEventDefinitionId.ToString(), "ClarityEventDefinitionId doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DestinationGoal_EventTool_MaxLenth_Success()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new[] { Features.CustomEventTool });

            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var tagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(customerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.ClarityEventDefinitionId = "348a9152-0575-4901-ac5d-776557e45720";
            goal.ClarityEventDefinitionObject = "{\"id\":\"348a9152-0575-4901-ac5d-776557e45720\",\"data\":[{\"name\":\"event_action\",\"preDefinedValue\":\"other\",\"clarityValue\":null,\"dataType\":\"string\"}],\"uetTagId\":\"12345678\",\"setupUrl\":https://www.testtesttesttesttesttesttesttesttesttesttesttesttesttest.com,\"trigger\":{\"eventType\":\"click\",\"eventTarget\":{\"h\":\"7rljemtne\",\"hb\":\"5mthu9r2a\",\"s\":\"HTML>BODY>DIV>DIV>MAIN>DIV>DIV>DIV>ARTICLE>DIV>DIV>DIV>DIV>A.btn.btn--primary--forest.iframe-lightbox-link.iframe-lightbox-link--is-binded~0\",\"sb\":\"HTML>BODY:nth-of-type(1)>DIV.dialog-off-canvas-main-canvas:nth-of-type(1)>DIV.layout-container:nth-of-type(1)>MAIN.main--wrapper:nth-of-type(1)>DIV.layout-content:nth-of-type(1)>DIV:nth-of-type(1)>DIV.block.block-system.block-system-main-block:nth-of-type(2)>ARTICLE:nth-of-type(1)>DIV.product--details-wrapper:nth-of-type(2)>DIV.product--sticky-nav:nth-of-type(3)>DIV.product--cta-links:nth-of-type(2)>DIV.product--buy:nth-of-type(1)>A.btn.btn--primary--forest.iframe-lightbox-link.iframe-lightbox-link--is-binded:nth-of-type(1)\",\"t\":\"Order Online\"}},\"status\":\"Active\"}";
            TestUETV2Operations.UpdateGoal(customerInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);

            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyEventTool: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(customerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);

            Assert.IsNotNull(actualGoal);
            Assert.AreEqual(goal.ClarityEventDefinitionId.ToString(), actualGoal.ClarityEventDefinitionId.ToString(), "ClarityEventDefinitionId doesn't match");
            Assert.AreEqual("", actualGoal.ClarityEventDefinitionObject?.ToString(), "ClarityEventDefinitionObject doesn't match");
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_EventGoal_EventTool_Success()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new[] { Features.CustomEventTool });

            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var tagId = (long)tagObj.Id.Value;
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(customerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.Label = "348a9152-0575-4901-ac5d-776557e45720";
            goal.LabelOperator = "EqualsTo";
            goal.ClarityEventDefinitionId = "348a9152-0575-4901-ac5d-776557e45720";
            goal.ClarityEventDefinitionObject = "object1";
            TestUETV2Operations.UpdateGoal(customerInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);

            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyEventTool: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(customerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);

            Assert.IsNotNull(actualGoal);
            Assert.AreEqual(goal.ClarityEventDefinitionId.ToString(), actualGoal.ClarityEventDefinitionId.ToString(), "ClarityEventDefinitionId doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_EventGoal_EventTool_MaxLength_Success()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new[] { Features.CustomEventTool });

            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var tagId = (long)tagObj.Id.Value;
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(customerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.Label = "348a9152-0575-4901-ac5d-776557e45720";
            goal.LabelOperator = "EqualsTo";
            goal.ClarityEventDefinitionId = "348a9152-0575-4901-ac5d-776557e45720";
            goal.ClarityEventDefinitionObject = "{\"id\":\"348a9152-0575-4901-ac5d-776557e45720\",\"data\":[{\"name\":\"event_action\",\"preDefinedValue\":\"other\",\"clarityValue\":null,\"dataType\":\"string\"}],\"uetTagId\":\"12345678\",\"setupUrl\":https://www.testtesttesttesttesttesttesttesttesttesttesttesttesttest.com,\"trigger\":{\"eventType\":\"click\",\"eventTarget\":{\"h\":\"7rljemtne\",\"hb\":\"5mthu9r2a\",\"s\":\"HTML>BODY>DIV>DIV>MAIN>DIV>DIV>DIV>ARTICLE>DIV>DIV>DIV>DIV>A.btn.btn--primary--forest.iframe-lightbox-link.iframe-lightbox-link--is-binded~0\",\"sb\":\"HTML>BODY:nth-of-type(1)>DIV.dialog-off-canvas-main-canvas:nth-of-type(1)>DIV.layout-container:nth-of-type(1)>MAIN.main--wrapper:nth-of-type(1)>DIV.layout-content:nth-of-type(1)>DIV:nth-of-type(1)>DIV.block.block-system.block-system-main-block:nth-of-type(2)>ARTICLE:nth-of-type(1)>DIV.product--details-wrapper:nth-of-type(2)>DIV.product--sticky-nav:nth-of-type(3)>DIV.product--cta-links:nth-of-type(2)>DIV.product--buy:nth-of-type(1)>A.btn.btn--primary--forest.iframe-lightbox-link.iframe-lightbox-link--is-binded:nth-of-type(1)\",\"t\":\"Order Online\"}},\"status\":\"Active\"}";
            TestUETV2Operations.UpdateGoal(customerInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId);

            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyEventTool: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(customerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);

            Assert.IsNotNull(actualGoal);
            Assert.AreEqual(goal.ClarityEventDefinitionId.ToString(), actualGoal.ClarityEventDefinitionId.ToString(), "ClarityEventDefinitionId doesn't match");
            Assert.AreEqual("", actualGoal.ClarityEventDefinitionObject?.ToString(), "ClarityEventDefinitionObject doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DestinationGoal_UrlWithInvisibleChar_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            // Unicode Character 'LEFT-TO-RIGHT MARK' (U+200E) at the end of below string.
            goal.UrlString = "hardhatdecals.com/thank-you/‎";
            Regex invisibleControlCharRegex = new Regex(@"\p{Cf}");
            Assert.AreEqual(true, invisibleControlCharRegex.Match(goal.UrlString).Success);
            Assert.AreEqual(29, goal.UrlString.Length);

            var postUrl = string.Format(TestUETV2Operations.UETV2BaseUrl + "/Goals", cInfo.CustomerId, cInfo.AccountIds[0]);
            string json = JsonConvert.SerializeObject(goal).Replace("odatatype", TestBulkEditOperation.TypeProperty);
            var response = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(postUrl, new StringContent(json, System.Text.Encoding.Unicode, "application/json")),
                e => Assert.AreEqual(true, e.IsSuccessStatusCode));
            Assert.IsNotNull(response, "response object");
            Assert.IsNotNull(response.value, "response object id");
            var goalId = (long)response.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            Assert.AreEqual(false, invisibleControlCharRegex.Match((string)actualGoal.UrlString).Success);
            Assert.AreEqual(28, ((string)actualGoal.UrlString).Length);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AppInstallGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateAppInstallGoal();

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "AppInstallGoal-" + StringUtil.GenerateUniqueId();
            // update the appId and platform is allowed if Feature MobileAppCampaignConversionGoal is disabled
            goal.AppId = "NewAppId";
            goal.ApplicationPlatform = "IOS";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AppInstallGoal_ChangeAppId_Failed()
        {
            dynamic goal = TestUETV2Operations.CreateAppInstallGoal();

            var obj = TestUETV2Operations.PostGoal(mobileAppCampaignCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(mobileAppCampaignCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(mobileAppCampaignCustomerInfo);
            goals = TestUETV2Operations.GetAppInstallGoalsFilterByAppStore(mobileAppCampaignCustomerInfo, goal.ApplicationStoreId, goal.ApplicationPlatform); 
            Assert.IsTrue(goals.value.Count >= 1, "goals count");
            // Test on the application store id and platform filter
            goals = TestUETV2Operations.GetAppInstallGoalsFilterByAppStore(mobileAppCampaignCustomerInfo, "iOS", goal.ApplicationPlatform);
            Assert.IsTrue(goals.value.Count == 0, "goals count");
            // update the appId and platform is not allowed if Feature MobileAppCampaignConversionGoal is enabled
            goal.ApplicationStoreId = "NewAppId";
            var response = TestUETV2Operations.UpdateGoal(mobileAppCampaignCustomerInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalApplicationStoreIdCannotBeChanged", response.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AppInstallGoal_ChangeAppPlatForm_Failed()
        {
            dynamic goal = TestUETV2Operations.CreateAppInstallGoal();

            var obj = TestUETV2Operations.PostGoal(mobileAppCampaignCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(mobileAppCampaignCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoalsFilterByType(mobileAppCampaignCustomerInfo, GoalEntityType.ApplicationInstallGoal);
            Assert.IsTrue(goals.value.Count == 1, "goals count");

            goal.ApplicationPlatform = "IOS";
            var response = TestUETV2Operations.UpdateGoal(mobileAppCampaignCustomerInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalApplicationPlatformCannotBeChanged", response.value[0].Code.ToString());
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DurationGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(cInfo, "false");

            goal.Duration = 158;
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_PageViewsPerVisitGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(cInfo, "false");

            goal.Operator = "EqualTo";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_MultiStageGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateMultiStageGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            dynamic stage1 = TestUETV2Operations.CreateStage(1);
            goal.Stages = new[] { stage1 };
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_OfflineGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateOfflineGoal();

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(cInfo, "false");

            goal.Name = "OfflineGoal-" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            GetMsClickIdTaggingAndValidate(cInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_OfflineGoal_Enabled_ThirdPartyConversion_Pilot_Success()
        {
            dynamic goal = TestUETV2Operations.CreateOfflineGoal();

            var obj = TestUETV2Operations.PostGoal(ThirdPartyConversionPilotCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(ThirdPartyConversionPilotCustomer, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(ThirdPartyConversionPilotCustomer);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(ThirdPartyConversionPilotCustomer, "false");

            goal.Name = "OfflineGoal-" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(ThirdPartyConversionPilotCustomer, goal);

            GetMsClickIdTaggingAndValidate(ThirdPartyConversionPilotCustomer, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Enabled_ViewThroughConversion_Pilot_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(viewThroughPilotCustomerTagId, 112);

            var obj = TestUETV2Operations.PostGoal(viewThroughConversionCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            GetMsClickIdTaggingAndValidate(viewThroughConversionCustomerInfo, "true");

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(viewThroughConversionCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(viewThroughConversionCustomerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            UpdateMsClickIdTaggingAndValidate(viewThroughConversionCustomerInfo, "false");

            goal.Name = "OfflineGoal-" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(viewThroughConversionCustomerInfo, goal);

            GetMsClickIdTaggingAndValidate(viewThroughConversionCustomerInfo, "true");

            actualGoal = TestUETV2Operations.GetGoalByGoalId(viewThroughConversionCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void CreateUpdateGetGoal_WithAttributionModel_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(conversionGoalAttributionModelCustomerTagId, attributionModelType: AttributionModelType.LastTouch);

            var obj = TestUETV2Operations.PostGoal(conversionGoalAttributionModelCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(conversionGoalAttributionModelCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyAttributionModel: true);

            goal.Name = "DestinationGoal-" + StringUtil.GenerateUniqueId();
            goal.AttributionModelType = AttributionModelType.Default.ToString();
            TestUETV2Operations.UpdateGoal(conversionGoalAttributionModelCustomerInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(conversionGoalAttributionModelCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyAttributionModel: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(conversionGoalAttributionModelCustomerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
            Assert.IsNotNull(actualGoal);
            Assert.AreEqual(goal.AttributionModelType.ToString(), actualGoal.AttributionModelType.ToString(), "AttributionModelType doesn't match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_ProductConversionGoal_Success()
        {
            CustomerInfo productConversionGoalPilotCustomer = ProductConversionGoalPilotCustomer;
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");

            var tagObj = TestUETV2Operations.PostTag(productConversionGoalPilotCustomer, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            var localTagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateProductConversionGoal(localTagId);

            var obj = TestUETV2Operations.PostGoal(productConversionGoalPilotCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(productConversionGoalPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goals = TestUETV2Operations.GetGoals(productConversionGoalPilotCustomer);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            goal.Name = "OfflineGoal-" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(productConversionGoalPilotCustomer, goal);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(productConversionGoalPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        //Ignore the tests because store visit goal hold and do not allow create/update
        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_Success()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;
            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreVisitGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store visit goal exists per account. But actually {0} under customerId: {1}. accountId: {2}", goals.value.Count, cInfo.CustomerId, cInfo.AccountIds[0]);
                return;
            }

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            Assert.IsTrue(goals.value.Count == 1, "goals count");


            goal.Name = "InStoreVisitGoal-" + StringUtil.GenerateUniqueId();
            goal.GoalValueType = "NoValue";
            goal.GoalValue = null;
            TestUETV2Operations.UpdateGoal(inStoreVisitCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_PilotNotEnabled_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("CustomerNotEligibleForInStoreVisitGoal", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_CustomerOneGoal_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, false, Features.InStoreVisit);

            dynamic goal1 = TestUETV2Operations.CreateInStoreVisitGoal();
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal1, HttpStatusCode.OK);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            dynamic goal2 = TestUETV2Operations.CreateInStoreVisitGoal();
            obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal2, HttpStatusCode.BadRequest);
            Assert.AreEqual("OnlyOneInStoreVisitGoalBeAllowedPerCustomer", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_AccountLevel_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.IsAccountLevel = true;
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InStoreVisitGoalShouldBeAcrossAllAccounts", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_ExcludeFromBidding_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.IsMainConversionGoal = true;
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InStoreVisitGoalShoudBeExcludeFromBidding", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_ChangeType_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreVisitGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store visit goal exists per customer. But actually {0} under customerId: {1}. accountId: {2}", goals.value.Count, cInfo.CustomerId, cInfo.AccountIds[0]);
                return;
            }

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.Id = goalId;
            var response = TestUETV2Operations.UpdateGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalTypeCannotBeChanged", response.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_ChangeToOtherGoalGoal_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.Type = "InStoreTransactionGoal";
            var obj = TestUETV2Operations.PostGoal(InStoreTransactionCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalEntityType", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_LookbackWindow_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.LookbackWindowInMinutes = 43201;
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalLookbackWindow", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_GoalRevenue_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.GoalValueType = "VariantValue";
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalValueType", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_GoalCategory_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.GoalCategory = "Other";
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidCategoryForGoalType", obj.value[0].Code.ToString());
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_ConversionCountType_NegativeCase()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            goal.ConversionCountType = "All";
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidConversionCountType", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_CreateFail()
        {
            CustomerInfo inStoreVisitCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.InStoreVisit);
            dynamic goal;
            goal = TestUETV2Operations.CreateInStoreVisitGoal();
            var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("CustomerNotEligibleForInStoreVisitGoal", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreVisitGoal_QueryExistingSuccessUpdateFail()
        {
            CustomerInfo inStoreVisitCustomer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true, Features.InStoreVisit);
            dynamic goal;
            long goalId;

            EventTrackingHelper.InsertStoreVisitGoal(inStoreVisitCustomer.CustomerId);

            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Should get one existing in-store visit goal for customer. But actually {0} under customerId: {1}. ", goals.value.Count, cInfo.CustomerId);
                return;
            }

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreVisitCustomer, goalId);
            var goalName = "InStoreVisitGoal";
            TestUETV2Operations.AssertStringValueEqual(goalName, actualGoal.Name, "Goal Name");

            goal = actualGoal;
            goal.Name = "InStoreVisitGoal-" + StringUtil.GenerateUniqueId();
            goal.GoalValueType = "NoValue";
            goal.GoalValue = null;
            var updateObj = TestUETV2Operations.UpdateGoal(inStoreVisitCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("CustomerNotEligibleForInStoreVisitGoal", updateObj.value[0].Code.ToString());
        }        

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_Success()
        {
            CustomerInfo inStoreTransactionCustomer = InStoreTransactionCustomer;
            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreTransactionCustomer, GoalEntityType.InStoreTransactionGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreTransactionGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", goals.value.Count, cInfo.CustomerId);
                return;
            }

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goals = TestUETV2Operations.GetGoalsFilterByType(inStoreTransactionCustomer, GoalEntityType.InStoreTransactionGoal);
            Assert.IsTrue(goals.value.Count == 1, "goals count");


            goal.Name = "InStoreTransactionGoal-" + StringUtil.GenerateUniqueId();
            goal.GoalValueType = "NoValue";
            goal.GoalValue = null;
            TestUETV2Operations.UpdateGoal(inStoreTransactionCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_AccountLevel_NegativeCase()
        {
            CustomerInfo inStoreTransactionCustomer = InStoreTransactionCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreTransactionGoal();
            goal.IsAccountLevel = true;
            var obj = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InStoreTransactionGoalShouldBeAcrossAllAccounts", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_CurrencyIsNull_NegativeCase()
        {
            CustomerInfo inStoreTransactionCustomer = InStoreTransactionCustomer;

            dynamic goal = TestUETV2Operations.CreateInStoreTransactionGoal();
            goal.CurrencyCode = null;
            var obj = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalValueCurrencyCodeShouldNotBeNull", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_AddMultiInStoreTransactionGoal_NegativeCase()
        {
            CustomerInfo inStoreTransactionCustomer = InStoreTransactionCustomer;

            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreTransactionCustomer, GoalEntityType.InStoreTransactionGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreTransactionGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", goals.value.Count, cInfo.CustomerId);
                return;
            }

            goals = TestUETV2Operations.GetGoalsFilterByType(inStoreTransactionCustomer, GoalEntityType.InStoreTransactionGoal);
            Assert.IsTrue(goals.value.Count == 1, "goals count");

            goal = TestUETV2Operations.CreateInStoreTransactionGoal();
            var response = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("OnlyOneInStoreTransactionGoalBeAllowedPerCustomer", response.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_ChangeType_NegativeCase()
        {
            CustomerInfo inStoreTransactionCustomer = InStoreTransactionCustomer;

            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreTransactionCustomer, GoalEntityType.InStoreTransactionGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreTransactionGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreTransactionCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store transaction goal exists per customer. But actually {0} under customerId: {1}.", goals.value.Count, cInfo.CustomerId);
                return;
            }

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(inStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.Id = goalId;
            var response = TestUETV2Operations.UpdateGoal(inStoreTransactionCustomer, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalTypeCannotBeChanged", response.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InStoreTransactionGoal_PilotNotEnabled_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateInStoreTransactionGoal();
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InStoreTransactionPilotNotEnabledForCustomer", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_DestinationGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.IsAccountLevel = false;
            goal.CurrencyCode = "USD";
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_EventGoal_Success()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            goal.IsAccountLevel = false;
            goal.CurrencyCode = "USD";
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_OfflineGoal_Success()
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 3, true);

            //Create an account level goal
            dynamic goal1 = TestUETV2Operations.CreateOfflineGoal();
            var obj = TestUETV2Operations.PostGoal(customerInfo, goal1);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId1 = (long)obj.value;
            goal1.Id = goalId1;
            Trace.WriteLine("Goal Id is " + goalId1);

            var actualGoal1 = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId1);
            TestUETV2Operations.AssertGoalEqual(goal1, actualGoal1, compareCurrencyCode: true);

            for (int i = 0; i < 3; i++)
            {
                var response = (JObject)ApiHelper.CallApi(customerInfo,
                    c => c.GetAsync(MsClickIdTaggingUrl(customerInfo.CustomerId, customerInfo.AccountIds[i])),
                    e => Assert.AreEqual(true, e.IsSuccessStatusCode));
                //we should suppose the auto tagging is enable by default
                Assert.AreEqual("true", response["MsClickIdTaggingEnabled"].Value<string>());
            }

            //Create a customer level goal
            dynamic goal2 = TestUETV2Operations.CreateOfflineGoal();
            goal2.IsAccountLevel = false;
            goal2.CurrencyCode = "USD";
            obj = TestUETV2Operations.PostGoal(customerInfo, goal2);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId2 = (long)obj.value;
            goal2.Id = goalId2;
            Trace.WriteLine("Goal Id is " + goalId2);

            var actualGoal2 = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId2);
            TestUETV2Operations.AssertGoalEqual(goal2, actualGoal2, compareCurrencyCode: true);

            for (int i = 0; i < 3; i++)
            {
                var response = (JObject)ApiHelper.CallApi(customerInfo,
                    c => c.GetAsync(MsClickIdTaggingUrl(customerInfo.CustomerId, customerInfo.AccountIds[i])),
                    e => Assert.AreEqual(true, e.IsSuccessStatusCode));
                Assert.AreEqual("true", response["MsClickIdTaggingEnabled"].Value<string>());
            }

            goal2.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(customerInfo, goal2);

            actualGoal2 = TestUETV2Operations.GetGoalByGoalId(customerInfo, goalId2);
            TestUETV2Operations.AssertGoalEqual(goal2, actualGoal2, compareCurrencyCode: true);

            for (int i = 0; i < 3; i++)
            {
                var response = (JObject)ApiHelper.CallApi(customerInfo,
                    c => c.GetAsync(MsClickIdTaggingUrl(customerInfo.CustomerId, customerInfo.AccountIds[i])),
                    e => Assert.AreEqual(true, e.IsSuccessStatusCode));
                Assert.AreEqual("true", response["MsClickIdTaggingEnabled"].Value<string>());
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_AddOfflineGoal_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateOfflineGoal();
            goal.IsAccountLevel = false;
            goal.CurrencyCode = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalValueCurrencyCodeShouldNotBeNull", obj.value[0].Code.ToString());

            goal.CurrencyCode = "XXX";
            obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_UpdateOfflineGoal_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateOfflineGoal();
            goal.IsAccountLevel = false;
            goal.CurrencyCode = "USD";
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            //CurrencyCode will be backfilled when it's null. 

            goal.CurrencyCode = null;
            obj = TestUETV2Operations.UpdateGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalValueCurrencyCodeShouldNotBeNull", obj.value[0].Code.ToString());

            goal.CurrencyCode = "XXX";
            obj = TestUETV2Operations.UpdateGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_Add_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.IsAccountLevel = false;
            goal.CurrencyCode = null;
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalValueCurrencyCodeShouldNotBeNull", obj.value[0].Code.ToString());

            goal = TestUETV2Operations.CreateEventGoal(tagId);
            goal.IsAccountLevel = false;
            goal.CurrencyCode = "XXX";
            obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CurrencyAcrossAllAccounts_Update_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            goal.IsAccountLevel = false;
            goal.CurrencyCode = "USD";
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            //CurrencyCode will be backfilled when it's null. 

            goal.CurrencyCode = null;
            obj = TestUETV2Operations.UpdateGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalValueCurrencyCodeShouldNotBeNull", obj.value[0].Code.ToString());

            goal.CurrencyCode = "XXX";
            obj = TestUETV2Operations.UpdateGoal(cInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DuplicateName_TwoAccounts_Success()
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, true);

            dynamic tag = TestUETV2Operations.CreateTag("TestTag-" + StringUtil.GenerateUniqueId(), "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo, tag);
            long tagId = (long)tagObj.Id.Value;

            dynamic goal1 = TestUETV2Operations.CreateDestinationGoal(tagId);
            TestUETV2Operations.PostGoal(customerInfo, goal1, accountId: customerInfo.AccountIds[0]);

            dynamic goal2 = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal2.Name = goal1.Name;
            TestUETV2Operations.PostGoal(customerInfo, goal2, accountId: customerInfo.AccountIds[1]);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DuplicateName_NegativeCase()
        {
            dynamic goal1 = TestUETV2Operations.CreateDestinationGoal(tagId);
            TestUETV2Operations.PostGoal(cInfo, goal1);

            dynamic goal2 = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal2.Name = goal1.Name;
            var response = TestUETV2Operations.PostGoal(cInfo, goal2, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalWithSameNameAlreadyExistsUnderTag", response.value[0].Code.ToString());

            dynamic goal3 = TestUETV2Operations.CreateDestinationGoal(tagId);
            response = TestUETV2Operations.PostGoal(cInfo, goal3);
            goal3.Id = (long)response.value;
            goal3.Name = goal1.Name;
            response = TestUETV2Operations.UpdateGoal(cInfo, goal3, HttpStatusCode.BadRequest);
            Assert.AreEqual("GoalWithSameNameAlreadyExistsUnderTag", response.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_UpdateStatus_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            dynamic durationGoal = TestUETV2Operations.CreateDurationGoal(tagId);
            durationGoal.Id = goalId;
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);

            durationGoal.Status = "Paused";
            durationGoal.CurrencyCode = actualGoal.CurrencyCode;
            TestUETV2Operations.UpdateGoal(cInfo, durationGoal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(durationGoal, actualGoal);

            durationGoal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, durationGoal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(durationGoal, actualGoal);
        }


        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_DestinationGoal_CustomerLevel_NullCurrencyCode()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.IsAccountLevel = false;
            //Null Revenue. Choose "Don't assign a value" in UI
            goal.GoalValue = null;
            goal.GoalValueType = "NoValue";
            goal.CurrencyCode = null;

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, urlString: "new.test.com");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            goal.UrlString = "new.test.com";
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            // This Customer level Url goal don't have currency code.
            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, goalValue: 20);
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Update_ChangeGoalLevel_Success()
        {
            var customerInfo_PilotGoalLevelChange =
                CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new int[] { });
            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo_PilotGoalLevelChange, tag);
            var tagId = (long)tagObj.Id.Value;

            dynamic eventGoal = TestUETV2Operations.CreateEventGoalForGoalCategory(tagId);
            eventGoal.IsAccountLevel = true; //create an account level goal
            var obj1 = TestUETV2Operations.PostGoal(customerInfo_PilotGoalLevelChange, eventGoal);

            eventGoal.Id = (long)obj1.value;
            eventGoal.IsAccountLevel = false; //change to customer level
            eventGoal.AccountId = null;
            TestUETV2Operations.UpdateGoal(customerInfo_PilotGoalLevelChange, eventGoal);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(customerInfo_PilotGoalLevelChange, eventGoal.Id);
            Assert.AreEqual(false, (bool)actualGoal.IsAccountLevel);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Update_ChangeGoalLevel_SmartGoal_Fail()
        {
            var customerInfo_PilotGoalLevelChange = 
                CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new int[] { });  
            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo_PilotGoalLevelChange, tag);
            var tagId = (long)tagObj.Id.Value;

            dynamic smartGoal = TestUETV2Operations.CreateSmartGoal(tagId, customerInfo_PilotGoalLevelChange.AccountIds[0]);
            smartGoal.IsAccountLevel = true; //create an account level goal
            var smartGoalObj = TestUETV2Operations.PostGoal(customerInfo_PilotGoalLevelChange, smartGoal);

            smartGoal.Id = (long)smartGoalObj.value;
            smartGoal.IsAccountLevel = false; //change to customer level
            smartGoal.AccountId = null;
            TestUETV2Operations.UpdateGoal(customerInfo_PilotGoalLevelChange, smartGoal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Update_ChangeGoalLevel_Downgrade_Fail()
        {
            var customerInfo_PilotGoalLevelChange = 
                CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, true, new int[]{});  
            dynamic tag = TestUETV2Operations.CreateTag(
               "TestTag-" + StringUtil.GenerateUniqueId(),
               "some description");
            var tagObj = TestUETV2Operations.PostTag(customerInfo_PilotGoalLevelChange, tag);
            var tagId = (long)tagObj.Id.Value;

            dynamic goal = TestUETV2Operations.CreateEventGoalForGoalCategory(tagId);
            goal.IsAccountLevel = false; //create a customer level goal
            var obj1 = TestUETV2Operations.PostGoal(customerInfo_PilotGoalLevelChange, goal);

            goal.Id = (long)obj1.value;
            goal.IsAccountLevel = true; // try to change to account level
            goal.AccountId = customerInfo_PilotGoalLevelChange.AccountIds[0];
            TestUETV2Operations.UpdateGoal(customerInfo_PilotGoalLevelChange, goal, expectedHttpStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_DestinationGoal_CustomerLevel_SetNullGoalValue()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            goal.IsAccountLevel = false;

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, GoalValueType: "NoValue");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.GoalValue = null;
            goal.GoalValueType = "NoValue";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_DestinationGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, goalValue: 34);
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.GoalValue = 34;
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Status = "Paused";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Status = "Active";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, currencyCode: "HKD");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.CurrencyCode = "HKD";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_EventGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal);
            goalWithUpdatedProperties.Category = "Purchase";
            goalWithUpdatedProperties.LabelOperator = "Contains";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Category = "Purchase";
            goal.LabelOperator = "Contains";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused", currencyCode: "CAD");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Status = "Paused";
            goal.CurrencyCode = "CAD";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            goal.Status = "Active";
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_InstallGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateAppInstallGoal();
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, platform: "iOS");
            goal.ApplicationPlatform = "iOS";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(goal.ApplicationPlatform.ToString(), actualGoal.ApplicationPlatform.ToString());

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused");
            goal.Status = "Paused";
            goal.CurrencyCode = "CAD";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_StoreVisitGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateInStoreVisitGoal();
            var obj = TestUETV2Operations.PostGoal(InStoreVisitCustomer, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, goalValue: 26);
            goal.GoalValue = 26;
            TestUETV2Operations.UpdateGoal(InStoreVisitCustomer, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused");
            goal.Status = "Paused";
            TestUETV2Operations.UpdateGoal(InStoreVisitCustomer, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(InStoreVisitCustomer, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreVisitCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_InStoreTransactionsGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateInStoreTransactionGoal();
            var obj = TestUETV2Operations.PostGoal(InStoreTransactionCustomer, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, currencyCode: "HKD");
            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(InStoreTransactionCustomer, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused", currencyCode: "CAD");
            goal.Status = "Paused";
            goal.CurrencyCode = "CAD";
            TestUETV2Operations.UpdateGoal(InStoreTransactionCustomer, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(InStoreTransactionCustomer, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(InStoreTransactionCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_OfflineGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateOfflineGoal();
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, currencyCode: "HKD");
            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused", currencyCode: "CAD");
            goal.Status = "Paused";
            goal.CurrencyCode = "CAD";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_ProductGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreateProductConversionGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, currencyCode: "HKD");
            goal.CurrencyCode = "HKD";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused", currencyCode: "CAD");
            goal.Status = "Paused";
            goal.CurrencyCode = "CAD";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_PageViewGoal_PassPartialProperties()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;

            dynamic goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal);
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Paused");
            goal.Status = "Paused";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, status: "Active");
            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_PassPartialProperties_ChangeGoalType()
        {
            // Destination -> Duration
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            var goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.DurationGoal", type: "DurationGoal");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);

            // DurationGoal don't have URL string.
            Assert.IsNull(actualGoal.UrlString);

            Assert.AreEqual(actualGoal.Type.ToString(), "DurationGoal");
            Assert.AreEqual(actualGoal.Operator.ToString(), "GreaterThan");
            Assert.AreEqual(actualGoal.Duration.ToString(), "0");

            goal.odatatype = "#Model.DurationGoal";
            goal.Type = "DurationGoal";
            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, duration: 100);
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Duration.ToString(), "100");

            //EventGoal must have one of label/action/catology/value at least.
            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.EventGoal", type: "EventGoal");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties, HttpStatusCode.BadRequest);

            goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.EventGoal", type: "EventGoal", action: "action");
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Action.ToString(), "action");
            // ActionOperator's defualt value is EqualsTo
            Assert.AreEqual(actualGoal.ActionOperator.ToString(), "EqualsTo");
        }

        [TestMethod, Priority(2)]
        [TestCategory(Constants.UETV2)]
        [Ignore]
        public void Goal_Update_PassPartialProperties_ChangeGoalTypeWithNullType()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            var goalId = (long)obj.value;
            goal.Id = goalId;
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            // Update value without type
            var goalWithUpdatedProperties = TestUETV2Operations.UpdateGoalWithNullProperties(originalGoal: goal, odatatype: "#Model.EventGoal", value: 7);
            goalWithUpdatedProperties.Type = null;
            TestUETV2Operations.UpdateGoal(cInfo, goalWithUpdatedProperties);
            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            Assert.AreEqual(actualGoal.Value.ToString(), "7");
            Assert.AreEqual(actualGoal.ValueOperator.ToString(), "EqualTo"); // default operator
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_UpdateStatus_Enabled_ThirdPartyConversion_Pilot_Success()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(thirdPartyPilotCustomerTagId);

            var obj = TestUETV2Operations.PostGoal(ThirdPartyConversionPilotCustomer, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            dynamic durationGoal = TestUETV2Operations.CreateDurationGoal(thirdPartyPilotCustomerTagId);
            durationGoal.Id = goalId;
            TestUETV2Operations.UpdateGoal(ThirdPartyConversionPilotCustomer, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);

            durationGoal.Status = "Paused";
            durationGoal.CurrencyCode = actualGoal.CurrencyCode;
            TestUETV2Operations.UpdateGoal(ThirdPartyConversionPilotCustomer, durationGoal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(durationGoal, actualGoal);

            durationGoal.Status = "Active";
            TestUETV2Operations.UpdateGoal(ThirdPartyConversionPilotCustomer, durationGoal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(ThirdPartyConversionPilotCustomer, goalId);
            TestUETV2Operations.AssertGoalEqual(durationGoal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_UpdateType_Success()
        {
            dynamic goal = TestUETV2Operations.CreateAppInstallGoal();

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.Status = "Paused";
            dynamic tag = new System.Dynamic.ExpandoObject();
            tag.Id = 0;
            goal.Tag = tag;
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);

            goal.Status = "Active";
            TestUETV2Operations.UpdateGoal(cInfo, goal);

            actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_MultiStageGoal_WithNoStage_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateMultiStageGoal(tagId);

            goal.Stages = new dynamic[] { };
            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);

            Assert.AreEqual("GoalStagesNotPassed", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Add_Invalid_CurrencyCode_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);

            goal.CurrencyCode = "XXX";

            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);

            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_Update_Invalid_CurrencyCode_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            var actualGoal = TestUETV2Operations.GetGoalByGoalId(cInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, compareCurrencyCode: true);

            goal.CurrencyCode = "";
            obj = TestUETV2Operations.UpdateGoal(cInfo, goal, HttpStatusCode.BadRequest);

            Assert.AreEqual("InvalidGoalValueCurrencyCode", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_DurationGoal_CurrencyCode_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateDurationGoal(tagId);

            goal.CurrencyCode = "USD";

            var obj = TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);

            Assert.AreEqual("GoalValueCurrencyCodeShouldBeNull", obj.value[0].Code.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_AttributeMismatch_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            goal.Category = null;
            TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_InvalidEnum_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(tagId);
            goal.Operator = "Random";

            TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_TagNotExist_NegativeCase()
        {
            dynamic goal = TestUETV2Operations.CreatePageViewsPerVisitGoal(0);

            TestUETV2Operations.PostGoal(cInfo, goal, HttpStatusCode.BadRequest);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_CustomerId_Verify()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);
            var newCInfo = cInfo.Clone();
            newCInfo.CustomerId = 1234567;
            var response = TestUETV2Operations.PostGoal(newCInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual(1, response.value.Count);
            Assert.AreEqual("InvalidCustomerId", response.value[0].Code.Value);

            goal.Id = 123456;
            response = TestUETV2Operations.UpdateGoal(newCInfo, goal, HttpStatusCode.BadRequest);
            Assert.AreEqual(1, response.value.Count);
            Assert.AreEqual("InvalidCustomerId", response.value[0].Code.Value);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_BVT()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                Random ra = new Random();
                var biData1 = GenerateBiData(ra.Next(1, 100), 1);
                biData1.GoalId = goalId;
                biData1.GoalName = goal.Name;

                var biData2 = GenerateBiData(ra.Next(1, 100), 2);
                biData2.GoalId = goalId;
                biData2.GoalName = goal.Name;

                BiDatabaseHelper.SetGoalMockBiData(
                    cInfo,
                    new List<BiData>() { biData1, biData2 });

                var goals = TestUETV2Operations.GetGoalsWithBiData(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                var actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
                Assert.IsNotNull(actualGoal);
                Assert.AreEqual(biData1.ViewThroughConversions + biData2.ViewThroughConversions, (int?)actualGoal.GoalPerformanceMetrics.ViewThroughConversions, "mismatch in ViewThroughConversions");
                Assert.AreEqual(biData1.Conversions + biData2.Conversions, (int?)actualGoal.GoalPerformanceMetrics.Conversions, "mismatch in Conversions");
                Assert.AreEqual(biData1.AdvertiserReportedRevenue + biData2.AdvertiserReportedRevenue, (double?)actualGoal.GoalPerformanceMetrics.Revenue, "mismatch in AdvertiserReportedRevenue");
                Assert.AreEqual(biData1.AllConversions + biData2.AllConversions, (long?)actualGoal.GoalPerformanceMetrics.AllConversions, "mismatch in AllConversions");
                double repeatRate = (double)(biData1.AllConversions + biData2.AllConversions) / (double)(biData1.UniqueConversions + biData2.UniqueConversions);
                Assert.IsTrue(Math.Abs(actualGoal.GoalPerformanceMetrics.RepeatRate.Value - repeatRate) < 0.01, "mismatch in GoalRepeatRate");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_ConversionDelay_BVT()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(conversionDelayCustomerTagId);

                var obj = TestUETV2Operations.PostGoal(conversionDelayCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                var biDatas = PopulateConversionDelayData(goalId, (string)goal.Name);

                var biDataDict = biDatas
                    .GroupBy(item => (long)item.GoalId)
                    .ToDictionary(group => group.Key, group => group.ToList());

                var goals = TestUETV2Operations.GetGoalsWithBiData(conversionDelayCustomer, start: -2, end: 10);
                
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                VerifyConversionDelayGridData(goals, biDataDict);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_ConversionDelay_SortingAndFiltering()
        {
            var biDatas = new List<BiData>();
            if (mockReporting)
            {
                var orderBy = "GoalPerformanceMetrics/ConversionDelayZeroDay desc";

                for (int i = 0; i < 3; i++)
                {
                    dynamic goal = TestUETV2Operations.CreateEventGoal(conversionDelayCustomerTagId);
                    var obj = TestUETV2Operations.PostGoal(conversionDelayCustomer, goal);
                    var goalId = (long)obj.value;
                    goal.Id = goalId;

                    biDatas.AddRange(PopulateConversionDelayData(goalId, (string)goal.Name));
                }
                
                var biDataDict = biDatas
                    .GroupBy(item => (long)item.GoalId)
                    .ToDictionary(group => group.Key, group => group.ToList());

                var goals = TestUETV2Operations.GetGoalsWithBiData(conversionDelayCustomer, start: -2, end: 10, orderBy: orderBy);

                for (int i = 1; i < goals.value.Count; i++)
                {
                    if (goals.value[i].GoalPerformanceMetrics.ConversionDelayZeroDay > goals.value[i - 1].GoalPerformanceMetrics.ConversionDelayZeroDay)
                    {
                        Assert.Fail("Sorting failed.");
                    }
                }

                var filter = "GoalPerformanceMetrics/ConversionDelayZeroDay gt 100 and GoalPerformanceMetrics/ConversionDelayZeroDay lt 150";
                goals = TestUETV2Operations.GetGoalsWithBiData(conversionDelayCustomer, start: -2, end: 10, filter: filter);

                for (int i = 0; i < goals.value.Count; i++)
                {
                    var conversionDelayZeroDay = goals.value[i].GoalPerformanceMetrics.ConversionDelayZeroDay;
                    if (conversionDelayZeroDay <= 100.0 || conversionDelayZeroDay >= 150.0)
                    {
                        Assert.Fail("Filtering failed.");
                    }
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_Credit()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                Random ra = new Random();
                var biData1 = GenerateBiData(ra.Next(1, 100), 1);

                //credit:
                biData1.GoalId = goalId;
                biData1.GoalName = goal.Name;

                biData1.ConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.UniqueConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.AllConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.ViewThroughConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.TotalConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());

                var biData2 = GenerateBiData(ra.Next(1, 100), 2);
                biData2.GoalId = goalId;
                biData2.GoalName = goal.Name;

                biData2.ConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData2.UniqueConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData2.AllConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData2.ViewThroughConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData2.TotalConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());

                //For debug:
                biData1.Conversions = 49;
                biData1.ConversionsCredit = (float)35.64;
                biData2.Conversions = 20;
                biData2.ConversionsCredit = (float)36.23;

                biData1.AllConversionsCredit = (float)(123.64);
                biData1.ViewThroughConversionsCredit = (float)(225.73);
                biData1.TotalConversionsCredit = (float)(84.93);
                biData2.AllConversionsCredit = (float)(123.64);
                biData2.ViewThroughConversionsCredit = (float)(225.73);
                biData2.TotalConversionsCredit = (float)(84.93);

                BiDatabaseHelper.SetGoalMockBiData(
                    cInfo,
                    new List<BiData>() { biData1, biData2 },
                    useCredit: true);

                var goals = TestUETV2Operations.GetGoalsWithBiData(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                var actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
                Assert.IsNotNull(actualGoal);
                Assert.AreEqual((int)RoundingFloatVal((float)(biData1.ConversionsCredit + biData2.ConversionsCredit)), (int)actualGoal.GoalPerformanceMetrics.Conversions, "mismatch in Conversions");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithSingleBIData_Credit()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                Random ra = new Random();
                var biData1 = GenerateBiData(ra.Next(1, 100), 1);

                //credit:
                biData1.GoalId = goalId;
                biData1.GoalName = goal.Name;

                biData1.ConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.UniqueConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.AllConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.ViewThroughConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());
                biData1.TotalConversionsCredit = (float)(ra.Next(100) + ra.NextDouble());

                //For debug:
                biData1.Conversions = 49;
                biData1.ConversionsCredit = (float)35.64;

                biData1.AllConversionsCredit = (float)(123.64);
                biData1.ViewThroughConversionsCredit = (float)(225.73);
                biData1.TotalConversionsCredit = (float)(84.93);

                BiDatabaseHelper.SetGoalMockBiData(
                    cInfo,
                    new List<BiData>() { biData1 },
                    useCredit: true);

                var goals = TestUETV2Operations.GetGoalsWithBiData(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                var actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
                Assert.IsNotNull(actualGoal);

                Assert.AreEqual(BiDatabaseHelper.RoundingFloatVal((float)(biData1.ConversionsCredit)), actualGoal.GoalPerformanceMetrics.Conversions.ToString(), "mismatch in Conversions");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_ViewThroughConversion_Pilot_Enabled()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(viewThroughPilotCustomerTagId);

                var obj = TestUETV2Operations.PostGoal(viewThroughConversionCustomerInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                Random ra = new Random();
                var biData1 = GenerateBiData(ra.Next(1, 100), 1);
                biData1.GoalId = goalId;
                biData1.GoalName = goal.Name;

                var biData2 = GenerateBiData(ra.Next(1, 100), 2);
                biData2.GoalId = goalId;
                biData2.GoalName = goal.Name;

                BiDatabaseHelper.SetGoalMockBiData(
                    viewThroughConversionCustomerInfo,
                    new List<BiData>() { biData1, biData2 });

                var goals = TestUETV2Operations.GetGoalsWithBiData(viewThroughConversionCustomerInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                var actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
                Assert.IsNotNull(actualGoal);
                Assert.AreEqual(biData1.Conversions + biData2.Conversions, (int?)actualGoal.GoalPerformanceMetrics.Conversions, "mismatch in Conversions");
                Assert.AreEqual(biData1.ViewThroughConversions + biData2.ViewThroughConversions, (double?)actualGoal.GoalPerformanceMetrics.ViewThroughConversions, "mismatch in ViewThroughConversions");
                Assert.AreEqual(biData1.AdvertiserReportedRevenue + biData2.AdvertiserReportedRevenue, (double?)actualGoal.GoalPerformanceMetrics.Revenue, "mismatch in AdvertiserReportedRevenue");
                Assert.AreEqual(biData1.AllConversions + biData2.AllConversions, (long?)actualGoal.GoalPerformanceMetrics.AllConversions, "mismatch in AllConversions");
                double repeatRate = (double)(biData1.AllConversions + biData2.AllConversions) / (double)(biData1.UniqueConversions + biData2.UniqueConversions);
                Assert.IsTrue(Math.Abs(actualGoal.GoalPerformanceMetrics.RepeatRate.Value - repeatRate) < 0.01, "mismatch in GoalRepeatRate");
            }
        }

        [Ignore]
        [TestMethod, Priority(0)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_InstoreVisit()
        {
            CustomerInfo inStoreVisitCustomer = InStoreVisitCustomer;
            dynamic goal;
            long goalId;
            var goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            if (goals.value.Count == 0)
            {
                goal = TestUETV2Operations.CreateInStoreVisitGoal();

                var obj = TestUETV2Operations.PostGoal(inStoreVisitCustomer, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);
            }
            else if (goals.value.Count == 1)
            {
                goal = goals.value[0];
                goalId = goal.Id;
            }
            else
            {
                Assert.Fail("Expect only one in-store visit goal exists per account. But actually {0} under customerId: {1}. accountId: {2}", goals.value.Count, cInfo.CustomerId, cInfo.AccountIds[0]);
                return;
            }

            goals = TestUETV2Operations.GetGoalsFilterByType(inStoreVisitCustomer, GoalEntityType.InStoreVisitGoal);
            if (goals.value.Count != 1)
            {
                Assert.Fail("Failed to get the instore visit goal after it is created.");
                return;
            }
            var actualGoal = goals.value[0]; ;
            Assert.IsNotNull(actualGoal);
            Assert.IsNull((int?)actualGoal.GoalPerformanceMetrics.Conversions);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.UETV2)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_RepeatRateNull()
        {
            if (mockReporting)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine("Goal Id is " + goalId);

                Random ra = new Random();
                var biData1 = GenerateBiData(ra.Next(1, 100), 1);
                biData1.GoalId = goalId;
                biData1.GoalName = goal.Name;
                biData1.AllConversions = null;
                var biData2 = GenerateBiData(ra.Next(1, 100), 2);
                biData2.GoalId = goalId;
                biData2.GoalName = goal.Name;
                biData2.AllConversions = null;
                BiDatabaseHelper.SetGoalMockBiData(
                    cInfo,
                    new List<BiData>() { biData1, biData2 });

                var goals = TestUETV2Operations.GetGoalsWithBiData(cInfo);
                Assert.IsTrue(goals.value.Count >= 1, "goals count");

                var actualGoal = ((IEnumerable<dynamic>)goals.value).Single(x => (long)x.Id == goalId);
                Assert.IsNotNull(actualGoal);
                Assert.IsNull(actualGoal.GoalPerformanceMetrics.RepeatRate.Value);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_ConversionGoalsByAccountId()
        {
            dynamic goal = TestUETV2Operations.CreateEventGoal(tagId);

            var obj = TestUETV2Operations.PostGoal(cInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);
            var goals = TestUETV2Operations.GetConversionGoalsByAccountId(cInfo);
            Assert.IsTrue(goals.value.Count >= 1, $"goals count: {goals.value.Count}");

            HashSet<long> goalIds = new HashSet<long>();

            for (int i = 0; i < goals.value.Count; ++i)
            {
                goalIds.Add(goals.value[i]["Id"].Value);
            }

            Assert.IsTrue(goalIds.Contains(goalId));
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_PartialConversion()
        {
            if (!mockReporting)
            {
                return;
            }

            // prepare data
            this.cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false);

            dynamic tag = TestUETV2Operations.CreateTag($"TestTag-{StringUtil.GenerateUniqueId()}", "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfo, tag);

            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            tagId = (long)tagObj.Id.Value;

            var goals = this.CreateGoals(3, false);
            var biDataByGoalId = PopulateBiData(DateTime.Today, goals, true);

            // filter
            var filter = "GoalPerformanceMetrics/ConversionsCredit gt 100 and GoalPerformanceMetrics/ConversionsCredit lt 200";
            var result = TestUETV2Operations.GetGoalsWithBiData(this.cInfo, filter: filter);
            var expectedGoalIds = new List<long> { (long)goals[1].Id };
            VerifyPartialConversion(result, expectedGoalIds, biDataByGoalId, false);

            // sort
            var orderBy = "GoalPerformanceMetrics/ConversionsCredit desc";
            result = TestUETV2Operations.GetGoalsWithBiData(this.cInfo, orderBy: orderBy);
            expectedGoalIds = biDataByGoalId.OrderByDescending(kvp => kvp.Value.ConversionsCredit).Select(kvp => kvp.Value.GoalId.Value).ToList();
            VerifyPartialConversion(result, expectedGoalIds, biDataByGoalId, false);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void Goal_WithBIData_PartialConversion_WithViewThrough()
        {
            if (!mockReporting)
            {
                return;
            }

            // prepare data
            this.cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false, Features.EnableNativeAdsFlagId);

            dynamic tag = TestUETV2Operations.CreateTag($"TestTag-{StringUtil.GenerateUniqueId()}", "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfo, tag);

            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");

            tagId = (long)tagObj.Id.Value;

            var goals = this.CreateGoals(2, true);

            // run
            var biDataByGoalId = PopulateBiData(DateTime.Today, goals, true);
            var result = TestUETV2Operations.GetGoalsWithBiData(this.cInfo);
            var expectedGoalIds = goals.Select(x => (long)x.Id).ToList();
            VerifyPartialConversion(result, expectedGoalIds, biDataByGoalId, true);
        }

        [TestMethod]
        [Priority(2)]
        public void CampaignGoal_RecordConv()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false, Features.EnableNativeAdsFlagId);
            TestCampaignCollection.Clean(customer);

            //Mock for tag (active)
            dynamic tag = TestUETV2Operations.CreateTag(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            TestUETV2Operations.PostTag(customer, tag);
            DatabaseHelper.SetUETTagStatus(customer, tag.Id, 1); //1:tag active

            //Mock for goal1: active goal with active tag, recorded conversion
            dynamic goal1 = TestUETV2Operations.CreateDestinationGoal(tag.Id);
            var obj1 = TestUETV2Operations.PostGoal(customer, goal1);
            var goalId1 = (long)obj1.value;
            DatabaseHelper.SetGoalTrackingStatus(customer, goalId1, 2, 1);

            TestCampaignCollection sourceCampaign = new TestCampaignCollection(customer.AccountIds.Count());
            var addSourceCampaignResponse = sourceCampaign.Add(customer);
            ResponseValidator.ValidateBasicSuccess(addSourceCampaignResponse);
            var campaignId = addSourceCampaignResponse.CampaignIds.First();
            DBHelper.InsertCampaignConversionGoalData(customer.CustomerId, customer.AccountIds[0], campaignId.Value, goalId1);

            var result = TestUETV2Operations.GetCampaignByIdWithCampaignGoal(customer, campaignId.Value);
            Assert.IsNotNull(result);
            Assert.AreEqual("RecordingConversions", result.CampaignConversionGoal.Goals[0].TrackingStatus.ToString());
        }

        [TestMethod]
        [Priority(2)]
        public void CampaignGoal_TagInActive()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false, Features.EnableNativeAdsFlagId);
            TestCampaignCollection.Clean(customer);

            //Mock for tag (active)
            dynamic tag = TestUETV2Operations.CreateTag(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            TestUETV2Operations.PostTag(customer, tag);
            DatabaseHelper.SetUETTagStatus(customer, tag.Id, 2); //1:tag inactive

            //Mock for goal1: active goal with inactive tag
            dynamic goal1 = TestUETV2Operations.CreateDestinationGoal(tag.Id);
            var obj1 = TestUETV2Operations.PostGoal(customer, goal1);
            var goalId1 = (long)obj1.value;
            DatabaseHelper.SetGoalTrackingStatus(customer, goalId1, 3, 1);

            TestCampaignCollection sourceCampaign = new TestCampaignCollection(customer.AccountIds.Count());
            var addSourceCampaignResponse = sourceCampaign.Add(customer);
            ResponseValidator.ValidateBasicSuccess(addSourceCampaignResponse);
            var campaignId = addSourceCampaignResponse.CampaignIds.First();
            DBHelper.InsertCampaignConversionGoalData(customer.CustomerId, customer.AccountIds[0], campaignId.Value, goalId1);

            var result = TestUETV2Operations.GetCampaignByIdWithCampaignGoal(customer, campaignId.Value);
            Assert.IsNotNull(result);
            Assert.AreEqual("TagInactive", result.CampaignConversionGoal.Goals[0].TrackingStatus.ToString());
        }

        [TestMethod]
        [Priority(2)]
        public void CampaignGoal_NoRecentConv()
        {
            CustomerInfo customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false, Features.EnableNativeAdsFlagId);
            TestCampaignCollection.Clean(customer);

            //Mock for tag (active)
            dynamic tag = TestUETV2Operations.CreateTag(
                 "TestTag-" + StringUtil.GenerateUniqueId(),
                 "some description");
            TestUETV2Operations.PostTag(customer, tag);
            DatabaseHelper.SetUETTagStatus(customer, tag.Id, 1); //1:tag active

            //Mock for goal1: active goal with active tag
            dynamic goal1 = TestUETV2Operations.CreateDestinationGoal(tag.Id);
            var obj1 = TestUETV2Operations.PostGoal(customer, goal1);
            var goalId1 = (long)obj1.value;
            DatabaseHelper.SetGoalTrackingStatus(customer, goalId1, 1, 1); //no recent conversion

            TestCampaignCollection sourceCampaign = new TestCampaignCollection(customer.AccountIds.Count());
            var addSourceCampaignResponse = sourceCampaign.Add(customer);
            ResponseValidator.ValidateBasicSuccess(addSourceCampaignResponse);
            var campaignId = addSourceCampaignResponse.CampaignIds.First();
            DBHelper.InsertCampaignConversionGoalData(customer.CustomerId, customer.AccountIds[0], campaignId.Value, goalId1);

            var result = TestUETV2Operations.GetCampaignByIdWithCampaignGoal(customer, campaignId.Value);
            Assert.IsNotNull(result);
            Assert.AreEqual("NoRecentConversion", result.CampaignConversionGoal.Goals[0].TrackingStatus.ToString());
        }
        
        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        public void CreateUpdateGetGoal_ServableExternalChannels_Success()
        {
            // Create goal, not servable in LinkedIn
            dynamic goal = TestUETV2Operations.CreateDestinationGoal(linkedInCustomerTagId, attributionModelType: AttributionModelType.Default);
            goal.ConversionCountType = "Unique";
            var obj = TestUETV2Operations.PostGoal(linkedInCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            var goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            goal.ServableExternalChannels = new string[0];
            var actualGoal = TestUETV2Operations.GetGoalByGoalId(linkedInCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyAttributionModel: true);

            // Update to servable in LinkedIn
            goal.ConversionCountType = "All";
            goal.Name = "DestinationGoal-" + StringUtil.GenerateUniqueId();
            TestUETV2Operations.UpdateGoal(linkedInCustomerInfo, goal);

            goal.ServableExternalChannels = new[] { "LinkedIn" };
            actualGoal = TestUETV2Operations.GetGoalByGoalId(linkedInCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal, isVerifyAttributionModel: true);

            // UI grid data call
            var goals = TestUETV2Operations.GetGoalsWithBiData(linkedInCustomerInfo);
            Assert.IsTrue(goals.value.Count >= 1, "goals count");

            // AppInstall goal should not be supported
            goal = TestUETV2Operations.CreateAppInstallGoal();

            obj = TestUETV2Operations.PostGoal(linkedInCustomerInfo, goal);
            Assert.IsNotNull(obj, "goal object");
            Assert.IsNotNull(obj.value, "goal object id");
            goalId = (long)obj.value;
            goal.Id = goalId;
            Trace.WriteLine("Goal Id is " + goalId);

            goal.ServableExternalChannels = new string[0];
            actualGoal = TestUETV2Operations.GetGoalByGoalId(linkedInCustomerInfo, goalId);
            TestUETV2Operations.AssertGoalEqual(goal, actualGoal);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [TestCategory(Constants.UETV2)]
        [Ignore] //no more mock data after we fully implement the api
        public void Goal_ConversionGoalsByAccountId_DiagnoseField_CiOnly()
        {
            int goalNum = 3;
            var cInfo = CustomerInfo.CreateStandardAdvertiser();
            dynamic tag = TestUETV2Operations.CreateTag(
                "TestTag-" + StringUtil.GenerateUniqueId(),
                "some description");
            var tagObj = TestUETV2Operations.PostTag(cInfo, tag);
            Assert.IsNotNull(tagObj, "tag object");
            Assert.IsNotNull(tagObj.Id, "tag object Id");
            var tagId = (long)tagObj.Id.Value;

            //mock for event goal
            for (int i = 0; i < goalNum; i++)
            {
                dynamic goal = TestUETV2Operations.CreateEventGoal(tagId, GoalName: $"GoalName-{i}");
                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine($"Goal Id is {goalId}, GoalName-{i}");
            }

            //mock for destination goal
            for (int i = 0; i < goalNum; i++)
            {
                dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine($"Destination goal Id is {goalId}");
            }

            //mock for offline goal
            for (int i = 0; i < goalNum; i++)
            {
                dynamic goal = TestUETV2Operations.CreateOfflineGoal();
                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;
                Trace.WriteLine($"Offline goal Id is {goalId}");
            }

            var goals = TestUETV2Operations.GetConversionGoalsByAccountId(cInfo);
            Assert.IsTrue(goals.value.Count == goalNum*3, $"goals count: {goals.value.Count * 3}");

            for (int i = 0; i < goals.value.Count; ++i)
            {
                Assert.IsNotNull(goals.value[i]["GoalDiagnosticsInfo"]);
                Assert.IsNotNull(goals.value[i]["GoalDiagnosticsInfo"]["GoalFixType"]);
            }
        }

        private IReadOnlyList<dynamic> CreateGoals(int count, bool hasViewThrough)
        {
            return Enumerable.Range(0, count).Select(i =>
            {
                dynamic goal = TestUETV2Operations.CreateDestinationGoal(tagId);
                if (!hasViewThrough)
                {
                    goal.ViewThroughLookbackWindowinMinutes = null;
                }

                var obj = TestUETV2Operations.PostGoal(cInfo, goal);
                Assert.IsNotNull(obj, "goal object");
                Assert.IsNotNull(obj.value, "goal object id");
                var goalId = (long)obj.value;
                goal.Id = goalId;

                return goal;
            }).ToList();
        }

        private IReadOnlyDictionary<long, BiData> PopulateBiData(DateTime date, IReadOnlyList<dynamic> goals, bool includePartialConversions)
        {
            if (!mockReporting)
            {
                return null;
            }

            var rand = new Random();
            var biData = goals.Select((goal, index) =>
            {
                var data = GenerateBiData(rand.Next(1, 100), 2);
                data.GoalId = goal.Id;
                data.GoalName = goal.Name;
                data.Date = date;

                if (includePartialConversions)
                {
                    data.ConversionsCredit = 7.25f + 100 * index;
                    data.UniqueConversionsCredit = 3.80f;
                    data.UniqueConversions = 2;
                    data.TotalConversionsCredit = 10.50f;
                    data.ViewThroughConversionsCredit = 123.23f;
                }

                return data;
            }).ToList();

            BiDatabaseHelper.SetGoalMockBiData(cInfo, biData, true);
            return biData.ToDictionary(x => x.GoalId.Value);
        }

        private void VerifyPartialConversion(dynamic response, IReadOnlyList<long> expectedGoalIdsInOrder, IReadOnlyDictionary<long, BiData> biDataLookup, bool withViewThrough)
        {
            Assert.AreEqual(expectedGoalIdsInOrder.Count, (int)response.value.Count, $"Incorrect count: response={response}");

            var index = 0;
            foreach (var goal in response.value)
            {
                var performanceMetrics = goal.GoalPerformanceMetrics;
                var goalId = (long)goal.Id;
                var biData = biDataLookup[goalId];
                var expectedGoalId = expectedGoalIdsInOrder[index];
                ++index;

                Assert.AreEqual(expectedGoalId, goalId, $"Incorrect goalId: response={response}");
                Assert.AreEqual(biData.ConversionsCredit.Value, (float)performanceMetrics.ConversionsCredit, $"ConverionsCredit mismatch: response={response}");
                Assert.AreEqual(biData.TotalConversionsCredit.Value, (float)performanceMetrics.AllConversionsCredit, $"AllConversionsCredit mismatch: response={response}");
                Assert.AreEqual(Math.Round(biData.ConversionsCredit.Value / biData.UniqueConversions.Value, 2), Math.Round((float)performanceMetrics.PartialConversionRepeatRate, 2), $"PartialConversionRepeatRate mismatch: response={response}");

                if (withViewThrough)
                {
                    Assert.AreEqual(biData.ViewThroughConversionsCredit, (float)performanceMetrics.ViewThroughConversionsCredit, $"ViewThroughConversionCredit mismatch: response={response}");
                }
                else
                {
                    Assert.IsNull((object)performanceMetrics.ViewThroughConversionsCredit.Value, $"ViewThroughConversionCredit mismatch: response={response}");
                }
            }
        }

        private BiData GenerateBiData(long seed, long orderId, int? conversionLag = null, DateTime? date = null)
        {
            var biData = new BiData
            {
                OrderId = orderId,
                Conversions = seed + 34,
                AdvertiserReportedRevenue = seed + 87,
                AllConversions = seed + 100,
                UniqueConversions = seed,
                ViewThroughConversions = seed + 56,
            };

            if (conversionLag != null)
            {
                biData.ConversionLag = (int)conversionLag;
            }
            if (date != null)
            {
                biData.Date = (DateTime)date;
            }

            return biData;
        }

        static private int RoundingFloatVal(float val)
        {
            int res = (int)val;
            if (val - res >= 0.5)
            {
                res += 1;
            }
            return res;
        }

        private void GetMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string expectedMsClickIdTagging)
        {
            var response = (JObject)ApiHelper.CallApi(customerInfo,
                c => c.GetAsync(MsClickIdTaggingUrl(customerInfo.CustomerId, customerInfo.AccountIds.First())),
                e => Assert.AreEqual(true, e.IsSuccessStatusCode));

            Assert.AreEqual(expectedMsClickIdTagging, response["MsClickIdTaggingEnabled"].Value<string>());
        }

        private void UpdateMsClickIdTaggingAndValidate(CustomerInfo customerInfo, string msClickIdTagging)
        {
            var url = ApiVersion.BaseUrl + string.Format("/Customers({0})/Accounts({1})", customerInfo.CustomerId, customerInfo.AccountIds.First());

            var patchJsonRequestStr = string.Format("{{\"@odata.type\":\"#Model.Account\",\"MsClickIdTaggingEnabled\":\"{0}\"}}", msClickIdTagging);
            var content = new StringContent(patchJsonRequestStr, System.Text.Encoding.UTF8, "application/json");
            var patchRequest = new HttpRequestMessage(new HttpMethod("PATCH"), url) { Content = content };

            var patchResponse = ApiHelper.CallApi(
                customerInfo,
                c => c.SendAsync(patchRequest),
                e => Assert.AreEqual(HttpStatusCode.NoContent, e.StatusCode));

            GetMsClickIdTaggingAndValidate(customerInfo, msClickIdTagging);
        }

        private sbyte? MapGoalAttributeOperator(string operatorStr)
        {
            if (operatorStr.ToLower() == "equalsto")
                return 1;
            if (operatorStr.ToLower() == "beginswith")
                return 2;
            if (operatorStr.ToLower() == "regularexpression")
                return 3;
            if (operatorStr.ToLower() == "contains")
                return 4;
            if (operatorStr.ToLower() == "greaterthan")
                return 6;

            //add other operator here if you need it in your test

            return null;
        }

        private List<BiData> PopulateConversionDelayData(long goalId, string goalName)
        {
            Random ra = new Random();
            var biDatas = new List<BiData>();
            for (int i = 0; i < 7; i++)
            {
                var biData = new BiData();
                switch (i)
                {
                    case 0:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 1:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 2:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 3:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 4:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 5:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                    case 6:
                        biData = GenerateBiData(ra.Next(1, 100), i, conversionLag: i, date: DateTime.Today);
                        break;
                }
                biData.GoalId = goalId;
                biData.GoalName = goalName;
                biDatas.Add(biData);
            }
            BiDatabaseHelper.SetGoalMockBiData(conversionDelayCustomer, biDatas);
            return biDatas;
        }

        private static void VerifyConversionDelayGridData(dynamic result, Dictionary<long, List<BiData>> biDataByGoald)
        {
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];

                if (biDataByGoald != null && biDataByGoald.ContainsKey((long)actualRow.Id))
                {
                    var expectedBiData = biDataByGoald[(long)actualRow.Id].Where(x => x.GoalId == (long)actualRow.Id);
                    dynamic actualMetrics = actualRow["GoalPerformanceMetrics"];

                    var expectedVal = Math.Round((double)expectedBiData.Where(x => x.ConversionLag == 0).Sum(x => x.AllConversions), 2, MidpointRounding.AwayFromZero).ToString();
                    Assert.AreEqual(expectedVal, actualMetrics["ConversionDelayZeroDay"].ToString(), "ConversionDelayZeroDay");

                    var lagToConversionsDictionary = new Dictionary<double, long>();
                    foreach (var biData in expectedBiData)
                    {
                        if (lagToConversionsDictionary.ContainsKey(biData.ConversionLag))
                        {
                            lagToConversionsDictionary[biData.ConversionLag] += biData.AllConversions ?? 0;
                        }
                        else
                        {
                            lagToConversionsDictionary.Add(biData.ConversionLag, biData.AllConversions ?? 0);
                        }
                    }
                    lagToConversionsDictionary.OrderBy(x => x.Key);

                    var totalSumConversions = expectedBiData.Sum(x => x.AllConversions ?? 0);

                    var ninetyPercentConversions = (long)(totalSumConversions * 0.9);

                    var currSumConversions = 0.0;
                    foreach (var kvp in lagToConversionsDictionary)
                    {
                        currSumConversions += kvp.Value;
                        if (currSumConversions >= ninetyPercentConversions)
                        {
                            Assert.AreEqual(kvp.Key.ToString(), actualMetrics["ConversionDelayNinety"].ToString(), "ConversionDelayNinety");
                            break;
                        }
                    }

                    var ninetyNinePercentConversions = (long)(totalSumConversions * 0.99);

                    currSumConversions = 0.0;
                    foreach (var kvp in lagToConversionsDictionary)
                    {
                        currSumConversions += kvp.Value;
                        if (currSumConversions >= ninetyNinePercentConversions)
                        {
                            Assert.AreEqual(kvp.Key.ToString(), actualMetrics["ConversionDelayNinetyNine"].ToString(), "ConversionDelayNinetyNine");
                            break;
                        }
                    }
                }
            }
        }
    }
}
#endif